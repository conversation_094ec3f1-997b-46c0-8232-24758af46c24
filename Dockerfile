FROM php:7.1.33-fpm

ENV LANG=en_US.UTF-8 LANGUAGE=en_US:en LC_ALL=en_US.UTF-8 DEBIAN_FRONTEND=noninteractive

RUN apt-get update \
    && apt-get -y --no-install-recommends install \
            libicu-dev \
            mariadb-client \
            # Required by composer
            git zlib1g-dev \

    # Required extension
    && docker-php-ext-install -j$(nproc) intl \

    # Additional common extensions
    && docker-php-ext-install -j$(nproc) opcache \
    && docker-php-ext-install bcmath \
    && docker-php-ext-install mysqli && docker-php-ext-install pdo_mysql \
    && pecl install apcu-5.1.8 && docker-php-ext-enable apcu \

    # Required by composer
    && docker-php-ext-install -j$(nproc) zip \

    # Cleanup to keep the images size small
    && apt-get purge -y \
        icu-devtools \
        libicu-dev \
        zlib1g-dev \
    && apt-get autoremove -y \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/* /usr/share/doc/*

# Install composer
#COPY install-composer /install-composer
#RUN /install-composer && rm /install-composer && useradd -u 1000 -s /bin/bash -d /home/<USER>

WORKDIR /application
