@echo off

set dbUser=root
set dbPassword=root
set backupDir="C:\Users\<USER>\Downloads"
set mysqldump="C:\Program Files\MySQL\MySQL Server 5.7\bin\mysqldump.exe"
set zip="7z.exe"
set dbname=carrier_ttwa


For /f "tokens=2-4 delims=/. " %%a in ('date /t') do (set mydate=%%c%%a%%b)
For /f "tokens=1-2 delims=/:" %%a in ("%TIME%") do (set mytime=%%a%%b)

REM ltrim
for /f "tokens=* delims= " %%a in ("%mytime%") do set mytime=%%a

set "filename=%dbname%_%mydate%_%mytime%.sql"
echo %filename%

%mysqldump% --host="localhost" --user=%dbUser% --password=%dbPassword% --single-transaction --add-drop-table --databases %dbname% > %backupDir%\%filename%

forfiles /p %backupDir% /s /m *.zip /d -30 /c "cmd /c del @file"

%zip% a -tzip %backupDir%\%filename%.zip %backupDir%\%filename%

del %backupDir%\%filename%
