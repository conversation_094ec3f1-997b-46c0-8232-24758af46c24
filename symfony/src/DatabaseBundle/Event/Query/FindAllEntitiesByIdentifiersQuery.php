<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Event\Query;

use LoginAutonom\DatabaseBundle\Interfaces\FindAllDatabaseQueryMessageInterface;

final class FindAllEntitiesByIdentifiersQuery implements FindAllDatabaseQueryMessageInterface
{
    private string $entityClass;
    private array $identifiers;

    public function __construct(string $entityClass = '', array $identifiers = [])
    {
        $this->entityClass = $entityClass;
        $this->identifiers = $identifiers;
    }

    public function getEntityClass(): string
    {
        return $this->entityClass;
    }

    public function setEntityClass(string $entityClass): self
    {
        $this->entityClass = $entityClass;
        return $this;
    }

    public function getIdentifiers(): array
    {
        return $this->identifiers;
    }

    public function setIdentifiers(array $identifiers): self
    {
        $this->identifiers = $identifiers;
        return $this;
    }
}
