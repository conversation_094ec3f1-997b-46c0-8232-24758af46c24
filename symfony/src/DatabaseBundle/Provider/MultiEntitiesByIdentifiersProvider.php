<?php

declare(strict_types=1);

namespace LoginAutonom\DatabaseBundle\Provider;

use LoginAutonom\CoreBundle\Interfaces\QueryBusInterface;
use LoginAutonom\DatabaseBundle\Event\Query\FindAllEntitiesByIdentifiersQuery;

final readonly class MultiEntitiesByIdentifiersProvider
{
    public function __construct(
        private QueryBusInterface $queryBus,
    ) {
    }

    public function provide(string $entityFQCN, array $identifiers, array $stamps = []): array
    {
        return  $this->queryBus->query(
            (new FindAllEntitiesByIdentifiersQuery())
                ->setEntityClass($entityFQCN)
                ->setIdentifiers($identifiers),
            $stamps
        );
    }
}
