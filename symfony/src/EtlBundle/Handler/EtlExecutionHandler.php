<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Handler;

use Carbon\Carbon;
use LoginAutonom\CoreBundle\Handler\InMemoryCacheClearingHandler;
use LoginAutonom\CoreBundle\Interfaces\CommandBusInterface;
use LoginAutonom\CoreBundle\Interfaces\QueryBusInterface;
use LoginAutonom\CoreBundle\Monolog\Handler\InProcessLogHandler;
use LoginAutonom\CoreBundle\Util\DebugUtils;
use LoginAutonom\DatabaseBundle\DTO\EntityPersistRequest;
use LoginAutonom\DatabaseBundle\Event\Command\PersistEntitiesCommand;
use LoginAutonom\DatabaseBundle\Exception\NotFoundDatabaseResultException;
use LoginAutonom\DatabaseBundle\Handler\EntityPersistRequestHandler;
use LoginAutonom\DatabaseBundle\Handler\TransactionHandler;
use LoginAutonom\EtlBundle\Builder\EtlProcessBuilder;
use LoginAutonom\EtlBundle\Descriptor\EtlWorkflowDescriptor;
use LoginAutonom\EtlBundle\DTO\FlowPositionDTO;
use LoginAutonom\EtlBundle\Entity\EtlProcess;
use LoginAutonom\EtlBundle\Enum\EtlProcessStatusEnum;
use LoginAutonom\EtlBundle\Guesser\FlowTransitionGuesser;
use LoginAutonom\EtlBundle\Interfaces\EtlExecutionHandlerInterface;
use LoginAutonom\EtlBundle\Interfaces\EtlExecutionPostProcessorInterface;
use LoginAutonom\EtlBundle\Interfaces\EtlExecutionPreProcessorInterface;
use LoginAutonom\EtlBundle\Interfaces\EtlWorkflowDescriptorBuilderInterface;
use LoginAutonom\EtlBundle\Interfaces\FlowTransitionProcessorInterface;
use LoginAutonom\EtlBundle\Message\Query\EtlProcessLastSuccessQuery;
use LoginAutonom\EtlBundle\Monolog\Processor\EtlFlowProcessor;
use LoginAutonom\EtlBundle\Provider\EtlWorkflowProvider;
use Monolog\Logger;
use Monolog\Processor\MemoryUsageProcessor;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;
use Symfony\Component\DependencyInjection\Attribute\TaggedLocator;
use Symfony\Contracts\Service\ServiceProviderInterface;

#[Autoconfigure(shared: true)]
final readonly class EtlExecutionHandler implements EtlExecutionHandlerInterface
{
    public const TYPE = 'type';
    public const CONFIG = 'config';
    public const FLOW = 'flow';
    public const TRANSITION = 'transition';

    public function __construct(
        private QueryBusInterface $queryBus,
        private EtlProcessBuilder $etlProcessBuilder,
        private EtlWorkflowProvider $etlWorkflowProvider,
        #[TaggedLocator(EtlWorkflowDescriptorBuilderInterface::TAG, defaultIndexMethod: 'getName')]
        private ServiceProviderInterface $workflowDescriptorBuilders,
        private EntityPersistRequestHandler $entityPersistRequestHandler,
        private CommandBusInterface $commandBus,
        private FlowTransitionGuesser $transitionGuesser,
        #[TaggedLocator(FlowTransitionProcessorInterface::TAG, defaultIndexMethod: 'getName')]
        private ServiceProviderInterface $processors,
        private InMemoryCacheClearingHandler $cacheClearingHandler,
        private TransactionHandler $transactionHandler,
        #[TaggedLocator(EtlExecutionPreProcessorInterface::TAG, defaultIndexMethod: 'getName')]
        private ServiceProviderInterface $preProcessors,
        #[TaggedLocator(EtlExecutionPostProcessorInterface::TAG, defaultIndexMethod: 'getName')]
        private ServiceProviderInterface $postProcessors,
    ) {
    }

    public function handle(string $workflow, ?\DateTimeInterface $fromDate): EtlProcess
    {
        $logger = $this->createLogger();
        $lastSuccess = $this->getLastSuccess($workflow, $logger);
        $process = $this->etlProcessBuilder->reset()
            ->setWorkflow($workflow)
            ->build();
        if ($lastSuccess instanceof EtlProcess) {
            $process->mergeMetadata($lastSuccess->getMetaData() ?? []);
        }

        $process->setProcessStatus(EtlProcessStatusEnum::SUCCESSFUL);
        $workflowConfigEntity = $this->etlWorkflowProvider->provide($workflow, $fromDate);
        $logger->info('Configuration row_id: ' . $workflowConfigEntity->getRow()->getRowId());
        $workflowConfig = $workflowConfigEntity->getConfig();
        /** @var EtlWorkflowDescriptorBuilderInterface $descriptorBuilder */
        $descriptorBuilder = $this->workflowDescriptorBuilders->get($workflowConfig[self::TYPE]);
        $descriptor = $descriptorBuilder->reset()
            ->setConfig($workflowConfig[self::CONFIG])
            ->setFromDate($fromDate)
            ->setLastSuccess($lastSuccess)
            ->setLogger($logger)
            ->setProcess($process)
            ->setLastSuccess($lastSuccess)
            ->build();
        $this->cacheClearingHandler->addCachePools($descriptor->cache()->getCacheItemPools());
        $descriptor->setWorkflowConfig($workflowConfigEntity);
        $startDate = Carbon::now();
        $process->setProcessStartDatetime($startDate);
        $process->setProcessStatus(EtlProcessStatusEnum::STARTED);
        $descriptor->setStartTime($startDate);

        foreach ($descriptor->getPreProcess() as $preProcessorConfig) {
            $config = $preProcessorConfig[self::CONFIG];
            $preProcessorName = $preProcessorConfig[self::TYPE];
            /** @var EtlExecutionPreProcessorInterface $preProcessor */
            $preProcessor = $this->preProcessors->get($preProcessorName);
            $preProcessor->process($config, $descriptor);
        }

        $currentOrder = 1;
        while ($descriptor->hasFlowByOrder($currentOrder)) {
            $flowName = $descriptor->getFlowNameByOrder($currentOrder);
            $flow = $descriptor->getFlowByName($flowName);
            $logger->info('Flow starting: ' . $flowName, [self::FLOW => $flowName]);
            DebugUtils::writeProfile('before_flow');
            try {
                $flow->run();
            } catch (\Throwable $e) {
                $process->setProcessStatus(EtlProcessStatusEnum::FAILED);
                break;
            }
            DebugUtils::writeProfile('after_flow');
            $logger->info('Flow finished: ' . $flowName);
            $this->executeTransitions($flowName, $descriptor);
            $currentOrder++;
        }

        foreach ($descriptor->getPostProcess() as $postProcessorConfig) {
            $config = $postProcessorConfig[self::CONFIG];
            $postProcessorName = $postProcessorConfig[self::TYPE];
            /** @var EtlExecutionPostProcessorInterface $postProcessor */
            $postProcessor = $this->postProcessors->get($postProcessorName);
            $postProcessor->process($config, $descriptor);
        }

        $process->setProcessStopDatetime(Carbon::now());
        $this->persistProcess($process);
        $descriptor->cache()->reset();
        if ($process->getProcessStatus() === EtlProcessStatusEnum::FAILED) {
            throw $e;
        }

        return $process;
    }

    private function createLogger(): LoggerInterface|Logger
    {
        return new Logger('etl', [
            new InProcessLogHandler(),
        ], [
            new MemoryUsageProcessor(useFormatting: false),
            new EtlFlowProcessor(),
        ]);
    }

    private function getLastSuccess(string $workflow, Logger|LoggerInterface $logger): ?EtlProcess
    {
        try {
            /** @var EtlProcess $lastSuccess */
            $lastSuccess = $this->queryBus->query(
                new EtlProcessLastSuccessQuery($workflow)
            );
            $logger->info('Last success run found: ' . $lastSuccess->getProcessId());
        } catch (NotFoundDatabaseResultException) {
            $lastSuccess = null;
            $logger->info('Last success run not found');
        }
        return $lastSuccess;
    }

    private function executeTransitions(string $flowName, EtlWorkflowDescriptor $descriptor): void
    {
        $processorGuess = $this->transitionGuesser->guess(
            new FlowPositionDTO(
                $descriptor->getFlowOrder(),
                $descriptor->getTransitions(),
                $flowName
            )
        );
        $logger = $descriptor->getLogger();
        foreach ($processorGuess->getProcessors() as $processorName => $processorConfig) {
            /** @var FlowTransitionProcessorInterface $processor */
            $processor = $this->processors->get($processorName);
            $logger->info("Executing processor: {$processorName}", [self::TRANSITION => $processorName]);
            $processor->process($processorConfig, $descriptor);
            $logger->info("Finished processor: {$processorName}");
        }
    }

    private function persistProcess(EtlProcess $process): void
    {
        $this->entityPersistRequestHandler->handle(
            new EntityPersistRequest($process, 'ETL is executed')
        );
        $this->commandBus->send(
            new PersistEntitiesCommand([$process])
        );
        $this->transactionHandler->commitTransaction();
    }
}
