<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Flow\Transformer;

use Carbon\Carbon;
use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use LoginAutonom\EtlBundle\Interfaces\FlowOneRowTransformerInterface;
use LoginAutonom\EtlBundle\Trait\EtlCommonFunctionsTrait;
use LoginAutonom\EtlBundle\Trait\FlowTransformerTrait;

final class StringToDateTransformer implements FlowOneRowTransformerInterface
{
    use FlowTransformerTrait;
    use EtlCommonFunctionsTrait;

    public function __construct(
        private array $fieldMap,
        private array $timeZones
    ) {
    }

    public function transform(Row $row, FlowContext $context): Row
    {
        foreach ($this->fieldMap as $fieldName => $format) {
            if (!$row->has($fieldName)) {
                continue;
            }
            $dateValue = $row->get($fieldName)->value();
            if (!is_string($dateValue)) {
                continue;
            }
            $dateObject = Carbon::rawCreateFromFormat(
                $format,
                $dateValue,
                $this->timeZones[$fieldName] ?? null
            );
            if ($dateObject instanceof \DateTimeInterface) {
                $dateObject = $dateObject->setTime(0, 0, 0, 0);
            }
            $row = $row->set(
                $context->entryFactory()->create(
                    $fieldName,
                    $dateObject
                )
            );
        }
        return $row;
    }
}
