<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Flow\Transformer;

use Flow\ETL\FlowContext;
use Flow\ETL\Row;
use LoginAutonom\CoreBundle\Exception\NotFoundException;
use LoginAutonom\CoreBundle\Util\DateTimeUtil;
use LoginAutonom\DatabaseBundle\Builder\StateSetDTOBuilder;
use LoginAutonom\DatabaseBundle\Builder\ValidityIntervalBasedEntitiesBuilder;
use LoginAutonom\DatabaseBundle\DTO\EntityPersistContext;
use LoginAutonom\DatabaseBundle\DTO\StateSetDTO;
use LoginAutonom\DatabaseBundle\Guesser\ValidityIntervalBasedEntityChangesGuesser;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoAutoJoinToEmployee;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoAutoJoinToEmployeeContract;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoValidityStamp;
use LoginAutonom\DatabaseBundle\Messenger\Stamp\NoVisibilityPermissionStamp;
use LoginAutonom\DatabaseBundle\Provider\MultiEntitiesByIdentifiersProvider;
use LoginAutonom\EtlBundle\Builder\EtlMappingBuilder;
use LoginAutonom\EtlBundle\Entity\EtlMapping;
use LoginAutonom\EtlBundle\Enum\EtlCommonFieldNamesEnum;
use LoginAutonom\EtlBundle\Interfaces\EtlCacheAwareInterface;
use LoginAutonom\EtlBundle\Interfaces\FlowPreProcessTransformerInterface;
use LoginAutonom\EtlBundle\Trait\EtlCacheAwareTrait;
use LoginAutonom\EtlBundle\Trait\EtlCommonFunctionsTrait;
use LoginAutonom\EtlBundle\Trait\EtlSerializableTrait;
use LoginAutonom\EtlBundle\Trait\FlowTransformerTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

final class CreateOrUpdateEntityWithValidityTransformer implements
    FlowPreProcessTransformerInterface,
    EtlCacheAwareInterface,
    LoggerAwareInterface
{
    use EtlCacheAwareTrait;
    use LoggerAwareTrait;
    use EtlSerializableTrait;
    use FlowTransformerTrait;
    use EtlCommonFunctionsTrait;

    public const IDENTIFIERS = 'identifiers';

    public function __construct(
        private readonly DateTimeUtil $dateTimeUtil,
        private readonly ValidityIntervalBasedEntityChangesGuesser $entityChangesGuesser,
        private readonly ValidityIntervalBasedEntitiesBuilder $validityIntervalBasedEntitiesBuilder,
        private readonly StateSetDTOBuilder $stateSetDTOBuilder,
        private readonly EtlMappingBuilder $etlMappingBuilder,
        private readonly MultiEntitiesByIdentifiersProvider $entitiesByIdentifiersProvider,
        private readonly string $entityClass,
        private readonly string $validFromField,
        private readonly string $validToField,
        private readonly array $identifierFields,
        private readonly array $fieldsToCopy,
        private readonly string $targetField,
        private readonly array $defaultValues = [],
        private readonly array $entityPersistContextMapping = [],
        private readonly ?string $lastValidityStampStorageField = null,
        private readonly ?string $mappingField = null,
        private readonly ?string $mappingType = null,
    ) {
    }

    public function collectInformation(Row $row, FlowContext $context, \ArrayObject $info): void
    {
        $info[self::IDENTIFIERS][] = $this->collectIdentifiers($row, $context);
    }

    public function executeAction(\ArrayObject $info, FlowContext $context): void
    {
        $multipleIdentifiers = [];
        foreach ($info[self::IDENTIFIERS] as $identifier) {
            if (empty($identifier)) {
                continue;
            }
            if (in_array($identifier, $multipleIdentifiers, true)) {
                continue;
            }
            $multipleIdentifiers[] = $identifier;
        }
        unset($info[self::IDENTIFIERS]);
        $entities = $this->entitiesByIdentifiersProvider->provide(
            $this->entityClass,
            $multipleIdentifiers,
            [
                new NoVisibilityPermissionStamp('To validity based guessing'),
                new NoValidityStamp(),
                new NoAutoJoinToEmployee(),
                new NoAutoJoinToEmployeeContract()
            ]
        );
        $etlCache = $this->getEntityCache($context);
        if ($etlCache->hasValidityBasedObject($this->entityClass)) {
            $entityCache = $etlCache->getValidityBasedObject($this->entityClass);
        } else {
            $entityCache = $this->validityIntervalBasedEntitiesBuilder->reset()
                ->setEntityClass($this->entityClass)
                ->build();
            $etlCache->addValidityBasedObject($this->entityClass, $entityCache);
        }
        $entityCache->addMultiple($entities);
        foreach ($multipleIdentifiers as $identifiers) {
            $entityCache->setFinalIdentifiers($identifiers);
        }
    }

    public function buildRow(Row $row, FlowContext $context, \ArrayObject $info): Row
    {
        $this->validateRequiredFields($row);
        $identifiers = $this->collectIdentifiers($row, $context);
        $fields = $this->prepareFields($row);

        if ($this->isIdentifierFieldNotExists($identifiers, $fields)) {
            return $row;
        }

        $stateSet = $this->buildStateSet($fields, $identifiers, $row);
        $objectChanges = $this->processChanges($row, $stateSet, $context);

        $row = $this->attachTarget($row, $objectChanges, $context);
        $row = $this->attachMapping($row, $objectChanges, $context);

        return $row;
    }

    private function validateRequiredFields(Row $row): void
    {
        if (!$row->has($this->validFromField) || is_null($row->get($this->validFromField)->value())) {
            throw new \Exception("Valid from field not found: {$this->validFromField}");
        }

        if (!$row->has($this->validToField)) {
            throw new \Exception("Valid to field not found: {$this->validToField}");
        }
    }

    private function prepareFields(Row $row): array
    {
        $fields = [];
        foreach ($this->fieldsToCopy as $fieldName => $targetFieldName) {
            if (!$row->has($fieldName)) {
                continue;
            }
            $fields[$targetFieldName] = $row->get($fieldName)->value();
        }

        foreach ($this->defaultValues as $fieldName => $fieldValue) {
            $fields[$fieldName] = $fieldValue;
        }

        return $fields;
    }

    private function buildStateSet(array $fields, array $identifiers, Row $row): object
    {
        $stateSet = $this->stateSetDTOBuilder->reset()
            ->setEntityFQCN($this->entityClass)
            ->setNewValidFrom($row->get($this->validFromField)->value())
            ->setNewValidTo($row->get($this->validToField)->value())
            ->setFields($fields)
            ->setIdentifiers($identifiers);

        if (
            isset($this->lastValidityStampStorageField) &&
            $row->has($this->lastValidityStampStorageField)
        ) {
            $stateSet->setStampStorage(
                $row->get($this->lastValidityStampStorageField)->value()
            );
        }

        return $stateSet->build();
    }

    private function processChanges(Row $row, StateSetDTO $stateSet, FlowContext $context): object
    {
        $etlCache = $this->getEntityCache($context);
        $entityCache = null;

        if ($etlCache->hasValidityBasedObject($this->entityClass)) {
            $entityCache = $etlCache->getValidityBasedObject($this->entityClass);
        }

        $objectChanges = $this->entityChangesGuesser->guess($stateSet, $entityCache);

        $changesStorage = $this->getChangesStorage($row);
        $changesStorage->mergeFrom($objectChanges);
        $etlCache->mergeObjectChanges($objectChanges);

        return $objectChanges;
    }

    private function attachTarget(Row $row, object $objectChanges, FlowContext $context): Row
    {
        if (!$objectChanges->hasTarget()) {
            return $row;
        }

        $targetEntity = $objectChanges->getTarget();
        $row = $row->set(
            $context->entryFactory()->create($this->targetField, $targetEntity)
        );

        $entityContext = $this->collectEntityPersistContext($row);
        if ($row->has(EtlCommonFieldNamesEnum::ENTITY_PERSIST_CONTEXT_STORAGE) && count($entityContext) !== 0) {
            $this->attachEntityPersistContext($row, $targetEntity, $entityContext);
        }

        return $row;
    }

    private function attachEntityPersistContext(Row $row, object $entity, array $entityContext): void
    {
        $entityPersistContextStorage = $this->getEntityPersistContextStorage($row);
        $entityPersistContext = new EntityPersistContext();
        $entityPersistContext->setContext($entityContext);
        $entityPersistContext->setEntity($entity);
        $entityPersistContextStorage->add($entity, $entityPersistContext);
    }

    private function attachMapping(Row $row, object $objectChanges, FlowContext $context): Row
    {
        if (
            !isset($this->mappingField, $this->mappingType) ||
            $row->has($this->mappingField) ||
            !$objectChanges->hasTarget()
        ) {
            return $row;
        }

        $etlCache = $this->getEntityCache($context);
        $mapping = $this->createMapping($row, $objectChanges->getTarget(), $etlCache, $context);

        try {
            $mapping = $etlCache->findMapping($mapping->getSourceIdentifier());
        } catch (NotFoundException $e) {
            $changesStorage = $this->getChangesStorage($row);
            $changesStorage->addNew($mapping);
            $etlCache->add($mapping);
        }

        return $row->set(
            $context->entryFactory()->create(
                $this->mappingField,
                $mapping
            )
        );
    }

    private function createMapping(Row $row, object $entity, object $etlCache, FlowContext $context): object
    {
        return $this->etlMappingBuilder->reset()
            ->setEntity($entity)
            ->setRow($row)
            ->setSourceFields($etlCache->getSourceMapping($this->mappingType))
            ->setTargetFields($etlCache->getTargetMapping($this->mappingType))
            ->setMappingType($this->mappingType)
            ->setWorkflow($context->config->id())
            ->build();
    }

    private function collectIdentifiers(Row $row, FlowContext $context): array
    {
        $etlCache = $this->getEntityCache($context);
        if (isset($this->mappingField, $this->mappingType) && $etlCache->hasMappingType($this->mappingType)) {
            return $this->collectIdentifiersByMapping($row, $context);
        }

        return $this->collectIdentifiersFromRow($row);
    }

    private function collectIdentifiersByMapping(Row $row, FlowContext $context): array
    {
        if ($row->has($this->mappingField)) {
            /** @var EtlMapping $mapping */
            $mapping = $row->get($this->mappingField)->value();
            return $mapping->getTargetIdentifier();
        }
        $etlCache = $this->getEntityCache($context);
        $sourceIdentifiers = $etlCache->collectIdentifiers(
            $row,
            $etlCache->getSourceMapping($this->mappingType)
        );
        try {
            $mapping = $etlCache->findMapping($sourceIdentifiers);

            return $mapping->getTargetIdentifier();
        } catch (NotFoundException) {
        }

        return [];
    }

    private function collectIdentifiersFromRow(Row $row): array
    {
        $identifiers = [];
        foreach ($this->identifierFields as $identifierFieldName => $fieldName) {
            if (!$row->has($fieldName)) {
                throw new \Exception("Identifier field not found: {$identifierFieldName} -> {$fieldName}");
            }
            $identifiers[$identifierFieldName] = $row->get($fieldName)->value();
        }

        return $identifiers;
    }

    private function collectEntityPersistContext(Row $row): array
    {
        $entityContext = [];
        foreach ($this->entityPersistContextMapping as $rowField => $contextFieldName) {
            if ($row->has(EtlCommonFieldNamesEnum::ENTITY_PERSIST_CONTEXT_STORAGE) && $row->has($rowField)) {
                $entityContext[$contextFieldName] = $row->get($rowField)->value();
            }
        }

        return $entityContext;
    }

    private function isIdentifierFieldNotExists(array $identifiers, array $fields): bool
    {
        $identifierFieldNotExists = false;
        $identifierNotExists = false;
        foreach ($this->identifierFields as $entityIdentifier => $entityIdentifierValue) {
            if (!isset($fields[$entityIdentifier])) {
                $identifierFieldNotExists = true;
            }
        }

        if (empty($identifiers)) {
            $identifierNotExists = true;
        }

        return $identifierFieldNotExists && $identifierNotExists;
    }
}
