<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Flow\Transformer;

use Flow\ETL\Row;
use Flow\ETL\Rows;
use LoginAutonom\CoreBundle\Descriptor\ConstraintAwareDescriptor;
use LoginAutonom\EtlBundle\DTO\RowsValidationDTO;
use LoginAutonom\EtlBundle\DTO\ValidationErrorDTO;
use LoginAutonom\EtlBundle\Enum\ValidationErrorHandlingTypedEnum;
use LoginAutonom\EtlBundle\Exception\ValidationBreakProcessException;
use LoginAutonom\EtlBundle\Exception\ValidationSkipLoopException;
use LoginAutonom\EtlBundle\Interfaces\ValidityCheckTransformerInterface;
use LoginAutonom\EtlBundle\Trait\EtlCommonFunctionsTrait;
use LoginAutonom\EtlBundle\Trait\FlowTransformerTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;
use Symfony\Component\Validator\Validator\ValidatorInterface;

final class SymfonyValidityCheckTransformer implements
    ValidityCheckTransformerInterface,
    LoggerAwareInterface
{
    use LoggerAwareTrait;
    use FlowTransformerTrait;
    use EtlCommonFunctionsTrait;

    public const HANDLE = 'handle';

    public function __construct(
        private readonly ValidatorInterface $validator,
        private readonly array $constraints,
        private readonly ValidationErrorHandlingTypedEnum $handleFieldNotFound,
        private readonly array $handleErrorsByField
    ) {
    }

    public function validate(RowsValidationDTO $dto): Rows
    {
        $this->checkFieldsExists($dto);
        /** @var Row $row */
        foreach ($dto->getRows() as $row) {
            if ($dto->canBeSkipped($this->getIdentifierByRow($row))) {
                continue;
            }
            if ($dto->isRemovable($this->getIdentifierByRow($row))) {
                continue;
            }
            foreach ($this->constraints as $fieldName => $fieldConstraints) {
                if ($row->has($fieldName)) {
                    $fieldValue = $row->valueOf($fieldName);
                } else {
                    $fieldValue = null;
                }
                /** @var ConstraintAwareDescriptor $fieldConstraint */
                foreach ($fieldConstraints as $fieldConstraint) {
                    $constraintViolationList = $this->validator->validate(
                        $fieldValue,
                        $fieldConstraint->getConstraint()
                    );
                    if ($constraintViolationList->getIterator()->count() === 0) {
                        continue;
                    }
                    $fieldHandling = $this->handleErrorsByField[$fieldName] ?? null;
                    $constraintHandling = $fieldConstraint->getOptionByName(self::HANDLE, null);
                    if ($fieldHandling !== null) {
                        $fieldHandling = ValidationErrorHandlingTypedEnum::from($fieldHandling);
                    }
                    if ($constraintHandling !== null) {
                        $constraintHandling = ValidationErrorHandlingTypedEnum::from($constraintHandling);
                    }
                    $action = $this->guessAction(
                        $fieldHandling,
                        $constraintHandling
                    );
                    if ($action === ValidationErrorHandlingTypedEnum::REMOVE_LINE) {
                        $dto->addIdentifierToRemoveLine(
                            $this->getIdentifierByRow($row)
                        );
                    }
                    if ($action === ValidationErrorHandlingTypedEnum::SKIP_LOOP) {
                        $dto->setSkipLoop();
                    }
                    if ($action === ValidationErrorHandlingTypedEnum::BREAK_PROCESS) {
                        $dto->setBreakProcess();
                    }
                    $dto->addError(
                        new ValidationErrorDTO(
                            $row,
                            $fieldName,
                            $constraintViolationList->getIterator()->getArrayCopy()
                        )
                    );
                }
            }
        }
        if ($dto->skipLoop()) {
            throw new ValidationSkipLoopException();
        }
        if ($dto->breakProcess()) {
            throw new ValidationBreakProcessException();
        }

        return $dto->getRows()->filter(function (Row $row) use ($dto) {
            return !$dto->canBeSkipped($this->getIdentifierByRow($row)) &&
                !$dto->isRemovable($this->getIdentifierByRow($row));
        });
    }

    private function checkFieldsExists(RowsValidationDTO $dto): void
    {
        $dto->getRows()->each(function (Row $row) use ($dto) {
            foreach ($this->constraints as $fieldName => $fieldConstraints) {
                if (
                    $this->handleFieldNotFound === ValidationErrorHandlingTypedEnum::SKIP_CHECK &&
                    !$row->has($fieldName)
                ) {
                    $this->logFieldNotFound($fieldName, $row, 'Skip check');
                    continue;
                }
                if (
                    $this->handleFieldNotFound === ValidationErrorHandlingTypedEnum::SKIP_LINE_CHECK &&
                    !$row->has($fieldName)
                ) {
                    $this->logFieldNotFound($fieldName, $row, 'Skip line check');
                    $dto->addIdentifierToSkipLineCheck($this->getIdentifierByRow($row));
                    return;
                }
                if (
                    $this->handleFieldNotFound === ValidationErrorHandlingTypedEnum::REMOVE_LINE &&
                    !$row->has($fieldName)
                ) {
                    $this->logFieldNotFound($fieldName, $row, 'Remove Line');
                    $dto->addIdentifierToRemoveLine($this->getIdentifierByRow($row));
                    return;
                }
                if (
                    $this->handleFieldNotFound === ValidationErrorHandlingTypedEnum::SKIP_LOOP &&
                    !$row->has($fieldName)
                ) {
                    $this->logFieldNotFound($fieldName, $row, 'Skip the loop');
                    $dto->setSkipLoop();
                    return;
                }
                if (
                    $this->handleFieldNotFound === ValidationErrorHandlingTypedEnum::BREAK_PROCESS &&
                    !$row->has($fieldName)
                ) {
                    $this->logFieldNotFound($fieldName, $row, 'Break the process');
                    $dto->setBreakProcess();
                    return;
                }
            }
        });
    }

    private function logFieldNotFound(string $fieldName, Row $row, string $action): void
    {
        $this->logger->warning(
            "Field not found: {$fieldName}. {$action}! Identifier: " . $this->getIdentifierByRow($row) .
            "LoopNumber: " . $this->getLoopNumberByRow($row)
        );
    }

    public function guessAction(
        ?ValidationErrorHandlingTypedEnum $fieldHandling = null,
        ?ValidationErrorHandlingTypedEnum $constraintHandling = null
    ): ValidationErrorHandlingTypedEnum {
        if ($fieldHandling === null) {
            $fieldHandling = ValidationErrorHandlingTypedEnum::BREAK_PROCESS;
        }
        if (
            in_array(
                $fieldHandling,
                [
                ValidationErrorHandlingTypedEnum::SKIP_CHECK,
                ValidationErrorHandlingTypedEnum::SKIP_LINE_CHECK,
                ],
                true
            )
        ) {
            return ValidationErrorHandlingTypedEnum::REMOVE_LINE;
        }
        if ($constraintHandling === null) {
            return $fieldHandling;
        }
        if (
            in_array(
                $constraintHandling,
                [
                ValidationErrorHandlingTypedEnum::SKIP_CHECK,
                ValidationErrorHandlingTypedEnum::SKIP_LINE_CHECK,
                ],
                true
            )
        ) {
            return $fieldHandling;
        }
        if ($fieldHandling->getNumeric() >= $constraintHandling->getNumeric()) {
            return $fieldHandling;
        }

        return $constraintHandling;
    }
}
