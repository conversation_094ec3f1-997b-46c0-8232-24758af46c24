<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Flow\Loader;

use Flow\ETL\FlowContext;
use Flow\ETL\Loader;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use LoginAutonom\CoreBundle\Interfaces\CommandBusInterface;
use LoginAutonom\DatabaseBundle\DTO\EntityPersistRequest;
use LoginAutonom\DatabaseBundle\Event\Command\PersistEntitiesCommand;
use LoginAutonom\DatabaseBundle\Handler\EntityPersistRequestHandler;
use LoginAutonom\DatabaseBundle\Handler\TransactionHandler;
use LoginAutonom\EtlBundle\Builder\ObjectsChangesFromEtlRowsBuilder;
use LoginAutonom\EtlBundle\Interfaces\EtlProcessAwareInterface;
use LoginAutonom\EtlBundle\Trait\EtlProcessAwareTrait;
use LoginAutonom\EtlBundle\Trait\EtlSerializableTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

final class AllEntityLoader implements
    Loader,
    EtlProcessAwareInterface,
    LoggerAwareInterface
{
    use EtlProcessAwareTrait;
    use EtlSerializableTrait;
    use LoggerAwareTrait;


    public function __construct(
        private readonly array $entityFields,
        private readonly array $config,
        private readonly array $persistReasons,
        private readonly CommandBusInterface $commandBus,
        private readonly EntityPersistRequestHandler $entityPersistRequestHandler,
        private readonly TransactionHandler $transactionHandler,
    ) {
    }

    public function load(Rows $rows, FlowContext $context): void
    {
        $entities = [];
        /** @var Row $row */
        foreach ($rows as $row) {
            foreach ($this->entityFields as $entityField) {
                if (!$row->has($entityField)) {
                    continue;
                }
                $entities[] = $row->valueOf($entityField);
            }
        }
        $objectsChangesFromRows = (new ObjectsChangesFromEtlRowsBuilder())
            ->reset()
            ->setRows($rows)
            ->build();
        $entitiesToPersist = array_merge(
            $entities,
            $objectsChangesFromRows->getNew(),
            $objectsChangesFromRows->getChanged(),
            $objectsChangesFromRows->getDeleted()
        );

        foreach ($entitiesToPersist as $entity) {
            $reason = $this->persistReasons[get_class($entity)];
            $this->entityPersistRequestHandler->handle(
                new EntityPersistRequest($entity, $reason)
            );
        }
        $this->commandBus->send(
            new PersistEntitiesCommand($entitiesToPersist)
        );
        $this->transactionHandler->commitTransaction();
        $this->transactionHandler->startTransaction();
    }
}
