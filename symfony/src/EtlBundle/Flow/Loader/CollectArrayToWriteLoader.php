<?php

declare(strict_types=1);

namespace LoginAutonom\EtlBundle\Flow\Loader;

use Flow\ETL\FlowContext;
use Flow\ETL\Loader;
use Flow\ETL\Row;
use Flow\ETL\Rows;
use LoginAutonom\CoreBundle\Interfaces\CommandBusInterface;
use LoginAutonom\DatabaseBundle\DTO\EntityPersistRequest;
use LoginAutonom\DatabaseBundle\Event\Command\PersistEntitiesCommand;
use LoginAutonom\DatabaseBundle\Handler\EntityPersistRequestHandler;
use LoginAutonom\DatabaseBundle\Handler\TransactionHandler;
use LoginAutonom\EtlBundle\Builder\EtlDataLineBuilder;
use LoginAutonom\EtlBundle\Enum\EtlProcessStatusEnum;
use LoginAutonom\EtlBundle\Interfaces\EtlArrayWriterInterface;
use LoginAutonom\EtlBundle\Interfaces\EtlDataLineHandlerAwareInterface;
use LoginAutonom\EtlBundle\Interfaces\EtlProcessAwareInterface;
use LoginAutonom\EtlBundle\Trait\EtlDataLineHandlerTrait;
use LoginAutonom\EtlBundle\Trait\EtlProcessAwareTrait;
use LoginAutonom\EtlBundle\Trait\EtlSerializableTrait;
use Psr\Log\LoggerAwareInterface;
use Psr\Log\LoggerAwareTrait;

final class CollectArrayToWriteLoader implements
    Loader,
    LoggerAwareInterface,
    EtlProcessAwareInterface,
    EtlDataLineHandlerAwareInterface
{
    use EtlSerializableTrait;
    use LoggerAwareTrait;
    use EtlDataLineHandlerTrait;
    use EtlProcessAwareTrait;

    private int $loopNumber = 1;

    public function __construct(
        private readonly array $fieldMap,
        private readonly array $writerConfig,
        private EtlArrayWriterInterface $writer,
        private array $lastRowIdentifiers,
        private EntityPersistRequestHandler $entityPersistRequestHandler,
        private CommandBusInterface $commandBus,
        private TransactionHandler $transactionHandler,
    ) {
    }

    public function load(Rows $rows, FlowContext $context): void
    {
        if ($rows->count() === 0) {
            return;
        }
        $rowsToWriter = [];
        $lastRowIdentifiers = [];
        $etlDataLineBuilder = (new EtlDataLineBuilder())
            ->reset()
            ->setProcess($this->process)
            ->setSource(self::class)
            ->addMetadata('writerClass', get_class($this->writer));

        $rowNumber = 1;
        /** @var Row $row */
        foreach ($rows as $row) {
            $rowToWriter = [];
            foreach ($this->fieldMap as $sourceField => $targetField) {
                $value = null;
                if ($row->has($sourceField)) {
                    $value = $row->valueOf($sourceField);
                }
                $rowToWriter[$targetField] = $value;
            }
            $this->etlDataLineHandler->handle(
                $etlDataLineBuilder->setLine($rowToWriter)
                ->setLoopNumber($this->loopNumber)
                ->setLineIdentifier((string)$rowNumber)
            );
            $rowsToWriter[] = $rowToWriter;
            $rowArray = $row->toArray();
            $lastRowIdentifiers = array_intersect_key($rowArray, array_flip($this->lastRowIdentifiers));
            $rowNumber++;
        }

        $this->process->mergeMetadata($lastRowIdentifiers);
        $this->process->setProcessStatus(EtlProcessStatusEnum::SUCCESSFUL);
        $this->entityPersistRequestHandler->handle(
            new EntityPersistRequest(
                $this->process,
                'New transaction in ETL load'
            )
        );
        $this->commandBus->send(
            new PersistEntitiesCommand([$this->process])
        );
        $this->writer->write($rowsToWriter, $this->logger, $this->writerConfig);

        $this->transactionHandler->commitTransaction();
        $this->transactionHandler->startTransaction();
        $this->loopNumber++;
    }
}
