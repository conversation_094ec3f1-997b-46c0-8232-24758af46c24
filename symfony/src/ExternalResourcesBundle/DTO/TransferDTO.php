<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\DTO;

use LoginAutonom\ExternalResourcesBundle\Interfaces\TransferableDataInterface;
use LoginAutonom\ExternalResourcesBundle\Interfaces\TransferConfigurationInterface;

final readonly class TransferDTO
{
    public function __construct(
        private string $type,
        private array $parameters,
        private TransferableDataInterface $data,
        private TransferConfigurationInterface $config,
    ) {
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function hasType(): bool
    {
        return isset($this->type);
    }

    public function getData(): TransferableDataInterface
    {
        return $this->data;
    }

    public function hasData(): bool
    {
        return isset($this->data);
    }

    public function getConfig(): TransferConfigurationInterface
    {
        return $this->config;
    }

    public function hasConfig(): bool
    {
        return isset($this->config);
    }

    public function getParameters(): array
    {
        return $this->parameters;
    }

    public function hasParameters(): bool
    {
        return isset($this->parameters);
    }
}
