<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\DTO;

final readonly class RequestParameterContextDTO
{
    public function __construct(
        private array $options,
        private string $requestType,
        private array $requestConfig,
    ) {
    }

    public function getOptions(): array
    {
        return $this->options;
    }

    public function getRequestType(): string
    {
        return $this->requestType;
    }

    public function getRequestConfig(): array
    {
        return $this->requestConfig;
    }

    public function hasOptions(): bool
    {
        return !empty($this->options);
    }

    public function hasRequestConfig(): bool
    {
        return !empty($this->requestConfig);
    }
}
