<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Enum;

enum ApiRequestConfigurationKeyEnum: string
{
    case TYPE = 'type';
    case INDEX_KEY = 'indexKey';
    case PARENT_KEY = 'parentKey';
    case PARAMS = 'params';
    case DYNAMIC_PARAMS = 'dynamicParams';
    case PARAMS_TO_EXTRACT = 'paramsToExtract';
    case FROM_REQUEST = 'fromRequest';
    case FIELD = 'field';
    case ORDER = 'order';
}
