<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Guesser;

use LoginAutonom\ExternalResourcesBundle\DTO\APIDynamicParamsDTO;
use LoginAutonom\ExternalResourcesBundle\DTO\RequestParameterContextDTO;
use LoginAutonom\ExternalResourcesBundle\Enum\ApiRequestConfigurationKeyEnum;

final readonly class DynamicParamsGuesser
{
    public function guess(RequestParameterContextDTO $context, APIDynamicParamsDTO $dynamicParams): array
    {
        if (!$context->hasRequestConfig()) {
            return [];
        }

        $requestConfig = $context->getRequestConfig();

        if (!isset($requestConfig[ApiRequestConfigurationKeyEnum::DYNAMIC_PARAMS->value])) {
            return [];
        }

        $dynamicParameters = [];
        $dynamicParamsConfig = $requestConfig[ApiRequestConfigurationKeyEnum::DYNAMIC_PARAMS->value];

        foreach ($dynamicParamsConfig as $sourceConfig) {
            $sourceRequest = $sourceConfig[ApiRequestConfigurationKeyEnum::FROM_REQUEST->value];
            $sourceField = $sourceConfig[ApiRequestConfigurationKeyEnum::FIELD->value];

            if (!$dynamicParams->hasDynamicParamsByRequestType($sourceRequest)) {
                continue;
            }

            if (!isset($dynamicParams->getDynamicParamsByRequestType($sourceRequest)[$sourceField])) {
                continue;
            }

            $storedParams = $dynamicParams->getDynamicParamsByRequestType($sourceRequest);
            $dynamicParameters[$sourceField] = $storedParams[$sourceField];
        }

        return $dynamicParameters;
    }
}
