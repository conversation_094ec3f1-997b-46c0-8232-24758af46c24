<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Guesser;

use LoginAutonom\ExternalResourcesBundle\DTO\RequestParameterContextDTO;
use LoginAutonom\ExternalResourcesBundle\Enum\ApiExtractorConfigurationKeyEnum;

final readonly class RequestSpecificParamsGuesser
{
    public function guess(RequestParameterContextDTO $context): array
    {
        if (!$context->hasRequestConfig() || !$context->hasOptions()) {
            return [];
        }

        $options = $context->getOptions();
        $requestType = $context->getRequestType();

        if (isset($options[ApiExtractorConfigurationKeyEnum::REQUEST_PARAMS->value][$requestType])) {
            return $options[ApiExtractorConfigurationKeyEnum::REQUEST_PARAMS->value][$requestType];
        }

        return [];
    }
}
