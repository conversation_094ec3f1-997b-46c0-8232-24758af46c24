<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Guesser;

use Carbon\Carbon;
use LoginAutonom\CoreBundle\Builder\DateRangeConfigDTOBuilder;
use LoginAutonom\CoreBundle\DTO\DateRangeConfigDTO;
use LoginAutonom\CoreBundle\Enum\DateRangeConfigKeyEnum;
use LoginAutonom\CoreBundle\Enum\DateRangePatternEnum;
use LoginAutonom\CoreBundle\Guesser\DateRangeGuesser;
use LoginAutonom\ExternalResourcesBundle\DTO\RequestParameterContextDTO;
use LoginAutonom\ExternalResourcesBundle\Enum\ApiExtractorConfigurationKeyEnum;

final readonly class DateParamGuesser
{
    public function __construct(
        private DateRangeGuesser $dateRangeGuesser,
        private DateRangeConfigDTOBuilder $dateRangeConfigDTOBuilder,
    ) {
    }

    public function guess(RequestParameterContextDTO $context): array
    {
        if (!$context->hasRequestConfig()) {
            return [];
        }

        $requestConfig = $context->getRequestConfig();
        $dateParameters = [];

        if (
            isset($requestConfig[DateRangeConfigKeyEnum::DATE_PATTERN->value]) &&
            isset($requestConfig[DateRangeConfigKeyEnum::DATE_FORMAT->value])
        ) {
            $dateRangeConfigDTO = $this->buildDateRangeConfig($requestConfig);
            $dateInterval = $this->dateRangeGuesser->guess($dateRangeConfigDTO);
            $format = $requestConfig[DateRangeConfigKeyEnum::DATE_FORMAT->value];

            return [
                ApiExtractorConfigurationKeyEnum::FROM->value => $dateInterval->getFrom()->format(
                    $format
                ),
                ApiExtractorConfigurationKeyEnum::TO->value => $dateInterval->getTo()->format(
                    $format
                ),
            ];
        }

        if (
            isset($requestConfig[ApiExtractorConfigurationKeyEnum::FROM->value]) &&
            isset($requestConfig[ApiExtractorConfigurationKeyEnum::TO->value])
        ) {
            $dateParameters = [
                ApiExtractorConfigurationKeyEnum::FROM->value => $requestConfig[ApiExtractorConfigurationKeyEnum::FROM->value],
                ApiExtractorConfigurationKeyEnum::TO->value => $requestConfig[ApiExtractorConfigurationKeyEnum::TO->value],
            ];
        }

        return $dateParameters;
    }

    private function buildDateRangeConfig(array $requestConfig): DateRangeConfigDTO
    {
        $dateRangeConfig = $this->dateRangeConfigDTOBuilder->reset()
            ->setDatePattern(
                DateRangePatternEnum::tryFrom(
                    $requestConfig[DateRangeConfigKeyEnum::DATE_PATTERN->value]
                )
            )
            ->setBaseDate(Carbon::now());
        if (
            isset($requestConfig[DateRangeConfigKeyEnum::CUSTOM_FROM->value]) &&
            isset($requestConfig[DateRangeConfigKeyEnum::CUSTOM_TO->value])
        ) {
            $dateRangeConfig
                ->setCustomFrom($requestConfig[DateRangeConfigKeyEnum::CUSTOM_FROM->value])
                ->setCustomTo($requestConfig[DateRangeConfigKeyEnum::CUSTOM_TO->value]);
        }

        return $dateRangeConfig->build();
    }
}
