<?php

declare(strict_types=1);

namespace LoginAutonom\ExternalResourcesBundle\Guesser;

use LoginAutonom\ExternalResourcesBundle\DTO\RequestParameterContextDTO;
use LoginAutonom\ExternalResourcesBundle\Enum\ApiRequestConfigurationKeyEnum;

final readonly class RequestConfigParamsGuesser
{
    public function guess(RequestParameterContextDTO $context): array
    {
        if (!$context->hasRequestConfig()) {
            return [];
        }

        $requestConfig = $context->getRequestConfig();
        if (isset($requestConfig[ApiRequestConfigurationKeyEnum::PARAMS->value])) {
            return $requestConfig[ApiRequestConfigurationKeyEnum::PARAMS->value];
        }

        return [];
    }
}
