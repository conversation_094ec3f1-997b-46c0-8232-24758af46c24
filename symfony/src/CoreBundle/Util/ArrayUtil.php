<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\Util;

use LoginAutonom\CoreBundle\Exception\NotFoundException;

final readonly class ArrayUtil
{
    public const ASCENDING_ORDER = 'ASC';
    public const DESCENDING_ORDER = 'DESC';

    public function findArrayInArray(array $needle, array $haystack): int|string
    {
        ksort($needle);
        foreach ($haystack as $key => $item) {
            $toNeedKeys = array_intersect_key($item, array_flip(array_keys($needle)));
            ksort($toNeedKeys);
            if ($needle === $toNeedKeys) {
                return $key;
            }
        }
        throw new NotFoundException(
            $haystack,
            $needle
        );
    }

    public static function arrayToMapByItem(array $array, string $key): array
    {
        $map = [];
        foreach ($array as $item) {
            $map[$item[$key]] = $item;
        }
        return $map;
    }

    public static function entityToMapWithId(array $entities, callable $idResolver): array
    {
        $map = [];
        foreach ($entities as $entity) {
            $key = $idResolver($entity);
            $map[$key] = $entity;
        }

        return $map;
    }

    public static function groupBy(array $values, callable $condition): array
    {
        $grouped = [];

        foreach ($values as $key => $value) {
            $groupKey = $condition($value);

            if (!array_key_exists($groupKey, $grouped)) {
                $grouped[$groupKey] = [];
            }

            $grouped[$groupKey][$key] = $value;
        }

        return $grouped;
    }

    public static function groupBySingle(array $values, callable $condition): array
    {
        $grouped = [];

        foreach ($values as $value) {
            $groupKey = $condition($value);
            $grouped[$groupKey] = $value;
        }

        return $grouped;
    }

    public static function sortByKey(array $values, string $key, string $orderBy = self::ASCENDING_ORDER): array
    {
        $column = array_column($values, $key);
        $direction = strtoupper($orderBy) === self::DESCENDING_ORDER ? SORT_DESC : SORT_ASC;

        array_multisort($column, $direction, $values);

        return $values;
    }

    public static function collectUniqueFieldValues(array $results, string $field): array
    {
        $values = [];

        array_walk_recursive($results, function ($value, $key) use ($field, &$values) {
            if ($key === $field) {
                $values[] = $value;
            }
        });

        return array_unique($values);
    }
}
