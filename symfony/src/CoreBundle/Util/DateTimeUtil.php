<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\Util;

use Carbon\Carbon;
use Symfony\Component\DependencyInjection\Attribute\Autowire;

final class DateTimeUtil
{
    public const MIDNIGHT = '00:00:00';
    public const DEFAULT_DATE_FORMAT = 'Y-m-d H:i:s';
    public const SQL_FORMAT = 'Y-m-d';

    public function __construct(
        #[Autowire(env: 'DEFAULT_VALID_FROM')]
        private string $defaultValidFrom,
        #[Autowire(env: 'DEFAULT_VALID_TO')]
        private string $defaultValidTo,
    ) {
    }

    public function convertSecToHourMinute(int $sec): string
    {
        $time = $this->convertStringToDateTime('00:00', 'H:i');
        $time->add($this->convertSecToDateTimeInterval($sec));

        return $time->format('H:i');
    }

    /**
     * @param string $dayTime
     * @param string $format
     * @return \DateTime
     * @throws \Exception
     */
    public function convertStringToDateTime(string $dayTime, string $format = self::DEFAULT_DATE_FORMAT): \DateTime
    {
        $converted = \DateTime::createFromFormat($format, $dayTime);
        if ($converted === false) {
            $message = sprintf("Date string to datetime convert error. (dayTime: %s)", $dayTime);
            throw new \Exception($message);
        }
        return $converted;
    }

    /**
     * @param int $sec
     * @return \DateInterval
     * @throws \Exception
     */
    public function convertSecToDateTimeInterval(int $sec): \DateInterval
    {
        return new \DateInterval("PT" . $sec . "S");
    }

    public function convertSecToMinuteSec(int $sec): string
    {
        $time = $this->convertStringToDateTime('00:00', 'H:i');
        $time->add($this->convertSecToDateTimeInterval($sec));

        return $time->format('i:s');
    }

    public function convertSecToMinute(int $sec): string
    {
        $time = $this->convertStringToDateTime('00:00', 'H:i');
        $time->add($this->convertSecToDateTimeInterval($sec));

        return $time->format('i');
    }

    public function convertDateTimeToString(
        \DateTimeInterface $dateTime,
        string $format = self::DEFAULT_DATE_FORMAT
    ): string {
        return $dateTime->format($format);
    }

    public function convertDateIntervalToHourMinute(\DateInterval $interval): string
    {
        $time = $this->convertStringToDateTime('00:00', 'H:i');
        $time->add($interval);

        return $time->format('H:i');
    }

    public function convertDateIntervalToSec(\DateInterval $interval): int
    {
        $reference = new \DateTimeImmutable();
        $endTime = $reference->add($interval);

        return abs($endTime->getTimestamp() - $reference->getTimestamp());
    }

    public function convertTimeStringToInt(string $time, string $format = "H:i"): int
    {
        $day = "2000-01-01";
        $format = "Y-m-d " . $format;
        $reference = \DateTime::createFromFormat("Y-m-d H:i", $day . " 00:00");
        $datetime = $this->convertStringToDateTime($day . " " . $time, $format);
        return abs($datetime->getTimestamp() - $reference->getTimestamp());
    }

    public function addTimeStringToDateTime(\DateTime $date, string $time): \DateTime
    {
        $date->add($this->convertTimeStringToDateInterval($time));
        return $date;
    }

    public function convertTimeStringToDateInterval(string $time): \DateInterval
    {
        $day = "2000-01-01";
        $reference = \DateTimeImmutable::createFromFormat("Y-m-d His", $day . "000000");
        $endTime = $this->convertDayAndTimeToDateTime($day, $time);
        return $endTime->diff($reference, true);
    }

    public function convertDayAndTimeToDateTime(string $day, string $time): \DateTime
    {
        if (preg_match('/^\d{1,2}:\d{2}:\d{2}$/', $time)) {
            return $this->convertStringToDateTime(
                "{$day} {$time}",
                self::DEFAULT_DATE_FORMAT
            );
        } elseif (preg_match('/^\d{1,2}:\d{2}$/', $time)) {
            return $this->convertStringToDateTime(
                "{$day} {$time}",
                "Y-m-d H:i"
            );
        }

        throw new \InvalidArgumentException();
    }

    public function convertSecToHourMinuteText(int $sec): string
    {
        $isNegative = 1;
        if ($sec < 0) {
            $isNegative = -1;
            $sec *= -1;
        }
        $wholeHours = $this->convertSecToHour($sec);
        $remainderSecFromHours = $sec - ($this->convertHourToSec($wholeHours));
        $minutes = (int)($remainderSecFromHours / 60);
        $remainderSecFromMinutes = (int)($sec - ($this->convertHourToSec($wholeHours) + $minutes * 60));

        return sprintf("%'02d:%'02d:%'02d", $wholeHours * $isNegative, $minutes, $remainderSecFromMinutes);
    }

    public function convertSecToHour(int $sec): int
    {
        return (int)($sec / 3600);
    }

    public function convertHourToSec(int $hour): int
    {
        return $hour * 3600;
    }

    public function nextDay(string $day): string
    {
        return date("Y-m-d", strtotime($day . ' + 1 day'));
    }

    public function dayMoveItOffset(string $day, int $offset, string $format = "Y-m-d"): string
    {
        return date($format, strtotime($day . " +  $offset  day"));
    }

    public function maximumDateStrings(array $dates): string
    {
        $dateTimes = $this->convertDateStringsToDateTime($dates);
        /** @var \DateTime $maximum */
        $maximum = max($dateTimes);

        return $maximum->format('Y-m-d');
    }

    /**
     * @param string[]|\DateTimeInterface[] $dates
     * @return \DateTimeInterface[]
     */
    private function convertDateStringsToDateTime(array $dates): array
    {
        $dateTimes = [];
        foreach ($dates as $date) {
            if ($date instanceof \DateTimeInterface) {
                $dateTimes[] = $date;
                continue;
            }
            $dateTimes[] = $this->convertDayAndTimeToDateTime($date, self::MIDNIGHT);
        }

        return $dateTimes;
    }

    public function minimumDateStrings(array $dates): string
    {
        $dateTimes = $this->convertDateStringsToDateTime($dates);
        /** @var \DateTime $maximum */
        $maximum = min($dateTimes);

        return $maximum->format('Y-m-d');
    }

    public function beforeDayObject(\DateTimeInterface $day): \DateTimeInterface
    {
        $carbon = $this->createNewCarbon($day);

        return $carbon->addDays(-1);
    }

    public function createNewCarbon(\DateTimeInterface $day): Carbon
    {
        if ($day instanceof Carbon) {
            return $day->clone();
        } else {
            return new Carbon($day);
        }
    }

    public function afterDayObject(\DateTimeInterface $day): \DateTimeInterface
    {
        $carbon = $this->createNewCarbon($day);

        return $carbon->addDay();
    }

    public function getDefaultValidFrom(): \DateTimeInterface
    {
        return $this->convertStringToDateTime("$this->defaultValidFrom 00:00:00", self::DEFAULT_DATE_FORMAT);
    }

    public function getDefaultValidTo(): \DateTimeInterface
    {
        return $this->convertStringToDateTime("$this->defaultValidTo 00:00:00", self::DEFAULT_DATE_FORMAT);
    }

    public function isWorkDay(\DateTimeInterface $day): bool
    {
        return !in_array($day->format('N'), ["6", "7"], true);
    }

    /**
     * @param \DateTimeInterface $from
     * @param \DateTimeInterface $to
     * @return \DateTimeInterface[]
     * @throws \DateMalformedPeriodStringException
     */
    public function generateDaysByRange(\DateTimeInterface $from, \DateTimeInterface $to): array
    {
        $range = new \DatePeriod(
            $from,
            new \DateInterval("P1D"),
            $to
        );
        $dates = iterator_to_array($range->getIterator(), false);
        $dates[] = $to;

        return $dates;
    }
}
