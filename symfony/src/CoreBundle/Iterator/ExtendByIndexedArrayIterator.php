<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\Iterator;

final readonly class ExtendByIndexedArrayIterator implements \Iterator
{
    public function __construct(
        private iterable $innerIterator,
        private array $indexedArray,
        private string $indexKey,
    ) {
    }

    public function current(): mixed
    {
        $current = $this->innerIterator->current();
        if (!array_key_exists($this->indexKey, $current))  {
            return $current;
        }
        $indexKey = $current[$this->indexKey];
        if (array_key_exists($indexKey, $this->indexedArray)) {
            $additionalData = $this->indexedArray[$indexKey];
            return array_merge($current, $additionalData);
        }

        return $current;
    }

    public function next(): void
    {
        $this->innerIterator->next();
    }

    public function key(): mixed
    {
        return $this->innerIterator->key();
    }

    public function valid(): bool
    {
        return $this->innerIterator->valid();
    }

    public function rewind(): void
    {
        $this->innerIterator->rewind();
    }
}
