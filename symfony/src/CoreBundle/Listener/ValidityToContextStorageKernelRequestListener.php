<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\Listener;

use Carbon\CarbonImmutable;
use <PERSON>ginAutonom\CoreBundle\Builder\ValidityObjectBuilder;
use LoginAutonom\CoreBundle\Enum\RequestAttributeNameEnum;
use LoginAutonom\CoreBundle\Guesser\ValidFromByContextGuesser;
use LoginAutonom\CoreBundle\Guesser\ValidToByContextGuesser;
use LoginAutonom\CoreBundle\Storage\ContextStorage;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;

#[AsEventListener(KernelEvents::REQUEST, priority: 11)]
final class ValidityToContextStorageKernelRequestListener
{
    public function __construct(
        private readonly ContextStorage $contextStorage,
        private readonly ValidityObjectBuilder $validityObjectBuilder,
    ) {
    }

    public function __invoke(RequestEvent $event)
    {
        $request = $event->getRequest();
        if ($request->attributes->has(RequestAttributeNameEnum::EXCEPTION)) {
            return;
        }
        if (!$request->attributes->has(RequestAttributeNameEnum::API_OPERATION)) {
            return;
        }
        if (
            !$request->query->has(ValidFromByContextGuesser::VALID_FROM) ||
            !$request->query->has(ValidToByContextGuesser::VALID_TO)
        ) {
            return;
        }
        $validFrom = CarbonImmutable::createFromFormat(
            'Y-m-d',
            $request->query->get(ValidFromByContextGuesser::VALID_FROM)
        )->setTime(0, 0, 0);
        $validTo = CarbonImmutable::createFromFormat(
            'Y-m-d',
            $request->query->get(ValidToByContextGuesser::VALID_TO)
        )->setTime(23, 59, 59);
        $this->contextStorage->setValidity(
            $this->validityObjectBuilder->reset()
            ->setValidFrom($validFrom)
            ->setValidTo($validTo)
            ->build()
        );
    }
}
