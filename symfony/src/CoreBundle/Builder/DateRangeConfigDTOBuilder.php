<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\Builder;

use LoginAutonom\CoreBundle\DTO\DateRangeConfigDTO;
use LoginAutonom\CoreBundle\Enum\DateRangePatternEnum;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;

#[Autoconfigure(shared: false)]
final class DateRangeConfigDTOBuilder
{
    private DateRangePatternEnum $datePattern;
    private \DateTimeInterface $baseDate;
    private int $maxMonths;
    private string $customFrom;
    private string $customTo;

    public function build(): DateRangeConfigDTO
    {
        if (!isset($this->datePattern)) {
            throw new \InvalidArgumentException('DateRangeConfigDTOBuilder requires a datePattern object');
        }

        if (!isset($this->baseDate)) {
            throw new \InvalidArgumentException('DateRangeConfigDTOBuilder requires a baseDate object');
        }

        return new DateRangeConfigDTO(
            $this->datePattern,
            $this->baseDate,
            $this->maxMonths ?? null,
            $this->customFrom ?? null,
            $this->customTo ?? null,
        );
    }

    public function reset(): self
    {
        unset(
            $this->datePattern,
            $this->maxMonths,
            $this->baseDate,
            $this->customFrom,
            $this->customTo
        );

        return $this;
    }

    public function getDatePattern(): DateRangePatternEnum
    {
        return $this->datePattern;
    }

    public function hasDatePattern(): bool
    {
        return isset($this->datePattern);
    }

    public function setDatePattern(DateRangePatternEnum $datePattern): self
    {
        $this->datePattern = $datePattern;
        return $this;
    }

    public function getBaseDate(): \DateTimeInterface
    {
        return $this->baseDate;
    }

    public function hasBaseDate(): bool
    {
        return isset($this->baseDate);
    }

    public function setBaseDate(\DateTimeInterface $baseDate): self
    {
        $this->baseDate = $baseDate;
        return $this;
    }

    public function getMaxMonths(): int
    {
        return $this->maxMonths;
    }

    public function hasMaxMonths(): bool
    {
        return isset($this->maxMonths);
    }

    public function setMaxMonths(int $maxMonths): self
    {
        $this->maxMonths = $maxMonths;
        return $this;
    }

    public function getCustomFrom(): string
    {
        return $this->customFrom;
    }

    public function hasCustomFrom(): bool
    {
        return isset($this->customFrom);
    }

    public function setCustomFrom(string $customFrom): self
    {
        $this->customFrom = $customFrom;
        return $this;
    }

    public function getCustomTo(): string
    {
        return $this->customTo;
    }

    public function hasCustomTo(): bool
    {
        return isset($this->customTo);
    }

    public function setCustomTo(string $customTo): self
    {
        $this->customTo = $customTo;
        return $this;
    }
}
