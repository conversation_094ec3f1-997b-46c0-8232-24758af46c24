<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\Builder;

use Carbon\Carbon;
use LoginAutonom\CoreBundle\DTO\SingleValidity;
use LoginAutonom\CoreBundle\DTO\ValidityInterval;
use LoginAutonom\CoreBundle\Interfaces\ValidityAwareObjectInterface;
use Symfony\Component\DependencyInjection\Attribute\Autoconfigure;

#[Autoconfigure(shared: false)]
final class ValidityObjectBuilder
{
    private \DateTimeInterface $validFrom;
    private \DateTimeInterface $validTo;

    public function build(): ValidityAwareObjectInterface
    {
        $from = new Carbon($this->validFrom);
        if ($from->eq($this->validTo)) {
            return new SingleValidity(
                $this->validFrom
            );
        }
        return new ValidityInterval(
            $this->validFrom,
            $this->validTo
        );
    }

    public function reset(): self
    {
        unset($this->validFrom, $this->validTo);

        return $this;
    }

    public function setValidFrom(\DateTimeInterface $validFrom): self
    {
        $this->validFrom = $validFrom;
        return $this;
    }

    public function setValidTo(\DateTimeInterface $validTo): self
    {
        $this->validTo = $validTo;
        return $this;
    }
}
