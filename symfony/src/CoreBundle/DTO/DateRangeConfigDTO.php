<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\DTO;

use LoginAutonom\CoreBundle\Enum\DateRangePatternEnum;

final readonly class DateRangeConfigDTO
{
    public function __construct(
        private DateRangePatternEnum $datePattern,
        private \DateTimeInterface $baseDate,
        private ?int $maxMonths,
        private ?string $customFrom,
        private ?string $customTo
    ) {
    }

    public function getDatePattern(): DateRangePatternEnum
    {
        return $this->datePattern;
    }

    public function hasDatePattern(): bool
    {
        return isset($this->datePattern);
    }

    public function getBaseDate(): \DateTimeInterface
    {
        return $this->baseDate;
    }

    public function hasBaseDate(): bool
    {
        return isset($this->baseDate);
    }

    public function getMaxMonths(): ?int
    {
        return $this->maxMonths;
    }

    public function hasMaxMonths(): bool
    {
        return isset($this->maxMonths);
    }

    public function getCustomFrom(): ?string
    {
        return $this->customFrom;
    }

    public function hasCustomFrom(): bool
    {
        return isset($this->customFrom);
    }

    public function getCustomTo(): ?string
    {
        return $this->customTo;
    }

    public function hasCustomTo(): bool
    {
        return isset($this->customTo);
    }
}
