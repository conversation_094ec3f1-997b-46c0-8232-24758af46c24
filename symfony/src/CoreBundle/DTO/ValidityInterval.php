<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\DTO;

use LoginAutonom\CoreBundle\Interfaces\ValidityAwareObjectInterface;

final class ValidityInterval implements ValidityAwareObjectInterface
{
    private \DateTimeInterface $from;
    private \DateTimeInterface $to;

    public function __construct(\DateTimeInterface $from, \DateTimeInterface $to)
    {
        $this->from = $from;
        $this->to = $to;
    }

    public function getFrom(): \DateTimeInterface
    {
        return $this->from;
    }

    public function getTo(): \DateTimeInterface
    {
        return $this->to;
    }
}
