<?php

declare(strict_types=1);

namespace LoginAutonom\CoreBundle\Guesser;

use Carbon\Carbon;
use LoginAutonom\CoreBundle\DTO\DateRangeConfigDTO;
use LoginAutonom\CoreBundle\DTO\ValidityInterval;
use LoginAutonom\CoreBundle\Enum\DateRangePatternEnum;

final readonly class DateRangeGuesser
{
    public function __construct(
        private int $defaultMaxMonths = 12
    ) {
    }

    public function guess(DateRangeConfigDTO $config): ValidityInterval
    {
        $pattern = $config->hasDatePattern() ? $config->getDatePattern() : DateRangePatternEnum::CURRENT_MONTH;
        $maxMonths = $config->hasMaxMonths() ? $config->getMaxMonths() : $this->defaultMaxMonths;
        $baseDate = $config->hasBaseDate() ? $config->getBaseDate() : Carbon::now();

        return match ($pattern) {
            DateRangePatternEnum::CURRENT_DATE => $this->getCurrentDateRange($baseDate),
            DateRangePatternEnum::NEXT_MONTH => $this->getNextMonthRange($baseDate),
            DateRangePatternEnum::PREV_MONTH => $this->getPrevMonthRange($baseDate),
            DateRangePatternEnum::LAST_N_MONTHS => $this->getLastNMonthsRange($baseDate, $maxMonths),
            DateRangePatternEnum::NEXT_N_MONTHS => $this->getNextNMonthsRange($baseDate, $maxMonths),
            DateRangePatternEnum::CUSTOM_RANGE => $this->getCustomRange($config, $maxMonths),
            default => $this->getCurrentMonthRange($baseDate),
        };
    }

    private function getCurrentDateRange(Carbon $baseDate): ValidityInterval
    {
        $startOfDay = $baseDate->copy()->startOfDay();
        $endOfDay = $baseDate->copy()->endOfDay();

        return new ValidityInterval(
            $startOfDay,
            $endOfDay
        );
    }

    private function getCurrentMonthRange(Carbon $baseDate): ValidityInterval
    {
        $startOfMonth = $baseDate->copy()->startOfMonth();
        $endOfMonth = $baseDate->copy()->endOfMonth();

        return new ValidityInterval(
            $startOfMonth,
            $endOfMonth,
        );
    }

    private function getNextMonthRange(Carbon $baseDate): ValidityInterval
    {
        $startOfMonth = $baseDate->copy()->addMonth()->startOfMonth();
        $endOfMonth = $baseDate->copy()->addMonth()->endOfMonth();

        return new ValidityInterval(
            $startOfMonth,
            $endOfMonth
        );
    }

    private function getPrevMonthRange(Carbon $baseDate): ValidityInterval
    {
        $startOfMonth = $baseDate->copy()->subMonth()->startOfMonth();
        $endOfMonth = $baseDate->copy()->subMonth()->endOfMonth();

        return new ValidityInterval(
            $startOfMonth,
            $endOfMonth,
        );
    }

    private function getLastNMonthsRange(Carbon $baseDate, int $months): ValidityInterval
    {
        $months = max(1, min($months, $this->defaultMaxMonths));
        $endOfRange = $baseDate->copy()->endOfMonth();
        $startOfRange = $baseDate->copy()->subMonths($months)->startOfMonth();

        return new ValidityInterval(
            $startOfRange,
            $endOfRange
        );
    }

    private function getNextNMonthsRange(Carbon $baseDate, int $months): ValidityInterval
    {
        $months = max(1, min($months, $this->defaultMaxMonths));
        $startOfRange = $baseDate->copy()->addMonth()->startOfMonth();
        $endOfRange = $baseDate->copy()->addMonths($months)->endOfMonth();

        return new ValidityInterval(
            $startOfRange,
            $endOfRange,
        );
    }

    private function getCustomRange(DateRangeConfigDTO $config, int $maxMonths): ValidityInterval
    {
        if ($config->hasCustomFrom() || !$config->hasCustomTo()) {
            throw new \InvalidArgumentException('Custom range requires both from and to dates');
        }

        $from = $config->getCustomFrom();
        $to = $config->getCustomTo();
        $fromDate = Carbon::parse($from);
        $toDate = Carbon::parse($to);

        $monthsDiff = $fromDate->diffInMonths($toDate);
        if ($monthsDiff > $maxMonths) {
            throw new \InvalidArgumentException(
                "Custom range exceeds maximum allowed months: {$maxMonths}"
            );
        }

        return new ValidityInterval(
            $fromDate,
            $toDate,
        );
    }
}
