version: "3.8"
services:
    ttwa_nginx:
        image: nginx
        container_name: ttwa_nginx
        #hostname: 'localhost.login'
        #user: "1000"
        depends_on:
            - ttwa_app
        ports:
            - "8080:80"
        volumes_from:
            - ttwa_app
        volumes:
            - ./.docker/nginx/nginx.conf:/etc/nginx/conf.d/default.conf:Z
            - ./.docker/nginx/logs:/var/log/nginx:Z
        networks:
            - frontend
            - backend

    ttwa_app:
        build: .
        container_name: ttwa_app
        #hostname: 'localhost.login'
        #user: "1000"
        depends_on:
            - ttwa_db
        environment:
            - MYSQL_DATABASE=rosenberger_ttwa
            - MYSQL_USER=ttwa
            - MYSQL_PASSWORD=ttwa
            - MYSQL_ROOT_PASSWORD=root
        volumes:
            - ./:/application:Z
            - ../ext:/ext:Z
            - ./.docker/php-fpm/php-ini-overrides.ini:/etc/php/7.1/fpm/conf.d/99-overrides.ini
            - ./.docker/php-fpm/logs:/var/log/php:Z
        expose:
            - "9000"
        networks:
            - backend

    ttwa_db:
        image: mysql:5.7
        container_name: ttwa_db
        privileged: true
        #user: "999"
        environment:
            - MYSQL_USER=ttwa
            - MYSQL_PASSWORD=ttwa
            - MYSQL_DATABASE=rosenberger_ttwa
            - MYSQL_ROOT_PASSWORD=root
        volumes:
            - ./.docker/mysql/db:/var/lib/mysql:Z
            - ./.docker/mysql/docker-entrypoint-initdb.d:/docker-entrypoint-initdb.d:Z
        ports:
            - "3306:3306"
        networks:
            - frontend
            - backend

networks:
    frontend:
        name: frontend
        driver: bridge
    backend:
        name: backend
