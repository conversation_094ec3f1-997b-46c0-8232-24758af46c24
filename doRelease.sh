#!/bin/bash

echo "==========================";

echo "";
echo "Kapcsolók:";
echo " -c: ügyfél megadása kapcsolóval - ./doRelease.sh -c abb"
echo " -m: patch készítése a megadott sha1 ID-tól, nem az utolsó tag-tól - ./doRelease.sh -m abab3fe18b6ad19be1b75a367719bdb14716d74d"
echo " -t: TAR archívum a ZIP helyett"
echo "";

cFlag=0;
cValue="";

mFlag=0;
mValue="";

tFlag=0;

while getopts "c:m:t" opt;
do
	case $opt in
		c)
			cFlag=1;
			cValue=$OPTARG
		;;
		m)
			mFlag=1;
			mValue=$OPTARG
		;;
		t)
			tFlag=1;
		;;
		\?)
		;;
	esac
done

echo "";

DATE=$(date +"%Y-%m-%d");
DATETIME=$(date +"%Y-%m-%d_%H-%M-%S");

clients=$(cat releaseParams | tr "\t" "|" | grep -Ev "^#");

clientId=0;

declare -a arrayClients;

if [ $cFlag -eq 0 ];
then
	maxLen=0;

	for client in $clients
	do
		clientNId=$(echo $client | tr "|" " " | awk '{print $1}');

		if [ ${#clientNId} -gt $maxLen ];
		then
			maxLen=${#clientNId};
		fi
	done

	for client in $clients
	do
		clientId=`expr $clientId + 1`;

		clientNId=$(echo $client | tr "|" " " | awk '{print $1}');

		clientNIdLen=${#clientNId};

		echo -e -n "[$clientNId]";

		if [ $maxLen -gt $clientNIdLen ];
		then
			while [ $maxLen -gt $clientNIdLen ]
			do
				echo -e -n " ";

				clientNIdLen=`expr $clientNIdLen + 1`;
			done
		fi

		echo -e -n "\t";

		arrayClients[$clientId]=$(echo $client | tr "|" " ");

		echo "branch: "$(echo ${arrayClients[$clientId]} | awk '{print $3}');
	done

	selectedId=0;

	echo "";
	echo -n "Enter client code: ";
	read selectedId;
else
	for client in $clients
	do
		clientId=`expr $clientId + 1`;

		arrayClients[$clientId]=$(echo $client | tr "|" " ");
	done

	if [ $(echo -n "$cValue" | tail -c 2) = "ci" ];
	then
		cValue=$(echo "$cValue" | rev | cut -c3- | rev)

		echo -e "$cValue :-))\n";
	fi

	selectedId=$cValue;
fi

clientId=0;

found=0;

for client in $clients
do
	clientId=`expr $clientId + 1`;

	clientNId=$(echo $client | tr "|" " " | awk '{print $1}');

	if [ "$selectedId" = "$clientNId" ];
	then
		found=1;

		selectedId=$clientId;
	fi
done

if [ $found -ne 1 ];
then
	echo "WRONG client!";
	echo "==========================";
	exit 1
fi

echo "Selected client: "$(echo ${arrayClients[$selectedId]} | awk '{print $1}');

currentBranch=$(git branch | grep "*" | tr -d "*" | tr -d " ");
echo "Current branch: "$currentBranch;

paramNo=0;

clientName="";
clientUrl="";
clientBranch="";

for param in ${arrayClients[$selectedId]}
do
	case $paramNo in
		0) # client name
			clientName=$param;
		;;
		1) # url
			clientUrl=$param;
		;;
		2) # branch
			clientBranch=$param;
		;;
		*)
		;;
	esac

	paramNo=`expr $paramNo + 1`;
done

echo "";

echo "Client: "$clientName;
echo "Client URL: "$clientUrl;
echo "Client branch: "$clientBranch;
clientTag=$(echo $clientBranch | sed "s#production#prod#g" | sed "s#prod-##g");
echo "Client tag: "$clientTag;

echo "";
echo "";

echo -n "DO Release? (Y/n): ";
read doRelease

echo "";

case $doRelease in
	"n")
		echo "Exit...";
		echo "==========================";
		exit 1
	;;
	*)
		echo "DO Release!";
	;;
esac

echo "";

git checkout $clientBranch

currentClientBranch=$(git branch | grep "*" | tr -d "*" | tr -d " ");

if [ "$clientBranch" != "$currentClientBranch" ];
then
	echo "Exit...";
	echo "==========================";
	exit 1
fi

git pull origin $clientBranch

echo "";
echo "";

echo -n "DO Merge from $currentBranch? (y/N): ";
read doMerge

echo "";

sha1BeforeMerge=$(git rev-parse $clientBranch)

mode="MERGE";

case $doMerge in
	"y")
		echo "DO Merge!";
		git merge $currentBranch
	;;
	*)
		mode="HOTFIX";
	;;
esac

git push origin $clientBranch
sha1AfterMerge=$(git rev-parse $clientBranch)
lastCommitMsg=$(git show -s --format=%B $sha1AfterMerge)

currRev=$(git show-ref --tags | cut -f2- -d ' ' | grep $clientTag"_" | sed 's#refs/tags/##g' | grep $DATE | sed 's#.*_r##g' | sort -rn | head -1);
rev="";

if [ "$currRev" = "" ];
then
	rev=1;
else
	rev=`expr $currRev + 1`;
fi

tagName=$clientTag"_"$DATE"_r"$rev;

git tag -a $tagName -m $tagName
git push origin $tagName

laServerCustomer=$(echo $clientUrl | grep "login.hu");
laStageServerCustomer=$(echo $clientUrl | grep "stage-ttwa.login.hu");

if [ "$clientBranch" = "production" ];
then
	echo "";

	echo "Gitmand production...";

	res=$(wget "https://ttwa-build:<EMAIL>/gitmand/?path=/var/www/ttwa-base.login.hu&action=pull&auto=1" --no-check-certificate -q -O -)

	echo "";
	echo -n "Git Pull? (Y/n): ";
	read gitPull

	echo "";

	case $gitPull in
		"n")
			echo "Exit...";
			echo "==========================";
			exit 1
		;;
		*)
			echo "";
		;;
	esac
fi

if [ "$clientBranch" = "production2" ];
then
	echo "";

	echo "Gitmand production2...";

	res=$(wget "https://ttwa-build:<EMAIL>/gitmand/?path=/var/www/gollam-ease.login.hu&action=pull&auto=1" --no-check-certificate -q -O -)

	echo "";
	echo -n "Git Pull? (Y/n): ";
	read gitPull

	echo "";

	case $gitPull in
		"n")
			echo "Exit...";
			echo "==========================";
			exit 1
		;;
		*)
			echo "";
		;;
	esac
fi

if [ "$clientBranch" = "production3" ];
then
	echo "";

	echo "Gitmand production3...";

	res=$(wget "https://ttwa-build:<EMAIL>/gitmand/?path=/var/www/gimli-ease.login.hu&action=pull&auto=1" --no-check-certificate -q -O -)

	echo "";
	echo -n "Git Pull? (Y/n): ";
	read gitPull

	echo "";

	case $gitPull in
		"n")
			echo "Exit...";
			echo "==========================";
			exit 1
		;;
		*)
			echo "";
		;;
	esac
fi

if [ "$clientBranch" = "production4" ];
then
	echo "";

	echo "Gitmand production4...";

	res=$(wget "https://ttwa-build:<EMAIL>/gitmand/?path=/var/www/aragorn-ease.login.hu&action=pull&auto=1" --no-check-certificate -q -O -)

	echo "";
	echo -n "Git Pull? (Y/n): ";
	read gitPull

	echo "";

	case $gitPull in
		"n")
			echo "Exit...";
			echo "==========================";
			exit 1
		;;
		*)
			echo "";
		;;
	esac
fi

if [ "$clientBranch" = "staging" ];
then
	echo "";

	echo "Gitmand staging...";

	res=$(wget "https://ttwa-build:<EMAIL>/gitmand/?path=/var/www/base.stage-ttwa.login.hu&action=pull&auto=1" --no-check-certificate -q -O -)

	echo "";
	echo -n "Git Pull? (Y/n): ";
	read gitPull

	echo "";

	case $gitPull in
		"n")
			echo "Exit...";
			echo "==========================";
			exit 1
		;;
		*)
			echo "";
		;;
	esac
fi

if [ "$clientBranch" = "devstaging" ];
then
	echo "";

	echo "Gitmand staging...";

	res=$(wget "https://ttwa-build:<EMAIL>/gitmand/?path=/var/www/dev1-stage-ease.login.hu&action=pull&auto=1" --no-check-certificate -q -O -)

	echo "";
	echo -n "Git Pull? (Y/n): ";
	read gitPull

	echo "";

	case $gitPull in
		"n")
			echo "Exit...";
			echo "==========================";
			exit 1
		;;
		*)
			echo "";
		;;
	esac
fi

# create ZIP from given hash to HEAD
if [ $mFlag -eq 1 -a "$mValue" != "" ];
then
	sha1BeforeMerge=$mValue;
fi

ugprade_from=$(echo $sha1BeforeMerge | cut -c1-8);
ugprade_to=$(echo $sha1AfterMerge | cut -c1-8);

releasedIssues=$(git --no-pager log --format=format:"%ad %H %s" --date=iso "$currentBranch" "$ugprade_from..$ugprade_to" | grep '#\|bugfix/\|feature/' | sed 's/.*#//' | sed 's#.*bugfix/##' | sed 's#.*feature/##' | sed 's/ .*//' | sed 's/-.*//' | sed "s/[^0-9]//g" | uniq);

declare -a issueIDs;
for issueId in $releasedIssues
do
	echo "https://jira.login.hu/browse/DEV-$issueId";
	issueIDs["$issueId"]="$issueId";
done

i=0;
releaseNote="";
for issueId in "${issueIDs[@]}"
do
	if [ $i -gt 0 ];
	then
		releaseNote=$releaseNote", ";
	fi

	#https://github.com/LoginAutonom/TTWA/issues/1335
	#<http://www.foo.com|www.foo.com>
	releaseNote=$releaseNote"_<https://github.com/LoginAutonom/TTWA/issues/$issueId|#"$issueId">_";
	i=`expr $i + 1`;
done

#echo $releaseNote;
msg_start="";
upgr_mode="Upgrade";

if [ "$mode" = "HOTFIX" ];
then
	msg_start="";
	upgr_mode="Hotfix";
else
	msg_start="";
fi

msg_upgr="• *TTWA Upgrade*\n*Branch:* _"$clientBranch"_\n*Tag:* _"$tagName"_\n*$upgr_mode:* _"$ugprade_from"_ => _"$ugprade_to"_"; # \n*Tasks:* $releaseNote
msg_urls="";

# current customer use Login Autonom server?
if [ "$laServerCustomer" != "" -o "$clientBranch" = "production" -o "$clientBranch" = "staging" -o "$clientBranch" = "devstaging" -o "$clientBranch" = "production2" -o "$clientBranch" = "production3" -o "$clientBranch" = "production4" ]; # not generate patches
then
	# find ALL Login Autonom server customers
	for client in "${arrayClients[@]}"
	do
		if [ "$laStageServerCustomer" != "" ]; # stage
		then
			laServerCustomer=$(echo $client | grep "stage-ttwa.login.hu");
		else
			laServerCustomer=$(echo $client | grep "login.hu" | grep -v "stage-ttwa.login.hu");
		fi

		if [ "$laServerCustomer" != "" -o "$clientBranch" = "production" -o "$clientBranch" = "staging" -o "$clientBranch" = "devstaging" -o "$clientBranch" = "production2" -o "$clientBranch" = "production3" -o "$clientBranch" = "production4" ];
		then
			currParamNo=0;

			currClientName="";
			currClientUrl="";
			currClientBranch="";

			for param in $client
			do
				case $currParamNo in
					0) # client name
						currClientName=$param;
					;;
					1) # url
						currClientUrl=$param;
					;;
					2) # branch
						currClientBranch=$param;
					;;
					*)
					;;
				esac

				currParamNo=`expr $currParamNo + 1`;
			done

			# gitmand wget here
			if [ "$laStageServerCustomer" != "" ]; # stage
			then
				:
			else
				if [ "$clientBranch" != "staging" -a "$clientBranch" != "devstaging" -a "$clientBranch" != "production" -a "$clientBranch" != "production2" -a "$clientBranch" != "production3" -a "$clientBranch" != "production4" -a "$currClientBranch" = "$clientBranch" ];
				then
					echo "";

					echo "Gitmand $clientBranch...";

					res=$(wget "https://ttwa-build:<EMAIL>/gitmand/?path=/var/www/$currClientUrl/app&action=pull&auto=1" --no-check-certificate -q -O -)
				fi
			fi

			if [ "$currClientBranch" = "$clientBranch" ];
			then
				echo "";

				echo "DB upgrade on https://$currClientUrl/upgrade/upgrade/dbUpgradeAll ...";

				#<http://www.foo.com|www.foo.com>
				shortName=$(echo $currClientUrl | sed 's/.stage-ttwa.login.hu//' | sed 's/-ttwa.login.hu//' | sed 's/.login.hu//');
				msg_urls="$msg_urls <https://"$currClientUrl"/|_"$shortName"_>";

				res=$(wget https://$currClientUrl/upgrade/upgrade/dbUpgradeAll --no-check-certificate -q -O -);
				resg_f=$(echo $res | grep "failed");
				resg_d=$(echo $res | grep "duplicated");
				resg_m=$(echo $res | grep "modulenotfound");

				if [ "$resg_f" != "" -o "$resg_d" != "" -o "$resg_m" != "" ];
				then
					res=$(echo $res | sed -e "s/}/} /g");

					for result in $res
					do
						echo "$result";
					done
				fi
			fi
		fi
	done

	echo "";

	git checkout $currentBranch

	if [ "$msg_urls" != "" ];
	then
		msg_upgr="$msg_upgr\n*Urls:*$msg_urls";
	fi

	if [ "$clientBranch" = "production" -o "$clientBranch" = "staging" -o "$clientBranch" = "devstaging" -o "$clientBranch" = "production2" -o "$clientBranch" = "production3" -o "$clientBranch" = "production4" ];
	then
		if [ "$clientBranch" = "production" ];
		then
			text="$msg_upgr\n*Released to production*";
		elif [ "$clientBranch" = "production2" ];
		then
			text="$msg_upgr\n*Released to production2*";
		elif [ "$clientBranch" = "production3" ];
		then
			text="$msg_upgr\n*Released to production3*";
		elif [ "$clientBranch" = "production4" ];
		then
			text="$msg_upgr\n*Released to production4*";
		elif [ "$clientBranch" = "devstaging" ];
		then
			text="$msg_upgr\n*Released to devstaging*";
		else
			text="$msg_upgr\n*Released to staging*";
		fi
	else
		shortClientName=$(echo $clientName | sed "s#st-##g" | tr '[:lower:]' '[:upper:]');
		if [ $(echo $clientBranch | grep "prod") != "" ];
		then
			text="$msg_upgr\n*Released to production:* _"$shortClientName"_";
		else
			text="$msg_upgr\n*Released to staging:* _"$shortClientName"_";
		fi
	fi
else # generate upgrade patches
	echo "";

	echo -n "Create ZIP? (Y/n): ";
	read czip

	echo "";

	case $czip in
		"n")
			git checkout $currentBranch

			text="$msg_upgr\n*Release is in progress*";

			curl -X POST -H 'Authorization: Bearer ****************************************************************************' -H 'Content-type: application/json; charset=utf-8' --data "{\"channel\":\"#releases\",\"text\":\"$text\",\"username\":\"Ease++\"}" https://slack.com/api/chat.postMessage

			echo "==========================";
			exit 1
		;;
		*)
			echo "DO ZIP!";
		;;
	esac

	echo "";

	dir=${PWD##*/};

	archiveType="ZIP";

	if [ $tFlag -eq 1 -o "$clientName" = "faurecia" ];
	then
		archiveType="TAR";
	fi

	if [ "$archiveType" = "TAR" ];
	then
		zipname="PATCH_"$dir"_"$clientBranch"_"$DATETIME"_from_"$ugprade_from"_to_"$ugprade_to".tar";
	else
		zipname="PATCH_"$dir"_"$clientBranch"_"$DATETIME"_from_"$ugprade_from"_to_"$ugprade_to".zip";
	fi

	patchname="PATCH_"$dir"_"$clientBranch"_"$DATETIME"_from_"$ugprade_from"_to_"$ugprade_to".list";

	git diff --name-status $ugprade_from $ugprade_to > ./$patchname

	files=$(cat ./$patchname | tr '\t' '#');
	> ./$patchname"_tmp"

	for file in $files
	do
		i=0;

		for f in $(echo $file | tr '#' '\t')
		do
			i=`expr $i + 1`;
		done

		if [ $i -eq 3 ]
		then
			echo ":::3 $file";

			i=0;

			for f in $(echo $file | tr '#' '\t')
			do
				i=`expr $i + 1`;

				if [ $i -eq 2 ]
				then
					echo -e 'D\t'$f >> ./$patchname"_tmp"
				fi

				if [ $i -eq 3 ]
				then
					echo -e 'A\t'$f >> ./$patchname"_tmp"
				fi
			done
		else
			i=0;

			for f in $(echo $file | tr '#' '\t')
			do
				i=`expr $i + 1`;

				if [ $i -eq 1 ]
				then
					echo -e -n $f'\t' >> ./$patchname"_tmp"
				fi

				if [ $i -eq 2 ]
				then
					echo -e $f >> ./$patchname"_tmp"
				fi
			done
		fi
	done

	mv ./$patchname"_tmp" ./$patchname

	cat ./$patchname | grep -v "doRelease.sh" > ./$patchname"_tmp"
	mv ./$patchname"_tmp" ./$patchname
	cat ./$patchname | grep -v "export.sh" > ./$patchname"_tmp"
	mv ./$patchname"_tmp" ./$patchname
	cat ./$patchname | grep -v "gitTrack.sh" > ./$patchname"_tmp"
	mv ./$patchname"_tmp" ./$patchname
	cat ./$patchname | grep -v "patch_create.sh" > ./$patchname"_tmp"
	mv ./$patchname"_tmp" ./$patchname
	cat ./$patchname | grep -v "releaseModules" > ./$patchname"_tmp"
	mv ./$patchname"_tmp" ./$patchname
	cat ./$patchname | grep -v "releaseParams" > ./$patchname"_tmp"
	mv ./$patchname"_tmp" ./$patchname

	#cat ./$patchname
	files=$(cat ./$patchname | cut -f 2);

	if [ "$archiveType" = "TAR" ];
	then
		tar -cvf ../$zipname ./$patchname
	else
		zip -rv ../$zipname ./$patchname
	fi

	for file in $files
	do
		if [ "$archiveType" = "TAR" ];
		then
			tar -rvf ../$zipname $file
		else
			zip -rv ../$zipname $file
		fi
	done

	rm -rf ./$patchname

	sh ./version.sh
	if [ "$archiveType" = "TAR" ];
	then
		tar -rvf ../$zipname tmp/.version
	else
		zip -rv ../$zipname tmp/.version
	fi

	correctVersion=$(cat tmp/.version | grep "$clientBranch");
	correctShaId=$(cat tmp/.version | grep "$ugprade_to");

	echo ${arrayClients[$selectedId]} > ./releaseCustomerParams;
	if [ "$archiveType" = "TAR" ];
	then
		tar -rvf ../$zipname ./releaseCustomerParams
	else
		zip -rv ../$zipname ./releaseCustomerParams
	fi
	rm -rf ./releaseCustomerParams

	sha1sum ../$zipname > "../$zipname.sha1"

	echo "";

	git checkout $currentBranch

	echo "";

	if [ "$correctVersion" = "" -o "$correctShaId" = "" ]; # rossz .version
	then
		echo "Patch ERROR! DO NOT INSTALL!"
		rm -rf ../$zipname
	else
		echo "Patch check OK!"
	fi

	text="$msg_upgr\n*Patch file:* _"$zipname"_\n*Release is in progress*";
fi

curl -X POST -H 'Authorization: Bearer ****************************************************************************' -H 'Content-type: application/json; charset=utf-8' --data "{\"channel\":\"#releases\",\"text\":\"$text\",\"username\":\"Ease++\"}" https://slack.com/api/chat.postMessage

echo "==========================";

#find . -type f -name "*.md" | grep -v "/protected/extensions/" | grep -v "README.md"
