#!/bin/sh

dir=${PWD##*/};
rm -rf ../"PATCH_"$dir*.zip;

if [ "$#" -eq 3 ];
then
	branch=$1;
	ugprade_from=$2;
	ugprade_to=$3;
	date=$(date +"%Y-%m-%d_%H-%M-%S");

	zipname="PATCH_"$dir"_"$branch"_"$date"_from_"$ugprade_from"_to_"$ugprade_to".zip";
	patchname="PATCH_"$dir"_"$branch"_"$date"_from_"$ugprade_from"_to_"$ugprade_to".list";

	git checkout $branch

	files=$(git diff --name-status $ugprade_from $ugprade_to | cut -f 2);

	for file in $files
	do
		zip -rv ../$zipname $file
	done

	sh ./version.sh
	zip -rv ../$zipname tmp/.version

	#zip -rv ../$zipname tmp/.version
	git diff --name-status $ugprade_from $ugprade_to > ./$patchname
	zip -rv ../$zipname ./$patchname
	rm -rf ./$patchname

	git checkout master
else
	echo "Missing parameter / wrong parameter count ...";
fi
