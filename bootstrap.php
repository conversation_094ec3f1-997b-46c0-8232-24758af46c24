<?php

declare(strict_types=1);

require_once(__DIR__ . '/webroot/helpers.general.php');

defined('EXT_FOLDER') or define('EXT_FOLDER', '/ext/backend_modules');

// check yii framework
$framework_found = false;
$yiit = '';
$config = '';

$yii = dirname(__FILE__) . EXT_FOLDER . '/yii/framework/yiit.php';

if (file_exists($yii)) {
} else {
    $yii = dirname(__FILE__) . EXT_FOLDER . '/yii/framework/yiit.php';
}
if (file_exists($yii)) {
    $yiit = $yii;
    $config = dirname(__FILE__) . '/protected/config/test.php';
    $framework_found = true;
} else {
    die("framework missing!");
}

// For more error messages...
defined('YII_DEBUG') or define('YII_DEBUG', true);
defined('YII_TRACE_LEVEL') or define('YII_TRACE_LEVEL', 10);

// yii framework exists
if ($framework_found) {
    require_once($yiit);
    Yii::$enableIncludePath = false;
    Yii::createWebApplication($config);
}
