<?php

function convertData($path,$fromFile,$intoFile,$converterName="") { // n-8kdjkf5x

    $ext1 = fileext($fromFile);
    $ext2 = fileext($intoFile);
    $defaultConverter = "$ext1 to $ext2";
    $c = $converterName ?: $defaultConverter;
    $c = strtr($c,[" --> "=>" to "]);

    $args = [];
    $argList = "";
    if(contains($c,"(")) {
        list($c,$argList) = explode("(",$c,2);
        $c = trim($c);
        $converterName = $c;
        $argList = substr(trim($argList),0,-1); // cut ending ')'
        $args = texplode(",","$c,$argList");
    }

    "If no A-B converter, look for A-json-B";{

        $validConverters = [];
        $a = linesOf(load(__FILE__));
        $a = array_grep('if($c=="',$a);
        $a = array_grep('")',$a);
        foreach($a as $x) {
            $name = string_between($x,'"','"');
            $validConverters[$name] = 1;
        }

        if(!$validConverters[$converterName]) {
            list($fmt1,$fmt2) = explode(" to ",$converterName,2);
            $c1 = "$fmt1 to json"; if(!$validConverters[$c1]) die("Can't convert $fmt1 to json (using $converterName)\n");
            $c2 = "json to $fmt2"; if(!$validConverters[$c2]) die("Can't convert $fmt1 to json (using $converterName)\n");
            // print "No direct converter; going with [$fmt1 -> json -> $fmt2]\n";

            $tempFile = "convertdata.temp";
            convertData($path,$fromFile,$tempFile,$c1);
            convertData($path,$tempFile,$intoFile,$c2);
            @unlink("$path/$tempFile");
            return;
        }

    }

    if($c=="daily grid decoder") {

        $home = HOME;
        $json = load("$path/$fromFile");
        $data = json_decode($json,'arrays pls');
        $data = $data["htmlData"];
        foreach($data as $html) break;

        $texter = new Texter();
        $texter->set("working path",getPathFor("tempfiles of texter"));
        $texter->setText($html);
        $texter->applyScript('

            join lines with |
            split before <
            remove lines with </div>
            remove lines with data-column="day_
            join lines
            split before title="
            apply regex \ntitle="(.*)<div class="localMenuTitle">([^<]+)< --> \n$2||$1||
            tabs to spaces
            reduce spaces
            apply regex / - ([0-9\.]+\.[0-9\.]+\.[0-9\.]+) / --> ||$1
            apply regex / - ([0-9]+)/ --> ||$1
            field limit "||"
            remove first line
            trim lines from \'" class\'
            we got name,code,date,text
            sort by date

        ');
        $json = $texter->getJSON();
        $data = json_decode($json,'arrays pls');
        $json = json($data);
        saveAs("$path/$intoFile",$json);
        return;

    }

    if($c=="report fetcher") {

        if(!is_file("$path/$fromFile"))                     die("Payroll response JSON does not exist\n");
        $json = load("$path/$fromFile");        if(!$json)  die("Payroll response is empty\n");
        $data = json_decode($json,'arrays');    if(!$data)  die("Payroll response is not the JSON I was expecting\n");
        $list = $data["files"];                 if(!$list)  die("Payroll returned no files in the list\n");
        $from = getPathFor("webroot");
        $code = $args[1] ?: ""; if($code) $code = "$code.";

        foreach($list as $file) {
            $input = "$from/$file";
            $name = basename($file);
            $name = preg_replace("/^[a-f0-9]+_/","",$name);
            $output = "$path/$code$name";
            copy($input,$output);
        }

        return;

    }

    if($c=="json to csv") {

        $json = load("$path/$fromFile");
        $data = json_decode($json,'arrays pls');
        $csv  = "";foreach($data as $row) $csv.=str_putcsv($row);
        saveAs("$path/$intoFile",$csv,0,'utf-8');
        return;

    }

    if($c=="json to txt") {

        $json = load("$path/$fromFile");
        $data = json_decode($json,'arrays pls');
        $txt  = "";foreach($data as $row) $txt.=join("\t",$row)."\n";
        saveAs("$path/$intoFile",$txt);
        return;

    }

    if($c=="report grid decoder") {

        $home = HOME;
        $json = load("$path/$fromFile");
        $data = json_decode($json,'arrays pls');
        $main = explode("<tr ",array_shift($data["htmlData"]));
        $froz = explode("<tr ",array_shift($data["htmlData"]));
        $html = "";

       foreach($main as $y=>$table) {
            $h = trim($main[$y]); $h = preg_replace("/^[^>]+>/","",$h); $h = preg_replace("/<\/tr>$/","",$h); $html1 = $h;
            $h = trim($froz[$y]); $h = preg_replace("/^[^>]+>/","",$h); $h = preg_replace("/<\/tr>$/","",$h); $html2 = $h;
            $html .= "<tr> $html2 $html1 </tr>\n";
        }

        $p = getPathFor("output files");
        file_put_contents("$p/grid.debug.html",$html);

        // $html = $data["htmlData"]["dhtmlxGrid"];

        $texter = new Texter();
        $texter->set("working path",getPathFor("tempfiles of texter"));
        $texter->setText($html);
        $texter->applyScript('

            join lines with |
            split before <tr
            reduce tags td
            field limit </td><td>
            remove all tags
            we got 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35
        ');
        $json = $texter->getJSON();
        saveAs("$path/$intoFile",$json);
        return;

    }



    // archive voicenote 20201230-020102


}
