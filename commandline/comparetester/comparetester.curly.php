<?php

class Curly {

    private $path;
    private $handle;
    private $cookie;

    public function __construct() {

        $path = getPathFor("tempfiles of curly");
        $this->path = $path; @mkdir($path,0775,"force pls");
        $this->cookie = "$path/cookiejar.txt";
        $this->handle = curl_init();

    }

    public function cookieAge() {

        $cc = $this->cookie;
        return time() - filemtime($cc);

    }

    public function get($url) {

        $ch = $this->handle;
        $cc = $this->cookie;

        curl_setopt($ch, CURLOPT_URL,               $url);
        curl_setopt($ch, CURLOPT_HEADER,            0);
        curl_setopt($ch, CURLOPT_USERAGENT,         "compareTester");
        curl_setopt($ch, CURLOPT_AUTOREFERER,       true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION,    true);
        curl_setopt($ch, CURLOPT_MAXREDIRS,         10);
        curl_setopt($ch, CURLOPT_COOKIEJAR,         $cc);
        curl_setopt($ch, CURLOPT_COOKIEFILE,        $cc);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER,    1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST,    0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER,    0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYSTATUS,  0);
        curl_setopt($ch, CURLOPT_POST,              0);

        $html = curl_exec($ch);
        return $html;

    }

    public function post($url,$postArray) {

        $ch = $this->handle;
        $cc = $this->cookie;

        curl_setopt($ch, CURLOPT_URL,               $url);
        curl_setopt($ch, CURLOPT_HEADER,            0);
        curl_setopt($ch, CURLOPT_USERAGENT,         "compareTester");
        curl_setopt($ch, CURLOPT_AUTOREFERER,       true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION,    true);
        curl_setopt($ch, CURLOPT_MAXREDIRS,         10);
        curl_setopt($ch, CURLOPT_COOKIEJAR,         $cc);
        curl_setopt($ch, CURLOPT_COOKIEFILE,        $cc);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER,    1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST,    0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER,    0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYSTATUS,  0);
        curl_setopt($ch, CURLOPT_POST,              1);
        curl_setopt($ch, CURLOPT_POSTFIELDS,        http_build_query($postArray));

        $html = curl_exec($ch);
        return $html;

    }

    public function close() {
        $ch = $this->handle;
        curl_close($ch);
        return;
    }

    public function error() {

        $ch = $this->handle;
        return curl_error($ch);

    }


}

