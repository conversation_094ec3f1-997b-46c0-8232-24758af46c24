<?php

function load               ($f) {return file_exists($f) ? file_get_contents($f) : null;}
function saveAs             ($f,$d,$a=0,$addUTF8BOM=false) 
    {
        if($addUTF8BOM) {
            $d=chr(239).chr(187).chr(191).$d;
        }
        file_put_contents($f, $d, $a?FILE_APPEND:0);
    }
function appendTo           ($f,$d) {file_put_contents($f,$d,FILE_APPEND);}
function saveJson           ($jsonFile,$rawData,$nice=false) {if(!$nice) {$flags=0;} else {$flags = JSON_PRETTY_PRINT + JSON_UNESCAPED_UNICODE + JSON_UNESCAPED_SLASHES;};saveAs($jsonFile,json_encode($rawData,$flags));}
function loadJson           ($jsonFile) {if(!file_exists($jsonFile)) return null;$json = @file_get_contents($jsonFile); if(!$json) return null;$data = json_decode($json,"arrays pls");return $data;}

function preg_parse     ($regex,$subject) {
    $x = preg_match($regex,$subject,$a);
    if(!$x) $a=[];
    array_shift($a);
    return $a;
}


function arg($x="",$default=null) {

    static $arginfo = [];

    /* helper */ $contains = function($h,$n) {return (false!==strpos($h,$n));};
    /* helper */ $valuesOf = function($s) {return explode(",",$s);};

    //  called with a multiline string --> parse arguments
    if($contains($x,"\n")) {

        //  parse multiline text input
        $args = $GLOBALS["argv"] ?: [];
        $rows = preg_split('/\s*\n\s*/',trim($x));
        $data = $valuesOf("char,word,type,help");
        foreach($rows as $row) {
            list($char,$word,$type,$help) = preg_split('/\s\s+/',$row);
            $char = trim($char,"-");
            $word = trim($word,"-");
            $key  = $word ?: $char ?: ""; if($key==="") continue;
            $arginfo[$key] = compact($data);
            $arginfo[$key]["value"] = null;
        }

        $nr = 0;
        while($args) {

            $x = array_shift($args); if($x[0]<>"-") {$arginfo[$nr++]["value"]=$x;continue;}
            $x = ltrim($x,"-");
            $v = null; if($contains($x,"=")) list($x,$v) = explode("=",$x,2);
            $k = "";foreach($arginfo as $k=>$arg) if(($arg["char"]==$x)||($arg["word"]==$x)) break;
            $t = $arginfo[$k]["type"];
            switch($t) {
                case "bool" : $v = true; break;
                case "str"  : if(is_null($v)) $v = array_shift($args); break;
                case "int"  : if(is_null($v)) $v = array_shift($args); $v = intval($v); break;
            }
            $arginfo[$k]["value"] = $v;

        }

        return $arginfo;

    }

    //  called with a question --> read argument value
    if($x==="") return $arginfo;
    if (isset($arginfo[$x]["value"]) && !is_null($arginfo[$x]["value"])) {
        return $arginfo[$x]["value"];
    }

    return $default;

}


function str_putcsv( $a, $comma=',', $quote='"', $newline = "\n" ) {

    $cells = array_values($a);
    $out = '';
    $max = count($cells);
    $more = $max-1;

    foreach($cells as $i=>$e) {
        if (is_array($e)) {
            $out.=str_putcsv($e,$comma,$quote,$newline);
            continue;
        }
        $v='';
        switch (gettype($e)) {
            case "NULL":     break;
            case "boolean":  $v = $e?'true':'false'; break;
            case "integer":  $v = intval($v); break;
            case "double":   $v = rtrim(sprintf('%.7f',$e),"0."); break;
            case "string":   $v = strtr($e,[$quote=>"$quote$quote"]); break;
        }

        $v = "$quote$v$quote";
        $x = ($more-->0) ? $comma:$newline;
        $out.="$v$x";

    }
    return $out;
}
