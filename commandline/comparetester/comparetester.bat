@echo off
php "%~dpn0.php" %*
exit /b

rem         ---------------------------------------------------------------------------------------------------------
rem
rem         You can copy this batch by any other name and it will call its own php file.
rem         That is, the same name with php extension instead of bat.
rem         Even from the very same location.
rem
rem         ---------------------------------------------------------------------------------------------------------
rem         ( This file is 555 bytes long. For fun. :)
