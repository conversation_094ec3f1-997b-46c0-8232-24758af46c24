<?php
include_once("comparetester.includes.php");
main($argv);
exit;




function main($args) {

    global $Current;

    if(count($args)==1) die(help());  // see comparetester.help.php
    arg("

        -r      --save-reference        bool    Save current results as reference
        -p      --entry-point           str     Label to start script from
        -v      --verbose               bool    Say what's happening

    ");

    $cmd = "run";
    $phpCall = "cmd_$cmd"; if(!function_exists($phpCall)) die("Invalid command ($cmd)\n");
    $phpCall(arg(2));
    exit;

}
