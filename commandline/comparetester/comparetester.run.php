<?php
function fail($msg)
{
    $time = date("H:i:s");
    print "  $time |  $msg\n";
    die(1);
}


function warn($msg)
{
    $time = date("H:i:s");
    print "  $time |  $msg\n";
}


function today()
{
    static $x;
    if (!$x) $x = date("Y-m-d"); // ne változzon egy futáson belül a today
    return $x;
}


function say($s)
{
    if (!arg("verbose")) {
        return;
    }
    $time = date("H:i:s");
    print "  $time |  $s\n";
    flush();
}


function setDefaultExtFor(&$name, $ext)
{
    if (!contains(".", $name)) $name = "$name.$ext";
    return $name; // for fun
}


function getPathFor($what, $autoCreate = 1)
{
    $ttwa = dirname(dirname(__DIR__));
    $dirs = parseAsKeyValue("

        tempfiles of curly          $ttwa/runtime/comparetester/tmp-curly
        tempfiles of texter         $ttwa/runtime/comparetester/tmp-texter
        output files                $ttwa/webroot/file_storage/comparetester
        current results             $ttwa/webroot/file_storage/comparetester/current
        reference files             $ttwa/webroot/file_storage/comparetester/reference
        scripts                     $ttwa/commandline/comparetester/scripts
        reports                     $ttwa/webroot/reports
        webroot                     $ttwa/webroot

    ");
    $path = $dirs[$what];
    if (!$path) fail("There is no path defined for $what");
    if (!is_dir($path)) {
        if ($autoCreate == 0) fail("Invalid path: $path\n(for $what)");
        if ($autoCreate == 1) @mkdir($path, 0775);
        if ($autoCreate > 99) @mkdir($path, $autoCreate);
    }
    return $path;
}


function compareFiles($file1, $file2)
{
    if (!file_exists($file1)) {
        fail("Rerefence file doesn't exist ($file1)");
    }
    if (!file_exists($file2)) {
        fail("Current file doesn't exist ($file2)");
    }

    $options = join(" ", linesOf('

        --side-by-side
        --suppress-common-lines
        --ignore-space-change
        --expand-tabs

    '));
    $file1 = quoted($file1);
    $file2 = quoted($file2);

    exec("diff $file1 $file2 $options", $a);
    return $a;
}


function compareToReference($file, $client, $date = "")
{

    $date = $date ?: today();
    $name = basename($file);
    $doc1 = getDocumentName("ref", $client, $name);
    $doc2 = getDocumentName("cur", $client, $name, $date);
    $diff = compareFiles($doc1, $doc2);
    if (is_null($diff)) return false;
    if (count($diff) == 0) return false;

    $GLOBALS["appStorage"]["allDiffs"][$doc1] = $diff;
    return $diff;
}

function sortCSV($client, $originalFile)
{
    $currentResultPath = getPathFor("current results");
    $referenceFilesPath = getPathFor("reference files");
    $todayDate = today();
    $originalCurrentFilePath = "$currentResultPath/$client/$todayDate/$originalFile";
    $originalReferenceFilePath = "$referenceFilesPath/$client/$originalFile";

    if (!is_file($originalReferenceFilePath)) {
        warn("File not found: $originalReferenceFilePath");
        return -1;
    }
    if (!is_file($originalCurrentFilePath)) {
        warn("File not found: $originalCurrentFilePath");
        return -1;
    }

    $sortedCurrentDirPath = "$currentResultPath/$client/$todayDate";
    $sortedReferenceDirPath = "$referenceFilesPath/$client";
    $sortedCurrentFilePath = "$sortedCurrentDirPath/$originalFile.sorted";
    $sortedReferenceFilePath = "$sortedReferenceDirPath/$originalFile.sorted";

    if (is_file($sortedCurrentFilePath)) {
        unlink($sortedCurrentFilePath);
    }
    if (is_file($sortedReferenceFilePath)) {
        unlink($sortedReferenceFilePath);
    }

    say("Sorting $originalCurrentFilePath to $sortedCurrentFilePath");
    exec("sort $originalCurrentFilePath > $sortedCurrentFilePath");

    say("Sorting $originalReferenceFilePath to $sortedReferenceFilePath");
    exec("sort $originalReferenceFilePath > $sortedReferenceFilePath");
}

function filterLineRegex($originalName, $client, $params) {
    list(0 => $regex) = texplode(' ', $params);
    $currentResultPath = getPathFor("current results");
    $todayDate = today();
    $originalCurrentFilePath = "$currentResultPath/$client/$todayDate/$originalName";
    $newCurrentFilePath = "$currentResultPath/$client/$todayDate/$originalName.tmp";

    say(sprintf("Filter lines from %s with regex %s and write to temporary file", $originalName, $regex));
    $filterCommand = "egrep -v $regex $originalCurrentFilePath > $newCurrentFilePath";
    say(sprintf("Use filter command: %s", $filterCommand));
    exec($filterCommand);
    say(sprintf("Rename temporary file to $originalName"));
    exec("mv $newCurrentFilePath $originalCurrentFilePath");
}


function copyAsReference($file, $client, $date = "")
{

    $date = $date ?: today();
    $curr = getPathFor("current results");
    $curr = "$curr/$client/$date";
    $refs = getPathFor("reference files");
    $refs = "$refs/$client";
    $file = basename($file);

    ensurePath($refs);
    copy("$curr/$file", "$refs/$file");

    if (file_exists("$refs/$file")) say("Saved [$file] as reference");
    if (!file_exists("$refs/$file")) say("WARNING: could not save [$file] as reference");
}


function getDocumentName($kind, $clientCode, $name, $date = "")
{

    if (!$clientCode) fail("No client specified");

    $date = $date ?: today();
    $kind = substr($kind, 0, 3);

    if ($kind == "ref") {
        $path = getPathFor("reference files");
        return "$path/$clientCode/$name";
    }
    if ($kind == "cur") {
        $path = getPathFor("current results");
        return "$path/$clientCode/$date/$name";
    }

    fail("getDocumentName() called with invalid \$kind");
}


function ensurePath($path, $chmod = 0775)
{
    if (!is_dir($path)) {
        @mkdir($path, $chmod);
    }
    return is_dir($path);
}


function runScriptLines($scriptLines, $skipTo = "")
{

    $curlyTemp  = getPathFor("tempfiles of curly");
    $outputNow  = getPathFor("current results");
    $outputRef  = getPathFor("reference files");

    $validCommands = [];
    $f = file(__FILE__);
    foreach ($f as $x) {
        $x = trim($x);
        if ((isset($x[0]) && $x[0] <> "i") || !string_begins($x, 'if ($action ==')) {
             continue;
        }

        $c = string_between($x, '"', '"');
        $validCommands[] = $c;
    }


    "Set global storage references"; {

        //  megosztjuk a változókat a rekurzión keresztül is,
        //  ugyanakkor nem szennyezzük a global teret

        global $appStorage;

        $rqData     = &$appStorage["rqData"];
        $curly      = &$appStorage["curly"];
        $lastHTML   = &$appStorage["lastHTML"];
        $clientURL  = &$appStorage["clientURL"];
        $clientName = &$appStorage["clientName"];
        $clientCode = &$appStorage["clientCode"];
        $diffFound  = &$appStorage["diffFound"];
    }
    $lineNr     = 0;
    $showPwd    = 0;
    $loopLevel  = 0;
    $today      = today();
    $loggedIn   = "";

    foreach ($scriptLines as $row) {
        if ($diffFound) {
            break;
        }
        "Preformat row"; {

            $originalRow = $row;
            ++$lineNr;
            $row = trim($row);                  //  spaces don't matter
            $row = strtr($row, ["\t" => "    "]);  //  tabs don't matter
            $row = explode(" //", $row, 2)[0];    //  comments at the end of line
            $row = trim($row);                  //  trim after removing line-end comments
            if (isset($row[0]) && $row[0] == "/") continue;          //  comments at the beginning
            if (isset($row[0]) && $row[0] == "#") continue;          //  comments at the beginning
            if (!$row) continue;                 //  empty row

        }

        "Handle label skipping (goto)"; {

            if ($skipTo) {
                if (":$skipTo" == $row) $skipTo = "";
                continue;
            }
            if ($row[0] == ":") continue;
        }

        "Parse to action & params"; {

            foreach ($validCommands as $c) if (string_begins("$row ", "$c ")) {
                break;
            } else {
                $c = "";
            }
            if (!$c) fail("\n    Not sure what you mean by line $lineNr:\n     |\n     |  $row\n     |\n");
            $params = trim(string_from("$row ", "$c "));
            $action = $c;

            if (isset($params[0]) && oneof($params[0], "{,[")) {
                $json = $params; // keep original value
                $params = json_decode($json, true);
            }
        }

        "Manage for..end loops"; {

            if ($loopLevel) {
                $loopLines[] = $originalRow;
                $closingOutermostLoop = ($action == "end") && ($loopLevel == 1);
                if (!$closingOutermostLoop) {
                    if ($action == "for") ++$loopLevel;
                    if ($action == "end") --$loopLevel;
                    continue;
                }
            }
        }

        if ($action == "stop") {
            say("Line $lineNr is a stop");
            break;
        }

        if ($action == "skip to") {
            $skipTo = $params;
            continue;
        }

        if ($action == "this is") $action = "save as"; // alias

        if ($action == "copy report") {

            $path = getPathFor("reports");
            $list = glob("$path/*.csv");
            rsort($list);
            $last = $list[0];

            $infile = $last;
            $outfile = string_from($params, "as ");
            $outfile = getDocumentName("current", $clientCode, $outfile);

            $name = basename($infile);

            say("Copying report $name");
            copy($infile, $outfile);

            $isRef = arg("save-reference") ? 1 : 0;
            if ($isRef == 1) copyAsReference(basename($outfile), $clientCode);

            continue;
        }

        if ($action == "client") {
            $clientName = $params;
            $clientCode = strtolower(dashedName($clientName));
            $clientURL = "http://$clientCode.hu.login";
            $clientURL = rtrim($clientURL, "/");
            continue;
        }
        if ($action == "clienturl") {
            list($clientURL) = texplode(" ", $params, 2);
            $clientURL = rtrim($clientURL, "/");
            continue;
        }
        if ($action == "logout") {
            if (!$loggedIn) {
                say("Skipping logout (we're not logged in)");
                continue;
            }
            say("Logging out as [$loggedIn] ... ");
            $html = $curly->get("$clientURL/login/logout");
            if ($html) say("Logged out successfully");
            if (!$html) say("Something was wrong with logout");
            continue;
        }
        if ($action == "print") {
            say($params);
            continue;
        }
        if ($action == "clear") {
            if ($params == "all") {
                $rqData = [];
                continue;
            }
            $fieldList = texplode(",", $params);
            foreach ($fieldList as $field) {
                say("Clearing '$field'");
                if (isset($rqData[$field])) unset($rqData[$field]);
            }
            continue;
        }
        if ($action == "set") {
            list($field, $value) = preg_split('/\s*[=:]\s*/', trim($params)); //texplode("=",$params);
            if ($value[0] == "@") {
                $func = string_between($value, "@", "(");
                $args = string_between($value, "(", ")");
                $args = explode(",", $args);
                $phpFunction = "scriptfunc_$func";
                $value = call_user_func_array($phpFunction, $args);
            }
            else if ($value[0] == "[" AND $value[strlen($value)-1] == "]") {
                $value = substr($value, 1, -1);
                $value = explode(",", $value);
            }
            say("Setting '$field' to '" . is_array($value)?json_encode($value):$value . "'");
            $rqData[$field] = $value;
            continue;
        }
        if ($action == "get") {
            list($link, $dump) = texplode(" ", $params, 2);
            $link = trim($link);
            $link = ltrim($link, "/");
            $link = "$clientURL/$link";

            say("Loading page [$link] ...");
            $vars = $rqData ? http_build_query($rqData) : "";
            $link = $vars ? "$link?$vars" : $link;
            $html = $curly->get($link);
            if ($html === false) fail("error");
            if (!$html) fail("zero content");
            $lastHTML = $html;

            if ($dump) {
                $path = getPathFor("tempfiles of curly");
                ensurePath($path);
                $file = "$path/$dump";
                setDefaultExtFor($file, "html");
                saveAs($file, $html);
            }

            $len = strlen($html);
            say("Loaded $len bytes");

            continue;
        }
        if ($action == "post") {
            $paramsTemp = texplode(" ", $params, 2);
            if (count($paramsTemp) == 1) {
                $link = $paramsTemp[0];
            }
            else {
                list($link, $dump) = texplode(" ", $params, 2);
            }
            
            $link = trim($link);
            $link = ltrim($link, "/");
            $link = "$clientURL/$link";
            $link;
            say("Posting to [$link] ...");
            $html = $curly->post($link, $rqData);
            if ($html === false) fail("error");
            if (!$html) fail("zero content");
            $lastHTML = $html;

            if (isset($dump)) {
                $path = getPathFor("tempfiles of curly");
                ensurePath($path);
                $file = "$path/$dump";
                setDefaultExtFor($file, "html");
                saveAs($file, $html);
            }

            $len = strlen($html);
            say("Post request sent, we got $len bytes back");

            continue;
        }
        if ($action == "show login as") {
            $action = "login as";
            $showPwd = 1;
        }
        if ($action == "login as") {
            if (!$clientName) fail("Please set 'client' before 'login'");
            list($user, $pass) = texplode(",", $params, 2);
            if ($showPwd) say("Logging in to $clientURL with [$user,$pass]");
            if (!$showPwd) say("Logging in to $clientURL with [$user,********]");

            $html = $curly->post("$clientURL/login/login", [
                "LoginForm[ttwaun]" => $user,
                "LoginForm[ttwapw]" => $pass,
                "yt0" => "",
            ]);
            $err = $curly->error();
            if ($err) fail("Curly says [$err]");
            if (!$err) say("Curly is fine");
            if (contains($html, "LoginForm[ttwaun]")) fail("Login rejected by TTWA");
            if (!$html) fail("Empty response to login attempt");
            say("We're in with [$user]");
            $loggedIn = $user;
            continue;
        }

        if ($action == "save as") {

            $file = getDocumentName("current", $clientCode, $params);
            $name = basename($file);
            $path = dirname($file);
            ensurePath($path);
            saveAs($file, $lastHTML);
            say("Saved contents as '$name'");

            $isRef = arg("save-reference") ? 1 : 0;
            if ($isRef == 1) copyAsReference($file, $clientCode);
            //($isRef==0) compareToReference($file,$clientCode);

            continue;
        }

        if ($action == "setCurrent") {
            $file = getDocumentName("current", $clientCode, $params);
            $name = basename($file);
            $path = dirname($file);
            ensurePath($path);
            $isRef = arg("save-reference") ? 1 : 0;
        }

        if ($action == "for") {

            $loopLevel++;

            if ($loopLevel == 1) {
                $loopLines = [];
                $p = texplode(" ", $params, 3);
                $loopVariable = array_shift($p);
                array_shift($p);
                $loopItems = texplode(",", array_shift($p));
            }
            continue;
        }

        if ($action == "end") {

            $loopLevel--;

            if ($loopLevel == 0) {

                foreach ((array)$loopItems as $v) {
                    $ll = $loopLines;
                    foreach ($ll as &$x) {
                        $x = strtr($x, [$loopVariable => $v]);
                    }
                    unset($x);
                    runScriptLines($ll);
                    if ($diffFound) {
                        break;
                    }
                }

                $loopLines = [];
            }
            continue;
        }

        if ($action == "include") {

            $includeScriptName = $params;
            $includeScriptFile = findScript($includeScriptName);
            $includeScriptRows = @file($includeScriptFile);
            runScriptLines($includeScriptRows);
            if ($diffFound) {
                break;
            }
            continue;
        }

        if ($action == "convert") {
            //  convert grid.json to csv using 'ajax response converter'
            //  20201230-001646

            if (!$clientCode) fail("No client specified");
            list($infile, $_to, $format, $_using, $converter) = texplode(" ", $params, 5);
            if (!$converter) $converter = fileext($infile) . " to $format";
            $outfile = stripext($infile);
            $outfile = "$outfile.$format";
            $converter = trim($converter, "'" . '"' . " ");
            say("Converting [$infile] --> [$outfile] using [$converter]");
            $path = "$outputNow/$clientCode/$today";
            ensurePath($path);
            convertData($path, $infile, $outfile, $converter);

            $isRef = arg("save-reference") ? 1 : 0;
            if ($isRef == 1) copyAsReference($outfile, $clientCode);
            //($isRef==0) compareToReference($outfile,$clientCode);
            continue;
        }

        if ($action == "sortCSV") {
            if (!isset($clientCode)) {
                fail("No client specified");
            }
            sortCSV($clientCode, $params);
            continue;
        }
        if ($action == "filterLineRegex") {
            filterLineRegex($name, $clientCode, $params);
            continue;
        }

        if ($action == "check") {

            if ($isRef == 1) {
                $file = $params;
                copyAsReference($file, $clientCode);
                continue;
            }

            $file = $params;
            $diff = compareToReference($file, $clientCode);
            if (!$diff) say("Compared [$file] - OK");
            if ($diff) {
                $diffFound = true;
                warn("Compared [$file] - Something changed");
            }
            continue;
        }
    }
}



function cmd_run($scriptName)
{

    global $appStorage;

    $rqData     = &$appStorage["rqData"];
    $rqData = [];
    $curly      = &$appStorage["curly"];
    $curly = null;
    $lastHTML   = &$appStorage["lastHTML"];
    $lastHTML = "";
    $clientURL  = &$appStorage["clientURL"];
    $clientURL = "";
    $clientName = &$appStorage["clientName"];
    $clientName = "";
    $clientCode = &$appStorage["clientCode"];
    $clientCode = "";
    $allDiffs   = &$appStorage["allDiffs"];
    $allDiffs = [];

    $curly = new Curly();
    $rqData = [];

    $entryPoint = arg("entry-point");
    if ($entryPoint) print "Starting script from [:$entryPoint]\n";

    $scriptFile = findScript($scriptName);
    $rows = @file($scriptFile);
    if (!$rows) fail("Could not read $scriptFile");

    runScriptLines($rows, $entryPoint);

    $diffCount = (count($allDiffs));
    $_s = ($diffCount == 1) ? "" : "s";
    print "$diffCount difference$_s\n";
    if (!$diffCount) {
        print "!! SUCCESS !!\n";
        return;
    }

    print "\n";
    foreach ($allDiffs as $file => $lines) {
        $name = basename($file);
        print "    in $name:\n";
        foreach ($lines as $line) {
            print "      | $line\n";
        }
        print "\n";
    }

    $curly->close();
}



function findScript($name)
{

    $scriptPath = getPathFor("scripts");
    $scriptFile = "*";
    if (!is_file($scriptFile)) $scriptFile = "$scriptPath/$name";
    if (!is_file($scriptFile)) $scriptFile = "$scriptPath/$name.script"; #documentThis // változott a kiterjesztés
    if (!is_file($scriptFile)) fail("Script not found: $name\n(path: $scriptPath)");
    return $scriptFile;
}
