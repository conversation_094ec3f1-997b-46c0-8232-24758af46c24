<?php

class FileScan {

    var $results;
    var $excludedPatterns;

    function __construct() {
        $this->clearResults();
    }


    function clearResults() {
        $this->excludedPatterns = [];
        $this->includePatterns = [];
        $this->results = [];
    }


    function exclude($patterns) {
        if(is_string($patterns)) {
            $patterns = strtr($patterns,";","\n");
            $multiline = contains(trim($patterns),"\n") ?1:0;
            if($multiline==1) $patterns = preg_split('/\s*\n\s*/',trim($patterns));
            if($multiline==0) $patterns = [$patterns];
        }
        foreach((array) $patterns as $p) $this->excludedPatterns[] = $p;
    }


    function filter($patterns) {
        if(is_string($patterns)) {
            $patterns = strtr($patterns,";","\n");
            $multiline = contains(trim($patterns),"\n") ?1:0;
            if($multiline==1) $patterns = preg_split('/\s*\n\s*/',trim($patterns));
            if($multiline==0) $patterns = [$patterns];
        }
        foreach((array) $patterns as $p) $this->includePatterns[] = $p;
    }


    function isExcluded($path) {
        $patterns = &$this->excludedPatterns;
        foreach((array) $patterns as $p) if(contains($path,$p)) return true;
        return false;
    }


    function scanFrom($from,$recurseDepth=0) {

        {;;// bind some shorthands

            $f = &$this->results["files"]; if(!$f) $f=array();
            $m = &$this->results["paths"];

        };;
        {;;// get the list of files & dirs from where we are

            list($dirs,$files) = fetch_dir($from);

        };;
        {;;// remove anything that fails all filters

            $filters = $this->includePatterns;
            if(count($filters)) {
                foreach((array) $files as $index=>$fn) {
                    $ok = 0;foreach((array) $filters as $s) if(fnmatch($s,$fn)) {$ok=1;break;}
                    if(!$ok) unset($files[$index]);
                }
            }

        };;
        {;;// scan subdirs

            foreach((array) $files as $fn) {
                if($this->isExcluded("$from/$fn")) continue;
                $f[]="$from/$fn";
            }
            if($recurseDepth) {
                foreach((array) $dirs as $d) {
                    $path = "$from/$d";
                    if($this->isExcluded("$path/")) continue;
                    if(contains($path,"/.svn")) continue;
                    if(contains($path,"/_svn")) continue;
                    if(contains($path,"/.git")) continue;
                    if(isset($m[$path])) continue;

                    $m[$path]=1; // mark seen
                    $this->scanFrom($path,$recurseDepth-1);

                }
            }

        };;

    }


    function results() {
        return $this->results;
    }



}


