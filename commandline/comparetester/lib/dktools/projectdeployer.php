<?php #crossProject

#{ Dependencies

    @include_once(__DIR__."/"."strings.php");
    @include_once(__DIR__."/"."files.php");
    @include_once(__DIR__."/"."misc.php");

#}

class ProjectDeployer {

    private $c; // main config
    private $stats;
    private $ftp = 0;
    private $printCall;
    private $cache;
    private $Hooks;

    public function __construct() {

        $this->c = [];
        $this->printCall = "printf";

    }

    public function hook($eventName,$callback) {                //  manage own events
        $this->Hooks[$eventName] = $callback;
    }

    public function pr($s) {                                    //  default output call, redirected to a normal "print"
        call_user_func($this->printCall,$s);
    }

    public function setPrintCall($functionName) {               //  hook for output capturing, //see "ProjectDeployer::setPrintCall"
        $this->printCall = $functionName;
    }

    public function simulate($configFile="",$printFunc="") {    //  just like "run" but no real operations done
        $this->config("simulate only",1); $this->run($configFile,$printFunc);
        $this->config("simulate only",0);
    }

    public function ftpEnsureConnection() {                     //  connect to FTP if necessary (on-demand)

        if(!function_exists("ftp_connect")) die(
            "Your PHP is not equipped with ftp functions.\n".
            "(I'm running on a PHP ".phpversion().", in case you have multiple versions installed.)\n"
        );

        if($this->ftp) return;

        if($this->config("ftp")) {
            $p = $this->config("ftp");
            $this->ftp = new Ftp;
            $resp = $this->ftp->open($p["host"],$p["user"],$p["pass"],[
                "allowNB" => 0,
            ]);
            if($resp<>"ok") {$this->pr("FTP has a problem ($resp)\n");return false;}
            $this->pr("Connected to FTP server\n");
        }

    }

    public function run($configFile="") {                       //  runs the specified config

        if(!$this->config("configLoaded")) $this->parseConfig($configFile);
        $sim = $this->config("simulate only");

        $logo = $this->config("logo")?:"";
        if($logo) foreach($logo as $fn) print @file_get_contents($fn);

        $this->stats = [
            "updated" => 0,
            "created" => 0,
            "skipped" => 0,
            "ignored" => 0,
        ];

        $sr = $this->config("source root")[0]; $sr=rtrim($sr,"/");
        $tr = $this->config("target root")[0]; $tr=rtrim($tr,"/");
        $ex = $this->config("exceptions");
        $fs = $this->config("folders");

        foreach($fs as $p) {

            $p = strtr($p,["\t"=>"    "]);
            list($p,$sw) = explode(" /",$p,2);
            list($p1,$p2) = preg_split("/\s\s+/",$p);
            if(!$p2) $p2=$p1;

            if($p1[0]<>"/") $p1="$sr/$p1";
            if($p2[0]<>"/") $p2="$tr/$p2";

            $this->copyFolder($p1,$p2,$sw);

        }

        $actionsTaken = $this->stats["updated"] + $this->stats["created"];
        if(!$actionsTaken) $this->pr("\n    It was already up to date.\n    No actions taken.\n\n");

        $this->pr("\n".str_repeat("-",79)."\n");
        foreach($this->stats as $act=>$ctr) $this->pr(sprintf("%6s file(s) %s\n",$ctr,$act));

        if($this->ftp) $this->ftp->close();

        $this->lastDeployTime("right now");

        if($sim) {
            print "\nPress Enter when you're done looking around ...\n";
            fgets(fopen('php://stdin','r'));
        }

    }

    public function configContains($group,$value) {             //  whether config(xx) has a specific line
        $a = $this->config($group);
        if($a[$value]) return 1;                // has a key like this
        return in_array($value,(array)$a);      // has a value like this
    }

    public function config($arg1=null,$arg2=null) {             //  get/set configuration items

        if(!isset($arg1)) return $this->c;
        if(!isset($arg2)) return $this->c[$arg1];
        if(is_array($arg1)) {foreach($arg1 as $key=>$val) $this->config($key,$val);return;}

        $key = $arg1;
        $val = $arg2;
        $this->c[$key] = $val;
        return $val;

    }

    public function loadConfig($fileName) {                     //  load a config file (rarely called by user)
        $this->parseConfig($fileName);
    }

    public function parseConfig($fileName) {                    //  same thing as loadConfig

        if(!file_exists($fileName)) return false;
        $f = file($fileName);

        {;;// merge with includes
            $f2 = [];
            foreach($f as $s) {
                if(!string_begins($s,"include ")) {$f2[]=$s;continue;}
                $incFile = trim(string_from($s,"include "));
                $incLines = @file($incFile);
                foreach((array)$incLines as $s) $f2[]=$s;
            }
            $f = $f2;
        };;

        $g = ""; // group
        foreach($f as $s) {

            $s = chop($s);if(!$s) continue;
            if(string_begins(trim($s),"//")) continue;
            $s = strtr($s,["\t"=>"    "]);
            list($s,$comment) = explode(" //",$s,2);
            if(!trim($s)) continue;

            $s = preg_replace("/^\s+/","\t",$s);  $indent = ($s[0]=="\t")?1:0;
            $s = trim($s,"\t\r\n :");
            if($indent==1) $this->c[$g][] = $s;
            if($indent==0) $g = $s;

        }

        $showWhat = firstof($this->c["show"][0],"actions");
        if($showWhat=="all")      $this->config("show",["created","updated","ignored","skipped"]);
        if($showWhat=="actions")  $this->config("show",["created","updated"]);

        {;;// convert certain sections to key-value lists

            $sectionsToBeConverted = $s2bc=[
                "ftp",              //  ftp connection details
                "whatever-else",    //  for future extensions
            ];
            foreach($s2bc as $sectionName) {
                $sect = $this->c[$sectionName]; if(!$sect) continue;
                foreach($sect as $i=>$x) {
                    list($key,$val)=texplode(":",$x,2);
                    $sect[$key]=$val;
                    unset($sect[$i]);
                }
                $this->c[$sectionName] = $sect;
            }

        };;

        $this->config("configLoaded",1);

        return $this->c;

    }

    public function parseSwitches($s) {                         //  parse a string like "/a /b /c" into ["a"=1,"b"=>1,"c"=>1]
        $out = [];
        $a = preg_split("/[\/\s]+/",$s);
        foreach($a as $word) $out[$word]=1;
        return $out;
    }

    public function isException($name) {                        //  decides if a certain file is excluded or not
        static $exlist; if(!$exlist)$exlist = $this->config("exceptions");
        foreach((array)$exlist as $x) if(contains($name,$x)) return true;
        return false;
    }

    public function trigger($event,$args="") {                  //  trigger something hooked with hook()
        $a = func_get_args();
        array_shift($a);
        $f = $this->Hooks[$event];
        if(is_callable($f)) call_user_func_array($f,$a);
    }

    public function copyFolder($source,$target,$switches="") {  //  deal with one folder - loop{copyFile}

        $showWhat = $this->config("show");

        if($this->isException($source)) return;
        $sw = $this->parseSwitches($switches);

        $source = trim(strtr($source,"\\","/"));
        $target = trim(strtr($target,"\\","/")); if(!is_dir($target)) $this->mkdirOnce($target);

        list($dirs,$files) = fetch_dir($source);
        foreach((array)$files as $fn) {

            $sourceFile = "$source/$fn";
            $targetFile = "$target/$fn";

            $this->trigger("beforeCopyFile", $sourceFile,$targetFile); $report = $this->copyFile($sourceFile,$targetFile,$sw);
            $this->trigger("afterCopyFile" , $sourceFile,$targetFile,  $report);

            $word = string_until($report,"\t");
            if(false!==array_search($word,$showWhat)) $this->pr("    $report\n");
            ++$this->stats[$word];

        }

        $isFTP = whether($this->configContains("ftp","host"));
        if(!$isFTP) {
            if(is_dir($target)) {
                $mtMax = 0;foreach((array)$files as $fn) $mtMax = max($mtMax,@filemtime("$target/$fn"));    // calculate latest modtime
                @touch($target,$mtMax);                                                                     // set folder modtime to that
            }
        }

        if($sw["r"]) {
            foreach((array)$dirs as $dir) {
                if($this->isException($dir)) continue;
                $this->copyFolder("$source/$dir","$target/$dir",$switches);
            }
        }

    }

    public function mkdirOnce($targetDir) {                     //  make directory but remember not to repeat it

        $this->ftpEnsureConnection();
        $sim = $this->config("simulate only");
        if($this->config("ftp")) $ftpc = $this->ftp->getConnection();

        {;;// do it only once - return on repeated calls

            static $happened=[];
            if($happened[$targetDir]) return;
            $happened[$targetDir]=1;

        };;
        {;;// simple case for local paths

            if(!$this->config("ftp")) {
                if(!$sim) @mkdir($targetDir,0775,true);
                return;
            }

        };;
        {;;// ftp-mkdir them one by one
            $dirs = explode("/",$targetDir);
            $path = "";
            foreach($dirs as $d) {
                if($path) $path.="/"; $path.=$d;
                if(!$sim) @ftp_mkdir($ftpc,$path);
            }
        };;

    }

    public function copyFile($ffn1,$ffn2,$sw) {                 //  copy one file - this is where the magic happens

        {;;// skip exceptions

            if($this->isException($ffn1)) {
                $word="ignored";
                return "$word   $ffn1"; // different!
            }

        };;
        {;;// get strategy

            $skn = $this->strategy("skip newer"     ); #documentThis
            $skx = $this->strategy("skip existing"  ); #documentThis
            $sku = $this->strategy("skip unchanged" ); #documentThis
            $igs = $this->strategy("ignore size"    ); #documentThis
            $cpa = $this->strategy("copy all"       ); #documentThis
            $sim = $this->config("simulate only");

        };;
        {;;// if we can't access it, skip it

            if(!@filemtime($ffn1)) goto _RETURN_;

        };;
        {;;// fast-skip if unchanged since last deploy
            if($sku) {
                $unchanged = whether( (filemtime($ffn1) < $this->lastDeployTime()) );
                if($unchanged) {$word="skipped";goto _RETURN_;}
            }
        };;
        {;;// gather file info

            $isFTP  = whether($this->configContains("ftp","host"));
            $osCopy = whether($this->configContains("strategy","use windows copy"));

            if($isFTP) {

                #missingPart // size checking - wonder if it's possible on FTP? with line endings and stuff?

                $word = "";
                $this->ftpEnsureConnection();
                $this->mkdirOnce(dirname($ffn2));
                $ftpc = $this->ftp->getConnection();
                $lmod = filemtime($ffn1);
                $mdtm = @ftp_mdtm($ftpc,$ffn2);

                $noNews    = whether($lmod<=$mdtm); // we don't have a newer version
                $isMissing = whether($mdtm==-1);    // outside file doesn't exist
                $isThere   = !$isMissing;           // outside file does exist

            }else{

                $word = "";
                $lmod = @filemtime($ffn1);
                $mdtm = @filemtime($ffn2);
                $tdir = dirname($ffn2); if(!is_dir($tdir)) if(!$sim) {mkdir($tdir,0775,true);}

                $noNews    = whether($lmod<=$mdtm);         // we don't have a newer version
                $isMissing = whether(!file_exists($ffn2));  // outside file doesn't exist
                $isThere   = !$isMissing;                   // outside file does exist

                if($isThere) {
                    $sameSize = whether(@filesize($ffn1) === @filesize($ffn2));
                    if(!$sameSize) if(!$igs) {
                        $isThere = false;
                        $noNews  = false;
                    }
                }

            }

        };;
        {;;// decision logic

            do{

                if($cpa) if($isMissing)     {$word="created";break;}
                if($cpa) if($isThere)       {$word="updated";break;}
                if($skx) if($isThere)       {$word="skipped";break;} // you said 'skip existing' - it's there, so skip it
                if($skn) if($noNews )       {$word="skipped";break;} // you said 'skip newer' - outside is newer (or equal), skip it
                if($isMissing)              {$word="created";break;} // it's not there so we create it
                if(1)                       {$word="updated";break;} // if we made it to this point, it's an update

            }while(0); //see "cut 20151119.214351", old logic

            if($sim)             goto _RETURN_; // we're just kidding
            if($word=="skipped") goto _RETURN_; // it's skipped

        };;
        {;;// cast the spell now

            if( $isFTP) $this->ftp->copyAs($ffn1,$ffn2,"replace"); #improveThis // missing: touch()
            if(!$isFTP) if( $osCopy) {exec("copy \"$ffn1\" \"$ffn2\"");@touch($ffn2,$lmod);}
            if(!$isFTP) if(!$osCopy) {copy($ffn1,$ffn2);@touch($ffn2,$lmod);}

        };;

        _RETURN_:

        return "$word\t$ffn2";

    }

    public function lastDeployTime($set=false) {                //  returns time() of last deployment (stored in a local file)

        $sim = $this->config("simulate only");
        if(!$set) if($x= $this->cache["last deploy time"]) return $x;
        $dir = firstof(
            $this->config("workfile path")[0],
            $this->config("source root")[0],
        ".");
        $file = firstof($this->config("time marker")[0],"$dir/deploy.lasttime.txt");
        if($set) if(!$sim) str2file($file,date("Y-m-d H:i:s"));
        $this->cache["last deploy time"] = 0; if(!file_exists($file)) return 0;
        $this->cache["last deploy time"] = $out = strtotime(trim(file_get_contents($file)));
        return $out;

    }

    public function strategy($s) {                              //  returns if a given strategy element is part of the plan

        $x = array_search($s,(array)$this->c["strategy"]);
        if($x===false) return false;

        return true;

    }



}


#documentThis
    /*
    strategy:
        skip unchanged
        skip existing
        skip newer
    */



