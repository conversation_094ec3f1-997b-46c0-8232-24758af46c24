<?php #crossProject // see 'd:/http/dktools/files.php'


function ftail($fname,$linesback,$linesize) {
    if(!file_exists($fname)) return false;
    ++$linesize; // include linefeed
    $seekback = $linesize*$linesback;
    $f=fopen($fname,"r");
    $ok=fseek($f,-$seekback,SEEK_END);
    if($ok<0) fseek($f,0);
    $s=fread($f,$seekback);
    fclose($f);
    $s=trim($s);
    $a=explode("\n",$s);
    $a=array_slice($a,-$linesback);
    return $a;
}


function tail($filename,$lines=50) {
    if(!file_exists($filename)) return array();
    $f=fopen($filename,"r");
    $size=filesize($filename);
    fseek($f,$size-($lines*250));
    $s=fread($f,999999);
    fclose($f);
    $s=str_replace("\r","",$s);
    $t=explode("\n",$s);
    array_pop($t);
    return $t;
}


function str2file($filename,$content,$fmode="none") {
    $f=fopen($filename,"w");
    fwrite($f,$content);
    fclose($f);
    if($fmode!="none") @chmod($filename,$fmode);
}


function fetch_dir($path) {
    if (!is_dir($path))return false;
    $dirs  = array();
    $files = array();
    if(!is_readable($path)) return false; // e.g. access denied
    $d = opendir($path);
    if(!$d) return false; // another case of access denied
    while (($f=readdir($d))!==false) {
        if (($f!=".")&&($f!="..")) {
            if (is_dir("$path/$f")) {
                $dirs[]=$f;
            }else{
                $files[]=$f;
            }
        }
    }
    closedir($d);
    $o=array($dirs,$files);
    return $o;
}


function fetch_filelist($path,$grep="") {
    if (!is_dir($path))return false;
    $files = array();
    $d=opendir($path);
    if($grep[0] == "/") { // ha regexpet greppelunk
        while (($f=readdir($d))!==false) {
            if (($f!=".")&&($f!="..")) {
                if ((!$grep)||(preg_match($grep,$f))) {
                    if (!is_dir("$path/$f")) $files[]=$f;
                }
            }
        }
    }else{
        while (($f=readdir($d))!==false) {
            if (($f!=".")&&($f!="..")) {
                if ((!$grep)||(strpos($f,$grep)!==false)) {
                    if (!is_dir("$path/$f")) $files[]=$f;
                }
            }
        }
    }
    closedir($d);
    return $files;
}


function fileage($fn) {
    if(!file_exists($fn)) return time();
    $out=time()-filemtime($fn);
    return $out;
}


function filecontents($filename) {
    if(!file_exists($filename)) return ""; // avoid warning
    $out = @file_get_contents($filename);
    return $out;
}


function file_getpart($filename,$part) {
    $s=filecontents($filename);
    $a=explode("[[-",$s);
    foreach($a as $p) {
        list($pname,$p)=explode("-]]",$p,2);
        if ($part==$pname) return $p;
    }
}


function delfile($fn) {
    if(!file_exists($fn)) return 0;
    $ok=@unlink($fn);
    return $ok;
}


function appendstr($filename,$s,$fmode="none") {
    $f=@fopen($filename,"a");
    @fwrite($f,$s);
    @fclose($f);
    if($fmode!="none") @chmod($filename,$fmode);
}


function append_locked($fileName,$s,$chmod=0) {
    $f=@fopen($fileName,"a");
    for($i=0;$i<3;++$i) {
        if($lock=@flock($f,LOCK_EX)) break;
        usleep(mt_rand(1,100));
    }
    if($lock) @fwrite($f,$s);
    @fclose($f); // this will release the lock too
    if($chmod) chmod($fileName,$chmod);
}


function fileext($s) {
    $n = strrpos($s,".");
    if($n===false) return "";
    return substr($s,$n+1);
}


function stripext($s) {
    $s=preg_replace("/\.[^\.]*$/","",$s);
    return $s;
}


function splitFileByTime($filename,$units="months") {

    $m = substr($units,0,3);
    if($m=="yea") $dtmMask = "Y";
    if($m=="mon") $dtmMask = "Y.m";
    if($m=="day") $dtmMask = "Y.m.d";
    if($m=="hou") $dtmMask = "Y.m.d.H";
    if($m=="min") $dtmMask = "Y.m.d.H.i";
    if($m=="sec") $dtmMask = "Y.m.d.H.i.s";

    $fmod = @filemtime($filename);
    if(!$fmod) return NULL; // file does not exist

    $mt = date($dtmMask,$fmod);
    $ct = date($dtmMask,time());
    if($mt==$ct) return 0;

    $p = strrpos($filename,".");
    $archname = substr($filename,0,$p).".$mt".substr($filename,$p);
    $ok = @rename($filename,$archname);
    if($ok) return $archname;
    if(!$ok) return NULL; // probably just renamed by another process

}


function trickyPath($p) {
    // returns 1 if your string contains backward moves
    return strpos("-/$p/","/../")?1:0; // dash is to avoid zero position index (vs false)
}


function loadFileDescriptions($path) {

    $out = [];

    $f=@file("$path/descript.ion"); if(!$f) return [];
    foreach($f as $x) {
        if($x[0]=='"') list($name,$desc) = explode('" ',ltrim($x,'"'),2);
        if($x[0]<>'"') list($name,$desc) = explode(' ',$x,2);
        $out[$name]=$desc;
    }

    return $out;

}


function fixSlashes($s,$slash="/") {
    $from = "\\/";
    $into = "$slash$slash";
    return strtr($s,$from,$into);
}


function slashed($s) {
    return rtrim($s,"/")."/";
}


function unslashed($s) {
    return rtrim($s,"/");
}


function subfolderOf($root) {
    $a = func_get_args();
    array_shift($a);
    $out = slashed($root).join("/",$a);
    return $out;
}


function fileIn($path,$name) {
    $out = slashed($path).$name;
    return $out;
}


function relativePathTo($root,$path) {
    $root = fixSlashes($root);
    $path = fixSlashes($path);
    $root = slashed($root);
    if(string_begins($path,$root)) return string_from($path,$root);
    $out = "";
    for($i=0;$i<999;++$i) {
        if($root=="/") return $out.string_from($path,$root);
        $root = fixSlashes(dirname($root));
        $root = slashed($root);
        $out.="../";
        if($root=="/") return $out.string_from($path,$root);
        if(string_begins($path,$root)) return $out.string_from($path,$root);
    }

}


function writeIfDifferent($filename,$newContents) { // returns whether changed
    $oldContents = @file_get_contents($filename);
    if($oldContents===$newContents) return false;
    file_put_contents($filename,$newContents);
    return true;
}


function replaceThingsInFile($fileName,$replaceRule,$extraData=null) {

    $singleFileMode = (contains($fileName,"->")) ? 0:1;
    if($singleFileMode==0) {list($oldName,$newName) = texplode("->",$fileName);}
    if($singleFileMode==1) {$oldName = "$fileName";$newName = "$filename.new";}

    $thisStep = "replaceThingsInFile()";
    $f1 = fopen($oldName,"r"); if(!$f1) return problemWith($thisStep,"File not found");
    $f2 = fopen($newName,"w"); if(!$f2) return problemWith($thisStep,"Could not create new file");
    $lineNr = 0;
    while(!feof($f1)) {
        $s = fgets($f1);
        if(is_callable ($replaceRule)) $s = $replaceRule($s,$lineNr,$extraData);
        if(is_array    ($replaceRule)) $s = strtr($s,$replaceRule);
        ++$lineNr;
        fwrite($f2,$s);
    }
    fclose($f2);
    fclose($f1);

    if($singleFileMode) {
        $ok = @unlink($oldName); if(!$ok) if(file_exists($oldName)) return problemWith($thisStep,"Could not delete old file");
        $ok = @rename($newName,$oldName); if(!$ok)                  return problemWith($thisStep,"There was a problem with renaming the file");
        if(!file_exists($newName))                                  return problemWith($thisStep,"Could not create new file");
    }

    return true;

}


function globDepth($n,$mask="*") {
    $x = "*";
    $o = $mask; while($n-->0) {$o="$o,$x/$mask";$x="*/$x";}
    $o = "{".$o."}";
    return $o;
}


function globDeep($startPathMLS,$fileMask="*",$maxDepth=2) {

    $files = "";
    $lists = linesOf($startPathMLS);
    $depthScan = globDepth($maxDepth,$fileMask);
    foreach($lists as $source) {
        $fileList = glob("$source/$depthScan",GLOB_BRACE);
        $files.=join("\n",$fileList)."\n";
    }

    $files = fixSlashes($files);
    return $files;

}



