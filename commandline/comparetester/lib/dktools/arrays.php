<?php #crossProject // see 'd:/http/dktools/arrays.php'

$sortByField_fieldname="";
$sortByField_asc=1;

define("SORTBYFIELD_ASCENDING",true);
define("SORTBYFIELD_DESCENDING",false);

if(!function_exists("array_column")) {;;
    function array_column($a,$colkey) {
        if (!is_array($a)) return "";
        $out=array();
        foreach ($a as $k=>$v) $out[$k]=$v[$colkey];
        return $out;
    }
}


function array_1() {
    $a=func_get_args();
    array_unshift($a,"");
    unset($a[0]);
    return $a;
}


function array_flatten($a,$kcol,$vcol) {
    if (!is_array($a)) return "";
    $out=array();
    foreach ($a as $k=>$v) $out[$v[$kcol]]=$v[$vcol];
    return $out;
}


function array_nonzero($a,$keep_keys=1) {
    $o=array();
    if($keep_keys==1) {foreach($a as $key=>$val) if ($val) $o[$key]=$val;}
    if($keep_keys==0) {foreach($a as $val) if ($val) $o[]=$val;}
    return $o;
}


function array_remove_all($a,$value1="",$value2="",$valueN="") {
    $vr = func_get_args();unset($vr[0]);
    $vr = array_flip($vr);
    foreach($a as $key=>$val) if($vr[$val]) unset($o[$key]);
    return $a;
}


function array_grep($s,$a) {
    $out=array();
    if (strpos($s,"^")===0) {
        $s=substr($s,1);
        foreach($a as $key=>$value) {
            if ((strpos($value,$s)===0)) $out[$key]=$value;
        }
    }else{
        foreach($a as $key=>$value) {
            if (!(strpos($value,$s)===FALSE)) $out[$key]=$value;
        }
    }
    return $out;
}


function array_keygrep($s,$a) {
    $out=array();
    if (strpos($s,"^")===0) {
        $s=substr($s,1);
        foreach($a as $key=>$value) {
            if ((strpos($key,$s)===0)) $out[$key]=$value;
        }
    }else{
        foreach($a as $key=>$value) {
            if (!(strpos($key,$s)===FALSE)) $out[$key]=$value;
        }
    }
    return $out;
}


function array_pick($d,$namelist) {
    $a = $namelist;
    if(!is_array($a)) $a = texplode(",",$a);
    foreach($a as $var) {
        if(strpos($var,"*")!==false) {
            $mask=str_replace("*","",$var);
            foreach($d as $fn=>$fv) if(strpos($fn,$mask)!==false) $out[$fn]=$fv;
        }else{
            $out[$var]=$d[$var];
        }
    }
    return $out;
}


function array_vlist_range($n1,$n2) {
    $a=array();
    for($i=$n1;$i<=$n2;++$i) $a[$i]=$i;
    return $a;
}


function array_stripslashes($a) {
    foreach($a as $key=>$val) {
        if(is_array($val)) {
            $a[$key]=array_stripslashes($val);
        }else{
            $a[$key]=stripslashes($val);
        }
    }
    return $a;
}


function array_copy_key(&$a,$oldkey,$newkey) {
    $a[$newkey]=$a[$oldkey];
}


function array_rename_key(&$a,$oldkey,$newkey) {
    $a[$newkey]=$a[$oldkey];
    unset($a[$oldkey]);
}


function array_safekeys($a) {
    $validChars="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_.-[]0123456789!";
    foreach($a as $key=>$val) {
        $n=strspn($key,$validChars);
        if($n==strlen($key)) continue;
        unset($a[$key]);
    }
    return $a;
}


function array_seq($min,$max,$fmt="^") {
    $out=array();
    if($fmt=="^") for($i=$min;$i<=$max;++$i) $out[$i]=$i;
    if($fmt!="^") for($i=$min;$i<=$max;++$i) $out[$i]=str_replace("^",$i,$fmt);
    return $out;
}


function sortByField_cmp($a,$b) {
    global $sortByField_fieldname;
    global $sortByField_asc;
    $x=$sortByField_fieldname;
    if($sortByField_asc) {
        if($a[$x]>$b[$x]) return 1;
        if($a[$x]<$b[$x]) return -1;
        return 0;
    }else{
        if($a[$x]<$b[$x]) return 1;
        if($a[$x]>$b[$x]) return -1;
        return 0;
    }
}


function sortByField(&$tb,$field,$isAsc) {
    global $sortByField_fieldname;
    global $sortByField_asc;
    $sortByField_fieldname=$field;
    $sortByField_asc=$isAsc;
    usort($tb,"sortByField_cmp");
}


function array2str($a,$arrow=" => ",$newline="\n") {
    $s=array();
    if(sizeof($a)) {
        foreach($a as $k=>$e) {
            if(is_array($e)) {
                $ea=array2str($e,$arrow,$newline);
                $ex=str_replace($newline,"$newline\t",$ea);
                if(count($e)==0) $ex="empty array";
                $e="array$newline\t".$ex;
                $e=rtrim($e,"$newline\t");
            }
            $out.="$k$arrow$e$newline";
        }
    }
    //$s=join($newline,$s);
    return $out;
}


function str2array($s,$arrow=" => ",$newline="\n") {
    if(!$s) return;
    $a=explode($newline,$s);
    $subarray="";
    foreach($a as $s) {
        if(!contains($s,$arrow)) continue;
        list($key,$val) = explode($arrow,$s);
        if($key[0]=="\t") {
            $subarray.=substr($s,1).$newline;
        }else{
            if($subarray) {
                $out[$lastkey]=str2array($subarray);
                $subarray="";
            }
            $lastkey=$key;
            $out[$key]=$val;
        }
    }
    if($subarray) $out[$lastkey]=str2array($subarray);
    return $out;
}


function file2array($fn,$arrow=" => ",$newline="\n") {
    $s=filecontents($fn);
    $a=str2array($s,$arrow,$newline);
    return $a;
}


function array2file($fn,$a,$arrow=" => ",$newline="\n") {
    $s=array2str($a,$arrow,$newline);
    str2file($fn,$s);
}


function pickfields($a,$keyprefix,$remove=0) {
    $o=array();
    $n=strlen($keyprefix);
    foreach($a as $fn=>$fv){
        if(substr($fn,0,$n)==$keyprefix) {
            if ($remove) $fn=substr($fn,$n);
            $o[$fn]=$fv;
        }
    }
    return $o;
}


function cs_array($cs,$comma=",") {
    return explode($comma,$cs);
}


function array_flagmap($a,$mapValue=1) {
    $out=array();
    foreach($a as $key=>$val) $out[$val]=$mapValue;
    return $out;
}


function array_import_prefixed(&$destArray,$prefix,$sourceArray) {
    if(is_array($sourceArray)) foreach($sourceArray as $key=>$val) $destArray["$prefix$key"]=$val;
}


function array_import(&$destArray,$sourceArray) {
    array_import_prefixed($destArray,"",$sourceArray);
}


function array_intval(&$a,$fieldList,$force=0) {
    if(!is_array($fieldList)) $fieldList=explode(",",$fieldList);
    foreach($fieldList as $key) {
        if(!array_key_exists($key,$a)) if(!$force) continue;
        $a[$key]=intval($a[$key]);
    }
    return $a;
}


function fieldsof($a,$fieldOrder="",$sep=",") {
    // csak azért van, hogy list($a,$b,$c)-vel ki lehessen nyerni a
    // mezõket egy tömbbõl, akkor is, ha hash

    if(!$fieldOrder) return array_values($a);

    $out = array();
    $fds = explode($sep,$fieldOrder);
    foreach($fds as $f) $out[]=$a[$f];

    return $out;

}


function array_indexby($a,$field,$keep_original_index_as="") {
    $k = $keep_original_index_as;
    $out=array();
    foreach($a as $key=>$val) {
        if($k) $val[$k]=$key;
        $index = $val[$field];
        $out[$index]=$val;
    }
    return $out;
}


function forcearray(&$x,$evenIfNotEmpty=0) {
    if($evenIfNotEmpty) {// whatever value it had, make it an empty array now
        if(!is_array($x)) $x=array();
        return (array)$x;
    }else{// do it only if it was empty (default)
        if(!$x) $x=array();
        return (array)$x;
    }
}


function array_firstvalue($a) {if(!is_array($a)) return null;reset($a);return current($a);};;


function array_firstkey  ($a) {if(!is_array($a)) return null;reset($a);return key    ($a);};;


function array_lastvalue ($a) {if(!is_array($a)) return null;end($a);  return current($a);};;


function array_lastkey   ($a) {if(!is_array($a)) return null;end($a);  return key    ($a);};;


function array_trim($a) {
    if(!is_array($a)) return trim($a);
    foreach($a as $i=>$x) $a[$i] = array_trim($x);
    return $a;
}


function array_duplex($a,$removeEmpties=1) {
    $out = [];
    if(!$removeEmpties) foreach($a as $x) $out[$x]=$x;
    if( $removeEmpties) foreach($a as $x) if($x) $out[$x]=$x;
    return $out;
}


function arrayToPlaintext($a,$minKeyLength=25) { //see "arrayToPlaintext / arrayFromPlaintext"

    $out = "\n";

    $ThreeSpaces = "   ";
    foreach($a as $sectionName=>$sect) {
        $line = str_repeat("-",strlen($sectionName)+2);
        $out.="\n$sectionName\n$line\n\n";
        $len = $minKeyLength;foreach($sect as $key=>$val) $len=max($len,strlen($key));
        foreach($sect as $key=>$val) $out.=sprintf("\t%-".$len."s%s%s\n",$key,$ThreeSpaces,$val);
        $out.="\n\n";
    }

    return $out;
}


function arrayFromPlaintext($text,$singleLevel=0) { //see "arrayToPlaintext / arrayFromPlaintext"

    $out = [];

    $ThreeSpaces = "   ";
    $text = expandTabs($text);
    $f = explode("\n",$text);
    if($singleLevel) {

        // simple case

        foreach($f as $s) {
            if(trim($s,"\t\r\n -")==="") continue;
            list($key,$val) = texplode($ThreeSpaces,ltrim($s),2);
            $out[$key]=$val;
        }

    }else{

        // 2-level case

        $section = "unknown"; // should never appear
        $lead = -1; // extra leading spaces to be removed (when the whole shit is indented)
        foreach($f as $s) {
            if(trim($s,"\t\r\n -")==="") continue;
            if($lead==-1) $lead=strspn($s," ");
            if($lead>0) $s=substr($s,$lead);
            if($s[0]<>" ") {
                $section = trim($s);
                continue;
            }
            list($key,$val) = texplode($ThreeSpaces,ltrim($s),2);
            $out[$section][$key]=$val;
        }

    }

    return $out;

}


function array_putfirst($a,$keysToPutFirst) {

    $k = $keysToPutFirst;
    if(!is_array($k)) $k = texplode(",",$k);
    $isMoved = array_flagmap($k);

    $out = [];
    foreach($k as $key) if(isset($a[$key])) $out[$key] = $a[$key];
    foreach($a as $key=>$val) if(!$isMoved[$key]) $out[$key] = $val;
    return $out;

}


