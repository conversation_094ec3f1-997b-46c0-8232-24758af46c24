<?php #crossProject // see 'd:/http/dktools/strings.php'

function mb_ucfirst         ($s) {
    $out=mb_strtoupper(mb_substr($s,0,1)).mb_substr($s,1);
    return $out;
}


function string_from        ($s,$part,$including=0) {
    $n=strpos($s,$part);
    if($n!==false) if(!$including) $n+=strlen($part);
    return substr($s,$n);
}


function string_until       ($s,$part,$including=0) {
    return string_till($s,$part,$including);
}


function string_till        ($s,$part,$including=0) {
    $n=strpos($s,$part);
    if($n!==false) if($including) $n+=strlen($part);
    return substr($s,0,$n);
}


function string_back        ($s,$part,$including=0) {
    $n = strrpos($s,$part);
    if($including) $n+=strlen($part);
    return substr($s,0,$n);
}


function cut_until          (&$s,$part,$skipDelimiter=0) {
    $n=strpos($s,$part);
    $out=substr($s,0,$n);
    if($skipDelimiter) $n+=strlen($part);
    $s=substr($s,$n);
    return $out;
}


function string_between     ($s,$part1,$part2) {
    return string_until(string_from($s,$part1),$part2);
}


function string_begins      ($main,$part) {
    return (substr($main,0,strlen($part))==$part);
}


function string_ends        ($main,$part) {
    if(!$part) return 1;
    $out=0; if(substr($main,-strlen($part))==$part) $out=1;
    return $out;
}


function string_skip        () {
    $a = func_get_args();
    $s = array_shift($a);
    foreach($a as $x) $s = string_from($s,$x);
    return $s;
}


function texplode           ($sep,$source,$limit="sky",$trimChars=" \t\r\n") {
    if($limit=="sky") $a = explode($sep,$source);
    if($limit<>"sky") $a = explode($sep,$source,$limit);
    foreach($a as $i=>$v) $a[$i] = trim($v,$trimChars);
    return $a;
}


function zeropad            ($nr,$digits) {
    return str_pad($nr,$digits,"0",STR_PAD_LEFT);
}


function nonzero            ($n) {
    return ($n==0?"":$n);
}


function singleline         ($s) {
    $s=preg_replace("/\s+/"," ",$s);
    $s=trim($s);
    return $s;
}


function trimlines          ($a,$removeEmptyLines=1) {
    foreach($a as $key=>$val) {
        $val=trim($val);
        if($removeEmptyLines) if(!$val) continue;
        $out[$key]=$val;
    }
    return $out;
}


function quotesafe          ($s,$mode=1) {
    if($mode==1) { // normal html/js protection
        $s=str_replace('"','&quot;',$s);
        $s=str_replace("'","&#39;",$s);
    }
    if($mode==2) { // js string protection for single quote
        $s=str_replace("'","'+\"'\"+'",$s);
    }
    if($mode==3) { // backslash for doubles, html for single
        $s=str_replace("'","&#39;",$s);
        $s=str_replace('"','\"',$s);
    }
    if($mode==4) { // html for doubles, backslash for single
        $s=str_replace('"',"&quot;",$s);
        $s=str_replace("'","\\'",$s);
    }
    if($mode==11) { // double quotes only
        $s=str_replace('"','&quot;',$s);
    }
    if($mode==12) { // single quotes only
        $s=str_replace("'","&#39;",$s);
    }
    return $s;
}


function oneof              ($s,$lst,$sep=",") {

    if(is_array($lst)) return in_array($s,$lst);
    if(contains($lst,"\n")) return in_array($s,linesOf($lst));
    return contains("$sep$lst$sep","$sep$s$sep");

}


function forceOneOf         ($s,$possibleValues,$defaultIfNone="",$sep=",") {
    if(oneof($s,$possibleValues,$sep)) return $s;
    return $defaultIfNone;
}


function cutwb              ($s,$maxlen,$maxword=20) {
    if (strlen($s)<=$maxlen)return $s;
    $s=substr($s,0,$maxlen);
    if (!strpos(substr($s,-$maxword)," "))return "$s ...";
    $s=preg_replace("/\s[^\s]*$/","",$s);
    return "$s ...";
}


function seems_utf8         ($s) {
    $out = whether(strpbrk($s,chr(0xC2).chr(0xC3).chr(0xC4).chr(0xC5).chr(0xC6).chr(0xC7)));
    return $out;
}


function utf8_smart_encode  ($s) {
    if(is_array($s)) {
        foreach($s as $key=>$str) if(!is_numeric($str)) $s[$key]=utf8_smart_encode($str);
        return $s;
    }
    if(seems_utf8($s)) return $s;

    if( function_exists("mb_convert_encoding")) $s = mb_convert_encoding($s,"utf-8");
    if(!function_exists("mb_convert_encoding")) $s = utf8_encode($s);

    return $s;
}


function utf8_smart_decode  ($s) {
    if(is_array($s)) {
        foreach($s as $key=>$str) if(!is_numeric($str)) $s[$key]=utf8_smart_decode($str);
        return $s;
    }
    if(!seems_utf8($s)) return $s;

    if( function_exists("mb_convert_encoding")) $s = mb_convert_encoding($s,"iso-8859-2","utf-8");
    if(!function_exists("mb_convert_encoding")) $s = utf8_decode($s);

    return $s;
}


function onespace           ($s) {
    $s=preg_replace("/ +/"," ",$s);
    return $s;
}


function nolf               ($s,$level=1) {
    $s=strtr($s,"\n\r","  "); if($level==1) return $s;
    $s=strtr($s,array("\\n"=>" ","\\r"=>" ")); if($level==2) return $s;
    // we don't know what level 3 is :)
    return $s;
}


function xlat_nonquoted     ($s,$quoteChar,$xlatArray) {

    // protect quoted contents

    $a=explode($quoteChar,$s);
    $prot = 0;
    foreach($a as $k=>$x) {
        if(!$prot) $a[$k]=strtr($x,$xlatArray);
        $prot=1-$prot;
    }
    $out=join($quoteChar,$a);

    return $out;
}


function occurs             ($needle,$haystack,$offset=0) {
    $x=@strpos($haystack,$needle,$offset);
    if($x===false)return 0;
    return 1;
}


function contains           ($haystack,$needle,$offset=0) {
    $x=@strpos($haystack,$needle,$offset);
    if($x===false)return 0;
    return 1;
}


function containsWords      ($haystack,$needles,$rule="any",$matchCase=1,$separator=",") {

    if(!$matchCase) $haystack = strtolower($haystack);
    if(is_array($needles)) {
        $words = array();
        if(!$matchCase) foreach($needles as $i=>$v) $words[]=strtolower($v); // not very efficient in repeated checks
        if( $matchCase) $words = $needles;
    }else{
        if(!$matchCase) $needles=strtolower($needles);
        $words = explode($separator,$needles);
    }
    if($rule=="any") {$out=0;foreach($words as $w) if(contains($haystack,$w)) ++$out;}
    if($rule=="all") {$out=1;foreach($words as $w) if(!contains($haystack,$w)) $out=0;}

    return $out;
}


function containsOnly       ($s,$chars) {

    static $maps; if(!$maps) $maps=[];
    $map = $maps[$chars];if(!$map) $map = array_flip(str_split($chars,1));$map[$chars[0]]=1; // nonzeroes!
    foreach(str_split($s,1)as $ch) if(!$map[$ch]) return false;
    return true;

}


function mb_containsOnly    ($s,$chars) {

    static $maps; if(!$maps) $maps=[];
    $map = $maps[$chars];if(!$map) {
        $map = [];
        $n=mb_strlen($chars);
        for($i=0;$i<$n;++$i) {$ch=mb_substr($chars,$i,1);$map[$ch]=1;}
    }
    $n = mb_strlen($s);
    for($i=0;$i<$n;++$i) {
        $ch = mb_substr($s,$i,1);
        if(!$map[$ch]) return false;
    }

    return true;

}


function validLettersFor    ($cc) {

    $en = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
    if($cc=="en") return $en;
    if($cc=="hu") return $en."áéíóúöüőűÁÉÍÓÚÖÜŐŰ";
    if($cc=="EU") return $en."ÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖ×ØÙÚÛÜÝÞßàáâãäåæçèéêëìíîïðñòóôõö÷øùúûüýþÿőűŐŰ";
    return "";

}


function strpos_last        ($haystack,$needle) {
    $size=strlen($haystack);
    $pos=strpos(strrev($haystack),strrev($needle));
    if($pos===false) return false;
    return $size-$pos-strlen($needle);
}


function implantData        ($sourceString,$dataHash,$removeEmptyTags=0,$implantBegin="[[",$implantEnd="]]") {
    $ib=$implantBegin;
    $ie=$implantEnd;
    $a=explode($ib,$sourceString);
    $out="";$count=0;
    foreach($a as $x) {
        if($count++) { //skip first
            list($key,$rest)=explode($ie,$x,2);
            if(isset($dataHash[$key])) {
                $val=$dataHash[$key];
                $out.=$val.$rest;
            }else{
                if($removeEmptyTags==1) $out.=              $rest; // remove empty tag
                if($removeEmptyTags==0) $out.= $ib.$key.$ie.$rest; // keep tag if no $data element exists
            }
        }else{
            $out.=$x;
        }
    }
    return $out;
}


function group1000          ($n,$digits=0,$decPoint=".",$groupSep=" ") {
    return number_format($n,$digits,$decPoint,$groupSep);
}


function purestr            ($s) {
    static $hunChars1,$hunChars2,$hunCharsDefined;
    $hunCharsDefined=0;
    if(!$hunCharsDefined) {
        $hunChars1 = "áéíóúöüőűÁÉÍÓÚÖÜŐŰ"; $hunChars1 = mb_convert_encoding($hunChars1,"iso-8859-2","utf-8");
        $hunChars2 = "aeiouououAEIOUOUOU";
        $hunCharsDefined=1;
    }
    if(is_array($s)) {
        foreach($s as $key=>$val) $out[$key]=purestr($val);
        return $out;
    }else{
        $s=mb_convert_encoding($s,"iso-8859-2","utf-8");
        $s=strtr($s,$hunChars1,$hunChars2);
        return strtolower($s);
    }
}


function str_more           ($s,$maxlen,$moreSign="...") {
    if(strlen($s)<=$maxlen) return $s;
    $s=substr($s,0,$maxlen-strlen($moreSign)).$moreSign;
    return $s;
}


function iso2utf            ($s,$mode=1) {
    // @ 2011.07.10, 07:12:58
    if(is_array($s)) {
        foreach($s as $key=>$str) if(!is_numeric($str)) $s[$key]=iso2utf($str,$mode);
        return $s;
    }
    if($mode==1) return encode_337_chars(utf8_smart_encode($s));
    if($mode==2) {
        $s=utf8_smart_encode($s);
        $s=strtr($s,array(
            //s//: side note: minden C3 karaktert a C5 megfelelõjére kellene alakítani, ha igazán szépen akarod.
            char_iso_o => char_utf_o,
            char_iso_u => char_utf_u,
            char_iso_O => char_utf_O,
            char_iso_U => char_utf_U,
        ));
        return $s;
    }
}


function utf2iso            ($s,$mode=1) {
    // @ 2011.07.11, 11:52:23
    if(is_array($s)) {
        foreach($s as $key=>$str) if(!is_numeric($str)) $s[$key]=utf2iso($str,$mode);
        return $s;
    }
    if($mode==CONVERT_MOBILE_ACCENTS) $s=convert_all_mobile_accents($s);
    if($mode==CONVERT_337_AND_MOBILE) $s=convert_all_mobile_accents($s);
    if($mode==CONVERT_337_AND_MOBILE) $s=encode_337_chars($s); // fontos, hogy ez van késõbb!
    if($mode==CONVERT_337_CHARS     ) $s=encode_337_chars($s);
    $s = utf8_smart_decode($s);
    return $s;
}


function hexdump            ($s) {
    $x=bin2hex($s);
    $n=strlen($x);
    $o="";for($i=0;$i<$n;$i+=2)$o.=substr($x,$i,2)." ";
    return $o;
}


function hexdump_old        ($s) {
    $sl=strlen($s);
    for($i=0;$i<$sl;++$i) {$x=dechex(ord($s[$i]))." ";if(strlen($x)==2) $x="0".$x;$h.=$x;}
    $h=trim($h);
    return $h;
}


function count_one_char     ($s,$ch) {
    $a = count_chars($s,1);
    $out = intval($a[ord($ch[0])]);
    return $out;
}


function fixLineEndings     ($s,$properEnding="\n") {
    $s = strtr($s,array(
        // this is tricky :) see ante:"fixLineEndings"
        "\r\n"  => $properEnding,
        "\n\r"  => $properEnding,
        "\r"    => $properEnding,
        "\n"    => $properEnding,
    ));
    return $s;
}


function encaps             ($data,$thingsAround,$hotSpot="^") {
    if(!contains($thingsAround,$hotSpot)) $thingsAround = str_replace("><",">$hotSpot<",$thingsAround);
    $out = strtr($thingsAround,array($hotSpot=>$data));
    return $out;
}


function getAnyTagAround    ($s,$part,$removeTags=1,$trim=1) {

    $n = strpos($s,$part);
    $p = strrpos(substr($s,0,$n),"<");
    $s = substr($s,$p);
    $tag = preg_replace("/<(\w+).*/s","$1",$s);
    $n = strpos($s,"</$tag");
    $n = strpos($s,">",$n);
    $s = substr($s,0,$n+1);

    if($removeTags==0) ;
    if($removeTags==1) {$s=preg_replace("/^[^>]+>/","",$s);$s=preg_replace("/<[^<]+$/","",$s);}
    if($removeTags==2) $s=strip_tags($s);
    if($trim) $s=trim($s);

    return $s;

}


function getTagAround       ($s,$part,$tagStrings="<td...</td>") {

    if(contains($tagStrings,"...")) {
        list($tagOpen,$tagClose)=explode("...",$tagStrings,2);
    }else{
        $tagOpen  = "<$tag";
        $tagClose = "</$tag>";
    }

    $n = strpos($s,$part);
    $p = strrpos(substr($s,0,$n),$tagOpen); if($p===false) return;
    $s = substr($s,$p);
    list($s) = explode($tagClose,$s,2);
    $s.=$tagClose;

    return $s;
}


function lpad               ($s,$n,$c=" ") {
    return str_pad($s,$n,$c,STR_PAD_RIGHT);
}


function rpad               ($s,$n,$c=" ") {
    return str_pad($s,$n,$c,STR_PAD_LEFT);
}


function tabsep             () {
    $a = func_get_args();
    $out = join("\t",$a);
    return $out;
}


function expandTabs         ($s,$tabSize=4) {
    $a = explode("\n",$s);
    foreach((array) $a as $index=>$row) {
        $e=&$a[$index];
        //if(!preg_match('/[^\s]\t+[^\s]/',$e)) continue;

        $bits = explode("\t",$e);
        $e="";
        foreach($bits as $bit) {
            $e.=$bit." ";
            $n = strlen($e)%$tabSize;
            if($n) $e.=str_repeat(" ",$tabSize-$n); // wtmh
        }
        $e = rtrim($e);
        $e = trim($e,"\r");

    }
    $s = join("\n",$a);
    return $s;
}


function safeString         ($s,$mode=0) {

    $s = var_export($s,1);
    $s = substr($s,1,-1);
    if($mode==1) $s = strtr($s,["\n"=>"\\n","\r"=>"\\r"]);
    if($mode==2) $s = strtr($s,["\n"=>"\\n","\r"=>"\\r","\t"=>"\\t"]);
    return $s;

}


function removeAccents      ($s) {

    $xlat1 =
        chr(0xE1).chr(0xE9).chr(0xED).chr(0xF3).chr(0xFA).chr(0xF6).chr(0xFC).chr(0xF5).chr(0xFB).
        chr(0xC1).chr(0xC9).chr(0xCD).chr(0xD3).chr(0xDA).chr(0xD6).chr(0xDC).chr(0xD5).chr(0xDB)
    ;
    $xlat2 = "aeiouououAEIOUOUOU";
    $s = strtr($s,$xlat1,$xlat2);

    $xlat = array(
        chr(0xC3).chr(0x83) => "",
        chr(0xC2).chr(0xA1) => chr(0xE1),
        chr(0xC2).chr(0xA9) => chr(0xE9),
        chr(0xC2).chr(0xBC) => chr(0xFC),
        chr(0xC2).chr(0xB6) => chr(0xF6),
        chr(0xE2).chr(0x80) => chr(0xD3),
    );
    $s = strtr($s,$xlat);

    $s = mb_convert_encoding($s,"iso-8859-2","utf-8");
    $s = strtr($s,$xlat1,$xlat2);
    return $s;
}


function pregSliceUp        ($regex,$s,$limit=-1) {
    return preg_split($regex,$s,$limit,PREG_SPLIT_DELIM_CAPTURE);
}


function pregPickAll        ($regex,$s,$which=1) {
    $a = [];
    preg_match_all($regex,$s,$a);
    return $a[$which];
}


function multisplit         ($a,$subject) {
    $preg = [];
    foreach($a as $s) {
        $preg[]=preg_quote($s);
    }
    $preg = join("|",$preg);
    preg_match_all("/($preg)/",$subject,$walls);
    $walls = $walls[0];
    array_unshift($walls,"");
    $parts = preg_split("/($preg)/",$subject);

    $out = [];foreach($parts as $n=>$p) $out[$n] = ["wall"=>$walls[$n],"part"=>$p];
    return $out;
}


function normalPath         ($s) {
    $s = realpath($s);
    $s = strtr($s,"\\","/");
    return $s;
}


function normalSlashes      ($s) {
    $s = strtr($s,"\\","/");
    return $s;
}


function dashedName         ($s) {

    $s = preg_replace("/\W+/","-",$s); // n-7vzs3gkb
    $s = trim($s,"-");
    return $s;

}


function functionName       ($s,$mask="%s") {

    $s = strtolower($s);
    $s = preg_replace("/[^a-z0-9]+/","_",$s);
    $s = trim($s,"\r\t\n _");
    $s = ($mask==="%s") ? $s : sprintf($mask,$s);
    return $s;

}


function fine               (&$var,$format="^",$trim=0,$caretChar="^") {
    if($format=="^") $format=$caretChar;
    $s=$var; if($trim) $s=trim($s);
    if($s=="") return "";
    $out=str_replace("^",$s,$format);
    $var=$out;
    return $out;
}


function strfine            ($var,$format="^",$trim=0,$caretChar="^") {
    fine($var,$format,$trim,$caretChar);
    return $var;
}


function htmlsafe           ($s) {
    // yes it's like htmlspecialchars - only without the return-empty annoyance

    $s = strtr($s,[
        "'" => "&apos;",
        '"' => "&quot;",
        '<' => "&lt;",
        '>' => "&gt;",
        '&' => "&amp;",

    ]);
    return $s;

}


function tpl                ($html,$p) {

    $out = "";
    if(is_string($p)) {
        $lines = preg_split("/\s*\n\s*/",trim(strtr($p,["\t"=>"    "])));
        $p = [];foreach($lines as $s) $p[] = preg_split("/\s\s+/",trim($s));
    }
    foreach((array)$p as $x) {
        ///if(is_string($x)) {$out.=$x;continue;}
        if(is_object($x)) $x=(array)$x;
        if(!is_array($x)) $x = ["x"=>$x];
        $h = implantData($html,$x,1);
        $out.=$h;
    }
    return $out;
}


function expandLeadingTabs  ($s,$tabSize=4,$maxTabDepth=16) {

    $tr = [];$s1="";$s2="";$spaces=str_repeat(" ",$tabSize);$tab="\t";
    for($i=0;$i<$maxTabDepth;++$i) {$s1.=$tab;$s2.=$spaces;$tr["\n$s1"]="\n$s2";$tr["\r$s1"]="\r$s2";}
    $tr = array_reverse($tr);

    $s = "\n".$s;
    $s = strtr($s,$tr); // a very elegant & superfast way!
    $s = substr($s,1);

    return $s;

}


function repairIndentation  ($s,$tabSize=4) {

    $s = expandLeadingTabs($s,$tabSize);
    $a = explode("\n",$s);
    $in = 999;foreach($a as $x) if(trim($x)) $in = min($in,strspn($x," "));
    $in = ($in-$in%$tabSize); // only integer multipliers of tabSize - wonder if we truly need this
    $sp = str_repeat(" ",$in);
    $tr = ["\n$sp"=>"\n","\r$sp"=>"\r"];

    $s = "\n".$s;
    $s = strtr($s,$tr);
    $s = substr($s,1);

    return $s;

}


function quo                ($s) {
    $s = strtr($s,['"'=>'&quot;']);
    return $s;
}


function apos               ($s) {
    $s = strtr($s,["'"=>'&apos;']);
    return $s;
}


function parseAsFixedTable  ($s) {

    if(contains($s,"\t")) $s = expandTabs($s);

    $s = preg_replace('/^\s*\n/',"",$s); // keep leading spaces of first valuable line
    $s = rtrim($s);
    $lines = explode("\n",$s);
    $header = array_shift($lines);

    $spacePrefix = 3; $spfx=str_repeat(" ",$spacePrefix); // help regex find the first column
    preg_match_all('/\s\s+([^\s]+)/',"$spfx$header",$a,PREG_OFFSET_CAPTURE);
    $a; // now we have the positions and the field names

    $matches = $a[1];
    $positions = [];
    $lengths = []; $prevPos = 0;
    foreach($matches as $m) {
        $name = $m[0];
        $pos  = $m[1] - $spacePrefix; // we added 2 spaces at start, remember?
        $len  = 0; // gets filled later
        $positions[$name] = ["pos"=>$pos,"len"=>$len];
        $lengths[] = $pos - $prevPos;  $prevPos=$pos;
    }
    array_shift($lengths); foreach($positions as $name=>$x) $positions[$name]["len"]=array_shift($lengths)?:9999;
    $positions; // now we have an array of each fieldname => [pos,len], //see "20170126.160224"

    $data = [];
    foreach($lines as $nr=>$line) {
        if(string_begins(trim($line),"---")) continue; // a line with only whitespace and dash -> separator
        foreach($positions as $key=>$p) {
            $value = trim(mb_substr($line,$p["pos"],$p["len"]));
            $data[$nr][$key] = $value;
        }
    }

    return $data;
}


function linesOf            ($mls,$keepIndents=0) {
    if(!$keepIndents) return preg_split('/\s*\n\s*/',trim($mls));
    $a = preg_split('/\s*\n/',$mls);
    $m = 999;foreach($a as $x) if($x) {$z = strspn($x," ");$m = min($m,$z);}
    foreach($a as &$x) $x = substr($x,$m);
}


function columnsOf          ($s) {
    return preg_split('/   +/',trim(strtr($s,["\t"=>"   "])));
}


function tokenSplit         ($s,$delimiters) {
    $out = [];
    $p = 0;
    while(1) {
        $l = strlen($s);
        $n = strcspn($s,$delimiters); $p+=$n;
        $x = substr($s,0,$n);
        $c = substr($s,$n,1);
        $s = substr($s,$n+1);
        $out[] = [$x,$c,$p,"value"=>$x,"place"=>$p,"limit"=>$c];
        ++$p;
        if($s===false) break;
        if(strlen($s)>=$l) break; // wtf
    }
    return $out;
}


function quoted             ($s,$q='"') {
    if(!string_begins($s,$q)) $s="$q$s$q";
    return $s;
}


function unquoted           ($s,$q='"') {
    if(substr($s,0,1)<>$q) return $s;
    if(substr($s, -1)<>$q) return $s;
    if(strlen($s)==1)      return $s;
    $s = substr($s,1,-1);  return $s;
}


function indentLevel        ($s,$tabs=4) {
    if(!$tabs) return strspn($s,' ');
    $n = strspn($s,"\t ");
    $k = substr($s,0,$n);
    $k = expandTabs($k."x");
    return strlen($k)-1;
}


function parseIndentedText  ($text,$lineOffset=0) {

    $a = explode("\n",$text);
    foreach($a as &$x) $x=rtrim($x); unset($x);
    $m = 999;foreach($a as $x) if($x) $m=min($m,indentLevel($x));

    $out = [];
    $add = false;
    foreach($a as $lineNr=>$x) {
        $x = rtrim($x);
        $k = indentLevel($x);
        $keyLevel = ($k==$m) && ($x);
        if($keyLevel) {
            if($add) $out[] = $add;
            $add["head"] = trim($x);
            $add["line"] = $lineNr + $lineOffset;
            $add["text"] = "";
            $add["vars"] = [];
        }else{
            if(!$add["head"]) continue;
            $x = substr($x,$m);
            $add["text"].="$x\n";
            if(contains($x,": ")) {
                list($key,$val) = texplode(": ",$x,2);
                $add["vars"][$key] = $val;
            }
        }
    }
    if(rtrim($add["head"])) $out[] = $add;

    foreach($out as &$item) {
        $text = rtrim($item["text"]); if(!$text) continue;
        $line = $item["line"] + 1;
        $item["subs"] = parseIndentedText($text,$line);
    }

    return $out;

}


function scanBlockByIndent  (array $lines,$startLine) {
    $ct  = count($lines);
    $m   = indentLevel($lines[$p1]);
    $out = [$startLine => $lines[$startLine]];

    for($i=$startLine+1;$i<=$ct;++$i) {
        $s = $lines[$i];
        $s = rtrim($s);
        if($s) {
            $n = indentLevel($s);
            if($n<=$m) break;
        }
        $out[$i] = $lines[$i];
    }

    return $out;

}


function replaceIfStarts    ($s,$startingWith,$replaceWith) {
    $o = $startingWith;
    $r = $replaceWith;
    if(!string_begins($s,$o)) return $s;
    $s = $r.substr($s,strlen($o));
    return $s;
}


function quotefix           ($s) {
    $s = strtr($s,'`','"');
    return $s;
}


function trimLinesOf        ($text) {
    return preg_replace('/[\t \r]*\n[\t ]*/',"\n",$text);
}


function extractPart        ($s,$part1,$part2,$default="",$including=0) {

    //  a well-defined version of string_between
    //  if $part1 or $part2 does not exist in the string, or is empty, default is returned.
    //  only if both are given and found, we return the string between them.
    //  also, if this string is empty, default value will be used.

    if("$part1"==="") return $default;
    $n = strpos($s,$part1);                     if($n===false)     return $default;
    $n = ($including)?$n:$n+strlen($part1);     if("$part2"==="")  return $default;
    $k = strpos($s,$part2,$n+1);                if($k===false)     return $default;
    if($including) $k+=strlen($part2);
    return substr($s,$n,$k-$n);

}


function alignBy            ($substringToAlign,$arrayOfStrings,$maxPad=999) {

    $a = $arrayOfStrings;
    $l = $substringToAlign;
    $m = 0;foreach($a as $x) {$p=strpos($x,$l);if($p>$m) if($p<=$maxPad) $m=$p;}
    foreach($a as $i=>$x) {
        $p = explode($l,$x,2);
        $p[0] = str_pad($p[0],$m);
        $a[$i] = join($l,$p);
    }
    return $a;

}


function parseAsKeyValue    ($mls) {
    $out = [];
    foreach(linesOf($mls) as $x) {
        $x = strtr(trim($x),["\t"=>"   "]);
        list($k,$v) = texplode("   ",$x,2);
        $out[$k]=$v;
    }
    return $out;
}


function parseAsKeyArray    ($mls,$arrayDelimiter=",") {
    $out = [];
    foreach(linesOf($mls) as $x) {
        $x = strtr(trim($x),["\t"=>"   "]);
        list($k,$v) = texplode("   ",$x,2);
        $out[$k]=texplode($arrayDelimiter,$v);
    }
    return $out;
}


function encodeFilename     ($x) {
    $a = explode(" ",$x);
    $a = array_map(rawurlencode,$a);
    $x = join(" ",$a);
    $x = strtr($x,"%","!");
    return $x;
}


function decodeFilename     ($x) {
    $x = strtr($x,"!","%");
    $x = rawurldecode($x);
    return $x;
}






