<?php #crossProject

class Texter {

	private $Config;
	private $Text;
	private $autoBeauty = true;
	private $ColToName = [];
	private $NameToCol = [];
	private $NumericFields = [];
	private $BaseURL = "";
	private $ScriptHalted = 0;

	//----------------------------------------------------------------------------------------------------------------  Public interface

	public function __construct($config=[]) {
		$this->Config = parseAsKeyValue('

			working path        .

		');
		if(!is_array($config)) $config = parseAsKeyValue($config);
		foreach($config as $key=>$val) $this->set($key,$val);
	}

	public function set($key,$value) {
		$this->Config[$key] = $value;
	}

	public function load($filename) {
		$path = $this->Config["working path"];
		$file = (contains('/',$filename)) ? $filename : "$path/$filename";
		$file = @file_get_contents($filename) ?: "";
		$this->Text = $file;
	}

	public function setText($text) {
		$this->Text = $text;
	}

	public function getText() {
		return $this->Text;
	}

	public function getJSON() {

		$a = explode("\n",$this->Text);
		$s = "";
		if(!$this->ColToName) return [];

		foreach($a as $x) {
			$fields = explode("\t",$x);
			$jsonItem = [];foreach($this->ColToName as $n=>$c) $jsonItem[$c] = $fields[$n];
			$out[] = $jsonItem;
		}

		return json_encode($out,JSON_PRETTY_PRINT);
	}

	public function applyScript($scriptTextOrFilename) {
		$script = $scriptTextOrFilename;
		if(!contains($script,"\n")) $script = @file_get_contents($script);

		$script = strtr($script,["\r\n"=>"\n","\r"=>"\n"]);
		$depth = 0;
		$sl = strlen($script);
		for($i=0;$i<$sl;++$i) {
			$c = $script[$i];
			if($c=="\\") {++$i;if($i>=$sl)break;continue;}
			if($c=="(") {++$depth;if($depth==1) $from=$i;}
			if($c==")") {--$depth;if($depth==0) $till=$i;}
			if($c==")") if($depth==0) {
				$part = substr($script,$from,$till-$from+1);
				$part = strtr($part,"\n",chr(7));
				$script = substr($script,0,$from).$part.substr($script,$till+1);
			}
		}

		$lines = linesOf($script);
		$this->ScriptHalted = 0;
		foreach($lines as $line) {
			$this->performLine($line);
			if($this->ScriptHalted) break;
		}

	}

	public function performLine($line) {

		$line = strtr($line,chr(7),"\n");
		$commands = linesOf('

			apply regex
			log
			progress total
			stop
			snapshot
			beauty off
			go from
			load
			save as
			clear cache
			raw fetch from
			fetch from
			convert encoding from
			skip until
			delete from
			join lines
			split after
			split before
			delete each
			erase
			field limit after
			field limit before
			field limit
			skip line
			remove first line
			remove last line
			remove empty lines
			remove dupes by
			sort by
			numeric
			remove columns
			remove column
			remove lines with
			keep lines with
			keep lines between
			reduce tags
			remove tags
			remove all tags
			reduce spaces
			compress spaces
			tabs to spaces
			replace
			trim lines from
			trim lines until
			trim lines between
			we got
			fetch all
			from
			fix nbsp

		');
		foreach($commands as $cmd) {
			$cmd = rtrim($cmd,".\r\n\t ");
			if(string_begins(" $line "," $cmd ")) {
				$pms = string_from(trim($line),trim($cmd));
				$pms = trim($pms); $pmsRaw = $pms;
				$pms = $this->solveQuotes($pms);
				$mth = $this->commandToMethod($cmd);
				call_user_func(["Texter",$mth],$pms,$pmsRaw);
				break;
			}
		}

	}

	//----------------------------------------------------------------------------------------------------------------  Script commands

	private function cmdSnapshot($p) {
		$work = $this->Config["working path"];
		$work = fixslashes(realpath($work));
		str2file("$work/output/snapshot.$p.txt",$this->Text,0664);
	}
	private function cmdApplyRegex($p) {
		list($rxFrom,$rxInto) = explode(" --> ",$p);
		if($rxFrom[0]<>"/") $rxFrom = "~$rxFrom~";
		$rxInto = strtr($rxInto,[
			"\\\\"=>"\\",
			"\\t"=>"\t",
			"\\n"=>"\n",
			"\\r"=>"\r",
		]);
		$this->Text = preg_replace($rxFrom,$rxInto,$this->Text);
	}
	private function cmdBeautyOff($p) {
		$this->autoBeauty = false;
	}
	private function cmdClearCache($p) {

		$work = $this->Config["working path"];
		$g = glob("$work/cache-fopen/*.htm");
		foreach($g as $file) @unlink($file);

	}
	private function cmdCompressSpaces($p) {
		$this->Text = trim(preg_replace("/[\t ]+/"," ",$this->Text));
	}
	private function cmdConvertEncodingFrom($p) {

        $this->Text = mb_convert_encoding($this->Text,"utf-8",$p);

	}
	private function cmdDeleteEach($p) {

		$this->Text = strtr($this->Text,[$p=>""]);

	}
	private function cmdDeleteFrom($p) {

		if(contains($this->Text,$p)) $this->Text = string_till($this->Text,$p);

	}
	private function cmdErase($p) {

		$this->Text = strtr($this->Text,[$p=>" "]);

	}
	private function cmdFetchAll($p) {

		$baseurl = $this->BaseURL;
		list($colName,$rest) = explode(" ",$p,2);
		$s = $rest;
		if(contains($s,"from")) $baseurl = trim(string_from($s,"from "));

		$colNr      = $this->NameToCol[$colName];
		$scriptName = $data["script.basename"];
		$subdir     = "output/$scriptName-$colName";
		if(!is_writable($subdir)) mkdir($subdir);

		$a = explode("\n",trim($this->Text));
		$this->Text = "";

		$logCount = 0;
		$logName  = "cache/fetch-all.".date("Ymd.His").".log";
		foreach($a as $row) {
			$c = explode("\t",$row);
			$v = $c[$colNr]; if(!trim($v)) continue;
			$f = $this->LinkToFilename($v);
			$ffn = "$subdir/$f.htm";
			$url = trim("$baseurl$v");

			usleep(1000*500);

			$fetched = fetchInto($ffn,$url);
			$newStuff = string_from(string_from($fetched,"<body"),">");
			$newStuff = preg_replace("/[\t ]+/"," ",$newStuff);
			$newStuff = preg_replace("/[\r\n]\s+/","\n",$newStuff);
			$this->Text .= $newStuff;

			++$count;
			progress("$count    Fetching '$url'");

			appendstr($logName,sprintf("Fetched link %s, %s bytes for %s\n",++$logCount,strlen($newStuff),$url),0644);

		}

	}
	private function cmdFetchFrom($p) {

		$s = $this->fetchWithCache($p,3*Hours);
		$s = string_from($s,"<body",1);
		$s = preg_replace("/[\t ]+/"," ",$s);
		$s = preg_replace("/[\r\n]\s+/","\n",$s);
		$this->Text = $s;

	}
	private function cmdFieldLimit($p) {

		if(contains($p," ... ")) { // regex trick!
			$parts = explode(" ... ",$p);
			foreach($parts as $i=>$x) $parts[$i] = preg_quote($x);
			$rx = join(".*",$parts);
			$rx = '/[ \t]*'.$rx.'[ \t]*/';
			$this->Text = preg_replace($rx,"\t",$this->Text);
			return;
		}

		$this->Text = strtr($this->Text,[$p=>"\t"]);

	}
	private function cmdFieldLimitAfter($p) {

		$this->Text = strtr($this->Text,[$p=>"$p\t"]);

	}
	private function cmdFieldLimitBefore($p) {

		$this->Text = strtr($this->Text,[$p=>"\t$p"]);

	}
	private function cmdFixNbsp($p) {

		$this->Text = strtr($this->Text,["&nbsp;"=>" "]);

	}
	private function cmdGoFrom($p) {

		$this->BaseURL = $p;

	}
	private function cmdJoinLines($p) {

		$this->Text = preg_replace("/[\r\n]/","\t",$this->Text);

	}
	private function cmdKeepLinesBetween($p) {
		die("Not supported?");
	}
	private function cmdKeepLinesWith($p) {
		$a = explode("\n",$this->Text);
		$s = "";foreach($a as $x) if(contains($x,$p)) $s.="$x\n";
		$this->Text = $s;
	}
	private function cmdLoad($p) {

		$work = $this->Config["working path"];
		if(contains($p,"(")) {
			$link = string_between($p,"(",")");
			$file = trim(string_until($p,"("));
		}else{
			$link = "";
			$file = trim($p);
		}

		$file = "$work/cache/$file";
		if(!file_exists($file)) file_put_contents($file,fetchWithCache($url));
		$s = filecontents($file);
	    $s = preg_replace("/\r\n/","\n",$s);
	    $this->Text = $s;

	}
	private function cmdNumeric($p) {
		$colName = $p;
		$colNr = $this->NameToCol[$colName];
		$a = explode("\n",$this->Text);
		foreach($a as $i=>$x) {
			$fields = explode("\t",$a[$i]);

			$w = &$fields[$colNr];
			$w = trim($w);
			$w = preg_replace('/[^0-9.,]/'," ",$w);
			$w = preg_replace('/[., ]+$/',"",$w);
			$w = preg_replace('/[., ]([0-9]{3})/',"$1",$w);

			$a[$i] = join("\t",$fields);
		}
		$this->Text = join("\n",$a);
		$this->NumericFields[$p] = 1;
	}
	private function cmdProgressTotal($p) {
		//	ezt csak tudomásul vesszük, de igazából mivel nincs display, nem kell semmire.
		//	merugye ez volt az, hogy hol járunk százalékban, még amikor a Rudiék látni akarták
		//	a progress bart, és akkor itt meg lehetett adni a teljeset. Most már felesleges,
		//	ezek a folyamatok a háttérben futnak.
	}
	private function cmdRawFetchFrom($p) {

        $this->Text = file_get_contents($p);

	}
	private function cmdReduceSpaces($p) {
		$this->Text = trim(preg_replace("/[\t ]+/"," ",$this->Text));
	}
	private function cmdReduceTags($p) {
		if(!$p) return;
		$tags = texplode(",",$p);
		$this->Text = trim(preg_replace('/<('.join("|",$tags).')[^>]*>/','<$1>',$this->Text));
	}
	private function cmdRemoveAllTags($p) {
		$this->Text = strip_tags($this->Text);
	}
	private function cmdRemoveTags($p) {
		if(!$p) { 
            $this->cmdRemoveAllTags($p); 
            return;
        }
		$tags = texplode(",",$p);
		$this->Text = trim(preg_replace('/<\/?('.join("|",$tags).')[^>]*>/','',$this->Text));
	}
	private function cmdRemoveColumn($p) {

		$this->cmdRemoveColumns($p);

	}
	private function cmdRemoveColumns($p) {

		$colNames = preg_split("/\s*,\s*/",$p);
		$colNrs = array();foreach($colNames as $colName) $colNrs[]=$this->NameToCol[$colName];
		$a = explode("\n",$this->Text);
		$s = "";
		foreach($a as $x) {
			$fields = explode("\t",$x);
			foreach($colNrs as $cn) $fields[$cn]="";
			$fields = array_nonzero($fields);
			$x = join("\t",$fields);
			$s.="$x\n";
		}
		$this->Text = $s;

		// rebuild field name table
		$weGot = ",".join(",",$this->ColToName).",";foreach($colNames as $cn) $weGot = str_replace(",$cn,",",",$weGot);
		$weGot = trim($weGot,",");
		$this->ColToName = explode(",",$weGot);
		$this->NameToCol = array_flip($this->ColToName);

	}
	private function cmdRemoveDupesBy($p) {

		$colName = $p;
		$colNr = $name2col[$colName];
		$a = explode("\n",$this->Text);
		$t = array();
		foreach($a as $x) {
			$fields = explode("\t",$x);
			$key = $fields[$colNr];
			$t[$key] = $x;
		}
		$this->Text = join("\n",$t);

	}
	private function cmdRemoveEmptyLines($p) {

		$this->Text = preg_replace("/\n+/","\n",$this->Text);

	}
	private function cmdRemoveFirstLine($p) {

		$this->Text = string_from($this->Text,"\n");

	}
	private function cmdRemoveLastLine($p) {

		$this->Text = preg_replace("/\n[^\n]+\n*$/","\n",$this->Text);

	}
	private function cmdRemoveLinesWith($p) {
		$a = explode("\n",$this->Text);
		$this->Text="";
		foreach($a as $x) if(!contains($x,$p)) $this->Text.="$x\n";
	}
	private function cmdTabsToSpaces() {
		$this->Text = strtr($this->Text,["\t"=>"    "]);
	}
	private function cmdReplace($p,$raw) {
		$raw; // ebben még nincs feloldva a quote; itt külön oldjuk fel mindkettőre
		list($from,$into) = texplode(" --> ",$raw);
		$from = $this->solveQuotes($from);
		$into = $this->solveQuotes($into);
		$this->Text = strtr($this->Text,[$from=>$into]);
	}
	private function cmdSaveAs($p) {
		$work = $this->Config["working path"];
		str2file("$work/cache/$p",$this->Text,0664);
		str2file("$work/output/$p",$this->Text,0664);
	}
	private function cmdSkipLine($p) {

		$this->Text = string_from($this->Text,"\n");

	}
	private function cmdSkipUntil($p) {

		if(contains($this->Text,$p)) $this->Text = string_from($this->Text,$p);

	}
	private function cmdSortBy($p) {

		$num = whether($numericFields[$p],1,0);
		$colName = $p;
		$colNr = $this->NameToCol[$colName];
		$a = explode("\n",$this->Text);
		foreach($a as $i=>$x) $a[$i] = explode("\t",$a[$i]);
		if($num==0) foreach($a as $i=>$x) $a[$i][$colNr] = trim($a[$i][$colNr]);
		if($num==1) foreach($a as $i=>$x) $a[$i][$colNr] = sprintf("%12.2f",trim($a[$i][$colNr]));
		sortByField($a,$colNr,1);
		foreach($a as $i=>$x) $a[$i] = join("\t",$a[$i]);
		$this->Text = join("\n",$a);

	}
	private function cmdSplitAfter($p) {

		$this->Text = strtr($this->Text,[$p=>"$p\n"]);

	}
	private function cmdSplitBefore($p) {

		$this->Text = strtr($this->Text,[$p=>"\n$p"]);

	}
	private function cmdStop($p) {

		$this->ScriptHalted = 1;

	}
	private function cmdTrimLines() {

		$this->Text = trim(preg_replace('/\s+[\r\n]\s+/',"\n",$this->Text));

	}
	private function cmdTrimLinesFrom($p) {

		$a = explode("\n",$this->Text);
		foreach($a as $i=>$s) if(contains($s,$p)) list($a[$i],$xx) = explode($p,$s,2);
		$this->Text = join("\n",$a);

	}
	private function cmdTrimLinesUntil($p) {

		$a = explode("\n",$this->Text);
		foreach($a as $i=>$s) if(contains($s,$p)) list($xx,$a[$i]) = explode($p,$s,2);
		$this->Text = join("\n",$a);

	}
	private function cmdTrimLinesBetween($p) {

		$a = explode("\n",$this->Text);
		list($b1,$b2) = explode(" and ",$p);
		foreach($a as $i=>$s) {
			if(!contains($s,$b1)) continue;
			if(!contains($s,$b2)) continue;
			list($s1,$s2) = explode($b1,$s,2);
			list($s2,$s3) = explode($b2,$s2,2);
			$a[$i] = "$s1$s3";
		}
		$this->Text = join("\n",$a);

	}
	private function cmdWeGot($p) {

		$this->ColToName = texplode(",",$p);
		$this->NameToCol = array_flip($this->ColToName);

	}


	//----------------------------------------------------------------------------------------------------------------  Internal helpers

	private function solveQuotes($s) {
		if(strlen($s)==1) return $s;
		if($s[0]=='"') if(string_ends($s,'"')) return substr($s,1,-1);
		if($s[0]=="'") if(string_ends($s,"'")) return substr($s,1,-1);
		return $s;
	}
	private function commandToMethod($s) {
		$words = explode(" ",onespace($s));
		$words = array_map("ucfirst",$words);
		$name = join("",$words);
		return "cmd$name";
	}
	private function fetchWithCache($url,$timeThreshold) {

		$work = $this->Config["working path"];
		$name = sprintf('%s-%s.htm',"texter",md5($url));
		$cacheFile = "$work/cache-fopen/$name";
		$cacheValid = file_exists($cacheFile);
		if(fileage($cacheFile) > $cacheTimeout) $cacheValid = false;
		if($cacheValid) return @file_get_contents($cacheFile);

		$startWithProxy = 0;
		if(string_begins($url,"proxy:")) {$startWithProxy=1;$url=substr($url,6);}

		'Try direct fetch';{
			$htm = "";
			if(!$startWithProxy) {
				$htm = file_get_contents($url);
				$htm = "";
				if($htm) {str2file($cacheFile,$htm);return $htm;}
			}
		}

		'Use proxy';{
			$fetchURL = base64_encode($url);
			$proxyURL = "http://heedesign.hu/pricepool-proxy/fetch.php";
			$url = "$proxyURL?from=$fetchURL";
			$htm = file_get_contents($url);
			if($htm) {str2file($cacheFile,$htm);return $htm;}
		}

		return "";

	}
	private function LinkToFilename($link) {

		static $isoAccentStr="";
		if(""==$isoAccentStr) {

			$chars = explode(" ",trim(
				"e1 e9 ed f3 fa f6 fc f5 fb ".
				"c1 c9 cd d3 da d6 dc d5 db"
			));
			foreach($chars as $ch) $isoAccentStr.=chr(hexdec($ch));

		}

	    $s = $url;
	    $s = preg_replace("/\.(htm|php).*/","",$s);
		$s = strtr($s,$isoAccentStr,"aeiouououAEIOUOUOU"); $s=mb_convert_encoding($s,"iso-8859-2");
		$s = strtr($s,$isoAccentStr,"aeiouououAEIOUOUOU");
		$s = strtolower($s);
		$s = preg_replace("/[^a-z0-9]+/","-",$s);
		$s = trim($s,"-");

		if(strlen($s)<=50) return $s;

		$md = substr(md5($url),0,8);
		$s = substr($s,0,15)."($md)".substr($s,-25);
		return $s;

	}


}
