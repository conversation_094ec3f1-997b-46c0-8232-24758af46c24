<?php #crossProject // see 'd:/http/dktools/misc.php'

//--------------------------------------------------------------------------------------------------------------------  logic tweaks
function flag10($bool) {
    return ($bool?1:0);
}


function whether($bool,$trueValue=true,$falseValue=false) {
    return ($bool?$trueValue:$falseValue);
}


function unless($bool,$trueValue=true,$falseValue=false) {
    return (!$bool?$trueValue:$falseValue);
}


function yesno($bool) {
    return ($bool?"yes":"no");
}


function onoff($bool) {
    return ($bool?"on":"off");
}


function xbits() {
    $a=func_get_args();
    $out="";
    foreach($a as $v) $out.=($v?"x":".");
    return $out;
}


function firstof() {
    $a=func_get_args();
    foreach($a as $v) if($v) return $v;
    return $v;
}


function forcerange($v,$min,$max) {
    if ($v<$min) $v=$min;
    if ($v>$max) $v=$max;
    return $v;
}


function inrange($v,$min,$max) {
    return (($v>=$min)and($v<=$max));
}


function obCapture() {
    // new style ob_capturing! @ 2013.03.14, 12:07:19
    return ob_start();
}


function obGet(&$x) {
    $x = ob_get_clean();
}


function obCancel() {
    ob_end_clean();
}



//--------------------------------------------------------------------------------------------------------------------  command line tools
function isCommandLine() {
    if(!$_SERVER["REMOTE_ADDR"]) if(strtolower(php_sapi_name())=="cli") return 1;
    return 0;
}


function parseCommandLine() {
    $argc = $_SERVER["argc"];
    $argv = $_SERVER["argv"];
    unset($argv[0]);
    $out=array();
    $anr=0;
    foreach($argv as $v) {
        if($v[0]=="-") {
            list($sn,$sv)=explode("=",$v,2);
            $out[$sn]=$sv;
        }else{
            $out[++$anr]=$v;
        }
    }
    return $out;
}


function writelines($s) {
    $s=trim($s,"\n\r");
    $s=rtrim($s);
    $a=explode("\n",$s);
    $cutTabs = strlen($a[0])-strlen(ltrim($a[0],"\t"));
    foreach($a as $nr=>$x) $a[$nr]=rtrim(substr($x,$cutTabs));
    $s=join("\r\n",$a);
    print $s;
}



//--------------------------------------------------------------------------------------------------------------------  validation basics
function validURL($s) {
    if(!preg_match("/http(s?):\/\/\w+\.\w\w+/",$s)) return false;
    $out = filter_var($s,FILTER_VALIDATE_URL);
    return $out;
}


function valid_email($s,$dnsCheck=0) {

    // formal check first
    if(!preg_match("/.+@.+\..+./",$s)) return false;

    // dns check if needed & possible
    if($dnsCheck) if(function_exists("checkdnsrr")) {
        $d=string_from($s,"@");
        if(!checkdnsrr($d,"MX")) return false;
    }

    return true;
}


function validmail($addr) {
    return valid_email($addr);
}



//--------------------------------------------------------------------------------------------------------------------  shared values, sessions, etc
if(!isset($_dktools_shared_values)) $_dktools_shared_values = []; // see Shared

function getAllSharedValues() {
    return $GLOBALS["_dktools_shared_values"];
}


function clearAllSharedValues() {
    $GLOBALS["_dktools_shared_values"] = [];
}


function setSharedValue($key,$val) {
    $sh = &$GLOBALS["_dktools_shared_values"];
    $sh[$key]=$val;
    return $val;
}


function getSharedValue($key,$default="") {
    $sh = &$GLOBALS["_dktools_shared_values"];
    $out=$default;
    if(isset($sh[$key])) $out=$sh[$key];
    return $out;
}


function unsetSharedValue($key) {
    $sh = &$GLOBALS["_dktools_shared_values"];
    unset($sh[$key]);
}


function setSessionValue($key,$val) {
    $svs=&$_SESSION["svs"];
    $svs[$key]=$val;
    return $val;
}


function getSessionValue($key,$default="") {
    $svs=&$_SESSION["svs"];
    $out=$default;
    if(isset($svs[$key])) $out=$svs[$key];
    return $out;
}


function unsetSessionValue($key) {
    $svs=&$_SESSION["svs"];
    unset($svs[$key]);
}


function setGlobal($name,$value) {$GLOBALS[$name]=$value;return $value;};;
function getGlobal($name,$default=null) {$out=firstof($GLOBALS[$name],$default);return $out;};;
if(!function_exists("share" )) {function share ($key,$val   ) {return setSharedValue($key,$val);}}
if(!function_exists("shared")) {function shared($key,$dft="") {return getSharedValue($key,$dft);}}


//--------------------------------------------------------------------------------------------------------------------  various shit
function randomize() {
    mt_srand((double)microtime()*1000000);
}


function devhost() {
    $docRoot = strtolower($_SERVER["DOCUMENT_ROOT"]);
    if(substr($docRoot,1,7)==":/http/") return 1;
    return 0;
}


function xtract($a,$prefix) {
    if($a) foreach($a as $key=>$val) {
        $GLOBALS["${prefix}_${key}"]=$val;
    }
}


function mime2ext($mimetype) {
    switch(strtolower($mimetype)) {
        case "image/jfif"  : return "jpg";
        case "image/jpg"   : return "jpg";
        case "image/jpeg"  : return "jpg";
        case "image/pjpeg" : return "jpg";
        case "image/x-png" : return "png";
        case "image/png"   : return "png";
    }
}


function unhandled_call() {

    print "
        <title>unhandled call</title>
        <body bgcolor=silver style='font:11px tahoma' onkeypress='if(event.keyCode==27)window.close()'>
        <div style='font:bold 17px tahoma'>unhandled call</div>
        by ".$_SERVER["REQUEST_URI"]."<br>
        <br><br>
    ";
    $x = "<div style='clear:both'></div><br><br>";
    print "<b>GET </b> variables: ";if(sizeof($_GET )) {xdebug($_GET ,0);print $x; }else{print "none<br>";}
    print "<b>POST</b> variables: ";if(sizeof($_POST)) {xdebug($_POST,0);print $x; }else{print "none<br>";}
    exit;
}


function options_get($key) {
    $a=explode(".",$key); $o=&$GLOBALS["Options"];foreach($a as $keypart) $o=&$o[$keypart];
    return $o;
}


function options_set($key,$value) {
    $a=explode(".",$key); $o=&$GLOBALS["Options"];foreach($a as $keypart) $o=&$o[$keypart];
    $o=$value;
    return $o;
}


function htmail($dest,$subj,$body,$from="HTMail",$charset="utf-8") {
    if(devhost()) {

        $eml =
            "Return-Path: [[from]]".CRLF.
            "X-Original-To: [[to]]".CRLF.
            "Delivered-To: [[to]]".CRLF.
            "Date: ".date("r").CRLF. // date
            "From: [[from]]".CRLF.
            "MIME-Version: 1.0".CRLF.
            "To: [[to]]".CRLF.
            "Subject: [[subject]]".CRLF.
            "Content-Type: text/html; charset=$charset; format=flowed".CRLF.
            "Content-Transfer-Encoding: 8bit".CRLF.
            "".CRLF.
            "[[htm]]".CRLF
        ;

        $eml = strtr($eml,array(
            "[[to]]"        => $dest,
            "[[subject]]"   => $subj,
            "[[htm]]"       => $body,
            "[[from]]"      => $from,
        ));

        $outFile = "d:/htmail-output.eml";
        str2file($outFile,$eml);
        exec(outFile);

        return false;
    }
    if(!contains($from,"<")) $from="$from <<EMAIL>>";
    return @mail($dest,$subj,$body,
        "From: $from\r\nMIME-Version: 1.0\r\n".
        "Content-Type: text/html; charset=\"$charset\"\r\n".
        "Content-Transfer-Encoding: 8bit\r\n"
    );
}


function cfc($s,$purpose="fun") {
    return md5("ewuiqwejr qe 20100728:124806 $s xxx for $purpose");
}


function dir_exists($dir) {
    return file_exists($dir);
}


function depends($a) {
    foreach($a as $val=>$cond) {if($cond) $out=$val;}
    return $out;
}


function xy($s) {
    $s=strtr($s,",.:;/",",,,,,");
    list($x,$y)=explode(",",$s);
    return array($x,$y);
}


function replaceValue($currentValue,$ifThis,$thenChangeToThis) { // nem egy bonyolult függvény :)
    if($currentValue==$ifThis) return $thenChangeToThis;
    return $currentValue;
}


function valueByIndex($n /*, more ... */) {
    $a=func_get_args();
    if(func_num_args()==2) if(is_array($a[1])) return $a[1][$n];
    $v="";if($n) $v=$a[$n];
    return $v;
}


function defaultIfNull(&$v,$defaultValue) {
    if($v===NULL) $v=$defaultValue;
    return $v; // not necessary but just in case
}


function defaultIfEmpty(&$v,$defaultValue) {
    if(!$v) $v=$defaultValue;
    return $v; // not necessary but just in case
}


function msleep($millisec) {
    usleep($millisec*1000-15); // approx
}


function runScoped(callable $f) {
    $a = func_get_args();
    array_shift($a);
    return call_user_func($f,$a);
}


function kellnerTime($ts=0) {
    $ts = $ts?:time();
    $x = ceil($ts/86400); // Budapesti idő szerint éjjel egykor vált
    $x-=15032;
    return $x;
}



//--------------------------------------------------------------------------------------------------------------------  math, gps, time
function dcos($x) {return cos($x*pi()/180);};;
function dsin($x) {return sin($x*pi()/180);};;
function lon2km($lat) {
    $m = 111320 * (dcos($lat) + dsin($lat*2)*dsin($lat*1.14)/648); // km/longitude ratio
    $km = round($m)/1000;
    return $km;
}


function lat2km($lat) {
    $m = 111132.954 - 559.822*dcos(2*$lat) + 1.175*dcos(4*$lat); // km/latitude ratio
    $km = round($m)/1000;
    return $km;
}


function sqlf_gps_distance($myLat,$myLon,$returnUnit="m") {
    $kmLat = lat2km($myLat);    // igen, mindkét helyen Lat szerepel, és az jó így!
    $kmLon = lon2km($myLat);    // mivel a földgömbön a szélességi körtöl függ, mennyi egy km
    $kmY="(gps_lat-$myLat)*$kmLat"; $y2 = "power($kmY,2)";
    $kmX="(gps_lon-$myLon)*$kmLon"; $x2 = "power($kmX,2)";
    $diagonalDistance = "sqrt($x2+$y2)";
    //x;flog($diagonalDistance);
    if($returnUnit=="m" ) $out = "round(1000*$diagonalDistance)";
    if($returnUnit=="km") $out = "$diagonalDistance";
    return $out;
}


function meaningOf($area,$code,$default="") {

    //  Good for ad-hoc dictionaries
    //  See this example from a Storage class:
    //
    //      meaningOf("status",[
    //          1 => "normal project",
    //          2 => "other activities",
    //      ]);
    //

    static $t; if(!isset($t)) $t=[];

    if(is_array($code)) {
        $t[$area] = $code; // set a new table
        return count($code);
    }

    return $t[$area][$code] ?: $default;


}



//--------------------------------------------------------------------------------------------------------------------  semantic functions (they do nothing)
function using($s)      {return $s;};;
function bmk($s)        {return $s;};;

