<?php #crossProject

class Routes {

    private static $all = [];
    private static $locked = 0;
    private static $lastMatchedRoute;

    public static function lock() {self::$locked=1;} // prevent hijacking
    public static function addTable($mls) {

        if(self::$locked) return false;
        foreach(linesOf($mls) as $line) {
            list($pattern,$file,$vars) = preg_split('/\s\s\s+/',$line);
            self::add($pattern,$file,$vars);
        }

    }

    public static function add($pattern,$file="",$variableNames="") {
        if(self::$locked) return false;
        if(!$file) ; //-----------------------------------------------------------------------------------------------  #implementThis
        $item = compact('pattern','file','variableNames');
        array_unshift(self::$all,$item);
    }

    public static function getMatched() {
        return self::$lastMatchedRoute;
    }

    public static function find($url=null) {

        self::$lastMatchedRoute = false;
        if(is_null($url)) $url = $_SERVER['REQUEST_URI'];
        $url = explode("?",$url,2)[0];
        $url = explode("&",$url,2)[0];

        $matches = [];
        $url = rtrim($url,'/')."/";

        foreach(self::$all as $info) {
            $patt = $info["pattern"];
            if(contains($patt,"___")) {
                $patt = preg_quote($patt,"/");
                $patt = strtr($patt,["___"=>"([^\/]+)"]);
                $patt = "~/$patt/";
            }
            switch($patt[0]) {
                case "~":   $hit = !!( preg_match(substr($patt,1),$url,$matches) ); break;
                case "[":   $hit = !!( $url===substr($patt,1,-1) );                 break;
                default:    $hit = !!( string_begins($url,$patt) );                 break;
            }
            if($hit) break;
        }
        if(!$hit) return problemWith("routing","Could not find a route for this URL ($url)");

        $segments = explode("/",rtrim($url,"/"));
        $cutSegments = count_chars(rtrim($info["pattern"],"/"),1)[ord("/")] + 1;
        $info["extra"] = array_slice($segments,$cutSegments);
        $info["matches"] = $matches;

        $vars = $info["variableNames"];
        if($vars) {
            if(!is_array($vars)) $vars = explode(",",$vars);
            $n = 0;
            foreach($vars as $v) {
                $v = trim($v);
                ++$n;
                $info["variables"][$v] = rawurldecode($matches[$n]);
            }
        }

        self::$lastMatchedRoute = $info;
        return $info;
    }

    public static function value($variableName) {
        $x = self::$lastMatchedRoute;
        return $x["variables"][$variableName];
    }

    public static function values() {
        $x = self::$lastMatchedRoute;
        return $x["variables"];
    }


}


