<?php #crossProject


//if(!Page::checkDependencies()) die(Problems::get());

class Page {

    private static $all;
    // private static $scripts;
    // private static $styles;
    private static $sourceDir       = '.';
    private static $tplNaming       = '%s.tpl';
    private static $dataToImplant   = [];

    //----------------------------------------------------------------------------------------------------------------  PUBLIC

    public static function checkDependencies() {
        if(!function_exists("lessImportFrom")) return problemWith("Page class","LESS compiler is missing");
        return true;
    }

    public static function source($dir,$naming='') {
        if($dir)    self::$sourceDir = $dir;
        if($naming) self::$tplNaming = $naming;
    }

    public static function init($pageTemplate) {
        $tpl = self::loadTemplate($pageTemplate);
        self::$all = $tpl["html"];
        $css = trim($tpl["css"]);if($css) self::addStringToSlot("styles",$css."\n");
        $jsc = trim($tpl["js" ]);if($jsc) self::addStringToSlot("scripts",$jsc."\n");
    }

    public static function add($slot,$template) {
        $tpl = self::loadTemplate($template);
        $htm = trim($tpl["html" ]);if($htm) self::addStringToSlot($slot,$htm."\n");
        $css = trim($tpl["css"  ]);if($css) self::addStringToSlot("styles",$css."\n");
        $jsc = trim($tpl["js"   ]);if($jsc) self::addStringToSlot("scripts",$jsc."\n");
        //$lss = trim($tpl["less" ]);if($lss) self::addStringToSlot("less",$lss."\n");
    }

    public static function set($key,$val) {
        $cursor = '{{'.$key.'}}';
        if(!contains(self::$all,$cursor)) return;
        list($before,$after) = explode($cursor,self::$all,2);
        if(class_exists("Language")) $val = Language::implant($val);
        self::$all = "$before$val$after";
    }

    public static function data($key,$val="",$prefix="") {

        if(is_array($key)) { // first-level array
            list($allValues,$prefix) = func_get_args();
            $allValues = self::flatten($allValues);
            $prefix = rtrim($prefix,"."); fine($prefix,"^.");
            foreach($allValues as $k=>$v) self::data("$prefix$k",$v);
            return;
        }
        if(is_array($val)) {
            $allValues = $val;
            $allValues = self::flatten($allValues);
            foreach($allValues as $k=>$v) self::data("$key.$k",$v);
            return;
        }

        self::$dataToImplant[$key] = $val;

    }

    public static function json($varName,$varData) {

        $json = json($varData);
        self::addStringToSlot("scripts","$varName = $json;");

    }

    public static function render() {

        $h = self::$all;
        $d = self::$dataToImplant;
        $h = implantData($h,$d,true);
        $h = preg_replace('/\{\{[^\}]+\}\} *\r?\n?/','',$h);
        //$h = Beautify::html($h);

        print $h;

    }



    //----------------------------------------------------------------------------------------------------------------  /

    private static function parseTemplate($s) {// #see "Page :: parseTemplate"                                                               #documentThis

        $h = $s;
        $j = "";
        $c = "";
        $d = self::$sourceDir;
        $a = explode("<less",$h);
        $h = array_shift($a);
        $l = "";
        foreach($a as $x) {
            $less = string_between($x,">","</less>");   $l.=$less."\n";
            $html = string_from($x,"</less>");          $h.=$html;
        }

        $a = explode("<js",$h);
        $h = array_shift($a);
        foreach($a as $x) {
            $j.= string_between($x,">","</js>");
            $h.= string_from($x,"</js>");
        }

        $a = explode("<include ",$h);
        $h = array_shift($a);
        foreach($a as $x) {
            $p = string_till($x,">");
            $h.= string_from($x,">");
            $t = contains($p,'type="') ? string_between($p,'type="','"') : "";
            $f = contains($p,'file="') ? string_between($p,'file="','"') : "";
            $t = $t ?: fileext(basename($f));
            $f = realpath("$d/$f");
            $f = @file_get_contents($f);
            switch($t) {
                case "js":   $j.="\n$f\n";break;
                case "css":  $c.="\n$f\n";break;
                case "less": $l.="\n$f\n";break;
            }
        }

        $cssFromPage = $c;
        $cssFromLess = "";
        if(trim($l)) {
            lessImportFrom(self::$sourceDir);
            $cssFromLess = less2css($l);
        }

        $c = join("\n\n",[$cssFromLess,$cssFromPage]);

        return [
            "html"  => $h,
            "css"   => $c,
            "js"    => $j,
        ];

    }

    private static function loadTemplate($name) {

        $filename = sprintf(self::$tplNaming,$name);
        $filename = self::$sourceDir."/$filename";
        $parsed = self::parseTemplate(file_get_contents($filename)); //                                                 #handleCase: not found
        if(class_exists("Language")) $parsed = Language::implant($parsed);

        return $parsed;

    }

    private static function addStringToSlot($slotName,$string) {
        $cursor = '{{'.$slotName.'}}';
        list($before,$after) = explode($cursor,self::$all,2);
        self::$all = "$before $string $cursor $after";
    }

    private static function flatten($nestedArray,$pathSeparator=".") {

        $ps = $pathSeparator;
        $out = [];
        foreach($nestedArray as $key=>$val) {
            if(!is_array($val)) {$out[$key]=$val;continue;}
            $subs = Self::flatten($val,$ps);
            if(!$ps) $out = array_merge($out,$subs);
            if( $ps) foreach($subs as $k=>$v) $out["$key$ps$k"]=$v;
        }
        return $out;
    }


}

