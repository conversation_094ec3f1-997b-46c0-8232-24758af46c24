<?php #crossProject

class Ftp {


    private $connection;
    private $options;
    private $progress = ""; // callback function, if given
    private $dir=""; // cwd
    private $blockSize = 8192;
    private $blocksRead = 0;
    private $bytesRead = 0;


    public function __construct() {
        $this->options = [
            "allowNB"           => 1,                           // allow non-blocking transfers
            "copyTempSuffix"    => ".ftp-copy-temp",            // upload with this suffix, then rename
        ];
    }

    public function opt($key,$val=null) {
        if(isset($val)) $this->options[$key] = $val;
        return $this->options[$key];
    }

    public function cwd() {
        return $this->dir;
    }

    public function progressCallback($func) {
        $this->progress = $func; // must be like 'function progress($percent,$what="")'
    }

    public function pg($pct,$what="") {
        if(!$this->progress) return;
        call_user_func($this->progress,$pct,$what);
    }

    public function open($host,$user,$pass,$otherOptions=[]) {

        if(is_string($otherOptions)) {$x=$otherOptions; $otherOptions = ["startDir"=>$x];}
        foreach((array)$otherOptions as $key=>$val) $this->opt($key,$val);

        $this->pg(1,"Connecting");
        $f = &$this->connection;
        $f = ftp_connect($host);           $this->pg(15);  if(!$f )  return "Cannot connect";
        $ok = @ftp_login($f,$user,$pass);  $this->pg(45);  if(!$ok)  return "Login failed";
        $ok = @ftp_pasv($f,true);          $this->pg(48);  if(!$ok)  return "Passive mode failed";

        if($x= $this->opt("startDir")) {
            $ok = @ftp_chdir($f,$x); $this->pg(42);
            if(!$ok)  return "Chdir failed ($x)";
        }

        $this->pg(85);
        $this->dir = @ftp_pwd($f); // it's your working directory, not a fuckin password :)
        $this->pg(99);
        return "ok";

    }

    public function close() {
        $f = &$this->connection;
        @ftp_close($f);
    }

    public function xferModeFor($fn) {

        $p=pathinfo($fn);
        if(oneof($p["extension"],"jpg,png,gif,mp3,wav,swf,dta")) return FTP_BINARY; // known-to-be-binary extensions

        $chs=count_chars(@file_get_contents($fn),1);            // look for control chars
        $chs[13]=0;$chs[10]=0;$chs[9]=0;                        // linefeeds and tabs are fine
        for($i=0;$i<=31;++$i) if($chs[$i]) return FTP_BINARY;   // anything else means binary

        return FTP_ASCII;
    }

    public function copy($file,$behaviour="replace") {

        // simple case when it's the same name and relative path
        // like local 'assets/myfile.js' to outside 'assets/myfile.js'
        $this->copyAs($file,$file,$behaviour);

    }

    public function copyAs($sourceFile,$targetFile,$behaviour="replace") {

        $f = &$this->connection;

        static $lastdir;
        $suffix = $this->opt("copyTempSuffix");
        $nblock = $this->opt("allowNB");

        $this->pg(1,"Copying '$sourceFile'");

        $fnTarget = $targetFile;
        $fnUpload = $targetFile.$suffix;
        $fnSource = $sourceFile;
        $binMode  = $this->xferModeFor($sourceFile);
        $uptemp   = ($fnUpload<>$fnTarget)?1:0;

        $dir=dirname($targetFile);
        if($lastdir<>$dir) {
            if($dir<>".") $this->mkdir($dir);
            $lastdir=$dir;
        }

        $this->pg(10);

        if($behaviour=="update") { // check if older, don't touch if newer
            $mtRemote = @ftp_mdtm($f, $fnTarget );
            $mtLocal  = @filemtime(   $fnSource );
            if($mtLocal < $mtRemote) {
                $this->pg(-1,"Skipped '$sourceFile'");
                return "Skipped by modtime";
            }
        }

        if(!$nblock) {

            // do a blocking transfer - it's a very simple thing
            $this->pg(15); $ok = @ftp_put($f,$fnUpload,$fnSource,$binMode);         if(!$ok) return "Upload failed";
            $this->pg(78); if($uptemp) $ok=ftp_rename($f,$fnUpload,$fnTarget);      if(!$ok) return "Temp file rename failed";
            $this->pg(99);
            return "ok";

        }else{

            // non-blocking, this is the modern way

            $fsize = filesize($fnSource);                           // we need file size for progress stats
            $count = 0;                                             // this will follow as we go
            $bigFile = ($fsize>8191);                               // decide if it's big enough for blocksize learning

            $q = @ftp_nb_put($f,$fnUpload,$fnSource,$binMode);      //  start transfer
            while($q==FTP_MOREDATA) {                               //  now as long as we have something to read,
                if($bigFile) ++$this->blocksRead;                   //      if it's a big-enough file, measure blocks
                $count+=$this->blockSize;                           //      count where we stand
                $p = round(100*$count/$fsize); if($p>98) $p=98;     //      calculate percentage
                $this->pg($p);                                      //      display progress
                $q = ftp_nb_continue($f);                           //      do the next FTP step
            }                                                       //  ;;

            if($q==FTP_FINISHED) {                                  //  we're done - now let's do some maths!
                if($bigFile) if($this->blocksRead) {                //  if we have a big-enough file,
                    $this->bytesRead += ceil($fsize/512)*512;       //      count with whole sectors
                    $x = round($this->bytesRead/$this->blocksRead); //      calculate all-time average
                    $x = round($x/512) * 512;                       //      round it up a little
                    $this->blockSize = $x;                          //      learn new value
                }                                                   //  ;;
                $ok=1;
                if($uptemp) $ok=ftp_rename($f,$fnUpload,$fnTarget); //  rename tempfile (atomic-replace any old version)
                if($ok) {$this->pg(99);return "ok";}                //  we're fine
                return "Temp file rename failed";
            }
            return "Upload failed";

        }
    }

    public function mkdir($dirName) {

        $f = &$this->connection;
        $dirs=explode("/",$dirName); $pathSoFar=".";
        foreach($dirs as $dir) {
            $lastPath = $pathSoFar;
            $pathSoFar.="/$dir";
            $ok=@ftp_mkdir($f,$pathSoFar);
            if(!$ok) return "Failed to mkdir '$dir' under '$lastPath'";
        }
        return "ok";

    }

    public function delete($fileName) {

        $f = &$this->connection;
        $ok = @ftp_delete($f,$fileName);
        return $ok?"ok":"Delete failed";

    }

    public function getConnection() {
        return $this->connection;
    }



}

