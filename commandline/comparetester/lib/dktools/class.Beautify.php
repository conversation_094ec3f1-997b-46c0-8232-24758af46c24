<?php #crossProject

class Beautify {

    public static $indentTags = [];

    public static function addIndentTags($spaceSeparatedListOfTags) {
        $t = $spaceSeparatedListOfTags;
        $t = preg_replace('/[\s,]+/'," ",$t);
        $a = explode(" ",$t);
        foreach($a as $tag) self::$indentTags[$tag]=1;
    }

    public static function html($htm,$tabString="\t") {

        self::addIndentTags("

            a, abbr, address, article, aside, audio, b, bdi, bdo, blockquote, body, button, canvas, caption,
            cite, code, colgroup, content, data, datalist, dd, del, details, dfn, dialog, div, dl, dt,
            element, em, fieldset, figcaption, figure, font, footer, form, frame, frameset, head, header,
            hgroup, html, i, iframe, ins, kbd, label, legend, li, main, map, mark, menu, meter, nav, nobr,
            noframes, noscript, object, ol, optgroup, option, output, p, picture, pre, progress, q, rp, rt,
            rtc, ruby, s, samp, script, section, select, shadow, small, span, strong, style, sub, summary,
            sup, table, tbody, td, template, textarea, tfoot, th, thead, time, title, tr, u, ul, var, video,
            h1,h2,h3,h4,h5,h6,

            svg,path,g,linearGradient,radialGradient

        ");

        $tsl=strlen($tabString);
        $a=explode("\n",$htm);
        $itg=array_keys(self::$indentTags);
        $t="";
        $out="";
        foreach((array) $a as $s) {
            $s=ltrim($s);
            $xs = preg_replace('/<([\w\-]+)[^>]*(\/?)>/','<$1$2>',$s); //see "20160524.182014" for results
            $diff=0; // indent difference that current line will cause
            foreach((array) $itg as $x) {
                $openCt  = substr_count($xs,"<$x>");
                $closeCt = substr_count($xs,"</$x>");
                $diff += $openCt - $closeCt;
            }
            if($diff<0) $t=substr($t,0,$tsl*$diff); // will be a negative number and that's exactly what we need!
            $out.=$t.$s."\n";
            if($diff>0) $t.=str_repeat($tabString,$diff);
        }

        return $out;
    }

}
