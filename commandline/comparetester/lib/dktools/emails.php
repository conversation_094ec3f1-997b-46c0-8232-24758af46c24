<?php #crossProject

include_once("class.Mailer.php"); //see "20151228.182137" on how to use it
include_once("problems.php"); // dependency

function sendMail($to,$subj,$body,$from,$otherOptions=[]) {

    try{

        list($fromName,$fromAddr) = texplode("<",trim("> \t",$from),2);
        list($destName,$destAddr) = texplode("<",trim("> \t",$to  ),2);

        $data = $otherOptions["data"]?:0;
        if($data) $body = implantData($body,$data);

        $m = new Mailer();
        $m->setFrom      ($fromName,$fromAddr);
        $m->addRecipient ($destName,$destAddr);
        $m->fillSubject  ($subj);
        $m->fillMessage  ($body);

        if($x= $otherOptions["cc"]) {
            list($name,$addr) = texplode("<",trim("> \t",$x),2);
            $m->addCC($name,$addr);
        }
        if($x= $otherOptions["bcc"]) {
            list($name,$addr) = texplode("<",trim("> \t",$x),2);
            $m->addCCO($name,$addr);
        }
        if($x= $otherOptions["files"]) {
            foreach($x as $fn) $m->attachFile($fn);
        }

        $m->send();

    }catch(Exception $Ex) {

        problemWith("sendMail()",$Ex->getMessage());
        return false;

    }

    return "ok";
}



