<?php #crossProject


class SimpleDataParser {

    protected $inputText;
    protected $input;
    protected $indentLevels = [];
    protected $differentLevels = 0;

    public function __construct($text) {
        $this->load($text);
    }

    public function sweepLinesOf(&$a,$func) {
        $p = func_get_args();
        array_shift($p);
        array_shift($p);
        foreach($a as $n=>$s) {
            $args = $p; array_unshift($args,$s);
            $a[$n] = call_user_func_array($func,$args);
        }
    }

    public function sweepLines($func) {
        $p = func_get_args();
        array_shift($p);
        $a = &$this->input;
        foreach($a as $n=>$s) {
            $args = $p; array_unshift($args,$s);
            $a[$n] = call_user_func_array($func,$args);
        }
    }

    public function load($text) {
        $text = expandTabs($text);
        $this->inputText = $text;
        $this->input = explode("\n",$text);
        $this->sweepLines(rtrim);
    }

    public function peel($a) {

        $indent = [];

        $level = 999;
        $vary = [];
        foreach($a as $n=>$s) if($s) {
            $indent[$n] = $xi = strspn($s," ");
            $level = min($level,$xi);
            $vary[$xi] = 1;
        }
        foreach($a as &$x) $x = substr($x,$level);
        //$this->sweepLinesOf($a,substr,$level);

        $this->differentLevels = count($vary);
        $allSameLevel = (1 == $this->differentLevels);
        if($allSameLevel) return $a; // bottom level, all with the same indention

        $key = "";
        $out = [];
        foreach($a as $s) if($s) {
            if($s[0]<>" ") {$key = $s;$out[$key] = [];continue;}
            if($key) $out[$key][] = $s;
        }

        return $out;
    }

    public function parseRecursively($a) {

        $a = $this->peel($a);
        $nestedMore = !!($this->differentLevels>1);

        switch($nestedMore) {
            case true:   foreach($a as $key=>$val) $a[$key] = $this->parseRecursively($val);break;
            case false:  $this->parseScalarsOf($a);if($a===[]) $a = "";break;
        }

        return $a;
    }

    public function parseScalarsOf(&$a) {

        $b = [];

        foreach($a as $key=>$val) {
            $p = strpos($val,": ");
            if($p!==false) {
                $key = substr($val,0,$p);
                $val = substr($val,$p+2);
                $b[$key] = $val;
            }elseif(string_ends($val,":")) {
                $key = substr($val,0,-1);
                $val = "";
                $b[$key] = $val;
            }else{
                $b[] = $val;
            }
        }

        $a = $b; // the real return
        return $a; // for fun
    }

    public function parse() {
        $a = $this->parseRecursively($this->input);
        return $a;
    }

}