<?php #crossProject // see 'd:/http/dktools/web.php'

include_once("class.Routes.php");        //  brand new (2018) version of explicit routing
include_once("class.Beautify.php");      //  for HTML, mostly


function js($code) {
    print "<script> try{ $code }catch(n){;} </script>";
}


function optionlist($t,$default="",$lf="") {
    $o="";
    $default = strtolower($default);
    foreach ($t as $n=>$x) {
        $sel=(strtolower($n)==$default?" selected":"");
        $o.="<option value=\"$n\"$sel>$x$lf";
    }
    return $o;
}


function header_encoding($what) {
    if($what=="i") $what="iso";
    if($what=="u") $what="utf";
    if(string_begins($what,"iso")) $what="iso-8859-2";
    if(string_begins($what,"utf")) $what="utf-8";
    header("Content-type: text/html; charset=$what");
}


function utf8_header() {
    header_encoding("utf");
}


function mixcolor($base,$factor,$other="#000000") {
    $other=trim($other);
    if($other=="black") $other="#000000";
    if($other=="white") $other="#ffffff";
    $base=trim($base);
    if($base=="") $base="#000000";
    if(substr($base,0,1)!="#") die("Invalid arguments to color mixer");
    $c1[1]=intval(substr($base,1,2),16);  $c2[1]=intval(substr($other,1,2),16);
    $c1[2]=intval(substr($base,3,2),16);  $c2[2]=intval(substr($other,3,2),16);
    $c1[3]=intval(substr($base,5,2),16);  $c2[3]=intval(substr($other,5,2),16);
    if($factor<00)$factor=00;
    if($factor>99)$factor=99;
    $factor=$factor/99;
    foreach($c1 as $i=>$v) {
        $mix[$i]=$c1[$i]+round($factor*($c2[$i]-$c1[$i]));
        if($mix[$i]<000)$mix[$i]=000;
        if($mix[$i]>255)$mix[$i]=255;
    }
    $out=
        "#".
        zeropad(dechex($mix[1]),2).
        zeropad(dechex($mix[2]),2).
        zeropad(dechex($mix[3]),2)
    ;
    return $out;
}


function rrggbb($c) {if(strlen($c)==4)$c="#".$c[1].$c[1].$c[2].$c[2].$c[3].$c[3];return $c;};;
function mixwhite($base,$factor) {return mixcolor(rrggbb($base),$factor,"#ffffff");};;
function mixblack($base,$factor) {return mixcolor(rrggbb($base),$factor,"#000000");};;
function deletecookie($name) {
    setcookie($name,"",time()-86400);
}


function opdefault($val) {
    if(!$GLOBALS["op"]) $GLOBALS["op"]=$val;
}


function htmlsc($x) {
    if( is_array($x)) foreach($x as $k=>$v) $x[$k]=htmlsc($v);
    if(!is_array($x)) $x = htmlspecialchars($x);
    return $x;
}


function isAbsoluteURI($s) {
    if($s[0]==" ") $s = trim($s);
    if($s[0]=="\t") $s = trim($s);
    if($s[0]<>"h") if($s[0]<>"/") return false;
    if(string_begins($s,"http://")) return true;
    if(string_begins($s,"https://")) return true;
    if(string_begins($s,"//")) return true;
    return false;
}


function json($x) {
    return json_encode($x,JSON_PRETTY_PRINT);
}


function json_preg_unidecode($matches) {
    return mb_convert_encoding(pack('H*',$matches[1]),'UTF-8','UTF-16');
}


function json_unidecode($jsonString) {
    $out = preg_replace_callback('/\\\\u([0-9a-zA-Z]{4})/',json_preg_unidecode,$jsonString);
    return $out;
}


function url_clear_arg($url,$arg,$force_arg=1) {
    if($url=="~") $url=myurl();
    $url=preg_replace("/[\?&]$arg=[^&]*$/","",$url);
    $url=preg_replace("/([\?&])$arg=[^&]*&/","$1",$url);
    if ($force_arg) if (strpos($url,"?")===false) $url="$url?F=1";
    return $url;
}


function url_clear_args($url,$args,$force_arg=1) {
    if($url=="~") $url=myurl();
    $a=explode(",",$args);
    foreach($a as $x) $url=url_clear_arg($url,$x,$force_arg);
    return $url;
}


function gp($name,$default="") {
    if(!isset($_REQUEST[$name])) return $default;
    return $_REQUEST[$name];
}


function gpi($name,$default=0) {
    return intval(gp($name,$default));
}


function pick_post_fields($keyprefix,$remove=0) {
    return pickfields($_POST,$keyprefix,$remove);
}


function sendPostRequest($url,$referer,$data) {

    $x="";
    foreach($data as $key=>$val) {
        $val=urlencode($val);
        $x.="$key=$val&";
    } //test1=a&test2=b
    $postData = substr($x,0,-1);
    $postDataLength = strlen($postData);

    $url=parse_url($url);
    if($url['scheme']!='http') return false;// http requests only!
    $host = $url['host'];
    $path = $url['path'];

    {;;// do the socket thing
        $head = preg_replace("/[\r\n]+\s+/","\r\n",trim("

            POST $path HTTP/1.1
            Host: $host
            Referer: $referer
            Content-type: application/x-www-form-urlencoded
            Content-length: $postDataLength
            Connection: close

        "));

        $postRequest = "$head\r\n\r\n$postData";

        $fp = fsockopen($host,80);
        fputs($fp,$postRequest);
        $result="";
        while(!@feof($fp)) $result.=@fgets($fp,128);
        fclose($fp);
    };;

    $result     = explode("\r\n\r\n",$result,2);
    $header  = isset($result[0]) ? $result[0] : '';
    $content = isset($result[1]) ? $result[1] : '';
    $out = array($header,$content);

    return $out;
}


function bsCols($shortVersion = "12 12 6 4 3") {

    $s = $shortVersion;
    if(false===strpos(trim($s)," ")) $s="$s $s $s $s $s"; // single item -> 5 times
    if(!is_array($s)) $s = preg_split("/[ ,;]+/",$s);
    $cc = [];foreach(["xxs","xs","sm","md","lg"] as $cs) {
        if($n= array_shift($s) ) {
            if(is_numeric($n)) {
                $cc[]="col-$cs-$n";
            }elseif($n[0]=='h'){
                $cc[]="hidden-$cs";
            }else{
                @list($n1,$n2) = preg_split("/[^0-9]+/",$n);
                $o = preg_replace("/[0-9]+/","",$n);
                if($o=="+") $cc[]="col-$cs-$n1 col-$cs-push-$n2 col-$cs-pull-0";
                if($o=="-") $cc[]="col-$cs-$n1 col-$cs-pull-$n2 col-$cs-push-0";
                if($o==">") $cc[]="col-$cs-$n1 col-$cs-offset-$n2";
            }
        }else{
            ;
        }
    }
    $cc = trim(join(" ",$cc));
    return $cc;

}


function terminal($text="",$more="") {
    ?><style> pre b {font-weight:normal;color:white;} </style><?php
    ?><body style='margin:0;padding:0;background:black;'><?php
    ?><pre style='background:black;border:none;color:#ae5;font:16px consolas;padding:20px;tab-size:4'><?php
    if(!$text) return;
    if($text=="dk"      ) $text = "DK Terminal version 1.0.22, (c) 1998, Denes Kellner";
    if($text=="debug"   ) $text = "Debug Terminal";
    if($text[1]==":"    ) $text = "[  ".basename($text)."  ]";
    if(contains($text,"[")) {
        $text = strtr($text,[
            "["=>"<span style='background:green;color:white;display:inline-block;padding:5px 0px'>",
            "]"=>"</span><br><ul>"
        ]);
        $text = trimLinesOf($text);
        $text = "$text$more";
        $text.="\n";
    }else{
        $n = strlen($text);
        $text.="\n".str_repeat("-",$n)."\n\n";
    }
    print $text;
}


function headersForDownload($filename) {

    $quotedFilename = '"'.addcslashes($filename,'"').'"';
    header("Cache-Control: public"); // needed for i.e.
    header("Content-Type: application/octet-stream");
    header("Content-Disposition: attachment; filename=".$quotedFilename);
    header("Content-Transfer-Encoding: Binary");

}






