<?php #crossProject

if(!function_exists("logan")) {
    function logan ($v="",$mode="straight") {

        static $fileMode="w";// this happens only the first time! after that, it will become "a"
        $now = date("Y-m-d H:i:s");
        $fileName = "d:/debug/logan.txt";
        if(!is_writable("d:/")) $fileName = "logan.txt"; // go wherever it may if we're on some Linux system

        if(oneof($mode,"1,r,print_r"    )) {//  log to file, print_r

            if(is_array($v)) {
                $v = print_r($v,1);
                $v = preg_replace("/\n([^\s\)])/"," $1",$v);
            }
            if(strpos($v,"\n")!==false) {
                $v = trim(string_from($v,"Array("));
                $v = preg_replace("/^\(/","",$v);
                $v = preg_replace("/\)$/","",$v);
                $v = trim($v);
                $v = strtr($v,["\n    "=>"\n\t"]);
                $v="\n```text\n\t$v\n```\n";
            }

            $f=fopen($fileName,$fileMode);fwrite($f,"$now       $v\n");fclose($f);
            goto _RETURN_;

        }
        if(oneof($mode,"2,c,console"    )) {//  log to console, json_encode

            $s = print_r($v,1);
            if(strpos($s,"\n")!==false) {
                // multiline
                $s="\n$s";$s=strtr($s,["\n"=>"\n  |\t"]);$s.="\n";
            }else{
                // single-line
                $s="[$s]";
            }
            print "<script> console.log(".json_encode($s).") </script>";
            goto _RETURN_;

        }
        if(oneof($mode,"3,x,var_export" )) {//  log to file, var_export

            if(is_array($v)) $v = "\n".var_export($v,1);
            $f=fopen($fileName,$fileMode);fwrite($f,"$now       $v\n");fclose($f);

            goto _RETURN_;
        }
        if(oneof($mode,"4,m,markdown"   )) {//  log to file, var_export, with Markdown

            if(is_array($v)) {
                $v = var_export($v,1);
                $v = "array(".string_from($v,"array (");
            }
            if(strpos($v,"\n")!==false) {
                $v = trim($v);
                $v = strtr($v,[
                    "\n        "    =>"\n\t\t\t\t",
                    "\n      "      =>"\n\t\t\t",
                    "\n    "        =>"\n\t\t",
                    "\n  "          =>"\n\t",
                ]);
                $v = strtr($v,["\n"=>"\n\t"]); // indent the whole thing by one
                $v = preg_replace("/=> \n\s+array \(\n/","=> array(\n",$v);
                $v="\n```php\n\t$v\n```\n";
            }

            $f=fopen($fileName,$fileMode);fwrite($f,"$now       $v\n");fclose($f);

            goto _RETURN_;
        }
        if(oneof($mode,"5,s,straight"   )) {//  log to file, plaintext (trusted)

            if(is_array($v)) $v = var_export($v,1);
            $v = strtr($v,["\n"=>"\n\t\t\t\t\t\t"]);
            $f=fopen($fileName,$fileMode);
            if( contains($v,"\n")) fwrite($f,"\n$now        $v\n\n");
            if(!contains($v,"\n")) fwrite($f,"$now      $v\n");
            fclose($f);

            goto _RETURN_;
        }

        _RETURN_:
        $fileMode = "a";

    }


}


;