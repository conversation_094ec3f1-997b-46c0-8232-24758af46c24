<?php #crossProject // see 'd:/http/dktools/datetime.php'


function secdiff2time($diff) {
    $sec=zeropad(floor($diff/Seconds)%60,2);
    $min=zeropad(floor($diff/Minutes)%60,2);
    $hrs=zeropad(floor($diff/Hours)%24,2);
    $days=floor($diff/Days);
    if($days) return "{$days}d +$hrs:$min:$sec";
    return "$hrs:$min:$sec";
}


function smartdate($s) {
    $months=array("?","jan.","febr.","márc.","ápr.","máj.","jún.","júl.","aug.","szept.","okt.","nov.","dec.");
    $now=YmdHis();
    if(substr($s,0,4)=="0000") return " - ";
    if(substr($now,0,10)==substr($s,0,10)) return substr($s,11);
    list($y,$m,$d)=explode("-",substr($s,0,10));
    $m=intval($m);
    $d=intval($d);
    $s=$months[$m].$d;
    return $s;
}


function Ymd2text($ymd="*") {
    static $a;
    if($ymd=="*") $ymd = date("Y-m-d");
    if(!$a) $a = array(
        "-01-"=>".jan.",
        "-02-"=>".febr.",
        "-03-"=>".márc.",
        "-04-"=>".ápr.",
        "-05-"=>".máj.",
        "-06-"=>".jún.",
        "-07-"=>".júl.",
        "-08-"=>".aug.",
        "-09-"=>".szept.",
        "-10-"=>".okt.",
        "-11-"=>".nov.",
        "-12-"=>".dec."
    );
    //$ymd = strtr($ymd,$a);
    $idx = substr($ymd,4,4);
    $ymd = substr($ymd,0,4).$a[$idx].substr($ymd,8);
    return $ymd;
}


function utime() {
   return microtime(1);
}


function date2date($dtm,$format) {
    return date($format,strtotime($dtm));
}


function dateBetween($interval,$sep=" - ") {
    list($date1,$date2)=explode($sep,$interval);
    $date1=strtr(trim($date1),"./","--");
    $date2=strtr(trim($date2),"./","--");
    if(strlen($date1)==10) $xdate=Ymd();
    if(strlen($date1)>=12) $xdate=YmdHis();
    if($date1<=$xdate) if($date2>=$xdate) return 1;
    return 0;
}


function YmdHis($time="now") {
    if($time=="now") $time=time();
    return date("Y-m-d H:i:s",$time);
}


function YmdHi($time="now") {
    if($time=="now") $time=time();
    return date("Y-m-d H:i",$time);
}


function Ymd($time="now") {
    if($time=="now") $time=time();
    return date("Y-m-d",$time);
}


function getday($delta=0) {
    return Ymd(time()+86400*$delta);
}


function encodeTime($ts="now") {
    if($ts=="now") $ts=time();
    $m=strtr(md5("timestampEncode($ts)see.idh_00095"),"abcdef","782693");
    $x=substr($m,11,8);
    $t=intval($ts)^intval($x);
    $x=zeropad($x,8);
    $t=zeropad($t,10);
    $e=$x.substr($t,0,5).substr($m,0,14).substr($t,5);
    return $e;
}


function decodeTime($s) {
    $x=substr($s,0,8);
    $t=substr($s,8,5).substr($s,-5);
    $ts=intval($t)^intval($x);
    $m=strtr(md5("timestampEncode($ts)see.idh_00095"),"abcdef","782693");
    $out["valid"]=0; if(substr($m,0,14)==substr($s,13,14)) $out["valid"]=1;
    $out["time"]=$ts;
    return $out;
}


function nosec($s) {
    if(substr($s,-3,1)==":") $s=substr($s,0,-3);
    return $s;
}


function weekdayName($w,$lang="hu") {
    static $weekdayNames;
    if(!$weekdayNames[$lang]) {
        if($lang=="hu") $weekdayNames[$lang]=explode(",","vasárnap,hétfő,kedd,szerda,csütörtök,péntek,szombat,vasárnap");
        if($lang=="en") $weekdayNames[$lang]=explode(",","sunday,monday,tuesday,wednesday,thursday,friday,saturday,sunday");
        if($lang=="de") $weekdayNames[$lang]=explode(",","Sonntag,Montag,Dienstag,Mittwoch,Donnerstag,Freitag,Samstag");
    }
    return $weekdayNames[$lang][$w];
}

// base32-encoding of date-time-ms

define("ENCODETIME_START_FROM","1497000000000");//"1478129953000"); // it's 2016-11-03 00:39:13.000
define("ENCODETIME_XLAT_BASE","0123456789abcdefghijklmnopqrstuv");
define("ENCODETIME_XLAT_CODE","56789bcghjklmpqstvwxyz01234nrafd");

function encodeShortTime($microtime="") {
    if(!$microtime) $microtime = microtime(1);
    if(!is_float($microtime)) if(!is_int($microtime)) {
        $microtime = explode(" ",$microtime)[1];
        if(!is_numeric($microtime)) return "";
    }
    $ms = round(1000*$microtime);
    $ms = $ms - ENCODETIME_START_FROM;
    $bc = base_convert($ms,10,32);
    $bc = strtr($bc,
        ENCODETIME_XLAT_BASE,
        ENCODETIME_XLAT_CODE
    );
    return $bc;
}


function decodeShortTime($short) {
    $cd = $short;
    $cd = strtr($cd,
        ENCODETIME_XLAT_CODE,
        ENCODETIME_XLAT_BASE
    );
    $ms = base_convert($cd,32,10);
    $ms = $ms + ENCODETIME_START_FROM;
    return $ms;
}

