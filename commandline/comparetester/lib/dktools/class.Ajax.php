<?php #crossProject


class Ajax {

    public static function respond($ok=1,$data=[],$mode="die") {
        if( $ok) $out = json(["ok"=>$ok,"data"=>$data]);
        if(!$ok) $out = json(["ok"=>$ok,"problem"=>problemWith("last"),"data"=>$data]);
        if($mode=="return") {return $out;}
        if($mode=="die"   ) {print $out;exit;}
        if($mode=="print" ) {print $out;}
    }

    public static function respondWhether($ok=1,$data=[],$mode="die") { // alias of 'respond'
        Ajax::respond($ok,$data,$mode);
    }

    public static function fail($text="",$data=[],$mode="die") {
        if(!$text) $text = problemWith("last"); // be careful with this approach, //see 20151230.154723
        $out = json(["ok"=>0,"problem"=>$text,"data"=>$data]);
        if($mode=="return") {return $out;}
        if($mode=="die"   ) {print $out;exit;}
        if($mode=="print" ) {print $out;}
    }

    public static function success($data=[],$mode="die") {
        return Ajax::respond(1,$data,$mode);
    }

    public static function beware() {
        if(Problems::occurred()) Ajax::fail();
    }

    public static function bewareOf($ok,$text="",$data=[]) {
        if(!$ok) Ajax::fail($text,$data);
    }

    public static function unless($ok,$text="",$data=[]) { // alias for "bewareOf"
        if(!$ok) Ajax::fail($text,$data);
    }

}

