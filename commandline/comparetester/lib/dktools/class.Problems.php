<?php #crossProject

$DKTools["Problems"] = []; // a global.  yes.
$DKTools["Problems"]["Storage"] = [];
$DKTools["Problems"]["Markers"] = [];


class Problems {

    public static function with($section,$set="") {
        global $DKTools;
        $p = &$DKTools["Problems"]["Storage"];
        if($section=="**"   ) return $p;
        if($section=="*"    ) {$out = "";foreach($p as $sct=>$msg) if($msg) $out .= "$sct: $msg\n";return $out;}
        if($section=="last+") {$out = "";foreach($p as $sct=>$msg) if($msg) $out =  "$sct: $msg"  ;return $out;} // notice the dot difference!
        if($section=="last" ) {$out = "";foreach($p as $sct=>$msg) if($msg) $out =        "$msg"  ;return $out;} // same, without section name
        if(!$set) return $p[$section];
        if( $set)        {unset($p[$section]);$p[$section] = $set;} // make it the last one
        return false; // so that it can be called like "return problemWith()"
    }

    public static function get($all=0) {
        if($all==2) return Problems::with("**");
        if($all==1) return Problems::with("*");
        if($all==0) return Problems::with("last+");
        return Problems::with($all); // for direct get('last')-s
    }

    public static function reset($markerName="noname") {
        global $DKTools;
        $p = &$DKTools["Problems"]["Storage"];
        $m = &$DKTools["Problems"]["Markers"];
        $m[$markerName] = count($p);
    }

    public static function since($markerName="noname") {
        global $DKTools;
        $p = &$DKTools["Problems"]["Storage"];
        $m = &$DKTools["Problems"]["Markers"];
        return count($p) - $m[$markerName];
    }

    public static function occurred() {
        return self::since();
    }

    public static function exitIfAny() {
        if(Problems::occurred()) die(Problems::with("last+"));
    }


}

