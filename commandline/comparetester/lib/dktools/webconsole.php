<?php #crossProject

$htmlflush_blank_size = 8192; // amount of blank to make sure client will refresh

function htmlflush($len=0,$scrollDown=1) {
    global $htmlflush_blank_size;
    if(!$len) $len=$htmlflush_blank_size;
    if($len<300) $len=$len*1024;
    $blank = str_repeat(" ",$len);
    $blank = "<!-- $blank\n -->";
    if($scrollDown) print "$blank<script> window.scrollBy(0,9999); </script>";
    flush();
}


function xflush() {
    htmlflush();
}


