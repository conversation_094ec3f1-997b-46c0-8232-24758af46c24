<?php

function help() {
    $text = "

        |
        |       Syntax:
        |
        |           comparetester <command> [<script>] [<options> ...]
        |
        |
        |       Commands:
        |
        |           install     Prepare everything for a first run
        |           run         Run script; it should be in ./scripts, or a relative path
        |           info        Show status info (installation check, running instances)
        |           stop        Stop any running instances
        |
        |
        |       Options for 'run':
        |
        |           -r      --save-reference        Save current results as reference
        |           -p      --entry-point           Label to start script from
        |           -v      --verbose               Say what's happening
        |
        |       This tool downloads a page from TTWA/Ease and extracts data from the html.
        |       It needs a script (see https://innote.login.hu/projects/tools/comparetester)
        |       and some writable folders under ttwa:
        |
        |           (rwx)   ttwa/webroot/file_storage/comparetester
        |           (rwx)   ttwa/runtime/comparetester
        |           (r x)   ttwa/commandline/comparetester/scripts
        |

    ";

    foreach(explode("\n",trim($text))as $x) print substr(ltrim(trim($x),"|"),3)."\n";
}

