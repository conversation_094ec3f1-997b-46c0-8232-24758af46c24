:Start

    client flex                                                           //  ez beállítja a clienturl-t is
    clienturl https://flex-treebeard-ease82.login.hu                      //  de mi óverrájdoljuk

    login as *********, Balaton111                               //  ki<PERSON><PERSON><PERSON> a j<PERSON><PERSON> (ha nem akarod, ho<PERSON>, ak<PERSON> "show" né<PERSON><PERSON>l ugy<PERSON>z)

            set searchInput[valid_date]         = 2023-02-28                        //  változók a következő requesthez
            set searchInput[company]            = ALL
            set searchInput[payroll]            = ALL
            set searchInput[workgroup]          = ALL
            set searchInput[unit]               = ALL
            set searchInput[employee_contract]  = ALL
            set searchInput[interval_type]      = date
            set searchInput[locked]             = unlocked
            set gridParams[objboxHeight]        = 70000                             //  Ebből tudja, hány sort hozzon le
            set forceLoad                       = 1                                 //  Úgy tűnik, ez kellett
            set regenerate                      = 1                                 //  Nem tudjuk, de legyen 1

        post customers/flex/frameReportFromBalanceManagement/getGridMultiHeaders?gridID=dhtmlxGrid
        post customers/flex/frameReportFromBalanceManagement/setInitProperties?gridID=dhtmlxGrid              
        post customers/flex/frameReportFromBalanceManagement/setPostInitProperties?gridID=dhtmlxGrid          
        post customers/flex/frameReportFromBalanceManagement/setLoadInitProperties?gridID=dhtmlxGrid
        post customers/flex/frameReportFromBalanceManagement/setColumnProperties?gridID=dhtmlxGrid

        print

        for $id in 000 

        print --- Getting data for workgroup_id = $id ------------------------------------------------------------------
            // set searchInput[workgroup] = $id
        post customers/flex/frameReportFromBalanceManagement/gridData?gridID=dhtmlxGrid
            this is keretidosReport_flex.$id.jhtml
            convert keretidosReport_flex.$id.jhtml to json using report grid decoder
            convert keretidosReport_flex.$id.json to csv
            sortCSV keretidosReport_flex.$id.csv
            check keretidosReport_flex.$id.csv.sorted
            print
        end

:Logout
    logout
    stop