
client hardrock
clienturl https://hardrock-treebeard-ease82.login.hu

		for $month in 01,02,03,04,05,06,07,08,09

			login as root, H4kuN4M4t4_T4!

			set searchInput[valid_date]: @date(2023,$month,1)
			set searchInput[company]: ALL
			set searchInput[payroll]: ALL
			set searchInput[workgroup]: ALL
			set searchInput[unit]: ALL
			// set searchInput[employee_contract]: ALL 
			set searchInput[ptrMode]: ptr 	//month
			set mode: DEFAULT
			set locked: 0

			post /payrollTransfer/payroll?dencihoztalropit

			this is payroll-file-list.$month.json
			convert payroll-file-list.$month.json to csv using report fetcher ($month)

			sortCSV $month.piramis.csv
			  check $month.piramis.csv.sorted

			logout
		end
