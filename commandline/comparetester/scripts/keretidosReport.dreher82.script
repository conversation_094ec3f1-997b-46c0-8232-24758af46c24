:Start

    client dreher                                                           //  ez beállítja a clienturl-t is
    clienturl https://dreher-treebeard-ease82.login.hu                      //  de mi óverr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

    login as root, H4kuN4M4t4_T4!                                           //  kií<PERSON><PERSON> a j<PERSON><PERSON> (ha nem akarod, hogy l<PERSON>, ak<PERSON> "show" nélkül ugyanez)

    set searchInput[valid_date]         = 2022-12-31                        //  változók a következő requesthez
    set searchInput[company]            = ALL
    set searchInput[payroll]            = ALL
    set searchInput[workgroup]          = ALL
    set searchInput[unit]               = ALL
    set searchInput[employee_contract]  = ALL
    set searchInput[interval_type]      = date
    set gridParams[objboxHeight]        = 70000                               //  Ebből tudja, hány sort hozzon le
    set forceLoad                       = 1                                 //  Úgy tűnik, ez kellett
    set regenerate                      = 1                                 //  Nem tudjuk, de legyen 1

    post wfm/frameReportFromBalanceManagement/getGridMultiHeaders?gridID=dhtmlxGrid
    post wfm/frameReportFromBalanceManagement/setInitProperties?gridID=dhtmlxGrid              //  Biztos van, amit sessionből tud majd meg; tegyük oda neki
    post wfm/frameReportFromBalanceManagement/setPostInitProperties?gridID=dhtmlxGrid          //  Ugyanez a sztori, nem tudom, melyik kell és melyik nem
    post wfm/frameReportFromBalanceManagement/setLoadInitProperties?gridID=dhtmlxGrid
    post wfm/frameReportFromBalanceManagement/setColumnProperties?gridID=dhtmlxGrid

    print

    for $id in 000 // 07f516b7855e0c66471b740c071ef460,1,10,10c599d02a2ee2d2adb0a257fcba8419,11,12,13,14,14b2e1f92338efdab33af74bcc885fa3,15,16,17,18,19,1a08e713c2c6e1d5c61c47a651de802e

        print --- Getting data for workgroup_id = $id ------------------------------------------------------------------
        // set searchInput[workgroup] = $id
        post wfm/frameReportFromBalanceManagement/gridData?gridID=dhtmlxGrid
        this is keretidosReport.$id.jhtml
        convert keretidosReport.$id.jhtml to json using report grid decoder
        convert keretidosReport.$id.json to csv
        check keretidosReport.$id.csv
        print
    end

:Logout
    logout
    stop