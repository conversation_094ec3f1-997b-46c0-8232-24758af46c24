

:Start

    client flex                                                           //  ez beállítja a clienturl-t is
    clienturl https://flex-treebeard-ease.login.hu/                          //  de mi óver<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

    login as root, Li@BUG4everH4ppy4pr1!                                      //  kií<PERSON><PERSON> a j<PERSON><PERSON> (ha nem <PERSON>, ho<PERSON>, ak<PERSON> "show" nélk<PERSON>l ugyanez)

    set searchInput[inside_type_id]     = ALL
    set searchInput[valid_from]         = 2023-01-01
    set searchInput[valid_to]           = 2023-02-28
    set searchInput[company]            = ALL
    set searchInput[location]           = ALL
    set searchInput[payroll]            = ALL
    // set searchInput[workgroup]          = ALL
    set searchInput[unit]               = ALL
    set searchInput[company_org_group2] = ALL
    set searchInput[cost_id]            = ALL
    set searchInput[employee_contract]  = ALL
    set searchInput[useTimeCalculation] = 0
    set searchInput[statusfilter]       = ALL
    set gridParams[objboxHeight]        = 900000
    set forceLoad                       = 1                     
    set regenerate                      = 1                                 

    post wfm/employeeCalcReport/setInitProperties?gridID=dhtmlxGrid
    post wfm/employeeCalcReport/getGridMultiHeaders?gridID=dhtmlxGrid
    post wfm/employeeCalcReport/getColumnProperties?gridID=dhtmlxGrid
    post wfm/employeeCalcReport/setPostInitProperties?gridID=dhtmlxGrid
    post wfm/employeeCalcReport/setLoadInitProperties?gridID=dhtmlxGrid
    post wfm/employeeCalcReport/gridData?gridID=dhtmlxGrid


    print

    for $wid in cc3bf17fb32d4c8db10025286d817e07 //, 15, 45, eee4c6e2011f9ed87c853bd616ed8bd3, 42a705b1a62475926d16a6e202eac58f, 625a033d2b5ba0272ca1802f3074e4b6, f0cc3bb2a6d0d548fbaa69a71d9e12f7, 52, ce3cea4acdb5254620cd904be382c4ab, 9257478f6dfc8ab009eaefc51d90f5a7, 35b397ad1e16ca320af0cd6b3fb41bf6, a2bd683d54d29f3d34604b2ceaaee433, 8564321293dffebf1196279e3110e6f1, 51, b0d1a171db73dcbc5e98c326179140cd, 42, d0afc7fe38e3de8475768f6012dd2b39, 6902abb303c10417b4c850a6fd77d22f, b120cc368a9448858b1bab2fc3972c67, 28, f0f619ee7385775629cf5c7492cebf83, 10, 08c23ad4244d918838587b628636929d, 00a7569f860c2d7ff62ab20419c83e2f, 13, 47, 49, 37, 29, b35438f520ebcd0d2a73d5118cefe1ba, 9c5deffe2a40699251dacb999331072e, 91fde22a12d5d112b166956f16a24182, 722156a3e0d5071c6a4c82d4db3db092, 40, 052802790564700af4cd1967b1c2ad78, 65da4eeb1a265fd4bf473a1bec672b0b, 44, b1ebeaa9a79274b0bc2ee64b2950090d, 9, 55, 22, 3c411a80b11340324ed703fe29e95e5f, 65331f39cf49a42a3c7cc8b2c9281f31, 18, fcc122219099e6d1e50843dc46714696



    print --- Getting data for workgroup_id = $wid ------------------------------------------------------------------
        set searchInput[workgroup] = $wid 
        
    post wfm/employeeCalcReport/gridData?gridID=dhtmlxGrid
        this is employeeCalcReport_flex.$wid.jhtml
        convert employeeCalcReport_flex.$wid.jhtml to json using report grid decoder
        convert employeeCalcReport_flex.$wid.json to csv
        check employeeCalcReport_flex.$wid.csv
        print
    end

:Logout
    logout
    stop

