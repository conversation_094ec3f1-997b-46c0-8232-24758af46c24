:Start

    client bos                                                          //  ez beállítja a clienturl-t is
    clienturl https://bos-treebeard-ease82.login.hu                       //  de mi óverr<PERSON>j<PERSON><PERSON><PERSON><PERSON>

    login as root, H4kuN4M4t4_T4!                                             //  kií<PERSON><PERSON> a j<PERSON><PERSON> (ha nem akarod, ho<PERSON>, ak<PERSON> "show" nélkül ugyanez)

            set searchInput[valid_date]         = 2023-12-31                        //  változók a következő requesthez
            set searchInput[company]            = ALL
            set searchInput[payroll]            = ALL
            set searchInput[workgroup]          = ALL
            set searchInput[unit]               = ALL
            set searchInput[employee_contract]  = ALL
            //set searchInput[interval_type]      = date
            set gridParams[objboxHeight]        = 70000                             //  Ebből tudja, hány sort hozzon le
            set forceLoad                       = 1                                 //  Úgy tűnik, ez kellett
            set regenerate                      = 1                                 //  Nem tudjuk, de legyen 1

        post wfm/balanceManagement/getGridMultiHeaders?gridID=dhtmlxGrid
        post wfm/balanceManagement/setInitProperties?gridID=dhtmlxGrid              //  Biztos van, amit sessionből tud majd meg; tegyük oda neki
        post wfm/balanceManagement/setPostInitProperties?gridID=dhtmlxGrid          //  Ugyanez a sztori, nem tudom, melyik kell és melyik nem
        post wfm/balanceManagement/setLoadInitProperties?gridID=dhtmlxGrid
        post wfm/balanceManagement/setColumnProperties?gridID=dhtmlxGrid

        print

        for $id in 000 

        print --- Getting data for workgroup_id = $id ------------------------------------------------------------------
            // set searchInput[workgroup] = $id
        post wfm/balanceManagement/gridData?gridID=dhtmlxGrid
            this is egyenleg_kezeles_bos.$id.jhtml
            convert egyenleg_kezeles_bos.$id.jhtml to json using report grid decoder
            convert egyenleg_kezeles_bos.$id.json to csv
            sortCSV egyenleg_kezeles_bos.$id.csv
            check egyenleg_kezeles_bos.$id.csv.sorted
            print
        end

:Logout
    logout
    stop