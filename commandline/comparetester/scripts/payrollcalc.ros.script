
client rosenberger
clienturl https://ros-treebeard-ease.login.hu

		for $month in 01,02,03,04,05,06,07,08,09,10,11,12

			// login as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>
			login as root, H4kuN4M4t4_T4!

			set searchInput[valid_from]: @date(2022,$month,1)
			set searchInput[valid_to]: @date(2022,$month,99)
			set searchInput[company]: ALL
			set searchInput[payroll]: ALL
			set searchInput[workgroup]: 2,4,6,8,12,14,20
			set searchInput[unit]: ALL
			// set searchInput[employee_contract]: ALL // ef7cbbfbab1fe4bb951a9249bbaa11c6
			set searchInput[ptrMode]: ptr
			set mode: DEFAULT
			set locked: 0

			post /payrollTransfer/payroll?dencihoztalropit

			this is payroll-file-list.$month.json
			convert payroll-file-list.$month.json to csv using report fetcher ($month)

			check $month.NBMunkr.csv
			check $month.NBMunkrOra.csv
			check $month.NBIdoadat.csv
			check $month.NBIdoadatOra.csv
			check $month.NBPBeoszt.csv
			check $month.NBKifiz.csv

			logout
		end