
client kometa
clienturl https://kometa-treebeard-ease82.login.hu

		for $month in 06,07,08,09,10

			login as root, H4kuN4M4t4_T4!

			set searchInput[valid_date]: @date(2023,$month,1)
			set searchInput[company]: ALL
			set searchInput[payroll]: ALL
			set searchInput[workgroup]: ALL
			set searchInput[unit]: ALL
			set searchInput[quitEmployee]: no 
			set searchInput[ptrMode]: ptr 	//month
			set mode: DEFAULT
			set locked: 0

			post /payrollTransfer/payroll?dencihoztalropit

			this is AbacusD_karb_m$month.json
			this is AbacusT_karb_m$month.json

			convert AbacusD_karb_m$month.json to csv using report fetcher
			convert AbacusT_karb_m$month.json to csv using report fetcher

			check AbacusD_karb_m$month.csv
			check AbacusT_karb_m$month.csv

			logout
		end


		// adatbázis frissítés után az app_settings -et <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy XLSX helyett CSV legyen: abacus_ptr_file_type !!!
