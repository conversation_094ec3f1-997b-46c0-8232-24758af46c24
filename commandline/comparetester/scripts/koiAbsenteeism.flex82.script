:Start

    client flex                                                           //  ez beállítja a clienturl-t is
    clienturl https://flex-treebeard-ease82.login.hu                       //  de mi óver<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

    login as root, H4kuN4M4t4_T4!                                            //  kiír<PERSON> a j<PERSON>t (ha nem <PERSON>, ho<PERSON>, ak<PERSON> "show" nélk<PERSON>l ugyanez)

            set searchInput[valid_from]             = 2023-01-01   
            set searchInput[valid_to]               = 2023-02-28  
            set searchInput[mep][]                    = ALL
            set searchInput[location][]               = ALL
            set searchInput[unit_id][]                = ALL
            set searchInput[cost_id][]                = ALL
            set searchInput[company_org_group2_id][]  = ALL
            set searchInput[dl_idl]                   = ALL
            set searchInput[format]                   = absolute
            set searchInput[state_type]               = total
            set gridParams[objboxHeight]        = 70000                             
            set forceLoad                       = 1                                 //  Úgy tűnik, ez kellett
            set regenerate                      = 1                                 //  Nem tud<PERSON>, de legyen 1


        print Sending setLoadInitProperties request ...

        post customers/flex/reportKOIAbsenteeism/getGridMultiHeaders?gridID=dhtmlxGrid
        post customers/flex/reportKOIAbsenteeism/setInitProperties?gridID=dhtmlxGrid              
        post customers/flex/reportKOIAbsenteeism/setPostInitProperties?gridID=dhtmlxGrid          
        post customers/flex/reportKOIAbsenteeism/setLoadInitProperties?gridID=dhtmlxGrid
        post customers/flex/reportKOIAbsenteeism/getColumnProperties?gridID=dhtmlxGrid

        print Sending gridData request ...
            
        post customers/flex/reportKOIAbsenteeism/gridData?gridID=dhtmlxGrid

            this is reportKOIAbsenteeism.jhtml
            convert reportKOIAbsenteeism.jhtml to json using report grid decoder
            convert reportKOIAbsenteeism.json to csv
            
:quicktest

            client flex // ha esetleg itt ébredtünk, -p opció okán
            check reportKOIAbsenteeism.csv
            
        logout
        stop


        print