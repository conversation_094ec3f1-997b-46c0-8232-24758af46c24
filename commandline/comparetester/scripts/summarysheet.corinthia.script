

:Start

    client corinthia                                                        //  ez beállítja a clienturl-t is
    clienturl https://corinthia.stage-ttwa.login.hu/                        //  de mi óverrájdolju<PERSON>

    show login as root, Li@BUG4everH4ppy4pr1!                                        //  ki<PERSON><PERSON><PERSON> a j<PERSON> (ha nem akarod, hogy <PERSON>, ak<PERSON> "show" nélkül ugyanez)

    set searchInput[valid_from]         = 2018-05-01                        //  változók a következő requesthez
    set searchInput[valid_to]           = 2018-05-30
    set searchInput[company]            = ALL
    set searchInput[payroll]            = ALL
    set searchInput[workgroup]          = ALL
    set searchInput[unit]               = ALL
    set searchInput[company_org_group1] = ALL
    //t searchInput[employee_contract]  = ALL
    set gridParams[objboxHeight]        = 705                               //  Ebből tudja, hány sort hozzon le
    set forceLoad                       = 1                                 //  <PERSON>gy tűnik, ez kellett
    set regenerate                      = 1                                 //  Nem tud<PERSON>, de legyen 1
    post wfm/summarySheet/setInitProperties?gridID=mainGrid                 //  Biz<PERSON> van, amit sessionből tud majd meg; tegyük oda neki
    post wfm/summarySheet/setPostInitProperties?gridID=mainGrid             //  Ugyanez a sztori, nem tudom, melyik kell és melyik nem
    print

    for $id in 21, 40, 77

        print --- Getting data for ECID=$id ------------------------------------------------------------------
        set searchInput[employee_contract] = $id
        post wfm/summarySheet/gridData?gridID=mainGrid
        this is summarysheet.$id.jhtml
        convert summarysheet.$id.jhtml to json using daily grid decoder
        convert summarysheet.$id.jhtml to csv
        convert summarysheet.$id.jhtml to txt
        check summarysheet.$id.txt
        print

    end

:Logout

    logout
    stop

