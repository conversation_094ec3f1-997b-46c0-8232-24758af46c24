:Start

    client bos                                                           //  ez beállítja a clienturl-t is
    clienturl https://bos-treebeard-ease81.login.hu/                       //  de mi óverr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

    login as gpinter, Balaton1                                             //  ki<PERSON><PERSON><PERSON> a j<PERSON><PERSON><PERSON><PERSON> (ha nem akarod, ho<PERSON>, ak<PERSON> "show" nélk<PERSON>l ugyanez)

            set searchInput[valid_date]         = 2020-12-31                        //  változók a következő requesthez
            set searchInput[company]            = ALL
            set searchInput[payroll]            = ALL
            set searchInput[workgroup]          = ALL
            set searchInput[unit]               = ALL
            set searchInput[company_org_group1] = ALL
            set searchInput[company_org_group3] = ALL
            set searchInput[interval_type]      = date
            set searchInput[employee_contract]  = ALL
            set gridParams[objboxHeight]        = 700000                               //  Ebből tudja, hány sort hozzon le
            set forceLoad                       = 1                                 //  <PERSON>gy tűnik, ez kellett
            set regenerate                      = 1                                 //  Nem tud<PERSON>, de legyen 1

        post wfm/frameReportFromBalanceManagement/getGridMultiHeaders?gridID=dhtmlxGrid
        post wfm/frameReportFromBalanceManagement/setInitProperties?gridID=dhtmlxGrid             //  Biztos van, amit sessionből tud majd meg; tegyük oda neki
        post wfm/frameReportFromBalanceManagement/setPostInitProperties?gridID=dhtmlxGrid          //  Ugyanez a sztori, nem tudom, melyik kell és melyik nem
        post wfm/frameReportFromBalanceManagement/setLoadInitProperties?gridID=dhtmlxGrid
        post wfm/frameReportFromBalanceManagement/setColumnProperties?gridID=dhtmlxGrid
        
        print

        for $id in 000 

        print --- Getting data for workgroup_id = $id ------------------------------------------------------------------
            // set searchInput[workgroup] = $id
        post wfm/frameReportFromBalanceManagement/gridData?gridID=dhtmlxGrid
            this is keretidosReport_bos.$id.jhtml
            convert keretidosReport_bos.$id.jhtml to json using report grid decoder
            convert keretidosReport_bos.$id.json to csv
            sortCSV keretidosReport_bos.$id.csv
            check keretidosReport_bos.$id.csv
            print
        end

:Logout
    logout
    stop