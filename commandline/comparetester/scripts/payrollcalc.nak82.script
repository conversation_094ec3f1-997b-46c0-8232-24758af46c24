
client nak
clienturl https://nak-treebeard-ease82.login.hu

		for $month in 01,02,03,04,05,06,07,08,09

			// login as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>on1
			login as root, H4kuN4M4t4_T4!

			set searchInput[valid_date]: @date(2023,$month,1)
			set searchInput[company]: ALL
			set searchInput[payroll]: ALL
			set searchInput[workgroup]: ALL
			set searchInput[unit]: ALL
			set searchInput[searchInput_company_org_group1]: ALL
			set searchInput[searchInput_company_org_group2]: ALL
			set searchInput[searchInput_company_org_group3]: ALL
			// set searchInput[employee_contract]: ALL 
			set searchInput[ptrMode]: ptr 	//month
			set mode: DEFAULT
			set locked: 0

			post /payrollTransfer/payroll?dencihoztalropit

			this is payroll-file-list.$month.json
			convert payroll-file-list.$month.json to csv using report fetcher ($month)

			sortCSV $month.kulcssoft_Jovedelem__2023$month.csv
			  check $month.kulcssoft_Jovedelem__2023$month.csv.sorted

			//sortCSV $month.kulcssoft_Munkarend_Jéger_2023$month.csv
			  //check $month.kulcssoft_Munkarend_Jéger_2023$month.csv.sorted

			sortCSV $month.kulcssoft_Munkarend__2023$month.csv
			  check $month.kulcssoft_Munkarend__2023$month.csv.sorted

			//sortCSV $month.kulcssoft_Jelenlet_Tavollet_Jéger_2023$month.csv
			  //check $month.kulcssoft_Jelenlet_Tavollet_Jéger_2023$month.csv.sorted

			sortCSV $month.kulcssoft_Jelenlet_Tavollet__2023$month.csv
			  check $month.kulcssoft_Jelenlet_Tavollet__2023$month.csv.sorted
			

			logout
		end
