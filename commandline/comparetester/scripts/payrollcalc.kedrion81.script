
client kedrion
clienturl https://kedrion-treebeard-ease81.login.hu

		for $month in 01,02,03,04,05,06,07

			login as root, H4kuN4M4t4_T4!

			set searchInput[valid_date]: @date(2023,$month,1)
			set searchInput[company]: ALL
			set searchInput[payroll]: ALL
			set searchInput[workgroup]: ALL
			set searchInput[unit]: ALL
			// set searchInput[employee_contract]: ALL // ef7cbbfbab1fe4bb951a9249bbaa11c6
			set searchInput[quitEmployee]: no
			set searchInput[ptrMode]: ptr
			set mode: DEFAULT
			set locked: 0

			post /payrollTransfer/payroll?dencihoztalropit

			this is payroll-file-list.$month.json
			convert payroll-file-list.$month.json to csv using report fetcher ($month)

	
			sortCSV $month.NBIdoadat.csv
			check $month.NBIdoadat.csv.sorted
			sortCSV $month.NBTortNapiTav.csv
			check $month.NBTortNapiTav.csv.sorted
			sortCSV $month.NBKifiz_Korr.csv
			check $month.NBKifiz_Korr.csv.sorted
			sortCSV $month.NBMunkr.csv
			check $month.NBMunkr.csv.sorted
			sortCSV $month.NBMunkrOra.csv
			check $month.NBMunkrOra.csv.sorted
			sortCSV $month.NBIdoadatOra.csv
			check $month.NBIdoadatOra.csv.sorted
			sortCSV $month.NBKifiz.csv		
			check $month.NBKifiz.csv.sorted


			logout
		end
