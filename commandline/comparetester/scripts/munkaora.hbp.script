:Start

    client kedrion                                                           //  ez beáll<PERSON>tja a clienturl-t is
    clienturl https://kedrion-treebeard-ease.login.hu/                       //  de mi óverr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

    login as root, H4kuN4M4t4_T4!                                            //  kií<PERSON><PERSON> a jels<PERSON><PERSON>t (ha nem akarod, ho<PERSON>, ak<PERSON> "show" n<PERSON><PERSON><PERSON>l ugyanez)

            set searchInput[valid_from]             = 2021-01-01   
            set searchInput[valid_to]               = 2021-12-31  
            set searchInput[company]                = ALL
            set searchInput[payroll]                = ALL
            set searchInput[unit_id]                = ALL
            set searchInput[company_org_group3]     = ALL
            set searchInput[employee_contract]      = ALL
                        
            set gridParams[objboxHeight]        = 70000                             
            set forceLoad                       = 1                                 //  <PERSON>gy tűnik, ez kellett
            set regenerate                      = 1                                 //  Nem tudjuk, de legyen 1


        print Sending setLoadInitProperties request ...
                    
        post wfm/reportWorkhourList/setLoadInitProperties?gridID=dhtmlxGrid         
        
        print Sending gridData request ...
            
        post wfm/reportWorkhourList/gridData?gridID=dhtmlxGrid

            this is HBP_munkaora_report.jhtml
            convert HBP_munkaora_report.jhtml to json using report grid decoder
            convert HBP_munkaora_report.json to csv
            
:quicktest

            client kedrion // ha esetleg itt ébredtünk, -p opció okán
            check HBP_munkaora_report.csv

        logout
        stop


        print