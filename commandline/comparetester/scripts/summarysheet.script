

:Start

    client rosenberger                                                              //  ez beállítja a clienturl-t is
    clienturl https://ros-treebeard-ease.login.hu/                           //  de mi óverr<PERSON>jdolju<PERSON>

    login as root, Li@BUG4everH4ppy4pr1!                                             //  egészen nyilvánvaló
    // show login as root, Li@BUG4everH4ppy4pr1!                                     //  kiírja a jelszót

    set searchInput[valid_from]         = 2019-05-01                        //  változók a következő requesthez
    set searchInput[valid_to]           = 2019-05-30
    set searchInput[company]            = ALL
    set searchInput[payroll]            = ALL
    set searchInput[workgroup]          = ALL
    set searchInput[unit]               = ALL
    set searchInput[company_org_group1] = ALL
    //t searchInput[employee_contract]  = ALL
    set gridParams[objboxHeight]        = 705                               //  Ebből tudja, hány sort hozzon le
    set forceLoad                       = 1                                 //  Úgy tűnik, ez kellett
    set regenerate                      = 1                                 //  Nem tud<PERSON>, de legyen 1
    post wfm/summarySheet/setInitProperties?gridID=mainGrid                 //  Biz<PERSON> van, amit sessionből tud majd meg; tegyük oda neki
    post wfm/summarySheet/setPostInitProperties?gridID=mainGrid             //  Ugyanez a sztori, nem tudom, melyik kell és melyik nem
    print

    for $id in 84288227511, 84112128151, 84663500981

        print --- Getting data for ECID=$id ------------------------------------------------------------------
        set searchInput[employee_contract] = $id
        post wfm/summarySheet/gridData?gridID=mainGrid
        this is summarysheet.$id.jhtml
        convert summarysheet.$id.jhtml to json
        convert summarysheet.$id.jhtml to csv
        convert summarysheet.$id.jhtml to txt
        check summarysheet.$id.txt
        print

    end

:Logout

    logout
    stop


:quicktest

    client bos
    for $id in 84288227511, 84112128151, 84663500981

        print --- Getting data for ECID=$id ------------------------------------------------------------------
        set searchInput[employee_contract] = $id
        convert summarysheet.$id.jhtml to json
        convert summarysheet.$id.jhtml to csv
        convert summarysheet.$id.jhtml to txt
        check summarysheet.$id.txt
        print

    end

