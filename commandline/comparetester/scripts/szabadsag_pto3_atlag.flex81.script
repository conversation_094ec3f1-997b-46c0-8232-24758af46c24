
:Start

    client flex                                                            //  ez beállítja a clienturl-t is
    clienturl https://flex-treebeard-ease81.login.hu/                          //  de mi óver<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

    
            login as root, H4kuN4M4t4_T4!

             set searchInput[valid_to]: 2023-02-28
             set searchInput[type]: 'Kivett s<PERSON>g átlag'
             set searchInput[active]: ALL
             
             set gridParams[objboxHeight]: 7160
             
            print Sending setLoadInitProperties request ...
            post /customers/flex/annualLeaveListPTO3/setLoadInitProperties?gridID=dhtmlxGrid

            print Sending gridData request ...
            post /customers/flex/annualLeaveListPTO3/gridData?gridID=dhtmlxGrid

            this is annualLeaveListPTO3_atlag.jhtml
            convert annualLeaveListPTO3_atlag.jhtml to json using report grid decoder
            convert annualLeaveListPTO3_atlag.json to csv
            
            
:quicktest

            client flex // ha esetleg itt ébredtünk, -p opci<PERSON> okán
            check annualLeaveListPTO3_atlag.csv

        logout
        stop


        print
            