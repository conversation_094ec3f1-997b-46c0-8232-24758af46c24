:Start

    client HBP                                                          //  ez beállítja a clienturl-t is
    clienturl https://kedrion-treebeard-ease.login.hu/                       //  de mi óverrájdoljuk

    login as 17430, Balaton1                                             //  kií<PERSON><PERSON> a j<PERSON><PERSON><PERSON><PERSON> (ha nem akarod, ho<PERSON>, a<PERSON><PERSON> "show" nélk<PERSON>l ugyanez)

            set searchInput[valid_date]         = 2021-12-19                        //  változók a következő requesthez
            set searchInput[company]            = ALL
            set searchInput[payroll]            = ALL
            set searchInput[workgroup]          = ALL
            set searchInput[unit]               = ALL
            set searchInput[employee_contract]  = ALL
            set searchInput[interval_type]      = date
            set gridParams[objboxHeight]        = 70000                             //  Ebből tudja, hány sort hozzon le
            set forceLoad                       = 1                                 //  Úgy tűnik, ez kellett
            set regenerate                      = 1                                 //  Nem tudjuk, de legyen 1

        post wfm/frameReportFromBalanceManagement/getGridMultiHeaders?gridID=dhtmlxGrid
        post wfm/frameReportFromBalanceManagement/setInitProperties?gridID=dhtmlxGrid              //  Biz<PERSON> van, amit sessionből tud majd meg; tegyük oda neki
        post wfm/frameReportFromBalanceManagement/setPostInitProperties?gridID=dhtmlxGrid          //  Ugyanez a sztori, nem tudom, melyik kell és melyik nem
        post wfm/frameReportFromBalanceManagement/setLoadInitProperties?gridID=dhtmlxGrid
        post wfm/frameReportFromBalanceManagement/setColumnProperties?gridID=dhtmlxGrid

        print

        for $id in 000 

        print --- Getting data for workgroup_id = $id ------------------------------------------------------------------
            // set searchInput[workgroup] = $id
        post wfm/frameReportFromBalanceManagement/gridData?gridID=dhtmlxGrid
            this is keretidosReport_HBP.$id.jhtml
            convert keretidosReport_HBP.$id.jhtml to json using report grid decoder
            convert keretidosReport_HBP.$id.json to csv
            sortCSV keretidosReport_HBP.$id.csv
            check keretidosReport_HBP.$id.csv.sorted
            print
        end

:Logout
    logout
    stop
