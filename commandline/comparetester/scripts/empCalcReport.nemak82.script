

:Start

    client nemak                                                            //  ez beállítja a clienturl-t is
    clienturl https://nemak-treebeard-ease82.login.hu                         //  de mi óver<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

    login as root, H4kuN4M4t4_T4!                                      //  kií<PERSON><PERSON> a j<PERSON>t (ha nem aka<PERSON>, ho<PERSON>, ak<PERSON> "show" nélkül ugyanez)

    set searchInput[inside_type_id]     = ALL
    set searchInput[valid_from]         = 2023-01-01
    set searchInput[valid_to]           = 2023-12-31
    set searchInput[company]            = ALL
    set searchInput[payroll]            = ALL
    set searchInput[workgroup]          = ALL
    set searchInput[unit]               = ALL
    set searchInput[company_org_group1] = ALL
    set searchInput[company_org_group3] = ALL
    set searchInput[employee_contract]  = ALL
    set searchInput[useTimeCalculation] = 0
    set searchInput[statusfilter]       = ALL
    set gridParams[objboxHeight]        = 900000
    set forceLoad                       = 1                     
    set regenerate                      = 1                                 

    post wfm/employeeCalcReport/setInitProperties?gridID=dhtmlxGrid
    post wfm/employeeCalcReport/getGridMultiHeaders?gridID=dhtmlxGrid
    post wfm/employeeCalcReport/getColumnProperties?gridID=dhtmlxGrid
    post wfm/employeeCalcReport/setPostInitProperties?gridID=dhtmlxGrid
    post wfm/employeeCalcReport/setLoadInitProperties?gridID=dhtmlxGrid
    post wfm/employeeCalcReport/gridData?gridID=dhtmlxGrid


    print

    for $id in 000
        

    print --- Getting data for workgroup_id = $id ------------------------------------------------------------------
       // set searchInput[workgroup] = $id 
        
    post wfm/employeeCalcReport/gridData?gridID=dhtmlxGrid
        this is employeeCalcReport_nemak.$id.jhtml
        convert employeeCalcReport_nemak.$id.jhtml to json using report grid decoder
        convert employeeCalcReport_nemak.$id.json to csv
        check employeeCalcReport_nemak.$id.csv
        print
    end

:Logout
    logout
    stop

