

:Start

    client nemak                                                            //  ez beállítja a clienturl-t is
    clienturl https://nemak-treebeard-ease.login.hu/                          //  de mi óverrájdoljuk

    // login as teszt.admin, Ba<PERSON>on1    
    login as root, H4kuN4M4t4_T4!                                   //  kií<PERSON><PERSON> a j<PERSON><PERSON> (ha nem akarod, hogy <PERSON>, ak<PERSON> "show" nélkül ugyanez)

    set searchInput[valid_from]: 2020-01-01
    set searchInput[valid_to]: 2020-12-31
    set searchInput[company]: ALL
    set searchInput[payroll]: ALL
    set searchInput[unit]: ALL
    set searchInput[company_org_group3]: ALL
    set searchInput[employee_contract]: ALL
    set searchInput[csvstatusfilter]: 2
    set gridParams[objboxHeight]: 716

    print Sending setLoadInitProperties request ...
    post /customers/nemak/hrKpiReportYear/setLoadInitProperties?gridID=dhtmlxGrid

    set forceLoad: 1
    set currentPage: 0
    set regenerate: 1

    print Sending gridData request ...
    post /customers/nemak/hrKpiReportYear/gridData?gridID=dhtmlxGrid

    // copy report as hrKpiReportYear.csv

:quicktest

    client nemak // ha esetleg itt ébredtünk, -p opció okán

    copy report as hrKpiReportYear.csv
    check hrKpiReportYear.csv
    // logout
    // stop

    // convert hrKpiReportYear.jhtml to json using report grid decoder
    // convert hrKpiReportYear.json to txt
    // check hrKpiReportYear.txt
    print

:Logout

    logout
    stop

