
client samyang
clienturl https://samyang-treebeard-ease82.login.hu

		for $month in 07,08,09,10,11,12

			login as root, H4kuN4M4t4_T4!

			set searchInput[valid_date]: @date(2023,$month,1)
			set searchInput[company]: ALL
			set searchInput[payroll]: ALL
			set searchInput[workgroup]: ALL
			set searchInput[unit]: ALL
			// set searchInput[employee_contract]: ALL // ef7cbbfbab1fe4bb951a9249bbaa11c6
			set searchInput[quitEmployee]: no
			set searchInput[ptrMode]: ptr
			set mode: DEFAULT
			set locked: 0

			post /payrollTransfer/payroll?dencihoztalropit

			this is payroll-file-list.$month.json
			convert payroll-file-list.$month.json to csv using report fetcher ($month)

	
			sortCSV $month.kulcssoft_Jelenlet_Tavollet_2023$month.csv
			check $month.kulcssoft_Jelenlet_Tavollet_2023$month.csv.sorted
			sortCSV $month.kulcssoft_Jövedelem_2023$month.csv
			check $month.kulcssoft_Jövedelem_2023$month.csv.sorted
			

			logout
		end
