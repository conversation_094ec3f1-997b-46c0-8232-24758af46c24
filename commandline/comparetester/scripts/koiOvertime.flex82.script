:Start

    client flex                                                           //  ez be<PERSON>ll<PERSON>tja a clienturl-t is
    clienturl https://flex-treebeard-ease82.login.hu                      //  de mi óver<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

    login as root, H4kuN4M4t4_T4!                                            //  kiír<PERSON> a j<PERSON>t (ha nem <PERSON>, ho<PERSON>, ak<PERSON> "show" nélk<PERSON>l ugyanez)

            set searchInput[valid_from]             = 2023-01-01   
            set searchInput[valid_to]               = 2023-02-28 
            set searchInput[mep][]                    = ALL
            set searchInput[location][]               = ALL
            set searchInput[unit_id][]                = ALL
            set searchInput[cost_id][]                = ALL
            set searchInput[company_org_group2_id][]  = ALL
            set searchInput[dl_idl]                   = ALL
            set searchInput[format]                   = absolute
            set searchInput[locked]                   = ALL
            set gridParams[objboxHeight]        = 70000                             
            set forceLoad                       = 1                                 //  Úgy tűnik, ez kellett
            set regenerate                      = 1                                 //  Nem tud<PERSON>, de legyen 1


        print Sending setLoadInitProperties request ...

        post customers/flex/reportKOIOvertime/getGridMultiHeaders?gridID=dhtmlxGrid
        post customers/flex/reportKOIOvertime/setInitProperties?gridID=dhtmlxGrid              
        post customers/flex/reportKOIOvertime/setPostInitProperties?gridID=dhtmlxGrid          
        post customers/flex/reportKOIOvertime/setLoadInitProperties?gridID=dhtmlxGrid
        post customers/flex/reportKOIOvertime/getColumnProperties?gridID=dhtmlxGrid

        print Sending gridData request ...
            
        post customers/flex/reportKOIOvertime/gridData?gridID=dhtmlxGrid

            this is reportKOIOvertime.jhtml
            convert reportKOIOvertime.jhtml to json using report grid decoder
            convert reportKOIOvertime.json to csv
            
:quicktest

            client flex                                                     // ha esetleg itt ébredtünk, -p opció okán
            check reportKOIOvertime.csv

        logout
        stop


        print