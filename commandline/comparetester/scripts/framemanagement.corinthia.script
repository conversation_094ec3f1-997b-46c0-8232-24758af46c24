

:Start

    client corinthia                                                        //  ez beállítja a clienturl-t is
    clienturl https://corinthia.stage-ttwa.login.hu/                        //  de mi óverr<PERSON>j<PERSON>lju<PERSON>

    show login as root, Li@BUG4everH4ppy4pr1!                               //  ki<PERSON><PERSON><PERSON> a j<PERSON> (ha nem akarod, hogy l<PERSON>, ak<PERSON> "show" nélkül ugyanez)

    set searchInput[valid_date]         = 2018-05-01                        //  változók a következő requesthez
    set searchInput[company]            = ALL
    set searchInput[payroll]            = ALL
    set searchInput[workgroup]          = ALL
    set searchInput[unit]               = ALL
    set searchInput[employee_contract]  = ALL
    set gridParams[objboxHeight]        = 705                               //  Ebből tudja, hány sort hozzon le
    set forceLoad                       = 1                                 //  Úgy tűnik, ez kellett
    set regenerate                      = 1                                 //  Nem tudjuk, de legyen 1

    set dhtmlxGridHeadFilters[fullname] =
    set dhtmlxGridHeadFilters[emp_id] =
    set dhtmlxGridHeadFilters[unit_name] =
    set dhtmlxGridHeadFilters[frame_from] =
    set dhtmlxGridHeadFilters[frame_to] =
    set dhtmlxGridHeadFilters[legal_restday] =
    set dhtmlxGridHeadFilters[used_restday] =
    set dhtmlxGridHeadFilters[frame_daily_worktime_5_2] =
    set dhtmlxGridHeadFilters[schedule_sum] =
    set dhtmlxGridHeadFilters[worktime_hours] =
    set dhtmlxGridHeadFilters[paid_absences] =
    set dhtmlxGridHeadFilters[unpaid_absences] =
    set dhtmlxGridHeadFilters[frame_balance] =
    set dhtmlxGridHeadFilters[paid_ot] =
    set dhtmlxGridHeadFilters[paid_ot_state] =
    set dhtmlxGridHeadFilters[unpaid_ot] =
    set dhtmlxGridHeadFilters[annual_ot_balance_orig] =
    set dhtmlxGridHeadFilters[ot_value] =
    set dhtmlxGridHeadFilters[annual_ot_balance] =


    post wfm/frameManagement/setInitProperties?gridID=mainGrid              //  Biztos van, amit sessionből tud majd meg; tegyük oda neki
    post wfm/frameManagement/setPostInitProperties?gridID=mainGrid          //  Ugyanez a sztori, nem tudom, melyik kell és melyik nem
    print

    for $id in ALL

        print --- Getting data for ECID=$id ------------------------------------------------------------------
        set searchInput[employee_contract] = $id
        post wfm/frameManagement/gridData?gridID=mainGrid
        this is framemanagement.$id.jhtml
        convert framemanagement.$id.jhtml to json
        convert framemanagement.$id.jhtml to csv
        convert framemanagement.$id.jhtml to txt
        check framemanagement.$id.txt
        print

    end

:Logout

    logout
    stop


:quicktest

    client bos
    for $id in 21, 40, 77

        print --- Getting data for ECID=$id ------------------------------------------------------------------
        set searchInput[employee_contract] = $id
        convert framemanagement.$id.jhtml to json
        convert framemanagement.$id.jhtml to csv
        convert framemanagement.$id.jhtml to txt
        check framemanagement.$id.txt
        print

    end
