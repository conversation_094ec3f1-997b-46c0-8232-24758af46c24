
client flex
clienturl https://flex-treebeard-ease82.login.hu

		for $month in 01

			// login as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>laton1
			login as root, H4kuN4M4t4_T4!

			//set searchInput[valid_from]: @date(2021,$month,1)
			//set searchInput[valid_to]: @date(2021,$month,99)
			//set searchInput_valid_month: @date(2023,$month,1)
			set searchInput[valid_month]: 2023-01
			set searchInput[company]: PCBA Zala, Hungary (452)
			set searchInput[company_org_group1]: ALL
			//set searchInput[payroll]: ALL
			set searchInput[workgroup]: ALL
			set searchInput[unit]: ALL
			set searchInput_company_org_group2: ALL
			//set searchInput[employee_contract]: ALL
			set searchInput[quitEmployee]: no
			set searchInput[ptrMode]: ptr 	//month
			set mode: DEFAULT
			set locked: 0

			post /payrollTransfer/payroll?dencihoztalropit

			this is payroll-file-list.$month.json
			convert payroll-file-list.$month.json to csv using report fetcher ($month)

			sortCSV $month.NBIdoadat.csv
			check $month.NBIdoadat.csv.sorted
			sortCSV $month.NBMunkr.csv
			check $month.NBMunkr.csv.sorted
			sortCSV $month.NBPBeoszt.csv
			check $month.NBPBeoszt.csv.sorted
			sortCSV $month.NBMunkrOra.csv
			check $month.NBMunkrOra.csv.sorted
			sortCSV $month.NBIdoadatOra.csv
			check $month.NBIdoadatOra.csv.sorted
			sortCSV $month.NBKifiz.csv
			check $month.NBKifiz.csv.sorted
			

			logout
		end
