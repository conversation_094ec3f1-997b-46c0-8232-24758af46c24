
client kuehnenagel
clienturl https://kuehnenagel-treebeard-ease.login.hu

		for $month in 01,02,03,04,05,06,07,08,09

			login as root, H4kuN4M4t4_T4!

			set searchInput[valid_date]: @date(2023,$month,1)
			set searchInput[company]: ALL
			set searchInput[payroll]: ALL
			set searchInput[workgroup]: ALL
			set searchInput[unit]: ALL
			set searchInput[company_org_group1]: ALL
			// set searchInput[employee_contract]: ALL
			set searchInput[quitEmployee]: no
			set searchInput[ptrMode]: ptr 	//month
			set mode: DEFAULT
			set locked: 0

			post /payrollTransfer/payroll?dencihoztalropit

			this is payroll-file-list.$month.json
			convert payroll-file-list.$month.json to csv using report fetcher ($month)

			sortCSV $month.NBIdoadat.csv
			check $month.NBIdoadat.csv.sorted
			sortCSV $month.NBIdoadat_elozo.csv
			check $month.NBIdoadat_elozo.csv.sorted
			sortCSV $month.NBKifiz_Korr.csv
			check $month.NBKifiz_Korr.csv.sorted
			sortCSV $month.NBMunkr.csv
			check $month.NBMunkr.csv.sorted
			sortCSV $month.NBMunkr_elozo.csv
			check $month.NBMunkr_elozo.csv.sorted
			sortCSV $month.NBMunkrOra.csv
			check $month.NBMunkrOra.csv.sorted
			sortCSV $month.NBMunkrOra_elozo.csv
			check $month.NBMunkrOra_elozo.csv.sorted
			sortCSV $month.NBIdoadatOra.csv
			check $month.NBIdoadatOra.csv.sorted
			sortCSV $month.NBIdoadatOra_elozo.csv
			check $month.NBIdoadatOra_elozo.csv.sorted
			sortCSV $month.NBKifiz.csv
			check $month.NBKifiz.csv.sorted
			

			logout
		end
