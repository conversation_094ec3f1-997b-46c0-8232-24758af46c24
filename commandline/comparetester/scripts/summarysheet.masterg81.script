

:Start

    client mastergood                                                            //  ez beállítja a clienturl-t is
    clienturl https://masterg-treebeard-ease81.login.hu/                          //  de mi óverrájdoljuk

    //login as 8468470643, <PERSON><PERSON><PERSON><PERSON>
    login as root, H4kuN4M4t4_T4!                               //  ki<PERSON><PERSON><PERSON> a j<PERSON><PERSON> (ha nem akarod, hogy l<PERSON>, ak<PERSON> "show" nélkül ugy<PERSON>z)

    set searchInput[inside_type_id]     = ALL
    set searchInput[valid_from]         = 2023-01-01
    set searchInput[valid_to]           = 2023-01-31
    set searchInput[company]            = ALL
    set searchInput[payroll]            = ALL
    set searchInput[workgroup]          = ALL
    set searchInput[unit]               = ALL
    set searchInput[employee_contract]  = ALL
    set searchInput[useTimeCalculation] = 0
    set searchInput[statusfilter]       = ALL
    set gridParams[objboxHeight]        = 900000
    set forceLoad                       = 1                     
    set regenerate                      = 1                                 

    post wfm/employeeCalcReport/setInitProperties?gridID=dhtmlxGrid
    post wfm/employeeCalcReport/getGridMultiHeaders?gridID=dhtmlxGrid
    post wfm/employeeCalcReport/getColumnProperties?gridID=dhtmlxGrid
    post wfm/employeeCalcReport/setPostInitProperties?gridID=dhtmlxGrid
    post wfm/employeeCalcReport/setLoadInitProperties?gridID=dhtmlxGrid
    post wfm/employeeCalcReport/gridData?gridID=dhtmlxGrid


    print

    for $id in ad825e730af5f41b63c6694fbb88f3d5, 145, 155, 132, 4c7bda5edc9b281de24cf1d400a6cfb2, 2f16f7e60ea919b7c0932014481b6581, 7234d1fdf7b0f0544d4d29d5583954ac, 145, 155, 132, 144
        

    print --- Getting data for workgroup_id = $id ------------------------------------------------------------------
        set searchInput[workgroup] = $id 
        
    post wfm/employeeCalcReport/gridData?gridID=dhtmlxGrid
        this is osszesitolapReport.$id.jhtml
        convert osszesitolapReport.$id.jhtml to json using report grid decoder
        convert osszesitolapReport.$id.json to csv
        check osszesitolapReport.$id.csv
        print
    end

:Logout
    logout
    stop

