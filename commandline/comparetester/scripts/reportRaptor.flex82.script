
:Start

    client flex                                                            //  ez beállítja a clienturl-t is
    clienturl https://flex-treebeard-ease82.login.hu                         //  de mi óverrájdoljuk

    //for $company in 432

            login as root, H4kuN4M4t4_T4!

             set searchInput[dl_idl]: DL
             set searchInput[valid_from]: 2023-02-01
             set searchInput[valid_to]: 2023-02-28
             set searchInput[company]: ALL   //MFG Tab, HUN 475
             set searchInput[payroll]: ALL
             set searchInput[workgroup]: ALL
             set searchInput[unit]: ALL
             set searchInput[company_org_group3]: ALL
             set searchInput_auto[employee_contract]: ALL // ef7cbbfbab1fe4bb951a9249bbaa11c6
             //set searchInput[ptrMode]: ptr
             set mode: DEFAULT
             set locked: 0

            post /customers/flex/reportRaptor/setLoadInitProperties?gridID=dhtmlxGrid
            post /customers/flex/reportRaptor/gridData?gridID=dhtmlxGrid


            //this is Attendance_Sarvar_432.csv
            this is DL_2023.csv
            
            print
            
            //check Attendance_Sarvar_432.csv
            check DL_2023.csv
            

            logout
        end

