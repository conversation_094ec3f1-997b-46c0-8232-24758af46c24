
client mastergood
clienturl https://masterg-treebeard-ease82.login.hu

		for $month in 01,02,03,04,05,06

			// login as <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>laton1
			login as root, H4kuN4M4t4_T4!

			//set searchInput[valid_from]: @date(2021,$month,1)
			//set searchInput[valid_to]: @date(2022,$month,99)
			set searchInput[valid_date]: @date(2023,$month,1)
			set searchInput[company]: ALL
			set searchInput[payroll]: ALL
			set searchInput[workgroup]: ALL
			set searchInput[unit]: ALL
			// set searchInput[employee_contract]: ALL 
			set searchInput[ptrMode]: ptr 	//month
			set mode: DEFAULT
			set locked: 0

			post /payrollTransfer/payroll?dencihoztalropit

			this is payroll-file-list.$month.json
			convert payroll-file-list.$month.json to csv using report fetcher ($month)

			sortCSV $month.baber_MIEX_2023$month.csv
			check $month.baber_MIEX_2023$month.csv.sorted
			

			logout
		end
