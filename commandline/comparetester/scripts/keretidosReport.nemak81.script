:Start

    client nemak                                                           //  ez beállítja a clienturl-t is
    clienturl https://nemak-treebeard-ease81.login.hu/                       //  de mi óverr<PERSON>j<PERSON>ljuk

    login as teszt.admin, Balaton1                              //  kiír<PERSON> a j<PERSON>t (ha nem akarod, ho<PERSON>, ak<PERSON> "show" nélkül ugyanez)

            set searchInput[valid_date]         = 2020-12-31                        //  változók a következő requesthez
            set searchInput[company]            = ALL
            set searchInput[payroll]            = ALL
            set searchInput[workgroup]          = ALL
            set searchInput[unit]               = ALL
            set searchInput[employee_contract]  = ALL
            set searchInput[interval_type]      = date
            set gridParams[objboxHeight]        = 70000                             //  Ebből tudja, hány sort hozzon le
            set forceLoad                       = 1                                 //  Úgy tűnik, ez kellett
            set regenerate                      = 1                                 //  Nem tudjuk, de legyen 1

        post wfm/frameReportFromBalanceManagement/getGridMultiHeaders?gridID=dhtmlxGrid
        post wfm/frameReportFromBalanceManagement/setInitProperties?gridID=dhtmlxGrid           //  Biztos van, amit sessionből tud majd meg; tegyük oda neki
        post wfm/frameReportFromBalanceManagement/setPostInitProperties?gridID=dhtmlxGrid       //  Ugyanez a sztori, nem tudom, melyik kell és melyik nem
        post wfm/frameReportFromBalanceManagement/setLoadInitProperties?gridID=dhtmlxGrid
        post wfm/frameReportFromBalanceManagement/setColumnProperties?gridID=dhtmlxGrid

        print

        for $id in 000 

        print --- Getting data for workgroup_id = $id ------------------------------------------------------------------
            // set searchInput[workgroup] = $id
        post wfm/frameReportFromBalanceManagement/gridData?gridID=dhtmlxGrid
            this is keretidosReport_nemak.$id.jhtml
            convert keretidosReport_nemak.$id.jhtml to json using report grid decoder
            convert keretidosReport_nemak.$id.json to csv
            check keretidosReport_nemak.$id.csv
            print
        end

:Logout
    logout
    stop