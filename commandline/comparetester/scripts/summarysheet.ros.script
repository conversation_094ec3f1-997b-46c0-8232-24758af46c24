

:Start

    client rosenberger
    clienturl https://ros-treebeard-ease.login.hu/

    for $month in 01,02,03,04,05,06,07,08,09,10,11,12

        for $wid in 15, 2, 12, d9fe8bd40c5ae46c01c8c033be2e748f, 114554d11843ac4ed9888a56cbe02302, c1559393214e59c645d1db4915dfe737, 5954863d151d8dd9febb85823aca0d55, db6c771f3a008bc2ed0388680bec912d

            login as <PERSON><PERSON><PERSON><PERSON><PERSON>, Balaton1

            set searchInput[valid_from]:        @date(2022,$month,1)
            set searchInput[valid_to]:          @date(2022,$month,99)
            set searchInput[company]            = ALL
            set searchInput[payroll]            = ALL
            set searchInput[workgroup]          = ALL
            set searchInput[unit]               = ALL
            set searchInput[company_org_group2] = ALL
            set searchInput[option1]            = ALL
            set searchInput[option2]            = ALL
            set searchInput[option3]            = ALL
            set searchInput[option7]            = ALL
            set searchInput[employee_contract]  = ALL
            set gridParams[objboxHeight]        = 1000000
            set forceLoad                       = 1
            set regenerate                      = 1


            post wfm/summarySheet/setInitProperties?gridID=mainGrid
            post wfm/summarySheet/setPostInitProperties?gridID=mainGrid
            post wfm/summarySheet/getGridMultiHeaders?gridID=mainGrid
            post wfm/summarySheet/getColumnProperties?gridID=mainGrid
            post wfm/summarySheet/setLoadInitProperties?gridID=mainGrid

        print

        print --- Getting data for workgroup_id = $wid, month = $month --------------------------------------------

            set searchInput[workgroup] = $wid

            post wfm/summarySheet/gridData?gridID=mainGrid

            this is summarysheet_ros.$wid.2022.$month.jhtml
            convert summarysheet_ros.$wid.2022.$month.jhtml to json using daily grid decoder
            convert summarysheet_ros.$wid.2022.$month.json to csv
            check summarysheet_ros.$wid.2022.$month.csv
        print
        end
    end

:Logout

    logout
    stop
