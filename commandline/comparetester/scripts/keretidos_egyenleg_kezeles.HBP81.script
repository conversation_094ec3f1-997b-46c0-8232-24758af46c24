:Start

    client HBP                                                          //  ez beállítja a clienturl-t is
    clienturl https://kedrion-treebeard-ease81.login.hu/                       //  de mi óverrájdoljuk

    login as 17430, Balaton1                                             //  ki<PERSON><PERSON><PERSON> a j<PERSON><PERSON><PERSON> (ha nem akarod, ho<PERSON>, ak<PERSON> "show" nélk<PERSON>l ugyanez)

            set searchInput[valid_date]         = 2023-12-17                        //  változók a következő requesthez
            set searchInput[company]            = ALL
            set searchInput[payroll]            = ALL
            set searchInput[workgroup]          = ALL
            set searchInput[unit]               = ALL
            set searchInput[employee_contract]  = ALL
            //set searchInput[interval_type]      = date
            set gridParams[objboxHeight]        = 70000                             //  Ebből tudja, hány sort hozzon le
            set forceLoad                       = 1                                 //  Úgy tűnik, ez kellett
            set regenerate                      = 1                                 //  Nem tudjuk, de legyen 1

        post wfm/frameManagement/getGridMultiHeaders?gridID=dhtmlxGrid
        post wfm/frameManagement/setInitProperties?gridID=dhtmlxGrid              //  Biztos van, amit sessionből tud majd meg; tegyük oda neki
        post wfm/frameManagement/setPostInitProperties?gridID=dhtmlxGrid          //  Ugyanez a sztori, nem tudom, melyik kell és melyik nem
        post wfm/frameManagement/setLoadInitProperties?gridID=dhtmlxGrid
        post wfm/frameManagement/setColumnProperties?gridID=dhtmlxGrid

        print

        for $id in 000 

        print --- Getting data for workgroup_id = $id ------------------------------------------------------------------
            // set searchInput[workgroup] = $id
        post wfm/frameManagement/gridData?gridID=dhtmlxGrid
            this is keretidos_egyenleg_kezeles_HBP.$id.jhtml
            convert keretidos_egyenleg_kezeles_HBP.$id.jhtml to json using report grid decoder
            convert keretidos_egyenleg_kezeles_HBP.$id.json to csv
            sortCSV keretidos_egyenleg_kezeles_HBP.$id.csv
            check keretidos_egyenleg_kezeles_HBP.$id.csv.sorted
            print
        end

:Logout
    logout
    stop