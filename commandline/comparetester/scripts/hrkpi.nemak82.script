

:Start

    client nemak                                                            //  ez beállítja a clienturl-t is
    clienturl https://nemak-treebeard-ease82.login.hu/                          //  de mi óverrájdoljuk

    // login as teszt.admin, <PERSON><PERSON>on1    
    login as root, H4kuN4M4t4_T4!                                   //  ki<PERSON><PERSON><PERSON> a j<PERSON> (ha nem akarod, hogy l<PERSON>, ak<PERSON> "show" nélkül ugyanez)

    set searchInput[valid_from]: 2023-07-01
    set searchInput[valid_to]: 2023-12-31
    set searchInput[company]: ALL
    set searchInput[payroll]: ALL
    set searchInput[unit]: ALL
    set searchInput[company_org_group3]: ALL
    set searchInput[employee_contract]: ALL
    // set searchInput[csvstatusfilter]: 2
    set gridParams[objboxHeight]: 700000

    //print Sending setLoadInitProperties request ...
    post /customers/nemak/hrKpiReportYear/setLoadInitProperties?gridID=dhtmlxGrid

    //set forceLoad: 1
    //set currentPage: 0
    //set regenerate: 1

    //print Sending gridData request ...
    post /customers/nemak/hrKpiReportYear/gridData?gridID=dhtmlxGrid

    copy report as hrKpiReportYear.csv
    check hrKpiReportYear.csv
    
    print

:Logout

    logout
    stop

