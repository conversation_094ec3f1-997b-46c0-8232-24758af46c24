
:Start

    client flex                                                            //  ez beállítja a clienturl-t is
    clienturl https://flex-treebeard-ease81.login.hu/                          //  de mi óver<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

    
            login as root, H4kuN4M4t4_T4!

             set searchInput[valid_year]: 2023
             set searchInput[valid_year2]: 2023
             set searchInput[valid_to]: 2023-02-28
             set searchInput[status]: ALL
             set searchInput[mep]: ALL
             set searchInput[location]: ALL
             set searchInput[unit_id]: ALL
             set searchInput[cost_center_id]: ALL
             set searchInput[company_org_group2_id]: ALL
             set gridParams[objboxHeight]: 716000
             
            print Sending setLoadInitProperties request ...
            post /customers/flex/annualLeaveListPTO2/setLoadInitProperties?gridID=dhtmlxGrid

            print Sending gridData request ...
            post /customers/flex/annualLeaveListPTO2/gridData?gridID=dhtmlxGrid

            this is annualLeaveListPTO2.jhtml
            convert annualLeaveListPTO2.jhtml to json using report grid decoder
            convert annualLeaveListPTO2.json to csv
            

:quicktest

            client flex // ha esetleg itt ébredtünk, -p opció okán
            check annualLeaveListPTO2.csv
            
        logout
        stop


        print
            