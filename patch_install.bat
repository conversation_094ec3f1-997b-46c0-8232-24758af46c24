@echo off
setlocal enableextensions enabledelayedexpansion

set argCount=0

for %%x in (%*) do (
	set /A argCount+=1
)

if %argCount% == 1 (
	IF exist %1 (
		set patchFileName=%1

		set needed_rev=""
		set uppgr_rev=""

		for /F "tokens=1-10 delims=_" %%a in ("!patchFileName!") do (
			set needed_rev=%%g
			set uppgr_rev=%%i
		)

		set uppgr_rev=!uppgr_rev:~0,8!

		set current_rev=""

		set /A lineCount=0

		for /F "tokens=*" %%a in (tmp\.version) do (
			if !lineCount! == 1 (
				set current_rev=%%a
			)

			set /A lineCount+=1
		)

		set current_rev=!current_rev:~0,8!

		if !current_rev! == !needed_rev! (
			del PATCH_*.list /s

			7z.exe x !patchFileName! -o%cd% -aoa

			set patchFileName=!patchFileName:~0,-4!

			for /F "tokens=1-2" %%a in (!patchFileName!.list) do (
				set toDel=%%b
				set toDel=!toDel:/=\!

				if "%%a" == "D" (
					echo To delete: !toDel!

					del !toDel! /s
				) else (
					echo Modified: !toDel!
				)
			)

			echo Upgrade FROM !current_rev! TO !uppgr_rev! completed!
		) else (
			echo Current revision shuld be !needed_rev! instead of !current_rev! ...
		)
	)
) else (
	echo Missing parameter / wrong parameter count ...
)

pause
