-- # RELEASE 1.0

CREATE TABLE IF NOT EXISTS `_sql_version` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `module` varchar(128) COLLATE utf8_unicode_ci NOT NULL,
  `major_minor` char(8) COLLATE utf8_unicode_ci DEFAULT NULL,
  `revision` int(11) NOT NULL,
  `upgrade_block` tinyint(1) NOT NULL DEFAULT '0',
  `updated_on` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci AUTO_INCREMENT=0 ;

INSERT INTO `_sql_version` (`id`, `module`, `major_minor`, `revision`, `updated_on`) VALUES ('1', 'ttwa-jasper', '1.0', '0', '2015-05-19 12:00:00') ON DUPLICATE KEY UPDATE `module`=VALUES(`module`), `major_minor`=VALUES(`major_minor`), `revision`=VALUES(`revision`);

UPDATE `_sql_version` SET `revision`=1, `updated_on`=NOW() WHERE `module` = 'ttwa-jasper';

-- VERSION --------------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `report` (
  `row_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'Azonosító',
  `report_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'Report azonosító',
  `file_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `file_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `database` varchar(64) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `jrxml` longtext CHARACTER SET utf8 COLLATE utf8_unicode_ci,
  `report_parameters` varchar(1024) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `report_path` varchar(256) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `report_created` datetime NOT NULL,
  `report_started` datetime DEFAULT NULL,
  `report_finished` datetime DEFAULT NULL,
  `status` enum('EMPTY','WAIT','SCHEDULED','IN_PROGRESS','DONE','FAILED') DEFAULT 'EMPTY',
  PRIMARY KEY (`row_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COMMENT='Report storage' AUTO_INCREMENT=0 ;

UPDATE `_sql_version` SET `revision`=2, `updated_on`=NOW() WHERE `module` = 'ttwa-jasper';

-- VERSION --------------------------------------------------------------------

ALTER TABLE  `report` ADD  `report_log` VARCHAR( 512 ) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL AFTER  `status` ;
ALTER TABLE  `report` CHANGE  `status`  `report_status` ENUM(  'EMPTY',  'WAIT',  'SCHEDULED',  'IN_PROGRESS',  'DONE',  'FAILED' ) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT  'EMPTY';

UPDATE `_sql_version` SET `revision`=3, `updated_on`=NOW() WHERE `module` = 'ttwa-jasper';

-- VERSION --------------------------------------------------------------------

ALTER TABLE  `report` ADD  `on_before_query_sql` VARCHAR( 512 ) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL AFTER  `jrxml` ;

UPDATE `_sql_version` SET `revision`=4, `updated_on`=NOW() WHERE `module` = 'ttwa-jasper';

-- VERSION --------------------------------------------------------------------

ALTER TABLE `report`
  ADD UNIQUE KEY `IDX_report_id` (`report_id`);

UPDATE `_sql_version` SET `revision`=5, `updated_on`=NOW() WHERE `module` = 'ttwa-jasper';

-- VERSION --------------------------------------------------------------------

ALTER TABLE `report` ADD `report_type` ENUM('JASPER','NODE') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT 'JASPER' AFTER `report_id`;

ALTER TABLE `report` ADD `host` VARCHAR(128) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL COMMENT 'TTWA host' AFTER `database`;

ALTER TABLE `report` ADD `orientation` ENUM('portrait', 'landscape') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT 'portrait' AFTER `report_type`;

ALTER TABLE `report` ADD `fs_file_id` VARCHAR(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL AFTER `host`;

UPDATE `_sql_version` SET `revision`=6, `updated_on`=NOW() WHERE `module` = 'ttwa-jasper';

-- VERSION --------------------------------------------------------------------