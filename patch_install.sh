#!/bin/sh

if [ "$#" -eq 1 ];
then
	if [ -f "$1" ];
	then
		needed_branch=$(echo "$1" | tr '_' '\n' | head -3 | tail -1);
		needed_rev=$(echo "$1" | tr '_' '\n' | head -7 | tail -1);

		current_branch=$(cat "tmp/.version" | head -4 | tail -1);
		current_rev=$(cat "tmp/.version" | head -2 | tail -1 | cut -c 1-8);

		check_rev=$(cat "tmp/.version" | head -2 | tail -1 | grep "$needed_rev");

		if [ "$check_rev" != "" -a "$needed_branch" = "$current_branch" ];
		then
			rm -rf "PATCH_"*".list"

			file="$1";

			tar=$(echo $file | grep ".tar" | grep -v ".tar.gz");

			if [ "$tar" != "" ]; # if TAR
			then
				tar -xvf "$file"
			else
				unzip -o "$file"
			fi

			files=$(cat "PATCH_"*".list" | grep "D$(printf '\t')" | cut -f 2); # grep deleted files/folders --LINUX, NOT MAC:  -P "D\t"
			
			for file in $files
			do
				rm -rf "$file"
			done

			new_rev=$(cat "tmp/.version" | head -2 | tail -1 | cut -c 1-8);

			echo "Upgrade FROM $current_rev TO $new_rev completed!";
		else
			echo "Current revision shuld be $needed_rev instead of $current_rev ... ($needed_branch/$current_branch)";
		fi
	else
		echo "Missing patch file ...";
	fi
else
	echo "Missing parameter / wrong parameter count ...";
fi
