version: "2"
exclude_patterns:
  - "**/*.js"
  - "**/*.jsx"
  - "**/*.d.ts"
  - "**/node_modules/"
  - "**/spec/"
  - "**/test/"
  - "**/tests/"
  - "**/vendor/"
  - "protected/extensions/**/*"
  - "webroot/ext/"
  - "webroot/images/**/*"
  - "webroot/js/**/*"

plugins:
  phan:
    enabled: false
    channel: "beta"
    config:
      file_extensions: "php"
      dead-code-detection: false # TODO: set back to true
      backward-compatibility-checks: false # TODO: set back to true
      minimum-severity: 5 # TODO: set back to 0 after some time
      ignore-undeclared: true
      quick: false # somewhy its failing
  phpcodesniffer:
    enabled: false
    channel: "beta"
    config:
      standard: "PSR1,PSR12"
  phpmd:
    enabled: true
  sonar-php:
    enabled: true
