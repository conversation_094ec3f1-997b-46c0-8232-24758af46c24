#!/bin/sh

if [ -d ".git" ]; then
	br=$(git rev-parse --abbrev-ref HEAD)

	git rev-list $br --count > tmp/.version
	git rev-parse $br >> tmp/.version
	git log -1 --format="%ci" $br >> tmp/.version
	git rev-parse --abbrev-ref $br >> tmp/.version
	git describe --tags $br >> tmp/.version
	# two lines for tag changelogs
	#git for-each-ref --format="%(refname:short)###%(contents)" --sort=-taggerdate refs/tags >> tmp/.version
fi
