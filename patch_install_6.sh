#!/bin/sh

if [ "$#" -eq 1 ];
then
	if [ -f "$1" ];
	then
		needed_rev=$(echo "$1" | tr '_' '\n' | head -7 | tail -1);

		current_rev=$(cat "tmp/.version" | head -2 | tail -1 | cut -c 1-6);

		check_rev=$(cat "tmp/.version" | head -2 | tail -1 | grep "$needed_rev");

		if [ "$check_rev" != "" ];
		then
			rm -rf "PATCH_"*".list"
			unzip -o "$1"
			files=$(cat "PATCH_"*".list" | grep -P "D\t" | cut -f 2); # grep deleted files/folders
			for file in $files
			do
				rm -rf "$file"
			done

			new_rev=$(cat "tmp/.version" | head -2 | tail -1 | cut -c 1-6);

			echo "Upgrade FROM $current_rev TO $new_rev completed!";
		else
			echo "Current revision shuld be $needed_rev instead of $current_rev ...";
		fi
	else
		echo "Missing patch file ...";
	fi
else
	echo "Missing parameter / wrong parameter count ...";
fi
