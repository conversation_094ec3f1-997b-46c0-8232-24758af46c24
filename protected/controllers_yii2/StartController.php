<?php

namespace app\controllers_yii2;

use Yii;
use yii\web\Controller;
use yii\web\Response;

class StartController extends Controller
{
	public function actionIndex() {
		//return "Yii2 - ".Yii::getVersion()."";

		//$this->view->theme->pathMap =['@app/views' => '@app/themes/myTheme/',]; 

		$this->layout = '@app/views_yii2/layouts/page';

		return $this->render('@app/views_yii2/start/index', [

        ]);
	}
}