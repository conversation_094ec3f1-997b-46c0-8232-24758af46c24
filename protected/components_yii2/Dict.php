<?php

use Yii;
use yii\helpers\Json;
// Json::encode([])

use app\models_yii2\Dictionary;

class Dict
{
	public static function getLang() {
		App::yii2NotImpl(__METHOD__);

		if (isset($_SESSION["tiptime"]["dict"]["short_lang"])) {
			return $_SESSION["tiptime"]["dict"]["short_lang"];
		}

		return "en";
	}
	
	public static function getModuleValue($module, $dict_id, $params = array()) {
		return self::getValue($dict_id, $params, $module);
	}

	public static function getValue($dict_id, $params = array(), $module = null) {
		if (session_status() == PHP_SESSION_NONE) {
			session_start();
		}

		if (!isset($_SESSION["tiptime"]["dict"][$dict_id])) {
			$lang = self::getLang();

			$found_val = null;

			if (empty($module)) { // ROLLBACK
				$dict = Dictionary::find()
									->where('`valid` = 1 AND `lang` = "'.$lang.'" AND `dict_id` = "'.$dict_id.'" AND `module` != "ttwa-base"')
									->one();

				if ($dict) {
					$found_val = $dict->dict_value;
				} else {
					$dict = Dictionary::find()
										->where('`valid` = 1 AND `lang` = "'.$lang.'" AND `dict_id` = "'.$dict_id.'" AND `module` = "ttwa-base"')
										->one();

					if ($dict) {
						$found_val = $dict->dict_value;
					}
				}
			} else {
				$d = Dictionary::find();
				$d->where('`valid` = 1 AND `lang` = "'.$lang.'" AND `dict_id` = "'.$dict_id.'" AND `module` = "'.$module.'"');
				$dict = $d->one();

				if ($dict) {
					$found_val = $dict->dict_value;
				}
			}

			$_SESSION["tiptime"]["dict"][$dict_id] = $found_val;
		}

		$val = $_SESSION["tiptime"]["dict"][$dict_id];

		if (!empty($val) && count($params)) {
			foreach ($params as $name => $value) {
				$val = str_replace("{".$name."}",$value,$val);
			}
		}

		return !empty($val)?$val:"";
	}
}