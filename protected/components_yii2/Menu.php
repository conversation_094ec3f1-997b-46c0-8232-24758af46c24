<?php

use Yii;
use yii\helpers\Json;

class Menu
{
	public static function get() {
		$modules = Yii::$app->params['modules'];
		$moduleToStr = implode(" ", $modules) . "<br/>";

		$main_no = 1;
		$menu_visible = false;
		if (!App::isGuest()) {
			$main_no = 4;
			$menu_visible = true;
		}

		if (strpos($moduleToStr, '-core') !== false) {
			$searchword = '-core';
			$moduleIfContainsCore = array_filter($modules, function($var) use ($searchword) {
				return preg_match("/\b$searchword\b/i", $var);
			});

			$core = [];

			foreach ($moduleIfContainsCore as $module) {
				$modulesNameWithoutCore[] = str_replace("-core", "", $module);
			}

			$result = array_diff($modulesNameWithoutCore, $modules);
			$modulesNameWithoutCore = array_diff($modulesNameWithoutCore, $result);

			$moduleCorrected = array_filter($modules, function($var) use ($modulesNameWithoutCore) {
				$ret = true;

				foreach ($modulesNameWithoutCore as $value) {
					$value = $value . "-core";

					$ret = $ret && !preg_match("/\b$value\b/i", $var);
				}

				return $ret;
			});

			if (App::getSetting("menu_order") == 0) {
				$allMenuSQL = "SELECT * FROM menu_item_table
					WHERE menu_item_table.`menu_modul` IN ('ttwa-base','" . implode("','", $moduleCorrected) . "')
					ORDER BY menu_item_table.`menu_order`";
			} else {
				$allMenuSQLMain = "SELECT * FROM menu_item_table
					LEFT JOIN `dictionary` ON
					dictionary.`dict_id` = menu_item_table.`menu_label` AND
					dictionary.`module` = menu_item_table.`menu_modul` AND
					dictionary.`lang` = '" . Dict::getLang() . "'
					WHERE menu_item_table.`menu_item_parent_id` IS NULL AND menu_item_table.`menu_modul` IN ('ttwa-base','" . implode("','", $moduleCorrected) . "')
					ORDER BY menu_item_table.`menu_order`";

				$allMenuSQLSub = "SELECT * FROM menu_item_table
					LEFT JOIN `dictionary` ON
					dictionary.`dict_id` = menu_item_table.`menu_label` AND
					dictionary.`module` = menu_item_table.`menu_modul` AND
					dictionary.`lang` = '" . Dict::getLang() . "'
					WHERE menu_item_table.`menu_item_parent_id` IS NOT NULL AND menu_item_table.`menu_modul` IN ('ttwa-base','" . implode("','", $moduleCorrected) . "')
					ORDER BY dictionary.`dict_value`";
			}
		} else {
			if (App::getSetting("menu_order") == 0) {
				$allMenuSQL = "SELECT * FROM menu_item_table
					WHERE menu_item_table.`menu_modul` IN ('ttwa-base','" . implode("','", $modules) . "')
					ORDER BY menu_item_table.`menu_order`";
			} else {
				$allMenuSQLMain = "SELECT * FROM menu_item_table
					LEFT JOIN `dictionary` ON
					dictionary.`dict_id` = menu_item_table.`menu_label` AND
					dictionary.`module` = menu_item_table.`menu_modul` AND
					dictionary.`lang` = '" . Dict::getLang() . "'
					WHERE menu_item_table.`menu_item_parent_id` IS NULL AND menu_item_table.`menu_modul` IN ('ttwa-base','" . implode("','", $modules) . "')
					ORDER BY menu_item_table.`menu_order`";

				$allMenuSQLSub ="SELECT * FROM menu_item_table
					LEFT JOIN `dictionary` ON
					dictionary.`dict_id` = menu_item_table.`menu_label` AND
					dictionary.`module` = menu_item_table.`menu_modul` AND
					dictionary.`lang` = '" . Dict::getLang() . "'
					WHERE menu_item_table.`menu_item_parent_id` IS NOT NULL AND menu_item_table.`menu_modul` IN ('ttwa-base','" . implode("','", $modules) . "')
					ORDER BY dictionary.`dict_value`";
			}
		}

		$conn = Yii::$app->getDb();
		if (isset($allMenuSQL)) {
			$menuRES = $conn->createCommand($allMenuSQL)->queryAll();
		} else {
			$menuRESMain = $conn->createCommand($allMenuSQLMain)->queryAll();
			$menuRESSub = $conn->createCommand($allMenuSQLSub)->queryAll();
	
			$menuRES = $menuRESMain;
			foreach ($menuRESSub as $item) {
				array_push($menuRES, $item);
			}
		}

		$tree = self::menuTree($menuRES);

		$menu_arr = array(
			'encodeLabel' => false,
			'items' => $tree,
		);

		if ($menu_visible) {
			return self::generateHtml($menu_arr);
		}

		return null;
	}

	public static function menuTree(array $allMenu, $parentId = null) {
		$guest = App::isGuest();
	
		$branch = [];
	
		foreach ($allMenu as $element) {
			if ($element['menu_item_parent_id'] == $parentId) {
				$element["label"] = Dict::getModuleValue(empty($element["menu_modul"]) ?
									 "ttwa-base" : $element["menu_modul"], $element["menu_label"]);
				$element["label"] = empty($element["label"])?$element["menu_label"]:$element["label"];
				$element["itemOptions"] = ["class" => $element["menu_item_css_class"]];
				$element["url"] = $element["menu_url"] === "#" ?
									$element["menu_url"] : [$element["menu_url"]];
				$element["visible"] = (!$guest && (App::getRight($element["menu_visible"],
									$element["menu_visible_operation"]) || empty($element["menu_visible"])));
	
				$children = self::menuTree($allMenu, $element['menu_item_id']);
	
							if ($element["menu_url"] === "#" && !isset($element['items'])) {
								$element['items'] = [];
							}
	
				if ($children) {
					if (!isset($element['items'])) {
										$element['items'] = [];
					}
					$element['items'] = $children;
				}
	
				$branch[$element['menu_item_name']] = $element;
			}
		}
	
		return $branch;
	}

	public static function generateHtml($menu_arr) {
		$mm_count = 0;

		foreach ($menu_arr['items'] as $index => $mainmenu) {
			$menu_arr['items'][$index]['label'] = '<div class="item"><div class="text">' . $mainmenu['label'] . '</div></div>';

			if (isset($mainmenu['items'])) {
				$lv2_count = 0;
				$lv2_visible = 0;

				foreach ($mainmenu['items'] as $index2 => $level2) {
					if (isset($level2['visible']) && $level2['visible']) {
						$lv2_visible++;
					}

					$menu_arr['items'][$index]['items'][$index2]['label'] = '<div class="item"><div class="text">' . $level2['label'] . '</div></div>';

					if (isset($level2['items'])) {
						$lv3_count = 0;
						$lv3_visible = 0;

						foreach ($level2['items'] as $index3 => $level3) {
							if (isset($level3['visible']) && $level3['visible']) {
								$lv3_visible++;
							}

							$menu_arr['items'][$index]['items'][$index2]['items'][$index3]['label'] = '<div class="item"><div class="text">' . $level3['label'] . '</div></div>';

							$lv3_count++;
						}

						if ($lv3_count && !$lv3_visible) {
							unset($menu_arr['items'][$index]['items'][$index2]);
						}
					}

					$lv2_count++;
				}

				if (/*$lv2_count && */!$lv2_visible) {
					unset($menu_arr['items'][$index]);
				}
			}

			$mm_count++;
		}

		$html = '';

		$html .= '
			<div id="gn-menu-div" style="top:0px;left:0px;">
				<ul id="gn-menu" class="gn-menu-main" style="z-index:2450;">
					<li class="gn-trigger">
						<a class="gn-icon gn-icon-menu"></a>
						<nav class="gn-menu-wrapper">
							<div id="gn-menu-sc">
								<div class="gn-scroller">
									<ul id="yw0">
		';
										foreach ($menu_arr['items'] as $mainIndex => $mainItem) {
											if (!$mainItem['visible']) continue;

											$mainClass = $mainItem['menu_item_css_class'];

											$hasSub = false;
											if (isset($mainItem['items']) && is_array($mainItem['items']) && count($mainItem['items'])) {
												$hasSub = true;

												$mainClass .= ' plus';
											}

											$html .= '<li class="'.$mainClass.'">';
												$html .= '<a href="'.$mainItem['menu_url'].'">'.$mainItem['label'].'</a>';
												if ($hasSub) {
													$html .= '<ul style="display: none;">';

													foreach ($mainItem['items'] as $subIndex => $subItem) {
														if (!$subItem['visible']) continue;

														$html .= '<li class="sub">';
															$html .= '<a href="'.$subItem['menu_url'].'">'.$subItem['label'].'</a>';
														$html .= '</li>';
													}

													$html .= '</ul>';
												}
											$html .= '</li>';
										}
		$html .= '
									</ul>
								</div>
							</div>
								
						</nav>
					</li>
				</ul>
			</div>
		';

		$html .= '
			<script type="text/javascript"> mainMenu(); </script>
		';

		return $html;
	}
}