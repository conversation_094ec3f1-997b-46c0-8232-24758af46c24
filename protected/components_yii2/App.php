<?php

use Yii;
use yii\helpers\Json;

// Yii::warning(Json::encode([]));
// Yii::$app->params['adminEmail'];

use app\models_yii2\User;

class App
{
	public static function getUserId() {
		App::yii2NotImpl(__METHOD__);

		$user_id = null;
		if (isset($_SESSION["tiptime"]['user']['user_id'])) {
			$user_id = $_SESSION["tiptime"]['user']['user_id'];
		}

		if (!empty($user_id))
			return $user_id;

		$username = null;
		if (isset($_SESSION["tiptime"]['user']['username'])) {
			$username = $_SESSION["tiptime"]['user']['username'];
		}

		if (empty($username))
			return null;

		$user = User::find()
					->where('`username` = "'.$username.'" AND `status` = 2
						AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, "'.App::defaultEnd().'")')
					->one();

		if (!$user)
			return null;

		return $_SESSION["tiptime"]['user']['user_id'] = $user->user_id;
	}

	public static function isGuest($user = null) {
		$user_id = !empty($user) ? $user : self::getUserId();

		return empty($user_id);
	}

	public function getRolegroup($user = null) {
		$user_id = !empty($user) ? $user : self::getUserId();

		if (!isset($_SESSION["tiptime"]["rolegroup"]) || !empty($user)) {
			$user = User::find()
						->where('`user_id` = "'.$user_id.'" AND `status` = 2
							AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, "'.App::defaultEnd().'")')
						->one();

			if ($user) {
				return $_SESSION["tiptime"]["rolegroup"] = $user->rolegroup_id;
			} else {
				return $_SESSION["tiptime"]["rolegroup"] = null;
			}
		} else {
			return $_SESSION["tiptime"]["rolegroup"];
		}
	}

	public static function hasRight($controller_id, $operation, $column = null) {
		return self::getRight($controller_id, $operation, $column);
	}

	public static function getRight($controller_id, $operation, $column = null, $user_id = null) {
		$rolegroup_id = self::getRolegroup($user_id);
		$guest = empty($user_id);

		$contr_id = empty($controller_id) ? "ALL" : $controller_id;

		if (empty($column)) {
			$usedSessionValue = isset($_SESSION["tiptime"]["rights"][$contr_id][$operation]) ?
									$_SESSION["tiptime"]["rights"][$contr_id][$operation] : "notSet";
		} else {
			$usedSessionValue = isset($_SESSION["tiptime"]["rights"][$contr_id][$operation]["column"][$column]) ?
									$_SESSION["tiptime"]["rights"][$contr_id][$operation]["column"][$column] : "notSet";
		}

		if ($usedSessionValue === "notSet") {
			if ( (!empty($rolegroup_id) || $guest) && !empty($operation)) {
				$SQL = "
					SELECT
						*
					FROM
						`auth_acl` aa
					LEFT JOIN
						`auth_role_in_group` arig ON
							aa.`role_id` = arig.`role_id`
					WHERE
						aa.`controller_id` = '$controller_id'
							AND aa.`operation_id` = '$operation'
				";

				if (!$guest) {
					$SQL .= " AND arig.`rolegroup_id` = '$rolegroup_id'";
				}

				if (empty($column)) {
					$SQL .= " AND aa.`column_name` IS NULL";
				} else {
					$SQL .= " AND aa.`column_name` = '$column'";
				}

				$conn = Yii::$app->getDb();
				$res_auth_acl = $conn->createCommand($SQL)->queryAll();

				// nem látogató a felhasználó VAGY látogató, de a művelethez nem szükséges bejelentkezni
				if (count($res_auth_acl) && ( !$guest || (!$res_auth_acl[0]["login_need"] && $guest) ) ) {
					if (empty($column)) {
						$_SESSION["tiptime"]["rights"][$controller_id][$operation] = $res_auth_acl[0]["access_right"];
					} else {
						$_SESSION["tiptime"]["rights"][$controller_id][$operation]["column"][$column] = $res_auth_acl[0]["access_right"];
					}

					return $res_auth_acl[0]["access_right"];
				} else {
					$original_controller_id = $contr_id;
					$controller_id = 'ALL';

					$SQL = "
						SELECT
							*
						FROM
							`auth_acl` aa
						LEFT JOIN
							`auth_role_in_group` arig ON
								aa.`role_id` = arig.`role_id`
						WHERE
							aa.`controller_id` = '$controller_id'
								AND aa.`operation_id` = '$operation'
					";

					if (!$guest) {
						$SQL .= " AND arig.`rolegroup_id` = '$rolegroup_id'";
					}

					if (empty($column)) {
						$SQL .= " AND aa.`column_name` IS NULL";
					} else {
						$SQL .= " AND aa.`column_name` = '$column'";
					}

					$conn = Yii::$app->getDb();
					$res_auth_acl = $conn->createCommand($SQL)->queryAll();

					// nem látogató a felhasználó VAGY látogató, de a művelethez nem szükséges bejelentkezni
					if (count($res_auth_acl) && ( !$guest || (!$res_auth_acl[0]["login_need"] && $guest) ) ) {
						if (empty($column)) {
							$_SESSION["tiptime"]["rights"][$original_controller_id][$operation] = $res_auth_acl[0]["access_right"];
						} else {
							$_SESSION["tiptime"]["rights"][$original_controller_id][$operation]["column"][$column] = $res_auth_acl[0]["access_right"];
						}

						return $res_auth_acl[0]["access_right"];
					} else if (!empty($column)) { // oszlop van megadva, de nem találtunk jogot hozzá
						$_SESSION["tiptime"]["rights"][$original_controller_id][$operation]["column"][$column] = true;

						return true;
					} else {
						$_SESSION["tiptime"]["rights"][$original_controller_id][$operation] = false;

						return false;
					}
				}
			}

			return null;
		} else {
			return $usedSessionValue;
		}
	}

	public static function getSetting($setting_id) {
		if (!isset($_SESSION["tiptime"]["settings"][$setting_id])) {
			$appSettings = AppSettings::find()
					->where('`setting_id` = "'.$setting_id.'" AND `valid` = 1')
					->one();

			$setting_value = null;
			if ($appSettings) {
				$setting_value = $appSettings->setting_type === "array" ? unserialize($appSettings->setting_value) : $appSettings->setting_value;
			}

			$_SESSION["tiptime"]["settings"][$setting_id] = $setting_value;
		}

		return $_SESSION["tiptime"]["settings"][$setting_id];
	}

	public static function defaultEnd() {
		return '2038-01-01';
	}

	public static function yii2NotImpl($method = __METHOD__) {
		Yii::warning($method." - Yii 1 => Yii 2: Not Fully Implemented");
	}
}