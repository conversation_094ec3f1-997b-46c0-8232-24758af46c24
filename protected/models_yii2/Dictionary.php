<?php

namespace app\models_yii2;

use Yii;

/**
 * This is the model class for table "dictionary".
 *
 * @property int $row_id
 * @property string $lang
 * @property string $module
 * @property string $dict_id
 * @property string $dict_value
 * @property int $valid Érvényes
 */
class Dictionary extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'dictionary';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['lang', 'dict_id', 'dict_value'], 'required'],
            [['lang'], 'string', 'max' => 8],
            [['module'], 'string', 'max' => 32],
            [['dict_id'], 'string', 'max' => 64],
            [['dict_value'], 'string', 'max' => 512],
            [['valid'], 'string', 'max' => 1],
            [['lang', 'module', 'dict_id', 'valid'], 'unique', 'targetAttribute' => ['lang', 'module', 'dict_id', 'valid']],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'row_id' => 'Row ID',
            'lang' => 'Lang',
            'module' => 'Module',
            'dict_id' => 'Dict ID',
            'dict_value' => 'Dict Value',
            'valid' => 'Valid',
        ];
    }
}
