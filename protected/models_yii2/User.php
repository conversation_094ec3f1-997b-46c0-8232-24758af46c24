<?php

namespace app\models_yii2;

use Yii;

/**
 * This is the model class for table "user".
 *
 * @property int $row_id
 * @property string $user_id
 * @property string $employee_id
 * @property string $username Felhasznál<PERSON><PERSON> név
 * @property string $password
 * @property string $password_salt
 * @property string $password_date
 * @property string $password_expired
 * @property int $disable_ldap_auth
 * @property string $last_logged_in
 * @property string $user_home
 * @property string $user_home_default_search
 * @property string $note Megjegyzés
 * @property string $email
 * @property int $receive_email Kap emailt
 * @property string $lang
 * @property string $rolegroup_id Szerepcsoport azonosító
 * @property string $managed_usergroup_id
 * @property string $managed_employee_group_id
 * @property string $valid_from
 * @property string $valid_to
 * @property int $status
 * @property string $created_by
 * @property string $created_on
 * @property string $modified_by Módosította
 * @property string $modified_on Módosítva
 * @property int $pre_row_id
 */
class User extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'user';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['user_id', 'username', 'lang', 'rolegroup_id', 'valid_from', 'status', 'created_by', 'created_on'], 'required'],
            [['password_date', 'password_expired', 'last_logged_in', 'valid_from', 'valid_to', 'created_on', 'modified_on'], 'safe'],
            [['pre_row_id'], 'integer'],
            [['user_id', 'employee_id', 'password_salt', 'rolegroup_id', 'managed_usergroup_id', 'managed_employee_group_id', 'created_by', 'modified_by'], 'string', 'max' => 32],
            [['username'], 'string', 'max' => 64],
            [['password', 'user_home'], 'string', 'max' => 128],
            [['disable_ldap_auth', 'receive_email', 'status'], 'string', 'max' => 1],
            [['user_home_default_search', 'note', 'email'], 'string', 'max' => 512],
            [['lang'], 'string', 'max' => 8],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'row_id' => 'Row ID',
            'user_id' => 'User ID',
            'employee_id' => 'Employee ID',
            'username' => 'Username',
            'password' => 'Password',
            'password_salt' => 'Password Salt',
            'password_date' => 'Password Date',
            'password_expired' => 'Password Expired',
            'disable_ldap_auth' => 'Disable Ldap Auth',
            'last_logged_in' => 'Last Logged In',
            'user_home' => 'User Home',
            'user_home_default_search' => 'User Home Default Search',
            'note' => 'Note',
            'email' => 'Email',
            'receive_email' => 'Receive Email',
            'lang' => 'Lang',
            'rolegroup_id' => 'Rolegroup ID',
            'managed_usergroup_id' => 'Managed Usergroup ID',
            'managed_employee_group_id' => 'Managed Employee Group ID',
            'valid_from' => 'Valid From',
            'valid_to' => 'Valid To',
            'status' => 'Status',
            'created_by' => 'Created By',
            'created_on' => 'Created On',
            'modified_by' => 'Modified By',
            'modified_on' => 'Modified On',
            'pre_row_id' => 'Pre Row ID',
        ];
    }
}
