<?php

namespace app\models_yii2;

use Yii;

/**
 * This is the model class for table "app_settings".
 *
 * @property int $row_id
 * @property string $setting_id
 * @property string $setting_value
 * @property string $setting_type
 * @property string $note
 * @property string $dict_id
 * @property int $valid Érvényes
 */
class AppSettings extends \yii\db\ActiveRecord
{
    /**
     * @inheritdoc
     */
    public static function tableName()
    {
        return 'app_settings';
    }

    /**
     * @inheritdoc
     */
    public function rules()
    {
        return [
            [['setting_id', 'setting_value'], 'required'],
            [['setting_type'], 'string'],
            [['setting_id', 'dict_id'], 'string', 'max' => 64],
            [['setting_value', 'note'], 'string', 'max' => 512],
            [['valid'], 'string', 'max' => 1],
            [['setting_id', 'valid'], 'unique', 'targetAttribute' => ['setting_id', 'valid']],
        ];
    }

    /**
     * @inheritdoc
     */
    public function attributeLabels()
    {
        return [
            'row_id' => 'Row ID',
            'setting_id' => 'Setting ID',
            'setting_value' => 'Setting Value',
            'setting_type' => 'Setting Type',
            'note' => 'Note',
            'dict_id' => 'Dict ID',
            'valid' => 'Valid',
        ];
    }
}
