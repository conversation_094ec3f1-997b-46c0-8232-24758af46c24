<?php

/**
 * Az EmployeeDataChanges controller a dolgozói adatok változásának megjelenítésére szolgáló kimutatás.
 * Adott intervallumban megjeleníti az összes adatváltozást (kivétel dinamikus tab!).
 * Megjeleníti az újonnan felvett dolgozókat, az intervallumon belüli történetiségeket és történetiség változásokat.
 * Egyebek: szűrő: normá<PERSON> ö<PERSON>zes<PERSON>, kezel felhasználó jogosultságot.
 *
 * Innote link: employee-data-changes
 */

class EmployeeDataChangesController extends Grid2Controller
{

	/**
	 * Dictionary felülírás változó oszlopra
	 */
	public $dict = [
		"base_absence_type_id"	=> "base_absence_type_id",
		"quantity_hour"			=> "hours",
		"created_on"			=> "new_historical_data",
		"fs_file_id"			=> "new_file_added",
		"work_activity_id"		=> "work_activity_id",
		"competency_id"			=> "competency_id",
		"employee_position_id"	=> "employee_position_name",
		"emp_id"				=> "emp_id"
	];

	/**
	 * Grid2s szülőosztály konstruktor meghívása
	 */
	public function __construct() {
        parent::__construct("employeeDataChanges");
		parent::enableLAGrid();
		parent::enableLAGridLight();
    }

	/**
	 * Grid2 inícializálása, használt mode: Array
	 */
	protected function G2BInit()
    {
		parent::setControllerPageTitleId("page_title_employee_data_changes");

		$this->LAGridRights->overrideInitRights("paging",				true);
		$this->LAGridRights->overrideInitRights("search",				true);
		$this->LAGridRights->overrideInitRights("search_header",		true);
		$this->LAGridRights->overrideInitRights("init_open_search",		true);
		$this->LAGridRights->overrideInitRights("select",				false);
		$this->LAGridRights->overrideInitRights("multi_select",			false);
		$this->LAGridRights->overrideInitRights("export_xlsx",			true);
		$this->LAGridRights->overrideInitRights("export_pdf_node",		false);

        $this->LAGridDB->enableArrMode();
		$this->LAGridDB->setPrimaryKey("primary_key");
		parent::setGridProperty("splitColumnEnabled", true,	 "dhtmlxGrid");
		parent::setGridProperty("splitColumn", 2,	 "dhtmlxGrid");
		parent::setExportFileName(Dict::getValue("page_title_employee_data_changes"));
		parent::G2BInit();
    }

	/**
	 * Oszlopok definiálása
	 * @return array
	 */
	public function columns()
    {
		$columns =
			[
				'fullname'			=>
					[
						'export'		=> true,
						'report_width'	=> 20,
						'col_type'		=> 'ed',
						'width'			=> 300,
						'class'			=> 'dialogTitle'
					],
				'emp_id'			=>
					[
						'export'		=> true,
						'report_width'	=> 20,
						'col_type'		=> 'ed',
						'width'			=> 150
					],
				'unit_name'			=>
					[
						'export'		=> true,
						'report_width'	=> 20,
						'col_type'		=> 'ed',
						'width'			=> 150
					],
				'modifier_username'	=>
					[
						'export'		=> true,
						'report_width'	=> 20,
						'col_type'		=> 'ed',
						'width'			=> 150
					],
				'changed_column'	=>
					[
						'export'		=> true,
						'report_width'	=> 20,
						'col_type'		=> 'ed',
						'width'			=> 220
					],
				'changed_from'		=>
					[
						'export'		=> true,
						'report_width'	=> 20,
						'col_type'		=> 'ed',
						'width'			=> 150
					],
				'changed_to'		=>
					[
						'export'		=> true,
						'report_width'	=> 20,
						'col_type'		=> 'ed',
						'width'			=> 150
					],
				'changed_datetime'	=>
					[
						'export'		=> true,
						'report_width'	=> 20,
						'col_type'		=> 'ed',
						'width'			=> 150
					]
			];

		$columns = $this->columnRights($columns);
		return $columns;
    }

	/**
	 * Oszlop elnevezések definiálása
	 * @return array
	 */
	public function attributeLabels()
    {
        $return =
			[
                'fullname'			=> Dict::getValue("name"),
				'emp_id'			=> Dict::getValue("emp_id"),
                'unit_name'			=> Dict::getValue("unit"),
				'modifier_username'	=> Dict::getValue("modifier_username"),
                'changed_column'	=> Dict::getValue("changed_column"),
                'changed_from'		=> Dict::getValue("changed_from"),
				'changed_to'		=> Dict::getValue("changed_to"),
				'changed_datetime'	=> Dict::getValue("changed_datetime"),
			];

		return $return;
    }

	/**
	 * Kereső definiálása - processId: workForce - felvett search_filter: EMPLOYEE_WITH_FROM_TO & DEFAULT_GROUP_FILTER
	 * @return array
	 */
	public function search() {
		return $this->getPreDefinedSearchFromDb("workForce");
	}

	/**
	 * Újonnan felvett dolgozók kinyerése
	 * @param string $gargSQL
	 * @param array $filter
	 * @return array
	 */
	public function getNewEmployees($gargSQL, $filter)
	{
		$resultArray = [];
		$gpf = new GetPreDefinedFilter(
			$this->getControllerID(),
			false,
			['company'=> "employee",'payroll'=> "employee"],
			false,
			$filter
		);
		$SQLfilter = $gpf->getFilter();

		$SQL = "SELECT
					`employee`.`row_id` AS `primary_key`,
					CONCAT(`employee`.`last_name`, ' ', `employee`.`first_name`) AS fullname,
					`employee`.`emp_id` AS emp_id,
					`unit`.`unit_name` AS unit_name,
					`user`.`username` AS modifier_username,
					'" . Dict::getValue("new_employee_recorded") . "' AS changed_column,
					'' AS changed_from,
					'' AS changed_to,
					`employee`.`created_on` AS changed_datetime
				FROM `employee`
				LEFT JOIN `employee_contract` ON
						`employee_contract`.`employee_id` = `employee`.`employee_id`
					AND `employee_contract`.`status` = " . Status::PUBLISHED . "
			";
		$SQL .= EmployeeGroup::getAllActiveLeftJoinSQL('employee_contract', '');
		$SQL .= EmployeeGroup::getAllBaseTablesWithGroupFromTo("employee", "employee_contract", $filter["valid_from"], $filter["valid_to"]);
		$SQL .= "
				LEFT JOIN `user` ON
						`user`.`user_id` = `employee`.`created_by`
					AND `user`.`status` = " . Status::PUBLISHED . "
					AND (`user`.`valid_to` >= '" . $filter["valid_from"] . "' AND `user`.`valid_from` <= '" . $filter["valid_to"] . "')
				JOIN `employee` AS e ON
						`employee`.`row_id` = `e`.`row_id`
					AND `e`.`valid_from` =
						(
							SELECT MIN(`valid_from`)
							FROM `employee`
							WHERE
									`emp_id` = `e`.`emp_id`
								AND `status` IN (" . Status::PUBLISHED . ", " . Status::STATUS_REMOVED . ")
						)
				WHERE
						`employee`.`status` IN (" . Status::PUBLISHED . ", " . Status::STATUS_REMOVED . ")
					AND {$SQLfilter}
					AND (`employee`.`created_on` BETWEEN '" . $filter["valid_from"] . "' AND '" . $filter["valid_to"] . "')
					AND (IFNULL(`employee_contract`.`valid_to`, '" . App::getSetting("defaultEnd") . "') >= `employee`.`valid_from` AND `employee_contract`.`valid_from` <= IFNULL(`employee`.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
					AND (IFNULL(`employee_contract`.`ec_valid_to`, '" . App::getSetting("defaultEnd") . "') >= `employee`.`valid_from` AND `employee_contract`.`ec_valid_from` <= IFNULL(`employee`.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
				";

		$SQL .= $gargSQL["where"];
		$SQL .=	"
				GROUP BY `employee`.`emp_id`
				ORDER BY fullname
				";

		$resultArray = $this->LAGridDB->getConnection()->createCommand($SQL)->queryAll();

		return $resultArray;
	}

	/**
	 * Amennyiben van AppLookUpba a változó mező neve akkor kikeresi a dict value értéket és azt adja vissza a kimutatásba.
	 * @param string $param
	 * @param string $lang
	 * @param array $changedData
	 * @return string
	 */
	public function getAppLookUpValue($param, $lang, $changedData)
	{
		if ($param == "old") {
			$actualValue = $changedData["prevValue"];
		} else if ($param == "new") {
			$actualValue = $changedData["newValue"];
		}

		$apl = new AppLookup;
		$crit = new CDbCriteria();
		$crit->condition = "
				`lookup_id` = '" . $changedData["changedColumn"] ."'
			AND `lookup_value` = '" . $actualValue . "'
			AND `valid` = 1";
		$app_lu = $apl->find($crit);

		if ($app_lu)
		{
			$dc = new Dictionary;

			$criteria = new CDbCriteria();
			$criteria->condition = "`dict_id` = '" . $app_lu->dict_id . "' AND `valid` = 1 AND `lang` = '" . $lang . "'";

			$dc_dict = $dc->find($criteria);

			if ($dc_dict) {
				$dataValue = $dc_dict->dict_value;
			} else {
				$dataValue = $actualValue;
			}
		} else {
			if ($changedData["changedColumn"] == "fs_file_id") {
				$dataValue = "";
			} else {
				$dataValue = $actualValue;
			}
		}

		return $dataValue;
	}

	/**
	 * Visszaadja a dolgozó módosításokhoz a korábbi adatot és az új adatot, valamint a feltételbe belerakja az employee_id vagy contract_id-t.
	 * A függvény csak az SQL részeit adja vissza a normál SQL előállításához.
	 * @param array $changedData
	 * @return array
	 */
	public function reformatSQL($changedData)
	{
		$lang = Dict::getLang();
		$explode = explode("_id", $changedData["changedColumn"]);

		if (count($explode) > 1)
		{
			$tableNames = dbGetTableNames();

			// Amennyiben saját táblába található az ID-hoz kapcsolódó érték (pl. name)
			if (in_array($explode[0], $tableNames))
			{
				// A név értéket tároló táblában található-e történetiség
				$tableCols = dbGetTableColumns($explode[0]);
				if (array_key_exists("valid_to", $tableCols) && array_key_exists("valid_from", $tableCols))
				{
					$oldValidTo = "AND (IFNULL(`oldVal`.`valid_to`, '" . App::getSetting("defaultEnd") . "') >= `employee`.`valid_from` AND `oldVal`.`valid_from` <= IFNULL(`employee`.`valid_to`, '" . App::getSetting("defaultEnd") . "'))";
					$newValidTo = "AND (IFNULL(`newVal`.`valid_to`, '" . App::getSetting("defaultEnd") . "') >= `employee`.`valid_from` AND `newVal`.`valid_from` <= IFNULL(`employee`.`valid_to`, '" . App::getSetting("defaultEnd") . "'))";
				} else {
					$oldValidTo = "";
					$newValidTo = "";
				}

				// Csoport1-2-3 esetén id nélkül name visszaadás
				if (strpos($changedData["changedColumn"], "company_org_group") !== false)
				{
					$SELECT = "`oldVal`.`company_org_group_name` AS changed_from,
								`newVal`.`company_org_group_name` AS changed_to,
								";
					$JOIN = "LEFT JOIN `" . $explode[0] . "` AS oldVal ON
									`oldVal`.`company_org_group_id` = '" . $changedData["prevValue"] . "'
								AND `oldVal`.`status` = " . Status::PUBLISHED . "
								{$oldValidTo}
							 LEFT JOIN `" . $explode[0] . "` AS newVal ON
									`newVal`.`company_org_group_id` = '" . $changedData["newValue"] . "'
								AND `newVal`.`status` = " . Status::PUBLISHED . "
								{$newValidTo}
							";

				// Szabadságtípus esetén dict érték visszaadása
				} else if (strpos($changedData["changedColumn"], "base_absence_type") !== false)
				{
					$SELECT = "`oldVal`.`dict_value` AS changed_from,
								`newVal`.`dict_value` AS changed_to,
								";
					$JOIN = "LEFT JOIN `base_absence_type` AS oldbat ON
									`oldbat`.`base_absence_type_id` = '" . $changedData["prevValue"] . "'
								AND `oldbat`.`status` = " . Status::PUBLISHED . "
							 LEFT JOIN `base_absence_type` AS newbat ON
									`newbat`.`base_absence_type_id` = '" . $changedData["newValue"] . "'
								AND `newbat`.`status` = " . Status::PUBLISHED . "
							 LEFT JOIN `dictionary` AS oldVal ON
									`oldVal`.`dict_id` = `oldbat`.`dict_id`
								AND `oldVal`.`valid` = 1
								AND `oldVal`.`lang` = '" . $lang . "'
							 LEFT JOIN `dictionary` AS newVal ON
									`newVal`.`dict_id` = `newbat`.`dict_id`
								AND `newVal`.`valid` = 1
								AND `newVal`.`lang` = '" . $lang . "'
							";

				// Tároló tábla name visszaadás
				} else {
					$SELECT = "`oldVal`.`" . $explode[0] . "_name` AS changed_from,
								`newVal`.`" . $explode[0] . "_name` AS changed_to,
								";
					$JOIN = "LEFT JOIN `" . $explode[0] . "` AS oldVal ON
									`oldVal`.`" . $changedData["changedColumn"] . "` = '" . $changedData["prevValue"] . "'
								AND `oldVal`.`status` = " . Status::PUBLISHED . "
								{$oldValidTo}
							 LEFT JOIN `" . $explode[0] . "` AS newVal ON
									`newVal`.`" . $changedData["changedColumn"] . "` = '" . $changedData["newValue"] . "'
								AND `newVal`.`status` = " . Status::PUBLISHED . "
								{$newValidTo}
							";
				}
			} else {
				// App lookup visszaadás, ha létezik
				$oldValue = $this->getAppLookUpValue("old", $lang, $changedData);
				$newValue = $this->getAppLookUpValue("new", $lang, $changedData);

				$SELECT = "'" . $oldValue . "' AS changed_from,
							'" . $newValue . "' AS changed_to,
							";
				$JOIN = "";
			}
		} else {
			//Csoportosítás esetén a group_id alapján tábla joinolás és érték visszaadás
			if ($explode[0] == "group_value")
			{
				$groupTable = explode("_id", $changedData["group_id"]);
				// Ha csoport1-2-3 akkor id nélkül name
				if (strpos($changedData["group_id"], "company_org_group") !== false)
				{
					$SELECT = "`oldVal`.`company_org_group_name` AS changed_from,
								`newVal`.`company_org_group_name` AS changed_to,
								";
					$JOIN = "LEFT JOIN `" . $groupTable[0] . "` AS oldVal ON
									`oldVal`.`company_org_group_id` = '" . $changedData["prevValue"] . "'
								AND `oldVal`.`status` = " . Status::PUBLISHED . "
								AND (IFNULL(`oldVal`.`valid_to`, '" . App::getSetting("defaultEnd") . "') >= `employee`.`valid_from` AND `oldVal`.`valid_from` <= IFNULL(`employee`.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
							 LEFT JOIN `" . $groupTable[0] . "` AS newVal ON
									`newVal`.`company_org_group_id` = '" . $changedData["newValue"] . "'
								AND `newVal`.`status` = " . Status::PUBLISHED . "
								AND (IFNULL(`newVal`.`valid_to`, '" . App::getSetting("defaultEnd") . "') >= `employee`.`valid_from` AND `newVal`.`valid_from` <= IFNULL(`employee`.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
							";
				// Ha nem csoport1-2-3 akkor a tábla _name értéke
				} else {
					$SELECT = "`oldVal`.`" . $groupTable[0] . "_name` AS changed_from,
								`newVal`.`" . $groupTable[0] . "_name` AS changed_to,
								";
					$JOIN = "LEFT JOIN `" . $groupTable[0] . "` AS oldVal ON
									`oldVal`.`" . $changedData["group_id"] . "` = '" . $changedData["prevValue"] . "'
								AND `oldVal`.`status` = " . Status::PUBLISHED . "
								AND (IFNULL(`oldVal`.`valid_to`, '" . App::getSetting("defaultEnd") . "') >= `employee`.`valid_from` AND `oldVal`.`valid_from` <= IFNULL(`employee`.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
							 LEFT JOIN `" . $groupTable[0] . "` AS newVal ON
									`newVal`.`" . $changedData["group_id"] . "` = '" . $changedData["newValue"] . "'
								AND `newVal`.`status` = " . Status::PUBLISHED . "
								AND (IFNULL(`newVal`.`valid_to`, '" . App::getSetting("defaultEnd") . "') >= `employee`.`valid_from` AND `newVal`.`valid_from` <= IFNULL(`employee`.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
							";
				}
			} else {
				// App lookup visszaadás, ha létezik
				$oldValue = $this->getAppLookUpValue("old", $lang, $changedData);
				$newValue = $this->getAppLookUpValue("new", $lang, $changedData);

				$SELECT = "'" . $oldValue . "' AS changed_from,
							'" . $newValue . "' AS changed_to,
							";
				$JOIN = "";
			}
		}

		// Adott változás employee_id alapú vagy employee_contract alapú
		if (isset($changedData["employee_id"])) {
			$WHERE = "AND (`employee`.`employee_id` = '" . $changedData["employee_id"] . "')";
		} else if ($changedData["employee_contract_id"]) {
			$WHERE = "AND (`employee_contract`.`employee_contract_id` = '" . $changedData["employee_contract_id"] . "')";
		}

		return
		[
            [
                "select"	=> $SELECT,
				"join"		=> $JOIN,
				"where"		=> $WHERE
            ]
        ];
	}

	/**
	 * A függvény leszedi a main_logból egy időintervallumba a gridben létrehozott módosításokat.
	 * @param array $filter
	 * @return array
	 */
	public function getMainLogData($filter)
	{
		$mainLogArray = [];
		$MAINLOGSQL = "	SELECT
							`main_log`.`event_time`,
							`main_log`.`username`,
							QUOTE(`main_log`.`log_params`) AS log_params
						FROM `main_log`
						WHERE
								`main_log`.`log_message` = 'GRID_DATA_CHANGE'
							AND (`main_log`.`event_time` BETWEEN '" . $filter["valid_from"] . "' AND '" . $filter["valid_to"] . "')
						GROUP BY `main_log`.`log_params`
						";
		$mainLogArray = $this->LAGridDB->getConnection()->createCommand($MAINLOGSQL)->queryAll();

		// log paramsból kiszedjük az adatokat asszociatív tömbbe json_decode-l
		for ($i = 0; $i < count($mainLogArray); $i++)
		{
			$log_params = [];
			$log_params = json_decode(substr($mainLogArray[$i]["log_params"], 1, -1), TRUE);

			$mainLogArray[$i]["data"] = $log_params["data"];
			unset($mainLogArray[$i]["log_params"]);
		}

		return $mainLogArray;
	}

	/**
	 * A függvény a main_logból kiszedett módosítások alapján leszedi a végleges adatot a dolgozók módosításaihoz.
	 * @param array $gargSQL
	 * @param array $filter
	 * @return array
	 */
	public function getEmployeeChanges($gargSQL, $filter)
	{
		$resultArray = [];

		$mainLogArray = $this->getMainLogData($filter);

		foreach ($mainLogArray as $mainLogArrayRows)
		{
			$changeTime = $mainLogArrayRows["event_time"];
			$modifierUsername = $mainLogArrayRows["username"];

			foreach ($mainLogArrayRows["data"] as $mLA)
			{
				// csak ha employee***Tab a formId tehát dolgozó kezelésbe történt módosítás
				if ((strpos($mLA["formId"], "employee") !== false) && (strpos($mLA["formId"], "Tab") !== false))
				{
					$explode = explode ("_id", $mLA["changedColumn"]);

					$reformatedData = $this->reformatSQL($mLA);

					// Változott oszlop dict_id felülírása - megadható osztály változóba, ill. csoportosítás fülnél melyik csoport adat változik
					if (array_key_exists($mLA["changedColumn"], $this->dict)) {
						$changedCol = $this->dict[$mLA["changedColumn"]];
					} else if ($mLA["changedColumn"] == "group_value") {
						$changedGroupExplode = explode("_id", $mLA["group_id"]);
						$changedCol = $changedGroupExplode[0];
					} else {
						$changedCol = $explode[0];
					}

					// Dolgozó fül dict_id kinyerés
					$explodeFormId = explode("Tab", $mLA["formId"]);
					if (strtolower($explodeFormId[0]) == "employeegroup") {
						$tabDictIdentifier = "tab_employeetabs_employee_group";
					} else {
						$tabDictIdentifier = "tab_employeetabs_" . strtolower($explodeFormId[0]);
					}

					$gpf = new GetPreDefinedFilter(
						$this->getControllerID(),
						false,
						['company'=> "employee",'payroll'=> "employee"],
						false,
						$filter
					);
					$SQLfilter = $gpf->getFilter();

					$SQL = "SELECT
								`employee`.`row_id` AS `primary_key`,
								CONCAT(`employee`.`last_name`, ' ', `employee`.`first_name`) AS fullname,
								`employee`.`emp_id` AS emp_id,
								`unit`.`unit_name` AS unit_name,
								'" . $modifierUsername . "' AS modifier_username,
								'" . str_replace("'", "", Dict::getValue($tabDictIdentifier) . " - " . Dict::getValue($changedCol)) . "' AS changed_column,
								{$reformatedData[0]["select"]}
								'" . $changeTime . "' AS changed_datetime
							FROM `employee`
							LEFT JOIN `employee_contract` ON
									`employee_contract`.`employee_id` = `employee`.`employee_id`
								AND `employee_contract`.`status` = " . Status::PUBLISHED . "
					";
					$SQL .= EmployeeGroup::getAllActiveLeftJoinSQL('employee_contract', '');
					$SQL .= EmployeeGroup::getAllBaseTablesWithGroupFromTo("employee", "employee_contract", $filter["valid_from"], $filter["valid_to"]);
					$SQL .= "
							{$reformatedData[0]["join"]}
							WHERE
									`employee`.`status` IN (" . Status::PUBLISHED . ", " . Status::STATUS_REMOVED . ")
								AND {$SQLfilter}
								AND employee.valid_from <= '{$filter["valid_to"]}' AND '{$filter["valid_from"]}' <= IFNULL(employee.valid_to, '" . App::getSetting('defaultEnd') . "')
								{$reformatedData[0]["where"]}
								AND (IFNULL(`employee_contract`.`valid_to`, '" . App::getSetting("defaultEnd") . "') >= `employee`.`valid_from` AND `employee_contract`.`valid_from` <= IFNULL(`employee`.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
								AND (IFNULL(`employee_contract`.`ec_valid_to`, '" . App::getSetting("defaultEnd") . "') >= `employee`.`valid_from` AND `employee_contract`.`ec_valid_from` <= IFNULL(`employee`.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
							";

					$SQL .= $gargSQL["where"];
					$SQL .=	"
							GROUP BY `employee`.`emp_id`
							ORDER BY fullname
							";

					$resultArray[] = $this->LAGridDB->getConnection()->createCommand($SQL)->queryAll();
				}
			}

		}

		return $resultArray;
	}

	/**
	 * Adathalmaz összeállítása a Grid2 feltöltéséhez
	 * @param string $gridID
	 * @param array $filter
	 * @param bool $isExport
	 * @return array
	 */
	protected function dataArray($gridID, $filter, $isExport = false)
	{
        $regenerate = false;
		foreach ($this->fetchParams as $var => $val) {
			${$var} = $val;
		}

		$results = [];

		if ($regenerate)
		{
			$art = new ApproverRelatedGroup;
			$gargSQL = $art->getApproverReleatedGroupSQLWithoutCalc("Employee", "employeeManagement", false, "AND", "CurrentDate", $this->getControllerID());

			$newEmployees = $this->getNewEmployees($gargSQL, $filter);
			$employeeChanges = $this->getEmployeeChanges($gargSQL, $filter);

			$results = $newEmployees;

			foreach ($employeeChanges as $employeeChangesRows) {
				//Duplázódás vizsgálás dupla action save miatt (changed_datetime eltér pár másodpercet) 5 mp belül
				$differs = $this->dataDiffers(5, $results, $employeeChangesRows);
				if ($differs) {
					$results = array_merge($results, $employeeChangesRows);
				}
			}
		}

		return $results;
	}

	/**
	 * Nézi hogy azonos oszlopból azonos dolgozónál megadott másodpercen belül már volt-e változás, ha igen akkor false-t ad vissza, egyébként true-t.
	 * @param int $maxDifference
	 * @param array $actualArray
	 * @param array $compareArray
	 * @return boolean
	 */
	public function dataDiffers($maxDifference, $actualArray, $compareArray)
	{
		$differs = true;
		foreach ($compareArray as $cA)
		{
			$date1 = new DateTime($cA["changed_datetime"]);

			foreach ($actualArray as $aA)
			{
				if ($aA["emp_id"] == $cA["emp_id"] && $aA["changed_column"] == $cA["changed_column"])
				{
					$date2 = new DateTime($aA["changed_datetime"]);
					$diffInSeconds = $date2->getTimestamp() - $date1->getTimestamp();

					if ($diffInSeconds < $maxDifference) {
						$differs = false;
					}
				}
			}
		}

		return $differs;
	}

	/**
	 * Biztonsági függvény #1
	 * @return array
	 */
	public function filters() {
        return [
            'accessControl', // perform access control for CRUD operations
        ];
    }

	/**
	 * Biztonsági függvény #2
	 * @return array
	 */
	public function accessRules()
	{
        return
		[
            [
				'allow', // allow authenticated users to access all actions
                'users' => ['@'],
            ],
            [
				'deny',  // deny all users
                'users' => ['*'],
            ],
        ];
    }
}
?>