<?php

class MenuRoleInGroupController extends Grid2Controller
{
	private $lang;
	private $modules;
	private $isRoot;

	public function __construct()
	{
		parent::__construct("menuRoleInGroup");
		parent::enableLAGrid();
		$this->lang = Dict::getLang();
		$modules = Yang::getModules();
		array_push($modules, 'ttwa-base');
		$this->modules = implode("', '", $modules);
		$this->isRoot = App::isRootSessionEnabled();
	}

	protected function G2BInit()
	{
		parent::enableMultiGridMode();

		parent::setControllerPageTitleId("page_title_menu_role_in_group");

		parent::setExportFileName(Dict::getValue('page_title_menu_role_in_group'));

		$this->LAGridRights->overrideInitRights("paging", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search_header", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("select", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("multi_select", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("column_move", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload_sortings", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("details", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("add", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("modify", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("delete", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("init_open_search", false, 'dhtmlxGrid');
		$this->LAGridRights->overrideInitRights("export_xls", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_xlsx", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("save_state", true, "dhtmlxGrid");

		parent::setGridProperty("splitColumnEnabled", true, "dhtmlxGrid");
		parent::setGridProperty("splitColumn", 1, "dhtmlxGrid");

		$this->LAGridDB->enableArrMode('dhtmlxGrid');

		parent::G2BInit();
	}

	protected function dataArray($gridID, $filter)
	{
		if ($gridID === "dhtmlxGrid") {
			$menuItems = $this->getMenuItems();
			$arig = $this->getAuthRoleInGroup();
			$roleGroups = $this->getRolegroups();

			foreach ($menuItems as $roleId => $menuItem) {
				$retArr[$roleId]['role_id'] = $roleId;
				$retArr[$roleId]['role_name'] = $menuItem['role_name'];
				$retArr[$roleId]['customer_visibility'] = $menuItem['customer_visibility'];
				foreach ($roleGroups as $rolegroupId => $rolegroup) {
					if (array_key_exists($roleId, $arig) && in_array($rolegroupId, $arig[$roleId])) {
						$retArr[$roleId][$rolegroupId] = '1';
					} else {
						$retArr[$roleId][$rolegroupId] = '2';
					}
				}
			}

			return $retArr;
		} else {
			return [];
		}
	}

	private function getMenuItems()
	{
		$SQL = "
			SELECT
				auth_role.role_id,
				IFNULL(CONCAT(d.dict_value, ' (', menu.menu_visible, ')'), auth_role.role_name) as role_name,
				auth_role.customer_visibility
			FROM
				auth_role
			LEFT JOIN auth_acl ON
					auth_acl.role_id = auth_role.role_id
				AND auth_acl.operation_id IN ('view', 'write')
			LEFT JOIN menu_item_table menu ON
					menu.menu_visible = auth_acl.controller_id
				AND menu.menu_modul IN ('{$this->modules}')
			LEFT JOIN dictionary d ON
					d.dict_id = menu.menu_label
				AND d.valid = 1
				AND d.lang =  '{$this->lang}'
			WHERE menu.menu_item_id IS NOT NULL";
		if (!$this->isRoot) {
			$SQL .= "
					AND auth_role.customer_visibility = 1
					";
		}
		$SQL .= "
					ORDER BY role_name
			";
		$this->LAGridDB->setSQLSelection($SQL, "role_id", "dhtmlxGrid");
		return dbFetchAll($SQL, 'role_id');
	}

	private function getRolegroups()
	{
		$rolegroupSQL = "SELECT rolegroup_id, rolegroup_name
				 FROM auth_rolegroup
				 WHERE visibility = 1";

		if ($this->isRoot) {
			$rolegroupSQL .= "
				 OR rolegroup_id = 'a5b6bd79e008725744118c7c46e10cda'";
		}
		return dbFetchAll($rolegroupSQL, 'rolegroup_id');
	}

	private function getAuthRoleInGroup()
	{
		$SQL = "SELECT rolegroup_id, role_id
			 FROM auth_role_in_group";
		$res = dbFetchAll($SQL);
		$retArr = [];
		foreach ($res as $r) {
			$retArr[$r['role_id']][] = $r['rolegroup_id'];
		}
		return $retArr;
	}

	public function columns()
	{
		$columns = [];

		$rolegroups = $this->getRolegroups();

		$role_SQL = "
			SELECT `role_id` as id, IFNULL(CONCAT(d.dict_value, ' (', menu.menu_visible, ')'), auth_role.role_name) AS value
			FROM `auth_role`
			LEFT JOIN auth_acl ON
					auth_acl.role_id = auth_role.role_id
			LEFT JOIN menu_item_table menu ON
					menu.menu_visible = auth_acl.controller_id
			LEFT JOIN dictionary d ON
					d.dict_id = menu.menu_label
				AND d.valid = 1
				AND d.lang = '{$this->lang}'
			WHERE menu.menu_item_id IS NOT NULL
		";

		if (!App::getRight(null, "su") && (int) App::getSetting("auth_role_customer_visibility")) {
			$role_SQL .= " AND `customer_visibility` = 1";
		}

		$role_SQL .= "
			ORDER BY `role_name`
		";

		$columns["dhtmlxGrid"]= [
			'role_name'			=> ['grid' => true, 'window' => false, 'export' => true, 'col_type' => 'ed', 'width' => 500],
			'customer_visibility'	=> ['grid' => false, 'window' => false, 'export' => false, 'col_type' => 'cb', 'width' => 15, 'align' => 'center'],

			'role_id'			=> [
				'grid'			=> false,
				'export'		=> true,
				'col_type'		=> 'combo',
				'options'		=>	[
					'mode'			=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'			=> $role_SQL,
				],
			],
		];

		foreach ($rolegroups as $id => $rolegroup) {
			$columns['dhtmlxGrid'][$id] = ['grid' => true, 'window' => false, 'export' => true, 'col_type' => 'cb', 'width' => 25, 'align' => 'center'];
		}

		return $columns;
	}

	public function attributeLabels()
	{
		$labels = [];

		$labels["dhtmlxGrid"] = [
			'role_name' => Dict::getValue("menu"),
			'customer_visibility' => Dict::getValue("customer_visibility"),
		];

		$rolegroups = $this->getRolegroups();

		$rolegroupLabels = [];
		foreach ($rolegroups as $id => $rolegroup) {
			$labels['dhtmlxGrid'][$id] = $rolegroup['rolegroup_name'];
		}

		return $labels;
	}

	public function actionSaveAll()
	{
		$this->layout = "//layouts/ajax";

		$this->G2BInit();

		$ids = requestParam('ids');
		$idsArray = [];
		if ($ids !== "") {
			$idsArray = explode(";", $ids);
			$idsArray = explodeKeyVal('[]', $idsArray, 0);
		}

		$uncheckedIds = requestParam('unids');
		$unIdsArray = [];
		if ($uncheckedIds !== "") {
			$unIdsArray = explode(";", $uncheckedIds);
			$unIdsArray = explodeKeyVal('[]', $unIdsArray, 0);
		}
		$arig = $this->getAuthRoleInGroup();

		foreach ($idsArray as $roleId => $rolegroupIds) {
			foreach ($rolegroupIds as $rgId) {
				if (!($rgId =='customer_visibility') && (!in_array($roleId, $arig) || (in_array($roleId, $arig) && !in_array($rgId, $arig[$roleId])))) {
					$roleInGroup = new AuthRoleingroup();
					$roleInGroup->rolegroup_id = $rgId;
					$roleInGroup->role_id = $roleId;
					$roleInGroup->save();
				}
			}
		}

		foreach ($unIdsArray as $roleId => $rolegroupIds) {
			foreach ($rolegroupIds as $rgId) {
				if (!($rgId =='customer_visibility') && in_array($rgId, $arig[$roleId])) {
					$delSQL="DELETE FROM auth_role_in_group WHERE role_id = '{$roleId}' AND rolegroup_id = '{$rgId}'";
					dbExecute($delSQL);
				}
			}
		}

		$status = [
			'status'	=> 1,
		];

		echo json_encode($status);
	}

}
