<?php

'yii2-only`;

	namespace app\controllers;
	use Yang;

`/yii2-only';


#yii2: done

class ReportMenuController extends Controller
{
	public $layout = 'ajax';

	public function actionIndex() {
		$this->layout = 'ajax';

		$user_id = userID();

		$getActualReportsSQL = "
			SELECT
				r.*
			FROM
				`report` r
			WHERE
				r.`user_id` = '{$user_id}'
				-- 	AND r.`created_on` >= DATE_ADD(NOW(), INTERVAL -1 DAY)
			ORDER BY
				r.`created_on` DESC
			LIMIT
				100
		";

		$actualReports = dbFetchAll($getActualReportsSQL);

		$this->render('index', ["actualReports" => $actualReports]);
	}

	public function actionMenuItem() {
		$this->layout = 'ajax';

		$reportId = requestParam('report_id');

		$getActualReportSQL = "
			SELECT
				r.`report_id`,
				r.`report_type`,
				r.`controller_id`,
				r.`page_title`,
				r.`file_type`,
				r.`file_name`,
				r.`report_path`,
				r.`report_created`,
				r.`report_status`
			FROM
				`report` r
			WHERE
				r.`report_id` = '{$reportId}'
		";

		$actualReport = dbFetchAll($getActualReportSQL);

		$this->render('reportMenuItem', ["actualReport" => $actualReport[0]]);
	}

	public function actionCopyReportResults() {
		$this->layout = 'ajax';

		$reportData = requestParam('report');

		$reportId		= $reportData["report_id"];
		$reportStatus	= $reportData["report_status"];
		$reportPath		= $reportData["report_path"];

		$updateActualReportSQL = "
			UPDATE
				`report`
			SET
				`report_status` = '{$reportStatus}',
				`report_path` = '{$reportPath}'
			WHERE
				`report_id` = '{$reportId}'
		";

		dbExecute($updateActualReportSQL);
	}
}

?>