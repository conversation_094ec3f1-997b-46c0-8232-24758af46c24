<?php

'yii2-only`;

	namespace app\controllers;
	use Yang;

`/yii2-only';


#yii2: done

ini_set('max_execution_time', 3600);
ini_set("display_errors", 1);
ini_set("memory_limit", "2048M");

define('DS', DIRECTORY_SEPARATOR);

/**
 * Régi adatok törlése
 * 
 * @date: 2018-05-01
 * @author: bg
 */
class deleteOldDataController extends Controller{


	// emmbe a mappába kerül a log file
	private $log_file_folder;

	// ebbe a fájlba mentjük a hibákat
    private $log_file;
	
	// futtatás kezdete
	private $start_time;

	private $database_name = NULL;

	//ádtum.. mely időpont előtti adatokat akarjuk törölni
	private $date = NULL;
		
	// get date adat megszűrve
	private $get_date = NULL;
	
	private $proc_columns = [];


	public function __construct(){

		// EZEKET KELL BEÁLLÍTANI
		
		// log file helye
		$this->log_file_folder = 'C:'.DS.'xampp'.DS.'htdocs'.DS.'Login'.DS.'TTWA'.DS.'runtime'.DS.'deleteOldData'.DS;
		
		// log file neve (ezt nem fontos módosítani)
		$this->log_file = "deleteOldData_log_" . date("Ymd_H") . ".txt";
		
		//adatbázis neve
		$this->database_name = 'bos_ttwa'; 
		
		// BEÁLLÍTÁS VÉGE
		
		$this->start_time = self::getMicrotime();
				
		$this->get_date = filter_input(INPUT_GET, 'date');

		$this->proc_columns = ['valid_to', 'date', 'day'];
    }


    public function actionIndex(){

		$this->writeLog('--------------------------------------------------', 'START');
		$this->writeLog('Process START!');
		
		// dátum validálása
		$this->validateDate();
		
		foreach ($this->proc_columns as $pc) {

			$this->processingTableColumn($pc);

		}

		$this->endProcess();
		
	}

		
	
	/**
	 * Dátum validálása
	 */
	private function validateDate(){
		
		if(is_null($this->get_date)){
			
			$this->writeLog('Missing date parameter!', 'WARNING');
			
			$this->endProcess();
			
		}
		
		$this->get_date = str_replace(".", "-", $this->get_date);
		
		if (preg_match("/^[0-9]{4}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/", $this->get_date)) {
			
			$this->date = date('Y-m-d', strtotime($this->get_date) ); 
			
			// Van dátum adat
			$this->writeLog('Date parameter OK! ('.$this->date.')');
		
		} else {
			
			$this->writeLog('Date parameter false! (date:'.$this->get_date.')', 'WARNING');
			
			$this->endProcess();
			
		}
		
	}
	
	
	/**
	 * Táblák feldolgozása
	 * Elsőnek lekérjük a táblákat, amikben szerepel a $column -ban lévő oszlop neve
	 * Majd ezt e tömböt bejárva töröljük az adatokat.
	 * 
	 * @param string $column
	 */
	private function processingTableColumn($column){
		
		$tables = $this->getTables($column);
				
		if( (is_array($tables)) AND (count($tables)) ){

			foreach($tables as $table){

				$rows = $this->deleteFromTable($table['table_name'], $column);

				if($rows !== FALSE){
					$this->writeLog('DELETE '.$rows.' row(s) for '.$table['table_name'].' table ('.$column.' < '.$this->date.')');
				}else{
					$this->writeLog('deleteFromTable return FALSE  for '.$table['table_name'].' table ('.$column.' < '.$this->date.')', 'WARNING');
				}
				
			}

		}
				
	}


	/**
	 * Adatok törlése adatbázisból
	 *
	 * @param string $table_name default null
	 * @param string $column default null
	 * @param string $and default null
	 * @return bool | int 
	 */
	private function deleteFromTable($table_name = NULL, $column = NULL, $and= NULL){
		
		$affected_rows = FALSE;
		
		$this->writeLog('function deleteFromTable(table:'.$table_name.', column:'.$column.', and:'.$and.')');
		
		$deleteDataSQL =  $this->prepareDeleteSql($table_name, $column, $and);
				
		// Ha összeraktunk valami SQL-t akkor megfuttatjuk
		if($deleteDataSQL != '' ){
			
			// loggoljuk az SQL-t	
			$this->writeLog('DELETE SQL:('.$deleteDataSQL.')');
	
			if( (!isset($_GET['teszt'])) AND (!isset($_GET['test'])) ){
				$affected_rows = dbExecute($deleteDataSQL);
			}else{
				$affected_rows = $this->getTableRowsCount($table_name, $column, $and);
			}
			
			
		}
		
		return $affected_rows;
		
	}
	
	/**
	 * Törlés végző sql összerakása
	 * 
	 * @param string $table_name
	 * @param string $column
	 * @param string $and
	 * @return string
	 */
	private function prepareDeleteSql($table_name = NULL, $column = NULL, $and = NULL){
	
		$ret = '';
		
		$where = '';
			
		if( (!is_null($table_name)) AND ($table_name != '') ){
			
			$this->writeLog('Table name ('.$table_name.')');
			
			if( (!is_null($column)) AND ($column != '') ){
				
				$this->writeLog('column name ('.$column.')');
				
				$where .= " AND `".$column."` < '".$this->date."' ";
											
			}
			
			// Az $and opcionális.. de hátha kell valamire
			if( (!is_null($and)) AND ($and != '') ){

				$this->writeLog('and param .. ('.$and.')');
				
				// ha nincs benne az AND akkor belerakjuk 
				if( (substr($and, 0, 3) != 'AND') AND (substr($and, 0, 4) != ' AND') ){
					$where .= "AND ".$and;
				}
				
			}
			
			if($where != ''){
				$ret = "DELETE FROM `".$table_name."` WHERE 1=1 ".$where.";";
			}
			
		}
		
		return $ret;
		
	}
	

	/**
	 * Teszt esetén ezzel kérdezzük le, hogy hány sort érintene a törlés
	 * 
	 * @param string $tabe Tábla neve
	 * @param string $column Oszlop neve
	 * @param string $and and feltétel a lekérdezélsehez
	 * @return int
	 */
	public function getTableRowsCount($table_name, $column = NULL, $and= NULL){
		
		$this->writeLog('function getTableRowsCount('.$table_name.', '.$column.', '.$and.')');
		
		$ret = 0;
	
		$sql = "SELECT COUNT(*) as db FROM `".$table_name."` WHERE 1=1 ";
		
		if( (!is_null($column)) AND ($column != '') ){
			
			$sql .= " AND `".$column."` < '".$this->date."' ";
			
			if( (!is_null($and)) AND ($and != '') ){
					
				// ha nincs benne az AND akkor belerakjuk kényelem lvl 99
				if( (substr($and, 0, 3) != 'AND') AND (substr($and, 0, 4) != ' AND') ){
					$and = "AND ".$and;
				}

				$sql .= $and;
			}
				
				
		}else{
				
			// Ha nincs megadva oszlop ($column) akkor csak az $and részt kell belerakni az SQL-be
			if( (!is_null($and)) AND ($and != '') ){

				if( (substr($and, 0, 3) != 'AND') AND (substr($and, 0, 4) != ' AND') ){
					$and = "AND ".$and;
				}

				$sql .= $and ;
			}

		}
		
		$sql .= ";";
		
		$this->writeLog('SQL:('.$sql.')');
				
        try{

            $result = dbFetchRow($sql);
			
			if(is_array($result)){
				$ret = $result['db'];
			}

        }catch (Exception $e){

            $this->writeLog($e->getMessage(), 'WARNING');

        }
		
		return $ret;
		
	}
	
	
	/**
	 * Táblák lekérdezése amelyikben szerepel a columns-ban megadott mezők
	 *
	 * @param string $columns
	 * @return bool | array
	 */
	private function getTables($columns=NULL){
		
		$this->writeLog('function getTables()');
		
		$ret = FALSE;
		
		if( (!is_null($this->database_name)) AND (!is_null($columns)) AND ($columns != '') ){
			
			$sql = "SELECT DISTINCT TABLE_NAME as table_name
							FROM INFORMATION_SCHEMA.COLUMNS
							WHERE COLUMN_NAME IN ('".$columns."')
							AND TABLE_SCHEMA='".$this->database_name."';";
				
			$this->writeLog('SQL:('.$sql.')');
				
			try{

				$result =  dbFetchAll($sql);

				if(is_array($result)){
					$ret = $result;
				}
			
			}catch (Exception $e){

				$this->writeLog($e->getMessage(), 'WARNING');

			}
			
		}else{
			$this->writeLog('Missing database_name!', 'WARNING');
		}
				
		return $ret;		
		
	}
	
		
	/**
	 * Folyamat lezárása program megállítása
	 */
	private function endProcess(){
		
		$end_time = self::getMicrotime();
		
		$this->writeLog('Process end... Total process time:'.round(($end_time - $this->start_time), 4).' seconds');
		
		$this->writeLog('--------------------------------------------------', 'END');
		
		exit();
		
	}
	
	
	/**
     * Write log file
     *
     * @param string $msg
     */
    private function writeLog($msg, $level = 'INFO'){

		if(!is_null($this->log_file_folder)){

			$this->checkDir();
			
			if($msg != ''){
				
				$file = fopen($this->log_file_folder . $this->log_file, "a+");

				$level = '['.$level.']';
				
				$level = str_pad($level, 9, "0");
				
				$level = str_replace("0", " ", $level);

				fwrite($file, date('Y-m-d H:i:s') . ' | '.$level.' | ' . $msg . PHP_EOL);

				fclose($file);
				
			}
			
		}
		
    }
	
	/**
     * Könyvtár létrehozása ha még nem létezik
     *
     * @param string $path
     */
    function checkDir(){
			
		if(!is_dir(trim($this->log_file_folder, DS))){
				
			mkdir($this->log_file_folder, 0755, TRUE);
				
		}
		
    }
	
	
	/**
	 * return float time
	 */
	private function getMicrotime(){
		
		$time = explode(' ', microtime());
		
		return $time[1] + $time[0];
				
	}
	

	
}

