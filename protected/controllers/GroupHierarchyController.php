<?php #yii2: done

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2\Grid2Controller;
	use app\models\AppSettings;
	use Yang;

`/yii2-only';


#yii2: done

class GroupHierarchyController extends Grid2Controller
{
	use EmployeeControllerWizards;
	
	private $groups = [];
	
	public function __construct() {
		parent::__construct("groupHierarchy");

		parent::enableLAGrid();
	}

	protected function G2BInit() {
		parent::setControllerPageTitleId("page_title_group_hierarchy");

		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("add",				true);
		$this->LAGridRights->overrideInitRights("modify",			false);
		$this->LAGridRights->overrideInitRights("delete",			true);

		$this->LAGridDB->enableArrMode();
		
		$this->groups = [
							'company_org_group1'	=> Dict::getValue('company_org_group1'),
							'company_org_group2'	=> Dict::getValue('company_org_group2'),
							'company_org_group3'	=> Dict::getValue('company_org_group3'),
							'unit'					=> Dict::getValue('unit'),
							'work_activity'			=> Dict::getValue('work_activity_name'),
						];
		$this->LAGridDB->setPrimaryKey('group');
		
		parent::G2BInit();
	}

	protected function dataArray($gridID, $filter) {
		$result = [];

		if (isset($_SESSION["tiptime"]["settings"]["group_hierarchy"])) {
                    unset($_SESSION["tiptime"]["settings"]["group_hierarchy"]);
		}
                
                $setting_value = App::getSetting("group_hierarchy");
                
                if ($setting_value !== "") {
                    $groups = explode(";", $setting_value);

                    $i = 0;
                    foreach ($groups as $group) {
                        $result[$i]['group'] = $group;
                        $result[$i]['group_name'] = $this->groups[$group];
                        $i++;
                    }
                }
                
		return $result;
	}

	public function columns()
	{
		$groups = [];
		foreach ($this->groups as $group => $groupName){
			$groups[] = ['id' => $group, 'value' => $groupName];
		}

		$columns = [
			'group'			=> array(
				'grid' 			=> false,
				'export'		=> true,
				'col_type'		=> 'combo',
				'options'		=>	array(
										'mode'					=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
										'array'					=> $groups,
									),
				'width' => 150,
			),
			'group_name'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'add' => false, 'edit' => false, 'width' => 150),
		];

		return $columns;
	}

	public function attributeLabels() {
		return [
				"group" => Dict::getValue('group_table_name'),
				"group_name" => Dict::getValue('group_name'),
				];
	}

	public function actionSave($data = array(), $modelName = null, $pk = null, $vOnly = false, $ret = false, $contentId = NULL) {
		$this->layout = "//layouts/ajax";
		$this->G2BInit();

		$generateFrom = requestParam('generateFrom');
		$data = requestParam('dialogInput_'.$generateFrom);
               
                $setting_value = App::getSetting("group_hierarchy");
                $filter = "`setting_id` = 'group_hierarchy'";
                
                if ($setting_value !== "") {
                    $groups = explode(";", $setting_value);
                    if (!in_array($data["group"], $groups)) {
                        $groups[] = $data["group"];
                        AppSettings::model()->updateAll(['setting_value' => implode(";", $groups)], $filter);
                    }
                } else {
                    AppSettings::model()->updateAll(['setting_value' => $data["group"]], $filter);
                }

		$status = [
			'status'	=> 1,
		];

		echo json_encode($status);
	}

	public function actionDelete($modelName = null, $hasRight = false) {
		$this->layout = "//layouts/ajax";

		$this->G2BInit();

		$ids = requestParam('ids');
		$idsArray = explode(";", $ids);
                
		$groups = explode(";",App::getSetting("group_hierarchy"));

		foreach ($groups as $key => &$group) {
			if (in_array($group, $idsArray)) {
				unset($groups[$key]);
			}
		}

		$filter = "`setting_id` = 'group_hierarchy'";
                
                if (count($groups) > 0) {
                    AppSettings::model()->updateAll(['setting_value' => implode(";", $groups)], $filter);
                } else {
                    AppSettings::model()->updateAll(['setting_value' => ""], $filter);
                }
	}
}