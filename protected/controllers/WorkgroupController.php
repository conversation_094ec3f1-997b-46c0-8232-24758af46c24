<?php

/**
 * Workgroup controller
 *
 */
class WorkgroupController extends Grid2HistoryController
{
	public $layout = '//layouts/main';
	private $user;
	private $showChickenEntitled;
	private $usedGroup;
	private $heatmapGroups;
	private $freezeColumns;
	private $showId;
	private $pub = Status::PUBLISHED;
	private $del = Status::DELETED;
	private $end;
	private $workgroupShowRoundTypes;
	private $specialFrameDaysGroup;
	private $summarySheetCalculationShiftStartInWorkgroup;
	private $summarySheetCalculationSundaySign;
	private $summarySheetPaidLunchtimeReduction;
    private $dailyWorkingTimeConsolidation;
	private $wfmUsedSeconds;
	private $balanceMax;
	private $balanceRoundTolerance;
	private $useCompanyAndPayrollRights;
	private $workgroupFiLoFilter;
	private $isWorkgroupAllowanceShown;

	public function __construct()
	{
		parent::__construct("workgroup");
		$this->user											= userID();
		$this->usedGroup									= App::getSetting("heatmapGroup");
		$this->heatmapGroups								= App::getSetting("heatmapGroups");
		$this->freezeColumns								= App::getSetting("freezeWorkgroupNameAndCompany");
		$this->showChickenEntitled							= App::getSetting("workgroupShowChickenPckgEntitled");
		$this->showId										= App::getSetting("workgroupController_show_id");
		$this->end											= App::getSetting("defaultEnd");
		$this->workgroupShowRoundTypes						= App::getSetting("workgroup_showRoundTypes");
		$this->specialFrameDaysGroup						= App::getSetting("specialFrameDaysGroup");
		$this->summarySheetCalculationShiftStartInWorkgroup	= App::getSetting("summarySheet_calculation_shiftStartInWorkgroup");
		$this->summarySheetCalculationSundaySign			= App::getSetting("summarySheet_calculation_sunday_sign");
		$this->summarySheetPaidLunchtimeReduction			= App::getSetting("summarySheet_paid_lunchtime_reduction");
        $this->dailyWorkingTimeConsolidation			    = App::getSetting("summarySheet_dailyWorkingTimeConsolidation");
		$this->wfmUsedSeconds								= App::getSetting("wfm_used_seconds");
		$this->balanceMax									= App::getSetting("workgroupBalanceMax");
		$this->balanceRoundTolerance						= App::getSetting("balanceRoundTolerance");
		$this->useCompanyAndPayrollRights					= App::getSetting("useCompanyAndPayrollRights");
		$this->workgroupFiLoFilter							= (bool) App::getSetting("wg_filo_filter");
		$this->isWorkgroupAllowanceShown					= (int)App::getSetting("isWorkgroupAllowanceShown") === 1;
		//parent::enableLAGrid();
	}

	protected function G2BInit() {
		$this->LAGridDB->setModelName("Workgroup");

		parent::setControllerPageTitleId("page_title_workgroup");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		true);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);

		$filters = Yang::session('workgroup_filters',[]);

		if (isset($filters["date"]))
		{
			$companyPayrollFilter = "";
			if ($this->useCompanyAndPayrollRights) {
				$companyPayrollFilter = "
					AND (`company_id` = '{company}' OR '{company}' = 'ALL' OR '{company}' = '')
					AND (`payroll_id` = '{payroll}' OR '{payroll}' = 'ALL' OR '{payroll}' = '')
				";
			}
			$art = new ApproverRelatedGroup;
			$gargSQL = $art->getApproverReleatedGroupSQL("Workgroup", "companyMainData", false, "'{date}'", "AND", $filters["date"]);

			$workgroup = new Workgroup;
			$workgroupCriteria = new CDbCriteria();
			$workgroupCriteria->alias = $workgroup->tableName();
			$workgroupCriteria->condition = "
					(`workgroup_id` = '{workgroup}' OR '{workgroup}' = 'ALL' OR '{workgroup}' = '')
				AND ('{date}' = '' OR ('{date}' BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->end}')))
				AND `status` = {$this->pub}
				{$companyPayrollFilter}
				{$gargSQL["where"]}";
			$workgroupCriteria->order = "`workgroup_name`";
			$this->LAGridDB->setModelSelection($workgroup, $workgroupCriteria);

			if (($this->showId || $this->user == '6acd9683761b153750db382c1c3694f6') && $this->freezeColumns) {
				parent::setGridProperty("splitColumnEnabled", true);
				parent::setGridProperty("splitColumn", 3);
			} elseif ($this->freezeColumns) {
				parent::setGridProperty("splitColumnEnabled", true);
				parent::setGridProperty("splitColumn", 2);
			}
		}
		parent::setExportFileName(Dict::getValue("export_file_workgroup"));
		parent::setDialogMaxWidth(6);
		parent::G2BInit();
	}

	/**
	 * Kereső értékek átadása sessionnek az oszlopok felépítéséhez
	 * @return void
	 */
	public function actionSetInitProperties()
	{
		Yang::setSessionValue('workgroup_filters', requestParam('searchInput'));

		parent::actionSetInitProperties();
	}

	public function search()
	{
		$dateOnChange = "";
		$companyPayrollSearchParameters = [];
		if ($this->useCompanyAndPayrollRights) {
			$art = new ApproverRelatedGroup;
			$gargCompanySQL = $art->getApproverReleatedGroupSQL("Company", "companyMainData", false, "'{date}'", "AND", "allDate");
			$gargPayrollSQL = $art->getApproverReleatedGroupSQL("Payroll", "companyMainData", false, "'{date}'", "AND", "allDate");

			$company = new Company();
			$companyCriteria = $company->getColumnGridCriteria($gargCompanySQL['where'], "{date}");

			$payroll = new Payroll;
			$payrollCriteria = $payroll->getColumnGridCriteria($gargPayrollSQL['where'], "{date}");
			$payrollCriteria->condition .= "
			AND ({$payrollCriteria->alias}.company_id = '{company}' OR {$payrollCriteria->alias}.company_id = 'ALL' OR '{company}' like 'ALL')";

			$companyPayrollSearchParameters =
			[
				'company'		=>
				[
					'label_text'=> Dict::getValue("company_id"),
					'col_type'	=> 'combo',
					'options'	=>
					[
						'mode'					=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
						'modelSelectionModel'	=> $company,
						'modelSelectionCriteria'=> $companyCriteria,
						'comboId'				=> 'company_id',
						'comboValue'			=> 'company_name',
						'array'	=> "",
					],
					'onchange'  => ['payroll'],
				],
				'payroll'		=>
				[
					'label_text'=> Dict::getValue("payroll_id"),
					'col_type'	=> 'combo',
					'options'	=>
					[
						'mode'					=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
						'modelSelectionModel'	=> $payroll,
						'modelSelectionCriteria'=> $payrollCriteria,
						'comboId'				=> 'payroll_id',
						'comboValue'			=> 'payroll_name',
						'array'					=> [["id"=>"ALL","value"=>Dict::getValue("all")]],
					],
					'default_value' => 'ALL',
				],

			];

			$dateOnChange = ["company","payroll"];
		}
		$art = new ApproverRelatedGroup;
		$gargSQL = $art->getApproverReleatedGroupSQL("Workgroup", "companyMainData", false, "'{date}'");

		$workgroup = new Workgroup;
		$workgroupCriteria = new CDbCriteria();
		$workgroupCriteria->alias = $workgroup->tableName();
		$workgroupFilterByCompanyIdPayrollId = "";
		if ($this->useCompanyAndPayrollRights) {
			$workgroupFilterByCompanyIdPayrollId = "
				AND ({$workgroupCriteria->alias}.company_id = '{company}' OR {$workgroupCriteria->alias}.company_id = 'ALL' OR '{company}' like 'ALL')
				AND ({$workgroupCriteria->alias}.payroll_id = '{payroll}' OR {$workgroupCriteria->alias}.payroll_id = 'ALL' OR  '{payroll}' like 'ALL')";
		}
		$workgroupCriteria->condition = "
				('{date}' = '' OR ('{date}' BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->end}')))
			{$workgroupFilterByCompanyIdPayrollId}
			AND (`workgroup_name` LIKE '%%{search}%%')
			AND `status` = {$this->pub}
			{$gargSQL["where"]}";
		$workgroupCriteria->order = "`workgroup_name`";


		return Yang::arrayMerge(
			[
				'date' =>
				[
					'col_type'      => 'ed',
					'dPicker'       => true,
					'width'         => '*',
					'label_text'    => Dict::getValue("valid_from"),
					'default_value' => date('Y-m-d'),
					'onchange'      => $dateOnChange
				],
			],
			$companyPayrollSearchParameters,
			[
				'workgroup' =>
				[
					'col_type'	=>'auto',
					'width'		=>'*',
					'label_text'=>Dict::getValue("workgroup_name"),
					'options'	=>
					[
						'mode'					=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
						'modelSelectionModel'	=> $workgroup,
						'modelSelectionCriteria'=> $workgroupCriteria,
						'comboId'				=> 'workgroup_id',
						'comboValue'			=> 'workgroup_name',
						'array'					=> array(array("id"=>"ALL","value"=>Dict::getValue("all"))),
					],
				],
				'submit'		=> array('col_type'=>'searchBarReinitGrid', 'width'=>'*', 'label_text'=>''),
			]
		);
	}

	/**
	 * @return array customized grid columns (name=>params)
	 */
	public function columns()
	{
		$lang = Dict::getLang();
		$filters = Yang::session('workgroup_filters',[]);
		if (!isset($filters["date"])) { return []; }

		$art = new ApproverRelatedGroup;
		$gargCompanySQL = $art->getApproverReleatedGroupSQL("Company", "companyMainData", false, "'{$filters["date"]}'", "AND", $filters["date"]);
		$gargPayrollSQL = $art->getApproverReleatedGroupSQL("Payroll", "companyMainData", false, "'{$filters["date"]}'", "AND", $filters["date"]);

		$company = new Company();
		$companyCriteriaGrid = $company->getColumnGridCriteria($gargCompanySQL['where'], $filters["date"]);
		$companyCriteriaDialog = $company->getColumnDialogCriteria($gargCompanySQL['where'], 'valid_from', 'valid_to');


		$payroll = new Payroll;
		$payrollCriteriaGrid = $payroll->getColumnGridCriteria($gargPayrollSQL['where'], $filters["date"]);
		$payrollCriteriaDialog = $payroll->getColumnDialogCriteria($gargPayrollSQL['where'], 'valid_from', 'valid_to');
		$payrollCriteriaDialog->condition .= " AND (`company_id` = '{company_id}' OR 'ALL' = '{company_id}' OR `company_id` = 'ALL')";

		$timtComboSql = "
			SELECT `app_lookup`.`lookup_value` AS id, `dictionary`.`dict_value` AS value
			FROM `app_lookup`
			JOIN `dictionary` ON `dictionary`.`dict_id` = `app_lookup`.`dict_id`
			WHERE 1
				AND `lookup_id`='time_interval_min_type'
				AND `dictionary`.`lang` = '$lang'
				AND `app_lookup`.`valid` = 1";

		$acs = [
			['id'=> 1,'value' => Dict::getValue("yes")],
			['id'=> 0,'value' => Dict::getValue("no")]
		];

		$id = ($this->showId || $this->user == '6acd9683761b153750db382c1c3694f6') ?
				['workgroup_id'	=> ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'edit'=>false],]:
				[];

		$column = [
			'workgroup_name'		=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed', 'width'=>'300'],
			'company_id'			=>
			[
				'export'	=> true,
				'grid'		=> true,
				'window'	=> true,
				'col_type'	=> 'combo',
				'options'   =>
				[
					'mode'                              => Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'               => $company,
					'modelSelectionCriteriaGridMode'    => $companyCriteriaGrid,
					'modelSelectionCriteriaDialogMode'  => $companyCriteriaDialog,
					'comboId'                           => 'company_id',
					'comboValue'                        => 'company_name',
					'array' => (!$this->useCompanyAndPayrollRights)?[["id" => "ALL", "value" => Dict::getValue("all")]]:"",
				],
				'width' 	=> "200",
				'onchange'  =>  ["payroll_id"],
			],
			'payroll_id'			=>
			[
				'export'	=> true,
				'grid'		=> true,
				'window'	=> true,
				'col_type'	=> 'combo',
				'options'	=>
				[
					'mode'					            => Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'	            => $payroll,
					'modelSelectionCriteriaGridMode'    => $payrollCriteriaGrid,
					'modelSelectionCriteriaDialogMode'  => $payrollCriteriaDialog,
					'comboId'				            => 'payroll_id',
					'comboValue'			            => 'payroll_name',
					'array' => (!$this->useCompanyAndPayrollRights)?[["id" => "ALL", "value" => Dict::getValue("all")]]:"",
				],
				'width'     => "150",
			],
			'minimal_group_count'   => ['export' => false, 'grid'=>false ,'window'=>false ,'col_type'=>'ed', 'width'=>'300', 'default_value'=>'0', ],
			'work_start'			=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed', 'tPicker'=>true, 'align'=>'center', 'width'=>'100'],
			'work_end'				=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed', 'tPicker'=>true, 'align'=>'center', 'width'=>'100'],
			'lunch_start'			=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed', 'tPicker'=>true, 'align'=>'center', 'width'=>'100'],
			'lunch_end'				=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed', 'tPicker'=>true, 'align'=>'center', 'width'=>'100'],
			'lunch_time'			=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed', 'tPicker'=>true, 'align'=>'center', 'width'=>'100'],
			'work_type'				=> [
												'export'	=> true,
												'grid'		=> true,
												'window'	=> true,
												'col_type'	=> 'combo',
												'options'	=>	[
																	'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																	'sql'	=> "SELECT `app_lookup`.`lookup_value` AS id, `dictionary`.`dict_value` AS value "
																				. " FROM `app_lookup`"
																				. " JOIN `dictionary`"
																				. " ON `dictionary`.`dict_id` = `app_lookup`.`dict_id`"
																				. " WHERE `lookup_id`='work_type' AND `dictionary`.`lang` = '$lang'",
												],
												'width' => "150",
										],
			'use_acs'					=> ['export'=>true ,'grid'=>true ,'window'=>true , 'col_type'=>'combo', 'align'=>'center', 'width' => '100',
													'options' => ['mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,'array'=>$acs]],
			'work_order'				=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed', 'align'=>'center', 'width'=>'100'],
			'worktime_interval_min'		=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed', 'tPicker'=>true, 'align'=>'center', 'width'=>'200'],
			'overtime_interval_min'		=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed', 'tPicker'=>true, 'align'=>'center', 'width'=>'200'],
			'cost_time_interval_min'	=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed', 'tPicker'=>true, 'align'=>'center', 'width'=>'200'],
			'balance_min'				=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed', 'tPicker'=>true, 'align'=>'center', 'width'=>'300'],
		];

		if ($this->balanceMax) {
			$column = Yang::arrayMerge($column,
				[
					'balance_max'			=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed', 'tPicker'=>true, 'align'=>'center', 'width'=>'300'],
				]);
		}
		$column = Yang::arrayMerge($column,
			[
				'balance_step'			=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed', 'tPicker'=>true, 'align'=>'center', 'width'=>'300'],
			]);

		if($this->workgroupShowRoundTypes){
			foreach(["worktime_interval_min_type", "overtime_interval_min_type", "balance_interval_min_type"] as $colName){
				$wtCol =
				[
					$colName => [
						'export'	=> true ,
						'grid'		=> true ,
						'window'	=> true ,
						'col_type'	=> 'combo',
						'options'	=> [
								'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
								'sql'	=> $timtComboSql,
						],
						'width'		=> '100'
					],
				];
				$column = Yang::arrayMerge($column, $wtCol);
			}
		}

		if ($this->balanceRoundTolerance)
		{
			$column = Yang::arrayMerge($column,
			[
				'balance_round_tolerance'	=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed', 'pmtPicker'=>true, 'align'=>'center', 'width'=>'200'],
			]);
		}

		//DEV-13562
		if($this->workgroupFiLoFilter) {
			$column['filter_only_filo_regs'] = [
				'export'    => true,
				'grid'      => true,
				'window'    => true,
				'col_type'  => 'combo',
				'width'     => "300",
				'options'   => [
					'mode'  => Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'   => "SELECT
									`lookup_value` as id,
									`dict_value` as value
								FROM
									`app_lookup`
								LEFT JOIN `dictionary` ON
										`app_lookup`.`dict_id`   = `dictionary`.`dict_id`
									AND `dictionary`.`valid`     = 1
									AND `dictionary`.`lang`      = '$lang'
								WHERE
									`app_lookup`.`valid`         = 1
									AND `app_lookup`.`lookup_id` = 'filter_only_filo_regs'
								ORDER BY
									`dict_value` ASC"
				]
			];
		}

		$column = Yang::arrayMerge($column,
			[

				'entry_exit_tolerance' 	=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed', 'pmtPicker'=>true, 'align'=>'center', 'width'=>'200'],
				'outside_breaktime'		=> [
													'export'	=> true,
													'grid'		=> true,
													'window'	=> true,
													'col_type'	=> 'combo',
													'options'	=>	[
																		'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																		'sql'	=> "SELECT `app_lookup`.`lookup_value` AS id, `dictionary`.`dict_value` AS value "
																					. " FROM `app_lookup`"
																					. " JOIN `dictionary`"
																					. " ON `dictionary`.`dict_id` = `app_lookup`.`dict_id`"
																					. " WHERE `lookup_id`='outside_breaktime' AND `dictionary`.`lang` = '$lang'",
													],
													'width' => "150",
												],
				'monitor_break' 		=> ['export'=>false ,'grid'=>false ,'window'=>false ,'col_type'=>'ed', 'tPicker'=>true, 'align'=>'center', 'width'=>'100'],
				'worktime_frame' 		=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed', 'align'=>'center', 'width'=>'120'],
				'worktime_frame_type' 	=> [
													'export'	=>true ,
													'grid'		=>true ,
													'window'	=>true ,
													'col_type'	=>'combo',
													'options'	=>	[
																		'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																		'sql'	=> "SELECT `app_lookup`.`lookup_value` AS id, `dictionary`.`dict_value` AS value "
																					. " FROM `app_lookup`"
																					. " JOIN `dictionary`"
																					. " ON `dictionary`.`dict_id` = `app_lookup`.`dict_id`"
																					. " WHERE `lookup_id`='worktime_frame_type' AND `dictionary`.`lang` = '$lang'",
													],
													'width'		=>'120'
												],
				'worktime_frame_begin' 	=> ['export'=>true ,'grid'=>true ,'window'=>true, 'col_type'=>'ed','dPicker'=>true, 'align'=>'center', 'width'=>'120'],
				'daily_worktime'		=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed', 'align'=>'center', 'width'=>'80'],
				'is_framework'  		=> ['col_type' => 'combo', 'grid' => true, 'window' => true, 'width' => '110', 'align' => 'center', 'export' => true, 'options' => ['mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY, 'array'=>$acs]],
			]
		);

		if ($this->showChickenEntitled)
		{
			$column['chicken_pack_entitled'] =
				['grid'=>true, 'window'=>true , 'col_type'=>'combo', 'width'=>'175',
					'options' => [
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> "
							SELECT
								`lookup_value` as id,
								`dict_value` as value
							FROM
								`app_lookup`
							LEFT JOIN `dictionary` ON
									`app_lookup`.`dict_id`	= `dictionary`.`dict_id`
								AND `dictionary`.`valid`	= 1
								AND `dictionary`.`lang`		= '$lang'
							WHERE
									`app_lookup`.`valid`		= 1
								AND `app_lookup`.`lookup_id`	= 'yes_no'
							ORDER BY
								`dict_value` ASC",
					],
				];
		}

		if($this->specialFrameDaysGroup == 'workgroup')
		{
			// fel kell venni az app_lookup értékeket, ha majd használni kell (sinia keretegyenleg kezelés miatt 1558)
			$column['special_frameday_option']	= [
				'grid'		=> true,
				'window'	=> true ,
				'col_type'	=> 'combo',
				'width'		=> '300',
				'options'	=> [
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> "SELECT
										`lookup_value` as id,
										`dict_value` as value
									FROM
										`app_lookup`
									LEFT JOIN `dictionary` ON
											`app_lookup`.`dict_id` 	= `dictionary`.`dict_id`
										AND `dictionary`.`valid`	= 1
										AND `dictionary`.`lang`		= '" . Dict::getLang() . "'
									WHERE
											`app_lookup`.`valid`		= 1
										AND `app_lookup`.`lookup_id`	= 'yes_no'
									ORDER BY
										`dict_value` ASC",
					],
			];
		}

		$shiftStartInWorkgroup = ($this->summarySheetCalculationShiftStartInWorkgroup)?\true:\false;
		if ($shiftStartInWorkgroup)
		{
			$shiftStartType = new ShiftStartType;
			$c = new CDbCriteria();
			$c->condition = "`status`= " . $this->pub;
			$c->order = "`shift_start_type_name`";

			$column['shift_start_type_id']	=
			[
				'export'	=> true,
				'grid'		=> true,
				'window'	=> true,
				'col_type'	=> 'combo',
				'width'		=> "150",
				'options'	=>	array(
									'mode'					=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
									'modelSelectionModel'	=> $shiftStartType,
									'modelSelectionCriteria'=> $c,
									'comboId'				=> 'shift_start_type_id',
									'comboValue'			=> 'shift_start_type_name',
									'array'					=> [["id"=>"ALL","value"=>Dict::getValue("all")]],
								),
			];
		}

		$sundaySignInterval = ($this->summarySheetCalculationSundaySign)?\true:\false;
		if($sundaySignInterval)
		{
			$column['sunday_sign_interval']	=
			[
				'export'	=> true,
				'grid'		=> true,
				'window'	=> true,
				'col_type'	=> 'combo',
				'width'		=> "150",
				'options'	=>
				[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "SELECT
									`lookup_value` as id,
									`dict_value` as value
								FROM
									`app_lookup`
								LEFT JOIN `dictionary` ON
										`app_lookup`.`dict_id`	= `dictionary`.`dict_id`
									AND `dictionary`.`valid`	= 1
									AND `dictionary`.`lang`		= '$lang'
								WHERE
										`app_lookup`.`valid`		= 1
									AND `app_lookup`.`lookup_id`	= 'sunday_sign_interval'
								ORDER BY
									`app_lookup`.`row_id` ASC",
				],
			];
		}

		$paidLunchtimeReduction = ($this->summarySheetPaidLunchtimeReduction)?\true:\false;
		if($paidLunchtimeReduction)
		{
			$column['paid_lunchtime_reduction']	=
			[
				'export'	=> true,
				'grid'		=> true,
				'window'	=> true,
				'col_type'	=> 'combo',
				'width'		=> "150",
				'options'	=>
				[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "SELECT
									`lookup_value` as id,
									`dict_value` as value
								FROM
									`app_lookup`
								LEFT JOIN `dictionary` ON
										`app_lookup`.`dict_id` 	= `dictionary`.`dict_id`
									AND `dictionary`.`valid`	= 1
									AND `dictionary`.`lang`		= '" . Dict::getLang() . "'
								WHERE
										`app_lookup`.`valid`		= 1
									AND `app_lookup`.`lookup_id`	= 'true_false'
								ORDER BY
									`dict_value` ASC",
				],
			];
		}

        $dailyWorkingTimeConsolidation = ($this->dailyWorkingTimeConsolidation)?\true:\false;
		if($dailyWorkingTimeConsolidation)
		{
			$column['daily_working_time_consolidation']	=
			[
				'export'	=> true,
				'grid'		=> true,
				'window'	=> true,
				'col_type'	=> 'combo',
				'width'		=> "150",
				'options'	=>
				[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> "
							SELECT
								`lookup_value` as id,
								`dict_value` as value
							FROM
								`app_lookup`
							LEFT JOIN `dictionary` ON
									`app_lookup`.`dict_id`	= `dictionary`.`dict_id`
								AND `dictionary`.`valid`	= 1
								AND `dictionary`.`lang`		= '$lang'
							WHERE
									`app_lookup`.`valid`		= 1
								AND `app_lookup`.`lookup_id`	= 'yes_no'
							ORDER BY
								`dict_value` ASC",
				],
			];
		}
		//DEV-14457
		if ($this->isWorkgroupAllowanceShown) {
			$column = Yang::arrayMerge($column,
				[
					'allowance'  		=> ['col_type' => 'combo', 'grid' => true, 'window' => true, 'width' => '110', 'align' => 'center', 'export' => true, 'options' => ['mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY, 'array'=>$acs]],
				]
			);
		}

		'szokásos oszlopok a végére';{
			$column = Yang::arrayMerge($column,
				[
					'valid_from'			=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed','dPicker'=>true,
													'align'=>'center', 'width'=>'150', 'onchange'=>['unit_id', 'company_id', 'payroll_id']],
					'valid_to'				=> ['export'=>true ,'grid'=>true ,'window'=>true ,'col_type'=>'ed','dPicker'=>true,
													'align'=>'center', 'width'=>'150', 'onchange'=>['unit_id', 'company_id', 'payroll_id']],
					'note'					=> ['export'=>true ,'grid'=>true,'window'=>true ,'col_type'=>'ed', 'width'=>'300'],
					'status'				=> ['export'=>false,'grid'=>false,'window'=>false,'col_type'=>'combo',
													'options'=>['comboModel'=>'Status','comboId'=>'row_id','comboValue'=>'name'],
													'align'=>'center', 'width'=>'100'],
					'created_by'			=> ['export'=>false,'grid'=>false,'window'=>false,'col_type'=>'combo',
													'options'=>['comboModel'=>'User','comboId'=>'user_id','comboValue'=>'username'],
													'align'=>'center', 'width'=>'100'],
					'created_on'			=> ['export'=>false,'grid'=>false,'window'=>false,'col_type'=>'ed','dPicker'=>true, 'align'=>'center', 'width'=>'100'],
				]
			);
		}
		if ($this->usedGroup == 'workgroup' || strpos( $this->heatmapGroups, 'workgroup') !== false) {
			$column["minimal_group_count"]['grid'] = true;
			$column["minimal_group_count"]['window'] = true;
			$column["minimal_group_count"]['export'] = true;
		}
		return Yang::arrayMerge($id,$column);
	}

	protected function getStatusButtons($gridID = null)
	{
		$buttons = [];

		// Initialize multi edit
		// onclick 2nd parameter: db tablename
		// onclick 3rd paramter: db identifier column
		if($gridID === "dhtmlxGrid")
		{
			$buttons["multiEditDialog"] =
			[
				"type"		=> "button",
				"id"		=> "openModDialog",
				"class"		=> "openModDialog",
				"name"		=> "openModDialog",
				"img"		=> "/images/status_icons/st_multimod.png",
				"label"		=> Dict::getValue("multi_edit"),
				"onclick"	=> "G2BMagicianDialog('" . Dict::getValue("multi_edit") . "', 'workgroup', 'workgroup_id');",
			];
		}

		return Yang::arrayMerge($buttons,parent::getStatusButtons($gridID));
	}

	/**
	 * Initialize multi edit dialog
	 * @return json
	 */
	public function actionGetMultiEditHtml()
	{
		$retArr = [];
		// g2b style
		$retArr["html"] = '<div class="g2b dialog"><div class="content">';
		$cols = $this->columns();
		if (isset($cols["shift_start_type_id"])) {
			unset($cols["shift_start_type_id"]);
		}
		if (isset($cols["sunday_sign_interval"]) && Yang::getParam('customerDbPatchName') != "masterg") {
			unset($cols["sunday_sign_interval"]);
		}
		unset($cols['company_id']); //ha a keresőben modelt használunk és ez nincs itt, akkor session összeomlik
		unset($cols['payroll_id']); // de hogy miért

		// No valid_from & valid_to
		$colKeys = array_keys($cols);
		if (($key = array_search("valid_from", $colKeys)) !== false) {
			unset($colKeys[$key]);
		}
		if (($key = array_search("valid_to", $colKeys)) !== false) {
			unset($colKeys[$key]);
		}

		// selector default
		$hiddenInputs = '';
		$selectChooseOptions = '<option value="default" selected>' . Dict::getValue("please_select") . '</option>';
		// loop through all other editable fields
		foreach ($colKeys as $inputs)
		{
			$hiddenInputs .= '<div class="formElement ' . $inputs . '" style="display:none; margin-top:10px; margin-bottom:10px;">';
			$hiddenInputs .= Dict::getValue($inputs) . '<br>';
			switch ($cols[$inputs]["col_type"])
			{
				// simple text / timepicker
				case "ed":
					$edInput = '<input class ="" id="' . $inputs . '" name="' . $inputs . '" type="text" value="" style="">';

					$timeFormat = ($this->wfmUsedSeconds === '0') ? "hh:mm" : "hh:mm:ss";
					$showSecond = ($this->wfmUsedSeconds === '0') ? "false" : "true";

					$timeFormat = (isset($cols[$inputs]['showSecond']) AND $cols[$inputs]['showSecond'] === true) ? "hh:mm:ss" : "hh:mm";
					$defValue = (isset($cols[$inputs]['showSecond']) AND $cols[$inputs]['showSecond'] === true) ? "00:00:00" : "00:00";
					$showSecond = (isset($cols[$inputs]['showSecond']) AND $cols[$inputs]['showSecond'] === true) ? "true" : "false";

					if (isset($cols[$inputs]['tPicker']) && $cols[$inputs]['tPicker'] === true)
					{
						$edInput = '<input class ="" id="' . $inputs . '" name="' . $inputs . '" type="text" value="' . $defValue . '" style="">';
						$hiddenInputs .= '
						<script type="text/javascript">
								$("input#' . $inputs . '").timepicker({
									timeOnlyTitle: "' . mb_strtolower(Dict::getModuleValue("ttwa-wfm", "time_selection")) . '",
									timeText: "' . mb_strtolower(Dict::getModuleValue("ttwa-base", "time")) . '",
									hourText: "' . mb_strtolower(Dict::getModuleValue("ttwa-wfm", "hour")) . '",
									minuteText: "' . mb_strtolower(Dict::getModuleValue("ttwa-base", "minute")) . '",
									secondText: "sec",
									currentText: "' . mb_strtolower(Dict::getModuleValue("ttwa-base", "now")) . '",
									closeText: "' . mb_strtolower(Dict::getModuleValue("ttwa-base", "done")) . '",
									timeFormat:"' . $timeFormat . '",
									showSecond:' . $showSecond . '
								});
						</script>';
					}
					$hiddenInputs .= $edInput;
				break;
				// combo SQL / Array
				case "combo":
					if (isset($cols[$inputs]['options']['mode']))
					{
						if ($cols[$inputs]['options']['mode'] == Grid2Controller::G2BC_QUERY_MODE_SQL)
						{
							$defaultArray = $cols[$inputs]['options']['array'] ?? [];

							$optionSql = str_replace(
								['{valid_from}', '{ec_valid_from}'],
								date('Y-m-d'),
								$cols[$inputs]['options']['sql']
							);

							$selectOptions = dbFetchAll($optionSql);

							$selectOptions = array_merge($defaultArray, $selectOptions);

						} else if ($cols[$inputs]['options']['mode'] == Grid2Controller::G2BC_QUERY_MODE_ARRAY){
							$selectOptions = $cols[$inputs]['options']['array'];
						}
						$hiddenInputs .= '<select id="'.$inputs.'" name="'.$inputs.'" data-error="" class="g2b styled-select" style="margin-bottom:10px;">';
						foreach ($selectOptions as $option) {
							$hiddenInputs .= '<option value="' . $option["id"].'">' . $option["value"] . '</option>';
						}
						$hiddenInputs .= '</select>';
					}
				break;
			}

			$hiddenInputs .= '</div>';

			// fill selector options
			$selectChooseOptions .= '<option value="' . $inputs . '">' . Dict::getValue($inputs) . '</option>';
		}

		// column picker
		$retArr["html"] .= '
			<select id="chooseColumn" name="chooseColumn" data-error="" class="g2b styled-select" style="display:none; margin-bottom:10px;">
				' . $selectChooseOptions . '
			</select>
		';
		// column remover
		$retArr["html"] .= '
			<select id="removeColumn" name="removeColumn" data-error="" class="g2b styled-select" style="display:none; margin-bottom:10px;">
			</select>
		';

		// Save with new historical data
		$retArr["html"] .= Dict::getValue("save_with_new_history") . "<br>";
		$retArr["html"] .= '
			<select id="saveWithHistory" name="saveWithHistory" data-error="" class="g2b styled-select" style="margin-bottom:10px;">
				<option value="0" selected="">' . Dict::getValue("no") . '</option>
				<option value="1">' . Dict::getValue("yes") . '</option>
			</select>
		';
		// Hided elements
		// valid date
		$retArr["html"] .= '<div class="formElement validDate" style="display:none; margin-top:10px; margin-bottom:10px;">';
		$retArr["html"] .= Dict::getValue("valid_from") . "<br>";
		$retArr["html"] .= '<input class="" id="validFrom" name="validFrom" type="text" value="" style=""><br>';
		$retArr["html"] .= Dict::getValue("valid_to") . "<br>";
		$retArr["html"] .= '<input class="" id="validTo" name="validTo" type="text" value="" style="">';
		$retArr["html"] .= '</div>';

		// column val inputs
		$retArr["html"] .= $hiddenInputs;

		// additional js like datepicker
		$retArr["html"] .= '
			<script type="text/javascript">
				$("input#validFrom").datepicker({dateFormat:"yy-mm-dd",
					qwshowWeek: false
				});
				$("#validFrom").focusin(function() {
					$(this).css({"background-color":"#FFEE58"});
				});
				$("#validFrom").focusout(function() {
					$(this).css({"background-color":""});
				});
				$("input#validTo").datepicker({dateFormat:"yy-mm-dd",
					qwshowWeek: false
				});
				$("#validTo").focusin(function() {
					$(this).css({"background-color":"#FFEE58"});
				});
				$("#validTo").focusout(function() {
					$(this).css({"background-color":""});
				});
				$("#chooseColumn").change(function() {
					var selectedOption = $("#chooseColumn").val();
					if (selectedOption != "default") {
						$("select#chooseColumn option[value=" + selectedOption + "]").attr("disabled", true);
						$("div." + selectedOption).css("display", "block");
						$("#" + selectedOption).addClass("to-serialize");
						$("#" + selectedOption).addClass("added-to-dialog");
						$("#chooseColumn").css("display", "none");
					}
				});
				$("#removeColumn").change(function() {
					var selectedOption = $("#removeColumn").val();
					if (selectedOption != "default") {
						$("select#chooseColumn option[value=" + selectedOption + "]").attr("disabled", false);
						$("div." + selectedOption).css("display", "none");
						$("#" + selectedOption).removeClass("to-serialize");
						$("#" + selectedOption).removeClass("added-to-dialog");
						$("#removeColumn").css("display", "none");
					}
				});
				$("#saveWithHistory").change(function() {
					var selectedOption = $("#saveWithHistory").val();
					if (selectedOption == "1") {
						$("div.validDate").css("display", "block");
						$("input#validFrom").addClass("to-serialize");
						$("input#validTo").addClass("to-serialize");
					} else if (selectedOption == "0") {
						$("div.validDate").css("display", "none");
						$("input#validFrom").removeClass("to-serialize");
						$("input#validTo").removeClass("to-serialize");
					}
				});
			</script>
		';

		// g2b buttons
		$retArr["html"] .= '
			<div class="buttons"><div class="buttonContent default" style="float:right;">
				<input class="addmodDialogHistoryNewItem" style="margin-left:5px;" onclick="G2BShowNewInputElement();" name="yt0" type="button" value="">
				<input class="addmodDialogHistoryEditItem" style="margin-left:5px;" onclick="G2BMagicianDialogSave();" name="yt1" type="button" value="">
				<input class="addmodDialogDelItem" style="margin-left:5px;" onclick="G2BHideNewInputElement();" name="yt2" type="button" value="">
			</div><div class="arcContent"></div></div>';

		// end g2b style
		$retArr["html"] .= '</div></div>';

		echo json_encode($retArr, JSON_HEX_QUOT | JSON_HEX_TAG | JSON_INVALID_UTF8_SUBSTITUTE);
	}

	/**
	 * Check valid dates in case of historical multi edit save
	 * @return json
	 */
	public function actionSaveMultiEditCheckDates()
	{
		// params
		$validFrom = trim(dbQuoteValue(requestParam('valid_from')), "'");
		$validTo = trim(dbQuoteValue(requestParam('valid_to')), "'");
		$table = trim(dbQuoteValue(requestParam('table')), "'");
		$tableId = trim(dbQuoteValue(requestParam('tableId')), "'");

		// default return
		$retArr = ["status" => 1, "confirmMsg" => "", "errorMsg" => "", "rowIds" => [], "futureRowIds" => []];

		$validFrom = new DateTime($validFrom);
		$validTo = new DateTime($validTo);

		// check if validfrom > valid_to --> return error
		if ($validFrom > $validTo) {
			$retArr["status"] = 0;
			$retArr["errorMsg"] = Dict::getValue("error_intervall");
			echo json_encode($retArr); exit;
		} else {
			// Get all active rows, if identifier column set select that too
			$sqlPart = ($tableId != "") ? ", `" . $tableId . "` " : "";
			$SQL = "SELECT `row_id`, `valid_from`, `valid_to` " . $sqlPart .  "FROM `" . $table . "` WHERE `status` = " . $this->pub . " AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '" . $this->end . "')";

			$currentRecords = dbFetchAll($SQL);

			$rowIds = [];
			$futureRecordsRowIds = [];
			$tableIds = [];
			if (count($currentRecords) > 0)
			{
				// store row_ids
				// check if active record valid_from > given valid_from + 1 day && given valid_from >  current valid_to
				// --> should give error
				$dateError = false;
				foreach ($currentRecords as $record)
				{
					$rowIds[] = $record["row_id"];
					($tableId != "") ? $tableIds[] = $record[$tableId] : "";
					$recVTo = new DateTime($record["valid_to"]);
					$recVFrom = new DateTime($record["valid_from"]);
					$recVFrom->add(new DateInterval('P1D'));
					if ($validFrom < $recVFrom || $validFrom > $recVTo) {
						$dateError = true;
					}
				}

				$retArr["rowIds"] = $rowIds;

				if ($dateError) {
					$retArr["status"] = 0;
					$retArr["errorMsg"] = Dict::getValue("vfrom_not_between_actualvalids");
					echo json_encode($retArr); exit;
				}

				// store row_ids with same identifiers with valid_from starting in future
				if ($tableId != "")
				{
					$SQL = "SELECT `row_id` FROM `" . $table . "` WHERE `" . $tableId . "` IN ('" . implode("','", $tableIds) . "') AND `status` = " . $this->pub . " AND `valid_from` > CURDATE()";

					$futureRecords = dbFetchAll($SQL);
					if (count($futureRecords) > 0) {
						foreach ($futureRecords as $rec) {
							$futureRecordsRowIds[] = $rec["row_id"];
						}
						$retArr["status"] = 2;
						$retArr["confirmMsg"] = Dict::getValue("are_you_sure_to_override");
						$retArr["futureRowIds"] = $futureRecordsRowIds;
					}
				}
			}
		}
		echo json_encode($retArr);
	}

	/**
	 * Save multi edit inputs
	 * @return json
	 */
	public function actionSaveMultiEdit()
	{
		// default return
		$retArray["title"] = Dict::getValue("successful_save");
		$retArray["msg"] = Dict::getValue("database_upgrade_success");

		// params
		$inputFields = requestParam('inputFields');
		$inputs = json_decode(str_replace('&#34;', '"', $inputFields), TRUE);
		$tableName = trim(dbQuoteValue($inputs["tableToSave"]), "'");
		$historical = trim(dbQuoteValue(requestParam('historical')), "'");
		// unset unnecesarry fields for insert string
		unset($inputs["tableToSave"]);
		unset($inputs["tableId"]);

		$count = count($inputs);

		// not historical save (no valid_from & valid_to given)
		if ($count > 0 && !$historical)
		{
			$updateSET = "UPDATE " . $tableName . " SET ";
			$counter = 0;

			$modelName = str_replace('_', '', ucwords($tableName, '_'));

			$model = new $modelName;

			// populate update sql
			foreach ($inputs as $name => $val)
			{
				if (strpos($val, ':') && $model->getMetaData()->columns[$name]->type !== "string") {
					$val = timeToSeconds($val);
				}

				$safeVal = dbQuoteValue($val);
				$safeName = trim(dbQuoteValue($name), "'");

				$counter++;
				if ($counter < $count) {
					$updateSET .= $safeName . " = " . $safeVal . ", ";
				} else {
					$updateSET .= $safeName . " = " . $safeVal . ", modified_by = '" . userID() . "', modified_on = '" . date("Y-m-d H:i:s") . "'
						WHERE `status` = " . $this->pub . " AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->end}')";
				}
			}

			dbExecute($updateSET);
		} else if ($count > 0 && $historical)
		{
			// historical save (valid_from & valid_to given)

			// row_ids & future row_ids (with same identifier)
			$rowIds = requestParam('rowIds');
			$futureRowIds = requestParam('futureRowIds');
			$SQL = "SELECT * FROM `" . $tableName . "` WHERE `row_id` IN (" . implode(",", $rowIds) . ")";
			$res = dbFetchAll($SQL);

			// unset row_id for insert
			$keys = array_keys($res[0]);
			if (($key = array_search("row_id", $keys)) !== false) {
				unset($keys[$key]);
			}

			$insertSQL = "INSERT INTO `" . $tableName . "` VALUES (NULL,";
			// populate insert sql
			foreach ($res as $records)
			{
				$keycounter = 0;
				$countKey = count($keys);
				foreach ($keys as $key) {
					$keycounter++;

					// check for last column
					// modifiy values
					if ($key == "created_by") {
						$insertSQL .= "'" . userID() . "',";
					} else if ($key == "valid_from") {
						$insertSQL .= dbQuoteValue($inputs["validFrom"]) . ",";
					} else if ($key == "created_on" && $keycounter < $countKey) {
						$insertSQL .= "'" . date("Y-m-d H:i:s") . "',";
					} else if ($key == "created_on") {
						$insertSQL .= "'" . date("Y-m-d H:i:s") . "'),(NULL,";
					} else if ($key == "valid_to" && $keycounter < $countKey) {
						$insertSQL .= dbQuoteValue($inputs["validTo"]) . ",";
					} else if ($key == "valid_to") {
						$insertSQL .= dbQuoteValue($inputs["validTo"]) . "),(NULL,";
					} else if ($keycounter < $countKey) {
						if (isset($inputs[$key])) {
							$insertSQL .= dbQuoteValue($inputs[$key]) . ",";
						} else {
							if ($records[$key] === NULL) {
								$insertSQL .= "NULL,";
							} else {
								$insertSQL .= "'" . $records[$key] . "',";
							}
						}
					} else {
						if (isset($inputs[$key])) {
							$insertSQL .= dbQuoteValue($inputs[$key]) . "),(NULL,";
						} else {
							if ($records[$key] === NULL) {
								$insertSQL .= "NULL),(NULL,";
							} else {
								$insertSQL .= "'" . $records[$key] . "'),(NULL,";
							}
						}
					}
				}
			}
			// no more rows in multi insert CUT new start & replace with ;
			dbExecute(substr($insertSQL, 0, -7) . ";");

			// UPDATE korábbi row_idk valid_to = megadott valid_from -1 nap.
			$newValidTo = new DateTime(trim(dbQuoteValue($inputs["validFrom"]), "'"));
			$newValidTo->modify("-1 day");
			$updateSQL = "UPDATE " . $tableName . " SET `valid_to` = '" . $newValidTo->format("Y-m-d") . "', `modified_by` = '" . userID() . "', `modified_on` = '" . date("Y-m-d H:i:s") . "' WHERE `row_id` IN (" . implode(",", $rowIds) . ")";
			dbExecute($updateSQL);

			// UPDATE futurerowids status 7
			if ($futureRowIds != "") {
				$updateSQL = "UPDATE " . $tableName . " SET `status` = " . $this->del . ", `modified_by` = '" . userID() . "', `modified_on` = '" . date("Y-m-d H:i:s") . "' WHERE `row_id` IN (" . implode(",", $futureRowIds) . ")";
				dbExecute($updateSQL);
			}
		}

		echo json_encode($retArray);
	}
}
?>
