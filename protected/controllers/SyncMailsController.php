<?php

class SyncMailsController extends Grid2HistoryController
{
   private $isRoot;

    public function __construct()
	{
		parent::__construct("syncMails");
        $this->isRoot = App::isRootSessionEnabled();
       
	}

    protected function G2BInit()
	{
        $this->LAGridDB->setModelName("SyncMails");

		parent::setControllerPageTitleId("page_title_sync_mails");
		
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("paging",			true);
        if($this->isRoot) {
            $this->LAGridRights->overrideInitRights("add",			true);
        }
        $this->LAGridRights->overrideInitRights("modify",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);

		$this->LAGridDB->enableSQLMode();

        $SQL = "
			SELECT
				*
			FROM
			sync_mails
            WHERE
            status = " . Status::PUBLISHED . "
			";

        $this->LAGridDB->setSQLSelection($SQL, "row_id");
        $this->enableLAGrid();
		parent::G2BInit();
	}

	public function columns()
	{
        $colums = [
            'email_title' => ['col_type'=>'ed', 'width'=>'250'],
            'email_addresses' => ['col_type'=>'ed', 'width'=>'250'],
            'email_text' => ['col_type'=>'hidden', 'width'=>'500', 'disabled' => 'disabled'],
            
        ];
        if($this->isRoot) {
            $colums['sync_id'] = ['col_type'=>'ed', 'width'=>'250'];
        }
        return $colums;
	}

    	/**
	 * Oszlop feliratok a Grid2-ben
	 * @return array
	 */
	public function attributeLabels()
	{
        return array(
            'row_id' => Dict::getValue('id'),
            'sync_id' => Dict::getValue('sync_id'),
            'email_title' => Dict::getValue('email_title'),
            'email_addresses' => Dict::getValue('email_addresses_comma'),
            'email_text' => Dict::getValue('email_text'),
            'status' => Dict::getValue('status'),
            'created_by' => Dict::getValue('created_by'),
            'created_on' => Dict::getValue('created_on'),
            'modified_by' => Dict::getValue('modified_by'),
            'modified_on' => Dict::getValue('modified_on'),
        );
    }

} 