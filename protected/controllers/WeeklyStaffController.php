<?php

Yang::import('application.components.PayrollReportCalcAPI.*');

class WeeklyStaffController extends Grid2Controller{

	private $payroll;
	private $cost;
	private $criteria;

	public function __construct(){
		parent::__construct("weeklyStaff");
	}

	protected function G2BInit() {
		
		$this->LAGridDB->setModelName("WeeklyStaff");

		parent::setControllerPageTitleId("page_title_weekly_staff_report");
		
		parent::setGridProperty("splitColumnEnabled", true,	 "dhtmlxGrid");
		parent::setGridProperty("splitColumn", 2,	 "dhtmlxGrid");
		
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);

		$this->LAGridDB->enableSQLMode();
		parent::G2BInit();
		
	}
	
	protected function beforeFetchGriData($gridID, $filter) { 
		$gpf = new GetPreDefinedFilter($this->getControllerID(), false, array('company' => "employee", 'payroll' => "employee",));
		$SQLfilter = $gpf->getFilter();
		$SQLfilter = App::replaceSQLFilter($SQLfilter, $filter);

		$conn = $this->LAGridDB->getConnection();
		$report = new EmployeeCalcData($conn, $filter["valid_date"], $filter["valid_date"], $SQLfilter);
		$report->frameMode = false;
		$report->createEmployeeDailyCalcDataTables();
		
	}

	protected function setSQL($filter, $gridID, $forReport = false) {
			
		$employee_contract	= $filter['employee_contract']		!= '' ? " AND ecii = '{$filter['employee_contract']}' " : '';
		$payroll			= $filter['payroll']				!= 'ALL' ? " AND p.payroll_id = '{$filter['payroll']}' " : '';
	//	$cost_center_id		= $filter['cost_center_id']			!= 'ALL' ? " AND cc.cost_center_id = '{$filter['cost_center_id']}' " : '';
		$cost				= '';
		if (isset($filter['cost']))
		{
			if (is_array($filter['cost'])) {
				if (!in_array('ALL', $filter['cost'])) {
					$cost = " AND co.cost_id IN ('" . implode("', '", $filter['cost']) . "') ";
				}
			} else {
				$cost = $filter['cost'] != 'ALL' ? " AND cost_name LIKE '{$filter['cost']}' " : '';
			}
		}
		$leader				= $filter['leader']					!= 'ALL' ? " AND vezeto LIKE '{$filter['leader']}' " : '';
//		$allomany			= $filter['allomany']				!= 'ALL' ? " AND ext.option2 = '{$filter['allomany']}' " : '';
		$cog3				= $filter['cog3']					!= 'ALL' ? " AND allomany_csoport LIKE '{$filter['cog3']}' " : '';
		$munkaterulet		= $filter['munkaterulet']			!= 'ALL' ? " AND munkaterulet LIKE '{$filter['munkaterulet']}' " : '';
		$munkakor			= $filter['employee_position_id']	!= 'ALL' ? " AND epi LIKE '{$filter['employee_position_id']}' " : '';
		$hfm				= $filter['hfm']					!= 'ALL' ? " AND hfm LIKE '{$filter['hfm']}' " : '';
		$headcount			= $filter['headcount']				!= 'ALL' ? " AND headcount LIKE '{$filter['headcount']}' " : '';
		$unit				= '';
		if (isset($filter['unit'])) {
			if (is_array($filter['unit'])) {
				if (!in_array('ALL', $filter['unit'])) {
					$unit 	= " AND unit.unit_id IN ('" . implode("', '", $filter['unit']) . "') ";
				}
			} else {
				$unit 		= $filter['unit'] != 'ALL' ? " AND unit_name LIKE '{$filter['unit']}' " : '';
			}
		}

		$from = "'{$filter["valid_date"]}'";
		$to = "'{$filter["valid_date"]}'";
		
		$statusPublished = Status::PUBLISHED;
		$defaultEnd = App::getSetting("defaultEnd");
	
		$SQL = /** @lang MySQL */

				"SELECT
					MD5(CONCAT(RAND(), employee_id)) as row_unique,
				  	epi AS employee_position_id ,
					week_start AS datum,
					year,
					week_number ,
					week_start,
					week_end,
					employee_id,
					employee_name,
					co.company_id,
					p.payroll_id,
				    '1' AS hc,
					company_name AS company_name2,
					p.payroll_name AS company_name,
					cost_center_name,
					co.cost_name,
					ep.employee_position_name,
					munkaterulet,
				    allomany_csoport,
					vezeto,
					allomany_kod,
					allomany_csoport,
					headcount,
					hfm,
					emp_id,
				  	ecii,
					concat(week_number, 'W') AS het,
					enter_date,
					enter_week,
					exit_date,
					exit_week

				FROM
					(
					SELECT
						ext.option6 AS munkaterulet,
						emp.company_org_group3_id AS allomany_csoport,
						ext.option7 AS vezeto,
						ext.option2 AS allomany_kod,
						ext.option8 AS headcount,
						ext.option9 AS hfm,
						company_name,
						employee_position_id AS epi,
						substring(week_firstday , 1, 4) AS year,
						week_starting_monday AS week_number,
						week_firstday AS week_start,
						week_lastday AS week_end,
						employee_name,
						ec.employee_contract_id,
				  		ec.employee_contract_id as ecii,
						payroll_id,
						unit_id,
						emp.employee_id,
						ec.row_id AS ecrowid,
						emp_id,
						ec_valid_from AS enter_date,
						DATE_FORMAT(ec_valid_from, '%yW%v') AS enter_week,
						ec_valid_to AS exit_date,
						DATE_FORMAT(ec_valid_to, '%yW%v') AS exit_week
					FROM
						(
						SELECT
							employee.payroll_id ,
							CONCAT(employee.last_name, ' ', employee.first_name) AS employee_name,
							employee.valid_from ,
							employee.valid_to ,
							employee.status,
							employee.company_id,
							employee.employee_id,
							employee.emp_id,
							employee.company_org_group3_id,
							employee.unit_id
						FROM
							employee
						WHERE
							((employee.valid_from <= {$from} AND employee.valid_to >= {$from}) OR (employee.valid_from <= {$to} AND employee.valid_to >= {$to}) OR (employee.valid_from >= {$from} AND employee.valid_to <= {$to}) OR (employee.valid_from <= {$from} AND employee.valid_to >= {$to}))
							AND employee.status = $statusPublished
						) emp
					LEFT JOIN (
						SELECT
							min(date) AS week_firstday,
							max(date) AS week_lastday,
							week_starting_monday
						FROM
							calendar
						GROUP BY
							year, month, week_starting_monday
							) calendar on
						((calendar.week_firstday <= {$from} AND week_lastday >= {$from}) OR (calendar.week_firstday <= {$to} AND week_lastday >= {$to}) OR (calendar.week_firstday >= {$from} AND week_lastday <= {$to}) OR (calendar.week_firstday <= {$from} AND week_lastday >= {$to}) )
					LEFT JOIN company ON
						(company.company_id = emp.company_id
							AND company.status = $statusPublished)
					LEFT JOIN employee_ext ext ON
						(ext.employee_id = emp.employee_id
							AND
				        ((ext.valid_from <= {$from} AND ext.valid_to >= {$from}) OR (ext.valid_from <= {$to} AND ext.valid_to >= {$to}) OR (ext.valid_from >= {$from} AND ext.valid_to <= {$to}) OR (ext.valid_from <= {$from} AND ext.valid_to >= {$to}) )
							AND ext.status = $statusPublished)
					LEFT JOIN employee_contract ec ON
						ec.employee_id = emp.employee_id
						AND
				        ((ec.valid_from <= {$from} AND ec.valid_to >= {$from}) OR (ec.valid_from <= {$to} AND ec.valid_to >= {$to}) OR (ec.valid_from >= {$from} AND ec.valid_to <= {$to}) OR (ec.valid_from <= {$from} AND ec.valid_to >= {$to}) )
						AND
				        ((ec.ec_valid_from <= {$from} AND ec.ec_valid_to >= {$from}) OR (ec.ec_valid_from <= {$to} AND ec.ec_valid_to >= {$to}) OR (ec.ec_valid_from >= {$from} AND ec.ec_valid_to <= {$to}) OR (ec.ec_valid_from <= {$from} AND ec.ec_valid_to >= {$to}) )
						AND ec.`status` = $statusPublished
						";
						if (Yang::customerDbPatchName() == 'carrier') {
							$SQL .= "WHERE company.company_id = 'CAR'";
						}
				$SQL .= 
				") temp1
				LEFT JOIN employee_position ep ON
					ep.employee_position_id = temp1.epi
					AND
				        ((ep.valid_from <= {$from} AND ep.valid_to >= {$from}) OR (ep.valid_from <= {$to} AND ep.valid_to >= {$to}) OR (ep.valid_from >= {$from} AND ep.valid_to <= {$to}) OR (ep.valid_from <= {$from} AND ep.valid_to >= {$to}) )
					AND ep.status = $statusPublished
				LEFT JOIN employee_cost ecost ON
					ecost.employee_contract_id = temp1.employee_contract_id
					AND
				        ((ecost.valid_from <= {$from} AND ecost.valid_to >= {$from}) OR (ecost.valid_from <= {$to} AND ecost.valid_to >= {$to}) OR (ecost.valid_from >= {$from} AND ecost.valid_to <= {$to}) OR (ecost.valid_from <= {$from} AND ecost.valid_to >= {$to}) )
					AND ecost.`status` = $statusPublished
				LEFT JOIN cost_center cc ON
					cc.cost_center_id = ecost.cost_center_id
					AND
				        ((cc.valid_from <= {$from} AND cc.valid_to >= {$from}) OR (cc.valid_from <= {$to} AND cc.valid_to >= {$to}) OR (cc.valid_from >= {$from} AND cc.valid_to <= {$to}) OR (cc.valid_from <= {$from} AND cc.valid_to >= {$to}) )
					AND cc.`status` = $statusPublished
				LEFT JOIN payroll p ON
					p.payroll_id = temp1.payroll_id
					AND
				        ((p.valid_from <= {$from} AND p.valid_to >= {$from}) OR (p.valid_from <= {$to} AND p.valid_to >= {$to}) OR (p.valid_from >= {$from} AND p.valid_to <= {$to}) OR (p.valid_from <= {$from} AND p.valid_to >= {$to}) )
					AND p.`status` = $statusPublished
				LEFT JOIN cost co ON
					co.cost_id = ecost.cost_id
					AND
				        ((co.valid_from <= {$from} AND co.valid_to >= {$from}) OR (co.valid_from <= {$to} AND co.valid_to >= {$to}) OR (co.valid_from >= {$from} AND co.valid_to <= {$to}) OR (co.valid_from <= {$from} AND co.valid_to >= {$to}) )
					AND co.`status` = $statusPublished
				";
				if(EmployeeGroupConfig::isActiveGroup('unit_id'))
				{
					$SQL.=EmployeeGroup::getLeftJoinSQL("unit_id");
				}
			$SQL .= "
				LEFT JOIN `unit` ON
						unit.`status`=".Status::PUBLISHED."
					AND unit.`unit_id`=".EmployeeGroup::getActiveGroupSQL("unit_id","temp1")."
					AND unit.`valid_from` <= {$to} 
					AND {$from} <= IFNULL(unit.`valid_to`, '".App::getSetting("defaultEnd")."')

				WHERE
					true
					{$payroll}
					{$cost}
					{$leader}
					{$munkaterulet}
					{$munkakor}
					{$hfm}
					{$headcount}
					{$employee_contract}					
					{$cog3}
					{$unit}
					AND ecrowid is not null
				/*	AND p.row_id is not null */
				order by
					lower(employee_name),
					week_number";
			
		$SQL = preg_replace('/(\b[a-z]+\b\.{1})*\bvalid_to\s*([>|<])=\s*\'/m', 'IFNULL($1valid_to,\''.$defaultEnd.'\')$2= \'', $SQL) ;
		
		$this->LAGridDB->setSQLSelection($SQL,'row_unique');

		return $SQL;
		
	}
	
	
	public function search(){
		
		$filter = requestParam('searchInput');
		$customer = Yang::getParam('customerDbPatchName');
		
		$ret = $this->getPreDefinedSearchFromDb("workForce", false);	
		$leader = [
				'col_type'			=> 'combo',
				'multiple'			=> false,
				'options'			=>	[
											'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
											'sql'	=> "SELECT DISTINCT eext.option7 as id, eext.option7 as value
														FROM employee_ext eext
														WHERE eext.option7 IS NOT NULL
														AND eext.option7 != ''
														;",
											'array'	=> ['options' => ["id"=>"ALL","value"=>Dict::getValue("all")]],
										],
				'label_text'		=> Dict::getValue("workplace_leader"),
				'onchange'			=> array('employee_contract'),
				'default_value'		=> "ALL"
		];

		$cog3 =  [
				'col_type'			=> 'combo',
				'multiple'			=> false,
				'options'			=>	[
											'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
											'sql'	=> "SELECT company_org_group_id as id, company_org_group_name as value 
														FROM company_org_group3
														WHERE company_org_group_id IS NOT NULL
														AND company_org_group_id != ''
														AND company_org_group_name IS NOT NULL
														AND company_org_group_name != ''
														;",
											'array'	=> ['options' => ["id"=>"ALL","value"=>Dict::getValue("all")]],
										],
				'label_text'		=> Dict::getValue("company_org_group3"),
				'onchange'			=> array(''),
				'default_value'		=> "ALL"
		];

		$munkaterulet = [
				'col_type'			=> 'combo',
				'multiple'			=> false,
				'options'			=>	[
											'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
											'sql'	=> "SELECT DISTINCT eext.option6 as id, eext.option6 as value
														FROM employee_ext eext
														WHERE eext.option6 IS NOT NULL
														AND eext.option6 != ''
														;",
											'array'	=> ['options' => ["id"=>"ALL","value"=>Dict::getValue("all")]],
										],
				'label_text'		=> Dict::getValue("option6"),
				'onchange'			=> array('employee_contract'),
				'default_value'		=> "ALL"
		];
		
		$employee_position_id = [
				'col_type'			=> 'combo',
				'multiple'			=> false,
				'options'			=>	[
											'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
											'sql'	=> "SELECT ep.employee_position_id as id, ep.employee_position_name as value 
														FROM employee_position ep
														WHERE ep.employee_position_id IS NOT NULL
														AND ep.employee_position_id != ''
														AND ep.employee_position_name IS NOT NULL
														AND ep.employee_position_name != ''
														;",
											'array'	=> ['options' => ["id"=>"ALL","value"=>Dict::getValue("all")]],
										],
				'label_text'		=> Dict::getValue("employee_position_name"),
				'onchange'			=> array(''),
				'default_value'		=> "ALL"
		];
		
		$hfm = [
				'col_type'			=> 'combo',
				'multiple'			=> false,
				'options'			=>	[
											'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
											'sql'	=> "SELECT DISTINCT eext.option9 as id, eext.option9 as value
														FROM employee_ext eext
														WHERE eext.option9 IS NOT NULL
														AND eext.option9 != ''
														AND eext.valid_from <= DATE_FORMAT('".$filter["valid_date"]."', '%Y-%m-%d') AND eext.valid_to >= DATE_FORMAT('".$filter["valid_date"]."', '%Y-%m-%d')
														;",
											'array'	=> ['options' => ["id"=>"ALL","value"=>Dict::getValue("all")]],
										],
				'label_text'		=> Dict::getValue("option9"),
				'onchange'			=> array(''),
				'default_value'		=> "ALL"
		];
		
		
		$headcount = [
				'col_type'			=> 'combo',
				'multiple'			=> false,
				'options'			=>	[
											'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
											'sql'	=> "SELECT DISTINCT eext.option8 as id, eext.option8 as value
														FROM employee_ext eext
														WHERE eext.option8 IS NOT NULL
														AND eext.option8 != ''
														;",
											'array'	=> ['options' => ["id"=>"ALL","value"=>Dict::getValue("all")]],
										],
				'label_text'		=> Dict::getValue("option8"),
				'onchange'			=> array(''),
				'default_value'		=> "ALL"
		];
	
				
		$cost = [
				'col_type'			=> 'combo',
				'multiple'			=>  $customer == 'carrier',
				'options'			=>	[
											'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
											'sql'	=> "SELECT DISTINCT c.cost_id as id, c.cost_name as value
														FROM cost c
														WHERE c.cost_name IS NOT NULL
														AND c.cost_name != ''
														AND c.cost_id IS NOT NULL
														AND c.cost_id != ''
														;",
											'array'	=> ['options' => ["id"=>"ALL","value"=>Dict::getValue("all")]],
										],
				'label_text'		=> Dict::getValue("cost"),
				'onchange'			=> array(''),
				'default_value'		=> "ALL"
		];
		
		$ret['valid_date']['onchange'][]	= 'cost_center_id';
		$ret['valid_date']['onchange'][]	= 'hfm';
		
		unset($ret['company']);
		unset($ret['workgroup']);
		if ($customer !== 'carrier') {
			unset($ret['unit']);
		} else {
			$cost['class'] = 'customSelect2Class';
		}
		
		$return = array_slice($ret, 0, -2) 
					+ ['leader' => $leader]
					+ ['cog3' => $cog3] 
					+ ['munkaterulet' => $munkaterulet] 
					+ ['employee_position_id' => $employee_position_id] 
					+ ['hfm' => $hfm] 
					+ ['headcount' => $headcount]
					+ ['cost' => $cost]
				+ array_slice($ret, -2);
		
		return $return;
		
	}

	
	public function columns(){
		
		return [
			
			// Dolgozó azonosítója
			'emp_id'					=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100',],
			
			//Dolgozó neve
			'employee_name'				=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150',],
			
			// Költséghely
			'cost_name'					=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150',],
			
			// állomány
			//'allomany'					=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '120',],
			
			// munkaterület
			'munkaterulet'				=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '120',],
			
			// munkakor
			'employee_position_name'	=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '120',],
			
			// HC
			'hc'						=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '120',],
			
			// állomany csoport
			'allomany_csoport'			=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100',],
				
			// Vezeő
			'vezeto'					=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150',],
			
			// állomany kód
			'allomany_kod'				=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150',],
			
			// Headcount
			'headcount'					=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '120',],
			
			// HFM
			'hfm'						=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '120',],
			
			// CÉG
			'company_name'				=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150',],
	
			// Belépés dátuma
			'enter_date'				=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150',],
			
			// Belépés hete
			'enter_week'				=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100',],
			
			// Kilépés dátuma
			'exit_date'					=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150',],		


			// Kilépés hete
			'exit_week'					=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100']
		];

	}
	
	public function attributeLabels(){
		
		return [
			
			'emp_id'					=> Dict::getValue("emp_id"),
			'employee_name'				=> Dict::getValue("employee_name"),
			'cost_name'					=> Dict::getValue("cost"),
			//'allomany'					=> 'Állomány', 
			'munkaterulet'				=> Dict::getValue("option6"), 
			'employee_position_name'	=> Dict::getValue("employee_position_name"),
			'hc'						=> Dict::getValue("unit_hc"),
			'allomany_csoport'			=> Dict::getValue("company_org_group3"), 
			'vezeto'					=> Dict::getValue("workplace_leader"),
			'allomany_kod'				=> Dict::getValue("option2"),
			'headcount'					=> Dict::getValue("option8"),
			'hfm'						=> Dict::getValue("option9"),
			'company_name'				=> Dict::getValue("company"),
			'enter_date'				=> Dict::getValue("valid_from_date"), 
			'enter_week'				=> Dict::getValue("valid_from_week"),
			'exit_date'					=> Dict::getValue("valid_to_date"),
			'exit_week'					=> Dict::getValue("valid_to_week"),
			
		];
		
	}
}
?>