<?php

class ReportWorkgroupsOfEmployeeController extends Grid2Controller
{
	public function __construct()
	{
		parent::__construct("reportWorkgroupsOfEmployee");
	}

	protected function G2BInit()
    {
		parent::setControllerPageTitleId("page_title_report_workgroups_of_employee");

		$this->LAGridRights->overrideInitRights("paging",				true);
		$this->LAGridRights->overrideInitRights("search",				true);
		$this->LAGridRights->overrideInitRights("search_header",		true);
		$this->LAGridRights->overrideInitRights("select",				true);
		$this->LAGridRights->overrideInitRights("multi_select",			false);
		$this->LAGridRights->overrideInitRights("column_move",			true);
		$this->LAGridRights->overrideInitRights("reload_sortings",		true);
		$this->LAGridRights->overrideInitRights("details",				false);
		$this->LAGridRights->overrideInitRights("init_open_search",		true);
		$this->LAGridRights->overrideInitRights("export_xlsx",			true);
		$this->LAGridRights->overrideInitRights("export_pdf_node",		false);

		$this->LAGridDB->enableSQLMode();

		parent::setGridProperty("splitColumnEnabled", true);
		parent::setGridProperty("splitColumn", 1);

		parent::G2BInit();
    }

	protected function setSQL($filter, $gridID, $forReport = false)
    {
		$gpf=new GetPreDefinedFilter($this->getControllerID(),\FALSE,array('company'=> "employee",'payroll'=> "employee"));
		$SQLfilter=$gpf->getFilter();

		$SQLfilter=App::replaceSQLFilter($SQLfilter,$filter);

		$AllData=new GetActiveEmployeeAllData($filter["valid_date"],$SQLfilter, "NONE", FALSE, "AND", "", TRUE, false, $this->getControllerID());
		$table=$AllData->getTableName();

		$workgroupActive=EmployeeGroupConfig::isActiveGroup("workgroup_id");

		$first_col = Employee::getParam('fullname_with_emp_id_ec_id',["all_data","all_data"])." AS fullname,";
		if	(App::getSetting("reportWorkGroupOfEmployeeChangeCols") > 0)
		{
			$first_col = ' CONCAT(all_data.last_name, \' \', all_data.first_name) as fullname, all_data.emp_id, ';
		}

		$SQL="
			SELECT
				".$first_col."
				all_data.`tax_number`,
				c.`company_name`,
				u.`unit_name`,
				wg.`workgroup_name`,
				company_org_group1.`company_org_group_name` company_org_group1_name,
				company_org_group2.`company_org_group_name` company_org_group2_name,
				company_org_group3.`company_org_group_name` company_org_group3_name,
			";
			if($workgroupActive)
			{
				$SQL.=" eg_wg.`valid_from`,
				IFNULL(eg_wg.`valid_to`, '".App::getSetting("defaultEnd")."') as valid_to
				";
			}
			else
			{
				$SQL.=" all_data.`employee_contract_valid_from` as valid_from,
				IFNULL(all_data.`employee_contract_valid_to`, '".App::getSetting("defaultEnd")."') as valid_to
				";
			}
			$SQL.="FROM $table all_data
			LEFT JOIN company_org_group1 ON
					company_org_group1.`company_org_group_id` = all_data.`company_org_group1_id`
				AND company_org_group1.`status` = ".Status::PUBLISHED."
				AND '{valid_date}' BETWEEN company_org_group1.`valid_from` AND company_org_group1.`valid_to`
			LEFT JOIN company_org_group2 ON
					company_org_group2.`company_org_group_id` = all_data.`company_org_group2_id`
				AND company_org_group2.`status` = ".Status::PUBLISHED."
				AND '{valid_date}' BETWEEN company_org_group2.`valid_from` AND company_org_group2.`valid_to`
			LEFT JOIN company_org_group3 ON
					company_org_group3.`company_org_group_id` = all_data.`company_org_group3_id`
				AND company_org_group3.`status` = ".Status::PUBLISHED."
				AND '{valid_date}' BETWEEN company_org_group3.`valid_from` AND company_org_group3.`valid_to`
			LEFT JOIN `company` c ON
					c.`company_id`=all_data.`company_id`
				AND c.`status` = ".Status::PUBLISHED."
				AND '{valid_date}' BETWEEN c.`valid_from` AND IFNULL(c.`valid_to`, '".App::getSetting("defaultEnd")."')
			LEFT JOIN `unit` u ON
					u.`unit_id`=all_data.`unit_id`
				AND u.`status`=".Status::PUBLISHED."
				AND '{valid_date}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '".App::getSetting("defaultEnd")."')
			LEFT JOIN `workgroup` wg ON
					wg.`workgroup_id` = all_data.`workgroup_id`
				AND wg.`status`=".Status::PUBLISHED."
				AND '{valid_date}' BETWEEN wg.`valid_from` AND IFNULL(wg.`valid_to`, '".App::getSetting("defaultEnd")."')
			";
			if($workgroupActive)
			{
				$SQL.="LEFT JOIN `employee_group` eg_wg ON
						eg_wg.`employee_contract_id`=all_data.`employee_contract_id`
					AND eg_wg.`group_id`='workgroup_id'
					AND eg_wg.`status`=".Status::PUBLISHED."
                    AND '{valid_date}' BETWEEN eg_wg.`valid_from` AND IFNULL(eg_wg.`valid_to`, '".App::getSetting("defaultEnd")."')
			";
			}
		$SQL.="
			ORDER BY fullname
		";

		return $SQL;
	}

	public function search()
    {
		return $this->getPreDefinedSearchFromDb();
	}

	public function columns()
    {
		$retArr = array(
			'fullname'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 300),
			'company_org_group2_name'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'tax_number'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 100),
			'company_name'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'unit_name'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'workgroup_name'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'valid_from'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 110),
			'valid_to'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 110),
		);

		$retArr = $this->columnRights($retArr);

		$customersName = App::getSetting("ptr_customer");

		if ($customersName == "BOS") {
			unset($retArr['tax_number']);
			$retArr = array_slice($retArr, 0, 1) + ["emp_id" => array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 150) ] + array_slice($retArr, 1, count($retArr)-1);
		}

		return $retArr;
	}

	public function attributeLabels()
    {
		return array(
			'fullname'			=> Dict::getValue("name"),
			'emp_id'			=> Dict::getValue("emp_id"),
			'company_org_group2_name' => Dict::getValue("company_org_group2"),
			'tax_number'		=> Dict::getValue("tax_number"),
			'company_name'		=> Dict::getModuleValue('ttwa-base','company'),
			'unit_name'			=> Dict::getModuleValue('ttwa-base','unit'),
			'workgroup_name'	=> Dict::getModuleValue('ttwa-base','workgroup'),
			'valid_from'		=> Dict::getValue("valid_from"),
			'valid_to'			=> Dict::getValue("valid_to"),
		);
	}
}

?>