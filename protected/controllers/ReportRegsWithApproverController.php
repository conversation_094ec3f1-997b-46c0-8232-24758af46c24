<?php

/**
 * A controller a ReportRegsController mint<PERSON><PERSON><PERSON><PERSON>; lehetővé teszi a<PERSON>t, hogy az egyes szerepkör-csoportok
 * csak a saját igénylőik regisztrációit lássák.
 *
 * A kimutatásban lévő szűrőmezők konfigurálása a search_filter táblában történik;
 */

class ReportRegsWithApproverController extends Grid2Controller
{
	protected $costMode;
	protected $useApprover;

	public function __construct($right="reportRegsWithApprover")
	{
		$this->useApprover = true;
        parent::__construct($right);
	}

	protected function G2BInit()
	{
			parent::setControllerPageTitleId("page_title_report_registration_with_approver");

            $this->LAGridRights->overrideInitRights("paging",			true);
            $this->LAGridRights->overrideInitRights("search",			true);
            $this->LAGridRights->overrideInitRights("search_header",	true);
            $this->LAGridRights->overrideInitRights("select",			true);
            $this->LAGridRights->overrideInitRights("multi_select",		false);
            $this->LAGridRights->overrideInitRights("column_move",		true);
            $this->LAGridRights->overrideInitRights("reload_sortings",	true);
			$this->LAGridRights->overrideInitRights("col_sorting",		true);
            $this->LAGridRights->overrideInitRights("details",			false);
            $this->LAGridRights->overrideInitRights("init_open_search",	true);
            $this->LAGridRights->overrideInitRights("export_xlsx",		true);
			$this->LAGridRights->overrideInitRights("export_pdf_node",	false,		"dhtmlxGrid");

            $this->costMode = empty(App::getSetting("costMode"))?"cost":App::getSetting("costMode");
            $costModeId		= "cost.`" . $this->costMode . "_id`";
            $costModeName	= "cost.`" . $this->costMode . "_name`";
			$activeSVM = weHaveModule('ttwa-svm');

			$controllersCustomersName = App::getSetting("controllersCustomersName");
			$controllerID = Yang::controllerID();

			if ($controllersCustomersName == "carrier" || $controllersCustomersName == "sanmina" || $controllersCustomersName == "flex") {
				$fullNameActiveSVM = "CONCAT(`employee`.`last_name`, ' ', `employee`.`first_name`)),'') AS full_name,";
				$fullNameSQL = "CONCAT(`employee`.`last_name`, ' ', `employee`.`first_name`),'') AS full_name,";
			} else {
				$fullNameActiveSVM = "CONCAT(`employee`.`last_name`, ' ', `employee`.`first_name`, ' - ', `employee`.`emp_id`)),'') AS full_name,";
				$fullNameSQL = "CONCAT(`employee`.`last_name`, ' ', `employee`.`first_name`, ' - ', `employee`.`emp_id`),'') AS full_name,";
			}

			$gpf=new GetPreDefinedFilter($this->getControllerID(),array("DATE(reg.`time`)"),array('company'=> "employee",'payroll'=> "employee"));
			$SQLfilter=$gpf->getFilter();

			$art = new ApproverRelatedGroup;
			$gargSQL = $art->getApproverReleatedGroupSQL("Employee", "workForce",false,"reg.time","AND","CurrentDate",$this->getControllerID());

            $SQL = "
                    SELECT
                            reg.`row_id`,
                            ";
					if($activeSVM)
					{
						$SQL.="IFNULL(IF(ecard.`card_type` = 3, v.`visitor_name`, ".$fullNameActiveSVM."
							`employee`.`emp_id` as emp_id,
                            ";
					}
					else
					{
						$SQL.="IFNULL(".$fullNameSQL."
								`employee`.`emp_id` as emp_id,
                            ";
					}
					$SQL.=	"reg.`time` AS `time`,
                            reg.`card`,
							CASE ecard.card_type
								WHEN 2 THEN '".Dict::getValue("card_type_temporary")."'
								WHEN 3 THEN '".Dict::getValue("card_type_visitor")."'
								ELSE '".Dict::getValue("card_type_normal")."'
							END AS card_type,
							IFNULL(t.terminal_name,'') AS terminal_name,
                            CONCAT(
									IFNULL(IFNULL(d.dict_value, et.`event_type_name`), reg.`event_type_id`),
									IF(reg.`random_selected` = 1, CONCAT(' - ', '".Dict::getValue('random_selected')."'),'')
																								) as event_type_name,
                            IFNULL(" . $costModeName . ",'') AS cost_id,
							IF(u.`user_id` is null,'".Dict::getValue("system")."','".Dict::getValue("user")."' ) AS recorder_type,
                            IFNULL(IF(u.`user_id` is null,r.`reader_name`,u.`username` ),'') as recorder_name,
							IF(reg.`status` =".Status::DELETED.",'".Dict::getValue("status_deleted")."',IF(reg.`modified_by` is not null,'".Dict::getValue("modified")."','".Dict::getValue("status_active")."')) as status,
							IFNULL(um.`username`,'') as modified_by,
							reg.`note`
                    FROM
                            registration reg
					LEFT JOIN reader r ON
							r.reader_id = reg.reader_id
						AND r.`status` = ".Status::PUBLISHED."
						AND date(reg.`time`) BETWEEN r.`valid_from` AND IFNULL(r.`valid_to`, '".App::getSetting("defaultEnd")."')
                    LEFT JOIN employee_card ecard ON
							reg.`card` = ecard.`card`
						AND ecard.`status` IN ( ".Status::PUBLISHED.", ".Status::STATUS_TEMPORARY_CARD.")
						AND date(reg.`time`) BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`, '".App::getSetting("defaultEnd")."')
                    LEFT JOIN `employee_contract` ON
							ecard.`employee_contract_id` = `employee_contract`.`employee_contract_id`
						AND `employee_contract`.`status` = ".Status::PUBLISHED."
						AND date(reg.`time`) BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '".App::getSetting("defaultEnd")."')
						AND date(reg.`time`) BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '".App::getSetting("defaultEnd")."')
                    LEFT JOIN `employee` ON
							`employee_contract`.`employee_id` = `employee`.`employee_id`
						AND `employee`.`status` = ".Status::PUBLISHED."
						AND date(reg.`time`) BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '".App::getSetting("defaultEnd")."')
					";
				if($activeSVM)
				{
					$SQL.="LEFT JOIN `visitor` v ON
							v.`visitor_id` = ecard.`employee_contract_id`
								AND ecard.`card_type` = 3
									AND v.`status` = ".Status::PUBLISHED."
										AND date(reg.`time`) BETWEEN v.`valid_from` AND IFNULL(v.`valid_to`, '".App::getSetting("defaultEnd")."')
						";
				}
				$SQL.="LEFT JOIN terminal t ON
							reg.terminal_id = t.terminal_id
						AND (reg.reader_id = t.reader_id
							OR ((reg.reader_id IS NULL OR reg.reader_id='') AND (t.reader_id IS NULL OR t.reader_id='')))
						AND t.`status` = ".Status::PUBLISHED."
                    LEFT JOIN event_type et ON
							reg.`event_type_id` = et.`event_type_id`
						AND et.`status` = ".Status::PUBLISHED."
						AND date(reg.`time`) BETWEEN et.`valid_from` AND IFNULL(et.`valid_to`, '".App::getSetting("defaultEnd")."')
					LEFT JOIN dictionary d ON
							d.dict_id = et.dict_id
						AND d.valid = 1
						AND d.module = 'ttwa-wfm'
						AND d.lang = '" . Dict::getLang() . "'";
				if(EmployeeGroupConfig::isActiveGroup('workgroup_id'))
				{
					$SQL.=EmployeeGroup::getLeftJoinSQL('workgroup_id',"employee_contract","date(reg.`time`)");
				}

				if(EmployeeGroupConfig::isActiveGroup('unit_id'))
				{
					$SQL.=EmployeeGroup::getLeftJoinSQL('unit_id',"employee_contract","date(reg.`time`)");
				}
				if(EmployeeGroupConfig::isActiveGroup('company_org_group1_id'))
				{
					$SQL.=EmployeeGroup::getLeftJoinSQL('company_org_group1_id',"employee_contract","date(reg.`time`)");
				}
				if(EmployeeGroupConfig::isActiveGroup('company_org_group2_id'))
				{
					$SQL.=EmployeeGroup::getLeftJoinSQL('company_org_group2_id',"employee_contract","date(reg.`time`)");
				}
				if(EmployeeGroupConfig::isActiveGroup('company_org_group3_id'))
				{
					$SQL.=EmployeeGroup::getLeftJoinSQL('company_org_group3_id',"employee_contract","date(reg.`time`)");
				}
			$SQL .= "LEFT JOIN unit ON
							unit.`unit_id`=".EmployeeGroup::getActiveGroupSQL("unit_id","employee")."
						AND unit.`status` = ".Status::PUBLISHED."
						AND date(reg.`time`) BETWEEN unit.`valid_from` AND IFNULL(unit.`valid_to`, '".App::getSetting("defaultEnd")."')
					LEFT JOIN workgroup ON
							workgroup.`workgroup_id`=".EmployeeGroup::getActiveGroupSQL("workgroup_id","employee_contract")."
						AND workgroup.`status` = ".Status::PUBLISHED."
						AND date(reg.`time`) BETWEEN workgroup.`valid_from` AND IFNULL(workgroup.`valid_to`, '".App::getSetting("defaultEnd")."')
					LEFT JOIN `company_org_group1` ON
							company_org_group1.company_org_group_id = ".EmployeeGroup::getActiveGroupSQL('company_org_group1_id','employee')."
						AND company_org_group1.status=".Status::PUBLISHED."
						AND date(reg.`time`) BETWEEN company_org_group1.`valid_from` AND IFNULL(company_org_group1.`valid_to`, '".App::getSetting("defaultEnd")."')
					LEFT JOIN `company_org_group2` ON
							company_org_group2.company_org_group_id = ".EmployeeGroup::getActiveGroupSQL('company_org_group2_id','employee')."
						AND company_org_group2.status=".Status::PUBLISHED."
						AND date(reg.`time`) BETWEEN company_org_group2.`valid_from` AND IFNULL(company_org_group2.`valid_to`, '".App::getSetting("defaultEnd")."')
					LEFT JOIN `company_org_group3` ON
							company_org_group3.company_org_group_id = ".EmployeeGroup::getActiveGroupSQL('company_org_group3_id','employee')."
						AND company_org_group3.status=".Status::PUBLISHED."
						AND date(reg.`time`) BETWEEN company_org_group3.`valid_from` AND IFNULL(company_org_group3.`valid_to`, '".App::getSetting("defaultEnd")."')
                    LEFT JOIN " . $this->costMode . " cost ON
									reg.`cost_id` = ". $costModeId ."
								AND cost.`status` = ".Status::PUBLISHED."
								AND date(reg.`time`) BETWEEN cost.`valid_from` AND IFNULL(cost.`valid_to`, '".App::getSetting("defaultEnd")."')
					LEFT JOIN `user` u ON
							u.`user_id`=reg.`created_by`
						AND u.`status` = ".Status::PUBLISHED."
						AND date(reg.`time`) BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '".App::getSetting("defaultEnd")."')
					LEFT JOIN `user` um ON
							um.`user_id`=reg.`modified_by`
						AND um.`status` = ".Status::PUBLISHED."
						AND date(reg.`time`) BETWEEN um.`valid_from` AND IFNULL(um.`valid_to`, '".App::getSetting("defaultEnd")."')
					";

			if(isset($gargSQL["join"])){
				$SQL .= $gargSQL["join"];
			}

			$SQL .= "
                    WHERE (reg.`status` = " . Status::PUBLISHED . " or  reg.`status` = " . Status::DELETED . ")
                            AND $SQLfilter
					";
			if (App::hasRight($controllerID, "filterseeshimself")) {
				$SQL .= "AND (1=1 " . $gargSQL["where"];
				if (empty(UserReportRights::checkUserReportRight(userID(), $controllerID))) {
					$SQL .= "
						OR `employee_contract`.`employee_contract_id` = '" . Yang::getUserECID() . "'
					";
				}
				$SQL .= ")";
			}
			else if (App::hasRight($controllerID, "sees_only_himself")) {
				if (empty(UserReportRights::checkUserReportRight(userID(), $controllerID))) {
					$SQL .= "
						AND `employee_contract`.`employee_contract_id` = '" . Yang::getUserECID() . "'";
				}
			}
			else if(isset($gargSQL["where"])) {
				$SQL .= $gargSQL["where"];
			}

			$SQL .= "GROUP BY reg.row_id
					ORDER BY full_name, reg.`time`";
            // $SQL .= "ORDER BY employee.last_name";
            // $SQL .= "ORDER BY employee.last_name, employee.first_name, reg.time";

            $this->LAGridDB->enableSQLMode();
			$this->LAGridDB->setSQLSelection($SQL,'row_id');

			$this->setReportOrientation("landscape");

            parent::setExportFileName(Dict::getValue("export_file_report_registration"));

            parent::G2BInit();

	}

	protected function search() {
		$filter = requestParam('searchInput');
		$ret = $this->getPreDefinedSearchFromDb("workForce", false);
		return $ret;
	}

	public function columns()
	{
            $retArr = array('full_name' => array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '250', 'report_width' => 20,));

			if(App::getSetting("employee_id_in_statement") == 1){
				$eiis = ['emp_id' => ['grid' => true, 'export'=> true, 'report_width' => 7, 'col_type'=>'ed', 'width' => 150] ];
				$retArr = array_merge($retArr, $eiis);
			}

            #todo // itt van a report sok buta oszlopa, itt majd rendet kell tenni

            $y = array(
					'time'				=> array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150', 'report_width' => 20,),
                    'card'				=> array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '100', 'report_width' => 7,),
                    'card_type'			=> array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '100', 'report_width' => 7,),
                    'terminal_name'		=> array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150', 'report_width' => 7,),
                    'event_type_name'	=> array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '200', 'report_width' => 7,),
                    'cost_id'           => array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '150', 'report_width' => 7,),
                    'recorder_type'		=> array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '150', 'report_width' => 7,),
                    'recorder_name'		=> array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '150', 'report_width' => 7,),
					'status'			=> array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '100', 'report_width' => 7,),
					'modified_by'		=> array('grid' => true, 'export' => false, 'col_type' => 'ed', 'align' => 'center', 'width' => '150', 'report_width' => 7,),
                    'note'				=> array('grid' => true, 'export' => false, 'col_type' => 'ed', 'align' => 'center', 'width' => '100', 'report_width' => 7,),
            );


			$retArr = array_merge($retArr, $y);
			$retArr = $this->columnRights($retArr);

			return $retArr;

	}

	public function attributeLabels()
	{
		$costMode_lookup = App::getLookup("costMode",true,1);

		$cosd_id_dict = isset($costMode_lookup[$this->costMode])?$costMode_lookup[$this->costMode]:Dict::getValue("cost_id");

        $ret = array('full_name' => Dict::getValue("employee_name"));



		if(App::getSetting("employee_id_in_statement") == 1){
			$eiis = ['emp_id' => Dict::getValue("emp_id") ];
			$ret = array_merge($ret, $eiis);
		}

		$y = array( 'time'              => Dict::getValue("time"),
                    'card'              => Dict::getValue("card"),
                    'card_type'			=> Dict::getValue("card_type"),
                    'terminal_name'		=> Dict::getValue("reader_id"),
                    'event_type_name'   => Dict::getValue("event"),
                    'cost_id'           => $cosd_id_dict,
                    'recorder_type'     => Dict::getValue("recorder_type"),
					'recorder_name'     => Dict::getValue("recorder_name"),
					'status'			=> Dict::getValue("status"),
					'modified_by'		=> Dict::getValue("modified_by"),
                    'note'              => Dict::getValue("note"),
        );

		$ret = array_merge($ret, $y);

		return $ret;

	}
}
?>
