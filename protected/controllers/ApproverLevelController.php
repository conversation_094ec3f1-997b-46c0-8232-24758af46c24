<?php

class ApproverLevelController extends Grid2Controller
{
	private $type;
	private $table;
	private $processId		= ['absenceApprover', 'workScheduleApprove', 'payrollApprove', 'overtimeApprover'];
	private $processName	= [];
	private $pub			= Status::PUBLISHED;
	private $end;

	public function __construct()
	{
		$this->processName = [
			['id' => 'absenceApprover',		'value' => Dict::getValue("absence_approver_process_id")],
			['id' => 'workScheduleApprove',	'value' => Dict::getValue("work_schedule_approve")],
			['id' => 'payrollApprove',		'value' => Dict::getValue("payroll_transfer_approve")],
			['id' => 'overtimeApprover',	'value' => Dict::getValue("overtime_approve")]
		];
		$this->end = App::getSetting("defaultEnd");

		parent::__construct("approverLevel");
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("ApproverLevel");
		parent::setControllerPageTitleId("page_title_approver_level");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",		true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);

		$this->LAGridRights->overrideInitRights("export_xls",		true);
		$this->LAGridRights->overrideInitRights("export_xlsx",		false);
		$this->LAGridRights->overrideInitRights("export_pdf_node",	false);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);
		$this->LAGridRights->overrideInitRights("inline_modify",		false);

		$this->LAGridDB->enableSQLMode();

		$this->table	= App::getSetting("approver_table");
		$this->type		= ApproverLevel::getTypesData();

		$SQL = "
			SELECT
				al.`row_id`,
				u.`username`,
				" . Employee::getParam('fullname_with_emp_id_ec_id', ["e", "ec"]) . " AS fullname,
				al.`user_id`,
				al.`process_id`,
				al.`group_id`,
				IF(al.`group_id` = 'ALL', '" . Dict::getValue("all_which_have") . "', g.{$this->type[$this->table]['name']}) AS group_name,
				al.`level`,
				al.`note`,
				al.`valid_from`,
				IFNULL(al.`valid_to`, '{$this->end}') AS valid_to
			FROM `approver_level` al
			LEFT JOIN `user` u ON
					u.`user_id` = al.`user_id`
				AND u.`status`= {$this->pub}
				AND '{date}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$this->end}')
			LEFT JOIN `employee` e ON
					e.`employee_id` = u.`employee_id`
				AND e.`status` = {$this->pub}
				AND '{date}' BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '{$this->end}')
			LEFT JOIN `employee_contract` ec ON
					ec.`employee_id` = e.`employee_id`
				AND ec.`status` = {$this->pub}
				AND '{date}' BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '{$this->end}')
				AND '{date}' BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '{$this->end}')
			LEFT JOIN {$this->table} g ON
					g.`{$this->type[$this->table]['id']}` = al.`group_id`
				AND g.`status`= {$this->pub}
				AND '{date}' BETWEEN g.`valid_from` AND IFNULL(g.`valid_to`, '{$this->end}')
			WHERE
					al.`status` = {$this->pub}
				AND '{date}' BETWEEN al.`valid_from` AND IFNULL(al.`valid_to`, '{$this->end}')
				AND (ec.`employee_contract_id` = '{employee_contract}' OR '{employee_contract}' = '' OR '{employee_contract}' = 'ALL')
				AND (al.`process_id` = '{process_id}' OR '{process_id}' = '' OR '{process_id}' = 'ALL')
				AND (al.`group_id` = '{group_id}' OR '{group_id}' = '')
		";

		$this->LAGridDB->setSQLSelection($SQL, "row_id");
		parent::G2BInit();
	}

	public function search()
	{
		return
		[
			'date' =>
			[
				'col_type'		=> 'ed',
				'dPicker'		=> true,
				'width'			=> '*',
				'label_text'	=> Dict::getValue("date"),
				'default_value'	=> date('Y-m-d'),
				'onchange'		=> ['employee_contract']
			],
			'process_id' =>
			[
				'col_type'		=> 'combo',
				'label_text'	=> Dict::getValue("process_id"),
				'default_value'	=> '',
				'onchange'		=> ['employee_contract'],
				'options'		=> ['mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY, 'array' => $this->processName]
			],
			'group_id'	=>
			[
				'col_type'		=> 'combo',
				'options'		=>
				[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							g.`{$this->type[$this->table]['id']}` AS id,
							g.`{$this->type[$this->table]['name']}` AS value
						FROM `{$this->table}` g
						WHERE
								g.`status` = {$this->pub}
						AND	CURDATE() BETWEEN g.`valid_from` AND IFNULL(g.`valid_to`, '{$this->end}')
						UNION
						SELECT 'ALL' AS id, '" . Dict::getValue("all_which_have") . "' AS value
						ORDER BY value
					",
					'array'	=> [["id" => "", "value" => Dict::getValue("all")]]
				],
				'label_text'	=> $this->type[$this->table]['title'],
				'default_value'	=> '',
			],
			'employee_contract'	=>
			[
				'col_type'		=> 'auto',
				'options'		=>
				[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							DISTINCT ec.`employee_contract_id` AS id,
							" . Employee::getParam('fullname_with_emp_id_ec_id', ["e", "ec"]) . " AS value
						FROM `employee` e
						LEFT JOIN `employee_contract` ec ON
								ec.`employee_id` = e.`employee_id`
							AND ec.`status` = {$this->pub}
							AND '{date}' BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '{$this->end}')
							AND '{date}' BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '{$this->end}')
						LEFT JOIN `user` u ON
								u.`employee_id` = e.`employee_id`
							AND u.`status`= {$this->pub}
							AND '{date}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$this->end}')
						LEFT JOIN approver a ON
								a.`approver_user_id` = u.`user_id`
							AND a.`status`= {$this->pub}
							AND
							(
								(a.`process_id` = '{process_id}' AND '{process_id}' <> 'ALL') OR
								(a.`process_id` IN ('" . implode("','", $this->processId) . "') AND '{process_id}' = 'ALL')
							)
							AND	CURDATE() BETWEEN a.`valid_from` AND IFNULL(a.`valid_to`, '{$this->end}')
						WHERE
								ec.`row_id` IS NOT NULL
							AND a.`row_id` IS NOT NULL
							AND	e.`status` = {$this->pub}
							AND '{date}' BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '{$this->end}')
							AND " . Employee::getParam('fullname_with_emp_id_ec_id', ["e", "ec"]) . " LIKE '%%{search}%%'
						ORDER BY value
					",
					'array'	=> [["id" => "", "value" => ""]]
				],
				'label_text'	=> Dict::getValue("name"),
				'default_value'	=> ''
			],
			'submit' => ['col_type' => 'searchBarReloadGrid', 'width' => '*', 'label_text' => '']
		];
	}

	public function columns()
	{
		$maxlevel = App::getSetting("approver_max_level");
		for($i = 1; $i <= $maxlevel; ++$i) {
			$level[] = ['id' => $i,'value' => $i];
		}
		if (App::getSetting("userRightRelatedValAcceptStarEnd") == "1") {
			$starEndingSQL	= " OR IF(RIGHT(a.`related_value`, 1) = '*', g.{$this->type[$this->table]['name']} LIKE CONCAT(LEFT(a.`related_value`, CHAR_LENGTH(a.`related_value`) - 1), '%'), 1=0) ";
		} else {
			$starEndingSQL	= "";
		}

		return
		[
			'username'		=> ['export' => true, 'report_width' => 20, 'col_type' => 'combo', 'width' => 220, 'window' => false],
			'fullname'		=> ['export' => true, 'report_width' => 20, 'col_type' => 'combo', 'width' => 220, 'window' => false],
			'user_id'		=>
			[
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width'			=> 220,
				'grid'			=> false,
				'onchange'		=> ['group_id'],
				'options'		=>
				[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							DISTINCT u.`user_id` AS id,
							u.`username` AS value
						FROM `user` u
						LEFT JOIN `approver` a ON
								a.`approver_user_id` = u.`user_id`
							AND a.`status`= {$this->pub}
							AND a.`process_id` IN ('" . implode("','", $this->processId) . "')
							AND	CURDATE() BETWEEN a.`valid_from` AND IFNULL(a.`valid_to`, '{$this->end}')
						WHERE
								u.`status`= {$this->pub}
							AND a.`row_id` IS NOT NULL
							AND	CURDATE() BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$this->end}')
						ORDER BY value
					",
					'array'	=> [["id"=>"","value"=>""]]
				]
			],
			'process_id'	=>
			[
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width'			=> 220,
				'onchange'		=> ['group_id'],
				'options'		=> ['mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY, 'array' => $this->processName]
			],
			'group_id'		=>
			[
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width'			=> 220,
				'grid'			=> false,
				'options'		=>
				[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							g.`{$this->type[$this->table]['id']}` AS id,
							g.`{$this->type[$this->table]['name']}` AS value
						FROM `{$this->table}` g
						LEFT JOIN `approver` a ON
								a.`related_model` = '{$this->type[$this->table]['model']}'
							AND (a.`related_value` = g.`{$this->type[$this->table]['id']}` OR a.`related_value` = 'ALL' {$starEndingSQL})
							AND a.`process_id` = '{process_id}'
							AND a.`approver_user_id` = '{user_id}'
							AND a.`status` = {$this->pub}
							AND	CURDATE() BETWEEN a.`valid_from` AND IFNULL(a.`valid_to`, '{$this->end}')
						WHERE
								a.`row_id` IS NOT NULL
							AND	g.`status` = {$this->pub}
							AND	CURDATE() BETWEEN g.`valid_from` AND IFNULL(g.`valid_to`, '{$this->end}')
						ORDER BY value
					",
					'array'	=> [["id" => "ALL", "value" => Dict::getValue("all_which_have")]]
				]
			],
			'group_name'	=> ['export' => true, 'report_width' => 20, 'col_type' => 'combo', 'width' => 220, 'window' => false],
			'level'			=>
			[
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width'			=> 100,
				'options'		=> ['mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY, 'array' => $level]
			],
			'valid_from'	=> ['export' => true, 'report_width' => 20, 'col_type' => 'ed', 'dPicker' => true, 'width' => 120],
			'valid_to'		=> ['export' => true, 'report_width' => 20, 'col_type' => 'ed', 'dPicker' => true, 'width' => 120],
			'note'			=> ['export' => true, 'report_width' => 20, 'col_type' => 'ed', 'width' => "*"],
		];
	}

	public function attributeLabels()
	{
		return
		[
			'username'		=> Dict::getValue("user"),
			'fullname'		=> Dict::getValue("name"),
			'user_id'		=> Dict::getValue("user"),
			'user_id'		=> Dict::getValue("user"),
			'process_id'	=> Dict::getValue("process_id"),
			'group_id'		=> $this->type[$this->table]['title'],
			'group_name'	=> $this->type[$this->table]['title'],
			'level'			=> Dict::getValue("level"),
			'valid_from'	=> Dict::getValue("valid_from"),
			'valid_to'		=> Dict::getValue("valid_to"),
			'note'			=> Dict::getValue("note"),
		];
	}
}
?>