<?php #yii2: done

'yii2-only`;

	namespace app\controllers;
	use app\components\API\GetPreDefinedFilter;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2\Grid2Controller;
	use app\components\PayrollReportCalcAPI\EmployeeCalcData;
	use app\models\Status;
	use Yang;

`/yii2-only';


#yii2: done

Yang::import('application.components.PayrollReportCalcAPI.*');

class TimeStatementController extends Grid2Controller{

	private $payroll;
	private $cost;
	private $criteria;

	public function __construct(){
		parent::__construct("timeStatement");
	}

	protected function G2BInit() {
		
		$this->LAGridDB->setModelName("TimeStatement");

		parent::setControllerPageTitleId("page_title_time_statement");
		
		parent::setGridProperty("splitColumnEnabled", true,	 "dhtmlxGrid");
		parent::setGridProperty("splitColumn", 3,	 "dhtmlxGrid");
		
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("init_open_search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("export_xlsx",	true);

		$this->LAGridDB->enableSQLMode();
		parent::G2BInit();
	}
	
	protected function beforeFetchGriData($gridID, $filter) {

		$gpf = new GetPreDefinedFilter($this->getControllerID(),\FALSE,array('company' => "employee", 'payroll' => "employee",));
		$SQLfilter = $gpf->getFilter();
		$SQLfilter = App::replaceSQLFilter($SQLfilter, $filter);

		$conn = $this->LAGridDB->getConnection();
		$report = new EmployeeCalcData($conn, $filter["valid_from"], $filter["valid_to"], $SQLfilter);
		$report->frameMode = false;
		$report->createEmployeeDailyCalcDataTables();
	}

	protected function setSQL($filter, $gridID, $forReport = false) {
					
		$company = $filter['company'] != 'ALL' ? " AND c.company_id = '{$filter['company']}' " : '';
		$payroll = $filter['payroll'] != 'ALL' ? " AND p.payroll_id = '{$filter['payroll']}' " : '';
		$workgroup = $filter['workgroup'] != 'ALL' ? " AND temp_base_calc.workgroup_id = '{$filter['workgroup']}' " : '';
		$unit = $filter['unit'] != 'ALL' ? " AND temp_base_calc.unit_id = '{$filter['unit']}' " : '';
		$employee_contract = $filter['employee_contract'] != '' ? " AND temp_base_calc.employee_contract_id = '{$filter['employee_contract']}' " : '';
		$ext = $filter['moi'] != '' ? " AND ( temp_base_calc.option5 = '{$filter['moi']}' OR '{$filter['moi']}' = 'ALL' OR '{$filter['moi']}' = '' ) " : "";
		
		$SQL = "
				SELECT 
				
				concat(temp_base_calc.emp_id,temp_base_calc.tax_number,calend.timestamp) as row_unique,
				temp_base_calc.emp_id,  
				temp_base_calc.tax_number, 
				CONCAT(temp_base_calc.last_name, ' ', temp_base_calc.first_name) as employee_name, 
				c.company_name as company_name, 
				IF(c.company_name LIKE 'Faurecia E.C.T.%', 'OWN', 'TEMP')as own_temp, 
				temp_base_calc.option1 as division, 
				cost.cost_name, 
				cc.cost_center_name, 
				temp_base_calc.option5 as beosztas,
				temp_base_calc.option4 as berf_cs_kod,

				calend.date as days,

				IF(calend.day_of_week <= 5, 
					ROUND( ( (temp_base_calc.base_worktime_sum / 60 ) / 60 ), 1 ), 
					IFNULL( ROUND( ( ( (temp_base_calc.worktime_sum + (temp_base_calc.otwdu1_sum + temp_base_calc.otwdu2_sum ) ) / 60 ) / 60 ), 1 ), 0) ) as daily_worktime,

				IFNULL( ROUND( ( ( (temp_base_calc.wtej_sum + temp_base_calc.wtdu2_sum) / 60 ) / 60 ), 1 ), 0) as 30_precent_allowance,

				IF(temp_base_calc.date_day_of_week = 1, ROUND( ( ( (temp_base_calc.worktime_sum) / 60 ) / 60 ), 1), 0) as sunday_work_hour, 

				IF(calend.day_of_week <= 5, IFNULL(ROUND( ( ( (temp_base_calc.ot_sum) / 60 ) / 60 ), 1 ), 0), 0 ) as weekday_overtime_allowance,

				IF(calend.day_of_week <= 5, IFNULL( ROUND( ( ( (temp_base_calc.otde_sum + temp_base_calc.otdu1_sum) / 60 ) / 60 ), 1 ), 0), 0 ) as weekday_overtime,

				IF(calend.day_of_week <= 5, IFNULL( ROUND( ( ( (temp_base_calc.otdu2_sum + temp_base_calc.otej_sum) / 60 ) / 60 ), 1 ), 0), 0 ) as weekend_overtime_18_06,
				
				IF(calend.day_of_week > 5, IFNULL( ROUND( ( ( (temp_base_calc.otwdu1_sum + temp_base_calc.otwdu2_sum )/ 60 ) / 60 ), 1 ), 0), 0) as weekend_overtime_0_24_with_extrapay,

				IF(calend.day_of_week > 5, IFNULL(  ROUND( ( ( (temp_base_calc.otwde_sum + temp_base_calc.otwdu1_sum) / 60 ) / 60 ), 1 ), 0), 0 ) as weekend_overtime_06_18,

				IF(calend.day_of_week > 5, IFNULL( ROUND( ( ( (temp_base_calc.otwdu2_sum + temp_base_calc.otwej_sum) / 60 ) / 60 ), 1 ), 0), 0) as weekend_overtime_with_extrapay_18_06,
	
				IF(temp_base_calc.is_public_holiday > 0, ROUND( ( (temp_base_calc.ec_daily_worktime / 60 ) / 60 ), 1 ), 0) as feastday,

				IF(st.state_type_id = 'a272f564576d443e7832587126b070aa', ROUND( ( ( (temp_base_calc.ec_daily_worktime) / 60 ) / 60 ) ), 0) as absence_hour,
				
				IF(st.state_type_id = 'fceedab36bb56f3a59665972fa0a7d54', ROUND( ( ( (temp_base_calc.ec_daily_worktime) / 60 ) / 60 ) ), 0) as extra_absence_hour,
				
				IF(st.state_type_id = 'ebf4ac30e7dc238fd2f4bc86332e0675', ROUND( ( ( (temp_base_calc.ec_daily_worktime) / 60 ) / 60 ) ), 0) as father_absence_hour,

				IF(temp_base_calc.state_type_id = '3ad610cfce362896dbb4b11858dfae40', ( (temp_base_calc.ec_daily_worktime / 60 ) / 60 ), 0 ) as sick_hour,

				IF(temp_base_calc.state_type_id = 'absence_type_justifiedabsence', ( ( temp_base_calc.ec_daily_worktime / 60 ) / 60 ), 0 ) as igazolt_fiz_tav,

				IF(temp_base_calc.state_type_id = 'absence_type_unjustifiedabsence', ( ( temp_base_calc.ec_daily_worktime / 60 ) / 60 ), 0 ) as igtlan_tav_hour,

				IF(temp_base_calc.state_type_id = 'absence_type_justifiedbutnotpaid', ( ( temp_base_calc.ec_daily_worktime / 60 ) / 60 ), 0 ) as igazolt_no_pay,

				IF(temp_base_calc.work_type = 'FLEXIBLE',  ROUND( ( ( (temp_base_calc.balance_sum) / 60 ) / 60 ), 1), 0) as balance
				
				FROM TEMP_employee_base_calc_daily temp_base_calc

				LEFT JOIN calendar calend ON calend.date >= '{$filter['valid_from']}' 
					AND calend.date <= '{$filter['valid_to']}'

				LEFT JOIN company c ON c.company_id = temp_base_calc.company_id 
					AND c.`status` = ".Status::PUBLISHED."
					AND calend.date BETWEEN c.valid_from AND c.valid_to
					
				LEFT JOIN payroll p ON p.payroll_id = temp_base_calc.payroll_id
					AND p.`status` = ".Status::PUBLISHED."
					AND calend.date BETWEEN p.valid_from AND p.valid_to
					
				LEFT JOIN cost ON cost.cost_id = temp_base_calc.cost_id
					AND cost.`status` = ".Status::PUBLISHED."
					AND calend.date BETWEEN cost.valid_from AND cost.valid_to

				LEFT JOIN cost_center cc ON cc.cost_center_id = temp_base_calc.cost_center_id
					AND cc.`status` = ".Status::PUBLISHED."		
					AND calend.date BETWEEN cc.valid_from AND cc.valid_to
	
				LEFT JOIN employee_absence ea ON ea.employee_absence_id = temp_base_calc.employee_absence_id
					AND ea.day = calend.date
					AND ea.`status` = ".Status::PUBLISHED."
					
				LEFT JOIN state_type st ON st.state_type_id = ea.state_type_id
					AND st.`status` = ".Status::PUBLISHED."
										
				WHERE 1=1  

				AND calend.date = temp_base_calc.date
				".$employee_contract."
				".$unit."
				".$payroll."
				".$company."
				".$workgroup."
				".$ext."
					
				ORDER BY temp_base_calc.last_name, calend.date
				";
	
		$this->LAGridDB->setSQLSelection($SQL,'row_unique');
		
		return $SQL;
		
	}
	
	
	public function search(){
		
		$filter = requestParam('searchInput');
		
		$ret = $this->getPreDefinedSearchFromDb("workForce", false);
		
		$moi = ['col_type'			=> 'combo',
				'multiple'			=> false,
				'options'			=>	[
											'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
											'sql'	=> "SELECT ex.option5 AS id, ex.option5 AS value 
														FROM employee_ext ex 
														WHERE ex.`status` = 2
														AND ex.option5 != ''
														AND ex.valid_from <= DATE_FORMAT('".$filter["valid_from"]."', '%Y-%m-%d') AND ex.valid_to >= DATE_FORMAT('".$filter["valid_to"]."', '%Y-%m-%d')
														GROUP BY ex.option5;",
											'array'	=> ['options' => ["id"=>"ALL","value"=>Dict::getValue("all")]],
										],
				'label_text'		=> Dict::getValue("option5"),
				'onchange'			=> array('employee_contract'),
				'default_value'		=> "ALL"
			];
		
		$ret['valid_from']['onchange'][] = 'moi';
		$ret['valid_to']['onchange'][] = 'moi';
		
		$return = array_slice($ret, 0, -2) + ['moi' => $moi] + array_slice($ret, -2);
		
		return $return;
		
		//return $this->getPreDefinedSearchFromDb("employeeManagement");
	}

	
	public function columns(){
		
		return [
			
			// Dolgozó azonosítója
			'employee_name'				=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150',],
			
			// Adószám
			'emp_id'					=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100',],
			
			//Dolgozó neve
			'tax_number'				=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100',],			
			
			// CÉG
			'company_name'				=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150',],
			
			// Számfejtési kör
			'own_temp'					=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150',],
			
			// Divízió
			'division'					=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100',],
			
			// Költségviselő
			'cost_name'					=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '130',],
			
			// Költséghely
			'cost_center_name'			=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '120',],
			
			// Beosztás megnevezés
			'beosztas'					=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100',],
			
			// Bérf cs kód
			'berf_cs_kod'				=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100',],
			
			// ---------- 10
			
			// Lekérdezett nap/napok
			'days'						=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100',],
			
			// Munkaidő: a hétköznapra vonatkozóan a dolgozó szerződésében feltüntetett óraszámot kell megjeleníteni. hétvégi napra: a ledolgozott óraszám megjelenik, 
			// illetve majd később látni fogod, ez a túlóra oszlopban is meg fog jelenni.
			'daily_worktime'			=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100', 'export_as' => 'decimal'],
			
			// 30%os pótlék 18:00-06:00ig ez bármelyik nap, és itt csak az az óraszám lesz megjelenítve ami a 30%-ra vonatkozik.
			'30_precent_allowance'		=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100', 'export_as' => 'decimal'],
			
			// Vasárnapi munkanap: (éjféltől számolja) ledolgozott óraszámot kell feltünteni.
			'sunday_work_hour'			=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100', 'export_as' => 'decimal'],
			
			// Hétköznapi túlóra óraszáma pótlékkal (0-24 bármikor túlórázott azt itt meg kell jelenítni)
			'weekday_overtime_allowance'	=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100', 'export_as' => 'decimal'],
			
			// Hétköznapi túlóra óraszáma: Olyan túlóra amihez műszakprótlék nem kapcsolódik: 06-18-ig hétköznap bármikor
			'weekday_overtime'			=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100', 'export_as' => 'decimal'],

			// Hétköznapi túlóra amihez pótlék kapcsolódik (18-06)
			'weekend_overtime_18_06'	=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100', 'export_as' => 'decimal'],

			// hétvégi túlóra óraszáma pótlékkal (0-24)
			'weekend_overtime_0_24_with_extrapay'	=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100', 'export_as' => 'decimal'],
			
			// hétvégi túlóra óraszáma (06-18) pótlék nélkül
			'weekend_overtime_06_18'	=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100', 'export_as' => 'decimal'],
			
			// hétvégi túlóra óraszáma pótlékkal (18-06)
			'weekend_overtime_with_extrapay_18_06'	=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100', 'export_as' => 'decimal'],
			
			
			// ---------- 20
			
			// Ünnepnap
			'feastday'					=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100', 'export_as' => 'decimal'],
			
			//Szabadság: óraszám kell, szerződés szerinti óraszám
			'absence_hour'				=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100', 'export_as' => 'numeric'],
			
			// Rendkívüli szabadság: óraszám kell, szerződés szerinti óraszám
			'extra_absence_hour'		=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100', 'export_as' => 'numeric'],
			
			// Apa szabadság: óraszám kell, szerződés szerinti óraszám
			'father_absence_hour'		=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100', 'export_as' => 'numeric'],
			
			//Betegség: óraszám kell, szerződés szerinti óraszám
			'sick_hour'					=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100', 'export_as' => 'numeric'],
			
			// Igazolt, fiz. táv.: óraszám kell, szerződés szerinti óraszám
			'igazolt_fiz_tav'			=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100', 'export_as' => 'numeric'],
			
			// Igazolatlan táv.:óraszám kell, szerződés szerinti óraszám
			'igtlan_tav_hour'			=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100', 'export_as' => 'numeric'],
			
			// Igazolt, nem fiz.:óraszám kell, szerződés szerinti óraszám
			'igazolt_no_pay'			=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100', 'export_as' => 'numeric'],
			
			//rugalmas munkabeosztás esetén a lekérdezett intervallumra vonatkozó balansz
			'balance'					=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '100', 'export_as' => 'decimal'],
		
		];

	}
	
	public function attributeLabels(){
		
		return [
			'emp_id'						=> Dict::getValue("emp_id"),
			'tax_number'					=> Dict::getValue("tax_number"),
			'employee_name'					=> Dict::getValue("employee_name"),
			'company_name'					=> Dict::getValue("company"),
			'own_temp'						=> 'Own / Temp',
			'division'						=> Dict::getValue("division"),
			'cost_name'						=> Dict::getValue("cost"),
			'cost_center_name'				=> Dict::getValue("costcenter"),
			'beosztas'						=> Dict::getValue("schedule"),
			'berf_cs_kod'					=> Dict::getValue("berf_cs_kod"),
			'days'							=> Dict::getValue("day"),
			'daily_worktime'				=> Dict::getModuleValue("ttwa-base", "worktime"), 
			'absence_hour'					=> Dict::getModuleValue("ttwa-base", "base_absence_type_onleave").' ('.Dict::getModuleValue("ttwa-base", "hours").')',
			'extra_absence_hour'			=> Dict::getModuleValue("ttwa-base", "base_absence_type_extraordinaryleave").' ('.Dict::getModuleValue("ttwa-base", "hours").')',
			'father_absence_hour'			=> Dict::getModuleValue("ttwa-base", "base_absence_type_paternityleave").' ('.Dict::getModuleValue("ttwa-base", "hours").')',
			'sunday_work_hour'				=> Dict::getValue("sunday_work_hour"),
			'30_precent_allowance'			=> '30 '.Dict::getValue("precent").' '.Dict::getValue("allowance"),
			'weekday_overtime'				=> Dict::getValue("weekday").' '.Dict::getValue("overtime").' 06-18',
			'sick_hour'						=> Dict::getValue("sick_leave").' '.Dict::getValue("hour"),
			'feastday'						=> Dict::getValue("feastday"),
			'weekday_overtime_allowance'	=> Dict::getValue("weekday").' '.Dict::getValue("overtime"),
			'weekend_overtime_18_06'		=> Dict::getValue("weekend").' '.Dict::getValue("overtime").' 18-06',
			'weekend_overtime_06_18'		=> Dict::getValue("weekend").' '.Dict::getValue("overtime").' 06-18',
			'weekend_overtime_with_extrapay_18_06'	=> Dict::getValue("weekend").' '.Dict::getValue("overtime").' 06-18 '.Dict::getValue("allowance"),
			'igtlan_tav_hour'						=> Dict::getValue("base_absence_type_unjustifiedabsence").' '.Dict::getValue("hour"),
			'igazolt_fiz_tav'						=> Dict::getValue("base_absence_type_justifiedabsence").' '.Dict::getValue("hour"),
			'igazolt_no_pay'						=> Dict::getValue("base_absence_type_justifiedabsence").' '.Dict::getValue("no").' '.Dict::getValue("pay"),
			'weekend_overtime_0_24_with_extrapay'	=> Dict::getValue("weekend").' '.Dict::getValue("overtime").' '.Dict::getValue("hours_count").' (0-24)',
			'balance'								=> Dict::getValue("balance"),
		];
		
	}
}
