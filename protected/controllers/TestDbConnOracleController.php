<?php

'yii2-only`;

	namespace app\controllers;
	use Yang;

`/yii2-only';


#yii2: done

/*
 * USAGE:
	http://localhost/testDbConnOracle/index

 *   PHP.INI:
	; PDO ORACLE SQL
	extension=php_oci8_12c.dll
	extension=php_pdo_oci.dll
  Connection String:
	array(
		'McHaleDB'=>array(
			'class'=>'CDbConnection',
			'connectionString'=>'oci:dbname=megaora.hu:666/db03;charset=UTF8',
			'username' => 'mchale',
			'password' => 'mchale',
			'charset' => 'utf8',
		)

 * Get Oracle Client version:
SELECT
  DISTINCT
  client_version
FROM
  v$session_connect_info
WHERE
  sid = sys_context('userenv', 'sid');

 * Get Oracle Server version:
select * from v$version where banner like 'Oracle%';
*/

class TestDbConnOracleController extends Controller
{
	public function actionIndex()
	{
		header('Content-Type: text/html; charset=utf-8');

		$codeParam = requestParam('code');
		if($codeParam != 'aaffea3fdb00a2af0a873bbd90ece19f-4c840639707e1f098f2e4aa00a494e56') {
			die('Missing code');
		}

		// print_r(get_loaded_extensions());
		if (!extension_loaded('pdo_oci')) {
			echo('Missing dl: pdo_oci');
		}

		//$sql = 'select * from v$version where banner like '."'Oracle%'";
		//$sql = "SELECT VEZETEK_NEV, KERESZT_NEV, NYILV_SZAM FROM MF03.BDG1_MC_DOLG";
		//$sql = " SELECT * FROM (SELECT * FROM MF03.BDG1_MC_DOLG) WHERE ROWNUM <= 10";
		//echo "<p>Név: ".$row['VEZETEK_NEV']." ".$row['KERESZT_NEV']."  Törzsszám: ".$row['NYILV_SZAM']."</p>\n";

		//$sql = "SELECT column_name FROM user_tab_columns WHERE table_name = 'YOUR TABLE NAME HERE';";
		//$sql = "SELECT column_name FROM all_tab_columns WHERE owner = 'MF03' AND table_name = 'BDG1_MC_DOLG'";

		$sql = "SELECT * FROM MF03.BDG1_MC_DOLG";

		$dbConn = Yii::app()->McHaleDB;
		$command = $dbConn->createCommand($sql);
		$rows = $command->queryAll();

		if(is_array($rows) && count($rows) > 0) {
			echo "<table border=1>\n";
			echo "<tr>";

			foreach ($rows as $row) {
				foreach ($row as $key => $value) {
					echo "<td>$key</td>";
				}
				break;
			}
			echo "</tr>\n";

			foreach ($rows as $row2) {
				echo "<tr>";
				foreach ($row2 as $key2 => $value2) {
					echo "<td>$value2</td>";
				}
				echo "</tr>\n";
			}

			echo "</table>\n";
		} else {
			die('ERROR!');
		}
		echo "<hr>\n";
	}
}
?>