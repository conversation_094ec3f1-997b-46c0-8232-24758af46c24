<?php

/**
 * Bővített lehetőségekkel megáldott felhasználói jogok felület
 * Innote link: user-right-extended
 */
Yang::import('application.components.API.ApproverStarTable');
class UserRightExtendedController extends Grid2Controller
{
    /* Osztályváltozók */
    /**
     * Root nézi-e
     * @var boolean
     */
	private $isRoot;
    /**
     * Root user id
     * @var string
     */
    private $rootUserId = "6acd9683761b153750db382c1c3694f6";
    /**
     * Aktív státusz
     * @var int
     */
	private $pub = Status::PUBLISHED;
    /**
     * Világvége
     * @var string
     */
	private $end;
    /**
     * Cég és számfejtési kör alapján szétv<PERSON>lasztott megoldás (Veluxnak készült eredetileg)
     * @var array
     */
	private $companyAndPayrollMode;
    /**
     * "Összes" dictionary
     * @var string
     */
	private $all;
    /**
     * Debug mód - yang log
     * @var boolean
     */
    private $debugMode = false;
	/**
	 * Kiválasztható a jogosultságnál * végződés (--> Minden olyan névvel kezdődőre ad jogot)
	 * @var int
	 */
	private $acceptRelatedValStarEnding = 0;

    /**
     * Grid2 konstruktor meghívása, controller id beállítás, osztályváltozó def
	 * @param string $controllerID
     */
	public function __construct(string $controllerID = "userRightExtended")
	{
		parent::__construct($controllerID);
		$this->isRoot						= App::isRootSessionEnabled();
		$this->end					        = App::getSetting("defaultEnd");
		$this->acceptRelatedValStarEnding	= App::getSetting("userRightRelatedValAcceptStarEnd");
		$this->companyAndPayrollMode		= $this->companyAndPayrollModeChanges();
		$this->all							= Dict::getValue("all");
        if ($this->debugMode)               { Yang::log($this->companyAndPayrollMode["setting"] ?? 0, "log", "useCompanyAndPayrollRights"); }
        if ($this->debugMode)               { Yang::log($this->isRoot, "log", "isRoot"); }
	}

    /**
     * Grid inicializálás, használt mód: SQL
     * @return void
     */
	protected function G2BInit(): void
	{
		parent::enableMultiGridMode();
		parent::setControllerPageTitleId("page_title_user_right_extended");
		parent::setExportFileName(Dict::getValue("page_title_user_right_extended"));

		$this->LAGridRights->overrideInitRights("paging",				true, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search",				true, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search_header",		true, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("select",				true, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("multi_select",			true, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("column_move",			true, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("col_sorting",			true, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload_sortings",		true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("details",				false,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload",				true, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_xls",			false, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_xlsx",			true, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_pdf_node",		false, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("init_open_search",		true, 	"dhtmlxGrid");

		$this->LAGridDB->setModelName("Approver", "dhtmlxGrid");
		$this->LAGridDB->enableSQLMode();
		$this->LAGridDB->setSQLSelection($this->getGridDataSQL(), "row_id", "dhtmlxGrid");

		parent::G2BInit();
	}

	/**
	 * Cég és számfejtési kör GARG vizsgálós működés extrák
	 * @return array
	 */
	private function companyAndPayrollModeChanges(): array
	{
		$companyPayrollChanges = [];

		/* Be van-e kapcsolva */
		$setting = (int)App::getSetting("useCompanyAndPayrollRights"); // Több cég kezelés esetén (veluxnak készült), garg és cég vizsgálat
		/* GARG Employee lekérés */
		$gargSQL = $this->getGargModeSQL($setting);
		$companyPayrollChanges["joinSQL"]		= $gargSQL["joins"] ?? "";
		$companyPayrollChanges["whereSQL"]		= $gargSQL["where"] ?? "";
		$companyPayrollChanges["userWhereSQL"]	= $gargSQL["userWhere"] ?? "";

		if ($setting)
		{
			/* Setting On / Off */
			$companyPayrollChanges["setting"] = 1;
			/* Company Search */
			$art	    = new ApproverRelatedGroup;
        	$gargSQL    = $art->getApproverReleatedGroupSQL("Company", ["employeeManagement"], userID(), "'{date}'", "AND", "allDate");
        	$companyPayrollChanges['search']				= $this->getDetailsHelper("*", "combo", "SQL", "company", $gargSQL["where"], ['employee_contract', 'user_id']);
			$companyPayrollChanges['companyEmpWhereSQL']	= " AND (`employee`.`company_id` = '{company}' OR '{company}' = 'ALL') ";
			$companyPayrollChanges['companyUserWhereSQL']	= " AND (`employee`.`company_id` = '{company}' OR '{company}' = 'ALL' OR `employee`.`row_id` IS NULL) ";
			$companyPayrollChanges['gridDataWhereSQL']		= str_replace_first("AND", "AND (('{employee_contract}' = '' AND `employee`.`row_id` IS NULL) OR (", $gargSQL["where"]) . "))";
			$companyPayrollChanges['gridDataWhereSQL']		.= " AND (`employee`.`company_id` = '{company}' OR '{company}' = 'ALL' OR `employee`.`row_id` IS NULL)";
			/* GARG companyMainData */
			$art = new ApproverRelatedGroup;
			$companyPayrollChanges["companyMainData"]["Unit"]               = $art->getApproverReleatedGroupSQL("Unit",				["companyMainData"],	userID(),	"'{date}'",	"AND",	"allDate");
			$companyPayrollChanges["companyMainData"]["Workgroup"]		    = $art->getApproverReleatedGroupSQL("Workgroup",		["companyMainData"],	userID(),	"'{date}'",	"AND",	"allDate");
			$companyPayrollChanges["companyMainData"]["Company"]            = $art->getApproverReleatedGroupSQL("Company",			["companyMainData"],	userID(),	"'{date}'",	"AND",	"allDate");
			$companyPayrollChanges["companyMainData"]["Payroll"]            = $art->getApproverReleatedGroupSQL("Payroll",			["companyMainData"],	userID(),	"'{date}'",	"AND",	"allDate");
			$companyPayrollChanges["companyMainData"]["CompanyOrgGroup1"]   = $art->getApproverReleatedGroupSQL("CompanyOrgGroup1",	["companyMainData"],	userID(),	"'{date}'",	"AND",	"allDate");
			$companyPayrollChanges["companyMainData"]["CompanyOrgGroup2"]   = $art->getApproverReleatedGroupSQL("CompanyOrgGroup2",	["companyMainData"],	userID(),	"'{date}'",	"AND",	"allDate");
			$companyPayrollChanges["companyMainData"]["CompanyOrgGroup3"]   = $art->getApproverReleatedGroupSQL("CompanyOrgGroup3",	["companyMainData"],	userID(),	"'{date}'",	"AND",	"allDate");
			/* Szuper speckó szűrés ami a Veluxnak készült és a Grid2Data helyettesíti be */
			/* Emlékeim szerint valami olyasmi volt a lényeg, hogy a jogoknál a céges alapadatok jognak is meg kell lennie minden máshoz, illetve struktúrálisan előbb a hozzá (pl egység, munkacsop) tartozó cég -> számfejtési kör .. jog is meglegyen, ha nincs nem lehet felvenni */
			$companyPayrollChanges["companyMainData"]["Unit"]["extraWhere"]				= " AND IF('{process_id}' = 'companyMainData', 'companyMainDataUnit' = 'companyMainDataUnit', `unit`.`payroll_id` = 'ALL' OR (1=1 AND 'unit' = 'unit')) ";
			$companyPayrollChanges["companyMainData"]["Workgroup"]["extraWhere"]		= " AND IF('{process_id}' = 'companyMainData', 'companyMainDataWorkgoup' = 'companyMainDataWorkgroup', `workgroup`.`payroll_id` = 'ALL' OR (1=1 AND 'workgroup' = 'workgroup')) ";
			$companyPayrollChanges["companyMainData"]["Company"]["extraWhere"]			= " AND IF('{process_id}' = 'companyMainData', 'companyMainDataCompany' = 'companyMainDataCompany', 1=1 AND 'company' = 'company') ";
			$companyPayrollChanges["companyMainData"]["Payroll"]["extraWhere"]			= " AND IF('{process_id}' = 'companyMainData', 'companyMainDataPayroll' = 'companyMainDataPayroll', `payroll`.`company_id` = 'ALL' OR (1=1 AND 'payroll' = 'payroll')) ";
			$companyPayrollChanges["companyMainData"]["CompanyOrgGroup1"]["extraWhere"]	= " AND IF('{process_id}' = 'companyMainData', 'companyMainDataCompanyOrgGroup1' = 'companyMainDataCompanyOrgGroup1', `company_org_group1`.`payroll_id` = 'ALL' OR (1=1 AND 'company_org_group1' = 'company_org_group1')) ";
			$companyPayrollChanges["companyMainData"]["CompanyOrgGroup2"]["extraWhere"]	= " AND IF('{process_id}' = 'companyMainData', 'companyMainDataCompanyOrgGroup2' = 'companyMainDataCompanyOrgGroup2', `company_org_group2`.`payroll_id` = 'ALL' OR (1=1 AND 'company_org_group2' = 'company_org_group2')) ";
			$companyPayrollChanges["companyMainData"]["CompanyOrgGroup3"]["extraWhere"]	= " AND IF('{process_id}' = 'companyMainData', 'companyMainDataCompanyOrgGroup3' = 'companyMainDataCompanyOrgGroup3', `company_org_group3`.`payroll_id` = 'ALL' OR (1=1 AND 'company_org_group3' = 'company_org_group3')) ";
			/* DEL onclick overwrite */
			$companyPayrollChanges["delDialogOnClick"] = "G2BDelDialog('dhtmlxGrid', './gridData', null, $('.offCanvasSearchSection .to-serialize').serialize()+'&mainGridID=dhtmlxGrid', controllerRights['dhtmlxGrid'], './delete', '" . Dict::getValue("deleteRelatedRights") . "', '" . Dict::getValue("please_select_line") . "');";
		} else {
			/* Setting On / Off */
			$companyPayrollChanges["setting"] = 0;
		}

		return $companyPayrollChanges;
	}

	/**
     * Kereső GARG lekérés Cég és Számfejtési kör módban - jogosultság vizsgálat
	 * @param int $setting
     * @return array
     */
    private function getGargModeSQL(int $setting): array
    {
        $retArr = [
            "joins"     => "",
            "where"     => "",
            "userWhere" => ""
        ];
        $art        = new ApproverRelatedGroup;
        $gargSQL    = $art->getApproverReleatedGroupSQL("Employee", ["employeeManagement"], userID(), "'{date}'", "AND", "allDate");
		if ($setting)
        {
			$retArr["joins"] = "
				" . EmployeeGroup::getAllActiveLeftJoinSQL("employee_contract", "'{date}'", "", "employee") . "
				" . EmployeeGroup::getAllBaseTablesWithGroup("employee", "employee_contract", "'{date}'") . "
			";
			if (isset($gargSQL["join"])) {
				$retArr["joins"]        .= $gargSQL["join"];
			}
			if (isset($gargSQL["where"])) {
				$retArr["where"]        = $gargSQL["where"];
				$retArr["userWhere"]    = str_replace_first("AND", "AND (`employee`.`row_id` IS NULL OR (", $gargSQL["where"]) . "))";
			}
		}

        return $retArr;
    }

    /**
	 * Grid2 kereső
	 * @return array
	 */
	protected function search(): array
    {
        /* Korábbi dátum behelyettesítés SESSION-ből */
        $userId         = userID();
		$controllerId   = $this->getControllerID();
		$input          = "searchInput_date";
        $replace        = false;
        $replaceWith    = "";
        if (isset($_SESSION["tiptime"][$userId][$controllerId][$input])) {
            $replace        = true;
            $replaceWith    = $_SESSION["tiptime"][$userId][$controllerId][$input];
        }

        /* Kereső mezők */
        $searchFields = [];
		$this->addDateSearch($searchFields);
        $this->addCompanySearch($searchFields, $replace, $replaceWith);
        $this->addContractSearch($searchFields, $replace, $replaceWith);
        $this->addUserSearch($searchFields, $replace, $replaceWith);
		$this->addProcessIdSearch($searchFields);
        $this->addRelatedModelSearch($searchFields);
        $this->addRelatedSearch($searchFields, $replace, $replaceWith);
		$searchFields['submit'] = ['col_type' => 'searchBarReinitGrid', 'width' => '*', 'label_text' => ''];

		return $searchFields;
    }

	/**
	 * Dátum kereső
	 * @param array $searchFields
	 * @return void
	 */
	private function addDateSearch(array &$searchFields): void {
		$searchFields['date'] = $this->getDetailsHelper("*", "ed", "", "date", "", ['employee_contract', 'user_id', 'related'], true, date("Y-m-d"));
	}

    /**
     * Cég kereső
     * @param array $searchFields
     * @param bool $replace
     * @param string $replaceWith
     * @return void
     */
    private function addCompanySearch(array &$searchFields = [], bool $replace = false, string $replaceWith = ""): void
    {
		if (isset($this->companyAndPayrollMode['search']))
		{
			$searchFields['date']['onchange'][] = 'company';

			$searchFields['company'] = $this->companyAndPayrollMode['search'] ?? [];

			if ($replace)			{ $searchFields["company"]["options"]["sql"] = str_replace("'{date}'", "'" . $replaceWith . "'", $searchFields["company"]["options"]["sql"] ?? ""); }
			if ($this->debugMode)   { Yang::log(json_encode($searchFields["company"] ?? [], JSON_PRETTY_PRINT), "log", "companySearch"); }
		}
    }

    /**
     * Dolgozó kereső
     * @param array $searchFields
     * @param bool $replace
     * @param string $replaceWith
     * @return void
     */
    private function addContractSearch(array &$searchFields = [], bool $replace = false, string $replaceWith = ""): void
    {
        $employeeName   = Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]);

        $searchFields['employee_contract'] =
		[
			'col_type'		=> 'auto',
			'options'		=>	[
				'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
				'sql'	=> "
					SELECT
						`employee_contract`.`employee_contract_id` AS id,
						{$employeeName} AS value
					FROM `employee`
					LEFT JOIN `employee_contract` ON
							`employee_contract`.`employee_id` = `employee`.`employee_id`
						AND `employee_contract`.`status` = {$this->pub}
						AND '{date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->end}')
						AND '{date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->end}')
                    " . ($this->companyAndPayrollMode["joinSQL"] ?? "") . "
					WHERE
							`employee`.`status` = {$this->pub}
						AND '{date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->end}')
						" . ($this->companyAndPayrollMode["whereSQL"] ?? "") . "
						" . ($this->companyAndPayrollMode["companyEmpWhereSQL"] ?? "") . "
						AND {$employeeName} LIKE '%%{search}%%'
					ORDER BY value
				",
				'array'	=> [["id" => "", "value" => ""]]
			],
			'label_text'	=> Dict::getValue("name"),
			'default_value'	=> ''
		];

        if ($replace)           { $searchFields["employee_contract"]["options"]["sql"] = str_replace("'{date}'", "'" . $replaceWith . "'", $searchFields["employee_contract"]["options"]["sql"] ?? ""); }
        if ($this->debugMode)   { Yang::log(json_encode($searchFields["employee_contract"] ?? [], JSON_PRETTY_PRINT), "log", "contractSearch"); }
    }

    /**
     * Felhasználó kereső
     * @param array $searchFields
     * @param bool $replace
     * @param string $replaceWith
     * @return void
     */
    private function addUserSearch(array &$searchFields = [], bool $replace = false, string $replaceWith = ""): void
    {
        $joinSQL        = $this->companyAndPayrollMode["joinSQL"] ?? ""; // Company mód GARG
        $whereSQL       = $this->companyAndPayrollMode["userWhereSQL"] ?? ""; // WHERE - Company mód GARG
		$whereSQL		.= $this->companyAndPayrollMode["companyUserWhereSQL"] ?? ""; // WHERE - Company mód cég szűrő
        $whereSQL       .= " AND `user`.`username` LIKE '%%{search}%%' "; // WHERE - User kereső
        $whereSQL       .= (!$this->isRoot) ? " AND `user`.`user_id` <> '{$this->rootUserId}' " : ""; // WHERE - root jogok jelenjenek-e meg

        $searchFields['user_id'] = $this->getDetailsHelper("*", "auto", "SQL", "user", $whereSQL, [], false, "", $joinSQL, "`user`.`employee_id`", [["id" => "", "value" => ""]]);

        if ($replace)           { $searchFields["user_id"]["options"]["sql"] = str_replace("'{date}'", "'" . $replaceWith . "'", $searchFields["user_id"]["options"]["sql"] ?? ""); }
        if ($this->debugMode)   { Yang::log(json_encode($searchFields["user_id"] ?? [], JSON_PRETTY_PRINT), "log", "userSearch"); }
    }

	/**
	 * Process id kereső
	 * @param array $searchFields
	 * @return void
	 */
	private function addProcessIdSearch(array &$searchFields): void {
        $processIds = App::getLookup('approver_process_ids', false, null, [], true);

		$searchFields['process_id'] = $this->getDetailsHelper("*", "combo", "ARR", "process_id", "", [], false, "ALL", "", "", array_merge([["id" => "ALL", "value" => $this->all]], $processIds));

        if ($this->debugMode) { Yang::log(json_encode($searchFields["process_id"] ?? [], JSON_PRETTY_PRINT), "log", "processIdSearch"); }
	}

    /**
     * Csoportosítás kereső
     * @param array $searchFields
     * @return void
     */
    private function addRelatedModelSearch(array &$searchFields = []): void
    {
        $searchFields['related_model'] =
		[
			'col_type'		=> 'combo',
			'options'		=>
            [
				'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
				'sql'	=> "
					SELECT
						art.`related_model` AS id,
						d.`dict_value` AS value
					FROM `approver_related_type` art
					LEFT JOIN `dictionary` d ON
							d.`dict_id` = art.`name_dict_id`
						AND d.`lang` = '" . Dict::getLang() . "'
					WHERE
							art.`status` = {$this->pub}
						AND (d.`dict_value` LIKE '%%{search}%%')
					ORDER BY d.`dict_value`
				",
				'array'	=> [["id" => "ALL", "value" => $this->all]]
			],
			'label_text'	=> Dict::getValue("classification"),
			'default_value'	=> ''
		];

        if ($this->debugMode) { Yang::log(json_encode($searchFields["related_model"] ?? [], JSON_PRETTY_PRINT), "log", "relatedModelSearch"); }
    }

    /**
     * Csoportosítás érték kereső
     * @param array $searchFields
     * @param bool $replace
     * @param string $replaceWith
     * @return void
     */
    private function addRelatedSearch(array &$searchFields = [], bool $replace = false, string $replaceWith = ""): void
    {
        $searchFields['related'] =
		[
			'col_type'		=> 'auto',
			'options'		=> [
				'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
				'sql'	=> $this->getSearchRelatedSQL(),
				'array'	=> [["id" => "", "value" => ""]]
			],
			'label_text'	=> Dict::getValue("selected_group_or_employee"),
			'default_value'	=> ''
		];

        if ($replace)           { $searchFields["related"]["options"]["sql"] = str_replace("'{date}'", "'" . $replaceWith . "'", $searchFields["related"]["options"]["sql"] ?? ""); }
        if ($this->debugMode)   { Yang::log(json_encode($searchFields["related"] ?? [], JSON_PRETTY_PRINT), "log", "relatedSearch"); }
    }

    /**
	 * Visszaadja a keresőbe a csoport érték lekérő SQL-t.
	 * @return string
	 */
	private function getSearchRelatedSQL(): string
	{
        $relatedDefinitions =
        [
            "unit"                  => ["cogId" => false, 'relatedModel' => 'Unit'],
            "workgroup"             => ["cogId" => false, 'relatedModel' => 'Workgroup'],
            "company"               => ["cogId" => false, 'relatedModel' => 'Company'],
            "payroll"               => ["cogId" => false, 'relatedModel' => 'Payroll'],
            "company_org_group1"    => ["cogId" => true,  'relatedModel' => 'CompanyOrgGroup1'],
            "company_org_group2"    => ["cogId" => true,  'relatedModel' => 'CompanyOrgGroup2'],
            "company_org_group3"    => ["cogId" => true,  'relatedModel' => 'CompanyOrgGroup3'],
            "cost"                  => ["cogId" => false, 'relatedModel' => 'Cost']
        ];

        $groupsSQL = "";
        foreach ($relatedDefinitions as $table => $tableData)
        {
            $tableId            = ($tableData["cogId"]) ? "company_org_group" : $table;
            $groupsSQL          .= "
                UNION
                SELECT
                    `{$table}`.`{$tableId}_id` AS id,
                    `{$table}`.`{$tableId}_name` AS value
                FROM `{$table}`
                WHERE
                        `{$table}`.`status` = {$this->pub}
                    AND '{date}' BETWEEN `{$table}`.`valid_from` AND IFNULL(`{$table}`.`valid_to`, '{$this->end}')
                    AND '" . ($tableData["relatedModel"] ?? "") . "' = '{related_model}'
                    " . ($this->companyAndPayrollMode["companyMainData"][$tableData["relatedModel"]]["where"] ?? "") . "
            ";
        }

        $employeeName = Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]);
		$SQL = "
			SELECT a.* FROM
			(
				SELECT 'ALL' AS `id`, '{$this->all}' AS `value`
                {$groupsSQL}
				UNION
			 	SELECT
				 	`employee_contract`.`employee_contract_id` AS id,
					{$employeeName} AS value
			 	FROM `employee_contract`
			 	LEFT JOIN `employee` ON
						`employee`.`employee_id` = `employee_contract`.`employee_id`
					AND `employee`.`status` = {$this->pub}
					AND '{date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->end}')
				" . ($this->companyAndPayrollMode["joinSQL"] ?? "") . "
			 	WHERE
						`employee_contract`.`status` = {$this->pub}
					AND '{date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->end}')
					AND '{date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->end}')
					AND 'EmployeeContract' = '{related_model}'
					" . ($this->companyAndPayrollMode["whereSQL"] ?? "") . "
			) a
			WHERE a.`value` like '%%{search}%%'
            ORDER BY a.`value`
		";

		return $SQL;
	}

    /**
	 * Grid2 col def helper
	 * @param [type] $width
	 * @param string $type
	 * @param string $mode
	 * @param string $labelAndSQLTable
	 * @param string $sqlModeWhereSQL
	 * @param array $onChange
	 * @param boolean $dPicker
	 * @param string $def
	 * @param string $sqlModeJoinEmpTablesSQL
	 * @param string $sqlModeJoinEmpTablesOn
	 * @param array $comboArr
	 * @param integer $grid
	 * @param integer $export
	 * @param integer $reportWidth
	 * @param integer $window
	 * @return array
	 */
	private function getDetailsHelper(
        $width                       		= "*",
        string $type                        = "ed",
        string $mode                        = "",
        string $labelAndSQLTable            = "",
        string $sqlModeWhereSQL             = "",
        array $onChange                     = [],
        bool $dPicker                       = false,
        string $def                         = "",
        string $sqlModeJoinEmpTablesSQL     = "",
        string $sqlModeJoinEmpTablesOn      = "",
        array $comboArr                     = [],
		int $grid							= 2,
		int $export							= 2,
		int $reportWidth					= 0,
		int $window							= 2,
		int $lineBreak						= 2
    ): array
	{
		$col					    = [];
		$col["width"]			    = $width;
		$col["col_type"]		    = $type;

        /* SQL Mode */
        if ($mode === "SQL" && ($type === "combo" || $type === "auto"))
        {
			// Szükség van az employee és contract táblákra garg miatt
			// $sqlModeJoinEmpTablesSQL - többi csoportosítás tábla garghoz
            if ($sqlModeJoinEmpTablesSQL != "")
			{
                $joinSQL = "
                    LEFT JOIN `employee` ON
                            `employee`.`employee_id` = {$sqlModeJoinEmpTablesOn}
                        AND `employee`.`status` = {$this->pub}
                        AND '{date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->end}')
                    LEFT JOIN `employee_contract` ON
                            `employee_contract`.`employee_id` = `employee`.`employee_id`
                        AND `employee_contract`.`status` = {$this->pub}
                        AND '{date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->end}')
                        AND '{date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->end}')
                    {$sqlModeJoinEmpTablesSQL}
                ";
            } else {
                $joinSQL = "";
            }
			// Sajnos nem user_name lett ..
            if ($labelAndSQLTable === "user") { $name = "username"; } else { $name = $labelAndSQLTable . "_name"; }
            $col["options"]         =
            [
                "mode"  => Grid2Controller::G2BC_QUERY_MODE_SQL,
                "sql"   => "
                    SELECT
                        `{$labelAndSQLTable}`.`{$labelAndSQLTable}_id` AS id,
                        `{$labelAndSQLTable}`.`{$name}` AS value
                    FROM `{$labelAndSQLTable}`
                    {$joinSQL}
                    WHERE
                            `{$labelAndSQLTable}`.`status` = {$this->pub}
                        AND '{date}' BETWEEN `{$labelAndSQLTable}`.`valid_from` AND IFNULL(`{$labelAndSQLTable}`.`valid_to`, '{$this->end}')
                        {$sqlModeWhereSQL}
                    ORDER BY `value`
                "
            ];
        }
        /* Array Mode */
        if ($mode === "ARR" && ($type === "combo" || $type === "auto")) {
            $col["options"]         = ["mode" => Grid2Controller::G2BC_QUERY_MODE_ARRAY];
        }
        /* Array */
        if (!empty($comboArr)) {
            $col["options"]["array"]= $comboArr;
        }
        /* Label */
        if ($labelAndSQLTable != "") {
            $col["label_text"]      = Dict::getValue($labelAndSQLTable);
        }
        /* Onchange Fields*/
        if (!empty($onChange)) {
            $col["onchange"]        = $onChange;
        }
        /* Datepicker */
        if ($dPicker) {
            $col["dPicker"]         = true;
        }
        /* Default Value */
        if ($def != "") {
            $col["default_value"]   = $def;
        }
		/* Grid */
		if ($grid === 1) {
			$col["grid"]			= true;
		} else if ($grid === 0) {
			$col["grid"]			= false;
		}
		/* Export */
		if ($export === 1) {
			$col["export"]			= true;
		} else if ($export === 0) {
			$col["export"]			= false;
		}
		/* Report Width */
		if ($reportWidth != 0) {
			$col["report_width"]	= $reportWidth;
		}
		/* Window */
		if ($window === 1) {
			$col["window"]			= true;
		} else if ($window === 0) {
			$col["window"]			= false;
		}
		/* Line Break */
		if ($lineBreak === 1) {
			$col["line_break"]		= true;
		} else if ($lineBreak === 0) {
			$col["line_break"]		= false;
		}

		return $col;
	}

    /**
     * Action gombok definiálása
     * @param string $gridID
     * @return array
     */
    protected function getStatusButtons($gridID = null): array
	{
		$buttons = [];
		if ($gridID === "dhtmlxGrid" && App::hasRight($this->getControllerID(), "copy"))
		{
			$buttons["openCopyDialog"] =
            [
				"type"		=> "button",
				"id"		=> "openCopyDialog",
				"class"		=> "openCopyDialog",
				"name"		=> "openCopyDialog",
				"img"		=> "/images/status_icons/st_copy.png",
				"label"		=> Dict::getValue("copy_user_rights"),
				"onclick"	=> "G2BDialogMultiGrid('dhtmlxGrid', 'copyDialog', null, 0, './dialog', '" . baseURL() . "/" . $this->getControllerID() . "/copyUserRights', './gridData', null, '" . Dict::getValue("copy_user_rights") . "', null);"
			];
			$buttons[] = [
				"type"	=> "selector",
			];
		}

		$parent = parent::getStatusButtons($gridID);
		if (isset($parent["openDelDialog"])) {
			$parent["openDelDialog"]["onclick"] = $this->companyAndPayrollMode["delDialogOnClick"] ?? ($parent["openDelDialog"]["onclick"] ?? "");
		}

		return Yang::arrayMerge($buttons, $parent);
	}

    /**
	 * Grid2 oszlop definiálás, column right check: ON
	 * @return array
	 */
	public function columns(): array
	{
        /* Folyamatok */
        $processIds			= App::getLookup('approver_process_ids', false, null, [], true);
		/* Copy Dialog Fullname */
		$fullName			= Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]);
		/* Kereső */
		$filter				= requestParam('searchInput');
		$rowId				= requestParam('editPK') !== null ? requestParam('editPK') : 0;
		$date				= $filter['date'] ?? "";
		/* Beállítások */
        $approverUserIdType	= App::getSetting("userRightDialogApproverUserIdComponentType"); // Auto típusú felvétel (flexnél túl nagy legördülő)
        $relatedValueType	= App::getSetting("userRightDialogRelatedValueComponentType"); // Auto típusú felvétel (flexnél túl nagy legördülő)
		$endorsement		= App::getSetting("useApprovalWithEndorsement"); // Több szintű jóváhagyás esetén szint berakása
        /* Autocomplete Mode */
        if ($approverUserIdType === 'auto') {
            $approverUserIdSearch	= " AND `user`.`username` LIKE '%%{search}%%' ";
			$copyDialogEmpSearch	= " AND {$fullName} LIKE '%%{search}%%' ";
			$defaultValueApprover	= ($rowId !== 0 ? $this->getApproverUserIdComputedValue((int)$rowId) : '');
        } else {
            $approverUserIdSearch	= "";
			$copyDialogEmpSearch	= "";
			$defaultValueApprover	= "";
        }
        if ($relatedValueType === 'auto') {
            $relatedValueSearch		= " AND value LIKE '%%{search}%%' ";
			$defaultValueRelated	= ($rowId !== 0 ? $this->getRelatedValueComputedValue((int)$rowId) : '');
        } else {
            $relatedValueSearch		= "";
			$defaultValueRelated	= "";
        }

		/* GARG cég választós verzióban */
        $joinSQL        = $this->companyAndPayrollMode["joinSQL"] ?? "";
		$whereSQL		= $this->companyAndPayrollMode["whereSQL"] ?? "";
		$userWhereSQL	= $this->companyAndPayrollMode["userWhereSQL"] ?? "";
		/* User WHERE */
        $userWhereSQL   .= (!$this->isRoot) ? " AND `user`.`user_id` <> '{$this->rootUserId}' " : ""; // Root jogok jelenjenek-e meg
		$userWhereSQL	.= " AND ('{valid_from}' BETWEEN `user`.`valid_from` AND IFNULL(`user`.`valid_to`, '{$this->end}') OR '{valid_to}' BETWEEN `user`.`valid_from` AND IFNULL(`user`.`valid_to`, '{$this->end}')) ";
		$userWhereSQL	.= $approverUserIdSearch;

		$columns['dhtmlxGrid'] =
		[
			'valid_from'		=> $this->getDetailsHelper("*", "ed", "", "", "", ['approver_user_id', 'related_value'], true, "", "", "", [], 0, 0, 20, 1),
			'valid_to'			=> $this->getDetailsHelper("*", "ed", "", "", "", ['approver_user_id', 'related_value'], true, "", "", "", [], 0, 0, 20, 1),
			'fullname'			=> $this->getDetailsHelper(220, "ed", "", "", "", [], false, "", "", "", [], 1, 1, 20, 0),
			'username'			=> $this->getDetailsHelper(220, "ed", "", "", "", [], false, "", "", "", [], 2, 1, 20, 0),
			'approver_user_id'	=> $this->getDetailsHelper(220, $approverUserIdType, "SQL", "user", $userWhereSQL, ['related_value'], false, $defaultValueApprover, $joinSQL, "`user`.`employee_id`", [["id" => "", "value" => ""]], 0, 0, 20, 1),
			'process_id'		=> $this->getDetailsHelper(220, "combo", "ARR", "", "", ['related_value'], false, "", "", "", $processIds, 1, 1, 20),
			'related_model'		=>
			[
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width' 		=> 220,
				'onchange' 		=> ['related_id', 'related_value'],
				'options'		=>
				[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							art.`related_model` AS id,
							d.`dict_value` AS value
						FROM `approver_related_type` art
						LEFT JOIN `dictionary` d ON
								d.`dict_id` = art.`name_dict_id`
							AND d.`lang` = '" . Dict::getLang() . "'
						WHERE art.`status` = {$this->pub}
						ORDER BY d.`dict_value`
					",
					'array'	=> [["id" => "", "value" => ""]]
				]
			],
			'related_value'	=>
			[
				'export' 		=> false,
				'report_width'	=> 20,
				'col_type' 		=> $relatedValueType,
				'width' 		=> 300,
				'grid' 			=> false,
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> $this->createRelatedValueSQL($joinSQL, $whereSQL, $date, $relatedValueSearch),
					'array'	=> [["id" => "", "value" => ""]]
				],
				'starExpression'=> ($this->acceptRelatedValStarEnding == "1") ? true : false,
				'default_value'	=> $defaultValueRelated
			],
			'related'	=> $this->getDetailsHelper(220, "ed", "", "", "", [], false, "", "", "", [], 1, 1, 20, 0),
		];
		/* Kereső dátum behelyettesítés */
		$columns['dhtmlxGrid']['approver_user_id']['options']['sql'] = str_replace("'{date}'", "'" . $date . "'", $columns['dhtmlxGrid']['approver_user_id']['options']['sql'] ?? "");

		if ($endorsement) {
			$columns['dhtmlxGrid']['level']			= $this->getDetailsHelper("*", "ed", "", "", "", [], false, "", "", "", [], 1, 1, 20);
		}
		$columns['dhtmlxGrid']['note']				= $this->getDetailsHelper("*", "ed", "", "", "", [], false, "", "", "", [], 1, 1, 20);
		$columns['dhtmlxGrid']['grid_valid_from']	= $this->getDetailsHelper("*", "ed", "", "", "", [], true, "", "", "", [], 1, 1, 20, 0);
		$columns['dhtmlxGrid']['grid_valid_to']		= $this->getDetailsHelper("*", "ed", "", "", "", [], true, "", "", "", [], 1, 1, 20, 0);

		/* Copy Dialog Overwrite */
		$type= [
			['id' => 'N', 'value' => Dict::getValue("no")],
			['id' => 'Y', 'value' => Dict::getValue("yes")]
		];
		/* Copy Dialog Process Id Def: ALL */
		foreach ($processIds as $i => $ro) {
			if (isset($ro["default"])) {
				unset($processIds[$i]["default"]);
			}
		}
		/* User Onchange */
		$userFromWhereSQL	= $userWhereSQL . " AND (`employee_contract`.`employee_contract_id` = '{from_fullname}' OR ('{from_fullname}' = '' AND (`user`.`employee_id` IS NULL OR `user`.`employee_id` = ''))) ";
		$userToWhereSQL		= $userWhereSQL . " AND (`employee_contract`.`employee_contract_id` = '{to_fullname}' OR ('{to_fullname}' = '' AND (`user`.`employee_id` IS NULL OR `user`.`employee_id` = ''))) ";

		$columns['copyDialog'] =
		[
			'valid_from'		=> $this->getDetailsHelper(220, "ed", "", "", "", ['from_fullname', 'from_username', 'to_fullname', 'to_username'], true, "", "", "", [], 2, 0, 20, 2, 1),
			'valid_to'			=> $this->getDetailsHelper(220, "ed", "", "", "", ['from_fullname', 'from_username', 'to_fullname', 'to_username'], true, "", "", "", [], 2, 0, 20),
			'from_fullname'		=>
			[
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> $approverUserIdType,
				'width' 		=> 220,
				'label_text'	=> Dict::getValue("from_employee"),
				'onchange' 		=> ['from_username'],
				'options'		=>
				[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							`employee_contract`.`employee_contract_id` AS id,
							{$fullName} AS value
						FROM `employee`
						LEFT JOIN `employee_contract` ON
								`employee_contract`.`employee_id` = `employee`.`employee_id`
							AND `employee_contract`.`status` = {$this->pub}
							AND '{$date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->end}')
							AND '{$date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->end}')
						{$joinSQL}
						RIGHT JOIN `user` u ON
								u.`employee_id` = `employee`.`employee_id`
							AND '{$date}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$this->end}')
							AND u.`status` = {$this->pub}
						WHERE
								`employee`.`status` = {$this->pub}
							{$whereSQL}
							AND '{$date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->end}')
							AND ('{valid_from}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->end}') OR '{valid_to}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->end}'))
							{$copyDialogEmpSearch}
						ORDER BY value",
					'array'	=> [["id" => "", "value" => ""]]
				]
			],
			'from_username'		=> $this->getDetailsHelper(200, $approverUserIdType, "SQL", "user", $userFromWhereSQL, [], false, "", " AND 1=1 ", "`user`.`employee_id`", [["id" => "", "value" => ""]], 2, 1, 20),
			'from_process_id'	=> $this->getDetailsHelper(220, "combo", "ARR", "", "", [], false, "", "", "", Yang::arrayMerge([["id" => "ALL", "value" => $this->all, "default" => 1]], $processIds), 2, 1, 20),
			'to_fullname'		=>
			[
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> $approverUserIdType,
				'width' 		=> 220,
				'label_text'	=> Dict::getValue("to_employee"),
				'onchange' 		=> ['to_username'],
				'line_break'	=> true,
				'options'		=>
				[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							`employee_contract`.`employee_contract_id` AS id,
							{$fullName} AS value
						FROM `employee`
						LEFT JOIN `employee_contract` ON
								`employee_contract`.`employee_id` = `employee`.`employee_id`
							AND `employee_contract`.`status` = {$this->pub}
							AND '{$date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->end}')
							AND '{$date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->end}')
						{$joinSQL}
						RIGHT JOIN `user` u ON
								u.`employee_id` = `employee`.`employee_id`
							AND '{$date}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$this->end}')
							AND u.`status` = {$this->pub}
						WHERE
								`employee`.`status` = {$this->pub}
							{$whereSQL}
							AND '{$date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->end}')
							AND ('{valid_from}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->end}') OR '{valid_to}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->end}'))
							{$copyDialogEmpSearch}
						ORDER BY value
					",
					'array'	=> [["id" => "", "value" => ""]]
				]
			],
			'to_username'		=> $this->getDetailsHelper(200, $approverUserIdType, "SQL", "user", $userToWhereSQL, [], false, "", " AND 1=1 ", "`user`.`employee_id`", [["id" => "", "value" => ""]], 2, 1, 20),
			'to_process_id'		=> $this->getDetailsHelper(220, "combo", "ARR", "", "", [], false, "ALL", "", "", Yang::arrayMerge([["id" => "ALL", "value" => $this->all, "default" => 1]], $processIds), 2, 1, 20),
			'overwrite'			=> $this->getDetailsHelper(200, "combo", "ARR", "overwrite", "", [], false, "no", "", "", $type)
		];
		$columns['copyDialog']['from_username']['options']['sql']	= str_replace("'{date}'", "'" . $date . "'", $columns['copyDialog']['from_username']['options']['sql'] ?? "");
		$columns['copyDialog']['to_username']['options']['sql']		= str_replace("'{date}'", "'" . $date . "'", $columns['copyDialog']['to_username']['options']['sql'] ?? "");
		$columns['copyDialog']['from_username']['label_text']		= Dict::getValue("from_username");
		$columns['copyDialog']['to_username']['label_text']			= Dict::getValue("to_username");
		$columns['copyDialog']['from_process_id']['label_text']		= Dict::getValue("from_process_id");
		$columns['copyDialog']['to_process_id']['label_text']		= Dict::getValue("to_process_id");

		$columns = $this->columnRights($columns, true);

		if ($this->debugMode) { Yang::log(json_encode($columns['dhtmlxGrid'] ?? [], JSON_PRETTY_PRINT), "log", "dhtmlxCols"); }
		if ($this->debugMode) { Yang::log(json_encode($columns['copyDialog'] ?? [], JSON_PRETTY_PRINT), "log", "copyDialogCols"); }

		return $columns;
	}

	/**
     * Get computed value for approver user autocomplete
     * @param int $id
     * @return string
     */
	private function getApproverUserIdComputedValue(int $id = 0): string
    {
		$approverId	= AnyCache::get("UserExtendedRight.getApproverUserIdComputedValueId");
		$result		= AnyCache::get("UserExtendedRight.getApproverUserIdComputedValueResult");

		if ($id != (int)$approverId)
		{
			$SQL = "
				SELECT u.`username` AS value
				FROM `approver` AS a
				LEFT JOIN `user` AS u ON a.`approver_user_id` = u.`user_id` AND u.`status` = {$this->pub}
				WHERE a.`row_id` = {$id} AND a.`status` = {$this->pub}
				LIMIT 1
			";
			$result = dbFetchValue($SQL);

			AnyCache::set("UserExtendedRight.getApproverUserIdComputedValueId",		$id);
			AnyCache::set("UserExtendedRight.getApproverUserIdComputedValueResult",	$result);
		}

        return $result;
    }

	/**
     * Get computed value for related value autocomplete
     * @param int $id
     * @return string
     */
    private function getRelatedValueComputedValue(int $id = 0): string
    {
		$approverId	= AnyCache::get("UserExtendedRight.getRelatedValueComputedValueId");
		$result		= AnyCache::get("UserExtendedRight.getRelatedValueComputedValueResult");
		if ($id != (int)$approverId)
		{
			// Get approver data
			$SQL = "
				SELECT
					a.`related_model` AS related_model,
					a.`related_id` AS related_id,
					a.`related_value` AS related_value
				FROM `approver` AS a
				WHERE a.`row_id` = {$id} AND a.`status` = {$this->pub}
			";
			$approver = dbFetchRow($SQL);
			if (!is_array($approver)) { return ''; }

			// Get related data
			$model			= $approver["related_model"];
			$columnId		= $approver["related_id"];
			$columnValue	= $approver["related_value"];
			$table			= $model::model()->tableSchema->name;

			$columnNames	=
			[
				'Unit'             => 'a.`unit_name`',
				'Workgroup'        => 'a.`workgroup_name`',
				'Company'          => 'a.`company_name`',
				'Payroll'          => 'a.payroll_name',
				'EmployeeContract' => Employee::getParam('fullname_with_emp_id_ec_id', ["e", "ec"]),
				'CompanyOrgGroup1' => 'a.`company_org_group_name`',
				'CompanyOrgGroup2' => 'a.`company_org_group_name`',
				'CompanyOrgGroup3' => 'a.`company_org_group_name`',
				'Cost'             => 'a.`cost_name`',
				'Competency'       => 'a.`competency_name`'
			];

			if ($columnValue == 'ALL') {
				$result = Dict::getValue($table . "_id") . " " . $this->all;
			} else {
				if ($model == 'EmployeeContract')
				{
					$SQL = "
						SELECT
							{$columnNames[$model]} AS name
						FROM `employee_contract` AS ec
						LEFT JOIN `employee` AS e ON ec.`employee_id` = e.`employee_id` AND e.`status` = {$this->pub}
						WHERE
								ec.{$columnId} = '{$columnValue}'
							AND ec.`status` = {$this->pub}
							AND ec.`valid_from` <= IFNULL(e.`valid_to`, '{$this->end}') AND e.`valid_from` <= IFNULL(ec.`valid_to`, '{$this->end}')
							AND ec.`ec_valid_from` <= IFNULL(e.`valid_to`, '{$this->end}') AND e.`valid_from` <= IFNULL(ec.`ec_valid_to`, '{$this->end}')
						GROUP BY e.`employee_id`
					";
				} else {
					$SQL = "
						SELECT
							{$columnNames[$model]} AS name
						FROM
							{$table} a
						WHERE
							a.{$columnId} = '{$columnValue}'
							AND a.status = {$this->pub}
							AND CURDATE() BETWEEN a.valid_from and IFNULL(a.`valid_to`, '{$this->end}')
					";
				}
				$result = dbFetchValue($SQL);

				if (empty($result) && substr($columnValue, -1) == "*") {
					$result = $columnValue;
				}
			}

			AnyCache::set("UserExtendedRight.getRelatedValueComputedValueId",		$id);
			AnyCache::set("UserExtendedRight.getRelatedValueComputedValueResult",	$result);
		}

        return $result;
    }

	/**
	 * Related valuekat generáló SQL
	 * @param string $employeeJoinsSQL
	 * @param string $employeeWhereSQL
	 * @param string $date
	 * @param string $relatedValueSearch
	 * @param bool $approverUpload
	 * @return string
	 */
	protected function createRelatedValueSQL(string $employeeJoinsSQL = "", string $employeeWhereSQL = "", string $date = "", string $relatedValueSearch = "", bool $approverUpload = false): string
	{
		// ApproverUploadból nincs osztályváltozó
		$all = Dict::getValue("all");
		$pub = Status::PUBLISHED;
		$end = App::getSetting("defaultEnd");

		$unionAllSQL = "
			UNION
			SELECT
				'ALL' AS id,
				IF('Unit'				= '{related_model}', '" . Dict::getValue("unit_id")					. " {$all}',
				IF('Workgroup'			= '{related_model}', '" . Dict::getValue("workgroup_id")			. " {$all}',
				IF('Company'			= '{related_model}', '" . Dict::getValue("company_id")				. " {$all}',
				IF('Payroll'			= '{related_model}', '" . Dict::getValue("payroll_id")				. " {$all}',
				IF('EmployeeContract'	= '{related_model}', '" . Dict::getValue("employee_contract_id")	. " {$all}',
				IF('CompanyOrgGroup1'	= '{related_model}', '" . Dict::getValue("company_org_group1")		. " {$all}',
				IF('CompanyOrgGroup2'	= '{related_model}', '" . Dict::getValue("company_org_group2")		. " {$all}',
				IF('CompanyOrgGroup3'	= '{related_model}', '" . Dict::getValue("company_org_group3")		. " {$all}',
				IF('Competency'			= '{related_model}', '" . Dict::getValue("competency") 				. " {$all}',
				IF('Cost'				= '{related_model}', '" . Dict::getValue("cost") 					. " {$all}',
				IF('Employee'			= '{related_model}', '" . Dict::getValue("employee") 				. " {$all}',
				'')))))))))))
		";

		$relatedDefinitions =
        [
            "unit"                  => ["cogId" => false, "joinCompany" => true,	"joinPayroll" => true,	"relatedModel" => 'Unit'],
            "workgroup"             => ["cogId" => false, "joinCompany" => true,	"joinPayroll" => true,	"relatedModel" => 'Workgroup'],
            "company"               => ["cogId" => false, "joinCompany" => false,	"joinPayroll" => false,	"relatedModel" => 'Company'],
            "payroll"               => ["cogId" => false, "joinCompany" => true,	"joinPayroll" => false,	"relatedModel" => 'Payroll'],
            "company_org_group1"    => ["cogId" => true,  "joinCompany" => true,	"joinPayroll" => true,	"relatedModel" => 'CompanyOrgGroup1'],
            "company_org_group2"    => ["cogId" => true,  "joinCompany" => true,	"joinPayroll" => true,	"relatedModel" => 'CompanyOrgGroup2'],
            "company_org_group3"    => ["cogId" => true,  "joinCompany" => true,	"joinPayroll" => true,	"relatedModel" => 'CompanyOrgGroup3'],
            "cost"                  => ["cogId" => false, "joinCompany" => true,	"joinPayroll" => true,	"relatedModel" => 'Cost']
        ];


        $groupsSQL = "";
        foreach ($relatedDefinitions as $table => $tableData)
        {
			if ($tableData["joinCompany"] && !$approverUpload) {
				$companyJoinSQL = "
				LEFT JOIN `company` ON
						`company`.`company_id` = `{$table}`.`company_id`
					AND `company`.`status` = {$pub}
					AND '{$date}' BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$end}')
				";
			} else {
				$companyJoinSQL = "";
			}
			if ($tableData["joinPayroll"] && !$approverUpload) {
				$payrollJoinSQL = "
				LEFT JOIN `payroll` ON
						`payroll`.`payroll_id` = `{$table}`.`payroll_id`
					AND `payroll`.`status` = {$pub}
					AND '{$date}' BETWEEN `payroll`.`valid_from` AND IFNULL(`payroll`.`valid_to`, '{$end}')
				";
			} else {
				$payrollJoinSQL = "";
			}
            $tableId	= ($tableData["cogId"]) ? "company_org_group" : $table;
			$gargWhere	= ($approverUpload) ? "" : ($this->companyAndPayrollMode["companyMainData"][$tableData["relatedModel"]]["where"] ?? "");
			$gargWhere2	= ($approverUpload) ? "" : ($this->companyAndPayrollMode["companyMainData"][$tableData["relatedModel"]]["extraWhere"] ?? "");
			$dateWhere	= ($approverUpload) ? " AND CURDATE() BETWEEN `{$table}`.`valid_from` AND IFNULL(`{$table}`.`valid_to`, '{$end}') " : " AND ('{valid_from}' BETWEEN `{$table}`.`valid_from` AND IFNULL(`{$table}`.`valid_to`, '{$end}') OR '{valid_to}' BETWEEN `{$table}`.`valid_from` AND IFNULL(`{$table}`.`valid_to`, '{$end}')) ";

            $groupsSQL	.= "
                SELECT
                    `{$table}`.`{$tableId}_id` AS id,
                    `{$table}`.`{$tableId}_name` AS value
                FROM `{$table}`
				{$companyJoinSQL}
				{$payrollJoinSQL}
                WHERE
                        `{$table}`.`status` = {$pub}
					AND '" . ($tableData["relatedModel"] ?? "") . "' = '{related_model}'
                    {$gargWhere}
					{$gargWhere2}
					{$dateWhere}
				UNION
            ";
        }

		$name1 = Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]);
		$name2 = Employee::getParam('fullname', 'employee');
		$SQL = "
			SELECT
				id,
				value
			FROM (
				{$groupsSQL}
				SELECT
					`employee_contract`.`employee_contract_id` AS id,
					{$name1} AS value
				FROM `employee_contract`
				LEFT JOIN `employee` ON
						`employee_contract`.`employee_id` = `employee`.`employee_id`
					AND `employee`.`status` = {$pub}
					AND (CURDATE() BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$end}'))
					AND ('{valid_from}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$end}') OR '{valid_to}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$end}'))
				{$employeeJoinsSQL}
				WHERE
						`employee_contract`.`status`= {$pub}
					AND 'EmployeeContract' = '{related_model}'
					{$employeeWhereSQL}
					AND IF('{process_id}' = 'companyMainData', 'companyMainDataEmployeeContract' = 'companyMainDataEmployeeContract', 1=1 AND 'employee_contract' = 'employee_contract')
					AND (CURDATE() BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$end}'))
					AND (CURDATE() BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$end}'))
					AND ('{valid_from}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$end}') OR '{valid_to}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$end}'))
					AND `employee`.`row_id` IS NOT NULL
		";

		if (weHaveModule('ttwa-csm')) {
			$SQL .= "
				UNION
				SELECT
					comp.`competency_id` AS id,
					comp.`competency_name` AS value
				FROM `competency` comp
				WHERE
						comp.`status` = {$pub}
					AND 'Competency' = '{related_model}'
			";
		}

		$SQL .= "
				UNION
				SELECT
					`employee`.`employee_id` AS id,
					{$name2} AS value
				FROM `employee`
				LEFT JOIN `employee_contract` ON
						`employee_contract`.`employee_id` = `employee`.`employee_id`
					AND `employee_contract`.`status` = {$pub}
					AND CURDATE() BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$end}')
					AND CURDATE() BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$end}')
				{$employeeJoinsSQL}
				WHERE
						`employee`.`status` = {$pub}
					AND 'Employee' = '{related_model}'
					AND (CURDATE() BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$end}'))
					{$employeeWhereSQL}
					AND IF('{process_id}' = 'companyMainData', 'companyMainDataEmployee' = 'companyMainDataEmployee', 1=1 AND 'employee' = 'employee')
					AND ('{valid_from}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$end}') OR '{valid_to}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$end}'))
				{$unionAllSQL}
				) as value
				WHERE 1 = 1 {$relatedValueSearch}
				ORDER BY value
		";

		return str_replace("{date}", $date, $SQL);
	}

    /**
	 * Grid2 oszlop felirat definiálás
	 * @return array
	 */
	public function attributeLabels(): array
	{
		$labels = [];
		$labels['dhtmlxGrid'] =
		[
			'process_id'		=> Dict::getValue("process_id"),
			'related_model'		=> Dict::getValue("classification"),
			'fullname'			=> Dict::getValue("name"),
			'username'			=> Dict::getValue("user"),
			'approver_user_id'	=> Dict::getValue("user"),
			'related'			=> Dict::getValue("selected_group_or_employee"),
			'related_value'		=> Dict::getValue("selected_group_or_employee"),
			'level'				=> Dict::getValue("approver_process_level"),
			'note'				=> Dict::getValue("note"),
			'valid_from'		=> Dict::getValue("valid_from"),
			'valid_to'			=> Dict::getValue("valid_to"),
			'grid_valid_from'	=> Dict::getValue("valid_from"),
			'grid_valid_to'		=> Dict::getValue("valid_to")
		];

		$labels['copyDialog'] =
		[
			'from_fullname'	=> Dict::getValue("from_employee"),
			'from_username'	=> Dict::getValue("from_username"),
			'to_fullname'	=> Dict::getValue("to_employee"),
			'to_username'	=> Dict::getValue("to_username"),
			'valid_from'	=> Dict::getValue("valid_from"),
			'valid_to'		=> Dict::getValue("valid_to")
		];

		return $labels;
	}

    /**
	 * Biztonsági függvény #1
	 * @return array
	 */
	public function filters(): array {
        return [
            'accessControl', // perform access control for CRUD operations
        ];
    }

	/**
	 * Biztonsági függvény #2
	 * @return array
	 */
	public function accessRules(): array
	{
        return
		[
            [
				'allow', // allow authenticated users to access all actions
                'users' => ['@'],
            ],
            [
				'deny',  // deny all users
                'users' => ['*'],
            ],
        ];
    }

	/**
	 * Grid adattartalmat visszaadó SQL
	 * @return string
	 */
	private function getGridDataSQL(): string
	{
		$joinSQL	= $this->companyAndPayrollMode["joinSQL"] ?? ""; // Company mód GARG
		$whereSQL	= $this->companyAndPayrollMode["gridDataWhereSQL"] ?? ""; // WHERE - Company mód GARG
        $whereSQL	.= (!$this->isRoot) ? " AND u.`user_id` <> '{$this->rootUserId}' " : ""; // WHERE - root jogok jelenjenek-e meg
		$whereSQL	.= " AND (`employee_contract`.`employee_contract_id` = '{employee_contract}' OR '{employee_contract}' = '' OR '{employee_contract}' = 'ALL')"; // WHERE - contract szűrő
		$whereSQL	.= " AND (a.related_value = '{related}' OR '{related}' = '' OR '{related}' = 'ALL') "; // WHERE - csoport érték szűrő
		$whereSQL	.= " AND (a.related_model = '{related_model}' OR '{related_model}' = '' OR '{related_model}' = 'ALL') "; // WHERE - csoportosítás szűrő
		$whereSQL	.= " AND ('{process_id}' = '' OR '{process_id}' = 'ALL' OR a.`process_id` LIKE '{process_id}') "; // WHERE - folyamatszűrő
		$whereSQL	.= " AND '{date}' BETWEEN a.`valid_from` AND IFNULL(a.`valid_to`, '{$this->end}') "; // WHERE - dátumszűrő
		$whereSQL	.= " AND (u.`user_id` = '{user_id}' OR '{user_id}' = '' OR '{user_id}' = 'ALL') "; // WHERE - approver userszűrő
		$fullName	= Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]);

		$SQL = "
			SELECT
				a.process_id,
				a.related_model,
				IF(a.`related_model` = 'Unit',				IF(a.related_value = 'ALL', '" . Dict::getValue("unit_id")				. " {$this->all}', IFNULL(un.`unit_name`, a.`related_value`)),
				IF(a.`related_model` = 'Workgroup',			IF(a.related_value = 'ALL', '" . Dict::getValue("workgroup_id")			. " {$this->all}', IFNULL(w.`workgroup_name`, a.`related_value`)),
				IF(a.`related_model` = 'Company',			IF(a.related_value = 'ALL', '" . Dict::getValue("company_id") 			. " {$this->all}', IFNULL(c.`company_name`, a.`related_value`)),
				IF(a.`related_model` = 'Payroll',			IF(a.related_value = 'ALL', '" . Dict::getValue("payroll_id") 			. " {$this->all}', IFNULL(p.`payroll_name`, a.`related_value`)),
				IF(a.`related_model` = 'EmployeeContract',	IF(a.related_value = 'ALL', '" . Dict::getValue("employee_contract_id") . " {$this->all}', IF(ec.`row_id` IS NOT NULL, " . Employee::getParam('fullname_with_emp_id_ec_id', ["e", "ec"]) . " , a.`related_value`)),
				IF(a.`related_model` = 'CompanyOrgGroup1',	IF(a.related_value = 'ALL', '" . Dict::getValue("company_org_group1") 	. " {$this->all}', IFNULL(cog1.`company_org_group_name`, a.`related_value`)),
				IF(a.`related_model` = 'CompanyOrgGroup2',	IF(a.related_value = 'ALL', '" . Dict::getValue("company_org_group2") 	. " {$this->all}', IFNULL(cog2.`company_org_group_name`, a.`related_value`)),
				IF(a.`related_model` = 'CompanyOrgGroup3',	IF(a.related_value = 'ALL', '" . Dict::getValue("company_org_group3") 	. " {$this->all}', IFNULL(cog3.`company_org_group_name`, a.`related_value`)),
				IF(a.`related_model` = 'Cost',				IF(a.related_value = 'ALL', '" . Dict::getValue("cost") 				. " {$this->all}', IFNULL(cost2.`cost_name`, a.`related_value`)),
		";
		if (weHaveModule('ttwa-csm')) {
			$SQL .= "
				IF(a.`related_model`= 'Competency',			IF(a.related_value = 'ALL', '" . Dict::getValue("comp") 				. " {$this->all}', IFNULL(comp.`competency_name`, a.`related_value`)),
				IF(a.`related_value` = 'ALL', '" . Dict::getValue("employee_id") . " {$this->all}', IF(em.`row_id` IS NOT NULL, CONCAT(" . Employee::getParam('fullname', 'em') . ", ' - ', em.`emp_id`), a.`related_value`)
				)))))))))))	as related,
			";
		} else {
			$SQL .=	"
				IF(a.`related_value` = 'ALL', '" . Dict::getValue("employee_id") . " {$this->all}', IF(em.`row_id` IS NOT NULL, CONCAT(" . Employee::getParam('fullname', 'em') . ", ' - ', em.`emp_id`), a.`related_value`)
				)))))))))) as related,
			";
		}
		$SQL .= "
				{$fullName} AS fullname,
				a.approver_user_id,
				u.username,
				a.`level`,
				a.note,
				a.valid_from,
				a.valid_to,
				a.valid_from AS grid_valid_from,
				a.valid_to AS grid_valid_to,
				a.row_id
			FROM `approver` a
			LEFT JOIN (
				SELECT
					`related_model`,
					`related_id`
				FROM `approver_related_group`
				WHERE `status`= {$this->pub}
				GROUP BY `related_model`
			) arg ON arg.`related_model` = a.`related_model` AND arg.`related_id` = a.`related_id`
			JOIN `approver_related_type` art ON
					art.`related_model` = arg.`related_model`
				AND art.`related_id` = arg.`related_id`
				AND art.`status` = {$this->pub}
			LEFT JOIN `user` u ON
					a.`approver_user_id` = u.`user_id`
				AND u.`status` = {$this->pub}
				AND '{date}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$this->end}')
			LEFT JOIN `unit` un ON
					a.`related_value`= un.`unit_id`
				AND un.`status` = {$this->pub}
				AND a.`related_model` = 'Unit'
				AND '{date}' BETWEEN un.`valid_from` AND IFNULL(un.`valid_to`, '{$this->end}')
			LEFT JOIN `workgroup` w ON
					a.`related_value` = w.`workgroup_id`
				AND w.`status` = {$this->pub}
				AND a.`related_model` = 'Workgroup'
				AND '{date}' BETWEEN w.`valid_from` AND IFNULL(w.`valid_to`, '{$this->end}')
			LEFT JOIN `company` c ON
					a.`related_value` = c.`company_id`
				AND c.`status` = {$this->pub}
				AND a.`related_model` = 'Company'
				AND '{date}' BETWEEN c.`valid_from` AND IFNULL(c.`valid_to`, '{$this->end}')
			LEFT JOIN `payroll` p ON
					p.`payroll_id` = a.`related_value`
				AND p.`status` = {$this->pub}
				AND a.`related_model` = 'Payroll'
				AND '{date}' BETWEEN p.`valid_from` AND IFNULL(p.`valid_to`, '{$this->end}')
			LEFT JOIN `employee_contract` ec ON
					ec.`employee_contract_id` = a.`related_value`
				AND ec.`status` = {$this->pub}
				AND a.`related_model` = 'EmployeeContract'
				AND '{date}' BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '{$this->end}')
				AND '{date}' BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '{$this->end}')
			LEFT JOIN `employee` e ON
					ec.`employee_id` = e.`employee_id`
				AND e.`status` = {$this->pub}
				AND '{date}' BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '{$this->end}')
			LEFT JOIN `company_org_group1` cog1 ON
					cog1.`company_org_group_id` = a.`related_value`
				AND cog1.`status` = {$this->pub}
				AND a.`related_model` = 'CompanyOrgGroup1'
				AND '{date}' BETWEEN cog1.`valid_from` AND IFNULL(cog1.`valid_to`, '{$this->end}')
			LEFT JOIN `company_org_group2` cog2 ON
					cog2.`company_org_group_id` = a.`related_value`
				AND cog2.`status` = {$this->pub}
				AND a.`related_model` = 'CompanyOrgGroup2'
				AND '{date}' BETWEEN cog2.`valid_from` AND IFNULL(cog2.`valid_to`, '{$this->end}')
			LEFT JOIN `company_org_group3` cog3 ON
					cog3.`company_org_group_id` = a.`related_value`
				AND cog3.`status` = {$this->pub}
				AND a.`related_model` = 'CompanyOrgGroup3'
				AND '{date}' BETWEEN cog3.`valid_from` AND IFNULL(cog3.`valid_to`, '{$this->end}')
			LEFT JOIN `cost` cost2 ON
					cost2.`cost_id` = a.`related_value`
				AND cost2.`status` = {$this->pub}
				AND a.`related_model` = 'Cost'
				AND '{date}' BETWEEN cost2.`valid_from` AND IFNULL(cost2.`valid_to`, '{$this->end}')
			LEFT JOIN `employee` em ON
					em.`employee_id` = ec.`employee_id`
				AND em.`status` = {$this->pub}
				AND '{date}' BETWEEN em.`valid_from` AND IFNULL(em.`valid_to`, '{$this->end}')
			LEFT JOIN `employee` ON
					u.`employee_id` = `employee`.`employee_id`
				AND `employee`.`status` = {$this->pub}
				AND '{date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->end}')
			LEFT JOIN `employee_contract` ON
					u.`employee_id` = `employee_contract`.`employee_id`
				AND `employee_contract`.`status` = {$this->pub}
				AND '{date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->end}')
				AND '{date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->end}')
			{$joinSQL}
		";
		if (weHaveModule('ttwa-csm')) {
			$SQL .= "
			LEFT JOIN `competency` comp ON
					comp.`competency_id` = a.`related_value`
				AND a.`related_model` = 'Competency'
				AND comp.`status` = {$this->pub}
			";
		}
		$SQL .= "
			WHERE
					a.`status` = {$this->pub}
				{$whereSQL}
			ORDER BY `process_id`, `related_model`
		";

		if ($this->debugMode) { Yang::log($SQL, "log", "gridDataSQL"); }

		return $SQL;
	}

	/**
	 * Törlés
	 * @param string $modelName
	 * @param boolean $hasRight
	 * @return void
	 */
	public function actionDelete($modelName = null, $hasRight = false): void
	{
		$this->layout = "//layouts/ajax";

		/* Nincs jog az alap */
		$status = [
			'status'	=> 0,
			'pkSaved'	=> null,
			'error'		=> Dict::getValue("error_permission_denied")
		];

		$ids = explode(";", requestParam('ids'));

		/* Ha van joga és van kiválasztott sor */
		if (App::getRight($this->getControllerID(), "delete") && (!is_null($ids) && $ids != ""))
		{
			foreach ($ids as $rowId)
			{
				$a			= new Approver();
				$criteria	= new CDbCriteria();
				$criteria->condition = "`row_id` = '" . $rowId . "'";
				$aI			= $a->find($criteria);
				if ($aI)
				{
					$aI->status			= Status::DELETED;
					$aI->modified_by	= userID();
					$aI->modified_on	= date("Y-m-d H:i:s");
					if ($aI->validate()) {
						$aI->save();
						if (mb_substr($aI->related_value, -1) === "*" && $this->acceptRelatedValStarEnding) {
							ApproverStarTable::refreshStarTableByParams($aI->related_model, $aI->process_id);
						}
					}
					$status = [
						'status'	=> 1,
						'pkSaved'	=> null,
						'error'		=> "",
					];
					if ($aI->valid_to == null) { $aI->valid_to = $this->end; }
					/* Velux cég és számfejtési kör üzemmódban törli az alárendelt jogokat is */
					if (($this->companyAndPayrollMode["setting"] ?? 0)) {
						$this->deleteRelatedRights($aI->process_id, $aI->related_model, $aI->approver_user_id, $aI->related_value, "", $aI->valid_from, $aI->valid_to);
					}
					/* Ne kelljen ki és belépni újra */
					$this->delCache();
				}
			}
		}

		echo json_encode($status);
	}

	/**
	 * Mentés
	 * @param array $data
	 * @param string $modelName
	 * @param string $pk
	 * @param boolean $vOnly
	 * @param boolean $ret
	 * @param string $contentId
	 * @return void
	 */
	public function actionSave($data = [], $modelName = null, $pk = null, $vOnly = false, $ret = false, $contentId = null): void
	{
		$this->layout = "//layouts/ajax";
		$this->G2BInit();

		$generateFrom	= requestParam('generateFrom');
		$data			= requestParam('dialogInput_' . $generateFrom);
		$rowId			= $data["row_id"] ?? 0;
		$confirmed		= requestParam('confirmed');

		/* Ha módosul a korábbiról akkor kövesse le az alárendelt viszonyt velux cég és számfejtési kör üzemmódban (törölje a korábbi alárendeltet) */
		if ($rowId)
		{
        	$a				= Approver::model()->find('row_id = :id', [':id' => $rowId]);
			$oldValue		= $a->related_value;

			if (strpos($a->created_by, "XMLAutoImport") !== false && Yang::customerDbPatchName() == "flex")
			{
				$status = [
					'status'	=> 0,
					'pkSaved'	=> null,
					'error'		=> Dict::getValue("error_wd_rights_cant_be_edited"),
				];
				echo json_encode($status);
				exit();
			}

			$new	= new Approver();
			$new->attributes = $data;
			if ($new->validate() && ($this->companyAndPayrollMode["setting"] ?? 0))
			{
				if ($a->valid_to == null) { $a->valid_to = $this->end; }
				if ($confirmed) {
					$this->refreshStarSigns($a, $data);
					parent::actionSave($data, $modelName, $pk, $vOnly, $ret, $contentId);
					/* Velux cég és számfejtési kör üzemmódban törli az alárendelt jogokat */
					$this->deleteRelatedRights($a->process_id, $a->related_model, $a->approver_user_id, $oldValue, $data["related_value"] ?? "", $a->valid_from, $a->valid_to);
					/* Ne kelljen ki és belépni újra */
					$this->delCache();
					exit();
				} else {
					/* Kapcsolódó jogok törlése megerősítés */
					$status = [
						'status'	=> "confirmAction",
						'msg'		=> ["title" => Dict::getValue("confirm"), "body" => Dict::getValue("deleteRelatedRights")]
					];
					echo json_encode($status);
					exit();
				}
			} else {
				$error	= MyActiveForm::_validate($new);
				$arr	= (array)json_decode($error);
				/* Valamiért van 1 ilyen model error ami ellenére lefut azért minden, de ugyanaz a megerősítés és kapcsolódó jog törlés velux cég és számf. kör üzemmódban */
				if(count($arr) == 1 && strpos($arr[0][0] ?? "", 'error_approver_use') !== false && ($this->companyAndPayrollMode["setting"] ?? 0))
				{
					if ($a->valid_to == null) { $a->valid_to = $this->end; }
					if ($confirmed) {
						$this->refreshStarSigns($a, $data);
						parent::actionSave($data, $modelName, $pk, $vOnly, $ret, $contentId);
						/* Velux cég és számfejtési kör üzemmódban törli az alárendelt jogokat */
						$this->deleteRelatedRights($a->process_id, $a->related_model, $a->approver_user_id, $oldValue, $data["related_value"] ?? "", $a->valid_from, $a->valid_to);
						/* Ne kelljen ki és belépni újra */
						$this->delCache();
						exit();
					} else {
						/* Kapcsolódó jogok törlése megerősítés */
						$status = [
							'status'	=> "confirmAction",
							'msg'		=> ["title" => Dict::getValue("confirm"), "body" => Dict::getValue("deleteRelatedRights")]
						];
						echo json_encode($status);
						exit();
					}
				}
			}
        }

		parent::actionSave($data, $modelName, $pk, $vOnly, $ret, $contentId);
		if ($this->acceptRelatedValStarEnding && mb_substr(($data["related_value"] ?? ""), -1) === "*") {
			ApproverStarTable::refreshStarTableByParams($data["related_model"] ?? "", $data["process_id"] ?? "");
		}
		/* Ne kelljen ki és belépni újra */
		$this->delCache();
	}

	/**
	 * Csillag vézdődésű táblák frissítése
	 * @param Approver $approver
	 * @param array $data
	 * @return void
	 */
	public function refreshStarSigns($approver, array $data = []) :void
	{
		if ((mb_substr($approver->related_value, -1) === "*" || mb_substr(($data["related_value"] ?? ""), -1) === "*") && $this->acceptRelatedValStarEnding)
		{
			$oldModel	= $approver->related_model;
			$newModel	= $data["related_model"] ?? "";
			$refModels	= false;
			if ($oldModel != $newModel) {
				$refModels = true;
			}
			$oldPid		= $approver->process_id;
			$newPid		= $data["process_id"] ?? "";
			$refIds		= false;
			if ($oldPid != $newPid) {
				$refIds = true;
			}

			if ($refModels && $refIds)
			{
				ApproverStarTable::refreshStarTableByParams($oldModel, $oldPid);
				ApproverStarTable::refreshStarTableByParams($oldModel, $oldPid);
				ApproverStarTable::refreshStarTableByParams($newModel, $oldPid);
				ApproverStarTable::refreshStarTableByParams($newModel, $newPid);
			} else if ($refModels && !$refIds) {
				ApproverStarTable::refreshStarTableByParams($oldModel, $oldPid);
				ApproverStarTable::refreshStarTableByParams($newModel, $oldPid);
			} else if (!$refModels && $refIds) {
				ApproverStarTable::refreshStarTableByParams($oldModel, $oldPid);
				ApproverStarTable::refreshStarTableByParams($oldModel, $newPid);
			} else if (!$refModels && !$refIds) {
				ApproverStarTable::refreshStarTableByParams($oldModel, $oldPid);
			}
		}
	}

	/**
	 * Jogok másolása
	 * @return void
	 */
	public function actionCopyUserRights(): void
	{
		$input  = requestParam('dialogInput_copyDialog');
		$result = ['status' => 1, 'error' => ''];

		$missingValuesError		= true;
		$allToOneError			= false;
		$sameFromToUserError	= false;

		$input["from_username"]		= $input["from_username"] ?? null;
		$input["to_username"]		= $input["to_username"] ?? null;
		$input["valid_from"]		= $input["valid_from"] ?? null;
		$input["valid_to"]			= $input["valid_to"] ?? null;
		$input["from_process_id"]	= $input["from_process_id"] ?? "";
		$input["to_process_id"]		= $input["to_process_id"] ?? "";
		$input["overwrite"]			= $input["overwrite"] ?? "";

		if ($input["from_username"] != null && $input["to_username"] != null && $input["valid_from"] != null && $input["valid_to"] != null)
		{
			$missingValuesError = false;
			if ($input["from_process_id"] == 'ALL' && $input["to_process_id"] !== 'ALL') {
				$allToOneError = true;
			} elseif ($input["from_process_id"] == 'ALL' && $input["to_process_id"] == 'ALL') {
				if ($input["from_username"] === $input["to_username"]) {
					$sameFromToUserError = true;
				} else {
					if ($input["overwrite"] == "Y") {
						$this->copyRightsAllToAllOverwrite($input);
					} elseif ($input["overwrite"] == "N") {
						$this->copyRightsAllToAllAdd($input);
					}
				}
			} elseif ($input["from_process_id"] !== 'ALL') {
				if ($input["overwrite"] == "Y") {
					if ($input["to_process_id"] == 'ALL') {
						$this->copyRightsOneToAllOverwrite($input);
					} elseif ($input["to_process_id"] !== 'ALL') {
						$this->copyRightsOneToOneOverwrite($input);
					}
				} elseif ($input["overwrite"] == "N") {
					if ($input["to_process_id"] == 'ALL') {
						$this->copyRightsOneToAllAdd($input);
					} elseif ($input["to_process_id"] !== 'ALL') {
						$this->copyRightsOneToOneAdd($input);
					}
				}
			}
		}

		if ($missingValuesError) {
			$result["error"]	= Dict::getValue("empty_fields");
			$result['status'] 	= 0;
		} elseif ($allToOneError) {
			$result["error"] 	= Dict::getValue("error_all_to_one_copy");
			$result['status'] 	= 0;
		} elseif ($sameFromToUserError) {
			$result["error"] 	= Dict::getValue("same_from_to_user");
			$result['status'] 	= 0;
		} else {
			ApproverStarTable::refreshStarTables();
		}

		/* Ne kelljen ki és belépni újra */
		$this->delCache();
		echo json_encode($result);
	}

	/**
	 * Bácsi féle cache törlés
	 * @return void
	 */
	private function delCache(): void {
		AnyCache::destroyByName('getApproverReleatedGroupSQL');
		AnyCache::destroyByName('getApproverArray');
		unset($_SESSION["tiptime"]["visibility"]);
	}

	/**
	 * Kitörli az alárendelt jogokat módosításnál és törlésnél
	 * @param string $processId
	 * @param string $relatedModel
	 * @param string $approverUserId
	 * @param string $oldValue
	 * @param string $newValue
	 * @param string $validFrom
	 * @param string $validTo
	 * @return void
	 */
	private function deleteRelatedRights(string $processId = "", string $relatedModel = "", string $approverUserId = "", string $oldValue = "", string $newValue = "", string $validFrom = "", string $validTo = ""): void
	{
		if ($relatedModel == "Company" && $newValue != "ALL" && $oldValue != $newValue)
		{
			if ($processId == 'companyMainData') { $tables["Company"] = "company"; }

			$tables =
			[
				"Payroll"			=> "payroll",
				"Workgroup"			=> "workgroup",
				"Unit"				=> "unit",
				"CompanyOrgGroup1"	=> "company_org_group1",
				"CompanyOrgGroup2"	=> "company_org_group2",
				"CompanyOrgGroup3"	=> "company_org_group3"
			];

			if ($processId == 'companyMainData')
			{
				$tables["Company"] = "company";
				$lastvalue	= end($tables);
				$lastkey	= key($tables);
				$arr1		= array($lastkey=>$lastvalue);
				array_pop($tables);
				$tables		= array_merge($arr1,$tables);
			}

			// Eredeti működés
			if ($this->acceptRelatedValStarEnding == "0")
			{
				if ($oldValue == "ALL") {
					$whereSQL		= " AND t.`company_id` <> '{$newValue}' ";
					$whereEmpSQL	= " AND ((a.`related_model` = 'EmployeeContract' AND e.`company_id` <> '{$newValue}') OR (a.`related_model` = 'Employee' AND `employee`.`company_id` <> '{$newValue}')) ";
				} else {
					$whereSQL		= " AND t.`company_id` = '{$oldValue}' ";
					$whereEmpSQL	= " AND ((a.`related_model` = 'EmployeeContract' AND e.`company_id` = '{$oldValue}') OR (a.`related_model` = 'Employee' AND `employee`.`company_id` = '{$oldValue}')) ";
				}

				foreach ($tables as $k => $t)
				{
					if ($k == "CompanyOrgGroup1" || $k == "CompanyOrgGroup2" || $k == "CompanyOrgGroup3") { $id = "company_org_group_id"; } else { $id = $t . "_id"; }
					$updateGroupsSQL = "
						UPDATE `approver` a
						JOIN `{$t}` AS t ON
								t.`{$id}` = a.`related_value`
							AND t.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(t.`valid_to`, '{$this->end}') AND t.`valid_from` <= '{$validTo}'
						SET
							a.`status` = " . Status::DELETED . ",
							a.`modified_by` = '" . userID() . "',
							a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
						WHERE
								a.`approver_user_id` = '{$approverUserId}'
							AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
							AND a.`related_model` = '{$k}'
							AND a.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->end}') AND a.`valid_from` <= '{$validTo}'
							{$whereSQL}
					";
					dbExecute($updateGroupsSQL);
				}

				$updateEmployeeSQL = "
					UPDATE `approver` a
					LEFT JOIN `employee_contract` ON
							`employee_contract`.`employee_contract_id` = a.`related_value`
						AND a.`related_model` = 'EmployeeContract'
						AND `employee_contract`.`status` = {$this->pub}
						AND '{$validFrom}' <= IFNULL(`employee_contract`.`valid_to`, '{$this->end}') AND `employee_contract`.`valid_from` <= '{$validTo}'
						AND '{$validFrom}' <= IFNULL(`employee_contract`.`ec_valid_to`, '{$this->end}') AND `employee_contract`.`ec_valid_from` <= '{$validTo}'
					LEFT JOIN `employee` e ON
							e.`employee_id` = `employee_contract`.`employee_id`
						AND a.`related_model` = 'EmployeeContract'
						AND e.`status` = {$this->pub}
						AND '{$validFrom}' <= IFNULL(e.`valid_to`, '{$this->end}') AND e.`valid_from` <= '{$validTo}'
					LEFT JOIN `employee` ON
							`employee`.`employee_id` = a.`related_value`
						AND a.`related_model` = 'Employee'
						AND `employee`.`status` = {$this->pub}
						AND '{$validFrom}' <= IFNULL(`employee`.`valid_to`, '{$this->end}') AND `employee`.`valid_from` <= '{$validTo}'
					SET
						a.`status` = " . Status::DELETED . ",
						a.`modified_by` = '" . userID() . "',
						a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
					WHERE
							a.`approver_user_id` = '{$approverUserId}'
						AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
						AND a.`status` = {$this->pub}
						AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->end}') AND a.`valid_from` <= '{$validTo}'
						{$whereEmpSQL}
				";
				dbExecute($updateEmployeeSQL);
			// Flexes új wildcard működés
			} else
			{
				$deleteRequired = false;
				// Expressionből expression, de más / szűkebb körbe
				if (substr($newValue, -1) == "*" && substr($oldValue, -1) == "*")
				{
					$lenghtOld	= strlen($oldValue);
					$lenghtNew	= strlen($newValue);
					if ($lenghtOld == $lenghtNew) {
						// pl.: HU-BU* -> HU-ZA* --> törölni kell
						$deleteRequired = true;
					} else if ($lenghtOld > $lenghtNew)
					{
						// pl.: HU-BUD* -> HU-BA* --> törölni kell
						// pl.: HU-BUD* -> HU-BU* --> nem kell törölni
						if (substr($newValue, 0, -1) != substr(substr($oldValue, -1), 0, strlen(substr($newValue, 0, -1)))) {
							$deleteRequired = true;
						}
					} else if ($lenghtOld < $lenghtNew) {
						// pl.: HU-BU* -> HU-BUD* --> törölni kell
						// pl.: HU-BU* -> HU-ZAL* --> törölni kell
						$deleteRequired = true;
					}
				// Expressionbe normál értékről
				// pl.: 1436(=HU-ZAL-AKÁRMI) -> HU-ZA* --> nem kell törölni
				// pl.: 1436(=HU-ZAL-AKÁRMI) -> HU-BU* --> törölni kell
				} else if (substr($newValue, -1) == "*" && substr($oldValue, -1) != "*")
				{
					$companyName = dbFetchValue("
						SELECT DISTINCT `company_name`
						FROM `company`
						WHERE
								`company_name` LIKE '" . substr($newValue, -1) . "%" . "'
							AND	`company_id` = '{$oldValue}'
							AND `status` = {$this->pub}
							AND `valid_from` <= '{$validTo}'
							AND '{$validFrom}' <= IFNULL(`valid_to`, '{$this->end}')
					");
					if ($companyName === false) {
						$deleteRequired = true;
					}
				// Expressionből normál értékbe
				// pl.: HU-BU* -> 1436(=HU-ZAL-AKÁRMI) --> törölni kell
				} else if (substr($oldValue, -1) == "*" && substr($newValue, -1) != "*") {
					$deleteRequired = true;
				// Eredeti, de lehet * jog
				} else if (substr($oldValue, -1) != "*" && substr($newValue, -1) != "*") {
					$deleteRequired = true;
				}

				// Törlés
				if ($deleteRequired)
				{
					foreach ($tables as $k => $t)
					{
						if ($k == "CompanyOrgGroup1" || $k == "CompanyOrgGroup2" || $k == "CompanyOrgGroup3") { $id = "company_org_group_id"; } else { $id = $t . "_id"; }
						$updateGroupsSQL = "
							UPDATE `approver` a
							-- Csoportosítás tábla (id szerint, % jogokat nem bántjuk)
							JOIN `{$t}` AS t ON
									t.`status` = {$this->pub}
								AND	t.`{$id}` = a.`related_value`
								AND '{$validFrom}' <= IFNULL(t.`valid_to`, '{$this->end}') AND t.`valid_from` <= '{$validTo}'
							-- Csoportosítás cége (company_id szerint ALL-osokat nem bántjuk, illetve aszerint ahol a törölt / módosított jog cég-e van)
							JOIN `company` AS c ON
									c.`status` = {$this->pub}
								AND '{$validFrom}' <= IFNULL(c.`valid_to`, '{$this->end}') AND c.`valid_from` <= '{$validTo}'
								AND c.`company_id` = t.`company_id`
								AND (c.`company_id` = '{$oldValue}' OR c.`company_name` LIKE '" . substr($oldValue, 0, -1) . "%" . "')
							-- Mivel előfordulhat olyan hogy %-os jogot módosított, de egyébként másik jog miatt még érvényes marad ez is, vagy az új jog miatt még1x approver tábla
							LEFT JOIN `approver` a2 ON
									a2.`status` = {$this->pub}
								AND a2.`row_id` <> a.`row_id`
								AND '{$validFrom}' <= IFNULL(a2.`valid_to`, '{$this->end}') AND a2.`valid_from` <= '{$validTo}'
								AND a2.`approver_user_id` = '{$approverUserId}'
								AND IF('{$processId}' = 'companyMainData', a2.`process_id` = 'companyMainData', a2.`process_id` = a.`process_id`)
								AND a2.`related_model` = 'Company'
								AND a2.`related_id` = 'company_id'
								AND (a2.`related_value` = c.`company_id` OR c.`company_name` LIKE CONCAT(LEFT(a2.`related_value`, CHAR_LENGTH(a2.`related_value`) - 1), '%') OR a2.`related_value` = 'ALL')
							SET
								a.`status` = " . Status::DELETED . ",
								a.`modified_by` = '" . userID() . "',
								a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
							WHERE
									a.`approver_user_id` = '{$approverUserId}'
								AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
								AND IF('{$t}' = 'company' AND '{$processId}' = 'companyMainData', a.`process_id` <> 'companyMainData', 1=1)
								AND a.`related_model` = '{$k}'
								AND a.`status` = {$this->pub}
								AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->end}') AND a.`valid_from` <= '{$validTo}'
								AND a2.`row_id` IS NULL
								AND a.`related_value` <> 'ALL'
						";
						dbExecute($updateGroupsSQL);
					}

					$updateEmployeeContractSQL = "
						UPDATE `approver` a
						JOIN `employee_contract` ON
								`employee_contract`.`employee_contract_id` = a.`related_value`
							AND `employee_contract`.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(`employee_contract`.`valid_to`, '{$this->end}') AND `employee_contract`.`valid_from` <= '{$validTo}'
							AND '{$validFrom}' <= IFNULL(`employee_contract`.`ec_valid_to`, '{$this->end}') AND `employee_contract`.`ec_valid_from` <= '{$validTo}'
						JOIN `employee` e ON
								e.`employee_id` = `employee_contract`.`employee_id`
							AND e.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(e.`valid_to`, '{$this->end}') AND e.`valid_from` <= '{$validTo}'
						JOIN `company` c ON
								c.`company_id` = e.`company_id`
							AND c.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(c.`valid_to`, '{$this->end}') AND c.`valid_from` <= '{$validTo}'
							AND (c.`company_id` = '{$oldValue}' OR c.`company_name` LIKE '" . substr($oldValue, 0, -1) . "%" . "')
						LEFT JOIN `approver` a2 ON
								a2.`status` = {$this->pub}
							AND a2.`row_id` <> a.`row_id`
							AND '{$validFrom}' <= IFNULL(a2.`valid_to`, '{$this->end}') AND a2.`valid_from` <= '{$validTo}'
							AND a2.`approver_user_id` = '{$approverUserId}'
							AND IF('{$processId}' = 'companyMainData', a2.`process_id` = 'companyMainData', a2.`process_id` = a.`process_id`)
							AND a2.`related_model` = 'Company'
							AND a2.`related_id` = 'company_id'
							AND (a2.`related_value` = c.`company_id` OR c.`company_name` LIKE CONCAT(LEFT(a2.`related_value`, CHAR_LENGTH(a2.`related_value`) - 1), '%') OR a2.`related_value` = 'ALL')
						SET
							a.`status` = " . Status::DELETED . ",
							a.`modified_by` = '" . userID() . "',
							a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
						WHERE
								a.`approver_user_id` = '{$approverUserId}'
							AND a.`related_model` = 'EmployeeContract'
							AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
							AND a.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->end}') AND a.`valid_from` <= '{$validTo}'
							AND a2.`row_id` IS NULL
							AND a.`related_value` <> 'ALL'
					";
					dbExecute($updateEmployeeContractSQL);
					$updateEmployeeSQL = "
						UPDATE `approver` a
						JOIN `employee` ON
								`employee`.`employee_id` = a.`related_value`
							AND `employee`.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(`employee`.`valid_to`, '{$this->end}') AND `employee`.`valid_from` <= '{$validTo}'
						JOIN `company` c ON
								c.`company_id` = `employee`.`company_id`
							AND c.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(c.`valid_to`, '{$this->end}') AND c.`valid_from` <= '{$validTo}'
							AND (c.`company_id` = '{$oldValue}' OR c.`company_name` LIKE '" . substr($oldValue, 0, -1) . "%" . "')
						LEFT JOIN `approver` a2 ON
								a2.`status` = {$this->pub}
							AND a2.`row_id` <> a.`row_id`
							AND '{$validFrom}' <= IFNULL(a2.`valid_to`, '{$this->end}') AND a2.`valid_from` <= '{$validTo}'
							AND a2.`approver_user_id` = '{$approverUserId}'
							AND IF('{$processId}' = 'companyMainData', a2.`process_id` = 'companyMainData', a2.`process_id` = a.`process_id`)
							AND a2.`related_model` = 'Company'
							AND a2.`related_id` = 'company_id'
							AND (a2.`related_value` = c.`company_id` OR c.`company_name` LIKE CONCAT(LEFT(a2.`related_value`, CHAR_LENGTH(a2.`related_value`) - 1), '%') OR a2.`related_value` = 'ALL')
						SET
							a.`status` = " . Status::DELETED . ",
							a.`modified_by` = '" . userID() . "',
							a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
						WHERE
								a.`approver_user_id` = '{$approverUserId}'
							AND a.`related_model` = 'Employee'
							AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
							AND a.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->end}') AND a.`valid_from` <= '{$validTo}'
							AND a2.`row_id` IS NULL
							AND a.`related_value` <> 'ALL'
					";
					dbExecute($updateEmployeeSQL);
				}
			}
		} else if ($relatedModel == "Payroll" && $newValue != "ALL" && $oldValue != $newValue)
		{
			$tables =
			[
				"Workgroup"			=> "workgroup",
				"Unit"				=> "unit",
				"CompanyOrgGroup1"	=> "company_org_group1",
				"CompanyOrgGroup2"	=> "company_org_group2",
				"CompanyOrgGroup3"	=> "company_org_group3"
			];

			if ($processId == 'companyMainData')
			{
				$tables["Payroll"] = "payroll";
				$lastvalue	= end($tables);
				$lastkey	= key($tables);
				$arr1		= array($lastkey=>$lastvalue);
				array_pop($tables);
				$tables		= array_merge($arr1,$tables);
			}

			// Eredeti működés
			if ($this->acceptRelatedValStarEnding == "0")
			{
				if ($oldValue == "ALL") {
					$whereSQL		= " AND t.`payroll_id` <> '{$newValue}' ";
					$whereEmpSQL	= " AND ((a.`related_model` = 'EmployeeContract' AND e.`payroll_id` <> '{$newValue}') OR (a.`related_model` = 'Employee' AND `employee`.`payroll_id` <> '{$newValue}')) ";
				} else {
					$whereSQL		= " AND t.`payroll_id` = '{$oldValue}' ";
					$whereEmpSQL	= " AND ((a.`related_model` = 'EmployeeContract' AND e.`payroll_id` = '{$oldValue}') OR (a.`related_model` = 'Employee' AND `employee`.`payroll_id` = '{$oldValue}')) ";
				}

				foreach ($tables as $k => $t)
				{
					if ($k == "CompanyOrgGroup1" || $k == "CompanyOrgGroup2" || $k == "CompanyOrgGroup3") { $id = "company_org_group_id"; } else { $id = $t . "_id"; }
					$updateGroupsSQL = "
						UPDATE `approver` a
						JOIN `{$t}` AS t ON
								t.`{$id}` = a.`related_value`
							AND t.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(t.`valid_to`, '{$this->end}') AND t.`valid_from` <= '{$validTo}'
						SET
							a.`status` = " . Status::DELETED . ",
							a.`modified_by` = '" . userID() . "',
							a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
						WHERE
								a.`approver_user_id` = '{$approverUserId}'
							AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
							AND a.`related_model` = '{$k}'
							AND a.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->end}') AND a.`valid_from` <= '{$validTo}'
							{$whereSQL}
					";
					dbExecute($updateGroupsSQL);
				}

				$updateEmployeeSQL = "
					UPDATE `approver` a
					LEFT JOIN `employee_contract` ON
							`employee_contract`.`employee_contract_id` = a.`related_value`
						AND a.`related_model` = 'EmployeeContract'
						AND `employee_contract`.`status` = {$this->pub}
						AND '{$validFrom}' <= IFNULL(`employee_contract`.`valid_to`, '{$this->end}') AND `employee_contract`.`valid_from` <= '{$validTo}'
						AND '{$validFrom}' <= IFNULL(`employee_contract`.`ec_valid_to`, '{$this->end}') AND `employee_contract`.`ec_valid_from` <= '{$validTo}'
					LEFT JOIN `employee` e ON
							e.`employee_id` = `employee_contract`.`employee_id`
						AND a.`related_model` = 'EmployeeContract'
						AND e.`status` = {$this->pub}
						AND '{$validFrom}' <= IFNULL(e.`valid_to`, '{$this->end}') AND e.`valid_from` <= '{$validTo}'
					LEFT JOIN `employee` ON
							`employee`.`employee_id` = a.`related_value`
						AND a.`related_model` = 'Employee'
						AND `employee`.`status` = {$this->pub}
						AND '{$validFrom}' <= IFNULL(`employee`.`valid_to`, '{$this->end}') AND `employee`.`valid_from` <= '{$validTo}'
					SET
						a.`status` = " . Status::DELETED . ",
						a.`modified_by` = '" . userID() . "',
						a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
					WHERE
							a.`approver_user_id` = '{$approverUserId}'
						AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
						AND a.`status` = {$this->pub}
						AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->end}') AND a.`valid_from` <= '{$validTo}'
						{$whereEmpSQL}
				";
				dbExecute($updateEmployeeSQL);
			// Flexes új wildcard működés
			} else
			{
				$deleteRequired = false;
				// Expressionből expression, de más / szűkebb körbe
				if (substr($newValue, -1) == "*" && substr($oldValue, -1) == "*")
				{
					$lenghtOld	= strlen($oldValue);
					$lenghtNew	= strlen($newValue);
					if ($lenghtOld == $lenghtNew) {
						// pl.: HU-BU* -> HU-ZA* --> törölni kell
						$deleteRequired = true;
					} else if ($lenghtOld > $lenghtNew)
					{
						// pl.: HU-BUD* -> HU-BA* --> törölni kell
						// pl.: HU-BUD* -> HU-BU* --> nem kell törölni
						if (substr($newValue, 0, -1) != substr(substr($oldValue, -1), 0, strlen(substr($newValue, 0, -1)))) {
							$deleteRequired = true;
						}
					} else if ($lenghtOld < $lenghtNew) {
						// pl.: HU-BU* -> HU-BUD* --> törölni kell
						// pl.: HU-BU* -> HU-ZAL* --> törölni kell
						$deleteRequired = true;
					}
				// Expressionbe normál értékről
				// pl.: 1436(=HU-ZAL-AKÁRMI) -> HU-ZA* --> nem kell törölni
				// pl.: 1436(=HU-ZAL-AKÁRMI) -> HU-BU* --> törölni kell
				} else if (substr($newValue, -1) == "*" && substr($oldValue, -1) != "*")
				{
					$payrollName = dbFetchValue("
						SELECT DISTINCT `payroll_name`
						FROM `payroll`
						WHERE
								`payroll_name` LIKE '" . substr($newValue, -1) . "%" . "'
							AND	`payroll_id` = '{$oldValue}'
							AND `status` = {$this->pub}
							AND `valid_from` <= '{$validTo}'
							AND '{$validFrom}' <= IFNULL(`valid_to`, '{$this->end}')
					");
					if ($payrollName === false) {
						$deleteRequired = true;
					}
				// Expressionből normál értékbe
				// pl.: HU-BU* -> 1436(=HU-ZAL-AKÁRMI) --> törölni kell
				} else if (substr($oldValue, -1) == "*" && substr($newValue, -1) != "*") {
					$deleteRequired = true;
				// Eredeti, de lehet * jog
				} else if (substr($oldValue, -1) != "*" && substr($newValue, -1) != "*") {
					$deleteRequired = true;
				}

				// Törlés
				if ($deleteRequired)
				{
					foreach ($tables as $k => $t)
					{
						if ($k == "CompanyOrgGroup1" || $k == "CompanyOrgGroup2" || $k == "CompanyOrgGroup3") { $id = "company_org_group_id"; } else { $id = $t . "_id"; }
						$updateGroupsSQL = "
							UPDATE `approver` a
							-- Csoportosítás tábla (id szerint, % jogokat nem bántjuk)
							JOIN `{$t}` AS t ON
									t.`status` = {$this->pub}
								AND	t.`{$id}` = a.`related_value`
								AND '{$validFrom}' <= IFNULL(t.`valid_to`, '{$this->end}') AND t.`valid_from` <= '{$validTo}'
							-- Csoportosítás cége (payroll_id szerint ALL-osokat nem bántjuk, illetve aszerint ahol a törölt / módosított jog cég-e van)
							JOIN `payroll` AS p ON
									p.`status` = {$this->pub}
								AND '{$validFrom}' <= IFNULL(p.`valid_to`, '{$this->end}') AND p.`valid_from` <= '{$validTo}'
								AND p.`payroll_id` = t.`payroll_id`
								AND (p.`payroll_id` = '{$oldValue}' OR p.`payroll_name` LIKE '" . substr($oldValue, 0, -1) . "%" . "')
							-- Mivel előfordulhat olyan hogy %-os jogot módosított, de egyébként másik jog miatt még érvényes marad ez is, vagy az új jog miatt még1x approver tábla
							LEFT JOIN `approver` a2 ON
									a2.`status` = {$this->pub}
								AND a2.`row_id` <> a.`row_id`
								AND '{$validFrom}' <= IFNULL(a2.`valid_to`, '{$this->end}') AND a2.`valid_from` <= '{$validTo}'
								AND a2.`approver_user_id` = '{$approverUserId}'
								AND IF('{$processId}' = 'companyMainData', a2.`process_id` = 'companyMainData', a2.`process_id` = a.`process_id`)
								AND a2.`related_model` = 'Payroll'
								AND a2.`related_id` = 'payroll_id'
								AND (a2.`related_value` = p.`payroll_id` OR p.`payroll_name` LIKE CONCAT(LEFT(a2.`related_value`, CHAR_LENGTH(a2.`related_value`) - 1), '%') OR a2.`related_value` = 'ALL')
							SET
								a.`status` = " . Status::DELETED . ",
								a.`modified_by` = '" . userID() . "',
								a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
							WHERE
									a.`approver_user_id` = '{$approverUserId}'
								AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
								AND IF('{$t}' = 'payroll' AND '{$processId}' = 'companyMainData', a.`process_id` <> 'companyMainData', 1=1)
								AND a.`related_model` = '{$k}'
								AND a.`status` = {$this->pub}
								AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->end}') AND a.`valid_from` <= '{$validTo}'
								AND a2.`row_id` IS NULL
								AND a.`related_value` <> 'ALL'
						";
						dbExecute($updateGroupsSQL);
					}

					$updateEmployeeContractSQL = "
						UPDATE `approver` a
						JOIN `employee_contract` ON
								`employee_contract`.`employee_contract_id` = a.`related_value`
							AND `employee_contract`.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(`employee_contract`.`valid_to`, '{$this->end}') AND `employee_contract`.`valid_from` <= '{$validTo}'
							AND '{$validFrom}' <= IFNULL(`employee_contract`.`ec_valid_to`, '{$this->end}') AND `employee_contract`.`ec_valid_from` <= '{$validTo}'
						JOIN `employee` e ON
								e.`employee_id` = `employee_contract`.`employee_id`
							AND e.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(e.`valid_to`, '{$this->end}') AND e.`valid_from` <= '{$validTo}'
						JOIN `payroll` p ON
								p.`payroll_id` = e.`payroll_id`
							AND p.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(p.`valid_to`, '{$this->end}') AND p.`valid_from` <= '{$validTo}'
							AND (p.`payroll_id` = '{$oldValue}' OR p.`payroll_name` LIKE '" . substr($oldValue, 0, -1) . "%" . "')
						LEFT JOIN `approver` a2 ON
								a2.`status` = {$this->pub}
							AND a2.`row_id` <> a.`row_id`
							AND '{$validFrom}' <= IFNULL(a2.`valid_to`, '{$this->end}') AND a2.`valid_from` <= '{$validTo}'
							AND a2.`approver_user_id` = '{$approverUserId}'
							AND IF('{$processId}' = 'companyMainData', a2.`process_id` = 'companyMainData', a2.`process_id` = a.`process_id`)
							AND a2.`related_model` = 'Payroll'
							AND a2.`related_id` = 'payroll_id'
							AND (a2.`related_value` = p.`payroll_id` OR p.`payroll_name` LIKE CONCAT(LEFT(a2.`related_value`, CHAR_LENGTH(a2.`related_value`) - 1), '%') OR a2.`related_value` = 'ALL')
						SET
							a.`status` = " . Status::DELETED . ",
							a.`modified_by` = '" . userID() . "',
							a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
						WHERE
								a.`approver_user_id` = '{$approverUserId}'
							AND a.`related_model` = 'EmployeeContract'
							AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
							AND a.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->end}') AND a.`valid_from` <= '{$validTo}'
							AND a2.`row_id` IS NULL
							AND a.`related_value` <> 'ALL'
					";
					dbExecute($updateEmployeeContractSQL);
					$updateEmployeeSQL = "
						UPDATE `approver` a
						JOIN `employee` ON
								`employee`.`employee_id` = a.`related_value`
							AND `employee`.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(`employee`.`valid_to`, '{$this->end}') AND `employee`.`valid_from` <= '{$validTo}'
						JOIN `payroll` p ON
								p.`payroll_id` = `employee`.`payroll_id`
							AND p.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(p.`valid_to`, '{$this->end}') AND p.`valid_from` <= '{$validTo}'
							AND (p.`payroll_id` = '{$oldValue}' OR p.`payroll_name` LIKE '" . substr($oldValue, 0, -1) . "%" . "')
						LEFT JOIN `approver` a2 ON
								a2.`status` = {$this->pub}
							AND a2.`row_id` <> a.`row_id`
							AND '{$validFrom}' <= IFNULL(a2.`valid_to`, '{$this->end}') AND a2.`valid_from` <= '{$validTo}'
							AND a2.`approver_user_id` = '{$approverUserId}'
							AND IF('{$processId}' = 'companyMainData', a2.`process_id` = 'companyMainData', a2.`process_id` = a.`process_id`)
							AND a2.`related_model` = 'Payroll'
							AND a2.`related_id` = 'payroll_id'
							AND (a2.`related_value` = p.`payroll_id` OR p.`payroll_name` LIKE CONCAT(LEFT(a2.`related_value`, CHAR_LENGTH(a2.`related_value`) - 1), '%') OR a2.`related_value` = 'ALL')
						SET
							a.`status` = " . Status::DELETED . ",
							a.`modified_by` = '" . userID() . "',
							a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
						WHERE
								a.`approver_user_id` = '{$approverUserId}'
							AND a.`related_model` = 'Employee'
							AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
							AND a.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->end}') AND a.`valid_from` <= '{$validTo}'
							AND a2.`row_id` IS NULL
							AND a.`related_value` <> 'ALL'
					";
					dbExecute($updateEmployeeSQL);
				}
			}

		} else if ($newValue != "ALL" && $oldValue != $newValue && ($relatedModel == "Workgroup" || $relatedModel == "Unit" || $relatedModel == "CompanyOrgGroup1" || $relatedModel == "CompanyOrgGroup2" || $relatedModel == "CompanyOrgGroup3"))
		{
			$tables =
			[
				"Workgroup"			=> "workgroup",
				"Unit"				=> "unit",
				"CompanyOrgGroup1"	=> "company_org_group1",
				"CompanyOrgGroup2"	=> "company_org_group2",
				"CompanyOrgGroup3"	=> "company_org_group3"
			];
			$contractOrEmployee =
			[
				"workgroup" 			=> "employee_contract",
				"unit"					=> "employee",
				"company_org_group1"	=> "employee",
				"company_org_group2"	=> "employee",
				"company_org_group3"	=> "employee"
			];

			$tables[$relatedModel]						= $tables[$relatedModel] ?? "";
			$contractOrEmployee[$tables[$relatedModel]] = $contractOrEmployee[$tables[$relatedModel]] ?? "";

			// Eredeti működés
			if ($this->acceptRelatedValStarEnding == "0")
			{
				if ($processId == 'companyMainData')
				{
					if ($tables[$relatedModel] == "CompanyOrgGroup1" || $tables[$relatedModel] == "CompanyOrgGroup2" || $tables[$relatedModel] == "CompanyOrgGroup3") { $id = "company_org_group_id"; } else { $id = $tables[$relatedModel] . "_id"; }
					if ($oldValue == "ALL") {
						$whereSQL = "AND t.`{$id}` <> '{$newValue}'";
					} else {
						$whereSQL = "AND t.`{$id}` = '{$oldValue}'";
					}
					$updateGroupsSQL = "
						UPDATE `approver` a
						JOIN `{$tables[$relatedModel]}` AS t ON
								t.`{$id}` = a.`related_value`
							AND t.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(t.`valid_to`, '{$this->end}') AND t.`valid_from` <= '{$validTo}'
						SET
							a.`status` = " . Status::DELETED . ",
							a.`modified_by` = '" . userID() . "',
							a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
						WHERE
								a.`approver_user_id` = '{$approverUserId}'
							AND a.`related_model` = '{$tables[$relatedModel]}'
							AND a.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->end}') AND a.`valid_from` <= '{$validTo}'
							{$whereSQL}
					";
					dbExecute($updateGroupsSQL);
				}

				if ($oldValue == "ALL") {
					$whereEmpContractSQL	= " AND (a.`related_model` = 'EmployeeContract' AND " . EmployeeGroup::getActiveGroupSQL($tables[$relatedModel] . "_id", $contractOrEmployee[$tables[$relatedModel]]) . " <> '{$newValue}') ";
					$whereEmpSQL			= " AND (a.`related_model` = 'Employee' AND " . EmployeeGroup::getActiveGroupSQL($tables[$relatedModel] . "_id", $contractOrEmployee[$tables[$relatedModel]]) . " <> '{$newValue}') ";
				} else {
					$whereEmpContractSQL	= "AND (a.`related_model` = 'EmployeeContract' AND " . EmployeeGroup::getActiveGroupSQL($tables[$relatedModel] . "_id", $contractOrEmployee[$tables[$relatedModel]]) . " = '{$oldValue}') ";
					$whereEmpSQL			= "AND (a.`related_model` = 'Employee' AND " . EmployeeGroup::getActiveGroupSQL($tables[$relatedModel] . "_id", $contractOrEmployee[$tables[$relatedModel]]) . " = '{$oldValue}') ";
				}

				$updateEmployeeContractSQL = "
					UPDATE `approver` a
					LEFT JOIN `employee_contract` ON
							`employee_contract`.`employee_contract_id` = a.`related_value`
						AND a.`related_model` = 'EmployeeContract'
						AND `employee_contract`.`status` = {$this->pub}
						AND '{$validFrom}' <= IFNULL(`employee_contract`.`valid_to`, '{$this->end}') AND `employee_contract`.`valid_from` <= '{$validTo}'
						AND '{$validFrom}' <= IFNULL(`employee_contract`.`ec_valid_to`, '{$this->end}') AND `employee_contract`.`ec_valid_from` <= '{$validTo}'
					LEFT JOIN `employee` ON
							`employee`.`employee_id` = `employee_contract`.`employee_id`
						AND a.`related_model` = 'EmployeeContract'
						AND `employee`.`status` = {$this->pub}
						AND '{$validFrom}' <= IFNULL(`employee`.`valid_to`, '{$this->end}') AND `employee`.`valid_from` <= '{$validTo}'
					" . EmployeeGroup::getLeftJoinSQLWithoutCal($tables[$relatedModel], "employee_contract", "", "", "employee", "'{$validFrom}'", "'{$validTo}'") . "
					SET
						a.`status` = " . Status::DELETED . ",
						a.`modified_by` = '" . userID() . "',
						a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
					WHERE
							a.`approver_user_id` = '{$approverUserId}'
						AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
						AND a.`status` = {$this->pub}
						AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->end}') AND a.`valid_from` <= '{$validTo}'
						{$whereEmpContractSQL}
				";
				dbExecute($updateEmployeeContractSQL);

				$updateEmployeeSQL = "
					UPDATE `approver` a
					LEFT JOIN `employee` ON
							`employee`.`employee_id` = a.`related_value`
						AND a.`related_model` = 'Employee'
						AND `employee`.`status` = {$this->pub}
						AND '{$validFrom}' <= IFNULL(`employee`.`valid_to`, '{$this->end}') AND `employee`.`valid_from` <= '{$validTo}'
					LEFT JOIN `employee_contract` ON
							`employee_contract`.`employee_id` = `employee`.`employee_id`
						AND a.`related_model` = 'Employee'
						AND `employee_contract`.`status` = {$this->pub}
						AND '{$validFrom}' <= IFNULL(`employee_contract`.`valid_to`, '{$this->end}') AND `employee_contract`.`valid_from` <= '{$validTo}'
						AND '{$validFrom}' <= IFNULL(`employee_contract`.`ec_valid_to`, '{$this->end}') AND `employee_contract`.`ec_valid_from` <= '{$validTo}'
					" . EmployeeGroup::getLeftJoinSQLWithoutCal($tables[$relatedModel], "employee_contract", "", "", "employee", "'{$validFrom}'", "'{$validTo}'") . "
					SET
						a.`status` = " . Status::DELETED . ",
						a.`modified_by` = '" . userID() . "',
						a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
					WHERE
							a.`approver_user_id` = '{$approverUserId}'
						AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
						AND a.`status` = {$this->pub}
						AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->end}') AND a.`valid_from` <= '{$validTo}'
						{$whereEmpSQL}
				";
				dbExecute($updateEmployeeSQL);
			// Flexes új wildcard működés
			} else
			{
				$deleteRequired = false;
				// Expressionből expression, de más / szűkebb körbe
				if (substr($newValue, -1) == "*" && substr($oldValue, -1) == "*")
				{
					$lenghtOld	= strlen($oldValue);
					$lenghtNew	= strlen($newValue);
					if ($lenghtOld == $lenghtNew) {
						// pl.: HU-BU* -> HU-ZA* --> törölni kell
						$deleteRequired = true;
					} else if ($lenghtOld > $lenghtNew)
					{
						// pl.: HU-BUD* -> HU-BA* --> törölni kell
						// pl.: HU-BUD* -> HU-BU* --> nem kell törölni
						if (substr($newValue, 0, -1) != substr(substr($oldValue, -1), 0, strlen(substr($newValue, 0, -1)))) {
							$deleteRequired = true;
						}
					} else if ($lenghtOld < $lenghtNew) {
						// pl.: HU-BU* -> HU-BUD* --> törölni kell
						// pl.: HU-BU* -> HU-ZAL* --> törölni kell
						$deleteRequired = true;
					}
				// Expressionbe normál értékről
				// pl.: 1436(=HU-ZAL-AKÁRMI) -> HU-ZA* --> nem kell törölni
				// pl.: 1436(=HU-ZAL-AKÁRMI) -> HU-BU* --> törölni kell
				} else if (substr($newValue, -1) == "*" && substr($oldValue, -1) != "*")
				{
					if ($tables[$relatedModel] == "CompanyOrgGroup1" || $tables[$relatedModel] == "CompanyOrgGroup2" || $tables[$relatedModel] == "CompanyOrgGroup3") { $groupName = "company_org_group_name"; } else { $groupName = $tables[$relatedModel] . "_name"; }
					if ($tables[$relatedModel] == "CompanyOrgGroup1" || $tables[$relatedModel] == "CompanyOrgGroup2" || $tables[$relatedModel] == "CompanyOrgGroup3") { $id = "company_org_group_id"; } else { $id = $tables[$relatedModel] . "_id"; }
					$name = dbFetchValue("
						SELECT DISTINCT `{$groupName}`
						FROM `{$tables[$relatedModel]}`
						WHERE
								`{$groupName}` LIKE '" . substr($newValue, -1) . "%" . "'
							AND	`{$id}` = '{$oldValue}'
							AND `status` = {$this->pub}
							AND `valid_from` <= '{$validTo}'
							AND '{$validFrom}' <= IFNULL(`valid_to`, '{$this->end}')
					");
					if ($name === false) {
						$deleteRequired = true;
					}
				// Expressionből normál értékbe
				// pl.: HU-BU* -> 1436(=HU-ZAL-AKÁRMI) --> törölni kell
				} else if (substr($oldValue, -1) == "*" && substr($newValue, -1) != "*") {
					$deleteRequired = true;
				// Eredeti, de lehet * jog
				} else if (substr($oldValue, -1) != "*" && substr($newValue, -1) != "*") {
					$deleteRequired = true;
				}

				// Törlés
				if ($deleteRequired)
				{
					if ($tables[$relatedModel] == "CompanyOrgGroup1" || $tables[$relatedModel] == "CompanyOrgGroup2" || $tables[$relatedModel] == "CompanyOrgGroup3") { $id = "company_org_group_id"; } else { $id = $tables[$relatedModel] . "_id"; }
					if ($tables[$relatedModel] == "CompanyOrgGroup1" || $tables[$relatedModel] == "CompanyOrgGroup2" || $tables[$relatedModel] == "CompanyOrgGroup3") { $groupName = "company_org_group_name"; } else { $groupName = $tables[$relatedModel] . "_name"; }

					if ($processId == 'companyMainData')
					{
						$updateGroupsSQL = "
							UPDATE `approver` a
							JOIN `{$tables[$relatedModel]}` AS t ON
									t.`{$id}` = a.`related_value`
								AND t.`status` = {$this->pub}
								AND '{$validFrom}' <= IFNULL(t.`valid_to`, '{$this->end}') AND t.`valid_from` <= '{$validTo}'
								AND (t.`{$id}` = '{$oldValue}' OR t.`{$groupName}` LIKE '" . substr($oldValue, 0, -1) . "%" . "')
							-- Mivel előfordulhat olyan hogy %-os jogot módosított, de egyébként másik jog miatt még érvényes marad ez is, vagy az új jog miatt még1x approver tábla
							LEFT JOIN `approver` a2 ON
									a2.`status` = {$this->pub}
								AND a2.`row_id` <> a.`row_id`
								AND '{$validFrom}' <= IFNULL(a2.`valid_to`, '{$this->end}') AND a2.`valid_from` <= '{$validTo}'
								AND a2.`approver_user_id` = '{$approverUserId}'
								AND a2.`related_model` = a.`related_model`
								AND a2.`related_id` = a.`related_id`
								AND a2.`process_id` = '{$processId}'
								AND (a2.`related_value` = t.`{$id}` OR t.`{$groupName}` LIKE CONCAT(LEFT(a2.`related_value`, CHAR_LENGTH(a2.`related_value`) - 1), '%') OR a2.`related_value` = 'ALL')
							SET
								a.`status` = " . Status::DELETED . ",
								a.`modified_by` = '" . userID() . "',
								a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
							WHERE
									a.`approver_user_id` = '{$approverUserId}'
								AND a.`related_model` = '{$tables[$relatedModel]}'
								AND a.`process_id` <> 'companyMainData'
								AND a.`status` = {$this->pub}
								AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->end}') AND a.`valid_from` <= '{$validTo}'
								AND a2.`row_id` IS NULL
								AND a.`related_value` <> 'ALL'
						";
						dbExecute($updateGroupsSQL);
					}

					$updateEmployeeContractSQL = "
						UPDATE `approver` a
						JOIN `employee_contract` ON
								`employee_contract`.`employee_contract_id` = a.`related_value`
							AND a.`related_model` = 'EmployeeContract'
							AND `employee_contract`.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(`employee_contract`.`valid_to`, '{$this->end}') AND `employee_contract`.`valid_from` <= '{$validTo}'
							AND '{$validFrom}' <= IFNULL(`employee_contract`.`ec_valid_to`, '{$this->end}') AND `employee_contract`.`ec_valid_from` <= '{$validTo}'
						JOIN `employee` ON
								`employee`.`employee_id` = `employee_contract`.`employee_id`
							AND a.`related_model` = 'EmployeeContract'
							AND `employee`.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(`employee`.`valid_to`, '{$this->end}') AND `employee`.`valid_from` <= '{$validTo}'
						" . EmployeeGroup::getLeftJoinSQLWithoutCal($tables[$relatedModel], "employee_contract", "", "", "employee", "'{$validFrom}'", "'{$validTo}'") . "
						JOIN `{$tables[$relatedModel]}` AS t ON
								t.`{$id}` = " . EmployeeGroup::getActiveGroupSQL($tables[$relatedModel] . "_id", $contractOrEmployee[$tables[$relatedModel]]) . "
							AND t.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(t.`valid_to`, '{$this->end}') AND t.`valid_from` <= '{$validTo}'
							AND (t.`{$id}` = '{$oldValue}' OR t.`{$groupName}` LIKE '" . substr($oldValue, 0, -1) . "%" . "')
						-- Mivel előfordulhat olyan hogy %-os jogot módosított, de egyébként másik jog miatt még érvényes marad ez is, vagy az új jog miatt még1x approver tábla
						LEFT JOIN `approver` a2 ON
								a2.`status` = {$this->pub}
							AND a2.`row_id` <> a.`row_id`
							AND '{$validFrom}' <= IFNULL(a2.`valid_to`, '{$this->end}') AND a2.`valid_from` <= '{$validTo}'
							AND a2.`approver_user_id` = '{$approverUserId}'
							AND a2.`related_model` = '{$relatedModel}'
							AND IF('{$processId}' = 'companyMainData', a2.`process_id` = 'companyMainData', a2.`process_id` = a.`process_id`)
							AND (a2.`related_value` = t.`{$id}` OR t.`{$groupName}` LIKE CONCAT(LEFT(a2.`related_value`, CHAR_LENGTH(a2.`related_value`) - 1), '%') OR a2.`related_value` = 'ALL')
						SET
							a.`status` = " . Status::DELETED . ",
							a.`modified_by` = '" . userID() . "',
							a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
						WHERE
								a.`approver_user_id` = '{$approverUserId}'
							AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
							AND a.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->end}') AND a.`valid_from` <= '{$validTo}'
							AND a2.`row_id` IS NULL
							AND a.`related_value` <> 'ALL'
					";
					dbExecute($updateEmployeeContractSQL);

					$updateEmployeeSQL = "
						UPDATE `approver` a
						LEFT JOIN `employee` ON
								`employee`.`employee_id` = a.`related_value`
							AND a.`related_model` = 'Employee'
							AND `employee`.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(`employee`.`valid_to`, '{$this->end}') AND `employee`.`valid_from` <= '{$validTo}'
						LEFT JOIN `employee_contract` ON
								`employee_contract`.`employee_id` = `employee`.`employee_id`
							AND a.`related_model` = 'Employee'
							AND `employee_contract`.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(`employee_contract`.`valid_to`, '{$this->end}') AND `employee_contract`.`valid_from` <= '{$validTo}'
							AND '{$validFrom}' <= IFNULL(`employee_contract`.`ec_valid_to`, '{$this->end}') AND `employee_contract`.`ec_valid_from` <= '{$validTo}'
						" . EmployeeGroup::getLeftJoinSQLWithoutCal($tables[$relatedModel], "employee_contract", "", "", "employee", "'{$validFrom}'", "'{$validTo}'") . "
						JOIN `{$tables[$relatedModel]}` AS t ON
								t.`{$id}` = " . EmployeeGroup::getActiveGroupSQL($tables[$relatedModel] . "_id", $contractOrEmployee[$tables[$relatedModel]]) . "
							AND t.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(t.`valid_to`, '{$this->end}') AND t.`valid_from` <= '{$validTo}'
							AND (t.`{$id}` = '{$oldValue}' OR t.`{$groupName}` LIKE '" . substr($oldValue, 0, -1) . "%" . "')
						-- Mivel előfordulhat olyan hogy %-os jogot módosított, de egyébként másik jog miatt még érvényes marad ez is, vagy az új jog miatt még1x approver tábla
						LEFT JOIN `approver` a2 ON
								a2.`status` = {$this->pub}
							AND a2.`row_id` <> a.`row_id`
							AND '{$validFrom}' <= IFNULL(a2.`valid_to`, '{$this->end}') AND a2.`valid_from` <= '{$validTo}'
							AND a2.`approver_user_id` = '{$approverUserId}'
							AND a2.`related_model` = '{$relatedModel}'
							AND IF('{$processId}' = 'companyMainData', a2.`process_id` = 'companyMainData', a2.`process_id` = a.`process_id`)
							AND (a2.`related_value` = t.`{$id}` OR t.`{$groupName}` LIKE CONCAT(LEFT(a2.`related_value`, CHAR_LENGTH(a2.`related_value`) - 1), '%') OR a2.`related_value` = 'ALL')
						SET
							a.`status` = " . Status::DELETED . ",
							a.`modified_by` = '" . userID() . "',
							a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
						WHERE
								a.`approver_user_id` = '{$approverUserId}'
							AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
							AND a.`status` = {$this->pub}
							AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->end}') AND a.`valid_from` <= '{$validTo}'
							AND a2.`row_id` IS NULL
							AND a.`related_value` <> 'ALL'
					";
					dbExecute($updateEmployeeSQL);
				}
			}
		} else if ($newValue != "ALL" && $oldValue != $newValue && ($relatedModel == "EmployeeContract" || $relatedModel == "Employee") && $processId == "companyMainData")
		{
			$name1 = Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]);
			$name2 = Employee::getParam('fullname', 'employee');

			$updateEmpyeeContractSQL = "
				UPDATE `approver` a
				JOIN `employee_contract` ON
						`employee_contract`.`employee_contract_id` = a.`related_value`
					AND a.`related_model` = 'EmployeeContract'
					AND `employee_contract`.`status` = {$this->pub}
					AND '{$validFrom}' <= IFNULL(`employee_contract`.`valid_to`, '{$this->end}') AND `employee_contract`.`valid_from` <= '{$validTo}'
					AND '{$validFrom}' <= IFNULL(`employee_contract`.`ec_valid_to`, '{$this->end}') AND `employee_contract`.`ec_valid_from` <= '{$validTo}'
				JOIN `employee` ON
						`employee`.`employee_id` = `employee_contract`.`employee_id`
					AND a.`related_model` = 'EmployeeContract'
					AND `employee`.`status` = {$this->pub}
					AND '{$validFrom}' <= IFNULL(`employee`.`valid_to`, '{$this->end}') AND `employee`.`valid_from` <= '{$validTo}'
				LEFT JOIN `approver` a2 ON
						a2.`status` = {$this->pub}
					AND a2.`row_id` <> a.`row_id`
					AND '{$validFrom}' <= IFNULL(a2.`valid_to`, '{$this->end}') AND a2.`valid_from` <= '{$validTo}'
					AND a2.`approver_user_id` = '{$approverUserId}'
					AND a2.`related_model` = a.`related_model`
					AND a2.`related_id` = a.`related_id`
					AND a2.`process_id` = 'companyMainData'
					AND (a2.`related_value` = a.`related_value` OR '{$name1}' LIKE CONCAT(LEFT(a2.`related_value`, CHAR_LENGTH(a2.`related_value`) - 1), '%') OR a2.`related_value` = 'ALL')
				SET
					a.`status` = " . Status::DELETED . ",
					a.`modified_by` = '" . userID() . "',
					a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
				WHERE
						a.`approver_user_id` = '{$approverUserId}'
					AND a.`status` = {$this->pub}
					AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->end}') AND a.`valid_from` <= '{$validTo}'
					AND a2.`row_id` IS NULL
					AND a.`related_value` <> 'ALL'
					AND (`employee_contract`.`employee_contract_id` = '{$oldValue}' OR '{$name1}' LIKE '" . substr($oldValue, 0, -1) . "%" . "')
			";
			dbExecute($updateEmpyeeContractSQL);

			$updateEmployeeSQL = "
				UPDATE `approver` a
				JOIN `employee` ON
						`employee`.`employee_id` = a.`related_value`
					AND a.`related_model` = 'Employee'
					AND `employee`.`status` = {$this->pub}
					AND '{$validFrom}' <= IFNULL(`employee`.`valid_to`, '{$this->end}') AND `employee`.`valid_from` <= '{$validTo}'
				LEFT JOIN `approver` a2 ON
						a2.`status` = {$this->pub}
					AND a2.`row_id` <> a.`row_id`
					AND '{$validFrom}' <= IFNULL(a2.`valid_to`, '{$this->end}') AND a2.`valid_from` <= '{$validTo}'
					AND a2.`approver_user_id` = '{$approverUserId}'
					AND a2.`related_model` = a.`related_model`
					AND a2.`related_id` = a.`related_id`
					AND a2.`process_id` = 'companyMainData'
					AND (a2.`related_value` = a.`related_value` OR '{$name2}' LIKE CONCAT(LEFT(a2.`related_value`, CHAR_LENGTH(a2.`related_value`) - 1), '%') OR a2.`related_value` = 'ALL')
				SET
					a.`status` = " . Status::DELETED . ",
					a.`modified_by` = '" . userID() . "',
					a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
				WHERE
						a.`approver_user_id` = '{$approverUserId}'
					AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
					AND a.`status` = {$this->pub}
					AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->end}') AND a.`valid_from` <= '{$validTo}'
					AND a2.`row_id` IS NULL
					AND a.`related_value` <> 'ALL'
					AND (`employee`.`employee_id` = '{$oldValue}' OR '{$name2}' LIKE '" . substr($oldValue, 0, -1) . "%" . "')
			";
			dbExecute($updateEmployeeSQL);
		}
	}

	/**
	 * Minden folyamatot minden folyamattal felülírás
	 * @param array $input
	 * @return void
	 */
	public function copyRightsAllToAllOverwrite(array $input = []): void
	{
		$toRightsSQL = "
			SELECT
				`row_id`,
				`process_id`,
				`related_model`,
				`related_id`,
				`related_value`,
				`valid_from`,
				`valid_to`
			FROM `approver`
			WHERE
					`approver_user_id` = '" . ($input["to_username"] ?? "") . "'
				AND `status` = {$this->pub}
				AND `valid_from` <= '" . ($input["valid_to"] ?? "") . "'
				AND `valid_to` >= '" . ($input["valid_from"] ?? "") . "'
		";
		$resultToRights = dbFetchAll($toRightsSQL);

		$fromRightsSQL = "
			SELECT
				`process_id`,
				`related_model`,
				`related_id`,
				`related_value`,
				`valid_to`
			FROM `approver`
			WHERE
					`approver_user_id` = '" . ($input["from_username"] ?? "") . "'
				AND `status` = {$this->pub}
				AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->end}')
		";
		$resultFromRights = dbFetchAll($fromRightsSQL);

		foreach ($resultToRights as $to) {
			$toApprover = Approver::model()->findByPk($to["row_id"] ?? 0);
			$toApprover->status = Status::DELETED;
			if ($toApprover->validate()) {
				$toApprover->save();
			}
		}

		foreach ($resultFromRights as $from)
		{
			$toApprover = new Approver();
			$toApprover->approver_user_id	= $input["to_username"] ?? "";
			$toApprover->process_id 		= $from["process_id"] ?? "";
			$toApprover->related_model 		= $from["related_model"] ?? "";
			$toApprover->related_id 		= $from["related_id"] ?? "";
			$toApprover->related_value 		= $from["related_value"] ?? "";
			$toApprover->valid_from 		= $input["valid_from"] ?? "";
			$toApprover->valid_to 			= $input["valid_to"] ?? "";
			$toApprover->status 			= $this->pub;
			if ($toApprover->validate()) {
				$toApprover->save();
			}
		}
	}

	/**
	 * Minden folyamatot minden folyamathoz hozzáad (nincs felülírás, max törlés ha Összes jogot megkap)
	 * @param array $input
	 * @return void
	 */
	public function copyRightsAllToAllAdd(array $input = []): void
	{
		$diffSQL = "
			SELECT
                a_from.`process_id`,
                a_from.`related_model`,
                a_from.`related_id`,
                a_from.`related_value`
			FROM `approver` a_from
			LEFT JOIN `approver` a_to ON
                    a_to.`process_id` = a_from.`process_id`
                AND	a_to.`related_model` = a_from.`related_model`
                AND	a_to.`related_id` = a_from.`related_id`
                AND	(a_to.`related_value` = a_from.`related_value` OR (a_to.`related_value` = 'ALL'))
                AND a_to.`status` = {$this->pub}
                AND a_to.`approver_user_id` = '" . ($input["to_username"] ?? "") . "'
			WHERE
					a_from.`approver_user_id` = '" . ($input["from_username"] ?? "") . "'
				AND a_from.`status` = {$this->pub}
				AND CURDATE() BETWEEN a_from.`valid_from` AND IFNULL(a_from.`valid_to`, '{$this->end}')
				AND a_to.`row_id` IS NULL
		";
		$diffResult = dbFetchAll($diffSQL);

		foreach ($diffResult as $dr)
		{
			if ($dr["related_value"] == 'ALL')
			{
				$SQL = "
					SELECT `row_id`
					FROM `approver`
					WHERE
							`approver_user_id` = '" . ($input["to_username"] ?? "") . "'
						AND `process_id` = '" . ($dr["process_id"] ?? "") . "'
						AND `related_model` = '" . ($dr["related_model"] ?? "") . "'
						AND `related_id` = '" . ($dr["related_id"] ?? "") . "'
				";
				$result = dbFetchAll($SQL);

				foreach ($result as $r) {
					$delApprover = Approver::model()->findByPk($r["row_id"]);
					$delApprover->status = Status::DELETED;
					if ($delApprover->validate()) {
						$delApprover->save();
					}
				}
			}

			$toApprover = new Approver();
			$toApprover->approver_user_id	= $input["to_username"] ?? "";
			$toApprover->process_id 		= $dr["process_id"] ?? "";
			$toApprover->related_model 		= $dr["related_model"] ?? "";
			$toApprover->related_id 		= $dr["related_id"] ?? "";
			$toApprover->related_value 		= $dr["related_value"] ?? "";
			$toApprover->valid_from 		= $input["valid_from"] ?? "";
			$toApprover->valid_to 			= $input["valid_to"] ?? "";
			$toApprover->status 			= $this->pub;
			if ($toApprover->validate()) {
				$toApprover->save();
			}
			$toApprover->save();
		}
	}

	/**
	 * Egy folyamat egy folyamatra felülírás
	 * @param array $input
	 * @return void
	 */
	public function copyRightsOneToOneOverwrite(array $input = []): void
	{
		$fromRightsSQL = "
			SELECT
				`related_model`,
				`related_id`,
				`related_value`
			FROM `approver`
			WHERE
					`approver_user_id` = '" . ($input["from_username"] ?? "") . "'
				AND `process_id` = '" . ($input["from_process_id"] ?? "") . "'
				AND `status` = {$this->pub}
				AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->end}')
		";
		$resultFromRights = dbFetchAll($fromRightsSQL);

		$delSQL = "
			SELECT `row_id`
			FROM `approver`
			WHERE
					`approver_user_id` = '" . ($input["to_username"] ?? "") . "'
				AND `process_id` = '" . ($input["to_process_id"] ?? "") . "'
				AND `status` = {$this->pub}
		";
		$delResult = dbFetchAll($delSQL);

		foreach ($resultFromRights as $rfr) {
			$insertSQL = "
				INSERT INTO `approver` (`approver_user_id`, `process_id`, `related_model`, `related_id`, `related_value`, `valid_from`, `valid_to`, `status`, `created_by`, `created_on`) VALUES
					('{$input["to_username"]}', '{$input["to_process_id"]}', '{$rfr["related_model"]}', '{$rfr["related_id"]}', '{$rfr["related_value"]}', '{$input["valid_from"]}', '{$input["valid_to"]}', $this->pub, '" . userID() . "', NOW());
			";
			dbExecute($insertSQL);
		}

		foreach ($delResult as $dr)
		{
			$delApprover = Approver::model()->findByPk($dr["row_id"]);
			if (($this->companyAndPayrollMode["setting"] ?? 0) && ($input["to_process_id"] ?? "") == "companyMainData")
			{
				$newValue = "";
				foreach ($resultFromRights as $r) {
					if (($r["related_model"] ?? "") == $delApprover->related_model && ($r["related_id"] ?? "") == $delApprover->related_id) {
						$newValue = $r["related_value"] ?? "";
					}
				}
			}

			$updateSQL = "UPDATE `approver` SET `status` = " . Status::DELETED . ", `modified_by` = '" . userID() . "', `modified_on` = NOW() WHERE `row_id` = {$dr['row_id']}";
			dbExecute($updateSQL);

			/* Kapcsolódó alárendelt jogok törlése veluxnak készült cég és számfejtési kör mód esetén, ha a felülírt folyamat a céges alapadatok */
			if (($this->companyAndPayrollMode["setting"] ?? 0) && ($input["to_process_id"] ?? "") == "companyMainData") {
				$this->deleteRelatedRights($delApprover->process_id, $delApprover->related_model, $delApprover->approver_user_id, $delApprover->related_value, $newValue, $input["valid_from"], $input["valid_to"]);
			}
		}
	}

	/**
	 * Egy folyamat egy folyamathoz hozzáad
	 * @param array $input
	 * @return void
	 */
	public function copyRightsOneToOneAdd(array $input = []): void
	{
		$diffSQL = "
			SELECT
				a_from.`process_id`,
				a_from.`related_model`,
				a_from.`related_id`,
				a_from.`related_value`
			FROM `approver` a_from
			LEFT JOIN `approver` a_to ON
					a_to.`related_model` = a_from.`related_model`
				AND	a_to.`related_id` = a_from.`related_id`
				AND	a_to.`related_value` = a_from.`related_value`
				AND a_to.`status` = {$this->pub}
				AND a_to.`approver_user_id` = '" . ($input["to_username"] ?? "") . "'
				AND a_to.`process_id` = '" . ($input["to_process_id"] ?? "") . "'
			WHERE
					a_from.`approver_user_id` = '" . ($input["from_username"] ?? "") . "'
				AND a_from.`process_id` = '" . ($input["from_process_id"] ?? "") . "'
				AND a_from.`status` = {$this->pub}
				AND CURDATE() BETWEEN a_from.`valid_from` AND IFNULL(a_from.`valid_to`, '{$this->end}')
				AND a_to.`row_id` IS NULL
		";
		$diffResult = dbFetchAll($diffSQL);

		foreach ($diffResult as $dr)
		{
			$toApprover = new Approver();
			$toApprover->approver_user_id	= $input["to_username"] ?? "";
			$toApprover->process_id			= $input["to_process_id"] ?? "";
			$toApprover->related_model		= $dr["related_model"] ?? "";
			$toApprover->related_id			= $dr["related_id"] ?? "";
			$toApprover->related_value 		= $dr["related_value"] ?? "";
			$toApprover->valid_from 		= $input["valid_from"] ?? "";
			$toApprover->valid_to 			= $input["valid_to"] ?? "";
			$toApprover->status 			= $this->pub;
			if ($toApprover->validate()) {
				$toApprover->save();
			}
		}
	}

	/**
	 * Egy folyamattal minden folyamat felülírása
	 * @param array $input
	 * @return void
	 */
	public function copyRightsOneToAllOverwrite(array $input = []): void
	{
		$fromRightsSQL = "
			SELECT
				`process_id`,
				`related_model`,
				`related_id`,
				`related_value`,
				`valid_to`
			FROM `approver`
			WHERE
					`approver_user_id` = '" . ($input["from_username"] ?? "") . "'
				AND `process_id` = '" . ($input["from_process_id"] ?? "") . "'
				AND `status` = {$this->pub}
				AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->end}')
		";
		$resultFromRights = dbFetchAll($fromRightsSQL);

		$delSQL = "
			SELECT `row_id`
			FROM `approver`
			WHERE
					`approver_user_id` = '" . ($input["to_username"] ?? "") . "'
				AND `status` = {$this->pub}
		";
		$delResult = dbFetchAll($delSQL);

		foreach ($delResult as $dr) {
			$delApprover = Approver::model()->findByPk($dr["row_id"]);
			$delApprover->status = Status::DELETED;
			if ($delApprover->validate()) {
				$delApprover->save();
			}
		}

		/* Folyamatok lekérése */
		$allProcessesSQL = "
			SELECT DISTINCT `lookup_value`
			FROM `app_lookup`
			WHERE
					`lookup_id` = 'approver_process_ids'
				AND `valid` = '1'
		";
		$resultAllProcesses = dbFetchAll($allProcessesSQL);

		foreach ($resultAllProcesses as $rap)
		{
			foreach ($resultFromRights as $rfr)
			{
				$toApprover = new Approver();
				$toApprover->approver_user_id	= $input["to_username"] ?? "";
				$toApprover->process_id 		= $rap["lookup_value"] ?? "";
				$toApprover->related_model 		= $rfr["related_model"] ?? "";
				$toApprover->related_id 		= $rfr["related_id"] ?? "";
				$toApprover->related_value 		= $rfr["related_value"] ?? "";
				$toApprover->valid_from 		= $input["valid_from"] ?? "";
				$toApprover->valid_to 			= $input["valid_to"] ?? "";
				$toApprover->status 			= $this->pub;
				if ($toApprover->validate()) {
					$toApprover->save();
				}
			}
		}
	}

	/**
	 * Egy folyamat minden folyamatra másolása
	 * @param array $input
	 * @return void
	 */
	public function copyRightsOneToAllAdd(array $input = []): void
	{
		$diffSQL = "
			SELECT
				a_from.`process_id`,
				a_from.`related_model`,
				a_from.`related_id`,
				a_from.`related_value`
			FROM (
				SELECT
                    al.`lookup_value` as process_id,
                    a_from.`related_model`,
                    a_from.`related_id`,
                    a_from.`related_value`
                FROM `approver` a_from
                LEFT JOIN `app_lookup` al ON
                        `lookup_id` = 'approver_process_ids'
                    AND `valid` = '1'
                WHERE a_from.`approver_user_id` = '" . ($input["from_username"] ?? "") . "'
                    AND a_from.`process_id` = '" . ($input["from_process_id"] ?? "") . "'
                    AND a_from.`status` = {$this->pub}
                    AND CURDATE() BETWEEN a_from.`valid_from` AND IFNULL(a_from.`valid_to`, '{$this->end}')
            ) a_from
			LEFT JOIN `approver` a_to ON
					a_to.`process_id` = a_from.`process_id`
				AND	a_to.`related_model` = a_from.`related_model`
				AND	a_to.`related_id` = a_from.`related_id`
				AND	(a_to.`related_value` = a_from.`related_value` OR (a_to.`related_value` = 'ALL'))
				AND a_to.`status` = {$this->pub}
				AND a_to.`approver_user_id` = '" . ($input["to_username"] ?? "") . "'
			WHERE a_to.`row_id` IS NULL
		";
		$diffResult = dbFetchAll($diffSQL);

		foreach ($diffResult as $dr)
		{
			if ($dr["related_value"] == 'ALL')
			{
				$SQL = "
					SELECT `row_id`
					FROM `approver`
					WHERE
							`approver_user_id` = '" . ($input["to_username"] ?? "") . "'
						AND `process_id` = '" . ($dr["process_id"] ?? "") . "'
						AND `related_model` = '" . ($dr["related_model"] ?? "") . "'
						AND `related_id` = '" . ($dr["related_id"] ?? "") . "'
				";
				$result = dbFetchAll($SQL);

				foreach ($result as $r) {
					$delApprover = Approver::model()->findByPk($r["row_id"]);
					$delApprover->status = Status::DELETED;
					if ($delApprover->validate()) {
						$delApprover->save();
					}
				}
			}

			$toApprover = new Approver();
			$toApprover->approver_user_id	= $input["to_username"] ?? "";
			$toApprover->process_id 		= $dr["process_id"] ?? "";
			$toApprover->related_model 		= $dr["related_model"] ?? "";
			$toApprover->related_id 		= $dr["related_id"] ?? "";
			$toApprover->related_value 		= $dr["related_value"] ?? "";
			$toApprover->valid_from 		= $input["valid_from"] ?? "";
			$toApprover->valid_to 			= $input["valid_to"] ?? "";
			$toApprover->status 			= $this->pub;
			if ($toApprover->validate()) {
				$toApprover->save();
			}
		}
	}
}

?>