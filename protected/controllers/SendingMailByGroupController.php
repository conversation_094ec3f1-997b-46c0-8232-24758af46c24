<?php

class SendingMailByGroupController extends Grid2HistoryController
{
	private $statusPublished;
	private $defaultEnd;
	private $date;
	private $optionArray;
	private $sendingMailByUsedGroup;
	private $groupList;

	public function __construct($controllerID = "sendingMailByGroup")
	{
		parent::__construct($controllerID);
		$this->statusPublished = Status::PUBLISHED;
		$this->defaultEnd = App::getSetting('defaultEnd');
		$this->date = date("Y-m-d");
		$this->optionArray = App::getLookup('sending_maily_by_group_options', false, null, [], true);
		(string)$this->sendingMailByUsedGroup = App::getSetting('sendingMailByUsedGroup');
		$this->groupList = $this->getUsedGroup();
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("SendingMailByGroup");

		parent::setControllerPageTitleId("page_title_sending_mail_by_group");

		$this->LAGridRights->overrideInitRights("paging", true);
		$this->LAGridRights->overrideInitRights("search_header", true);
		$this->LAGridRights->overrideInitRights("select", true);
		$this->LAGridRights->overrideInitRights("column_move", true);
		$this->LAGridRights->overrideInitRights("reload_sortings", true);
		$this->LAGridRights->overrideInitRights("details", false);
		$this->LAGridRights->overrideInitRights("column_move", true);
		$this->LAGridRights->overrideInitRights("col_sorting", true);
		$this->LAGridRights->overrideInitRights("add", true);
		$this->LAGridRights->overrideInitRights("delete", true);

		$sendingMailByGroup = new SendingMailByGroup;
		$sendingMailByGroupCriteria = new CDbCriteria();
		$sendingMailByGroupCriteria->alias = $sendingMailByGroup->tableName();
		$sendingMailByGroupCriteria->condition = "`status` = " . Status::PUBLISHED . "";
		$sendingMailByGroupCriteria->order = "`group_value`, `option`";

		$this->LAGridDB->setModelSelection($sendingMailByGroup, $sendingMailByGroupCriteria);

		parent::G2BInit();
	}

	public function columns()
	{
		$columns = [];

		$columns = [
			'group_value' => [
				'grid'=>true,
				'width'=>300,
				'window'=>true,
				'col_type'=>'combo',
				'options'	=>	[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
					'array'	=> $this->groupList
				],
			],
			'option' => [
				'grid'=>true,
				'width'=>300,
				'window'=>true,
				'col_type'=>'combo',
				'options'	=>	[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
					'array'	=> $this->optionArray
				],
			],
		];

        return $columns;
	}

	public function attributeLabels()
    {
        $attritubeLabels = [];

		$attritubeLabels = [
            'group_value' => Dict::getValue('group_value'),
            'option' => Dict::getValue('option'),
        ];

        return $attritubeLabels;
	}

	protected function getUsedGroup()
	{
		$groupResult = [];

		if (!empty($this->sendingMailByUsedGroup)) {
			$id = $this->sendingMailByUsedGroup . '_id';
			$value = $this->sendingMailByUsedGroup . '_name';

			if (strpos($this->sendingMailByUsedGroup, 'company_org_group') !== false) {
				$id = 'company_org_group_id';
				$value = 'company_org_group_name';
			}

			$SQL = "
				SELECT
					{$id} AS id,
					{$value} AS value
				FROM {$this->sendingMailByUsedGroup}
				WHERE
						`status` = " . Status::PUBLISHED . "
					AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '" . $this->defaultEnd . "')
				ORDER BY value
			";

			$groupResult = dbFetchAll($SQL);
		}

		return $groupResult;
	}
}