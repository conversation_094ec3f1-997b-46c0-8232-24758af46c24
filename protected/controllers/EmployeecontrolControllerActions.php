<?php

'yii2-only`;

	namespace app\controllers;
	use Yii;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2\Grid2Controller;
	use app\components\Helpers\AnyCache;
	use app\components\Log;
	use app\components\MyActiveForm;
	use app\models\AppLookup;
	use app\models\BulkGroupChangeByCogRequest;
	use app\models\ColumnTabs;
	use app\models\Employee;
	use app\models\EmployeeAbsence;
	use app\models\EmployeeAbsenceCalculation;
	use app\models\EmployeeGroup;
	use app\models\EmployeeGroupConfig;
	use app\models\EmployeeUploadData;
	use app\models\Status;
	use app\models\Tab;
	use Yang;

`/yii2-only';


trait EmployeecontrolControllerActions
{

	public $deleted = Status::DELETED;
	public $statusPublished = Status::PUBLISHED;
	public $intervalDatesSQL = [];

	public function actionIndex($layout = '//employee/layouts/indexLayout', $view = '/Grid2/index', $params = array()) {
		parent::actionIndex($layout, $view, $params);
	}

	public function actionDialog($layout = '//Grid2/layouts/wizardDialogLayout', $view = '/Grid2/wizardDialog',	$additionalParams = []) {
		parent::actionDialog('//Grid2/layouts/wizardDialogLayout', '/employeecontrol/employeecontrolWizardDialog', $additionalParams);
	}

	public function actionGetHistoryDates($employeeId, $activeTab, $ajax = false)
	{
		$editPK = $employeeId;
		$published = $this->statusPublished;
		$defaultEnd =  App::getSetting('defaultEnd');
		$defaultStart = App::getSetting('defaultStart');

		if (isset($_POST['editPK']) && isset($_POST['activeTab'])) {
			$editPK = !empty($_POST['editPK']) ? $_POST['editPK'] : null;
			$activeTab = !empty($_POST['activeTab']) ? $_POST['activeTab'] : null;
			$ajax = isset($_POST['ajax']) && !empty($_POST['ajax']) ? $_POST['ajax'] == 'true' ? true : false : false;
		}

		//A dokumentumok tabon ne legyen történetiség mert ott választó van eleve.
		if( $activeTab == 'tab5' ){
			if( $ajax ){
				echo json_encode([],JSON_UNESCAPED_UNICODE);
				return;
			}else{
				return [];
			}
		}
		$returnArray = [];
		if ($editPK === null && $activeTab === null) {
			return $returnArray;
		}

		$editPK = isset(explode('_', $editPK)[0]) ? explode('_', $editPK)[0] : null;

		$groupSQL = $this->groupSQLFetchAll;

		$tabColumns = ColumnTabs::getTabColumns($activeTab);
		$count = 0;
		$usedTables = [];
		$tableColumns = [];

		$specialTables = [];
		$specialTableColumns = [];

		foreach ($tabColumns as $key => $value)
		{
			if ($value['model_name'] == 'LinkGroupToTerminal') {
				continue;
			}

			if ($value['save_with_history'] == '1') {
				$model = new EmployeeGroup;
				$tableName = $model->tableName().'_'.$value['column_id'];
			} else {
				$model = new $value['model_name'];
				$tableName = $model->tableName();
			}

			if ($value['model_name'] == 'EmployeeBaseAbsence')
			{
				if (!in_array($tableName, $specialTables)) {
					$specialTables[] = $tableName;
				}
				$specialTableColumns[$tableName][] = $value['column_id'];
			} else if ($value['save_with_history'] == '1') {
				$tableColumns[$tableName][] = 'group_value';
			} else {
				$tableColumns[$tableName][] = $value['column_id'];
			}

			if (!in_array($tableName, $usedTables) && $value['model_name'] != 'EmployeeBaseAbsence' && $value['save_with_history'] != '1') {
				$usedTables[] = $tableName;
			} else if ($value['save_with_history'] == 1) {
				$usedTables[] = $tableName;
			}
		}

		$maxCount = count($usedTables);
		$groupBy = "GROUP BY ";

		$select = 'DISTINCT ';

		foreach ($usedTables as $table)
		{
			if ($table !== "employee_group") {
				$select .= 'CONCAT(' . $table . '.valid_from, " - ", ' . $table . '.valid_to) as ' . $table . '_interval,';
			}
		}

		foreach ($groupSQL as $group) {
			$table = 'employee_group_' . $group['id'];
			$select .= 'CONCAT(' . $table . '.valid_from, " - ", ' . $table . '.valid_to) as ' . $table . '_interval,';
		}

		$select = rtrim($select, ",");

		foreach ($tableColumns as $table => $columns) {
			foreach ($columns as $columnKey => $columnVal) {
				$groupBy .= $table . '.' . $columnVal . ',';
			}
		}

		$groupBy = '';//mb_substr($groupBy, 0, -1);

		$SQL = "
		SELECT
		$select
		FROM employee
		LEFT JOIN
		    employee_contract
				ON employee.employee_id = employee_contract.employee_id
				AND employee_contract.status = $published  ";
		if( in_array('employee_ext2', $usedTables) ){
			$SQL .= "
			LEFT JOIN
		    employee_ext2
				ON employee.employee_id = employee_ext2.employee_id
				AND employee_ext2.status = $published
			";
		}

		if( in_array('employee_ext', $usedTables) ){
			$SQL .= "
			LEFT JOIN
		    employee_ext
				ON employee.employee_id = employee_ext.employee_id
				AND employee_ext.status = $published
			";
		}

		if( in_array('employee_ext3', $usedTables) ){
			$SQL .= "
			LEFT JOIN
		    employee_ext3
				ON employee.employee_id = employee_ext3.employee_id
				AND employee_ext3.status = $published
			";
		}

		if( in_array('employee_ext4', $usedTables) ){
			$SQL .= "
			LEFT JOIN
		    employee_ext4
				ON employee.employee_id = employee_ext4.employee_id
				AND employee_ext4.status = $published
			";
		}

		if( in_array('company', $usedTables) ){
			$SQL .= "
			LEFT JOIN
		    company
				ON company.`company_id` = employee.`company_id`
				AND company.`status` = $published
			";
		}

		if( in_array('payroll', $usedTables) ){
			$SQL .= "
			LEFT JOIN
		    `payroll`
				ON payroll.`payroll_id` = employee.`payroll_id`
				AND payroll.`status` = $published
			";
		}

		if (EmployeeGroupConfig::isActiveGroup('unit_id')) {
			$SQL .= EmployeeGroup::getLeftJoinSQLWithoutCalAndate("unit_id","employee_contract");
		}else{
			if( in_array('unit', $usedTables) ){
				$SQL .= "
				LEFT JOIN
				`unit`
					ON unit.`unit_id` = employee.`unit_id`
					AND unit.`status` = $published
				";
			}
		}

		if (EmployeeGroupConfig::isActiveGroup('workgroup_id')) {
			$SQL .= EmployeeGroup::getLeftJoinSQLWithoutCalAndate("workgroup_id","employee_contract");
		}else{
			if( in_array('workgroup', $usedTables) ){
				$SQL .= "
				LEFT JOIN
				workgroup
					ON workgroup.workgroup_id = employee_contract.workgroup_id
					AND workgroup.status = $published
				";
			}
		}

		if (EmployeeGroupConfig::isActiveGroup('company_org_group1_id')) {
			$SQL .= EmployeeGroup::getLeftJoinSQLWithoutCalAndate("company_org_group1_id","employee_contract");
		}else{
			if( in_array('company_org_group1', $usedTables) ){
				$SQL .= "
				LEFT JOIN
				`company_org_group1`
					ON company_org_group1.`company_org_group_id` = employee.`company_org_group1_id`
					AND company_org_group1.`status` = $published
				";
			}
		}

		if (EmployeeGroupConfig::isActiveGroup('company_org_group2_id')) {
			$SQL .= EmployeeGroup::getLeftJoinSQLWithoutCalAndate("company_org_group2_id","employee_contract");
		}else{
			if( in_array('company_org_group2', $usedTables) ){
				$SQL .= "
				LEFT JOIN
				`company_org_group2`
					ON company_org_group2.`company_org_group_id` = employee.`company_org_group2_id`
					AND company_org_group2.`status` = $published
				";
			}
		}

		if (EmployeeGroupConfig::isActiveGroup('company_org_group3_id')) {
			$SQL .= EmployeeGroup::getLeftJoinSQLWithoutCalAndate("company_org_group3_id","employee_contract");
		}else{
			if( in_array('company_org_group3', $usedTables) ){
				$SQL .= "
				LEFT JOIN
				`company_org_group3`
					ON company_org_group3.`company_org_group_id` = employee.`company_org_group3_id`
					AND company_org_group3.`status` = $published
				";
			}
		}

		if( in_array('employee_cost', $usedTables) ){
			$SQL .= "
			LEFT JOIN
		    employee_cost
				ON employee_cost.employee_contract_id = employee_contract.employee_contract_id
				AND employee_cost.status = $published
			";
		}

		if( in_array('employee_base_absence', $usedTables) ){
			$SQL .= "
			LEFT JOIN
		    employee_base_absence
				ON employee_base_absence.employee_contract_id = employee_contract.employee_contract_id
				AND employee_base_absence.status = $published
			";
		}

		if( in_array('employee_card', $usedTables) ){
			$SQL .= "
			LEFT JOIN
		    employee_card
				ON employee_card.employee_contract_id = employee_contract.employee_contract_id
				AND employee_card.status = $published
			";
		}

		if( in_array('employee_docs', $usedTables) ){
			$SQL .= "
			LEFT JOIN
		    employee_docs
				ON employee_docs.employee_id = employee.employee_id
				AND employee_docs.status = $published
			";
		}

		if( in_array('workgroup', $usedTables) ){
			$SQL .= "
			LEFT JOIN
		    employee_docs
				ON employee_docs.employee_id = employee.employee_id
				AND employee_docs.status = $published
			";
		}

		if( in_array('employee_address', $usedTables) ){
			$SQL .= "
			LEFT JOIN
		    employee_address
				ON employee_address.employee_id = employee.employee_id
				AND employee_address.status = $published
			";
		}

		if( in_array('employee_salary', $usedTables) ){
			$SQL .= "
			LEFT JOIN
		    employee_salary
				ON employee_salary.employee_contract_id = employee_contract.employee_contract_id
				AND employee_salary.status = $published
			";
		}

		$SQL .= "
		WHERE
			employee.status = $published
			AND employee.employee_id = '$editPK'
		$groupBy
		ORDER BY employee_contract.valid_from
		";

		$historyResults = [];

		if( $activeTab != 'tab8' ){

			if ($this->intervalDatesSQL['SQL'] != $SQL) {
				$results = dbFetchAll($SQL);
				$this->intervalDatesSQL['SQL'] = $SQL;
				$this->intervalDatesSQL['SQLFetchAll'] = $results;
			}
			else {
				$results = $this->intervalDatesSQL['SQLFetchAll'];
			}
			
			foreach ( $results as $resKey => $resVal ){

				foreach ( $resVal as $resKey2 => $resVal2 ){
					if( is_null($resVal2) ){
						continue;
					}
					$intervalExplode = explode(' - ',$resVal2);
					$intervalStart = $intervalExplode[0];
					$intervalEnd = $intervalExplode[1];

					$historyResults[$intervalStart] = $intervalEnd;
				}
			}

			$results = $this->groupDates($historyResults);
		}else{
			$results = [];
		}

		if (!empty($specialTables)) {
			foreach ($specialTables as $key => $table) {
				foreach ($specialTableColumns[$table] as $tableColumnKey => $tableColumnValue) {
					$greatest = '';
					$least = '';

					foreach ( $results as $resKey => $resVal ){
						foreach ( $resVal as $resKey2 => $resVal2 ){
							$resExplode = explode(' - ',$resVal2);
							$start = $resExplode[0];
							$end = $resExplode[1];

							$greatest .= ',"'.$start.'"';
							$least .= ',"'.$end.'"';
						}
					}

					if ($table == 'employee_group') {
						$specialSql = "
						SELECT
						    CONCAT(GREATEST(COALESCE(eg.valid_from, '".$defaultStart."'), '".$defaultStart."'"
							.$greatest."), ' - ', LEAST(COALESCE(eg.valid_to, '".$this->defaultEnd."'),'"
							.$this->defaultEnd."'".$least.")) as employee_group_interval
						FROM employee_contract ec
						LEFT JOIN employee_group eg
						ON eg.employee_contract_id = ec.employee_contract_id
							AND eg.status = $published
							AND group_id = '" . $tableColumnValue . "'
							AND eg.valid_from <= LEAST(IFNULL(ec.valid_to,'".$this->defaultEnd."'),'".$this->defaultEnd."')
							AND GREATEST(IFNULL(ec.valid_from,'".$defaultStart."'), '".$defaultStart."') <= eg.valid_to
						WHERE ec.employee_id = '" . $editPK . "'
							AND ec.status = $published
						GROUP BY group_value
						";
					} else {
						if ($table == 'employee_base_absence') {
							$specialSql = "
						SELECT
							DISTINCT CONCAT(eba.valid_from, ' - ', eba.valid_to) as employee_base_absence_interval
						FROM employee_contract ec
						LEFT JOIN employee_base_absence eba
						ON eba.employee_contract_id = ec.employee_contract_id
							AND eba.status = $published
							AND eba.base_absence_type_id = '" . $tableColumnValue . "'
						WHERE ec.employee_id = '" . $editPK . "'
							AND ec.status = $published
							AND eba.row_id IS NOT NULL
						";
						}
					}
					$specialResults = dbFetchAll($specialSql);
					if($specialResults) $results = array_merge($results,$specialResults);
				}
			}
		}

		$intervals = [];

		if ($results) {
			foreach ($results as $resKey => $resValue) {
				foreach ($resValue as $columnKey => $val) {
					if (!in_array($val, $intervals) && !is_null($val) && !empty($val)) {
						$intervalExplode = explode(' - ', $val);
						$start = $intervalExplode[0];
						$end = $intervalExplode[1];
						if( strtotime($end) >= strtotime($start) ){
							$intervals[$val] = $val;
						}
					}
				}
			}
		}

		rsort($intervals);

		if ($ajax) {
			print json_encode($intervals, JSON_UNESCAPED_UNICODE);
			return;
		} else {
			return $intervals;
		}

	}

	public function actionDeleteLastHistory (){

		if (isset($_POST['editPK']) && isset($_POST['activeTab']) && isset($_POST['interval'])) {
			$employeeId = !empty($_POST['editPK']) ? $_POST['editPK'] : null;
			$activeTab = !empty($_POST['activeTab']) ? $_POST['activeTab'] : null;

			$interval = !empty($_POST['interval']) ? $_POST['interval'] : null;
			$validFrom = $interval[0];
			$validTo = $interval[1];

			$nextInterval = !empty($_POST['nextInterval']) ? $_POST['nextInterval'] : null;
			$nextValidFrom = $nextInterval[0];
			$nextValidTo = $nextInterval[1];
		}else{
			print Dict::getValue('missing_parameter'). ' - editPK, activeTab, interval';
			return;
		}

		$employeeId = isset(explode('_', $employeeId)[0]) ? explode('_', $employeeId)[0] : null;

		if( is_null($employeeId) ){
			print Dict::getValue('missing_parameter'). ' - editPK';
			return;
		}

		$employeeContractSql = "
					SELECT
					employee_contract_id
					FROM employee_contract
					WHERE employee_id = '" . $employeeId . "'
					AND status = '" . Status::PUBLISHED . "'
					AND CURDATE() BETWEEN employee_contract.valid_from AND IFNULL(employee_contract.valid_to, '" . App::getSetting('defaultEnd') . "') ";
		$query = dbFetchRow($employeeContractSql);

		$employeeContractId = $query['employee_contract_id'];

		$tabColumns = ColumnTabs::getTabColumns($activeTab);
		$usedTables = [];

		foreach ($tabColumns as $key => $value) {

			if ($value['save_with_history'] == '1') {
				$model = 'EmployeeGroup';
			} else {
				$model = $value['model_name'];
			}


			if (!in_array($model, $usedTables)) {
				$usedTables[] = $model;
			}

		}

		$savedTables = $this->getNextIntervalTable($usedTables, $employeeId, $employeeContractId, $validFrom, $nextValidFrom);

		$savedModels = [];
		$savedNewModels = [];

		$modelValidTo = null;

		$transaction = Yii::app()->db->beginTransaction();

		try{

			/*
			 * Tudom hogy nem túl előnyös modelekkel dolgozni foreach-en belül de jelenleg rendkívül sok plusz
			 * időbe telne megoldani másképp a dolgokat viszont ezért kellett többek között beletenni a transaction-t
			 * így csak a végén futtatja le a mentéseket egyben nem pedig a foreach-ben.
			 */

			foreach ($savedTables['models'] as $modelName => $models){
				foreach ($models as $modelKey => $model){
					if( $model ){
						$model->status = $this->deleted;
						if($model->save(false)){
							$modelValidTo = $model->valid_to;
							$errors = $model->getErrors();
							if( empty($errors) ){
								$savedModels[] = $modelName;
							}
						}
					}
				}
			}

			if( !is_null($modelValidTo) ){
				foreach ($savedTables['newModels'] as $newModelName => $newModels){
					foreach ($newModels as $newModelKey => $newModel){
						if( $newModel ){
							$newModel->valid_to = $modelValidTo;
							if($newModel->save(false)){
								$errors = $newModel->getErrors();
								if( empty($errors) ){
									if( !in_array($modelName, $savedNewModels) ){
										$savedNewModels[] = $modelName;
									}
								}
							}
						}
					}
				}
			}

			$transaction->commit();

		} catch (Exception $e){
			$transaction->rollback();
			print Dict::getValue('an_error_occured') . ' - '.$e;
			return;
		}


		if( !empty($savedModels) && !empty($savedNewModels) ){
			print 1;
			return;
		}else{
			print Dict::getValue('failed_delete');
			return;
		}

	}

	public function actionGetDownloadableFiles (){
		$returnString = '';

		if( !isset($_POST['editPK']) || ( isset($_POST['editPK']) && empty($_POST['editPK']) )){
			print $returnString;
			return;
		}

		$editPK = $_POST['editPK'];
		$editPK = isset(explode('_', $editPK)[0]) ? explode('_', $editPK)[0] : null;

		$SQL = "
		SELECT
			fs_file_id AS id,
			CONCAT_WS(' - ', file.file_original_name, employee_docs.valid_from, employee_docs.valid_to) AS value
		FROM employee_docs
		LEFT JOIN file_storage ON file_storage.file_id = employee_docs.fs_file_id
		LEFT JOIN file ON file.file_new_name COLLATE utf8_unicode_ci = file_storage.file_name COLLATE utf8_unicode_ci COLLATE utf8_unicode_ci
		WHERE  `status`=2  AND (employee_docs.employee_id = '{$editPK}' )
		ORDER BY employee_docs.row_id DESC
		";

		$results = dbFetchAll($SQL);

		if($results){
			$result = ArrayMap($results,'id','value');
			$returnString .= '<option></option>';
			foreach ( $result as $id => $value ){
				$returnString .= '<option value="'.$id.'">'.$value.'</option>';
			}

		}

		print $returnString;
		return;

	}

	public function actionGetFileInfo ($fileId, $ajax = false)
	{
		$row = [];

		if(isset($_POST["fileId"])) $fileId = $_POST["fileId"] ?: null;													#verifyThis [n-7wh6j9ly] override logic

		if( !is_null($fileId) ){

			$SQL = "

				SELECT
							employee_docs.fs_file_id,
							employee_docs.note,
							employee_docs.valid_from,
							employee_docs.valid_to

				FROM		employee_docs
				LEFT JOIN	file_storage ON file_storage.file_id = employee_docs.fs_file_id
				LEFT JOIN	file ON file.file_new_name COLLATE utf8_unicode_ci = file_storage.file_name COLLATE utf8_unicode_ci COLLATE utf8_unicode_ci

				WHERE 1		AND `status`=2
							AND employee_docs.fs_file_id = '$fileId'

				;

			";

			$row = dbFetchRow($SQL) ?: [];

		}

		if(!$ajax) return $row;

		print json_encode($row);
		return;

	}

	public function actionGetTabHistory()
	{

		$returnScript = "";

		$editPK = isset($_POST['editPK']) && !empty($_POST['editPK']) ? $_POST['editPK'] : null;
		$activeTab = isset($_POST['activeTab']) && !empty($_POST['activeTab']) ? $_POST['activeTab'] : null;
		$interval = isset($_POST['interval']) && !empty($_POST['interval']) ? $_POST['interval'] : null;

		if ($editPK === null && $activeTab === null) {
			echo $returnScript;
			return;
		}
		$validFrom = $editPK[1];
		$editPK = isset(explode('_', $editPK)[0]) ? explode('_', $editPK)[0] : null;

		$published = Status::PUBLISHED;
		$defaultEnd = App::getSetting('defaultEnd');

		$groupSQL = dbFetchRow($this->group_SQL());
		$groupID = $groupSQL['id'];

		$exprFullName = AnyCache::get("G2BInit.exprFullName2");
		if (!$exprFullName) {
			$exprFullName = Employee::getParam('fullname', 'employee');
			AnyCache::set("G2BInit.exprFullName2", $exprFullName,"employee");
		}

		$intervalSQL = "";
		$groupIntervalSQL = "";
		
		$validTo = $defaultEnd;
		$absenceIntervalSQL = '';

		if (is_null($interval)) {
			$intervalSQL = "
			AND '$validFrom' BETWEEN employee.valid_from AND IFNULL(employee.valid_to, '$defaultEnd')
			";

			$absenceIntervalSQL = "
			AND '$validFrom' BETWEEN eba.valid_from AND IFNULL(eba.valid_to, '$defaultEnd')
			";
		} else {
			$validFrom = $interval[0];
			$validTo = $interval[1];
			$intervalSQL = "
			AND employee.valid_from BETWEEN '$validFrom' AND '$validTo'
			";
			/*$groupIntervalSQL = "
			AND eg.valid_from BETWEEN '$validFrom' AND '$validTo'
			";*/
			$groupIntervalSQL = "
			AND '$validFrom' BETWEEN eg.valid_from AND IFNULL(eg.valid_to, '$defaultEnd')
			";
			$absenceIntervalSQL = "
			AND eba.valid_from BETWEEN '$validFrom' AND '$validTo'
			";

		}

		$defaultTabSql = "
						SELECT
							employee.`row_id`,
							employee.`first_name`,
							employee.`last_name`,
							employee.`emp_id`,
							employee.`valid_from`,
							employee.`valid_to`,
							employee.emp_id as employee_id,
							employee.title,
							company.company_id,
							company.row_id AS company_row_id,
							payroll.`payroll_id`,
							payroll.row_id AS payroll_row_id,
							unit.`unit_id`,
							" . $exprFullName . " as fullname,
							unit.unit_name,
							unit.row_id AS unit_row_id,
							company_org_group1.company_org_group_id AS company_org_group1_id,
							company_org_group2.`company_org_group_id` AS company_org_group2_id,
							company_org_group3.`company_org_group_id` AS company_org_group3_id,
							company_org_group1.row_id AS company_org_group1_row_id,
							company_org_group2.row_id AS company_org_group2_row_id,
							company_org_group3.row_id AS company_org_group3_row_id,
							employee_contract.`employee_position_id`,
							employee_contract.`ec_valid_from`,
							employee_contract.`ec_valid_to`,
							employee_contract.employee_contract_number,
							employee_contract.row_id AS employee_contract_row_id,
							employee_group.`group_value`,
							employee_group.`group_id`,
							employee_group.row_id AS employee_group_row_id,
							employee_cost.`cost_id`,
							employee_cost.`cost_center_id`,
							employee_cost.row_id AS employee_cost_row_id,
							employee_base_absence.`quantity`,
							employee_base_absence.row_id AS employee_base_absence_row_id,
							employee_group.valid_from as history_valid_from,
							employee_card.card,
							employee_contract.employee_contract_type,
							employee.nameofbirth,
							employee_ext2.ext2_option1,
							employee_ext2.ext2_option10,
							employee_ext2.ext2_option11,
							employee_ext2.ext2_option12,
							employee_ext.option10,
							employee_ext2.ext2_option13,
							employee_ext2.ext2_option8,
							employee_ext2.ext2_option9,
							employee_ext2.ext2_option14,
							employee_ext2.ext2_option15,
							employee_ext2.ext2_option16,
							workgroup.workgroup_id as workgroup_id,
							employee_ext2.ext2_option17,
							employee_ext2.ext2_option18,
							employee_ext2.ext2_option19,
							employee_ext2.ext2_option20,
							employee_ext2.ext2_option21,
							employee_ext2.ext2_option22,
							employee_ext2.ext2_option23,
							employee_ext2.ext2_option24,
							employee_ext2.ext2_option25,
							employee_ext2.ext2_option26,
							employee_ext2.ext2_option27,
							employee_ext2.ext2_option28,
							employee_ext2.ext2_option29,
							employee_ext2.ext2_option30,
							employee_ext2.ext2_option31,
							employee_ext2.ext2_option32,
							employee_ext.date_of_birth,
							employee_ext.place_of_birth,
							employee_ext.mothers_name,
							employee.tax_number,
							employee_ext.ssn,
							employee_ext.personal_id_card_number,
							employee_ext.option1,
							employee.gender,
							employee_address.address_card_number,
							employee_address.full_address,
							employee_address.zip_code,
							employee_address.city,
							employee_address.district,
							employee_address.public_place_name,
							employee_address.public_place_type,
							employee_address.house_number,
							employee_address.floor,
							employee_address.door,
							employee_address.res_full_address,
							employee_address.res_zip_code,
							employee_address.res_city,
							employee_address.res_district,
							employee_address.res_public_place_name,
							employee_address.res_public_place_type,
							employee_address.res_house_number,
							employee_address.res_floor,
							employee_address.res_door,
							employee_address.accomodated,
							employee_address.acc_zip_code,
							employee_address.acc_city,
							employee_address.acc_district,
							employee_address.acc_public_place_name,
							employee_address.acc_public_place_type,
							employee_address.acc_house_number,
							employee_address.acc_floor,
							employee_address.acc_door,
							employee_ext3.ext3_option1,
							employee_ext3.ext3_option2,
							employee_ext3.ext3_option3,
							employee_ext3.ext3_option4,
							employee_ext3.ext3_option5,
							employee_ext3.ext3_option6,
							employee_ext3.ext3_option7,
							employee_ext3.ext3_option8,
							employee_ext3.ext3_option9,
							employee_ext3.ext3_option10,
							employee_ext3.ext3_option11,
							employee_ext3.ext3_option12,
							employee_ext3.ext3_option13,
							employee_ext3.ext3_option14,
							employee_ext3.ext3_option15,
							employee_ext3.ext3_option16,
							employee_ext3.ext3_option17,
							employee_ext3.ext3_option18,
							employee_ext3.ext3_option19,
							employee_ext3.ext3_option20,
							employee_ext3.ext3_option21,
							employee_ext3.ext3_option22,
							employee_ext3.ext3_option23,
							employee_ext3.ext3_option24,
							employee_ext3.ext3_option25,
							employee_ext3.ext3_option26,
							employee_ext3.ext3_option27,
							employee_ext3.ext3_option28,
							employee_ext3.ext3_option29,
							employee_ext3.ext3_option30,
							employee_ext3.valid_from as ext3_valid_from,
							employee_ext3.valid_to as ext3_valid_from,
							employee_ext3.ext3_option31,
							employee_ext3.ext3_option32,
							employee_ext3.ext3_option33,
							employee_ext3.ext3_option34,
							employee_ext3.ext3_option35,
							employee_ext3.ext3_option36,
							employee_ext3.ext3_option37,
							employee_ext3.ext3_option38,
							employee_ext3.ext3_option39,
							employee_salary.personal_month_salary,
							employee_salary.es_option4,
							employee_salary.es_option5,
							employee_salary.es_option2,
							employee_salary.es_option3,
							employee_salary.es_option6,
							employee_salary.es_option7,
							employee_salary.es_option8,
							employee_salary.es_option9,
							employee_salary.es_option10,
							employee_salary.es_option11,
							employee_salary.es_option12,
							employee_salary.es_option13,
							employee_salary.es_option14,
							employee_salary.es_option15,
							employee_salary.es_option16,
							employee_salary.es_option17,
							employee_salary.es_option18,
							employee_salary.es_option19,
							employee_salary.es_option20,
							employee_salary.es_option21,
							employee_salary.es_option22,
							employee_salary.es_option23,
							employee_salary.es_option24,
							employee_ext2.row_id as employee_ext2_row_id,
							employee_ext3.row_id as employee_ext3_row_id,
							employee_ext.row_id as employee_ext_row_id,
							employee_address.row_id as employee_address_row_id,
							employee_contract.employee_contract_id,
							employee_ext4.row_id as employee_ext4_row_id,
							employee_ext3.ext3_option41,
							employee_ext3.ext3_option42,
							employee_ext3.ext3_option43,
							employee_ext3.ext3_option44,
							employee_ext3.ext3_option45,
							employee_ext3.ext3_option46,
							employee_ext3.ext3_option47,
							employee_ext3.ext3_option48,
							employee_ext3.ext3_option49,
							employee_ext3.ext3_option50,
							employee_ext3.ext3_option51,
							employee_ext3.ext3_option52,
							employee_ext3.ext3_option53,
							employee_ext3.ext3_option54,
							employee_ext3.ext3_option55,
							employee_ext3.ext3_option56,
							employee_ext3.ext3_option57,
							employee_ext3.ext3_option58,
							employee_ext3.ext3_option59,
							employee_ext3.ext3_option60,
							employee_ext3.ext3_option61,
							employee_ext3.ext3_option62,
							employee_ext3.ext3_option63,
							employee_ext3.ext3_option64,
							employee_ext3.ext3_option65,
							employee_ext3.ext3_option66,
							employee_ext3.ext3_option67,
							employee_ext3.ext3_option68,
							employee_ext3.ext3_option69,
							employee_ext3.ext3_option70,
							employee_ext3.ext3_option71,
							employee_ext3.ext3_option72,
							employee_ext3.ext3_option73,
							employee_ext3.ext3_option74,
							employee_ext3.ext3_option75,
							employee_ext3.ext3_option76,
							employee_ext3.ext3_option77,
							employee_ext3.ext3_option78,
							employee_ext3.ext3_option79,
							employee_ext4.ext4_option1,
							employee_ext4.ext4_option2,
							employee_ext4.ext4_option3,
							employee_ext4.ext4_option4,
							employee_ext4.ext4_option5,
							employee_ext4.ext4_option6,
							employee_ext4.ext4_option7,
							employee_ext4.ext4_option8,
							employee_ext4.ext4_option9,
							employee_ext4.ext4_option10,
							employee_ext4.ext4_option11,
							employee_ext4.ext4_option12,
							employee_ext4.ext4_option13,
							employee_ext4.ext4_option14,
							employee_ext4.ext4_option15,
							employee_ext4.ext4_option16,
							employee_ext4.ext4_option17,
							employee_ext4.ext4_option18,
							employee_ext4.ext4_option19,
							employee_ext4.ext4_option20,
							employee_ext4.ext4_option21,
							employee_ext4.ext4_option22,
							employee_ext4.ext4_option23,
							employee_ext4.ext4_option24,
							employee_ext4.ext4_option25,
							employee_ext4.ext4_option26,
							employee_ext4.ext4_option27,
							employee_ext4.ext4_option28,
							employee_ext4.ext4_option29,
							employee_ext4.ext4_option30,
							employee_ext4.ext4_option31,
							employee_ext4.ext4_option32,
							employee_ext4.ext4_option33,
							employee_ext4.ext4_option34,
							employee_ext4.ext4_option35,
							employee_ext4.ext4_option36,
							employee_ext4.ext4_option37,
							employee_ext4.ext4_option38,
							employee_ext4.ext4_option39,
							employee_ext4.ext4_option40,
							employee_ext4.ext4_option41,
							employee_ext4.ext4_option42,
							employee_ext4.ext4_option43,
							employee_ext4.ext4_option44,
							employee_ext4.ext4_option45,
							employee_ext4.ext4_option46,
							employee_ext4.ext4_option47,
							employee_ext4.ext4_option48,
							employee_ext4.ext4_option49,
							employee_ext4.ext4_option50,
							employee_ext4.ext4_option51,
							employee_ext4.ext4_option52,
							employee_ext4.ext4_option53,
							employee_ext4.ext4_option54,
							employee_ext4.ext4_option55,
							employee_ext4.ext4_option56,
							employee_ext4.ext4_option57,
							employee_ext4.ext4_option58,
							employee_ext4.ext4_option59,
							employee_ext4.ext4_option60,
							employee_salary.es_option25,
							employee_salary.es_option26,
							employee_salary.es_option27,
							employee_salary.es_option28,
							employee_salary.es_option29,
							employee_salary.es_option30,
							employee_salary.es_option31,
							employee_salary.es_option32,
							employee_salary.es_option33,
							employee_salary.es_option34,
							employee_salary.es_option35,
							employee_salary.es_option36,
							employee_salary.es_option37,
							employee_salary.es_option38,
							employee_salary.es_option39,
							employee_salary.es_option40,
							employee_salary.es_option41,
							employee_salary.es_option42,
							employee_salary.es_option43,
							employee_salary.es_option44,
							employee_salary.es_option45,
							employee_salary.es_option46,
							employee_salary.es_option47,
							employee_salary.es_option48,
							employee_salary.es_option49,
							employee_salary.es_option50,
							employee_salary.es_option51,
							employee_salary.es_option52,
							employee_salary.es_option53,
							employee_salary.es_option54,
							employee_salary.es_option55,
							employee_salary.es_option56,
							employee_salary.es_option57,
							employee_docs.row_id as employee_docs_row_id,
							employee_contract.employee_contract_id,
							employee_docs.fs_file_id
						FROM
							`employee`
						LEFT JOIN
							`company` ON
								company.`company_id` = employee.`company_id`
									AND ('$validFrom' BETWEEN company.valid_from AND IFNULL(company.`valid_to`, '$defaultEnd'))
										AND company.`status` = $published
						LEFT JOIN
							`payroll` ON
								payroll.`payroll_id` = employee.`payroll_id`
									AND ('$validFrom' BETWEEN payroll.`valid_from` AND IFNULL(payroll.`valid_to`, '$defaultEnd'))
										AND payroll.`status` = $published
						LEFT JOIN
							`unit` ON
								unit.`unit_id` = employee.`unit_id`
									AND ('$validFrom' BETWEEN unit.`valid_from` AND IFNULL(unit.`valid_to`, '$defaultEnd'))
										AND unit.`status` = $published
						LEFT JOIN
							`company_org_group1` ON
								company_org_group1.`company_org_group_id` = employee.`company_org_group1_id`
									AND ('$validFrom' BETWEEN company_org_group1.`valid_from` AND IFNULL(company_org_group1.`valid_to`, '$defaultEnd'))
										AND company_org_group1.`status` = $published
						LEFT JOIN
							`company_org_group2` ON
								company_org_group2.`company_org_group_id` = employee.`company_org_group2_id`
									AND ('$validFrom' BETWEEN company_org_group2.`valid_from` AND IFNULL(company_org_group2.`valid_to`, '$defaultEnd'))
										AND company_org_group2.`status` = $published
						LEFT JOIN
							`company_org_group3` ON
								company_org_group3.`company_org_group_id` = employee.`company_org_group3_id`
									AND ('$validFrom' BETWEEN company_org_group3.`valid_from` AND IFNULL(company_org_group3.`valid_to`, '$defaultEnd'))
										AND company_org_group3.`status` = $published
						LEFT JOIN
							employee_contract ON
								employee_contract.employee_id = employee.employee_id
									AND ('$validFrom' BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`, '$defaultEnd'))
									AND ('$validFrom' BETWEEN employee_contract.`ec_valid_from` AND IFNULL(employee_contract.`ec_valid_to`, '$defaultEnd'))
									AND employee_contract.status = $published
						LEFT JOIN
							employee_group ON
								employee_group.employee_contract_id = employee_contract.employee_contract_id
									AND ('$validFrom' BETWEEN employee_group.`valid_from` AND IFNULL(employee_group.`valid_to`, '$defaultEnd'))
									AND employee_group.status = $published";

		if (!is_null($groupID) && !empty($groupID)) {
			$defaultTabSql .= " AND employee_group.group_id = '" . $groupID . "'";
		}

		$defaultTabSql .= "
						LEFT JOIN
							employee_cost ON
								employee_cost.employee_contract_id = employee_contract.employee_contract_id
									AND ('$validFrom' BETWEEN employee_cost.`valid_from` AND IFNULL(employee_cost.`valid_to`, '$defaultEnd'))
									AND employee_cost.status = $published
						LEFT JOIN
							employee_base_absence ON
								employee_base_absence.employee_contract_id = employee_contract.employee_contract_id
									AND ('$validFrom' BETWEEN employee_base_absence.`valid_from` AND IFNULL(employee_base_absence.`valid_to`, '$defaultEnd'))
									AND employee_base_absence.status = $published
						LEFT JOIN
							employee_card ON
								employee_card.employee_contract_id = employee_contract.employee_contract_id
									AND ('$validFrom' BETWEEN employee_card.`valid_from` AND IFNULL(employee_card.`valid_to`, '$defaultEnd'))
									AND employee_card.status = $published
						LEFT JOIN
							employee_ext ON
								employee_ext.employee_id = employee.employee_id
									AND ('$validFrom' BETWEEN employee_ext.`valid_from` AND IFNULL(employee_ext.`valid_to`, '$defaultEnd'))
									AND employee_ext.status = $published
						LEFT JOIN
							employee_ext2 ON
								employee_ext2.employee_id = employee.employee_id
									AND ('$validFrom' BETWEEN employee_ext2.`valid_from` AND IFNULL(employee_ext2.`valid_to`, '$defaultEnd'))
									AND employee_ext2.status = $published
						LEFT JOIN
							employee_ext3 ON
								employee_ext3.employee_id = employee.employee_id
									AND ('$validFrom' BETWEEN employee_ext3.`valid_from` AND IFNULL(employee_ext3.`valid_to`, '$defaultEnd'))
									AND employee_ext3.status = $published
						LEFT JOIN
							employee_ext4 ON
								employee_ext4.employee_id = employee.employee_id
									AND ('$validFrom' BETWEEN employee_ext4.`valid_from` AND IFNULL(employee_ext4.`valid_to`, '$defaultEnd'))
									AND employee_ext4.status = $published
						LEFT JOIN
							employee_docs ON
								employee_docs.employee_id = employee.employee_id
									AND ('$validFrom' BETWEEN employee_docs.`valid_from` AND IFNULL(employee_docs.`valid_to`, '$defaultEnd'))
									AND employee_docs.status = $published
						LEFT JOIN
							workgroup ON
								workgroup.workgroup_id = employee_contract.workgroup_id
									AND ('$validFrom' BETWEEN workgroup.`valid_from` AND IFNULL(workgroup.`valid_to`, '$defaultEnd'))
									AND workgroup.status = $published
						LEFT JOIN
							employee_address ON
								employee_address.employee_id = employee.employee_id
									AND ('$validFrom' BETWEEN employee_address.`valid_from` AND IFNULL(employee_address.`valid_to`, '$defaultEnd'))
									AND employee_address.status = $published
						LEFT JOIN
							employee_salary ON
								employee_salary.employee_contract_id = employee_contract.employee_contract_id
									AND ('$validFrom' BETWEEN employee_salary.`valid_from` AND IFNULL(employee_salary.`valid_to`, '$defaultEnd'))
									AND employee_salary.status = $published
						WHERE
							employee.`emp_id` = '$editPK'
						AND employee.`status` = $published
						AND ('$validFrom' BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`, '$defaultEnd'))

						GROUP BY employee.employee_id
						ORDER BY
							employee.`valid_from` DESC, employee.`valid_to` DESC
					";

		$results = dbFetchRow($defaultTabSql);

		$returnScript .= '
			var oldValues = [];
			$(".content input, .content select").each(function (){
				var oldVal = $(this).val()
				var fieldId = $(this).prop("id");

				oldValues.push({ "name": fieldId, "value": oldVal });
			});
		';

		$skipFields = [
			'ext4_option30',
			'ext4_option31',
			'ext4_option34',
			'ext4_option36',
			'ext4_option35',
			'ext4_option37',
			'ext4_option38',
			'ext4_option39',
			'ext4_option40',
			'ext4_option41',
			'ext4_option42',
			'ext4_option43',
			'ext4_option44',
			'ext4_option45',
			'ext4_option46',
			'ext4_option47',
			'ext4_option48',
			'ext4_option49',
			'ext4_option50',
			'ext4_option32',
			'ext4_option33',
			'download_files'
		];

		if ($results) {
			$tabColumns = ColumnTabs::getTabColumns($activeTab);
			foreach ($tabColumns as $key => $value) {
				$columnName = $value['column_id'];
				$columnValue = isset($results[$columnName]) ? $results[$columnName] : null;
				$columnType = $value['col_type'];

				if( in_array($columnName, $skipFields) ){
					continue;
				}

				if ($value['save_with_history'] == '1') {
					$groupSQL = "
					SELECT
					    group_value
					FROM employee_group eg
					WHERE
						eg.group_id = '" . $columnName . "'
					AND eg.employee_contract_id = '" . $results['employee_contract_id'] . "'
					AND eg.status = $published
					$groupIntervalSQL
					";

					$groupValue = dbFetchRow($groupSQL);
					if ($groupValue && !empty($groupValue['group_value']) && !is_null($groupValue['group_value']))
					{
						$columnValue = $groupValue['group_value'];
						if ($columnName == App::getSetting('link_group_to_terminal_related_id'))
						{
							$SQL = "
							SELECT
								lgtt.related_id,
								lgtt.related_value,
								lgtt.terminal_id,
								t.terminal_name
							FROM `link_group_to_terminal` lgtt
							LEFT JOIN `terminal` t ON
									t.`terminal_id` = lgtt.`terminal_id`
								AND t.`status` = {$published}
								AND '{$validFrom}' BETWEEN t.`valid_from` AND IFNULL(t.`valid_to`, '{$defaultEnd}')
							WHERE
									lgtt.`related_id` = '{$columnName}'
								AND lgtt.`related_value` = '{$groupValue['group_value']}'
								AND lgtt.`status` = {$published}
							GROUP BY
								lgtt.`terminal_id`
							";
							$relatedFields = dbFetchAll($SQL);
							$order = 0;
							$i = 0;
							if( $relatedFields ){
								foreach( $relatedFields as $key => $val ){
									$i++;
									$order = ($key + 1);
									$returnScript .= "
									$('#dialogInput_" . $activeTab . '_' . $columnName . '_' . $order . "').val('" . $val['terminal_name'] . "');
									" . PHP_EOL;
								}
								$i++;
								for($i; $i <= 60; $i++) {
									$returnScript .= "
									$('#dialogInput_" . $activeTab . '_' . $columnName . '_' . $i . "').val('');
									" . PHP_EOL;
								}
							}
						}
					}
				}else if ( $value['model_name'] == 'EmployeeBaseAbsence' ){
						$showInHour = 0;

						if (App::getSetting('showAbsencesInHours') == 1) {
							$showInHour = 1;
						}

						$absenceSql = "
					SELECT
					    IF('" . $showInHour . "' = 1, quantity_hour, quantity) as value
					FROM employee_base_absence eba
					WHERE
						eba.base_absence_type_id = '" . $columnName . "'
					AND eba.employee_contract_id = '" . $results['employee_contract_id'] . "'
					AND eba.status = $published
					$absenceIntervalSQL
					";

						$absenceValue = dbFetchRow($absenceSql);
						if ($absenceValue && !empty($absenceValue['value']) && !is_null($absenceValue['value'])) {
							$columnValue = $absenceValue['value'];
					}
				}

				$returnScript .= "
				$('#dialogInput_" . $activeTab . '_' . $columnName . "').val('" . $columnValue . "');
				" . PHP_EOL;

				switch ($columnType) {
					case "combo":
						$returnScript .= "
							$('#dialogInput_" . $activeTab . '_' . $columnName . "').trigger('change')
						" . PHP_EOL;
						break;
				}
			}
		}

		$returnScript .= '
			var newValues = [];
			$(".content input, .content select").each(function (){
				var newVal = $(this).val()
				var fieldId = $(this).prop("id");

				newValues.push({ "name": fieldId, "value": newVal });
			});

			for( var i = 0; i < oldValues.length; i++ ){
				if( oldValues[i]["name"] == newValues[i]["name"]){

					if(oldValues[i]["value"] != newValues[i]["value"]){

						$("#"+oldValues[i]["name"]).animate({
						  backgroundColor: "#ffee58",
						},{
						  duration: 700,
						  complete: function (){
							var bgColor = "#F1F1F5";
							if( !$(this).prop("disabled") && !$(this).prop("readonly") ){
							   bgColor = "white";
							}

							$(this).animate({ backgroundColor: bgColor },500);
						  }
						});
					}
				}
			}
		';

		echo $returnScript;

		return;
	}

	public function actionGetColumnHistory()
	{
		$data = $_POST['data'];

		$validFrom = $data['valid_from'];
		$validTo = $data['valid_to'];
		$employeeContractId = $data['employee_contract_id'];
		$fieldName = $data['input'];
		$fieldValue = $data['value'];

		/*
		 * Össze kell fűzni az adatokat az előzőekből és az employee_groupból egyaránt, hogy ne legyen szakadás
		 * a történetiségek között
		 * */
		$sql = "
		SELECT
		    group_value,
			CONCAT(valid_from,' - ',valid_to) as intval
		FROM employee_group
		WHERE employee_contract_id = '" . $employeeContractId . "'
		AND status IN ('$this->published')
		AND group_id = '" . $fieldName . "'
		/*AND LOWER(group_value) NOT LIKE '" . mb_strtolower($fieldValue) . "'*/
		ORDER BY valid_from, valid_to
		";

		$results = dbFetchAll($sql);

		if ($results) {
			$resultString = "";
			foreach ($results as $key => $val) {
				$name = $val['group_value'];
				if ($fieldName == 'workgroup_id') {
					$SQL = "
					SELECT
						workgroup_name
					FROM workgroup
					WHERE workgroup_id = '" . $val['group_value'] . "'
					AND status = '" . $this->published . "'
					AND CURDATE() BETWEEN workgroup.valid_from AND IFNULL(workgroup.valid_to, '" . $this->defaultEnd . "')
					ORDER BY row_id DESC LIMIT 1
					";
					$res = dbFetchRow($SQL);
					if ($res && $res['workgroup_name'] !== null && !empty($res['workgroup_name'])) {
						$name = $res['workgroup_name'];
					}
				} else {
					if ($fieldName == 'unit_id') {
						$SQL = "
					SELECT
						unit_name
					FROM unit
					WHERE unit_id = '" . $val['group_value'] . "'
					AND status = '" . $this->published . "'
					AND CURDATE() BETWEEN unit.valid_from AND IFNULL(unit.valid_to, '" . $this->defaultEnd . "')
					ORDER BY row_id DESC LIMIT 1
					";
						$res = dbFetchRow($SQL);
						if ($res && $res['unit_name'] !== null && !empty($res['unit_name'])) {
							$name = $res['unit_name'];
						}
					} else {
						$isLookupId = AppLookup::isLookupId($fieldName);
						if ($isLookupId) {

							$alWhere = "AND al.lookup_value = '" . $fieldValue . "'";
							if (empty($fieldValue)) {
								$alWhere = "AND al.lookup_default_value = 1";
							}

							$sql = "
						SELECT
							dict.dict_value as name
						FROM app_lookup al
						LEFT JOIN dictionary dict
							ON dict.dict_id = al.dict_id
							AND dict.valid = 1
							AND dict.lang = '" . Dict::getLang() . "'
						WHERE
								al.lookup_id = '" . $fieldName . "'
							{$alWhere}
							AND al.valid = 1
						";
							$res = dbFetchRow($sql);
							if ($res && !empty($res['name']) && !$res['name'] !== null) {
								$name = $res['name'];
							}
						}
					}
				}

				$resultString .= '<option value="' . $val['group_value'] . '">' . $name . ' ' . $val['intval'] . '</option>';

			}

			echo $resultString;
			return;
		}

		return 0;
	}

	public function actionSave($data = array(), $modelName = null, $pk = null, $vOnly = false, $ret = false, $contentId = null) { 
		AnyCache::clear('dialogColumns');
		$this->layout = "//layouts/ajax";

		$this->G2BInit();

		$gridID = requestParam('gridID');
		$generateFrom = requestParam('generateFrom');

		if ($generateFrom === "employeeLockDialog") {
			$form = requestParam("dialogInput_$generateFrom");

			$editPKArr = explode("_", $_POST['editPK']);

			$eID = $editPKArr[0];
			$filter_date = isset($editPKArr[1]) ? $editPKArr[1] : null;
			$lock_date = $form["lock_date"];

			$this->lockEmployee($eID, $filter_date, $lock_date);

			// felhasználó lezárása
			$this->lockUserByEid($eID, $lock_date);

			$status = array(
				'status' => 1,
				'error'  => null,
			);

			echo json_encode($status);
			return;
		}

		if (empty($data) || is_null($data)) {
			$data = $_POST['dialogInput_' . $generateFrom];
		}

		$validFrom = date('Y-m-d', strtotime(date('Y-m-d') . " +0 day"));

		$historyValidFrom = "";
		$historyValidTo = "";

		$getOldHistoryData = false;

		if( isset($_POST['historyInterval']) && !empty($_POST['historyInterval']) ){
			if( stristr($_POST['historyInterval'], ' - ') ){
				$historySplit = explode(' - ', $_POST['historyInterval']);
				$historyValidFrom = $historySplit[0];
				$historyValidTo = $historySplit[1];

				$getOldHistoryData = true;
			}
		}

		if (!is_null($data['valid_from']) && !empty($data['valid_from'])) {
			$validFrom = $data['valid_from'];
		}

		if (!is_null($data['history_valid_from']) && !empty($data['history_valid_from'])) {
			$validFrom = $data['history_valid_from'];
		}

		$prevDay = date("Y-m-d", strtotime($validFrom . " -0 days"));
		$validTo = $this->defaultEnd;

		if (!is_null($data['valid_to']) && !empty($data['valid_to'])) {
			$validTo = $data['valid_to'];
		}

		$statuses = [];
		$errorCount = 0;
		$status = [];

		$alreadySaved = [];

		$editPKArr = explode("_", $_POST['editPK']);

		$employeeId = $editPKArr[0];
		$employeeContractDate = $editPKArr[1];
		$employeeContractSql = "
					SELECT
					employee_contract_id
					FROM employee_contract
					WHERE employee_id = '" . $employeeId . "'
					AND status = '" . Status::PUBLISHED . "'
					AND '$employeeContractDate' BETWEEN employee_contract.valid_from AND IFNULL(employee_contract.valid_to, '" . App::getSetting('defaultEnd') . "') ";
		$query = dbFetchRow($employeeContractSql);

		$employeeContractId = $query['employee_contract_id'];
		$employeeValidFrom = isset($editPKArr[1]) ? $editPKArr[1] : null;

		$defaultEnd = App::getSetting('defaultEnd');

		foreach ( $data as $columnName => $columnValue ){
			if (
				   in_array($columnName, $alreadySaved)
				|| $columnName == 'valid_from'
				|| $columnName == 'valid_to'
			)
			{
				continue;
			}

			if (is_null($columnValue)) {
				if ($columnValue != "0") {
					continue;
				}
			}

			$columnData = $this->getDialogColumn($columnName, $generateFrom);

			if( isset($columnData['options']['skipSave']) ){
				continue;
			}

			$modelName = $columnData['model_name'];
			$pk = $columnData['row_id'];

			if (isset($columnData['history_save']) && $columnData['history_save'] == "1") {
				$modelName = 'EmployeeGroup';
			}

			$m = null;

			$historyInsert = false;

			$tableColumnId = '';
			$tableColumnValue = '';
			$attributes = [];
			$requiredAttrs = [];

			$m = new $modelName;
			$attributes = $m->attributes;
			foreach ($attributes as $attrKey => $attrVal) {
				if ($m->isAttributeRequired($attrKey)) {
					if (!in_array($attrKey, $requiredAttrs) && (is_null($attrVal) || empty($attrVal) )) {
						$requiredAttrs[] = $attrKey;
					}
				}
			}

			if ($modelName == 'EmployeeGroup' || $modelName == 'EmployeeBaseAbsence') {

				$tableColumnId = 'group_id';
				$tableColumnValue = 'group_value';
				if( $modelName == 'EmployeeBaseAbsence' ){
					$tableColumnId = 'base_absence_type_id';
					$tableColumnValue = 'quantity';
				}

				$criteria = new CDbCriteria;
				if( $getOldHistoryData ){
					$criteria->condition = "employee_contract_id = '".$employeeContractId."' AND ".$tableColumnId." = '"
						.$columnName."' AND status = '".$this->published."' AND '".$historyValidFrom."' BETWEEN valid_from AND IFNULL(valid_to, '".$this->defaultEnd."') ";
				}else{
					$criteria->condition = "employee_contract_id = '".$employeeContractId."' AND ".$tableColumnId." = '"
						.$columnName."' AND status = '".$this->published."' AND '$employeeContractDate' BETWEEN valid_from AND IFNULL(valid_to, '".$this->defaultEnd."') ";
				}

				$m = $modelName::model()->find($criteria);

				if( is_null($m) ){
					$m = new $modelName;
				}

				if ($m === null) {
					$pk = null;
					if( is_null($columnValue) || empty($columnValue) ){
						continue;
					}
				} else {
					$pk = $m->row_id;
				}

				if (!is_null($pk) && !empty($pk)) {
					$historyInsert = true;
				} else {
					$m = new $modelName('insert');
					$m->$tableColumnId = $columnName;
					$m->$tableColumnValue = $columnValue;

					if( $modelName == 'EmployeeBaseAbsence' ){
						if ($columnValue > 0) {
							$m->quantity_hour = (new EmployeeAbsence())->employeeAbsenceHourCalculate([
								'valid_from'           => $m->valid_from,
								'valid_to'             => $m->valid_to,
								'employee_contract_id' => $employeeContractId,
								'quantity'             => $columnValue
							]);
						}else{
							continue;
						}
					}
				}

			}else{

				$tableConnect = '';

				if (in_array('employee_id', $requiredAttrs)) {
					$tableConnect = 'employee_id = "'.$employeeId.'"';
				}
				if (in_array('employee_contract_id', $requiredAttrs)) {
					$tableConnect = 'employee_contract_id = "'.$employeeContractId.'"';
				}

				if (!is_null($pk) && !empty($pk)) {

					if( $getOldHistoryData ){
						$criteria = new CDbCriteria;
						$criteria->condition = $tableConnect." AND status = '".$this->published."'
						AND '".$historyValidFrom."' BETWEEN valid_from AND IFNULL(valid_to, '".$this->defaultEnd."') ";

						$m = $modelName::model()->find($criteria);
						if( is_null($m) ){
							$m = new $modelName;
						}
					}else{
						$m = $modelName::model()->findByPk($pk);
					}

					$historyInsert = true;
				} else {

					if( $getOldHistoryData ){
						$criteria = new CDbCriteria;
						$criteria->condition = $tableConnect." AND status = '".$this->published."'
						AND '".$historyValidFrom."' BETWEEN valid_from AND IFNULL(valid_to, '".$this->defaultEnd."') ";

						$m = $modelName::model()->find($criteria);
						if( is_null($m) ){
							$m = new $modelName;
						}
					}else{
						if (App::getSetting('showEmployeeHistoryError') == 1) {
							continue;
						}

						if (isset($_POST['newItem']) && $_POST['newItem'] == "true") {
							$m = new $modelName('insert');
							$historyInsert = true;
						}else{
							$m = new $modelName('insert');
							$historyInsert = false;
						}

					}

				}
			}

			if (in_array('employee_id', $requiredAttrs)) {
				$m->employee_id = $employeeId;
			}
			if (in_array('employee_contract_id', $requiredAttrs)) {
				$m->employee_contract_id = $employeeContractId;
			}


			if (isset($_POST['newItem']) && $_POST['newItem'] == "false") {
				$historyInsert = false;
			}

			$insertModel = null;

			$m->setTitleId($this->getControllerPageTitleId());

			if ($historyInsert && $modelName != "EmployeeTabItem") {
				$insertModel = new $modelName('insert');
				$insertModel->attributes = $m->attributes;

				if ($modelName == "EmployeeGroup" || $modelName == 'EmployeeBaseAbsence') {
					$insertModel->$tableColumnId = $columnName;
					$insertModel->$tableColumnValue = $columnValue;
					if( $modelName == 'EmployeeBaseAbsence' ){
						if ($columnValue > 0) {
							$insertModel->quantity_hour = (new EmployeeAbsence())->employeeAbsenceHourCalculate([
								'valid_from'           => $m->valid_from,
								'valid_to'             => $m->valid_to,
								'employee_contract_id' => $employeeContractId,
								'quantity'             => $columnValue
							]);
						}
					}
					$insertModel->created_by = userID();
					$insertModel->created_on = date('Y-m-d H:i:s');
				}else{
					foreach ($data as $columnName2 => $columnValue2) {
						$columnData2 = $this->getDialogColumn($columnName2, $generateFrom);
						$modelName2 = $columnData2['model_name'];
						if (isset($columnData2['history_save']) && $columnData2['history_save'] == "1") {
							$modelName2 = 'EmployeeGroup';
						}

						//Ha van több mező ugyanabból a modellből akkor ezeket vonjuk össze
						if (
							($columnName != $columnName2)
							&& ($modelName == $modelName2)
							&& !in_array($columnName2, $alreadySaved)
							&& !isset($columnData2['skipSave'])
						) {
							if (
								!isset($columnData2['options']['skipSave'])
								|| (
									isset($columnData2['options']['skipSave'])
									&& is_null($columnData2['options']['skipSave'])
								)
							) {
								$alreadySaved[] = $columnName2;
								if( $m->$columnName2 == $columnValue2 && $validFrom >= date('Y-m-d') ){
									continue;
								}
								$insertModel->$columnName2 = $columnValue2;
							}
						}

					}

					if (!in_array($columnName, $alreadySaved)) {
						$alreadySaved[] = $columnName;
						if( $insertModel->$columnName == $columnValue && empty($alreadySaved) && $validFrom >= date('Y-m-d') ){
							continue;
						}
						$insertModel->$columnName = $columnValue;
					}
				}

				if (isset($insertModel->valid_to)) {
					if( $validTo != $defaultEnd ){
						$insertModel->valid_to = $validTo;
					}else{
						$insertModel->valid_to = $defaultEnd;
					}

					if ($modelName == "EmployeeBaseAbsence") {
						$insertModel->valid_to = $validTo;
					}
				}
				if (isset($insertModel->valid_from)) {
					$insertModel->valid_from = $validFrom;
				}

				$insertModel->status = Status::PUBLISHED;
				if ($modelName == "EmployeeBaseAbsence") {
					$m->status = Status::DELETED;
				} else {
					$m->status = Status::PUBLISHED;
				}

				if (isset($m->valid_to) && $validFrom >= date('Y-m-d')) {
					$prevDay = date("Y-m-d", strtotime($validFrom . " -1 days"));
					if( strtotime($m->valid_from) > strtotime($prevDay) ){
						$prevDay = $m->valid_from;
					}
					$m->valid_to = $prevDay;
				}

			}else{

				if( $modelName == 'EmployeeTabItem' ){
					$m->value = $columnValue;
				}else{

					if ($modelName != "EmployeeGroup" && $modelName != 'EmployeeBaseAbsence') {

						foreach ($data as $columnName2 => $columnValue2) {
							$columnData2 = $this->getDialogColumn($columnName2, $generateFrom);
							$modelName2 = $columnData2['model_name'];
							if (isset($columnData2['history_save']) && $columnData2['history_save'] == "1") {
								$modelName2 = 'EmployeeGroup';
							}

							//Ha van több mező ugyanabból a modellből akkor ezeket vonjuk össze
							if (
								($columnName != $columnName2)
								&& ($modelName == $modelName2)
								&& !in_array($columnName2, $alreadySaved)
								&& !isset($columnData2['skipSave'])
							) {
								if (
									!isset($columnData2['options']['skipSave'])
									|| (
										isset($columnData2['options']['skipSave'])
										&& is_null($columnData2['options']['skipSave'])
									)
								) {
									$alreadySaved[] = $columnName2;
									if( $m->$columnName2 == $columnValue2 && $validFrom >= date('Y-m-d') ){
										continue;
									}
									$m->$columnName2 = $columnValue2;
								}
							}

						}

						if (!in_array($columnName, $alreadySaved)) {
							$alreadySaved[] = $columnName;
							$insertModel = new $modelName('insert');
							if( $insertModel->$columnName == $columnValue && empty($alreadySaved) && $validFrom >= date('Y-m-d') ){
								continue;
							}
							$m->$columnName = $columnValue;
						}

					}else{

						$m->$tableColumnId = $columnName;
						$m->$tableColumnValue = $columnValue;

						if( $modelName == 'EmployeeBaseAbsence' ){
							if ($columnValue > 0) {
								$m->quantity_hour = (new EmployeeAbsence())->employeeAbsenceHourCalculate([
									'valid_from'           => $m->valid_from,
									'valid_to'             => $m->valid_to,
									'employee_contract_id' => $employeeContractId,
									'quantity'             => $columnValue
								]);
							}else{
								continue;
							}
						}

						$m->modified_by = userID();
						$m->modified_on = date('Y-m-d H:i:s');
					}
				}
			}

			if( $modelName == 'EmployeeGroup' || $modelName == 'EmployeeBaseAbsence' ){
				if( $historyInsert ){
					if(
						$modelName == 'EmployeeGroup'
						&& $m->group_value == $insertModel->group_value
						&& $validFrom >= date('Y-m-d')
					){
						continue;
					}else if(
						$modelName == 'EmployeeBaseAbsence'
						&& $m->quantity == $insertModel->quantity
						&& $validFrom >= date('Y-m-d')
					){
						continue;
					}
				}
			}

			if( $getOldHistoryData ){
				if (isset($m->valid_from)) {
					$m->valid_from = $historyValidFrom;
				}

				if (isset($m->valid_to) && $validFrom >= date('Y-m-d')) {
					if( $defaultEnd == $historyValidTo ){
						$m->valid_to = $validTo;
					}else{
						$m->valid_to = $historyValidTo;
					}
				}
			}

			if ($historyInsert && $validFrom >= date('Y-m-d')) {
				if( !is_null($insertModel) && !empty($insertModel) ){
					$insertModel->created_by = userID();
					$insertModel->created_on = date('Y-m-d H:i:s');
				}
				$valid = $m->validate();
			} else {
				$valid = true;
			}

			$warnings = $m->getWarnings();
			if( empty($warnings) || is_null($warnings)){
				$warnings = $m->getErrors();
			}

			if ($valid) {
					$m->save(false);

				if( empty($warnings) || is_null($warnings)){
					$warnings = $m->getErrors();
				}

				if ( empty($warnings) && $historyInsert && $modelName != "EmployeeTabItem") {
					$insertModel2 = new $modelName();
					$insertModel2->attributes = $insertModel->attributes;
					$insertModel = $insertModel2;
					$insertModelValid = $insertModel->validate();
					$warnings = $insertModel->getWarnings();
					if( empty($warnings) || is_null($warnings)){
						$warnings = $insertModel->getErrors();
					}
					if ($insertModelValid) {
						$insertModel->save();
					}
				}

				// Log employee data changes to main log
				if (
					$historyInsert &&
					method_exists($m, "getOldAttributes") &&
					method_exists($m, "getAttributes") &&
					method_exists($m, "getIdentifyColumn")
				) {
					$oldData = $m->getAttributes();
					$newData = $insertModel->getAttributes();
					$identifyColumn = $m->getIdentifyColumn();
					$formId = $generateFrom;
					$changesArray = [];
					// these columns changes are not saved
					$ignoreCols = [
						"modified_on",
						"modified_by",
						"employee_id",
						"employee_contract_id",
						"row_id",
						"created_by",
						"status",
						"group_id",
						"is_default",
					];

					// save column changes to json data rows - main log
					foreach ($newData as $key => $newDataRow) {
						if ($newDataRow != $oldData[$key] && !in_array($key, $ignoreCols)) {
							// When using add button saves with employeeBaseAbsenceTab -> override if column not in employee_base_absence table
							if ($formId == "employeeBaseAbsenceTab") {
								$table = Yii::app()->getDb()->getSchema()->getTable("employee_base_absence");
								$columNames = $table->getColumnNames();
								if (!in_array($key, $columNames)) {
									$formId = "employeeNewEmployeeTab";
								}
							}

							// when Group Tab save group_id aswell
							if ($key == "group_value") {
								$changesArray[] =
									[
										"formId"        => $formId,
										$identifyColumn => $newData[$identifyColumn],
										"prevValue"     => $oldData[$key],
										"newValue"      => $newDataRow,
										"changedColumn" => $key,
										"group_id"      => $newData["group_id"],
									];
							} else {
								$changesArray[] =
									[
										"formId"        => $formId,
										$identifyColumn => $newData[$identifyColumn],
										"prevValue"     => $oldData[$key],
										"newValue"      => $newDataRow,
										"changedColumn" => $key,
									];
							}
						}
					}

					Log::create($this, "GRID_DATA_CHANGE", ["data" => $changesArray]);
				}

				$insertPK = $insertModel->tableSchema->primaryKey;

				$status = array(
					'status'   => 1,
					'pkSaved'  => $insertModel->$insertPK,
					'error'    => null,
					'continue' => 1,
				);
			} else {
				$error = MyActiveForm::_validate($m);

				$arr = (array)json_decode($error);
				$msg = "";

				foreach ($arr as $value) {
					foreach ($value as $val) {
						$msg .= $val . "<br/>";
					}
				}

				$status = array(
					'status'   => 0,
					'pkSaved'  => null,
					'error'    => $msg,
					'continue' => 1,
				);
			}

			$status['warning_status'] = 1;
			$status['warning_msg'] = "";

			if (count($warnings)) {
				$status['warning_status'] = 0;
				$status['warning_msg'] = "";

				foreach ($warnings as $warn) {
					$params = [];

					if (isset($warn["params"])) {
						$params = $warn["params"];
					}

					$status['warning_msg'] .= Dict::getValue($warn["message"], $params) . "<br/>";
				}
			}
			$statuses[] = $status;
		}

		if (is_null($data) || empty($data)) {
			$status["status"] = 1;
			$status["error"] = null;
			$status["continue"] = 0;
		}

		$validFromMessage = str_replace('{date}', $validFrom, Dict::getValue('warning_history_valid_from'));
		$status['validFromMessage'] = $validFromMessage;
		if (App::getSetting('showEmployeeHistoryError') != 1) {
			$status['continue'] = 0;
		}

		if (!$ret) {
			echo json_encode($status);
			return;
		} else {
			return $status;
		}

	}

	public function actionDelete($modelName = null, $hasRight = false)
	{
		$ids = requestParam('ids');

		$idsArray = explode(";", $ids);

		$hasRequests = false;

		foreach ($idsArray as $editPK) {
			$editPKArr = explode("_", $editPK);
			$employee_contract_id = isset($editPKArr[0]) ? $editPKArr[0] : null;

			$hasRequests = $hasRequests || BulkGroupChangeByCogRequest::haveBulkGroupChangeByCogRequest($employee_contract_id);
		}

		if ($hasRequests) {
			$status = array(
				'status'  => 0,
				'message' => Dict::getValue("employee_under_modification_cant_delete"),
			);

			echo json_encode($status);
			return false;
		}

		AnyCache::clear('dialogColumns');
		parent::actionDelete($modelName, $hasRight);
	}

	private function lockEmployee($eID, $filter_date, $lock_date)
	{
		$dinamicTabs = Tab::getTabs();

		$SQL = "
            SELECT
                ";
		foreach ($dinamicTabs as $tab) {
			if ((int)$tab["valid"]) {
				$SQL .= "et_" . $tab["tab_id"] . ".`row_id` as et_" . $tab["tab_id"] . "_row_id,";
			}
		}
		$SQL .= "e.`row_id` AS e_row_id,
				eext.`row_id` AS eext_row_id,
				eext2.`row_id` AS eext2_row_id,
				ea.`row_id` AS ea_row_id,
                ec.`row_id` AS ec_row_id,
				es.`row_id` AS es_row_id,
				ecaf.`row_id` AS ecaf_row_id,
				eba.`row_id` AS eba_row_id,
                ecard.`row_id` AS ecard_row_id,
				ecost.`row_id` AS ecost_row_id
            FROM
                `employee` e
			LEFT JOIN
				`employee_ext` eext ON
					eext.`status` = " . Status::PUBLISHED . "
 				AND eext.`employee_id` = e.`employee_id`
				AND '$filter_date' BETWEEN eext.`valid_from` AND IFNULL(eext.`valid_to`, '" . App::getSetting("defaultEnd") . "')
			LEFT JOIN
				`employee_ext2` eext2 ON
					eext2.`status` = " . Status::PUBLISHED . "
 				AND eext2.`employee_id` = e.`employee_id`
				AND '$filter_date' BETWEEN eext2.`valid_from` AND IFNULL(eext2.`valid_to`, '" . App::getSetting("defaultEnd") . "')
			LEFT JOIN
				`employee_address` ea ON
					ea.`status` = " . Status::PUBLISHED . "
 				AND ea.`employee_id` = e.`employee_id`
				AND '$filter_date' BETWEEN ea.`valid_from` AND IFNULL(ea.`valid_to`, '" . App::getSetting("defaultEnd") . "')
			LEFT JOIN
                `employee_contract` ec ON
					ec.`status` = " . Status::PUBLISHED . "
				AND ec.`employee_id` = e.`employee_id`
				AND '$filter_date' BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '" . App::getSetting("defaultEnd") . "')
				AND '$filter_date' BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '" . App::getSetting("defaultEnd") . "')
			LEFT JOIN `employee_salary` es ON
					es.`status` = " . Status::PUBLISHED . "
 				AND es.`employee_contract_id` = ec.`employee_contract_id`
 				AND '$filter_date' BETWEEN es.`valid_from` AND IFNULL(es.`valid_to`, '" . App::getSetting("defaultEnd") . "')
			LEFT JOIN `employee_cafeteria` ecaf ON
					ecaf.`status` = " . Status::PUBLISHED . "
 				AND ecaf.`employee_contract_id` = ec.`employee_contract_id`
 				AND '$filter_date' BETWEEN ecaf.`valid_from` AND IFNULL(ecaf.`valid_to`, '" . App::getSetting("defaultEnd") . "')
            LEFT JOIN
                `employee_base_absence` eba ON
                    eba.`status` = " . Status::PUBLISHED . "
 				AND eba.`employee_contract_id` = ec.`employee_contract_id`
 				AND '$filter_date' BETWEEN eba.`valid_from` AND IFNULL(eba.`valid_to`, '" . App::getSetting("defaultEnd") . "')
            LEFT JOIN
                `employee_card` ecard ON
                    ecard.`status` = " . Status::PUBLISHED . "
 				AND ecard.`employee_contract_id` = ec.`employee_contract_id`
 				AND '$filter_date' BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`, '" . App::getSetting("defaultEnd") . "')
            LEFT JOIN
                `employee_cost` ecost ON
                    ecost.`status` = " . Status::PUBLISHED . "
 				AND ecost.`employee_contract_id` = ec.`employee_contract_id`
 				AND '$filter_date' BETWEEN ecost.`valid_from` AND IFNULL(ecost.`valid_to`, '" . App::getSetting("defaultEnd") . "')
            ";
		foreach ($dinamicTabs as $tab) {
			if ((int)$tab["valid"]) {
				$SQL .= "LEFT JOIN employee_tab et_" . $tab["tab_id"] . " ON
								et_" . $tab["tab_id"] . ".`tab_id`='" . $tab["tab_id"] . "'
							AND et_" . $tab["tab_id"] . ".`status` = " . Status::PUBLISHED . "
							AND '$filter_date' BETWEEN et_" . $tab["tab_id"] . ".`valid_from` AND IFNULL(et_" . $tab["tab_id"] . ".`valid_to`, '" . App::getSetting("defaultEnd") . "')
							";
				if ($tab["connect"] === Tab::CONNECT_EMPLOYEE) {
					$SQL .= "AND et_" . $tab["tab_id"] . ".`connect_id`= e.`employee_id`
						";
				} else {
					if ($tab["connect"] === Tab::CONNECT_EMPLOYEE_CONTRACT) {
						$SQL .= "AND et_" . $tab["tab_id"] . ".`connect_id`= ec.`employee_contract_id`
						";
					}
				}
			}
		}
		$SQL .= "WHERE
                e.`employee_id` = '$eID'
				    AND e.`status` = " . Status::PUBLISHED . "
				    AND '$filter_date' BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '" . App::getSetting("defaultEnd") . "')
            /*LIMIT 1*/
        ";

		$results = dbFetchAll($SQL);

		foreach ($results as $res) {
			$this->lockEmployeeSave("Employee", $res["e_row_id"], $lock_date);
			$this->lockEmployeeSave("EmployeeExt", $res["eext_row_id"], $lock_date);
			$this->lockEmployeeSave("EmployeeExt2", $res["eext2_row_id"], $lock_date);
			$this->lockEmployeeSave("EmployeeAddress", $res["ea_row_id"], $lock_date);
			$this->lockEmployeeSave("EmployeeContract", $res["ec_row_id"], $lock_date);
			$this->lockEmployeeSave("EmployeeSalary", $res["es_row_id"], $lock_date);
			$this->lockEmployeeSave("EmployeeCafeteria", $res["ecaf_row_id"], $lock_date);
			$this->lockEmployeeSave("EmployeeBaseAbsence", $res["eba_row_id"], $lock_date);
			$this->lockEmployeeSave("EmployeeCard", $res["ecard_row_id"], $lock_date);
			$this->lockEmployeeSave("EmployeeCost", $res["ecost_row_id"], $lock_date);

			foreach ($dinamicTabs as $tab) {
				if ((int)$tab["valid"]) {
					$this->lockEmployeeSave("EmployeeTab", $res["et_" . $tab["tab_id"] . "_row_id"], $lock_date);
				}
			}
		}
		AnyCache::clear('dialogColumns');
	}

	private function lockEmployeeSave($model, $row_id, $lock_date)
	{
		AnyCache::clear('dialogColumns');
		if (!empty($model) && class_exists($model) && !empty($row_id) && !empty($lock_date)) {
			$m = $model::model()->findByPk($row_id);

			if (strtotime($m->valid_from) <= strtotime($lock_date)) {
				$m->valid_to = $lock_date;
			}

			if ($model === "EmployeeContract" && strtotime($m->ec_valid_from) <= strtotime($lock_date)) {
				$m->ec_valid_to = $lock_date;
			}

			$m->save(false); //TODO: zárolásnál nem kell validáln

			return true;
		}

		return false;
	}


	/**
	 * Dolgozó lezárása esetén a felhasználót is lezárjuk a megadot nappal
	 *
	 * @param int    $employee_id
	 * @param string $lock_date
	 */
	public function lockUserByEid($employee_id, $lock_date)
	{

		$SQL = "SELECT * FROM `user` u
				WHERE u.employee_id = '" . $employee_id . "'
				AND u.`status` = " . Status::PUBLISHED . "
				AND '" . $lock_date . "' BETWEEN u.valid_from AND IFNULL(u.`valid_to`, '" . App::getSetting("defaultEnd") . "'); ";

		try {

			$result = dbFetchAll($SQL);

		} catch (Exception $e) {
		}

		$update = '';

		if ((is_array($result)) AND count($result) > 0) {

			foreach ($result as $row) {

				$update .= "UPDATE `user` SET valid_to = '" . $lock_date . "', modified_by = '" . userID() . "', modified_on = NOW()
							WHERE row_id = " . $row['row_id'] . " ; ";

			}

		}

		if ($update != '') {

			try {

				dbExecute($update);

			} catch (Exception $e) {
			}

		}

	}

	public function actionGoToMissingCompetencyByPosition()
	{
		$employee_id = substr(requestParam('editPK'), 0, -11);
		$SQL = "SELECT " . Employee::getParam('fullname_with_emp_id') . " AS fullname
				FROM `employee`
				WHERE `status` = " . Status::PUBLISHED . "
						AND CURDATE() BETWEEN `valid_from` AND default_end(`valid_to`)
						AND employee_id = '$employee_id'";
		$res = dbFetchValue($SQL);
		$_SESSION['tiptime'][userID()]['csm/missingCompetencyByPosition']['searchInput_position'] = requestParam('dialogInput_employeeChangePositionDialog')['position'];
		$_SESSION['tiptime'][userID()]['csm/missingCompetencyByPosition']['searchInput_employee'] = $employee_id;
		$_SESSION['tiptime'][userID()]['csm/missingCompetencyByPosition']['searchInput_auto_employee'] = $res;

		echo json_encode(["status" => 1]);
	}

	/**
	 * Elmenti az összevont feltöltőform tartalmát az uploadDialogApproved jogtól függően:
	 *        -    Ha nincs meg a jogosultság, akkor csak a jóváhagyásra váró dolgozók adatait
	 *            tartalmazó employee_upload_data táblába mentjük az adatokat.
	 *        -    Ha rendelkezik a jogosultsággal, akkor az employee_upload_data táblán felül
	 *            a megfelelő modellekbe és felvisszük a dolgozó adatait. Az employee_upload_data
	 *            táblába ilyenkor jóváhagyottként kerül be a dolgozó.
	 *
	 * Az összes táblában a valid_from, valid_to mezők a szerződés ec_valid_from, ec_valid_to
	 * értékeit veszik fel.
	 *
	 * Az employee_id megegyezik az emp_id értékével, az employee_contract_id értéke pedig az
	 * emp_id-hez hozzáfűzött "1".
	 *
	 * Validálási hiba esetén sehova nem kerülnek mentésre az adatok!
	 */
	public function actionUploadDialogSave()
	{
		$params = requestParam('dialogInput_uploadDialog');

		$data = [];
		foreach ($params as $paramId => $paramVal) {
			$paramIdArray = explode("__", $paramId);
			if (count($paramIdArray) !== 2) continue;

			if (!isset($data[$paramIdArray[0]])) $data[$paramIdArray[0]] = [];
			$data[$paramIdArray[0]][$paramIdArray[1]] = $paramVal;
		}

		if (!isset($data["employeeContractTab"]["ec_valid_from"]) || !isset($data["employeeContractTab"]["ec_valid_to"])) {
			return false;
		}

		$emp_id = $data["employeeTab"]["emp_id"];
		$employee_id = $emp_id;
		$employee_contract_id = $emp_id . "1";
		$valid_from = $data["employeeContractTab"]["ec_valid_from"];
		$valid_to = $data["employeeContractTab"]["ec_valid_to"];

		$valid_to = !empty($valid_to) ? $valid_to : App::getSetting("defaultEnd");

		$wizards = $this->wizards();
		$wizModels = [];
		$wizTitles = [];
		foreach ($wizards['dhtmlxGrid'] as $wiz) {
			$wizModels[$wiz["contentId"]] = $wiz["modelName"];
			$wizTitles[$wiz["contentId"]] = $wiz["contentTitle"];
		}

		$globalParams = [];
		$respArr = [];

		$dataToSave = [];
		foreach ($wizModels as $contentId => $modelName) {
			$model = null;

			if ($modelName && class_exists($modelName)) {
				$model = new $modelName;
			} else {
				continue;
			}

			if (!$model) continue;

			//$modelIdentifyColumn = $modelName::model()->getIdentifyColumn();

			/*if (!isset($globalParams[$modelIdentifyColumn])) {
				$globalParams[$modelIdentifyColumn] = md5(__CLASS__.date('YmdHis').rand(0,1000).$modelName.$modelIdentifyColumn);
			}*/

			$dataToSave[$contentId] = [];
			if (isset($data[$contentId])) $dataToSave[$contentId] = $data[$contentId];

			if ($modelName === 'EmployeeTab') {
				$dataToSave[$contentId]["employee_id"] = $employee_id;
				$dataToSave[$contentId]["employee_contract_id"] = $employee_contract_id;
			}

			if ($model->hasAttribute("emp_id")) $dataToSave[$contentId]["emp_id"] = $emp_id;
			if ($model->hasAttribute("employee_id")) $dataToSave[$contentId]["employee_id"] = $employee_id;
			if ($model->hasAttribute("employee_contract_id")) $dataToSave[$contentId]["employee_contract_id"] = $employee_contract_id;
			if ($model->hasAttribute("valid_from")) $dataToSave[$contentId]["valid_from"] = $valid_from;
			if ($model->hasAttribute("valid_to")) $dataToSave[$contentId]["valid_to"] = $valid_to;

			/*foreach ($globalParams as $gpColumn => $gpValue) {
				$dataToSave[$contentId][$gpColumn] = $gpValue;
			}*/

			$respArr[$contentId] = Grid2Controller::actionSave($dataToSave[$contentId], $modelName, $model->tableSchema->primaryKey, true, true, $contentId);
		}

		$status = [];
		$status["status"] = 1;
		$status["error"] = "";

		foreach ($respArr as $contentId => $errors) {
			$status["status"] = $status["status"] && $errors["status"];

			if (!empty($errors["error"])) {
				$status["error"] .= '<span style="font-size:18px;font-weight:bold;">' . $wizTitles[$contentId] . '</span>' . "<br/>" . $errors["error"];
			}
		}

		if ($status["status"]) {
			$status["status"] = 1;
			$status["error"] = null;

			$saveDataToJSON = [];

			foreach ($wizModels as $contentId => $modelName) {
				$model = null;

				if ($modelName && class_exists($modelName)) {
					$model = new $modelName;
				} else {
					continue;
				}

				if (!$model) continue;

				$saveDataToJSON[$modelName] = $dataToSave[$contentId];

				if ((int)App::getRight($this->getControllerId(), "uploadDialogApproved")) {
					Grid2Controller::actionSave($dataToSave[$contentId], $modelName, $model->tableSchema->primaryKey, false, true, $contentId);
				}
			}

			$eud = new EmployeeUploadData;
			$eud->employee_contract_id = isset($globalParams["employee_contract_id"]) ? $globalParams["employee_contract_id"] : "";
			$eud->employee_id = isset($globalParams["employee_id"]) ? $globalParams["employee_id"] : "";
			$eud->data = json_encode($saveDataToJSON);
			$eud->created_by = userID();
			$eud->created_on = date("Y-m-d H:i:s");

			if ((int)App::getRight($this->getControllerId(), "uploadDialogApproved")) {
				$eud->is_approved = 1;
				$eud->approved_by = $eud->created_by;
				$eud->approved_on = $eud->created_on;
			}

			$eud->save();
		}

		echo json_encode($status);
	}

	public function actionAbsenceCalc()
	{
		$absenceCalc = new EmployeeAbsenceCalculation($_SESSION['tiptime'][userID()]['employeecontrol']);

		if ($absenceCalc->calculate()) {
			$status = [
				'status'  => 1,
				'title'   => Dict::getValue('message'),
				'message' => Dict::getValue('successful_save')
			];
		} else {
			$status = [
				'status'  => 0,
				'title'   => Dict::getValue('message'),
				'message' => Dict::getValue('an_error_occured')
			];
		}

		echo json_encode($status);
	}

}