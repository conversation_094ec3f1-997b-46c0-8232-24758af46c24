<?php

class BackupandRestorationController extends Grid2Controller
{
	private $user;
	private $fullpath;
	private $backupStatus = 0;
	private $backupMaxSaveCount = 4;

	public function __construct()
	{
		parent::__construct("backupandRestoration");
		$customerDbPatchName 	= Yang::getParam('customerDbPatchName');
		$this->fullpath = Yang::getBasePath() . '/../webroot/file_storage/backup/' . $customerDbPatchName;
		$this->actionbackupandRestorationGC();
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("BackupandRestoration", "dhtmlxGrid");
		parent::setControllerPageTitleId("page_title_backupandRestoration");
		parent::enableMultiGridMode();

		$this->LAGridRights->overrideInitRights("paging", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search_header", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("select", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("column_move", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload_sortings", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("col_sorting", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("modify", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("delete", true, "dhtmlxGrid");

		$this->LAGridRights->overrideInitRights("details", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("multi_select", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search", false, "dhtmlxGrid");

		$this->user = new User();
		$backupandRestoration = new BackupandRestoration();
		$c = new CDbCriteria();
		$c->order = "`row_id` DESC";
		$this->LAGridDB->setModelSelection($backupandRestoration, $c, "dhtmlxGrid");

		$path = Yang::addAsset(Yang::getAlias('application.assets.base'), false, -1, true);
		Yang::registerScriptFile($path . '/backupandRestoration/js/backupandRestoration.js');

		parent::G2BInit();
	}

	public function actionDelete($modelName = null, $hasRight = false)
	{
		$this->G2BInit();
		$gridID = requestParam('gridID');
		$gridID = empty($gridID) ? "dhtmlxGrid" : $gridID;
		$row_id = requestParam('ids');

		$backupgc = new BackupandRestoration();
		$crit = new CDbCriteria();
		$crit->select = "`filename`";
		$crit->condition = "`row_id`= $row_id";
		$backupgcRes = $backupgc->findAll($crit);

		foreach ($backupgcRes as $res) {
			$filePath = $this->fullpath . '/' . $res->filename . '.sql.zip';
			@unlink($filePath);
		}

		$crit = new CDbCriteria();
		$crit->condition = "`row_id`= $row_id";
		BackupandRestoration::model()->deleteAll($crit);
	}

	protected function getStatusButtons($gridID = null)
	{
		$buttons = [];

		$originalButtons = parent::getStatusButtons();

		$buttons["backup"] = [
			"type" => "button",
			"id" => "backup",
			"class" => "backup",
			"name" => "backup",
			"img" => "/images/status_icons/st_backup_opened.png",
			"label" => "backup",
			"onclick" => 	"backup('" . Dict::getValue("backup") . "', '" . Dict::getValue("errors") . "');",
		];

		$buttons["restoration"] = [
			"type" => "button",
			"id" => "restoration",
			"class" => "restoration",
			"name" => "restoration",
			"img" => "/images/status_icons/st_restore_opened.png",
			"label" => "restoration",
			"onclick" => 	"restoration('" . Dict::getValue("please_select_line") . "', '" . Dict::getValue("are_you_restore_the_previous_backup") . "');",
		];

		return Yang::arrayMerge($originalButtons, $buttons);
	}

	public function actionbackup()
	{
		$backupMod = requestParam('dialogInput_backupDialog');
		$backupDumpSetting = $this->backupMods($backupMod);
		$backupModType = $backupMod['backup_type'];
		$dbBackupStatus = 1;

		$crit = new CDbCriteria();
		$crit->condition = "`backup_status`= 0 AND backup_mod = $backupModType";
		$count = BackupandRestoration::model()->count($crit);
		$msg = '';
		if ($count > $this->backupMaxSaveCount) {
			$dbBackupStatus = 2;
			$msg = Dict::getValue("error_max_backup");
		}

		$dumpSettings = [
			'include-tables' => $backupDumpSetting['tables'],
			'add-drop-table' => true,
		];

		$dbParam['dbHost'] 		= Yang::getParam('dbHost');
		$dbParam['dbUser'] 		= Yang::getParam('dbUser');
		$dbParam['dbPass'] 		= Yang::getParam('dbPass');
		$dbParam['dbName'] 		= Yang::getParam('dbName');

		$fileName = 'backup' . '_' . date("Y-m-d") . '_' . rand(100, 999);
		$fullFileName = $fileName . '.sql';

		if (!file_exists($this->fullpath)) {
			mkdir($this->fullpath, 0750, true);
		}

		$filePath = $this->fullpath . '/' . $fullFileName;

		$dbBackupStatus == 1 ? $dbBackupStatus = $this->dbBackupSQL($dumpSettings, $dbParam, $filePath) : $dbBackupStatus;
		$dbBackupStatus == 1 ? $dbBackupStatus = $this->dbBackupZip($filePath, $fullFileName) : $dbBackupStatus;

		$operation = Dict::getValue("backup");
		$note = $backupDumpSetting['note'];
		$dbBackupStatus == 1 ? $this->dbBackupWriteInTable(0, $operation, $fileName, $note, $backupModType) : $dbBackupStatus;

		$status = [
			'status' => $dbBackupStatus,
			'title' => '',
			'error'	=> $msg
		];
		echo json_encode($status);
	}

	private function dbBackupSQL($dumpSettings, $dbParam, $filePath)
	{
		require_once(Yang::getBasePath() . DIRECTORY_SEPARATOR . 'extensions' . DIRECTORY_SEPARATOR . 'Ifsnop' . DIRECTORY_SEPARATOR . 'Mysqldump' . DIRECTORY_SEPARATOR . 'Mysqldump.php');
		$status = 1;
		try {
			$dbHost = $dbParam['dbHost'];
			$dbName = $dbParam['dbName'];
			$dbUser = $dbParam['dbUser'];
			$dbPass = $dbParam['dbPass'];
			$dump = new Ifsnop\Mysqldump\Mysqldump('mysql:host=' . $dbHost . ';dbname=' . $dbName . '', $dbUser, $dbPass, $dumpSettings);
			$dump->start($filePath);
		} catch (\Exception $e) {
			$status = 2;
		}

		return $status;
	}

	private function dbBackupZip($filePath, $fileName)
	{
		$dbBackupStatus = 1;
		$zipFilePath = $filePath . '.zip';
		$zip = new ZipArchive();
		if (!$zip->open($zipFilePath, ZipArchive::CREATE)) {
			$dbBackupStatus = 2;
			return $dbBackupStatus;
		}
		$zip->addFile($filePath, $fileName);
		$zip->close();
		@unlink($filePath);

		return $dbBackupStatus;
	}

	private function dbBackupWriteInTable($operation_id, $operation, $fileName, $note, $backupModType = null, $createdBy = '')
	{
		$operation_id == 0 ? $expirationDate = date("Y-m-d", strtotime("+3 month")) : $expirationDate = date("Y-m-d");
		$writeInTable = new BackupandRestoration();
		$writeInTable->created_by = UserID() ?? $createdBy;
		$writeInTable->created_on = date("Y-m-d H:i");
		$writeInTable->operation_id = $operation_id;
		$writeInTable->operation = $operation;
		$writeInTable->filename = $fileName;
		$writeInTable->note = $note;
		$writeInTable->expiration_date = $expirationDate;
		if ($operation_id == 0) {
			$writeInTable->backup_status = $this->backupStatus;
			$writeInTable->backup_mod = $backupModType;
		}

		$writeInTable->save();
	}

	public function actionrestoration()
	{
		$rowId         = requestParam('selected_id');
		$selectedBackup = BackupandRestoration::model()->findByPk($rowId);
		$dbBackupStatus = 1;
		$msg = Dict::getValue("restoration_ready");
		if ($selectedBackup->operation_id == 1) {
			$msg = Dict::getValue("no_save_operation_selected");
			$dbBackupStatus = 2;
		}

		if ($selectedBackup->backup_status == 1) {
			$msg = Dict::getValue("error_delete_restored");
			$dbBackupStatus = 2;
		}
        $preSql = '';
        if ((int)$selectedBackup->backup_mod == 99) {
            $preSql = "DELETE FROM `approver` WHERE `created_by` = 'XMLAutoImport_AEI' AND `modified_by` is null;";
        }

		$dbBackupStatus == 1 ? $this->dbBackupUnzip($selectedBackup) : $dbBackupStatus;

		$dbBackupStatus == 1 ? $this->dbBackupUnSQL($selectedBackup, $preSql) : $dbBackupStatus;

		$operation = Dict::getValue("restoration");
		$note = $selectedBackup->note . ' ' . Dict::getValue("has_been_restored");
		$fileName = $selectedBackup->filename;
		$dbBackupStatus == 1 ? $this->dbBackupWriteInTable(1, $operation, $fileName, $note) : $dbBackupStatus;

		$status = [
			'status'	=> $dbBackupStatus,
			'title' 	=> Dict::getValue("restoration"),
			'msg'		=> $msg,
		];
		echo json_encode($status);
	}

	private function dbBackupUnSQL($selectedBackup, $preSql = '')
	{
        if (!empty($preSql)) { dbExecute($preSql); }

		$filePath = $this->fullpath . '/' . $selectedBackup->filename . '.sql';
		$file = new SplFileObject($filePath, 'r');
		$delimiter = false;
		$query = '';
		while (!$file->eof()) {
			$line  =  $file->fgets();
			if (substr($line, 0, 2)=='--' or trim($line)=='') {
				continue;
			}

			if (trim($line) == 'DELIMITER ;;') {
				$delimiter = true;
				continue;
			}

			if ($delimiter && trim($line) != 'DELIMITER ;') {
				$query .= $line;
				continue;
			}

			if ($delimiter) {
				dbExecute($query);
				$query = '';
				$delimiter = false;
				continue;
			}
			$query .= $line;
			if (substr(trim($query), -1)==';') {
				dbExecute($query);
				$query = '';
			}
		}
		@unlink($filePath);
		return 1;
	}

	private function dbBackupUnzip($selectedBackup)
	{
		$filePath = $this->fullpath . '/' . $selectedBackup->filename . '.sql.zip';
		$zip = new ZipArchive();
		if ($zip->open($filePath) === true) {
			$zip->extractTo($this->fullpath);
			$zip->close();
			$status = 1;
		} else {
			$status = 2;
		}
		return $status;
	}

	private function backupMods($backupMod)
	{
		//Dolgozói törzsadatok
		$employeeMasterData = [
			'employee',
			'employee_address',
			'employee_base_absence',
			'employee_cafeteria',
			'employee_card',
			'employee_competency',
			'employee_contract',
			'employee_cost',
			'employee_docs',
			'employee_ext',
			'employee_ext2',
			'employee_ext3',
			'employee_ext4',
			'employee_group',
			'employee_salary',
		];
		//Felhasználói adatok
		$userMasterData = [
			'approver',
			'approver_level',
			'approver_related_group',
			'approver_related_type',
			'auth_role',
			'auth_rolegroup',
			'auth_role_in_group',
			'user',
			'user_report_rights',
		];
		//Szoftver beállítások
		$softverSettingsData = [
			'employee_group_config',
			'daytype_visibility_group',
			'absence_daytype_by_workgroup',
			'summary_sheet_limitation',
			'work_schedule_limitation',
			'work_schedule_by_group_max_days',
			'event_type_right',
			'holiday_planner_limitations',
			'holiday_planner_group_limitations',
			'state_type_right',
			'state_type',
			'shift_start_type',
			'cplatform_button_config',
		];
		//Céges adatok
		$companyMasterData = [
			'daytype',
			'terminal',
			'company',
			'payroll',
			'workgroup',
			'unit',
			'cost',
			'cost_center',
			'employee_position',
			'company_org_group1',
			'company_org_group2',
			'company_org_group3',
		];
		//Beosztás + túlóra
		$workScheduleData = [
			'employee_extra_hours',
			'work_schedule_by_employee',
			'work_schedule_by_unit',
			'work_schedule_overtime',
			'work_schedule_saved',
			'work_schedule_used',
		];

		//Mentett távollét és munkaidő adatok
		$employeeCalcAndAbsenceData = [
			'employee_calc',
			'employee_absence',
		];

		$allData = Yang::arrayMerge($employeeMasterData, $userMasterData, $softverSettingsData, $companyMasterData, $workScheduleData);

		switch ($backupMod['backup_type']) {
			case '0':
				$backup['tables'] = $allData;
				$backup['note'] = Dict::getValue("save_entire_data_set");
				break;
			case '1':
				$backup['tables'] = $employeeMasterData;
				$backup['note'] = Dict::getValue("save_employee_strain_data");
				break;
			case '2':
				$backup['tables'] = $userMasterData;
				$backup['note'] = Dict::getValue("save_user_data");
				break;
			case '3':
				$backup['tables'] = $softverSettingsData;
				$backup['note'] = Dict::getValue("save_software_settings");
				break;
			case '4':
				$backup['tables'] = $companyMasterData;
				$backup['note'] = Dict::getValue("save_company_data");
				break;
			case '5':
				$backup['tables'] = $workScheduleData;
				$backup['note'] = Dict::getValue("save_job_details");
				break;
			case '6':
				$backup['tables'] = $employeeCalcAndAbsenceData;
				$backup['note'] = Dict::getValue("saved_absence_and_working_time_data");
				break;
			default:
				$backup['tables'] = $allData;
				$backup['note'] = Dict::getValue("save_entire_data_set");
				break;
		}

		return $backup;
	}

	public function actionbackupandRestorationGC()
	{
		$backupgc = new BackupandRestoration();
		$crit = new CDbCriteria();
		$crit->select = "`filename`";
		$crit->condition = "`backup_status`= 0 AND expiration_date < CURDATE()";
		$backupgcRes = $backupgc->findAll($crit);

		foreach ($backupgcRes as $res) {
			$filePath = $this->fullpath . '/' . $res->filename . '.sql.zip';
			@unlink($filePath);
		}

		BackupandRestoration::model()->updateAll(['backup_status'=> '1'], '`backup_status`= 0 AND expiration_date < CURDATE()');
	}

	public function columns()
	{
		$columns["dhtmlxGrid"] = [
			'created_by'	=>  ['width' => 250, 'col_type' => 'combo', 'grid' => true, 'export' => false, 'align' => 'left', 'window' => false,
				'options'	=>	[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'	=> $this->user,
					'comboId'				=> 'user_id',
					'comboValue'			=> 'username',
					'array'	=> [["id"=>"ALL", "value"=>Dict::getValue("all")]],
				],
			],
			'created_on'		=> ['width' => 200, 'col_type' => 'ro', 'grid' => true, 'export' => false, 'window' => false],
			'expiration_date'	=> ['width' => 200, 'col_type' => 'ro', 'grid' => true, 'export' => false, 'window' => false],
			'operation'			=> ['width' => 200, 'col_type' => 'ro', 'grid' => true, 'export' => false, 'window' => false],
			'filename'			=> ['width' => 250, 'col_type' => 'ro', 'grid' => true, 'export' => false, 'window' => false],
			'note'				=> ['width' => 250, 'col_type' => 'ed', 'grid' => true, 'export' => false, 'window' => true],
			'backup_status'		=> ['width' => 100, 'col_type' => 'combo', 'grid' => true, 'export' => false, 'window' => false,
				'options'	=>	[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
					'array'=> [
						['id'=> '0',	'value'	=> 'Aktív'],
						['id'=> '1',	'value'	=> 'Törölt!'],
					],
				],
			],
		];

		$backupMODS = [
			["id" => "0", "value" => Dict::getValue("save_entire_data_set")],
			["id" => "1", "value" => Dict::getValue("save_employee_strain_data")],
			["id" => "2", "value" => Dict::getValue("save_user_data")],
			["id" => "3", "value" => Dict::getValue("save_software_settings")],
			["id" => "4", "value" => Dict::getValue("save_company_data")],
			["id" => "5", "value" => Dict::getValue("save_job_details")],
			["id" => "6", "value" => Dict::getValue("saved_absence_and_working_time_data")],
		];

		$columns["backupDialog"] = [
			'backup_type'	=> ['label_text' => Dict::getValue("types_of_backups"), 'col_type' => 'combo',
				'options' =>	[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
					'array'	=> $backupMODS,
				],
			],
		];
		return $columns;
	}

	public function attributeLabels()
	{
		$attributeLabels['dhtmlxGrid'] = [
			'created_by'	=> Dict::getValue("started_an_operation"),
			'created_on'	=> Dict::getValue("date_of_operation"),
			'operation'		=> Dict::getValue("operation"),
			'filename'		=> Dict::getValue("save_name"),
			'note'			=> Dict::getValue("note"),
			'expiration_date'	=> Dict::getValue("recovery_deadline"),
			'backup_status'		=> Dict::getValue("save_status"),
		];

		$attributeLabels['backupDialog'] = [
			'backup_type'		=> '',
		];

		return $attributeLabels;
	}

	public function filters()
	{
		return [
			'accessControl', // perform access control for CRUD operations
		];
	}

	public function accessRules()
	{
		return [
            ['allow',
				'actions'=>array('backupWdApprover'),
				'users'=>array('*'),
            ],
			['allow', // allow authenticated users to access all actions
				'users' => ['@'],
			],
			['deny',  // deny all users
				'users' => ['*'],
			],
		];
	}

    public function actionbackupWdApproverAEI()
	{
		$dbBackupStatus = 1;
        $backupModType = 99;
        $this->actionbackupandRestorationGC();

		$dumpSettings = [
			'include-tables' => ['approver'],
			'add-drop-table' => false,
            'skip-triggers' => true,
            'add-drop-trigger' => false,
            'if-not-exists' => true,
            'where' => 'created_by = "XMLAutoImport_AEI" AND modified_by is null',
		];

		$dbParam['dbHost'] 		= Yang::getParam('dbHost');
		$dbParam['dbUser'] 		= Yang::getParam('dbUser');
		$dbParam['dbPass'] 		= Yang::getParam('dbPass');
		$dbParam['dbName'] 		= Yang::getParam('dbName');

		$fileName = 'backup' . '_' . date("Y-m-d") . '_' . rand(100, 999);
		$fullFileName = $fileName . '.sql';

		if (!file_exists($this->fullpath)) {
			mkdir($this->fullpath, 0750, true);
		}

		$filePath = $this->fullpath . '/' . $fullFileName;

		$dbBackupStatus == 1 ? $dbBackupStatus = $this->dbBackupSQL($dumpSettings, $dbParam, $filePath) : $dbBackupStatus;
		$dbBackupStatus == 1 ? $dbBackupStatus = $this->dbBackupZip($filePath, $fullFileName) : $dbBackupStatus;

		$operation = Dict::getValue("backup");
		$note = "WD Approver tábla AEI sorok";
		$dbBackupStatus == 1 ? $this->dbBackupWriteInTable(0, $operation, $fileName, $note, $backupModType, "WD Sync") : $dbBackupStatus;

		$status = [
			'status' => $dbBackupStatus,
			'title' => '',
			'error'	=> ''
		];

		return $status;
	}
}
