<?php

class Questionnaire<PERSON>ontroller extends Controller
{
	const MAX_POSSIBLE_ANSWERS 					= 5;
    private int $publishedStatus 				= Status::PUBLISHED;
	private int $draftStatus					= Status::DRAFT;
	private int $deletedStatus					= Status::DELETED;
	private int $answeredAndSavedStatus 		= Status::STATUS_ANSWERED_AND_SAVED;
	private int $answeredAndLockedStatus 		= Status::STATUS_ANSWERED_AND_LOCKED;
	private int $answeredAndSuccessfulStatus	= Status::STATUS_ANSWERED_AND_SUCCESSFUL;
	private int $answeredAndFailedStatus		= Status::STATUS_ANSWERED_AND_FAILED;
	private int $timeoutStatus					= Status::STATUS_TIMEOUT;

	public function actionGetQuestionnaires()
	{
		$questionnaireResults = Questionnaire::model()->findAllByAttributes(['status' => $this->publishedStatus]);
		$results = [];

		if (count($questionnaireResults) > 0) 
		{
			$results["empty"] = 0;

			foreach ($questionnaireResults as $result) 
			{
                $results['questionnaires'][] = [
					'questionnaireId' 	=> $result->questionnaire_id,
					'questionnaireName'	=> $result->questionnaire_name
				];
            }
		} else {
			$results["empty"] = 1;
		}

		echo json_encode($results);
	}

	public function actionCheckNewExams()
	{
		$openEmployeeCompetencyExam = $this->getOpenEmployeeCompetencyExam();
		$results = [];

		if (count($openEmployeeCompetencyExam) > 0) {
			$results['existing'] = true;
		} else {
			$results['existing'] = false;
		}

		echo json_encode($results);
	}

	public function actionGetQuestionnairesFromEmployeeCompetencyExam()
	{
		$openEmployeeCompetencyExam = $this->getOpenEmployeeCompetencyExam();
		$results = [];

		if (count($openEmployeeCompetencyExam) > 0) 
		{
			$results["empty"] = 0;

			foreach ($openEmployeeCompetencyExam as $exam) 
			{
				$results['questionnaires'][] = [
					'questionnaireId' 	=> $exam->competency_id,
                    'questionnaireName' => (new Competency)->getCompetencyNameById($exam->competency_id)
				];
			}
		} else {
			$results["empty"] = 1;
			$results["message"] = Dict::getValue("noOpenExam");
		}

		echo json_encode($results);
	}

	public function actionGetQuestionsAndAnswers() 
    {
		$questionnaireId 	= requestParam('questionnaireId');
		$userId          	= userID();
		$results			= [];
        $questionnaireName  = (new Competency)->getCompetencyNameById($questionnaireId);

		$questionAndAnswerResults 	= QuestionnaireManagement::model()->findAllByAttributes(['status' => $this->publishedStatus, 'questionnaire_id' => $questionnaireId]);
		
		$existingAnswers 	= QuestionnaireAnswer::model()->findAllByAttributes([
			'status' 			=> $this->answeredAndSavedStatus, 
			'questionnaire_id' 	=> $questionnaireId, 
			'created_by' 		=> $userId
		]);

		$indexedExistingAnswers = [];

		foreach ($existingAnswers as $result) {
            $indexedExistingAnswers[$result->question_id][] = $result;
        }

		if (count($questionAndAnswerResults) > 0) 
		{
			$results["empty"]               = 0;
            $results["questionnaireName"]   = $questionnaireName;

			foreach ($questionAndAnswerResults as $question) 
			{
				$questionAnswered = array_key_exists($question->question_id, $indexedExistingAnswers);

				$q = &$results['questions'][$question->question_id];
				$q = [
					'questionnaireId' 	=> $question->questionnaire_id,
					'questionId'		=> $question->question_id,
					'questionOrder'		=> $question->question_order,
					'questionText'		=> $question->question_text,
					'questionStatus'	=> ($question->question_order == 1) ? 'current' : ($questionAnswered ? 'answered' : 'noanswer'),
					'answers'			=> []
				];

				for ($i = 1; $i <= (self::MAX_POSSIBLE_ANSWERS); $i++) 
				{
					if (!empty($question->{"question_answer_text_$i"})) 
					{
						$results['questions'][$question->question_id]['answers'][] = [
							'questionAnswerId'		=> $question->{"question_answer_id_$i"},
							'questionAnswerText'	=> $question->{"question_answer_text_$i"},
							'selectedAnswer'		=> ($indexedExistingAnswers[$question->question_id]->question_answer_response === $question->{"question_answer_id_$i"}) ? 
															true : false,
							'correctAnswer'			=> md5($question->{"question_answer_id_$i"} . $question->{"question_answer_is_right_$i"})
						];
					}
				}
			}
		} else {
			$results["empty"] = 1;
			$results["message"] = Dict::getValue("noQuestionsAndAnswers");
		}

		echo json_encode($results);
	}

	public function actionSaveAnswer() 
	{
		$questionnaireId 		= requestParam('questionnaireId');
		$questionId 			= requestParam('questionId');
		$questionAnswerResponse = requestParam('questionAnswerResponse');
		$correctAnswer			= requestParam('correctAnswer');
		$userId 				= userID();
		$response 				= [];

		$answerModel = QuestionnaireAnswer::model()->findByAttributes([
			'questionnaire_id' 	=> $questionnaireId, 
			'question_id' 		=> $questionId, 
			'created_by' 		=> $userId, 
			'status' 			=> $this->answeredAndSavedStatus
		]);

		if (!$answerModel) {
			$answerModel = new QuestionnaireAnswer();
			$answerModel->questionnaire_id = $questionnaireId;
			$answerModel->question_id = $questionId;
			$answerModel->status = $this->answeredAndSavedStatus;
		}

		$answerModel->question_answer_response 	= $questionAnswerResponse;
		$answerModel->correct_answer			= $correctAnswer;

		try {
			$answerModel->save();
			$response["status"] = 1;
		} catch (\Exception $e) {
			$response["status"] = 0;
		}

		echo json_encode($response);
	}

	public function actionLockAllAnswer()
	{
		$questionnaireId 		= requestParam('questionnaireId');
		$userId 				= userID();
		$response 				= [];

		$answers = QuestionnaireAnswer::model()->findAllByAttributes([
			'questionnaire_id' 	=> $questionnaireId, 
			'created_by' 		=> $userId, 
			'status' 			=> $this->answeredAndSavedStatus
		]);
		
		foreach ($answers as $answer) 
		{
			$answer->status = $this->answeredAndLockedStatus;
		
			try {
				if($answer->save()) {
					$response["status"] = 1;
				} else {
					$response["status"] = 0;
				}
			} catch (\Exception $e) {
				$response["status"] = 0;
				$response["message"] = Dict::getValue("errorLockAnswers");
			}
		}

		echo json_encode($response);
	}

	public function actionGetExamResult()
	{
		$questionnaireId 		= requestParam('questionnaireId');
		$userId 				= userID();
		$currentEcId			= (new User)->getUserEmployeeContractID();
		$response 				= [];
		$resultText				= "";
		$allAnswers				= 0;
		$correctAnswers			= 0;
		$wrongAnswers			= 0;
		$successCriteria		= 0.8;
		$successful				= 0;

		$lockedAnswers = QuestionnaireAnswer::model()->findAllByAttributes([
			'questionnaire_id' 	=> $questionnaireId, 
			'created_by' 		=> $userId, 
			'status' 			=> $this->answeredAndLockedStatus
		]);

		foreach ($lockedAnswers as $lockedAnswer)
		{
			$currentAnswer = $lockedAnswer->question_answer_response;
			$correctAnswer = $lockedAnswer->correct_answer;

			$allAnswers++;
			(md5($currentAnswer . 1) == $correctAnswer) ? $correctAnswers++ : $wrongAnswers++;
		}

		$successful = ($correctAnswers / $allAnswers) >= $successCriteria;

		if ($successful) {
			$this->saveEmployeeCompetency($currentEcId, $questionnaireId);
			$this->updateTrainingRequestDraftStatus($currentEcId, $questionnaireId);
			$this->updateEmployeeCompetencyExamData($currentEcId, $questionnaireId, 'successful');
		} else {
			$this->updateEmployeeCompetencyExamData($currentEcId, $questionnaireId, 'failed');
		}

		$response["status"]     = $this->updateLockedAnswers($lockedAnswers, $successful);
		$response["resultText"] = $successful ? Dict::getValue('successfulExamMessage') : Dict::getValue('failedExamMessage');
        $response["class"]      = $successful ? "success" : "failed";

		echo json_encode($response);
	}

	public function actionSaveExamTimeout()
	{
		$questionnaireId 		= requestParam('questionnaireId');
		$userId 				= userID();
		$response 				= [];
		$saveErrorMessage		= Dict::getValue("unsuccessful_save");

		$answers = QuestionnaireAnswer::model()->findAllByAttributes([
			'questionnaire_id' 	=> $questionnaireId,
			'created_by' 		=> $userId,
			'status' 			=> $this->answeredAndSavedStatus
		]);

		foreach ($answers as $answer)
		{
			$answer->status = $this->timeoutStatus;

			try {
				if($answer->save()) {
					$response["status"] = 1;
				} else {
					$response["status"] = 0;
					$response["message"] = $saveErrorMessage;
				}
			} catch (\Exception $e) {
				$response["status"] = 0;
				$response["message"] = $saveErrorMessage;
			}
		}

		$response["resultText"] = Dict::getValue('timeoutExamMessage');

		echo json_encode($response);
	}

	private function getOpenEmployeeCompetencyExam() 
	{
		$currentEcId = (new User)->getUserEmployeeContractID();
	
		return EmployeeCompetencyExam::model()->findAllByAttributes([
			'employee_contract_id'	=> $currentEcId,
			'is_exam_active'		=> 1,
			'status'				=> $this->publishedStatus
		]);
	}

	private function updateLockedAnswers($lockedAnswers, $successful)
	{
		foreach ($lockedAnswers as $lockedAnswer)
		{
			$lockedAnswer->status = $successful ? $this->answeredAndSuccessfulStatus : $this->answeredAndFailedStatus;

			try {
				if(!$lockedAnswer->save()) {
					return 0;
				} 
			} catch (\Exception $e) {
				return 0;
			}
		}

		return 1;
	}

	private function saveEmployeeCompetency($employeeContractId, $competencyId)
	{
		$criteria = new CDbCriteria([
			'condition' => 'employee_contract_id=:contractId AND competency_id=:competencyId AND status=:status AND CURDATE() BETWEEN valid_from AND IFNULL(valid_to, :defaultEnd)',
			'params' => [
				':contractId'	=> $employeeContractId,
				':competencyId' => $competencyId,
				':status'		=> $this->draftStatus,
				':defaultEnd'	=> App::getSetting("defaultEnd")
			]
		]);
		
		$employeeCompetency = EmployeeCompetency::model()->find($criteria);

		if (!$employeeCompetency) {
			$employeeCompetency							= new EmployeeCompetency();
			$employeeCompetency->employee_contract_id 	= $employeeContractId;
			$employeeCompetency->competency_id			= $competencyId;
			$employeeCompetency->level_id				= 2;
		}

		$employeeCompetency->status = $this->publishedStatus;

		try {
			$employeeCompetency->save();
			return true;
		} catch (\Exception $e) {
			Yii::log($e, 'log', 'ECompSaveError');
			return false;
		}
	}

	private function updateTrainingRequestDraftStatus($employeeContractId, $competencyId)
	{
		$trainingRequest = TrainingRequest::model()->findByAttributes([
			'employee_contract_id' 	=> $employeeContractId,
			'training_id'			=> $competencyId,
			'status'				=> $this->draftStatus
		]);

		if ($trainingRequest !== null) {
			$trainingRequest->status = $this->deletedStatus;
			$trainingRequest->save();

			return true;
		} else {
			return false;
		}
	}

	private function updateEmployeeCompetencyExamData($employeeContractId, $competencyId, $result)
	{
		$employeeCompetencyExam = EmployeeCompetencyExam::model()->findByAttributes([
			'employee_contract_id'	=> $employeeContractId,
			'competency_id'			=> $competencyId,
			'is_exam_active'		=> 1,
			'status'				=> $this->publishedStatus
		]);

		if ($employeeCompetencyExam !== null) 
		{
			$employeeCompetencyExam->is_exam_active = 0;

			if ($result === 'successful') {
				$employeeCompetencyExam->has_successful_exam = 1;
			} else {
				$employeeCompetencyExam->number_of_exam_attempts++; 
			}

			try {
				$employeeCompetencyExam->save();
				return true;
			} catch (\Exception $e) {
				Yii::log($e, 'log', 'ECESaveError');
				return false;
			}

		} else {
			return false;
		}
	}

}