<?php

class UserController extends Grid2Controller
{
	private $defaultEnd;
	private $statusPublished = Status::PUBLISHED;
	private $useCompanyAndPayrollRights;
	private $hasRocketChat = FALSE;

	public function actionIndex($layout = '//Grid2/layouts/indexLayout', $view = '/Grid2/index', $params = []) {
		$path = Yang::addAsset(Yang::getAlia<PERSON>('application.assets.base.user'), false, -1, true);
		Yang::registerScriptFile($path . '/js/user.js');

		parent::actionIndex($layout, $view);
	}

	public function __construct()
	{
		parent::__construct("user");
		$this->defaultEnd = App::getSetting("defaultEnd");
		$this->useCompanyAndPayrollRights = (int)App::getSetting("useCompanyAndPayrollRights");
		$this->hasRocketChat = !is_null(Yang::getParam('rocketChatURL'));
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("User");

		parent::setControllerPageTitleId("page_title_user_management");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		true);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);
		$this->LAGridRights->overrideInitRights("export_xls",		false);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);
		$this->LAGridRights->overrideInitRights("export_pdf_node",	false);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);

		$this->LAGridDB->enableSQLMode();

		$maxAttempts = (int)App::getSetting('max_number_of_attempts') ?: 3;
		$showUserListPlusSearchInput = App::getSetting("showUserListPlusSearchInput");

		$this->LAGridDB->setSQLSelection($this->getGridSQL($maxAttempts, $showUserListPlusSearchInput), "row_id");
		$this->LAGridDB->setSQLSelectionForReport($this->getGridReportSQL());

		parent::setReportOrientation("landscape");
		parent::setReportHeader('"' . Dict::getValue("page_title_user_management") . '"', '"{dateTime}"', false);
		parent::setReportFooter('"' . "TipTime Web Access - Reporting" . '"', '$V{PAGE_NUMBER}', false);

		parent::setExportFileName(Dict::getValue("export_file_user_management"));

		parent::G2BInit();
	}

	/**
	 * Kereső értékek átadása sessionnek az oszlopok felépítéséhez
	 * @return void
	 */
	public function actionSetInitProperties() {
		Yang::setSessionValue('user_filters', requestParam('searchInput'));
		parent::actionSetInitProperties();
	}

	protected function fetchGridData($filter, $isExport = false, $excelExport = false, $csvString = false)
	{
		$retArr = parent::fetchGridData($filter, $isExport);
		$empData = $this->getEmpValidDates();
        foreach ($retArr['data'] as $key => &$row) {
			$employeeId = $row['columns']['grid_employee_id']['data'];
			if (isset($empData[$employeeId]) && $empData[$employeeId]['e_valid_to'] < $filter['date'])
			{
				if (Yang::customerDbPatchName() == "velux" && !App::hasRight(null, "su"))
				{
					unset($retArr['data'][$key]);
				} else {
					foreach ($row['columns'] as &$column)
					{
						$column['cssClass'] 		= 'bgColorRed';
						$column['excel_bgColor'] 	= '#af2f2f';
						$column['excel_color'] 		= '#fff';
					}
				}
			}
			else if (isset($empData[$employeeId]) && $filter['date'] < $empData[$employeeId]['e_valid_from'])
			{
				foreach ($row['columns'] as &$column)
				{
					$column['cssClass'] 		= 'bgColorBlue';
					$column['excel_bgColor'] 	= '#3b74e6';
					$column['excel_color'] 		= '#fff';
				}
			}
        }

		return $retArr;
	}

	/**
	 * Mentés
	 * @param array $data
	 * @param string $modelName
	 * @param string $pk
	 * @param boolean $vOnly
	 * @param boolean $ret
	 * @param string $contentId
	 * @return void
	 */
	public function actionSave($data = [], $modelName = null, $pk = null, $vOnly = false, $ret = false, $contentId = null)
	{
		$this->layout = "//layouts/ajax";
		$this->G2BInit();

		$generateFrom	= requestParam('generateFrom');
		$data			= requestParam('dialogInput_' . $generateFrom);
		$rowId			= CHtml::value($data, 'row_id');
		$pub			= Status::PUBLISHED;
		$end			= App::getSetting("defaultEnd");
		if ($rowId) {
			$userSQL = "SELECT `rolegroup_id`, `user_id`, `valid_from`, IFNULL(`valid_to`, '{$end}') AS valid_to, `google_two_fa` FROM `user` WHERE `row_id` = {$rowId}";
			$beforeSave = dbFetchRow($userSQL);
		}

		if (isset($beforeSave["google_two_fa"]) && isset($data["google_two_fa"]) && (int)$beforeSave["google_two_fa"] === 1 && (int)$data["google_two_fa"] === 0) {
			$data["google_two_fa_secret_key"]	= "";
			$data["google_two_fa_validated"]	= 0;
		}

		parent::actionSave($data, $modelName, $pk, $vOnly, $ret, $contentId);

		if ($rowId && isset($data["rolegroup_id"]) && $data["rolegroup_id"] != $beforeSave["rolegroup_id"])
		{
			$updateSubstitutionSQL = "
				UPDATE `user_substitution` us
				LEFT JOIN `user` u1 ON
						u1.`user_id` = us.`user_id`
					AND u1.`status` = {$pub}
					AND u1.`valid_from` <= IFNULL(us.`valid_to`, '{$end}')
					AND us.`valid_from` <= IFNULL(u1.`valid_to`, '{$end}')
				LEFT JOIN `user` u2 ON
						u2.`user_id` = us.`user_substitution_id`
					AND u2.`status` = {$pub}
					AND u2.`valid_from` <= IFNULL(us.`valid_to`, '{$end}')
					AND us.`valid_from` <= IFNULL(u2.`valid_to`, '{$end}')
				LEFT JOIN `user_substitution_rolegroup` usr1 ON
						usr1.`rolegroup_id` = IF(u1.`user_id` = '{$beforeSave["user_id"]}', '{$data["rolegroup_id"]}', '0')
					AND usr1.`rolegroup_substitution_id` = u2.`rolegroup_id`
					AND usr1.`status` = {$pub}
					AND usr1.`valid_from` <= IFNULL(us.`valid_to`, '{$end}')
					AND us.`valid_from` <= IFNULL(usr1.`valid_to`, '{$end}')
				LEFT JOIN `user_substitution_rolegroup` usr2 ON
						usr2.`rolegroup_substitution_id` = IF(u2.`user_id` = '{$beforeSave["user_id"]}', '{$data["rolegroup_id"]}', '0')
					AND usr2.`rolegroup_id` = u1.`rolegroup_id`
					AND usr2.`status` = {$pub}
					AND usr2.`valid_from` <= IFNULL(us.`valid_to`, '{$end}')
					AND us.`valid_from` <= IFNULL(usr2.`valid_to`, '{$end}')
				SET
					us.`status` = " . Status::DELETED . ",
					us.`modified_by` = '" . userID() . "',
					us.`modified_on` = '" . date("Y-m-d H:i:s") . "'
				WHERE
						us.`status` = {$pub}
					AND us.`valid_from` <= '{$beforeSave["valid_to"]}'
					AND '{$beforeSave["valid_from"]}' <= IFNULL(us.`valid_to`, '{$end}')
					AND (us.`user_substitution_id` = '{$beforeSave["user_id"]}' OR us.`user_id` = '{$beforeSave["user_id"]}')
					AND (
						(u1.`row_id` IS NOT NULL AND u1.`user_id` = '{$beforeSave["user_id"]}' AND usr1.`row_id` IS NULL)
						OR
						(u2.`row_id` IS NOT NULL AND u2.`user_id` = '{$beforeSave["user_id"]}' AND usr2.`row_id` IS NULL)
					)
			";
			dbExecute($updateSubstitutionSQL);
		}
	}

	private function getEmpValidDates()
	{
		$SQL = "
			SELECT
				`employee_id`,
			  	MIN(`valid_from`) AS e_valid_from,
				MAX(`valid_to`) AS e_valid_to
			FROM `employee`
			WHERE `status` = " . Status::PUBLISHED . "
			GROUP BY `employee_id`
		";
		return dbFetchAll($SQL, 'employee_id');
	}

	public function search()
	{
		$statusOptions = App::getLookup('status', false, '2', [$this->statusPublished, Status::STATUS_REMOVED, Status::STATUS_LOCKED]);
		$showUserListPlusSearchInput = App::getSetting("showUserListPlusSearchInput") == 1 ? true : false;
		if ($this->useCompanyAndPayrollRights) {
			$art = new ApproverRelatedGroup;
			$gargARSQL = $art->getApproverReleatedGroupSQL("AuthRolegroup", ["companyMainData"], userID(), "'{date}'", "AND", "allDate");
		} else {
			$gargARSQL = ["where" => ""];
		}

		$search['date'] =
		[
			'col_type'		=> 'ed',
			'dPicker'		=> true,
			'width'			=> '*',
			'label_text'	=> Dict::getValue("date"),
			'default_value'	=> date('Y-m-d'),
			'onchange'		=> ["employee", "rolegroup_id"]
		];
		if ($this->useCompanyAndPayrollRights)
		{
			$art	= new ApproverRelatedGroup;
			$gargSQL= $art->getApproverReleatedGroupSQL("Company", ["employeeManagement"], userID(), "'{date}'", "AND", "allDate");
			$search['date']['onchange'][] = 'company';
			$search['company'] =
			[
				'col_type'		=> 'combo',
				'width'			=> '*',
				'label_text'	=> Dict::getModuleValue("ttwa-base", "company"),
				'options'		=>
				[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							`company`.`company_id` AS id,
							`company`.`company_name` AS value
						FROM `company`
						WHERE
								`company`.`status` = {$this->statusPublished}
							AND '{date}' BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$this->defaultEnd}')
					",
				],
				'onchange'		=> ['employee', 'rolegroup_id']
			];
			$search['company']['options']['sql'] .= " {$gargSQL["where"]} ORDER BY value";
		}
		$search['employee'] =
		[
			'col_type'		=> 'auto',
			'options'		=>	[
				'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
				'sql'	=> $this->getEmployeeSearchSQL(),
				'array'	=> [["id" => "ALL", "value" => Dict::getValue("all")]],
			],
			'label_text'	=> Dict::getValue("name"),
			'default_value'	=> ''
		];
		if ($this->useCompanyAndPayrollRights) {
			$search['employee']['options']['sql'] .= " AND (`employee`.`company_id` = '{company}' OR '{company}' = 'ALL') ORDER BY value";
		} else {
			$search['employee']['options']['sql'] .= " ORDER BY value";
		}
		$search['status'] =
		[
			'col_type'		=> 'combo',
			'width'			=> '*',
			'label_text'	=> Dict::getValue('status'),
			'options'		=>	['mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY, 'array' => $statusOptions],
			'default_value'	=> '',
		];

		$search['rolegroup_id']	=
		[
			'col_type'		=> 'combo',
			'options'		=>	[
				'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
				'sql'	=> "
					SELECT
						`rolegroup_id` AS id,
						`rolegroup_name` AS value
					FROM `auth_rolegroup`
					WHERE `visibility` = 1 {$gargARSQL["where"]}
				",
				'array'	=> [["id" => "ALL", "value" => Dict::getValue("all")]]
			],
			'label_text'	=> Dict::getValue("rolegroup"),
			'default_value'	=> ''
		];
		if ($this->useCompanyAndPayrollRights) {
			$search['rolegroup_id']['options']['sql'] .= " AND (`auth_rolegroup`.`company_id` = '{company}' OR '{company}' = 'ALL' OR `auth_rolegroup`.`company_id` = 'ALL') ORDER BY value";
		} else {
			$search['rolegroup_id']['options']['sql'] .= " ORDER BY value";
		}

		$search['submit'] = ['col_type' => 'searchBarReinitGrid', 'width' => '*', 'label_text' => ''];

		if(!$showUserListPlusSearchInput) {
			unset($search['status']);
			unset($search['rolegroup_id']);
		}

		return $search;
	}

	public function columns()
	{
		$filters			= Yang::session('user_filters', []);
		$filters["date"]	= isset($filters["date"]) ? $filters["date"] : date("Y-m-d");
		$langOptions		= App::getLookup('lang');
		$defaultEnd			= App::getSetting("defaultEnd");
		$ldapAuth			= App::getSetting("ldapauth");
		$empMandatory		= (int)App::getSetting("userControllerEmpMandatory");
		$allowGoogle2FAAuth	= (int)App::getSetting("allowGoogle2FAAuth");
		// ne írd át "$this->"-re, mert elrontja a dolgozó betöltést
		$statusOptions		= App::getLookup('status', false, '2', [Status::DRAFT, Status::PUBLISHED, Status::STATUS_REMOVED, Status::STATUS_LOCKED]);

		if ((int)App::getSetting("useCompanyAndPayrollRights")) {
			$art = new ApproverRelatedGroup;
			$gargARSQL = $art->getApproverReleatedGroupSQL("AuthRolegroup", ["companyMainData"], userID(), "CURDATE()", "AND", "CurrentDate");
		} else {
			$gargARSQL = ["where" => ""];
		}

		$rolegroup			= new AuthRolegroup;
		$rolegroupCriteria	= new CDbCriteria();
		$rolegroupCriteria->condition = "`visibility` = 1 " . str_replace("auth_rolegroup.", "t.", $gargARSQL["where"]) . "";
		$rolegroupGrid 		= new AuthRolegroup;
		$receive_email 		= App::getLookup('yes_no');

		$receive_email = [
			['id'=> 1, 'value' => Dict::getValue("yes"), 'default' => \TRUE],
			['id'=> 0, 'value' => Dict::getValue("no")]
		];
		$disableLdapAuth = [
			['id'=> 1, 'value' => Dict::getValue("yes")],
			['id'=> 0, 'value' => Dict::getValue("no")]
		];
		$userNameEdit = (int)App::getSetting('userNameEdit') == 1;
		$columns['username'] = [
			'export'		=> true,
            'grid'			=> true,
			'report_width'	=> 20,
			'col_type'		=> 'ed',
			'edit'			=> $userNameEdit,
			'width'			=> 150
		];
		$columns['employee_id']	= [
			'export'		=> false,
			'grid'			=> false,
			'window'		=> true,
			'report_width'	=> 30,
			'col_type'		=> 'combo',
			'options'		=>	[
				'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
				'sql'	=> UserController::getEmployeeDropdownSQL($filters["date"]),
				'array'	=> [["id" => "", "value" => ""]],
			],
			'width'			=> 200,
		];
		$columns['grid_employee_id'] = [
			'export'		=> true,
			'grid'			=> true,
			'window'		=> false,
			'report_width'	=> 30,
			'col_type'		=> 'combo',
			'options'		=>	[
				'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
				'sql'	=> UserController::getEmployeeDropdownSQL($filters["date"], false),
				'array'	=> [["id" => "", "value" => ""]],
			],
			'width'			=> 200,
		];
		$columns['email'] = [
			'export'		=> true,
            'grid'			=> true,
			'report_width'	=> 10,
			'col_type'		=> 'ed',
			'width'			=> 150
		];
		if ($allowGoogle2FAAuth)
		{
			$columns['google_two_fa'] = [
				'export'		=> false,
                'grid'			=> true,
				'report_width'	=> 10,
				'col_type'		=> 'combo',
				'width' 		=> 100,
				'window' 		=> true,
				'options' 		=> ['mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY, 'array' => $disableLdapAuth]
			];
		}
		$columns['receive_email'] = [
			'export'=> true,
            'grid'			=> true,
			'report_width'	=> 10,
			'col_type'		=> 'combo',
			'width'			=> 100,
			'options'		=> ['mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY, 'array' => $receive_email]
		];
		$columns['rolegroup_id']	= [
			'export'		=> true,
            'grid'			=> true,
			'col_type'		=> 'combo',
			'options'		=>	[
				'mode'					=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
				'modelSelectionModel'	=> $rolegroup,
				'modelSelectionCriteria'=> $rolegroupCriteria,
				'comboId'				=>'rolegroup_id',
				'comboValue'			=> 'rolegroup_name'
			],
			'gridOptions'	=>	[
				'mode'					=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
				'modelSelectionModel'	=> $rolegroupGrid,
				'comboId'				=> 'rolegroup_id',
				'comboValue'			=> 'rolegroup_name'
			],
			'width'			=> 150,
		];
		$columns['password_date'] = [
			'export'		=> true,
			'report_width'	=> 10,
			'col_type'		=>'ed',
			'window'		=> false,
			'width'			=> 200,
			'default_value' => date('Y-m-d H:i:s'),
		];
		$columns['last_logged_in'] = [
			'export'		=> true,
			'report_width'	=> 10,
			'col_type'		=> 'ed',
			'window' 		=> false,
			'width' 		=> 200,
			'default_value' => App::getSetting('defaultStart'),
		];
		$columns['lang'] = [
			'export'	=> false,
			'col_type'	=> 'combo',
			'options'	=> ['mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY, 'array' => $langOptions],
			'col_align'	=> 'center',
			'width' 	=> 70,
		];
		$columns['note'] = [
			'export'	=> false,
			'col_type'	=> 'ed',
			'width' 	=> 150
		];
		$columns['valid_from'] = [
			'export'		=> true,
			'grid'			=> true,
			'window'		=> true,
			'width' 		=> 150,
			'report_width' 	=> 10,
			'col_type'		=> 'ed',
			'dPicker'		=> true,
			'col_align'		=> 'center',
			'dialog_width'	=> '1',
			'line_break'	=> true,
			'onchange'		=> ['employee_id']
		];
		$columns['valid_to'] = [
			'export'		=> true,
			'grid'			=> true,
			'window'		=> true,
			'width' 		=> 150,
			'report_width' 	=> 10,
			'col_type'		=> 'ed',
			'dPicker'		=> true,
			'col_align'		=> 'center',
			'dialog_width'	=> '1',
			'onchange'		=> ['employee_id']
		];
		$columns['status'] = [
			'export'	=> false,
			'col_type'	=> 'combo',
			'options'	=> ['mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY, 'array' => $statusOptions],
			'col_align'	=> 'center',
			'width' 	=> 100,
		];
		if ($this->hasRocketChat) {

			$rocketRoles = App::getLookup('rocket_role');

			$columns['rocket_role'] = [
				'export'	=> false,
				'col_type'	=> 'combo',
				'options'	=> ['mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY, 'array' => $rocketRoles],
			];
		}
		if ($ldapAuth) {
			$columns['disable_ldap_auth'] = [
				'export'		=> false,
				'report_width'	=> 10,
				'col_type'		=> 'combo',
				'width' 		=> 100,
				'window' 		=> true,
				'options' 		=> ['mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY, 'array' => $disableLdapAuth]
			];
		}
		$columns['blocked_user'] = [
			'export'	=> false,
			'col_type'	=> 'ed',
			'width' 	=> 100,
			'window' 	=> false,
			'align' 	=> 'center'
		];
		$columns['blocked_pin']	= [
			'export'	=> false,
			'col_type'	=> 'ed',
			'width' 	=> 100,
			'window' 	=> false,
			'align' 	=> 'center'
		];
		$columns['e_valid_from'] = [
			'export'	=> false,
			'window' 	=> false,
			'col_type'	=> 'ed',
			'width' 	=> 0
		];
		$columns['e_valid_to'] = [
			'export'	=> false,
			'window' 	=> false,
			'col_type'	=> 'ed',
			'width' 	=> 0
		];

		$ldapLogin = Yang::getParam('ldapLogin');
		$mantisLogin = Yang::getParam('mantisLogin');
		$ldapLogin = is_null($ldapLogin) ? false : $ldapLogin;
		$mantisLogin = is_null($mantisLogin) ? false : $mantisLogin;
		if (!$ldapLogin || !$mantisLogin)
		{
			$columns['password'] = [
				'grid' 			=> false,
				'export'		=> false,
				'col_type'		=> 'pw',
				'line_break'	=> true
			];
		}

		if ($empMandatory && isset($columns['employee_id'])) {
			$columns['employee_id']['mandatory'] = true;
		}

		return $columns;
	}

	public function attributeLabels()
	{
		return [
			'blocked_user'		=> Dict::getValue("blocked_user"),
			'blocked_pin'		=> Dict::getValue("blocked_pin"),
			'grid_employee_id'	=> Dict::getValue("employee"),
			'disable_ldap_auth'	=> Dict::getValue("disableLdapAuth")
		];
	}

	protected function getStatusButtons($gridID = null)
	{
		if (!isset($gridID)) {
			$gridID = 'dhtmlxGrid';
		} else if (empty($gridID)) {
			$gridID = 'dhtmlxGrid';
		}

		$buttons = [];

		if((!is_null(Yang::getParam('rocketChatURL'))) && App::hasRight($this->getControllerID(), "chatSyncron"))
		{
			$buttons["rocketSyncron"] = [
				"type"		=> "button",
				"id"		=> "rocketSyncron",
				"class"		=> "rocketSyncron",
				"name"		=> "rocketSyncron",
				"img"		=> "/images/status_icons/st_loop.png",
				"label"		=> Dict::getValue("chat_syncron"),
				"onclick"	=> "callRocketChatSyncron();",
			];
			$buttons[] = [
				"type" => "selector",
			];
		}

		if (App::hasRight($this->getControllerID(), "ask_password_change"))
		{
			$buttons["askPasswordChange"] = [
				"type"		=> "button",
				"id"		=> "askPasswordChange",
				"class"		=> "askPasswordChange",
				"name"		=> "askPasswordChange",
				"img"		=> "/images/status_icons/st_pw_exp.png",
				"label"		=> Dict::getValue("ask_password_change"),
				"onclick"	=> "askPasswordChange('".Dict::getValue("do_you_want_select_all")."','".Dict::getValue("please_select_line")."', '".Dict::getValue("ask_password_change_confirm")."', '".Dict::getValue("ask_password_change_success")."');",
			];
			$buttons[] = [
				"type" => "selector",
			];
		}

		if (App::hasRight($this->getControllerID(), "forgotten_password"))
		{
			$buttons["forgottenPassword"] = [
				"type"		=> "button",
				"id"		=> "forgottenPassword",
				"class"		=> "forgottenPassword",
				"name"		=> "forgottenPassword",
				"img"		=> "/images/status_icons/st_pw_exp.png",
				"label"		=> Dict::getValue("fpw_forgotten_password")/*."(Ctrl+Shift+a)"*/,
				// G2BDialogMultiGrid(grid_id, generate_from, selected_ids, dialogMode, dialogContentUrl, actionSaveUrl, gridDataUrl, params, windowtitle, errortext)
				"onclick"	=> "forgottenPassword('".Dict::getValue("please_select_line")."', '".Dict::getValue("fpw_confirm")."', '".Dict::getValue("fpw_success")."');",
			];
			$buttons[] = [
				"type" => "selector",
			];
		}

		if (App::hasRight($this->getControllerID(), "unlock_blocked_user"))
		{
			$buttons["unlockBlockedUser"] = [
				"type"		=> "button",
				"id"		=> "unlockBlockedUser",
				"class"		=> "unlockBlockedUser",
				"name"		=> "unlockBlockedUser",
				"img"		=> "/images/status_icons/st_unlock.png",
				"label"		=> Dict::getValue("unlock_blocked_user")/*."(Ctrl+Shift+a)"*/,
				"onclick"	=> "unlockBlockedUser('".Dict::getValue("please_select_line")."');",
			];

			$buttons[] = [
				"type" => "selector",
			];
		}

		if (App::hasRight($this->getControllerID(), "unlock_blocked_pin"))
		{
			$buttons["unlockBlockedPin"] = [
				"type"		=> "button",
				"id"		=> "unlockBlockedPin",
				"class"		=> "unlockBlockedPin",
				"name"		=> "unlockBlockedPin",
				"img"		=> "/images/status_icons/st_unlock.png",
				"label"		=> Dict::getValue("unblock_pin")/*."(Ctrl+Shift+a)"*/,
				"onclick"	=> "unlockBlockedPin('".Dict::getValue("please_select_line")."');",
			];
		}

		$originalButtons = parent::getStatusButtons($gridID);

		return Yang::arrayMerge($buttons, $originalButtons);
	}

	/**
	 * A kijelölt user-eknek módosítaniuk kell a jelszavukat a következő belépéskor
	 */
	public function actionAskPasswordChange()
	{
		$results = [
			"status" => 0,
		];

		$selected_ids	= requestParam('selected_ids');

		if (empty($selected_ids)) {
			echo json_encode($results); return false;
		}

		$updateSql="
				UPDATE
					`user`
				SET
					`password_date` = '" . App::getSetting('defaultStart') . "',
					`modified_by`   = '" . userID() . "',
					`modified_on`	= NOW()
				WHERE
					`row_id` in (" . str_replace(";",",",$selected_ids) . ")
		";
		dbExecute($updateSql);

		$results["status"] = 1;
		echo json_encode($results);
	}

	/**
	 * A kijelölt user-ekre meghívja a forgottenPassword($user_row_id) funkciót
	 */
	public function actionForgottenPassword()
	{
		$results = [
			"status" => 0,
		];

		$selected_ids	= requestParam('selected_ids');
		$username		= requestParam('username');

		if (empty($selected_ids) && empty($username)) {
			echo json_encode($results); return false;
		}

		$idsArr = !is_null($selected_ids) ? explode(";", $selected_ids) : null;
		if (!(is_array($idsArr) && count($idsArr)) && empty($username) ) {
			echo json_encode($results); return false;
		}

		if (empty($username) && is_array($idsArr) && count($idsArr)) {
			foreach ($idsArr as $user_row_id) {
				$this->forgottenPassword($user_row_id);
			}
		} else if (!empty($username)) {
			$this->forgottenPassword(null, $username);
		}

		$results["status"] = 1;

		echo json_encode($results);
	}

	/**
	 * Új elfelejtett jelszó folyamatot indít, új azonosítóval, amelyet linkelve kiküld emailben.
	 * A korábbi esetlegesen be nem fejezett folyamatokat érvénytelenné teszi.
	 *
	 * @param $user_row_id: a felhasználó row_id-ja
	 */
	private function forgottenPassword($user_row_id, $username = null)
	{
		$user = null;

		if (!empty($username)) {
			$user = User::model()->findByAttributes(['username' => $username], "`status` = " . $this->statusPublished . " AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->defaultEnd}')");
		} else {
			$user = User::model()->findByPk($user_row_id);
		}

		if (!$user) return false;

		$act_password_hash = $user->password;
		$user->password = hash('sha512', $user->username . $user->created_on . $act_password_hash . date("YmdHis") . rand(1,100000));
		$user->save();

		$fpw_attrs = [
			"fpw_id" => hash('sha512', $user->user_id . $user->created_on . $act_password_hash . date("YmdHis") . rand(1,100000)),
			"user_id" => $user->user_id,
			"prev_password" => $act_password_hash
		];

		ForgottenPassword::model()->updateAll(['fpw_used' => 1,], "`user_id` = '{$user->user_id}' AND `fpw_used` = 0");

		$fpw = new ForgottenPassword;
		$fpw->attributes = $fpw_attrs;
		if (!empty($username)) {
			$fpw->created_by = $user->user_id;
		}
		$fpw->save();

		// The value will be the view file name. The default value is messageemail
		$view = App::getSetting("fpw_email_view");

		$addressLangSql = "
			SELECT
				`lang`
			FROM `user`
			WHERE
					`email` = '{$user->email}'
				AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->defaultEnd}')
				AND `status` = " . $this->statusPublished;

		$lang = dbFetchValue($addressLangSql);

		if (empty($lang))
			$lang = Dict::getLang();

		$emailAttrs = [
			'emailTitle'	=> Dict::getValueWithLang("fpw_forgotten_password", $lang),
			'emailLayout'	=> [
				'folder'		=> 'application.views.email',
				'view'			=> $view,
			], //fpw_forgotten_password_content
			'emailParams'	=> [
				'recipient_fullname'		=> Employee::getEmployeeFullnameByUserID($user->user_id),
				'recipient_fullname_eng'	=> Employee::getEmployeeFullnameByUserID($user->user_id),
				'sender_fullname'			=> Employee::getEmployeeFullnameByUserID($fpw->created_by),
				'sender_fullname_eng'		=> Employee::getEmployeeFullnameByUserID($fpw->created_by),
				'theme'						=> Dict::getValueWithLang("fpw_forgotten_password", 'hu'),
				'theme_eng'					=> Dict::getValueWithLang("fpw_forgotten_password", 'en'), //Dict::getValueWithLang($theme_dict_id, 'en', $generalContentParams)
				'generalContent'			=> Dict::getValueWithLang("fpw_forgotten_password_content", 'hu', ["link" => Yang::getParam('serverUrl') . 'forgottenPassword/index?id=' . $fpw_attrs["fpw_id"] . "&lang={$lang}",]),
				'generalContent_eng'		=> Dict::getValueWithLang("fpw_forgotten_password_content", 'en', ["link" => Yang::getParam('serverUrl') . 'forgottenPassword/index?id=' . $fpw_attrs["fpw_id"] . "&lang={$lang}",]),
				'message'					=> "",
				'sw_link'					=> Yang::getParam('serverUrl'),
				'message_created_on'		=> date("Y-m-d H:i:s"),
			],
		];

		$es = new EmailSender();
		$es->sendMail(
			/* $addresses */
			[
				'addr' => [
					[
						'email'	=> $user->email,
						'name'	=> $user->username,
					],
				],
			],
			/* $subject */
			$emailAttrs['emailTitle'],
			/* $message */
			null,
			/* $view */
			[$emailAttrs['emailLayout']['folder'], $emailAttrs['emailLayout']['view']],
			/* $vars */
			$emailAttrs['emailParams'],
			/* $images */
			['logo' => Yang::getBasePath().'/../webroot/images/module_logos/logo_epp_software_name.png'],
			/* $iCal */
			false,
			/* $iCalString */
			"",
			/* $notSkipAppSettings */
			\FALSE
		);
	}

	/*
	 * A függvény egy AJAX-hivást követően vizsgálja, hogy az adott felhasználó blokkolva van-e, ha igen, akkor visszaállítja
	 * az unlockBlockedUser meghívásával feloldja a felhasználó tiltását (visszaállítja a user táblában a number_of_attempts és
	 * a locked_until oszlopok értékeit null-ra).
	 */

	public function actionUnlockBlockedUser()
	{
		$selected_id	= requestParam('selected_id');
		$selected_user	= User::model()->findByPk($selected_id);

		$response = [];

		if (!empty($selected_user) && !empty($selected_id))
		{
			if ($selected_user->number_of_attempts >= 3 && $selected_user->locked_until > date("Y-m-d H:i:s")) {
				$this->unlockBlockedUser($selected_id);

				$response = [
					"status"	=> 1,
					"message"	=> Dict::getValue("success_unlocking_was_successful")
				];
			} else {
				$response = [
					"status"	=> 2,
					"message"	=> Dict::getValue("error_user_isnt_blocked")
				];
			}
		}

		echo json_encode($response);
	}

	private function unlockBlockedUser($user_row_id)
	{
		$user = User::model()->findByPk($user_row_id);

		$user->number_of_attempts	= 0;
		$user->locked_until			= null;
		$user->modified_by			= userID();
		$user->modified_on			= date("Y-m-d H:i:s");

		$user->save();
	}

	public function actionUnlockBlockedPin()
	{
		$selectedId		= requestParam('selected_id');
		$selectedUser	= User::model()->findByPk($selectedId);
		$response		= [];

		if (!empty($selectedUser) && !empty($selectedId))
		{
			if ($selectedUser->pin_attempts == 5)
			{
				$date = date('Y-m-d H:i:s');
				$userId = $selectedUser->user_id;
				$sql = "UPDATE `user` SET `pin_attempts` = 0, `pin_date_until` = '{$date}' WHERE `user_id` = '{$userId}' LIMIT 1";
				dbExecute($sql);

				$response = [
					'status'	=> 1,
					'message'	=> Dict::getValue('unblock_pin_succeeded')
				];
			}
			else
			{
				$response = [
					'status'	=> 0,
					'message'	=> Dict::getValue('unblock_pin_failed')
				];
			}
		}

		echo json_encode($response);
	}

	/**
	 * Grid adattartalmát visszaadó SQL
	 * @param integer $maxAttempts
	 * @param boolean $showUserListPlusSearchInput
	 * @return string
	 */
	private function getGridSQL($maxAttempts, $showUserListPlusSearchInput)
	{
		if ($this->useCompanyAndPayrollRights)
		{
			$art		= new ApproverRelatedGroup;
			$gargSQL	= $art->getApproverReleatedGroupSQL("Employee", ["employeeManagement"], userID(), "'{date}'", "AND", "allDate");
			$SQL = "
				SELECT
					u.*,
					u.`employee_id` AS grid_employee_id,
					IF(u.`number_of_attempts` >= {$maxAttempts} AND u.`locked_until` > NOW(), '" . Dict::getValue("yes") . "', '" . Dict::getValue("no") . "') AS blocked_user,
					IF(u.`pin_attempts` = 5, '" . Dict::getValue("yes") . "', '" . Dict::getValue("no") . "') AS blocked_pin
				FROM `user` AS u
				LEFT JOIN `employee` ON
						`employee`.`employee_id` = u.`employee_id`
					AND `employee`.`status` = {$this->statusPublished}
					AND '{date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `employee_contract` ON
						`employee_contract`.`employee_id` = `employee`.`employee_id`
					AND `employee_contract`.`status` = {$this->statusPublished}
					AND '{date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->defaultEnd}')
					AND '{date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->defaultEnd}')
				" . EmployeeGroup::getAllActiveLeftJoinSQL("employee_contract", "'{date}'", "", "employee") . "
				" . EmployeeGroup::getAllBaseTablesWithGroup("employee", "employee_contract", "'{date}'") . "
			";
			if (isset($gargSQL["join"])) {
				$SQL .= $gargSQL["join"];
			}
			$SQL .= "
				WHERE
						(u.`employee_id` = '{employee}' OR '{employee}' = 'ALL' OR '{employee}' = '')
					AND '{date}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$this->defaultEnd}')
					AND u.`status` IN (" . Status::DRAFT . ", {$this->statusPublished}, " . Status::LOCKED . ")
					AND (`employee`.`company_id` = '{company}' OR '{company}' = 'ALL' OR `employee`.`row_id` IS NULL)
				";
			if($showUserListPlusSearchInput) {
				$SQL .= "
					AND (u.`status` = '{status}' OR '{status}' = '' OR '{status}' = 'ALL')
					AND (u.`rolegroup_id` = '{rolegroup_id}' OR '{rolegroup_id}' = 'ALL')
				";
			}
			if(!App::isRootSessionEnabled()) {
				$SQL .= "
					AND u.`rolegroup_id` != 'a5b6bd79e008725744118c7c46e10cda'
				";
				if (Yang::customerDbPatchName() == "velux") {
					$SQL .= "
					AND `employee`.`row_id` IS NOT NULL
					AND `employee_contract`.`row_id` IS NOT NULL
					";
				}
			}
			if (isset($gargSQL["where"])) {
				$SQL .= str_replace_first("AND", "AND (('{employee}' = '' AND `employee`.`row_id` IS NULL) OR (", $gargSQL["where"]) . "))";
			}

			$SQL .= "
				ORDER BY u.`username`
			";
		} else {
			$SQL = "
				SELECT
					`user`.*,
					`user`.`employee_id` AS grid_employee_id,
					IF(`user`.`number_of_attempts` >= {$maxAttempts} AND `user`.`locked_until` > NOW(), '" . Dict::getValue("yes") . "', '" . Dict::getValue("no") . "') AS blocked_user,
					IF(`user`.`pin_attempts` = 5, '" . Dict::getValue("yes") . "', '" . Dict::getValue("no") . "') AS blocked_pin
				FROM `user`
				LEFT JOIN `employee` ON
						`employee`.`employee_id` = `user`.`employee_id`
					AND `employee`.`status` = {$this->statusPublished}
					AND '{date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->defaultEnd}')
				WHERE
						(`user`.`employee_id` = '{employee}' OR '{employee}' = 'ALL' OR '{employee}' = '')
					AND '{date}' BETWEEN `user`.`valid_from` AND IFNULL(`user`.`valid_to`, '{$this->defaultEnd}')
					AND `user`.`status` IN (" . Status::DRAFT . ", {$this->statusPublished}, " . Status::LOCKED . ")
				";
			if($showUserListPlusSearchInput) {
				$SQL .= "
					AND (`user`.`status` = '{status}' OR '{status}' = '' OR '{status}' = 'ALL')
					AND (`user`.`rolegroup_id` = '{rolegroup_id}' OR '{rolegroup_id}' = 'ALL')
				";
			}
			if(!App::isRootSessionEnabled()) {
				$SQL .= "
					AND `user`.`rolegroup_id` != 'a5b6bd79e008725744118c7c46e10cda'
				";
			}

			$SQL .= "
				ORDER BY `user`.`username`
			";
		}

		return $SQL;
	}

	/**
	 * Grid reportot előállító SQL-t adja vissza
	 * @return string
	 */
	private function getGridReportSQL()
	{
		if ($this->useCompanyAndPayrollRights)
		{
			$art		= new ApproverRelatedGroup;
			$gargSQL	= $art->getApproverReleatedGroupSQL("Employee", ["employeeManagement"], userID(), "'{date}'", "AND", "allDate");
			$SQL = "
				SELECT
					u.`username`,
					CONCAT(" . Employee::getParam('fullname_with_emp_id') . ", ' - ', MIN(`employee`.`valid_from`), ' - ', MAX(`employee`.`valid_to`)) AS `employee_id`,
					u.`employee_id` AS `grid_employee_id`,
					IFNULL(u.`email`, '') AS `email`,
					ar.`rolegroup_name` AS `rolegroup_id`,
					u.`lang`,
					u.`note`,
					u.`valid_from`,
					IFNULL(u.`valid_to`, '{$this->defaultEnd}') AS `valid_to`,
					u.`status`,
					u.`password`,
					u.`password_date`,
					IFNULL(u.`last_logged_in`, '1970-01-01 00:00:00') AS last_logged_in
				FROM `user` u
				LEFT JOIN `employee` ON
						`employee`.`employee_id` = u.`employee_id`
					AND `employee`.`status` = {$this->statusPublished}
					AND '{date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `employee_contract` ON
						`employee_contract`.`employee_id` = `employee`.`employee_id`
					AND `employee_contract`.`status` = {$this->statusPublished}
					AND '{date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->defaultEnd}')
					AND '{date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->defaultEnd}')
				" . EmployeeGroup::getAllActiveLeftJoinSQL("employee_contract", "'{date}'", "", "employee") . "
				" . EmployeeGroup::getAllBaseTablesWithGroup("employee", "employee_contract", "'{date}'") . "
			";
			if (isset($gargSQL["join"])) {
				$SQL .= $gargSQL["join"];
			}
			$SQL.= "
				LEFT JOIN `auth_rolegroup` ar ON
						ar.`rolegroup_id` = u.`rolegroup_id`
					AND ar.`visibility` = 1
				WHERE
						(u.`employee_id` = '{employee}' OR '{employee}' = 'ALL' OR '{employee}' = '')
					AND '{date}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '" . $this->defaultEnd . "')
					AND u.`status` IN (" . Status::DRAFT . ", " . $this->statusPublished . ", " . Status::LOCKED . ")
					AND (`employee`.`company_id` = '{company}' OR '{company}' = 'ALL' OR `employee`.`row_id` IS NULL)
			";
			if(!App::isRootSessionEnabled()) {
				$SQL .= "
					AND u.`rolegroup_id` != 'a5b6bd79e008725744118c7c46e10cda'
				";
			}
			if (isset($gargSQL["where"])) {
				$SQL .= str_replace_first("AND", "AND (('{employee}' = '' AND `employee`.`row_id` IS NULL) OR (", $gargSQL["where"]) . "))";
			}

			$SQL .= "
				GROUP BY `user_id`
				ORDER BY `username`
			";
		} else {
			$SQL = "
				SELECT
					u.`username`,
					CONCAT(" . Employee::getParam('fullname_with_emp_id', 'e') . ", ' - ', MIN(e.`valid_from`), ' - ', MAX(e.`valid_to`)) AS `employee_id`,
					u.`employee_id` AS `grid_employee_id`,
					IFNULL(u.`email`, '') AS `email`,
					ar.`rolegroup_name` AS `rolegroup_id`,
					u.`lang`,
					u.`note`,
					u.`valid_from`,
					IFNULL(u.`valid_to`, '" . $this->defaultEnd . "') AS `valid_to`,
					u.`status`,
					u.`password`,
					u.`password_date`,
					IFNULL(u.`last_logged_in`, '1970-01-01 00:00:00') AS last_logged_in
				FROM `user` u
				LEFT JOIN `employee` e ON
						u.`employee_id` = e.`employee_id`
					AND e.`status` = " . $this->statusPublished . "
					AND u.`valid_from` <= IFNULL(e.`valid_to`, '" . $this->defaultEnd . "')
					AND e.`valid_from` <= IFNULL(u.`valid_to`, '" . $this->defaultEnd . "')
				LEFT JOIN `auth_rolegroup` ar ON
						ar.`rolegroup_id` = u.`rolegroup_id`
					AND ar.`visibility` = 1
				WHERE
						(u.`employee_id` = '{employee}' OR '{employee}' = 'ALL' OR '{employee}' = '')
					AND '{date}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '" . $this->defaultEnd . "')
					AND u.`status` IN (" . Status::DRAFT . ", " . $this->statusPublished . ", " . Status::LOCKED . ")
			";
			if(!App::isRootSessionEnabled()) {
				$SQL .= "
					AND u.`rolegroup_id` != 'a5b6bd79e008725744118c7c46e10cda'
				";
			}

			$SQL .= "
				GROUP BY `user_id`
				ORDER BY `username`
			";
		}

		return $SQL;
	}

	/**
	 * Munkavállaló kereső SQL-t adja vissza
	 * @return string
	 */
	private function getEmployeeSearchSQL()
	{
		if ($this->useCompanyAndPayrollRights)
		{
			$art		= new ApproverRelatedGroup;
			$gargSQL	= $art->getApproverReleatedGroupSQL("Employee", ["employeeManagement"], userID(), "'{date}'", "AND", "allDate");
			$SQL = "
				SELECT
					`employee`.`employee_id` AS id,
					" . Employee::getParam('fullname_with_emp_id') . " AS value
				FROM `employee`
				LEFT JOIN `employee_contract` ON
						`employee_contract`.`employee_id` = `employee`.`employee_id`
					AND `employee_contract`.`status` = {$this->statusPublished}
					AND '{date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->defaultEnd}')
					AND '{date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->defaultEnd}')
				" . EmployeeGroup::getAllActiveLeftJoinSQL("employee_contract", "'{date}'", "", "employee") . "
				" . EmployeeGroup::getAllBaseTablesWithGroup("employee", "employee_contract", "'{date}'") . "
			";
			if (isset($gargSQL["join"])) {
				$SQL .= $gargSQL["join"];
			}
			$SQL .= "
				WHERE
						`employee`.`status` = " . $this->statusPublished . "
					AND (" . Employee::getParam('fullname_with_emp_id')." LIKE '%%{search}%%')
					AND ('{date}' BETWEEN `employee`.`valid_from` AND default_end(`employee`.`valid_to`))
			";
			if (isset($gargSQL["where"])) {
				$SQL .= $gargSQL["where"];
			}
			$userId = userID();
			$controllerId = $this->getControllerID();
			$input = "searchInput_date";
			if(isset($_SESSION["tiptime"][$userId][$controllerId][$input])) {
				$SQL = str_replace("'{date}'", "'" . $_SESSION["tiptime"][$userId][$controllerId][$input] . "'", $SQL);
			}
		} else {
			$SQL = "
				SELECT
					`employee_id` AS id,
					" . Employee::getParam('fullname_with_emp_id') . " AS value
				FROM `employee`
				WHERE
						`status` = " . $this->statusPublished . "
					AND (" . Employee::getParam('fullname_with_emp_id')." LIKE '%%{search}%%')
					AND ('{date}' BETWEEN `valid_from` AND default_end(`valid_to`))
			";
		}

		return $SQL;
	}

	/**
	 * Visszaadjuk a munkavállaló legördülő SQL-ét.
	 * @param string $date
	 * @param boolean $onchange
	 * @return string
	 */
	public function getEmployeeDropdownSQL($date, $onchange = true)
	{
		$useCompanyAndPayrollRights	= (int)App::getSetting("useCompanyAndPayrollRights");
		$statusPublished			= Status::PUBLISHED;
		$defaultEnd					= App::getSetting("defaultEnd");
		if ($onchange) {
			if ($useCompanyAndPayrollRights) {
				$onchangeSQL = "
				AND ('{valid_from}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$defaultEnd}') OR '{valid_to}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$defaultEnd}'))
			";
			} else {
				$onchangeSQL = "
            	AND ('{valid_from}' BETWEEN `e1`.`valid_from` AND IFNULL(`e1`.`valid_to`, '{$defaultEnd}') OR '{valid_to}' BETWEEN `e2`.`valid_from` AND IFNULL(`e2`.`valid_to`, '{$defaultEnd}'))
        	";
			}
		} else {
			$onchangeSQL = "";
		}
		if ($useCompanyAndPayrollRights)
		{
			$art		= new ApproverRelatedGroup;
			$gargSQL	= $art->getApproverReleatedGroupSQL("Employee", ["employeeManagement"], userID(), "'" . $date . "'", "AND", "allDate");

			$SQL = "
				SELECT
					`employee`.`employee_id` AS id,
					CONCAT(" . Employee::getParam('fullname_with_emp_id') . ", ' - ', MIN(`employee`.`valid_from`), ' - ',MAX(`employee`.`valid_to`)) AS value
				FROM `employee`
				LEFT JOIN `employee_contract` ON
						`employee_contract`.`employee_id` = `employee`.`employee_id`
					AND `employee_contract`.`status` = {$statusPublished}
					AND '{$date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$defaultEnd}')
					AND '{$date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$defaultEnd}')
				" . EmployeeGroup::getAllActiveLeftJoinSQL("employee_contract", "'" . $date . "'", "", "employee") . "
				" . EmployeeGroup::getAllBaseTablesWithGroup("employee", "employee_contract", "'" . $date . "'") . "
			";
			if (isset($gargSQL["join"])) {
				$SQL .= $gargSQL["join"];
			}
			$SQL .= "
				WHERE
						`employee`.`status`= {$statusPublished}
					{$onchangeSQL}
			";
			if (isset($gargSQL["where"])) {
				$SQL .= $gargSQL["where"];
			}
			$SQL .= "
				GROUP BY `employee`.`employee_id`
				ORDER BY value
			";
		} else {
			$SQL = "
				SELECT
					e1.`employee_id` AS id,
					CONCAT(" . Employee::getParam('fullname_with_emp_id') . ", ' - ', e2.`valid_from`, ' - ',e2.`valid_to`) AS value
				FROM `employee`  e1
				LEFT JOIN (SELECT `employee_id`,
									 MIN(`valid_from`)  AS valid_from,
									 MAX(`valid_to`) AS valid_to
								FROM `employee`
								WHERE
										`status` = {$statusPublished}
								GROUP BY `employee_id`) e2 ON
						e2.employee_id = e1.employee_id
				WHERE
						e1.`status` = {$statusPublished}
					AND ('" . ($date ?? date('Y-m-d')) . "' BETWEEN e1.valid_from AND e1.valid_to
							OR '" . ($date ?? date('Y-m-d')) . "' > e2.valid_to)
					{$onchangeSQL}
				GROUP BY e1.`employee_id`
				ORDER BY value
			";
		}

		return $SQL;
	}
}
