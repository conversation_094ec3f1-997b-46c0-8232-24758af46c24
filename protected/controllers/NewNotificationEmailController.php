<?php

class NewNotificationEmailController extends Controller
{
    private const MAILS = 1;
    private const ROLEG = 2;
    private const RIGHT = 3;
    private const ALL = "'ALL'";
    private const GROUP_VALUE = 'group_value';
    private const EMPLOYEE_GROUP = 'employee_group_';
    private const SEVEN = '7';
    private const REMINDER_NOTIFICATION = 'Emlékeztető|Notification';
    private const NO_EMAIL_ADDRESS = 'no email address';
    private array $apconf;
    private array $clconf;
    private array $placeholders;
    private array $queries;
    private array $payload = [
        self::MAILS => [],
        self::ROLEG => [],
        self::RIGHT => []
    ];
    private array $attachments = [];
    private string $error = '';
    private int $showEmployeeDataInNotificationEmail;

    public function __construct($controllerID = 'newNotificationEmail')
    {
        parent::__construct($controllerID);

        $this->apconf = [
            'published' => Status::PUBLISHED,
            'defaultEnd' => App::getSetting('defaultEnd'),
            'customer' => Yang::getParam('customerDbPatchName'),
            'uploads' => dirname(dirname(__DIR__)) . DIRECTORY_SEPARATOR . 'webroot' . DIRECTORY_SEPARATOR . 'upload' . DIRECTORY_SEPARATOR,
        ];

        $this->clconf = [
            'pid' => requestParam('pid'),
            'path' => $this->apconf['uploads'] . 'PayrollTransfer' . DIRECTORY_SEPARATOR . $this->apconf['customer'] . DIRECTORY_SEPARATOR . date('YmdHis'),
            'debug' => requestParam('debug') && requestParam('debug') === '1',
            'fullname' => Employee::getParam('fullname', 'e')
        ];

        $this->showEmployeeDataInNotificationEmail = App::getSetting('showEmployeeDataInNotificationEmail');

        if (isset($this->clconf['pid']) && empty($this->clconf['pid'])) {
            $this->errorHandling('Missing request parameter: process id!');
        }

        $config = dbFetchRow("
            SELECT * FROM
                `notification_email_config`
            WHERE
                `process_id` = '{$this->clconf['pid']}'
            AND
                `status` = '{$this->apconf['published']}'
        ");

        if (!$config) {
            $this->errorHandling('The SQL query on `notification_email_config` table returned false value!');
        }

        if (empty($config['addresses']) && empty($config['rolegroup_ids']) && empty($config['process_ids'])) {
            $this->errorHandling('Config error: at least one of the three arguments must has value!');
        }
        if (!empty($config['addresses']) && !empty($config['rolegroup_ids']) && !empty($config['process_ids'])) {
            $this->errorHandling('Config error: at least one or at most two of the three arguments must has value!');
        }

        foreach ($config as $key => $value)
        {
            switch ($key) {
                case 'day_before_event':
                    $this->clconf[$key] = !empty($value) ? $value : self::SEVEN;
                    break;
                case 'subject':
                    $this->clconf[$key] = $value ?? ($config['note'] ?? self::REMINDER_NOTIFICATION);
                    break;
                case 'data_to_csv':
                case 'sendToEmployees':
                    $this->clconf[$key] = ($value == 1);
                    break;
                case 'sendEmptyNotificationEmail':
                    $this->clconf[$key] = (
                        ((int)App::getSetting('sendEmptyNotificationEmail') === 1 && $value === 1) ||
                        ((int)App::getSetting('sendEmptyNotificationEmail') === 0 && $value === 1)
                    );
                    break;
                case 'addresses':
                    if (!empty($value)) {
                        if (empty($config['rolegroup_ids']) && empty($config['process_ids'])) {
                            $this->clconf['mode'] = self::MAILS;
                            $this->clconf['addresses'] = explode(';', $value);
                            $this->clconf['rolegroups'] = '';
                            $this->clconf['processes'] = [];
                        }
                    }
                    break;
                case 'rolegroup_ids':
                    if (!empty($value) && !empty($config['rolegroup_ids'])) {
                        $this->clconf['mode'] = self::ROLEG;
                        $this->clconf['addresses'] = !empty($config['addresses']) ? explode(';', $config['addresses']) : [];
                        $this->clconf['rolegroups'] = str_replace(';', "','", $value);
                        $this->clconf['processes'] = weHaveModule('ttwa-csm') ? ['competency'] : ['employeeManagement'];
                    }
                    break;
                case 'process_ids':
                    if (!empty($value) && !empty($config['process_ids'])) {
                        $this->clconf['mode'] = self::RIGHT;
                        $this->clconf['addresses'] = !empty($config['addresses']) ? explode(';', $config['addresses']) : [];
                        $this->clconf['processes'] = explode(';', $value);
                    }
                    break;
                default:
                    $this->clconf[$key] = $value;
                    break;
            }
        }

        $this->placeholders = [
            '{full_name}' => $this->clconf['fullname'],
            '{end_date}' => date('Y-m-d', strtotime(date('Y-m-d') . ' + ' . $this->clconf['day_before_event'] . ' days'))
        ];

        if (empty($this->clconf['sql'])) {
            $this->errorHandling('Incomplete config: missing SQL query!');
        }

        $sql = $this->bindValues($this->clconf['sql']);
        Yang::log($sql, 'log', 'system.SOAD');
        $this->queries = explode(';', $sql);

        foreach ($this->queries as $query)
        {
            $result = dbFetchAll($query);

            if ($this->clconf['data_to_csv']) {
                $this->attachments['csv'] .= ArrayToCsv::getCsvText($result);
            }

            if ($this->clconf['sendEmptyNotificationEmail']) {
                $this->payload[self::MAILS][] = $result;
            } elseif (!empty($result)) {
                $this->payload[self::MAILS][] = $result;
            }
        }

        if ($this->clconf['data_to_csv']) {
            if (!$this->writeDataToCsv()) {
                $this->errorHandling('Writing to csv file failed.');
            }
        }
    }

    private function errorHandling(string $message = ''): void
    {
        $error = empty($message) ? $this->error : $message;

        if (!empty($error)) {
            Yang::log($error, 'error', 'system.SOAD');
        }
    }

    private function bindValues(string $sql): string
    {
        return str_replace(array_keys($this->placeholders), array_values($this->placeholders), $sql);
    }

    public function actionIndex(): void
    {
        if ($this->clconf['sendToEmployees']) {
            $this->sendToEmployees();
        }

        switch ($this->clconf['mode']) {
            case self::MAILS:
                $this->sendByMailList();
                break;
            case self::ROLEG:
            case self::RIGHT:
                $this->sendToApprovers();
                break;
        }

        $this->errorHandling();
    }

    private function writeDataToCsv(): bool
    {
        if (!is_dir($this->clconf['path'])) {
            mkdir($this->clconf['path'], 0777, true);
        }

        $fgID = md5(__CLASS__ . date('YmdHis') . userID() . rand(0, 1000000));
        $fileName = 'expiringCompetencyMail.csv';
        $csv = iconv('UTF-8', 'ISO-8859-2', $this->attachments['csv']);

        $fs = new FS();
        $fs->disableMySQLStore();
        $fs->setFileGroupID($fgID);
        $fs->uploadTextFileFromContent($fileName, $csv, 'text/csv');

        $file = fopen($this->clconf['path'] . DIRECTORY_SEPARATOR . $fileName, 'w');
        $result = fwrite($file, $csv);
        return !$result ? $result : fclose($file);
    }

    private function getEmployeeName(string $address): array
    {
        $SQL = "
            SELECT
                email,
                IF(u.`employee_id` IS NULL OR u.`employee_id` = '', u.`username`, {$this->clconf['fullname']}) as fullname
            FROM user u
            LEFT JOIN employee e ON
                u.employee_id  = e.employee_id
                AND e.`status` = {$this->apconf['published']}
                AND NOW() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '{$this->apconf['defaultEnd']}')
            WHERE
                u.email = '{$address}'
                AND u.status = {$this->apconf['published']} AND NOW() BETWEEN u.valid_from AND u.valid_to";

        $result = dbFetchRow($SQL);
        return is_array($result) ? $result : [];
    }

    private function sendMail(?string $address, ?string $employee_name, array $content = []): void
    {
        $valid_address = filter_var($address, FILTER_VALIDATE_EMAIL);

        if ($valid_address)
        {
            if (empty($employee_name)) {
                $addr = $this->getEmployeeName($valid_address);
                if ($addr) {
                    $employee_name = (empty($addr['fullname']) ? strstr($addr['email'], '@', true) : $addr['fullname']);
                } else {
                    $employee_name = strstr($valid_address, '@', true);
                }
            }

            if (empty($content)) {
                $content = $this->payload[self::MAILS];
            }

            $attachmentPath = $this->clconf['data_to_csv'] ? $this->clconf['path'] . DIRECTORY_SEPARATOR . 'expiringCompetencyMail.csv' : '';
            $attachmentFileName = $this->clconf['data_to_csv'] ? 'expiringCompetencyMail.csv' : '';
            $es = new EmailSender($attachmentPath, $attachmentFileName);

            $result = $es->sendMail(
            /* $addresses */
                [
                    'addr' => [
                        [
                            'email' => $valid_address,
                            'name' => $employee_name
                        ]
                    ]
                ],
                /* $subject */
                Dict::getValue($this->clconf['subject']) ?: $this->clconf['subject'],
                /* $message */
                '',
                /* $view */
                ['application.views.notification', $this->clconf['file_name']],
                /* $vars */
                [
                    'employee_name' => $employee_name,
                    'mailContent' => $content,
                    'params' => $this->placeholders,
                    'messageText' => $this->clconf['message_text_dict_id']
                ],
                /* $images */
                [],
                /* $iCal */
                FALSE,
                /* $iCalString */
                '',
                /* $notSkipAppSettings */
                TRUE
            );

            if (!$result) {
                $this->error .= 'Something went wrong with sending to ' . $valid_address . '<br/>';
            }

        } else {
            $this->error .= 'Given e-mail address is invalid: ' . (empty($address) ? self::NO_EMAIL_ADDRESS : $address) . '<br/>';
        }
    }

    private function sendToEmployees(): void
    {
        $empComp = [];

        foreach ($this->payload[self::MAILS] as $index => $employees)
        {
            foreach ($employees as $key => $employee) {
                if (!isset($employee['email'])) {
                    $employee['email'] = User::getEmailAddressByEmployeeId($employee['employee_id']);
                }

                if (isset($employee['expiring_thing'], $employee['expiring_date'])) {
                    $empComp[$employee['email']]['expiring_thing'][] = [
                        'expiring_thing'    => $employee['expiring_thing'],
                        'expiring_date'     => $employee['expiring_date']
                    ];
                }

                if (!empty($employee['email'] && (int)$this->showEmployeeDataInNotificationEmail === 1)) {
                    $empComp[$employee['email']][$key] = [
                        'fullname'      => $employee['fullname'],
                        'position_name' => $employee['position_name'],
                        'emp_id'        => $employee['emp_id'],
                        'field_value'   => $employee['field_value'],
                        'field_name'    => $employee['field_name']
                    ];

                    $this->sendMail($employee['email'], $empComp[$employee['email']][$key]['fullname'], $empComp);
                    unset($empComp[$employee['email']]);

                } elseif (!empty($employee['email'])) {
                    $empComp[$employee['email']]['fullname'] = $employee['fullname'];
                }
            }
        }

        foreach ($empComp as $email => $value) {
            if (!$this->clconf['sendEmptyNotificationEmail'] && empty($value)) {
                $this->error .= $email . ': Empty result!';
            } else {
                $this->sendMail($email, $value['fullname'], $value);
            }
        }
    }

    private function sendByMailList(): void
    {
        foreach ($this->clconf['addresses'] as $email) {
            $this->sendMail($email, '', $this->payload[self::MAILS]);
        }
    }

    private function sendToApprovers(): void
    {
        $approversByRoleGroupIds = [];

        if (isset($this->clconf['rolegroups'])) {
            $approversByRoleGroupIds = $this->getApproverByRoleGroupIds($this->clconf['rolegroups']);
        }

        foreach ($this->payload[self::MAILS] as $approvers) {
            foreach ($approvers as $approver) {
                $actualApprovers = $this->getApproverUsersByEmpID($approver['emp_id']);

                if (!empty($approversByRoleGroupIds)) {
                    $allApprovers = $this->allApprovers($actualApprovers, $approversByRoleGroupIds);
                }

                $this->updatePayload($allApprovers ?? $actualApprovers, $approver);
            }
        }

        foreach ($this->payload[$this->clconf['mode']] as $user) {
            $this->sendMail($user['email'], $user['fullname'], [$user['messages']]);
        }

        if (!empty($this->clconf['addresses'])) {
            $this->sendByMailList();
        }
    }

    private function allApprovers(array $actualApprovers, array $approversByRoleGroupIds): array
    {
        $uniqueIds = array_column($actualApprovers, 'uid');

        foreach ($approversByRoleGroupIds as $approverByRoleGroupIds) {
            if (!in_array($approverByRoleGroupIds['uid'], $uniqueIds, true)) {
                $actualApprovers[] = $approverByRoleGroupIds;
            }
        }

        return $actualApprovers;
    }

    private function updatePayload(array $allApprovers, array $row): void
    {
        foreach ($allApprovers as $user) {
            if (!isset($this->payload[$this->clconf['mode']][$user['uid']])) {
                $this->payload[$this->clconf['mode']][$user['uid']] = [
                    'email'     => $user['email'],
                    'fullname'  => $user['fullname'],
                    'messages'  => [$row]
                ];
            } else {
                $this->payload[$this->clconf['mode']][$user['uid']]['messages'][] = $row;
            }
        }
    }

    protected function getActiveGroups(): array
    {
        $SQL = "
        SELECT DISTINCT
            `arg`.`related_group` AS `related_group`,
            `arg`.`related_table` AS `related_table`,
            `arg`.`related_model` AS `related_model`,
            `arg`.`related_id`    AS `related_id`
        FROM
            `approver_related_group` `arg`
        INNER JOIN
            `approver_related_type`  `art`
            ON  `art`.`related_model` = `arg`.`related_model`
            AND `art`.`related_id`    = `arg`.`related_id`
            AND `art`.`status` = {$this->apconf['published']}
        WHERE
            `arg`.`status` = {$this->apconf['published']}
        ";

        $query_result = dbFetchAll($SQL);
        $result = [];

        if (is_array($query_result)) {
            foreach ($query_result as $row) {
                $result[$row['related_group']][] = [
                    'related_table' => $row['related_table'],
                    'related_model' => $row['related_model'],
                    'related_id' => $row['related_id']
                ];
            }
        }

        return $result;
    }

    private function getApproverByRoleGroupIds($roleGroupIds): array
    {
        $result = [];

        $u = new User();
        $crit = new CDbCriteria();
        $crit->alias = 'u';
        $crit->select = 'u.`user_id`, u.`email`';
        $crit->condition = "
			    u.`rolegroup_id` IN ('$roleGroupIds')
			AND u.`status` = {$this->apconf['published']}
			AND u.`email` IS NOT NULL
			AND u.`email` <> ''
			AND u.`receive_email` = 1
			AND CURDATE() BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$this->apconf['defaultEnd']}')
		";

        $usersData = $u->findAll($crit);

        foreach ($usersData as $userData) {
            $result[] = [
                'uid'       => $userData['user_id'],
                'fullname'  => Employee::getEmployeeFullnameByUserID($userData['user_id']),
                'email'     => $userData['email']
            ];
        }

        return $result;
    }

    private function getEmployeeGroupDataByEmployeeContractId(string $empId): array
    {
        $employeeContractId = EmployeeContract::getEcIDByEmpId($empId);

        $eg = new EmployeeGroup();
        $crit = new CDbCriteria();
        $crit->alias = 'eg';
        $crit->condition = "
			    eg.`employee_contract_id` = '$employeeContractId'
			AND eg.`status` = {$this->apconf['published']}
			AND CURDATE() BETWEEN eg.`valid_from` AND IFNULL(eg.`valid_to`, '{$this->apconf['defaultEnd']}')
		";

        return $eg->findAll($crit);
    }

    public function getApproverUsersByEmpID(string $empId): array
    {
        $approvers = 0;
        $relatedName = '';
        $groupTable = '';
        $processCount = count($this->clconf['processes']);
        $employeeGroupDataByEcId = $this->getEmployeeGroupDataByEmployeeContractId($empId);

        $where = '
                        WHERE';
        $SQL = "
                        SELECT
                            `user`.`user_id` AS `uid`,
                            {$this->clconf['fullname']} AS `fullname`,
                            CASE
                                WHEN (`user`.`email` IS NOT NULL AND `user`.`email` <> '') AND `user`.`receive_email` = 1
                                    THEN `user`.`email`
                                WHEN (`user`.`email` IS NOT NULL AND `user`.`email` <> '') AND `user`.`receive_email` = 0
                                    THEN CONCAT(({$this->clconf['fullname']}), 'cannot receive email')
                                WHEN (`user`.`email` IS NULL OR `user`.`email` = '')
                                    THEN CONCAT(({$this->clconf['fullname']}), 'does not have an email address')
                            END AS `email`
                        FROM `user`
        ";

        $SQL .= "
                        LEFT JOIN `employee` `e`
                            ON  `e`.`employee_id` = `user`.`employee_id`
                            AND CURDATE() BETWEEN `e`.`valid_from` AND IFNULL(`e`.`valid_to`, '{$this->apconf['defaultEnd']}')
                            AND `e`.`status` = {$this->apconf['published']}
                        LEFT JOIN `employee_contract` `ec`
                            ON  `ec`.`employee_id` = `e`.`employee_id`
                            AND CURDATE() BETWEEN `ec`.`valid_from` AND IFNULL(`ec`.`valid_to`, '{$this->apconf['defaultEnd']}')
                            AND CURDATE() BETWEEN `ec`.`ec_valid_from` AND IFNULL(`ec`.`ec_valid_to`, '{$this->apconf['defaultEnd']}')
                            AND `ec`.`status` = {$this->apconf['published']}
                        LEFT JOIN `employee`
                            ON  `employee`.`emp_id` = '$empId'
                            AND CURDATE() BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->apconf['defaultEnd']}')
                            AND `employee`.`status` = {$this->apconf['published']}
                        LEFT JOIN `company` ON
                                `company`.`company_id` = `employee`.`company_id`
                            AND `company`.`status` = {$this->apconf['published']}
                            AND CURDATE() BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$this->apconf['defaultEnd']}')
                        LEFT JOIN `payroll` ON
                                `payroll`.`payroll_id` = `employee`.`payroll_id`
                            AND `payroll`.`status` = {$this->apconf['published']}
                            AND CURDATE() BETWEEN `payroll`.`valid_from` AND IFNULL(`payroll`.`valid_to`, '{$this->apconf['defaultEnd']}')
                        LEFT JOIN `employee_contract`
                            ON  `employee_contract`.`employee_id` = `employee`.`employee_id`
                            AND CURDATE() BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->apconf['defaultEnd']}')
                            AND CURDATE() BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->apconf['defaultEnd']}')
                            AND `employee_contract`.`status` = {$this->apconf['published']}
        ";

        if (weHaveModule('ttwa-csm')) {
            $SQL .= "
                        LEFT JOIN `employee_competency` AS `employee_competency`
                            ON  `employee_competency`.`employee_contract_id` = `employee_contract`.`employee_contract_id`
                            AND `employee_competency`.`status` = {$this->apconf['published']}
            ";
        }

        $activeGroups = (int)$this->showEmployeeDataInNotificationEmail === 1 ? EmployeeGroupConfig::getActiveGroups(true) : $this->getActiveGroups();

        if (isset($activeGroups['Employee'])) {
            foreach ($activeGroups['Employee'] as $group) {
                $dtn = ($group['related_table'] == 'workgroup') ? 'employee_contract' : 'employee';
                $relatedName = '';

                if ((int)App::getSetting('userRightRelatedValAcceptStarEnd') === 1) {
                    $relTable = $group['related_table'] ?? '';
                    $relModel = $group['related_model'] ?? '';
                    $relId = $group['related_id'] ?? '';
                    $name = substr($relId, 0, -2) . 'name';

                    switch ($relModel) {
                        case 'Company':
                            $table = 'company';
                            $relatedName = "`{$table}`" . '.' . "`{$name}`";
                            break;
                        case 'Payroll':
                            $table = 'payroll';
                            $relatedName = "`{$table}`" . '.' . "`{$name}`";
                            break;
                        case 'employee':
                            $relatedName = Employee::getParam('fullname', 'employee');
                            break;
                        case 'EmployeeContract':
                            $relatedName = Employee::getParam('fullname_with_emp_id_ec_id', ['employee', 'employee_contract']);
                            break;
                        default:
                            $table = $relTable;
                            $relatedName = "`{$table}`" . '.' . "`{$name}`";
                            break;
                    }
                }

                $SQL .= "
                        LEFT JOIN `{$group['related_table']}`
                            ON  `{$group['related_table']}`.`{$group['related_id']}` = `$dtn`.`{$group['related_id']}`
                            AND CURDATE() BETWEEN `{$group['related_table']}`.`valid_from` AND IFNULL(`{$group['related_table']}`.`valid_to`, '{$this->apconf['defaultEnd']}')
                            AND `{$group['related_table']}`.`status` = {$this->apconf['published']}
                ";

                foreach ($this->clconf['processes'] as $process) {
                    if (empty($process)) {
                        continue;
                    }

                    $approvers++;

                    $SQL .= $this->leftJoinApproverSQL($process, $group['related_id'], $group['related_model'], $group['related_table'], $relatedName);
                    $where .= $this->getWhereCondition($process, $group['related_id'], $processCount, $approvers);
                }
            }
        } else {
            foreach ($activeGroups as $activeGroup) {
                $groupId = $activeGroup['groupId'];
                $groupModel = $activeGroup['groupModel'];

                if ($groupId != $employeeGroupDataByEcId[0]->group_id) {
                    continue;
                }

                if (EmployeeGroupConfig::isActiveGroup($groupId)) {
                    $SQL .= EmployeeGroup::getLeftJoinSQL($groupId, 'employee_contract', 'CURDATE()');
                }

                foreach ($this->clconf['processes'] as $process) {
                    if (empty($process)) {
                        continue;
                    }
                    $approvers++;

                    $SQL .= $this->leftJoinApproverSQL($process, $groupId, $groupModel, $groupTable, $relatedName);
                    $where .= $this->getWhereCondition($process, $groupId, $processCount, $approvers);
                }
            }
        }

        $where .= "
                    AND CURDATE() BETWEEN `user`.`valid_from` AND IFNULL(`user`.`valid_to`, '{$this->apconf['defaultEnd']}')
                    AND `user`.`status` = {$this->apconf['published']}
                    GROUP BY uid
        ";

        $SQL .= $where;

        $results = dbFetchAll($SQL);

        return is_array($results) ? $results : [];
    }

    private function leftJoinApproverSQL(string $process, string $groupId, string $groupModel, string $groupTable, ?string $relatedName): string
    {
        $starEndingSQL = '';
        $groupTable = !empty($groupTable) ? $groupTable : self::EMPLOYEE_GROUP . $groupId;
        $groupValue = (int)$this->showEmployeeDataInNotificationEmail === 1 ? self::GROUP_VALUE : $groupId;

        if ($relatedName != '') {
            $starEndingSQL = "OR IF(RIGHT({$process}_approver.`related_value`, 1) = '*', {$relatedName} LIKE CONCAT(LEFT({$process}_approver.`related_value`, CHAR_LENGTH({$process}_approver.`related_value`) - 1), '%'), 1=0) ";
        }

        return "
                LEFT JOIN `approver` `{$process}_approver_$groupId`
                    ON  `{$process}_approver_$groupId`.`process_id`    = '$process'
                    AND `{$process}_approver_$groupId`.`related_model` = '$groupModel'
                    AND `{$process}_approver_$groupId`.`related_id`    = '$groupId'
                    AND (
                        `{$process}_approver_$groupId`.`related_value` = `$groupTable`.`$groupValue`
                        OR
                        `{$process}_approver_$groupId`.`related_value` = " . self::ALL . "
                        {$starEndingSQL}
                    )
                    AND  CURDATE() BETWEEN `{$process}_approver_$groupId`.`valid_from` AND IFNULL(`{$process}_approver_$groupId`.`valid_to`, '{$this->apconf['defaultEnd']}')
                    AND `{$process}_approver_$groupId`.`status` = {$this->apconf['published']}
                ";
    }

    private function getWhereCondition(string $process, string $groupId, int $processCount, int $approvers): string
    {
        $whereCondition = "
                `user`.`user_id` = `{$process}_approver_$groupId`.`approver_user_id`
            ";

        if ($processCount === 1) {
            return $whereCondition;
        }

        if ($approvers === 1) {
            return " ( $whereCondition OR ";
        }

        if ($approvers === $processCount) {
            return "$whereCondition )";
        }

        return $whereCondition;
    }
}