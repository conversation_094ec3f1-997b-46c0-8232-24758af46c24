<?php

class MissingEmployeeController extends Grid2Controller
{
	private $draft;
	private $published;
	private $archieved;
	private $draftDelete;
	private $deleted;
	private $saved;
	private $locked;
	private $invalid;
	private $user;
	private $customer;
	private array $filter;
	private string $filterName;

	public function __construct()
	{
		parent::__construct("missingEmployee");

		parent::enableLAGrid();
		$this->user         = isset($_SESSION["tiptime"]["userIdToSwitch"]) ? $_SESSION["tiptime"]["userIdToSwitch"] : userID();
		$this->draft		= Status::DRAFT;
		$this->published	= Status::PUBLISHED;
		$this->archieved	= Status::ARCHIVED;
		$this->draftDelete	= Status::DRAFT_DELETE;
		$this->deleted		= Status::DELETED;
		$this->saved		= Status::SAVED;
		$this->locked		= Status::LOCKED;
		$this->invalid		= Status::INVALID;
		$this->customer     = Yang::getParam('customerDbPatchName');
		$this->filter 		= requestParam('searchInput', []);
		$this->filterName	= str_replace('/', '_', $this->getControllerID()) . '_filters';
	}

	public function actionSetInitProperties() {

		if (!empty($this->filter)) {
			Yang::setSessionValue($this->filterName, $this->filter);
		} else {
			$this->filter = requestParam('searchInput', []);
		}

		parent::actionSetInitProperties();
	}

	protected function getStatusButtons($gridID = null)
	{
		if (!isset($gridID)) {
			$gridID = 'dhtmlxGrid';
		} else if (empty($gridID)) {
			$gridID = 'dhtmlxGrid';
		}

		$actButtons = parent::getStatusButtons($gridID);

		$modButtons = [];

		$modButtons["getDuplicateAbsences"] = [
						"type"		=> "button",
						"id"		=> "getDuplicateAbsences",
						"class"		=> "getDuplicateAbsences",
						"name"		=> "getDuplicateAbsences",
						"img"		=> "/images/status_icons/st_copy.png",
						"label"		=> "Szabadság duplázódás",
						"onclick"	=> "getDuplicateAbsences()",
					];

		$modButtons["deleteDuplicateAbsences"] = [
						"type"		=> "button",
						"id"		=> "deleteDuplicateAbsences",
						"class"		=> "deleteDuplicateAbsences",
						"name"		=> "deleteDuplicateAbsences",
						"img"		=> "/images/status_icons/st_del.png",
						"label"		=> "Szabadság duplázódás törlés",
						"onclick"	=> "deleteDuplicatedAbsence()",
					];

		$modButtons["tableSizeList"] = [
						"type"		=> "button",
						"id"		=> "tableSizeList",
						"class"		=> "tableSizeList",
						"name"		=> "tableSizeList",
						"img"		=> "/images/status_icons/st_details.png",
						"label"		=> "Tábla méret lista",
						"onclick"	=> "tableSizeList()",
					];
		$modButtons["getDuplicateEmployee"] = [
						"type"		=> "button",
						"id"		=> "getDuplicateEmployee",
						"class"		=> "getDuplicateEmployee",
						"name"		=> "getDuplicateEmployee",
						"img"		=> "/images/status_icons/st_copy.png",
						"label"		=> "Employee duplázódás",
						"onclick"	=> "getDuplicateEmployee()",
					];

		$buttons = array_merge($modButtons, $actButtons);

		return $buttons;
	}

	protected function G2BInit()
	{
		parent::setControllerPageTitleId("page_title_missingEmployee");

		$session_filter = Yang::session($this->filterName, []);

		if (empty($this->filter) && !empty($session_filter)) { $this->filter = $session_filter;	}

		$assetPath = Yang::addAsset(Yang::getAlias('application.assets.base.missingEmployee.js'), false, -1, true);
		Yang::registerScriptFile($assetPath . '\missingEmployee.js');

		if (isset($this->filter["browseTab"])) {
			$functionName = 'get' . $this->filter["browseTab"] . 'Grid';
			$this->LAGridDB->setModelName($this->filter["browseTab"]);
			$SQL = $this->$functionName();
			$this->LAGridDB->setSQLSelection($SQL, "row_id");
		}

		$this->LAGridDB->enableSQLMode();

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("export_xlsx",		false);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("modify",			true);
		$this->LAGridRights->overrideInitRights("reload",			true);

		parent::G2BInit();
	}

	private function getEmployeeGrid()
	{
		$SQL = "SELECT employee.*,
					IFNULL(userCreated.username, employee.created_by) AS createdBy,
					IFNULL(userMod.username, employee.modified_by) AS modifiedBy
				FROM employee
				LEFT JOIN user userCreated ON
						employee.created_by = userCreated.user_id
					AND userCreated.status = " . $this->published . "
				LEFT JOIN user userMod ON
						employee.modified_by = userMod.user_id
					AND userMod.status = " . $this->published . "
				";
		return $SQL;
	}

	private function getEmployeeContractGrid()
	{
		$SQL = "SELECT employee_contract.*,
					IFNULL(userCreated.username, employee_contract.created_by) AS createdBy,
					IFNULL(userMod.username, employee_contract.modified_by) AS modifiedBy
				FROM employee_contract
				LEFT JOIN user userCreated ON
						employee_contract.created_by = userCreated.user_id
					AND userCreated.status = " . $this->published . "
				LEFT JOIN user userMod ON
						employee_contract.modified_by = userMod.user_id
					AND userMod.status = " . $this->published . "
				";
		return $SQL;
	}

	private function getEmployeeCardGrid()
	{
		$SQL = "SELECT employee_card.*,
					IFNULL(userCreated.username, employee_card.created_by) AS createdBy,
					IFNULL(userMod.username, employee_card.modified_by) AS modifiedBy
				FROM employee_card
				LEFT JOIN user userCreated ON
						employee_card.created_by = userCreated.user_id
					AND userCreated.status = " . $this->published . "
				LEFT JOIN user userMod ON
						employee_card.modified_by = userMod.user_id
					AND userMod.status = " . $this->published . "
				";
		return $SQL;
	}

	private function getEmployeeBaseAbsenceGrid()
	{
		$SQL = "SELECT employee_base_absence.*,
					d.dict_value AS base_absence_type_name,
					IFNULL(userCreated.username, employee_base_absence.created_by) AS createdBy,
					IFNULL(userMod.username, employee_base_absence.modified_by) AS modifiedBy
				FROM employee_base_absence
				LEFT JOIN base_absence_type bat ON
					bat.base_absence_type_id = employee_base_absence.base_absence_type_id
					AND bat.status =  " . $this->published . "
				LEFT JOIN dictionary d ON
					d.dict_id = bat.dict_id
					AND d.valid = 1
					AND d.lang = '" . Dict::getLang() . "'
				LEFT JOIN user userCreated ON
						employee_base_absence.created_by = userCreated.user_id
					AND userCreated.status = " . $this->published . "
				LEFT JOIN user userMod ON
						employee_base_absence.modified_by = userMod.user_id
					AND userMod.status = " . $this->published . "
				";
		return $SQL;
	}

	public function search()
	{
		$search['browseTab'] =
		[
			'col_type'		=> 'combo',
			'options'		=>	[
				'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
				'array'	=> [	["id" => "Employee", "value" => Dict::getValue("tab_employeetabs_employee")],
								["id" => "EmployeeContract", "value" => Dict::getValue("tab_employeetabs_employeecontract")],
								["id" => "EmployeeCard", "value" => Dict::getValue("tab_employeetabs_employeecard")],
								["id" => "EmployeeBaseAbsence", "value" => Dict::getValue("tab_employeetabs_employeebaseabsence")]
							],
			],
			'label_text'	=> Dict::getValue("operation_select"),
			'default_value'	=> 'Employee'
		];

		$submit = ['submit' => ['col_type' => 'searchBarReinitGrid', 'gridID' => 'dhtmlxGrid', 'width' => '*', 'label_text' => ''] ];

		return Yang::arrayMerge($search, $submit);
	}

	public function columns()
	{
		$disabled = true;
		if ($this->user == '6acd9683761b153750db382c1c3694f6')
		{
			$disabled = false;
		}

		if ($this->filter["browseTab"] == 'Employee') {
			$columns = [
				'row_id' => ['export' => false, 'width' => 150, 'grid' => false, 'window' => false],
				'employee_id' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'company_id' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'company_org_group1_id' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'company_org_group2_id' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'company_org_group3_id' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'unit_id' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'payroll_id' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'emp_id' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'first_name' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'last_name' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'tax_number' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'status' => [
					'col_type' => 'combo',
					'width' => 150,
					'options' => [
						'mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY,
						'array' => [
							["id" => $this->draft, "value" => Dict::getValue('status_wait_for_approval')],
							["id" => $this->published, "value" => Dict::getValue('status_active')],
							["id" => $this->archieved, "value" => Dict::getValue('status_archived')],
							["id" => $this->draftDelete, "value" => Dict::getValue('wait_for_delete')],
							["id" => $this->deleted, "value" => Dict::getValue('status_deleted')],
							["id" => $this->saved, "value" => 'Mentett'],
							["id" => $this->locked, "value" => Dict::getValue('locked')],
							["id" => $this->invalid, "value" => 'Érvénytelen'],
						],
					],
					'cssClass' => 'bluegrey',
				],
				'valid_from' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'dPicker' => true, 'cssClass' => 'bluegrey'],
				'valid_to' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'dPicker' => true, 'cssClass' => 'bluegrey'],
				'createdBy' => ['export' => true, 'width' => 150, 'grid' => true, 'window' => false, 'col_type' => 'ed', 'disabled' => true],
				'created_by' => ['export' => true, 'width' => 150, 'grid' => false, 'window' => true, 'col_type' => 'ed', 'disabled' => true],
				'created_on' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => true],
				'modifiedBy' => ['export' => true, 'width' => 150, 'grid' => true, 'window' => false, 'col_type' => 'ed', 'disabled' => true],
				'modified_by' => ['export' => true, 'width' => 150, 'grid' => false, 'window' => true, 'col_type' => 'ed', 'disabled' => true],
				'modified_on' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => true],
			];
		} else if ($this->filter["browseTab"] == 'EmployeeContract') {
			$columns = [
				'employee_id' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'employee_contract_id' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'employee_contract_number' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'ec_valid_from' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'ec_valid_to' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'employee_contract_type' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'wage_type' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'workgroup_id' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'employee_position_id' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'daily_worktime' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => $disabled],
				'status' => [
					'col_type' => 'combo',
					'width' => 150,
					'options' => [
						'mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY,
						'array' => [
							["id" => $this->draft, "value" => Dict::getValue('status_wait_for_approval')],
							["id" => $this->published, "value" => Dict::getValue('status_active')],
							["id" => $this->archieved, "value" => Dict::getValue('status_archived')],
							["id" => $this->draftDelete, "value" => Dict::getValue('wait_for_delete')],
							["id" => $this->deleted, "value" => Dict::getValue('status_deleted')],
							["id" => $this->saved, "value" => 'Mentett'],
							["id" => $this->locked, "value" => Dict::getValue('locked')],
							["id" => $this->invalid, "value" => 'Érvénytelen'],
						],
					],
					'cssClass' => 'bluegrey'
				],
				'valid_from' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'dPicker' => true, 'cssClass' => 'bluegrey'],
				'valid_to' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'dPicker' => true, 'cssClass' => 'bluegrey'],
				'createdBy' => ['export' => true, 'width' => 150, 'grid' => true, 'window' => false, 'col_type' => 'ed', 'disabled' => true],
				'created_by' => ['export' => true, 'width' => 150, 'grid' => false, 'window' => true, 'col_type' => 'ed', 'disabled' => true],
				'created_on' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => true],
				'modifiedBy' => ['export' => true, 'width' => 150, 'grid' => true, 'window' => false, 'col_type' => 'ed', 'disabled' => true],
				'modified_by' => ['export' => true, 'width' => 150, 'grid' => false, 'window' => true, 'col_type' => 'ed', 'disabled' => true],
				'modified_on' => ['export' => true, 'width' => 150, 'grid' => true, 'col_type' => 'ed', 'disabled' => true],
			];
		}
		else if ($this->filter["browseTab"] == 'EmployeeCard') {
			$columns = [
				'employee_contract_id'		=> ['export'=> true, 'width' => 150, 'grid'=>true, 'col_type'=>'ed', 'disabled' => $disabled],
				'card'		=> ['export'=> true, 'width' => 150, 'grid'=>true, 'col_type'=>'ed', 'disabled' => $disabled],
				'status'					=> [
												'col_type'=>'combo',
												'width'=>150,
												'options'	=>	[
															'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
															'array'	=>	[
																			["id" => $this->draft, "value" => Dict::getValue('status_wait_for_approval')],
																			["id" => $this->published, "value" => Dict::getValue('status_active')],
																			["id" => $this->archieved, "value" => Dict::getValue('status_archived')],
																			["id" => $this->draftDelete, "value" => Dict::getValue('wait_for_delete')],
																			["id" => $this->deleted, "value" => Dict::getValue('status_deleted')],
																			["id" => $this->saved, "value" => 'Mentett'],
																			["id" => $this->locked, "value" => Dict::getValue('locked')],
																			["id" => $this->invalid, "value" => 'Érvénytelen'],
																		],
														],
												'cssClass' => 'bluegrey'
											],
				'valid_from'				=> ['export'=>true, 'width' => 150, 'grid'=>true, 'col_type'=>'ed', 'dPicker' =>true, 'cssClass' => 'bluegrey'],
				'valid_to'					=> ['export'=>true, 'width' => 150, 'grid'=>true, 'col_type'=>'ed', 'dPicker' =>true, 'cssClass' => 'bluegrey'],
				'createdBy'					=> ['export'=> true, 'width' => 150, 'grid'=>true, 'window' => false, 'col_type'=>'ed', 'disabled' => true],
				'created_by'				=> ['export'=> true, 'width' => 150, 'grid'=>false, 'window' => true, 'col_type'=>'ed', 'disabled' => true],
				'created_on'				=> ['export'=> true, 'width' => 150, 'grid'=>true, 'col_type'=>'ed', 'disabled' => true],
				'modifiedBy'				=> ['export'=> true, 'width' => 150, 'grid'=>true, 'window' => false, 'col_type'=>'ed', 'disabled' => true],
				'modified_by'				=> ['export'=> true, 'width' => 150, 'grid'=>false, 'window' => true, 'col_type'=>'ed', 'disabled' => true],
				'modified_on'				=> ['export'=> true, 'width' => 150, 'grid'=>true, 'col_type'=>'ed', 'disabled' => true],
			];
		}
		else if ($this->filter["browseTab"] == 'EmployeeBaseAbsence') {
			$columns = [
				'employee_contract_id'		=> ['export'=> true, 'width' => 150, 'grid'=>true, 'col_type'=>'ed', 'disabled' => $disabled],
				'base_absence_type_name'	=> ['export'=> true, 'width' => 150, 'grid'=>true, 'window' => false, 'col_type'=>'ed', 'disabled' => $disabled],
				'quantity'		=> ['export'=> true, 'width' => 150, 'grid'=>true, 'col_type'=>'ed', 'disabled' => $disabled],
				'status'					=> [
												'col_type'=>'combo',
												'width'=>150,
												'options'	=>	[
															'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
															'array'	=>	[
																			["id" => $this->draft, "value" => Dict::getValue('status_wait_for_approval')],
																			["id" => $this->published, "value" => Dict::getValue('status_active')],
																			["id" => $this->archieved, "value" => Dict::getValue('status_archived')],
																			["id" => $this->draftDelete, "value" => Dict::getValue('wait_for_delete')],
																			["id" => $this->deleted, "value" => Dict::getValue('status_deleted')],
																			["id" => $this->saved, "value" => 'Mentett'],
																			["id" => $this->locked, "value" => Dict::getValue('locked')],
																			["id" => $this->invalid, "value" => 'Érvénytelen'],
																		],
														],
												'cssClass' => 'bluegrey'
											],
				'valid_from'				=> ['export'=>true, 'width' => 150, 'grid'=>true, 'col_type'=>'ed', 'dPicker' =>true, 'cssClass' => 'bluegrey'],
				'valid_to'					=> ['export'=>true, 'width' => 150, 'grid'=>true, 'col_type'=>'ed', 'dPicker' =>true, 'cssClass' => 'bluegrey'],
				'createdBy'					=> ['export'=> true, 'width' => 150, 'grid'=>true, 'window' => false, 'col_type'=>'ed', 'disabled' => true],
				'created_by'				=> ['export'=> true, 'width' => 150, 'grid'=>false, 'window' => true, 'col_type'=>'ed', 'disabled' => true],
				'created_on'				=> ['export'=> true, 'width' => 150, 'grid'=>true, 'col_type'=>'ed', 'disabled' => true],
				'modifiedBy'				=> ['export'=> true, 'width' => 150, 'grid'=>true, 'window' => false, 'col_type'=>'ed', 'disabled' => true],
				'modified_by'				=> ['export'=> true, 'width' => 150, 'grid'=>false, 'window' => true, 'col_type'=>'ed', 'disabled' => true],
				'modified_on'				=> ['export'=> true, 'width' => 150, 'grid'=>true, 'col_type'=>'ed', 'disabled' => true],
			];
		}

		return $columns;
	}

	public function attributeLabels()
	{
		if ($this->filter["browseTab"] == 'Employee') {
			return [
				'employee_id' => Dict::getValue("tab_employeetabs_employee") . "<br>" . Dict::getValue("employee_id"),
				'createdBy' => Dict::getValue("created_by"),
				'modifiedBy' => Dict::getValue("modified_by"),
			];
		}
		else if ($this->filter["browseTab"] == 'EmployeeContract') {
			return [
				'employee_id' => Dict::getValue("tab_employeetabs_employeecontract") . "<br>" . Dict::getValue("employee_id"),
				'createdBy' => Dict::getValue("created_by"),
				'modifiedBy' => Dict::getValue("modified_by"),
			];
		}
		else if ($this->filter["browseTab"] == 'EmployeeCard') {
			return [
				'employee_contract_id'		=> Dict::getValue("tab_employeetabs_employeecard") . "<br>" . Dict::getValue("employee_contract_id"),
				'createdBy'					=> Dict::getValue("created_by"),
				'modifiedBy'				=> Dict::getValue("modified_by"),
			];
		}
		else if ($this->filter["browseTab"] == 'EmployeeBaseAbsence') {
			return [
				'employee_contract_id'		=> Dict::getValue("tab_employeetabs_employeebaseabsence") . "<br>" . Dict::getValue("employee_contract_id"),
				'createdBy'					=> Dict::getValue("created_by"),
				'modifiedBy'				=> Dict::getValue("modified_by"),
				'base_absence_type_name'	=> Dict::getValue("absence_name"),
				];
		}
	}

	public function actionGetDuplicateEmployee(){
		$sql = "SELECT
					tb1.row_id,
                    tb1.emp_id,
                    tb1.first_name,
                    tb2.last_name,
                    CONCAT(tb2.last_name, ' ', tb1.first_name, ' ', tb1.emp_id ) AS fullname,
                    tb1.employee_id,
                    tb1.valid_from as valid_from1,
                    tb1.valid_to as valid_to1,
                    tb2.row_id,
                    tb2.employee_id,
                    tb2.valid_from as valid_from2,
                    tb2.valid_to as valid_to2
				FROM
					employee as tb1
				LEFT JOIN
					employee as tb2
				ON
					tb1.employee_id = tb2.employee_id
                    AND current_date() BETWEEN tb1.valid_from AND IFNULL(tb1.valid_to, '2038-01-01')
                    AND tb1.status=2
                    AND tb2.status=2
				WHERE
					tb1.valid_from<>tb2.valid_from
                    AND tb1.valid_to = tb2.valid_to";

		$res = dbFetchAll($sql);

		if (count($res) > 0){
			$message = '<table cellpadding="0" cellspacing="0" style="border-collapse: collapse">
						<tr style = "background-color: #039BE5; color: #FEFEFF">
							<td><b> ' . Dict::getValue('fullname') . ' </b></td>
							<td><b> ' . Dict::getValue('valid_from') . ' </b></td>
							<td><b> ' . Dict::getValue('valid_to') . ' </b></td>
						</tr>';

			$rowNum = 0;
			$color = '';
			foreach ($res as $row)
			{
				$color = '#F9F9FC';
				if ($rowNum % 2 == 0)
				{
					$color = '#FEFEFF';
				}
				$message .= '<tr style = "background-color: ' . $color . '">
							<td style = "border:1px solid; border-color: #E6E6E6">' . $row['fullname'] . '</td>
							<td style = "border:1px solid; border-color: #E6E6E6"> ' . $row['valid_from1'] . '</td>
							<td style = "border:1px solid; border-color: #E6E6E6"> ' . $row['valid_to1'] . '</td>
						</tr>';
				$message .= '<tr style = "background-color: ' . $color . '">
							<td style = "border:1px solid; border-color: #E6E6E6"></td>
							<td style = "border:1px solid; border-color: #E6E6E6"> ' . $row['valid_from2'] . '</td>
							<td style = "border:1px solid; border-color: #E6E6E6"> ' . $row['valid_to2'] . '</td>
						</tr>';
				$rowNum++;
			}
			$message .= '</table>';
		}
		else{
			$message = '';
		}


		$response = [
			'title'		=> Dict::getModuleValue('ttwa-base', 'employee'),
			'message'	=> $message,
		];

		echo json_encode($response);
	}

	public function actionGetDuplicateAbsences()
	{
		$sql = "
				SELECT
					COUNT(ea.employee_contract_id),
					ea.employee_contract_id,
					CONCAT(e.last_name, ' ', e.first_name, ' ', e.emp_id ) AS fullname,
					ea.day,
					d.dict_value,
					st.state_type_id as st_id
				FROM employee_absence ea
				LEFT JOIN employee_contract ec ON
						ec.employee_contract_id = ea.employee_contract_id
					AND ec.status = " . $this->published . "
					AND ea.day BETWEEN ec.valid_from AND ec.valid_to
					AND ea.day BETWEEN ec.ec_valid_from AND ec.ec_valid_to
				LEFT JOIN employee e ON
						 e.employee_id = ec.employee_id
					AND e.status = " . $this->published . "
					AND ea.day BETWEEN e.valid_from AND e.valid_to
				LEFT JOIN state_type st ON
						st.state_type_id = ea.state_type_id
					AND st.status = " . $this->published . "
				LEFT JOIN dictionary d ON
						d.dict_id = st.name_dict_id
					AND d.valid = 1
					AND d.lang = '" . Dict::getLang() . "'
					AND d.module = 'ttwa-ahp-core'
				 WHERE ea.status = " . $this->published . "
					AND ec.row_id IS NOT NULL
					AND e.row_id IS NOT NULL
				 GROUP BY ea.employee_contract_id, ea.day, st_id
				 HAVING COUNT(ea.employee_contract_id) > 1
				 ORDER BY COUNT(ea.employee_contract_id) ASC
				";

		$res = dbFetchAll($sql);

		if (count($res) > 0)
		{
			$message = '<table cellpadding="0" cellspacing="0" style="border-collapse: collapse">
						<tr style = "background-color: #039BE5; color: #FEFEFF">
							<td><b> ' . Dict::getValue('fullname') . ' </b></td>
							<td><b> ' . Dict::getValue('day') . ' </b></td>
							<td><b> ' . Dict::getValue('absence') . ' </b></td>
						</tr>';

			$rowNum = 0;
			$color = '';
			foreach ($res as $row)
			{
				$color = '#F9F9FC';
				if ($rowNum % 2 == 0)
				{
					$color = '#FEFEFF';
				}
				$message .= '<tr style = "background-color: ' . $color . '">
							<td style = "border:1px solid; border-color: #E6E6E6">' . $row['fullname'] . '</td>
							<td style = "border:1px solid; border-color: #E6E6E6"> ' . $row['day'] . '</td>
							<td style = "border:1px solid; border-color: #E6E6E6"> ' . $row['dict_value'] . '</td>
						</tr>';
				$rowNum++;
			}
			$message .= '</table>';
		}
		else
		{
			$message = Dict::getValue('duplicatedAbsenceRows',
								['attribute' => count($res), 'absence' => Dict::getModuleValue('ttwa-base', 'absence')]);
		}


		$response = [
			'title'		=> Dict::getModuleValue('ttwa-base', 'absence'),
			'message'	=> $message,
		];

		echo json_encode($response);
	}


	public function actionDeleteDuplicatedAbsence()
	{
		$updateSQL = "
			UPDATE employee_absence ea1
			LEFT JOIN employee_absence ea2 ON
					ea1.employee_contract_id = ea2.employee_contract_id
				AND ea1.day = ea2.day
				AND ea2.status = " . $this->published . "
				AND ea1.state_type_id = ea2.state_type_id
			SET ea1.status = " . $this->invalid . ",
				ea1.modified_by = 'duplicatedAbsences',
				ea1.modified_on = NOW()
			WHERE ea1.status = " . $this->published . "
			   AND ea1.row_id > ea2.row_id";

		$update = dbExecute($updateSQL);

		$response = [
			'title'		=> Dict::getModuleValue('ttwa-base', 'absence'),
			'message'	=> Dict::getValue('deletedAbsenceRows',
								['attribute' => $update, 'absence' => Dict::getModuleValue('ttwa-base', 'absence')]),
		];

		echo json_encode($response);
	}

	public function actionTableSizeList()
	{
		$res = $this->getTableSizes();

		$message = '<table cellpadding="0" cellspacing="0" style="border-collapse: collapse">
						<tr style = "background-color: #039BE5; color: #FEFEFF">
							<td><b> ' . Dict::getValue('table_name') . ' </b></td>
							<td><b> ' . Dict::getValue('size') . ' </b></td>
						</tr>';
		$rowNum = 0;
		$color = '';
		foreach ($res as $row)
		{
			$color = '#F9F9FC';
			if ($rowNum % 2 == 0)
			{
				$color = '#FEFEFF';
			}
			$message .= '<tr style = "background-color: ' . $color . '">
							<td style = "border:1px solid; border-color: #E6E6E6">' . $row['table_name'] . '</td>
							<td style = "border:1px solid; border-color: #E6E6E6"> ' . $row['sizeInMB'] . '</td>
						</tr>';
			$rowNum++;
		}
		$message .= '</table>';
		$response = [
			'title'		=> 'Tábla méret lista',
			'message'	=> $message,
		];

		echo json_encode($response);
	}

	private function getTableSizes()
	{
		$connectionString = explode(';', dbConnectionString());
		$database = explode('=', $connectionString[1]);
		$dbName = $database[1];

		$SQL = "
			SELECT
				table_name,
				round(((data_length + index_length) / 1024 / 1024), 2) AS sizeInMB
			FROM information_schema.TABLES
			WHERE table_schema = '" . $dbName . "'
			ORDER BY (data_length + index_length) DESC
			";

		return dbFetch($SQL);
	}

}
?>