<?php

class PublicholidayController extends Grid2Controller
{
	public $layout = '//layouts/main';

	public function __construct(){
		parent::__construct("publicholiday");
	}

	protected function G2BInit() {

		$this->LAGridDB->setModelName("PublicHoliday");

		parent::setControllerPageTitleId("page_title_public_holiday");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);

		$this->LAGridRights->overrideInitRights("export_xls",		false);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);
		$this->LAGridRights->overrideInitRights("export_pdf_node",	true);

		$ph = new PublicHoliday;
		$c = new CDbCriteria();
		$c->condition = "
			(SUBSTRING(IF('{holidaydate}' = '', NOW(), '{holidaydate}'), 1, 4) = SUBSTRING(`holidaydate`, 1, 4))
		";
		$c->order = "`holidaydate` ASC";
		$this->LAGridDB->setModelSelection($ph, $c);

		parent::setExportFileName(Dict::getValue("export_file_public_holiday_management"));

		parent::G2BInit();
	}

	public function search() {

		return array(
			'holidaydate'	=> array('col_type'=>'ed', 'dPicker'=>true, 'width'=>'*', 'label_text'=>Dict::getValue("valid_from"), 'default_value'=>date('Y-m-d')),
			'submit'		=> array('col_type'=>'searchBarReloadGrid', 'width'=>'*', 'label_text'=>''),
		);
	}

	public function columns()
	{
		$type_SQL = "SELECT
						DISTINCT `public_holiday_types`.`type_id` as id,
						REPLACE(`dictionary`.`dict_value`, '({date})', '') as value
					FROM `public_holiday_types`
					LEFT JOIN `dictionary` ON
							`public_holiday_types`.`type_dict_name` = `dictionary`.`dict_id`
					WHERE
							`dictionary`.`lang` = '" . Dict::getLang() . "'
						AND `public_holiday_types`.`type_id` IN ('3','5','D')
					ORDER BY `public_holiday_types`.`type_id`";

		$ndi_SQL = "SELECT
						DISTINCT `public_holiday`.`name_dict_id` as id,
						REPLACE(`dictionary`.`dict_value`, '({date})', '') as value
					FROM `public_holiday`
					LEFT OUTER JOIN `dictionary` ON
						`public_holiday`.`name_dict_id` = `dictionary`.`dict_id`
					WHERE
							`dictionary`.`lang` = '" . Dict::getLang() . "'
					ORDER BY `dictionary`.`dict_value`";

		$country_SQL = "SELECT DISTINCT country as id, country as value FROM public_holiday";

		return array(
			'holidaydate'				=> array('export'=> true , 'col_type'=>'ed', 'dPicker'=>true, 'width'=>'250'),
			'type'						=> array(
												'export'	=> true ,
												'col_type'	=> 'combo',
												'options'	=> array(
													'mode'		=> Grid2Controller::G2BC_QUERY_MODE_SQL,
													'sql'		=> $type_SQL),
													'array'	=> array(array("id"=>"","value"=>"")
												),
							 				 	'width'=>'250'
											  ),
			'country'					=> array(
												'export'	=> true ,
												'col_type'	=>'combo',
												'options'	=> array(
													'mode'		=> Grid2Controller::G2BC_QUERY_MODE_SQL,
													'sql'		=> $country_SQL),
													'array'		=> array(array("id"=>"","value"=>"")
												),
												'width'			=>'80'
											),
			'name_dict_id'				=> array(
												'export'	=> true , 'col_type'=>'combo',
												'options'	=> array(
													'mode'		=> Grid2Controller::G2BC_QUERY_MODE_SQL,
													'sql'		=> $ndi_SQL,'comboId'=>'name_dict_id','comboValue'=>'dict_value'),
													'array'	=> array(array("id"=>"","value"=>"")
												),
												'width'=>'250'
											),
			'name'						=> array('export'=> true , 'col_type'=>'ed', 'width'=>'250'),
			'chdate'					=> array('export'=> true , 'col_type'=>'ed', 'dPicker'=>true, 'width'=>'250'),
		);
	}

	protected function dropdownModify(&$res_sql) {
		for ($i = 0; $i < count($res_sql); $i++) {
			$res_sql[$i]["value"] = str_replace('({date})', '', $res_sql[$i]["value"]);
		}
	}

	/**
	 * Új munkaszüneti nap hozzáadása az adatbázishoz. Ha a megadott napra, a megadott országban már
	 * létezik egy munkaszüneti nap (és nem annak módosítása a szándék), akkor hibaüzenetet küld.
	 * @param mixed $data
	 * @param mixed $modelName
	 * @param mixed $pk
	 * @param mixed $vOnly
	 * @param mixed $ret
	 * @param mixed $contentId
	 * @return void
	 */
	public function actionSave($data = [], $modelName = 'PublicHoliday', $pk = 'row_id', $vOnly = false, $ret = false, $contentId = null): void
	{
		$this->layout			= "//layouts/ajax";
		$this->G2BInit();
		$generateFrom			= requestParam('generateFrom');
		$data					= empty($data) ? requestParam('dialogInput_'.$generateFrom) : $data;
		$data['holidaydate']	= (isset($data['holidaydate']) && $data['holidaydate'] != '') ? $data['holidaydate'] : date('Y-m-d');
		$data['name']			= (isset($data['name']) && $data['name'] != '') ? $data['name'] : null;
		$data['chdate']			= (isset($data['chdate']) && $data['chdate'] != '') ? $data['chdate'] : null;
		$data['company_id']		= (isset($data['company_id']) && $data['company_id'] != '') ? $data['company_id'] : 'ALL';
        
        if (empty($data['type'] || empty($data['country'])) || empty($data['name_dict_id'])) {
            echo json_encode([
                'status' => 0,
                'title' => Dict::getValue("error"),
                'error' => Dict::getValue('error_adding_holiday_failed')
            ]);
            return;
        }
        
        $year = substr($data['holidaydate'], 0, 4);
        $isDateAlreadyHolidayInCountry	= PublicHoliday::isDateAlreadyHolidayInCountry($data['holidaydate'], $data['country']);
        $isHolidayAlreadyExistsInCountry= PublicHoliday::isHolidayAlreadyExistsInCountry($data['name_dict_id'], $year, $data['country']);
        $isHolidayUniqueInCountry		= in_array($data['type'],['1','2','3','4']);
        $isDateAlreadyHolidayErrorMessage = Dict::getModuleValue('ttwa-ahp','error_is_date_already_holiday', ['date' => $data['holidaydate']]);
        $isHolidayAlreadyExistsErrorMessage = Dict::getModuleValue('ttwa-ahp','error_is_holiday_already_exists', ['name' => Dict::getValue($data['name_dict_id']), 'year' => $year]);
        $error_message = '';
        
        if (!empty($data[$pk])) {
            $model = $modelName::model()->findByPk($data[$pk]);
            $model->modified_by	= userID();
            $model->modified_on	= date("Y-m-d H:i:s");
            
            if ($isDateAlreadyHolidayInCountry && $data['holidaydate'] !== $model->holidaydate) {
                $error_message = $isDateAlreadyHolidayErrorMessage;
            }
            
            if ($isHolidayUniqueInCountry && $isHolidayAlreadyExistsInCountry && $data['name_dict_id'] !== $model->name_dict_id) {
                $error_message = $isHolidayAlreadyExistsErrorMessage; 
            }
        } else {
            $model 					= new PublicHoliday();
            $model->created_by		= userID();
            $model->created_on		= date("Y-m-d H:i:s");

            if ($isDateAlreadyHolidayInCountry) {
                $error_message = $isDateAlreadyHolidayErrorMessage;
            }

            if ($isHolidayUniqueInCountry && $isHolidayAlreadyExistsInCountry) {
                $error_message = $isHolidayAlreadyExistsErrorMessage;
            }
        }

        if (!empty($error_message)) {
            echo json_encode([
                'status' => 0,
                'title' => Dict::getValue("error"),
                'error' => $error_message
            ]);
            return;
        }

		if (!is_null($model)) {

			$model->holidaydate		= $data['holidaydate'];
			$model->country			= $data['country'];
			$model->name_dict_id	= $data['name_dict_id'];
			$model->name			= $data['name'];
			$model->type			= $data['type'];
			$model->chdate			= $data['chdate'];
			$model->company_id		= $data['company_id'];
			$model->status			= Status::PUBLISHED;

			if ($model->save()) {
				$status = ['status' => 1];
			} else {
				$status = [
					'status'	=> 0,
					'error'		=> Dict::getValue('error_adding_holiday_failed')
				];
			}

		}

		echo json_encode($status);
	}

}