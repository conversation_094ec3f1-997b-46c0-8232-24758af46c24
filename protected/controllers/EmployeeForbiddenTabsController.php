<?php

'yii2-only`;

	namespace app\controllers\uploaders;
	use EmployeeControllerWizards;
	use app\components\App;
	use app\components\Grid2\Grid2Controller;
	use app\models\AppSettings;
	use Yang;

`/yii2-only';


class EmployeeForbiddenTabsController extends Grid2Controller
{
	use EmployeeControllerWizards;
	
	private $tabs = [];
	
	public function __construct()
	{
		parent::__construct("employeeForbiddenTabs");

		parent::enableLAGrid();
	}

	protected function G2BInit()
	{
		parent::setControllerPageTitleId("page_title_employee_forbidden_tabs");

		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("add",				true);
		$this->LAGridRights->overrideInitRights("delete",			true);

		$this->LAGridDB->enableArrMode();
		
		$this->tabs = array_column($this->wizards()['dhtmlxGrid'], 'contentTitle', 'contentId');
		$this->LAGridDB->setPrimaryKey('tab_id');
		
		parent::G2BInit();
	}

	protected function dataArray($gridID, $filter)
	{
		$result = [];

		if (isset($_SESSION["tiptime"]["settings"]["employeeForbiddenTabs"])) {
                    unset($_SESSION["tiptime"]["settings"]["employeeForbiddenTabs"]);
		}
                
		$setting_value = App::getSetting("employeeForbiddenTabs");
		
		if ($setting_value !== "") {
			$employeeForbiddenTabs = explode(",", $setting_value);

			$i = 0;
			foreach ($employeeForbiddenTabs as $tab) {
				$result[$i]['tab_id'] = $tab;
				$result[$i]['tab_name'] = $this->tabs[$tab];
				$i++;
			}
		}
                
		return $result;
	}

	public function columns()
	{
		$tabs = [];
		foreach ($this->tabs as $tabId => $tabName){
			$tabs[] = ['id' => $tabId, 'value' => $tabName];
		}

		$columns = [
			'tab_id'		=> [
				'grid' 			=> false,
				'col_type'		=> 'combo',
				'options'		=>	[
										'mode'					=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
										'array'					=> $tabs,
									],
				'width' => 150,
			],
			'tab_name'			=> ['report_width' => 20, 'col_type'=>'ed', 'add' => false, 'edit' => false, 'width' => 150],
		];

		return $columns;
	}

	public function attributeLabels()
	{
		return [
				"tab_id" => "Tab azonosító",
				"tab_name" => "Tab neve",
				];
	}

	public function actionSave($data = array(), $modelName = null, $pk = null, $vOnly = false, $ret = false, $contentId = NULL)
	{
		$this->layout = "//layouts/ajax";
		$this->G2BInit();

		$generateFrom = requestParam('generateFrom');
		$data = requestParam('dialogInput_'.$generateFrom);
               
		$setting_value = App::getSetting("employeeForbiddenTabs");
		$filter = "`setting_id` = 'employeeForbiddenTabs'";
		
		if ($setting_value !== "") {
			$employeeForbiddenTabs = explode(",", $setting_value);
			if (!in_array($data["tab_id"], $employeeForbiddenTabs)) {
				$employeeForbiddenTabs[] = $data["tab_id"];
				AppSettings::model()->updateAll(['setting_value' => implode(",", $employeeForbiddenTabs)], $filter);
			}
		} else {
			AppSettings::model()->updateAll(['setting_value' => $data["tab_id"]], $filter);
		}

		$status = [
			'status'	=> 1,
		];

		echo json_encode($status);
	}

	public function actionDelete($modelName = null, $hasRight = false)
	{
		$this->layout = "//layouts/ajax";

		$this->G2BInit();

		$id = requestParam('ids');


		$employeeForbiddenTabs = explode(",",App::getSetting("employeeForbiddenTabs"));

		foreach ($employeeForbiddenTabs as $key => &$tab) {
			if ($tab === $id) {
				unset($employeeForbiddenTabs[$key]);
			}
		}

		$filter = "`setting_id` = 'employeeForbiddenTabs'";
				
		if (count($employeeForbiddenTabs) > 0) {
			AppSettings::model()->updateAll(['setting_value' => implode(",", $employeeForbiddenTabs)], $filter);
		} else {
			AppSettings::model()->updateAll(['setting_value' => ""], $filter);
		}
	}
}