<?php

ini_set('max_execution_time', 3600);
ini_set('memory_limit', '2048M');

const DATE_PATTERN = '/^[0-9]{4}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/';

class ExtendedAbsenceExpenditureController extends Grid2Controller
{
    private ?string $valid_from = NULL;
    private ?string $valid_to = NULL;
    private ?string $acceptance = NULL; // szabadság elfogadás kell-e
    private ?string $simulation = NULL; // szimulációban legyen-e futtatva
    private string $error_log = ''; // hibák
    private int $table_row = 0; // táblázat sora
    private array $result_array = []; // eredmény tömb amit a grid-nek adunk át
    private string $abs_status = '1';
    private array $employee_data = [];
    private bool $public_holiday = FALSE;
    public ?string $action = NULL;
    private ?string $absence = NULL;
    private array $workgroup = [];
    private array $unit = [];
    private array $cost = [];
    private array $employeeContract = [];
    private int $publishedStatus;
    private ?string $defaultEnd;
    private int $draftStatus;
    private int $deleted;

    public function __construct($id, $module = null)
    {
        parent::__construct('extendedAbsenceExpenditure');
        $this->publishedStatus = Status::PUBLISHED;
        $this->draftStatus = Status::DRAFT;
        $this->deleted = Status::DELETED;
        $this->defaultEnd = App::getSetting('defaultEnd');
    }

    protected function G2BInit(): void
    {
        parent::setControllerPageTitleId('page_title_extended_absence_expenditure');
        parent::setGridProperty('splitColumnEnabled', true, 'dhtmlxGrid');

        $this->LAGridRights->overrideInitRights('search', true);
        $this->LAGridRights->overrideInitRights('init_open_search', true);
        $this->LAGridRights->overrideInitRights('search_header', true);
        $this->LAGridRights->overrideInitRights('column_move', true);
        $this->LAGridRights->overrideInitRights('paging', true);
        $this->LAGridRights->overrideInitRights('export_xlsx', true);

        $this->LAGridDB->enableSQLMode();
        parent::setExportFileName(Dict::getValue('page_title_extended_absence_expenditure'));
        parent::G2BInit();
    }


    /**
     * @throws Exception
     */
    protected function setSQL($filter, $gridID, $forReport = false)
    {
        $this->valid_from = $filter['valid_from'];
        $this->valid_to = $filter['valid_to'];
        $this->acceptance = $filter['acceptance'];
        $this->simulation = $filter['simulation'];
        $this->action = $filter['abs_action'];
        $this->absence = $filter['absence'];
        $this->workgroup = $filter['workgroup'];
        $this->unit = $filter['unit'];
        $this->cost = $filter['cost'];
        $this->employeeContract = $filter['employee_contract'];

        $this->abs_status = $this->acceptance == 'y' ? '2' : '1';

        if (isset($this->valid_from) && isset($this->valid_to)) {
            $this->validationPost();
            if ($this->error_log === 'ok') {
                $employees = $this->getEmployees();
                // szabadság kiadás
                if ($this->action == 'abs_in') {
                    $this->public_holiday = $this->checkPublicHoliday();
                    if (is_array($employees) && count($employees) > 0) {
                        $this->processEmployee($employees, $filter);
                    }
                }

                //szabadság visszavétel
                if ($this->action == 'abs_revert') {
                    if (count($employees)) {
                        $this->processEmployeeRevertAbsence($employees, $filter);
                    }
                }
            } else {
                $this->result_array[] = [
                    'table_row' => '',
                    'employee_name' => '',
                    'emp_id' => '',
                    'company_name' => '',
                    'msg' => '<span style="color:red; font-size:16px;"><b>' . $this->error_log . '</b></span>'
                ];
            }

            $this->create_temp_table();
            $this->multiInsert($this->result_array);
            $SQL = 'SELECT * FROM temp_extended_absence_expenditure ';
            $this->LAGridDB->setSQLSelection($SQL, 'table_row');

            return $SQL;
        }
        else {
            exit;
        }
    }


    /**
     * Grid oszlopok
     * @return array
     */
    public function columns(): array
    {
        $columns = [
            'employee_name' => ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '200'],
            'emp_id' => ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150'],
            'company_name' => ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '200'],
            'msg' => ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '500']
        ];

        return $this->columnRights($columns);
    }

    /**
     * Nyelvi elemek
     * @return array
     */
    public function attributeLabels(): array
    {
        return [
            'emp_id' => Dict::getValue('emp_id'),
            'employee_name' => Dict::getValue('employee_name'),
            'company_name' => Dict::getValue('company'),
            'msg' => Dict::getValue('operation')
        ];
    }

    /**
     * Kereső mező beállítása
     * @return array
     */
    public function search(): array
    {
        $roleGroupId = $_SESSION['tiptime']['rolegroup'];
        $onChangeFilters = ['acceptance', 'simulation', 'abs_action', 'absence', 'workgroup', 'unit', 'cost', 'employee_contract'];

        $search = [
            'valid_from' => [
                'col_type' => 'ed',
                'dPicker' => TRUE,
                'width' => '*',
                'label_text' => Dict::getValue('valid_from'),
                'default_value' => date('Y-m-d'),
                'onchange' => $onChangeFilters
            ],
            'valid_to' => [
                'col_type' => 'ed',
                'dPicker' => TRUE,
                'width' => '*',
                'label_text' => Dict::getValue('valid_to'),
                'default_value' => date('Y-m-d'),
                'onchange' => $onChangeFilters
            ],
            'acceptance' => [
                'col_type' => 'combo',
                'multiple' => false,
                'options' => [
                    'mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY,
                    'array' => [
                        ['id' => '0', 'value' => Dict::getValue('please_select')],
                        ['id' => 'y', 'value' => Dict::getValue('yes')],
                        ['id' => 'n', 'value' => Dict::getValue('no') . ' (' . Dict::getValue('without_approval_email') . ')'],
                        ['id' => 'ne', 'value' => Dict::getValue('no') . ' (' . Dict::getValue('send_approval_email') . ')']
                    ]
                ],
                'label_text' => Dict::getValue('accepting_holidays'),
                'default_value' => '0'
            ],
            'simulation' => [
                'col_type' => 'combo',
                'multiple' => false,
                'options' => [
                    'mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY,
                    'array' => [
                        ['id' => '0', 'value' => Dict::getValue('please_select')],
                        ['id' => 'y', 'value' => Dict::getValue('yes')],
                        ['id' => 'n', 'value' => Dict::getValue('no')]
                    ]
                ],
                'label_text' => Dict::getValue('run_simulation'),
                'default_value' => '0'
            ],
            'abs_action' => [
                'col_type' => 'combo',
                'multiple' => false,
                'options' => [
                    'mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY,
                    'array' => [
                        ['id' => 'abs_in', 'value' => Dict::getModuleValue('ttwa-base', 'absence') . ' ' . strtolower(Dict::getValue('release'))],
                        ['id' => 'abs_revert', 'value' => Dict::getModuleValue('ttwa-base', 'absence') . ' ' . strtolower(Dict::getValue('delete'))]
                    ]
                ],
                'label_text' => Dict::getValue('absence_operation'),
                'default_value' => '0'
            ],
            'absence' => [
                'col_type' => 'combo',
                'multiple' => false,
                'options' => [
                    'mode' => Grid2Controller::G2BC_QUERY_MODE_SQL,
                    'sql' => $this->getStateTypeByRightsSQL($roleGroupId)
                ],
                'label_text' => Dict::getModuleValue('ttwa-base', 'absence'),
                'default_value' => '0'
            ],
        ];

        $search += $this->getPreDefinedSearchFromDb();

        unset($search['submit']);

        $search['submit'] = ['col_type' => 'searchBarReinitGrid', 'width' => '*', 'label_text' => ''];

        return $search;
    }

    private function getStateTypeByRightsSQL($roleGroupId = ''): string
    {
        $SQL = "SELECT
					ar.`rolegroup_id`,
					st.`state_type_id` AS id,
					d.`dict_value` AS value
				FROM `state_type_right` str
				LEFT JOIN `auth_rolegroup` ar ON 1
					AND ar.`rolegroup_id` = str.`rolegroup_id`
					AND ar.`visibility` = 1
				LEFT JOIN `state_type` st ON 1
					AND st.`state_type_id` = str.`state_type_id`
					AND st.`status` = '$this->publishedStatus'
					AND st.`in_use` = 1
				LEFT JOIN `dictionary` d ON 1
					AND d.`dict_id` = st.`name_dict_id`
					AND d.`valid` = 1
					AND d.`lang` = '" . Dict::getLang() . "'
					AND d.`module` = 'ttwa-ahp-core'
				WHERE 1
					AND ar.`row_id` IS NOT NULL
					AND st.`row_id` IS NOT NULL ";

        if ($roleGroupId !== '') {
            $SQL .= "AND ar.`rolegroup_id` = '$roleGroupId' ";
        }

        $SQL .= 'ORDER BY value';

        return $SQL;
    }

    /**
     * Bejövő adatok validálása
     * @return string
     */
    public function validationPost(): string
    {
        if ((!isset($this->valid_from)) || (strlen($this->valid_from) != 10) || (!preg_match(DATE_PATTERN, $this->valid_from))) {
            $this->error_log = Dict::getModuleValue('ttwa-base', 'wrong_date');
            return '';
        }

        // szabadság elfogadás validáció
        if ($this->acceptance == '0') {
            $this->error_log = Dict::getModuleValue('ttwa-base', 'should_absences_automatically_accepted');
            return '';
        }

        // szimuláció validáció
        if ($this->simulation == '0') {
            $this->error_log = Dict::getModuleValue('ttwa-base', 'should_run_in_simulation');
            return '';
        }

        $this->error_log = 'ok';
        return '';
    }


    /**
     * Lekért Dolgozók feldolgozása
     * @param array $employees
     * @param array $filter
     * @throws Exception
     */
    public function processEmployee(array $employees, array $filter): void
    {
        foreach ($employees as $employee) {
            $this->employee_data = $employee;
            if (App::getSetting('absenceexpediture_only_workday') == 1) {
                // ha nincs munkanapja arra a napra
                if ($this->checkWorkday() === FALSE) {
                    $this->writeResultArray($filter['absence'], $this->abs_status);
                    continue;
                }
            } elseif ((is_array($this->public_holiday)) && (count($this->public_holiday) > 0)) {
                    $this->writeResultArray('<b>' . Dict::getModuleValue('ttwa-base', 'public_holiday') . '</b>', $this->abs_status);
                    continue;
            }

            // van-e már az adott napra szabadsága
            if ($this->checkABS() === TRUE) {
                continue;
            }

            $employeAbs = $this->checkFreeAbsence();
            if (empty($employeAbs)) {
                $this->writeResultArray($filter['absence'], $this->abs_status);
                continue;
            }

            $contractId = $this->employee_data['employee_contract_id'];
            $year = explode('-', $this->valid_from)[0];
            $frameBalance = $this->getFrameBalanceByContractIdAndYear($contractId, $year);

            if ($frameBalance['available'] <= 0) {
                $this->writeResultArray($filter['absence'], $this->abs_status);
                continue;
            }

            // szabadság mentése
            $employee_absence_id = $this->insertAbsence();
            if ($employee_absence_id !== FALSE) {
                // jóváhagyó email küldése
                if ($filter['acceptance'] == 'ne') {
                    $this->sendApprovalEmail($employee_absence_id);
                }
            }
        }
    }


    /**
     * Szabadság visszavétel dolgozóknak
     *
     * @param array $employees
     * @param array $filter
     */
    public function processEmployeeRevertAbsence(array $employees, array $filter): void
    {
        foreach ($employees as $employee) {
            $this->employee_data = $employee;
            $abs = $this->getEmployeeAbsByDate(TRUE);
            if (is_array($abs)) {
                if ((isset($this->simulation)) && ($this->simulation === 'y')) {
                    $this->writeResultArray($filter['absence'], $this->abs_status);
                } else {
                    $revert = $this->revertAbsence($abs, $filter);
                    if ($revert === TRUE) {
                        $this->writeResultArray($filter['absence'], $this->abs_status);
                    }
                }
            } else {
                $this->writeResultArray($filter['absence'], $this->abs_status);
            }
        }
    }


    /**
     * Szabadság visszavétel update SQL
     *
     * @param array $abs
     * @return bool
     */
    public function revertAbsence(array $abs): bool
    {
        $update = '';

        for ($i = 0; $i < count($abs); $i++)
        {
            $update .= 'UPDATE employee_absence SET'
                . '	status = ' . $this->deleted . ','
                . "	modified_by = 'extendedAbsenceExpenditureRevert',"
                . ' modified_on = NOW()'
                . " WHERE row_id = '" . $abs[$i]['row_id'] . "'; ";
        }

        if ($update != '') {
            try {
                $this->abs_status = $this->deleted;
                dbExecute($update);
                return true;
            } catch (Exception $e) {
                Yang::log($e->getMessage(), 'log', 'system');
                $this->writeResultArray($abs['state_type_id'], $this->abs_status);
                return false;
            }
        }
        return false;
    }


    /**
     * Jóváhagyó email küldése
     * @param string $employee_absence_id
     */
    public function sendApprovalEmail(string $employee_absence_id): void
    {
        if ((isset($this->simulation)) && ($this->simulation === 'y')) {
            $this->writeResultArray(Dict::getModuleValue('ttwa-base', 'email_sending_ok'), $this->abs_status);
        } else {
            $employee_absence_row_ids = $this->getEmployeeAbsenceRowId($employee_absence_id);
            $messaging = new Messaging('absenceapproval', 'EmployeeAbsence', $employee_absence_id, ['employee_absence_row_ids' => $employee_absence_row_ids,]);
            $senderUserID = userID();
            $recipientUsers = [Employee::getEmployeeUserIDByEcID($this->employee_data['employee_contract_id'])];
            $absenceData[] = AHPAbsenceFunctions::getAbsenceDataByID($employee_absence_id);
            foreach ($absenceData as $absence) {
                $generalContentParams = $absence;
            }
            $event = 'req_add';
            $process_id = 'absenceApprover';
            $message = '';
            $messaging->sendMessage($senderUserID, $recipientUsers, $generalContentParams, $event, $process_id, $message);
            $this->writeResultArray(Dict::getModuleValue('ttwa-base', 'approving_email_sent'), $this->abs_status);
        }
    }


    /**
     * Szabadság adatok lekérdezése $employee_absence_id alapján
     */
    public function getEmployeeAbsenceRowId(string $employee_absence_id)
    {
        $ret = FALSE;
        $select_sql = "SELECT * FROM employee_absence ea
						WHERE ea.`employee_absence_id` = '" . $employee_absence_id . "'
						AND ea.`status` = 1
						AND ea.`day` BETWEEN '$this->valid_from' AND '$this->valid_to'
						AND ea.`employee_contract_id` = '" . $this->employee_data['employee_contract_id'] . "'
						;";

        $result = $this->getSqlResult($select_sql, TRUE);

        if ((is_array($result)) && count($result) > 0) {
            $ret = $result['row_id'];
        }

        return $ret;
    }


    /**
     * Minden olyan dolgozó lekérdezése akinek ki kell adni szabadnapot
     */
    public function getEmployees()
    {
        $art = new ApproverRelatedGroup();
        $gargSQL = $art->getApproverReleatedGroupSQLWithoutCalc('Employee', 'absenceApprover', false, 'AND', 'CurrentDate', $this->getControllerID());
        $and = '';

        if ((isset($this->workgroup)) && ($this->workgroup['0'] != 'ALL')) {
            $and .= " AND `workgroup`.`workgroup_id` IN ('" . implode("','", $this->workgroup) . "') ";
        }

        if (isset($this->unit) && ($this->unit['0'] !== 'ALL')) {
            $and .= " AND `unit`.`unit_id` IN ('" . implode("','", $this->unit) . "') ";
        }

        if (isset($this->cost) && ($this->cost['0'] !== 'ALL')) {
            $and .= " AND `employee_cost`.`cost_id` IN ('" . implode("','", $this->cost) . "') ";
        }

        if (isset($this->employeeContract) && ($this->employeeContract['0'] !== 'ALL')) {
            $and .= " AND `employee_contract`.`employee_contract_id` IN ('" . implode("','", $this->employeeContract) . "') ";
        }

        $select_sql = "
						SELECT
							`employee`.`employee_id`,
							`employee`.`emp_id`,
							`employee_contract`.`employee_contract_id`,
							CONCAT(`employee`.`last_name`, ' ', `employee`.`first_name`) as full_name,
							`employee`.`company_id`,
							`company`.`company_name`,
							`unit`.`unit_id`,
							`workgroup`.`workgroup_id`,
							`employee_cost`.`cost_id`
						FROM `employee`
						LEFT JOIN `employee_contract` ON
								`employee_contract`.`employee_id` = `employee`.`employee_id`
							AND `employee_contract`.`status` = {$this->publishedStatus}
							AND '$this->valid_from' <= `employee_contract`.`valid_to` AND '$this->valid_to' >= IFNULL(`employee_contract`.`valid_from`, '$this->defaultEnd')
                            AND '$this->valid_from' <= `employee_contract`.`ec_valid_to` AND '$this->valid_to' >= IFNULL(`employee_contract`.`ec_valid_from`, '$this->defaultEnd')
						LEFT JOIN `employee_cost` ON
						        `employee_cost`.`employee_contract_id` = `employee_contract`.`employee_contract_id`
						    AND `employee_cost`.`status` = {$this->publishedStatus}
						    AND '$this->valid_from' <= `employee_cost`.`valid_to` AND '$this->valid_to' >= IFNULL(`employee_cost`.`valid_from`,'$this->defaultEnd')
						";

        if (EmployeeGroupConfig::isActiveGroup('company_id')) {
            $select_sql .= EmployeeGroup::getLeftJoinSQLWithoutCal('company_id');
        }
        if (EmployeeGroupConfig::isActiveGroup('workgroup_id')) {
            $select_sql .= EmployeeGroup::getLeftJoinSQLWithoutCal('workgroup_id');
        }
        if (EmployeeGroupConfig::isActiveGroup('unit_id')) {
            $select_sql .= EmployeeGroup::getLeftJoinSQLWithoutCal('unit_id');
        }
        if (EmployeeGroupConfig::isActiveGroup('company_org_group1_id')) {
            $select_sql .= EmployeeGroup::getLeftJoinSQLWithoutCal('company_org_group1_id');
        }
        if (EmployeeGroupConfig::isActiveGroup('company_org_group2_id')) {
            $select_sql .= EmployeeGroup::getLeftJoinSQLWithoutCal('company_org_group2_id');
        }
        if (EmployeeGroupConfig::isActiveGroup('company_org_group3_id')) {
            $select_sql .= EmployeeGroup::getLeftJoinSQLWithoutCal('company_org_group3_id');
        }

        $select_sql .= "
						LEFT JOIN `company` ON
								`company`.`company_id` = `employee`.company_id
							AND `company`.`status` = {$this->publishedStatus}
							AND '$this->valid_from' <= `company`.`valid_to` AND '$this->valid_to' >= IFNULL(`company`.`valid_from`,'$this->defaultEnd')
						LEFT JOIN `workgroup` ON
								`workgroup`.`workgroup_id` = " . EmployeeGroup::getActiveGroupSQL('workgroup_id', 'employee_contract') . "
							AND `workgroup`.`status` = {$this->publishedStatus}
							AND '$this->valid_from' <= `workgroup`.`valid_to` AND '$this->valid_to' >= IFNULL(`workgroup`.`valid_from`,'$this->defaultEnd')
						LEFT JOIN `unit` ON
								`unit`.`unit_id` = " . EmployeeGroup::getActiveGroupSQL('unit_id', 'employee') . "
							AND `unit`.`status` = {$this->publishedStatus}
							AND '$this->valid_from' <= `unit`.`valid_to` AND '$this->valid_to' >= IFNULL(`unit`.`valid_from`,'$this->defaultEnd')
						LEFT JOIN `company_org_group1` ON
								`company_org_group1`.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL('company_org_group1_id', 'employee') . "
							AND	`company_org_group1`.`status` = {$this->publishedStatus}
							AND '$this->valid_from' <= `company_org_group1`.`valid_to` AND '$this->valid_to' >= IFNULL(`company_org_group1`.`valid_from`,'$this->defaultEnd')
						LEFT JOIN `company_org_group2` ON
								`company_org_group2`.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL('company_org_group2_id', 'employee') . "
							AND `company_org_group2`.`status` = {$this->publishedStatus}
							AND '$this->valid_from' <= `company_org_group2`.`valid_to` AND '$this->valid_to' >= IFNULL(`company_org_group2`.`valid_from`,'$this->defaultEnd')
						LEFT JOIN `company_org_group3` ON
								`company_org_group3`.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL('company_org_group3_id', 'employee') . "
							AND `company_org_group3`.`status` = {$this->publishedStatus}
							AND '$this->valid_from' <= `company_org_group3`.`valid_to` AND '$this->valid_to' >= IFNULL(`company_org_group3`.`valid_from`,'$this->defaultEnd')
		";

        $select_sql .= $gargSQL['join'];

        $select_sql .= "
						WHERE
								`employee`.`status` = {$this->publishedStatus}
							AND '$this->valid_from' <= `employee`.`valid_to` AND '$this->valid_to' >= IFNULL(`employee`.`valid_from`,'$this->defaultEnd')
							AND `employee_contract`.`row_id` IS NOT NULL
							" . $and . '
						';

        $select_sql .= $gargSQL['where'];
        $select_sql .= ' GROUP BY emp_id ORDER BY full_name';
        return $this->getSqlResult($select_sql);
    }


    /**
     * Munkanap ellenőrzése
     * @return bool
     */
    public function checkWorkday(): bool
    {
        $wsu = $this->getWorkDayByWSU();
        $wsbe = (!$wsu) ? $this->getWorkDayByWSBE() : false;
        $wsbu = (!$wsu && !$wsbe) ? $this->getWorkDayByWSBU() : false;
        if ($wsu || $wsbe || $wsbu) {
            return true; // van munkanap
        }

        return false;
    }


    /**
     * Munkanap lekérdezése a work_schedule_by_employee táblából
     * @return bool
     */
    public function getWorkDayByWSBE(): bool
    {
        $wsbeSQL = "SELECT wsbe.`employee_contract_id`, wsbe.`day`, wsbe.`daytype_id`, d.`name`
						FROM work_schedule_by_employee wsbe
						LEFT JOIN daytype d ON d.`daytype_id` = wsbe.`daytype_id`
						WHERE wsbe.`employee_contract_id` = '" . $this->employee_data['employee_contract_id'] . "'
						AND wsbe.`status` = '" . $this->publishedStatus . "'
						AND d.`type_of_daytype` = 'WORKDAY'
						AND wsbe.`day` BETWEEN '$this->valid_from' AND '$this->valid_to' ";

        $result = $this->getSqlResult($wsbeSQL);

        return !!$result;
    }


    /**
     * Munkanap lekérdezése a work_schedule_by_unit táblából
     * @return bool
     */
    public function getWorkDayByWSBU(): bool
    {
        $wsbuSQL = "SELECT wsbu.`unit_id`, wsbu.`workgroup_id`, wsbu.`day`, wsbu.`daytype_id` , d.`name`
						FROM work_schedule_by_unit wsbu
						LEFT JOIN daytype d ON d.`daytype_id` = wsbu.`daytype_id`
						WHERE wsbu.`unit_id` = '" . $this->employee_data['unit_id'] . "'
						AND wsbu.`workgroup_id` = '" . $this->employee_data['workgroup_id'] . "'
						AND wsbu.`day` BETWEEN '$this->valid_from' AND '$this->valid_to'
						AND d.`type_of_daytype` = 'WORKDAY'
						AND wsbu.`status` = '" . $this->publishedStatus . "' ";

        $result = $this->getSqlResult($wsbuSQL);

        return !!$result;
    }


    /**
     * Munkanap lekérdezése a work_schedule_used táblából
     * @return bool
     */
    public function getWorkDayByWSU(): bool
    {
        $wsuSQL = "
					SELECT
						`row_id`
					FROM `work_schedule_used`
					WHERE
							`employee_contract_id` = '" . $this->employee_data['employee_contract_id'] . "'
						AND `status` = {$this->publishedStatus}
						AND `type_of_daytype` = 'WORKDAY'
						AND `day` BETWEEN '$this->valid_from' AND '$this->valid_to'
					";

        $result = $this->getSqlResult($wsuSQL);

        return !!$result;
    }


    /**
     * Elérhető szabadság lekérdezése
     */
    public function checkFreeAbsence()
    {
        $fullAbsenceSQLl = "SELECT SUM(eba.`quantity`) as quantity
								FROM employee_base_absence eba
								WHERE eba.`employee_contract_id` = '" . $this->employee_data['employee_contract_id'] . "'
								AND eba.`valid_from` >= '" . date('Y-01-01', strtotime($this->valid_from)) . "'
								AND eba.`valid_to` <= '" . date('Y-12-31', strtotime($this->valid_to)) . "'
								AND '$this->valid_from' <= eba.`valid_to` AND '$this->valid_to' >= eba.`valid_from`
								AND eba.`status` = '" . $this->publishedStatus . "'
								AND eba.`base_absence_type_id`  IN (
												SELECT base_absence_type_id
												FROM link_at_to_bat
												WHERE `state_type_id` = 'a272f564576d443e7832587126b070aa')
								";

        $result = $this->getSqlResult($fullAbsenceSQLl, TRUE);
        if ($result !== FALSE) {
            return $result['quantity'] ?? false;
        }

        return FALSE;
    }


    /**
     * Log abs status
     *
     */
    public function logEmployeeAbsStatus(): void
    {
        $this->table_row++;
        $this->result_array[] = [
            'table_row' => $this->table_row,
            'employee_name' => $this->employee_data['full_name'],
            'emp_id' => $this->employee_data['emp_id'],
            'company_name' => $this->employee_data['company_name'],
            'msg' => Dict::getModuleValue('ttwa-base', 'absence_already_approved')
        ];
    }

    public function insertAbsence()
    {
        $ret = FALSE;
        if ((isset($this->simulation)) && ($this->simulation === 'y')) {
            $ret = md5(time() . rand(10, 10000000) . uniqid());
            $this->writeResultArray($this->absence, $this->abs_status);
        } else {
            $dateFrom = new DateTime($this->valid_from);
            $dateTo = new DateTime($this->valid_to);
            $dateTo->modify('+1 day');

            $dateInterval = new DateInterval('P1D');
            $datePeriod = new DatePeriod($dateFrom, $dateInterval, $dateTo);

            $dates = [];
            $insertRows = [];
            
            foreach ($datePeriod as $date) {
                $dates[] = $date->format('Y-m-d');
            }
            
            for ($i = 0; $i < count($dates); $i++) {
                $employee_absence_id = md5($this->getControllerId() . $this->employee_data['employee_contract_id'] . $this->valid_from . $this->valid_to . $this->absence . date('YmdHis'). $dates[$i]);
                $insertRows[] = "('" . $employee_absence_id . "', '" . $this->employee_data['company_id'] . "', '" . $this->employee_data['employee_contract_id'] . "', '" . $this->employee_data['employee_id'] . "', '" . $dates[$i] . "', '" . $this->absence . "', '" . $this->publishedStatus ."', 'extendedAbsenceExpenditure', NOW())";
            }

            $this->insertSql($insertRows);

            if (empty($insertRows)) {
                Yang::log(Dict::getModuleValue('ttwa-base', 'error_in_issue_of_absence'), 'log', 'system');
            }
        }
        return $ret;
    }

    public function insertSql($rows): void
    {
        if (!empty($rows))
        {
            $insertSQL = '
                INSERT INTO employee_absence (employee_absence_id, company_id, employee_contract_id, employee_id, day, state_type_id, status, created_by, created_on) VALUES 
                ' . implode(', ', $rows);
            
            $finalInsert = rtrim($insertSQL, ',') . ';';
            
            dbExecute($finalInsert);

            $this->writeResultArray($this->absence, $this->abs_status);
        }
    }


    /**
     * Adottnapra van-e már szabadsága a dolgozónak
     * @return bool
     */
    public function checkABS(): bool
    {
        $abs_check = $this->getEmployeeAbsByDate();
        if ($abs_check === FALSE) {
            // nincs szabadság az adott napra
            return false;
        } else {
            // van szabadság az adott napra
            $this->logEmployeeAbsStatus();
            return true;
        }
    }

    /**
     * Adott időintervallumban van-e már a dolgozónak kiadott szabadsága
     */
    public function getEmployeeAbsByDate(bool $revert = FALSE)
    {
        $and = '';
        if ($revert === TRUE) {
            $and = " AND ea.created_by LIKE '%absenceExpenditure%' ";
        }
        $sql = "SELECT * FROM employee_absence ea
				WHERE ea.`employee_contract_id` = '" . $this->employee_data['employee_contract_id'] . "'
				AND ea.`day` BETWEEN '$this->valid_from' AND '$this->valid_to'
				AND ea.`status` IN ('$this->draftStatus', '$this->publishedStatus')
				$and;";

        return $this->getSqlResult($sql);
    }


    /**
     * Eredmény tömb írása
     *
     * @param string $msg
     * @param $abs_status
     */
    public function writeResultArray(string $msg, $abs_status): void
    {
        $this->table_row++;
        $this->result_array[] = [
            'table_row' => $this->table_row,
            'employee_name' => $this->employee_data['full_name'],
            'emp_id' => $this->employee_data['emp_id'],
            'company_name' => $this->employee_data['company_name'],
            'msg' => (isset($abs_status) && $abs_status == $this->publishedStatus) ? Dict::getModuleValue('ttwa-base', 'absence_issued') : ($abs_status == $this->deleted ? Dict::getValue('deleted_absence') : StateType::getStateTypeName($msg))
        ];
    }


    /**
     * Többsoros beszúrás
     *
     * @param array $data
     */
    public function multiInsert(array $data = []): void
    {
        dbMultiInsert('temp_extended_absence_expenditure', $data);
    }

    public function getSqlResult(string $sql, bool $one_row = FALSE)
    {
        $ret = FALSE;
        $result = '';
        try {
            if ($one_row === FALSE) {
                $result = dbFetchAll($sql);
            } else {
                $result = dbFetchRow($sql);
            }
        } catch (Exception $e) {
            Yang::log($e->getMessage(), 'log', 'system');
        }

        if ($result) {
            $ret = $result;
        }

        return $ret;
    }


    /**
     * temp tábla létrehozása (mivel ebből olvassuk ki a grid adatokat)
     */
    public function create_temp_table(): void
    {
        $sql = 'DROP TABLE IF EXISTS `temp_extended_absence_expenditure` ';
        dbExecute($sql);
        
        $sql = "CREATE TEMPORARY TABLE IF NOT EXISTS `temp_extended_absence_expenditure` (
                        `table_row` INT(11) NOT NULL AUTO_INCREMENT,
                        `employee_name` VARCHAR(150) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',
                        `emp_id` VARCHAR(150) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',
                        `company_name` VARCHAR(150) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',
                        `msg` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',

                        PRIMARY KEY (`table_row`)
                )
                COLLATE='utf8_unicode_ci'
                ENGINE=InnoDB;";

        dbExecute($sql);
    }


    public function checkPublicHoliday()
    {
        $sql = "SELECT ph.`holidaydate`, ph.`name`, ph.`type`, IF(ph.`type` IN ('1','2','3','4'), ph.`name`, '') AS paidholiday
				FROM public_holiday ph
				WHERE
				ph.`holidaydate` BETWEEN '$this->valid_from' AND '$this->valid_to'
				AND ph.`type` IN ('1','2','3','4')
				AND ph.`status` = '" . $this->publishedStatus . "'
				;";

        return $this->getSqlResult($sql, TRUE);
    }

    /**
     * Megmondja egy évre, hogy adott contract id-hez kapcsolódóan mekkora a szabadság keret, mennyi lett ebből elhasználva és hogy mennyi maradt még.
     *
     * @param $employeeContractId
     * @param $yearFilter
     * @return array
     */
    private function getFrameBalanceByContractIdAndYear($employeeContractId, $yearFilter): array
    {
        $settingAbsenceCalculationHour = App::getSetting('absence_calculation_hour');
        $settingAbsenceFloorRounding = App::getSetting('absence_floor_rounding');
        $stateTypeId = App::getSetting('getBaseAbsencesByStateType');
        $baseAbsences = AHPAbsenceFunctions::getBaseAbsencesByStateType($stateTypeId);
        $frame = AHPEmployeeFunctions::getEmployeeAbsenceFrameByEcID($employeeContractId, $yearFilter, $baseAbsences);
        $used = AHPEmployeeFunctions::getEmployeeAbsenceUsedByEcID($employeeContractId, $yearFilter, false, $baseAbsences);
        $dailyWorktime = AHPEmployeeFunctions::getEmployeeDailyWorktime($employeeContractId, true, $yearFilter);

        if ($settingAbsenceCalculationHour == '1' && $settingAbsenceCalculationHour === '0') {
            $frame = round(($frame / $dailyWorktime), 1);
            //ha órában számolja a szabadságot, rosszul számolja a visszakapott értéket, fv-ben dw daily_worktime helyett
            //$used = round( ($used / $dailyWorktime), 1);
        }

        $available = round(($frame - $used), 2);

        //szabadság lefelé kerekítés
        if ($settingAbsenceFloorRounding == '1') {
            $frame = floor($frame);
            $used = floor($used);
            $available = floor($available);
        }

        return compact('frame', 'used', 'available');
    }
}