<?php

'yii2-only`;

	namespace app\controllers;
	use Yii;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2\Grid2Controller;
	use app\components\Helpers\AnyCache;
	use app\models\ColumnTabs;
	use app\models\EmployeeAbsence;
	use app\models\EmployeeGroupConfig;
	use app\models\Status;
	use app\models\TabColumn;
	use app\models\TabColumnComboItem;
	use Yang;

`/yii2-only';


trait EmployeecontrolControllerColumns
{
	public $ecValidFrom;

	public function search()
	{
		return $this->getPreDefinedSearchFromDb("employeeManagement");
	}

	/**
	 * @param $column_name    string   - Az oszlop neve
	 * @param $withModelExist boolean - Csak akkor küldjön vissza értékeket ha a megadott oszlopnak van model értéke
	 * @return array |null - Vagy null vagy pedig a generált tömb értékeit
	 */
	public function getDialogColumn($column_name, $tabId, $withModelExist = true)
	{
		$result = null;
		$columns = $this->dialogColumns();
		$columns = $columns[$tabId];

		$fieldValue = $columns[$column_name];

		if ($withModelExist) {
			if (
				isset($fieldValue['model_name'])
				&& !empty($fieldValue['model_name'])
				&& !is_null($fieldValue['model_name'])
			) {
				$result = $fieldValue;
			}
		} else {
			$result = $fieldValue;
		}

		return $result;
	}

	/**
	 * Visszaadja a dhtmlxGrid és az összes dialog oszlopait, valamint azok konfigurációját
	 *
	 * @return mixed
	 */
	public function dialogColumns($dialogColumns = false, $columnTabId = 'dhtmlxGrid')
	{
		$editPK = explode('_', requestParam('editPK'));
		$employeeContractDate = $editPK[1];
		$editPK = $editPK[0];

		//Dénes féle egyszerű dátum mező generálás
		$dpick = function($width, $class = "", $otherProps = [])
		{
			$a = [
				"width"        => $width, "export" => true, "col_type" => "ed",
				"report_width" => 20, "dPicker" => true, "col_align" => "center", "dialog_width" => "1",
			];
			if ($class) {
				$a["class"] = $class;
			}
			foreach ((array)$otherProps as $p => $v) {
				$a[$p] = $v;
			}
			return $a;
		};

		//Kiszedjük hogy a jelenleg szerkesztésre nyitott felhasználónak mi a group_id ja
		$groupSQL = dbFetchRow($this->group_SQL());
		$groupID = $groupSQL['id'];

		$employeeContractId = '';

		$ecSQL = "
		SELECT
		       ec_valid_from,
		       employee_contract_id
		FROM employee_contract
		WHERE
				employee_id = '".$editPK."'
			AND status = " . Status::PUBLISHED . "
			AND '$employeeContractDate' BETWEEN valid_from AND IFNULL(valid_to,'".$this->defaultEnd."') ";

		$ecResult = dbFetchRow($ecSQL);
		if( $ecResult && !empty($ecResult['ec_valid_from']) && $ecResult['ec_valid_from'] !== NULL ){
			$employeeContractId = $ecResult['employee_contract_id'];
			$this->ecValidFrom = $ecResult['ec_valid_from'];
		}

		/*
		 * Mivel a wizards() függvényben lekérdezzük az alábbi oszlopok jelenlegi adatait
		 * így itt kérdezzük le azt is hogy ezeknek az adatoknak mi a row_id értéke
		 * amelyen keresztül majd a történetiség szempontjából a lezárást és nyitást tudjuk majd végezni
		 * */


		//Dénes féle egyszerű field generálás
		$field = function($width, $otherProps = [])
		{
			$a = ["width" => $width, "export" => true, "col_type" => "ed"];
			foreach ((array)$otherProps as $p => $v) {
				$a[$p] = $v;
			}
			return $a;
		};

		$text = function($width,$otherProps=[]) {
			$a = ["width"=>$width,"export"=>true,"col_type"=>"textarea"];
			foreach((array)$otherProps as $p=>$v) $a[$p]=$v;
			return $a;
		};

		//Dénes féle egyszerű select generálás
		$combo = function($width, $otherProps = [])
		{
			$a = ["width" => $width, "export" => true, "col_type" => "combo"];
			foreach ((array)$otherProps as $p => $v) {
				$a[$p] = $v;
			}
			return $a;
		};


		/*
		 * Déne féle options generátor a combo mezőkhöz amelyben
		 * az lehetőség lekérdezéséhez generál egy sql-t
		 * */

		$optionsFromArray = function($source)
		{
			return [
				"mode"  => Grid2Controller::G2BC_QUERY_MODE_ARRAY,
				"array" => $source
			];
		};



		$optionsFromQuery = function(
			$table, $keyField = 0, $valField = 0, $addEmpty = false, $grouping = "", $where = "", $order = "value"
		)
		{
			$keyField = $keyField ? : "{$table}_id";
			$valField = $valField ? : "{$table}_name";
			$published = sprintf(" `status`=%s ", Status::PUBLISHED);
			$defaultEnd = App::getSetting("defaultEnd");
			$validRange = "'{valid_from}' BETWEEN `valid_from` and IFNULL(`valid_to`, '{$defaultEnd}')";
			$unionEmpty = ($addEmpty ? " UNION SELECT null as id, '' as value " : "");
			$where = $where ? : $validRange; // give it "1" if you want to clear the range condition
			$grouping = $grouping ? " GROUP BY $grouping " : "";
			$orderBy = ($order) ? "ORDER BY $order" : "";
			return [
				"mode" => Grid2Controller::G2BC_QUERY_MODE_SQL,
				"sql"  => trim(
					"

							SELECT
								{$keyField} AS id,
								{$valField} AS value
							FROM {$table}
							WHERE {$published} AND ({$where})
							{$grouping} {$unionEmpty} {$orderBy}

						"
				),
			];
		};

		$optionsForDownload = function(
			$table,
			$keyField = 0,
			$valField = 0,
			$addEmpty = false,
			$grouping = "",
			$where = "",
			$order = "value",
			$join = ""
		)
		{
			$keyField = $keyField ? : "{$table}_id";
			$valField = $valField ? : "{$table}_name";
			$published = sprintf(" `status`=%s ", Status::PUBLISHED);
			$defaultEnd = App::getSetting("defaultEnd");
			$validRange = "'{valid_from}' BETWEEN `valid_from` and IFNULL(`valid_to`, '{$defaultEnd}')";
			$unionEmpty = ($addEmpty ? " UNION SELECT null as id, '' as value " : "");
			$where = $where ? : $validRange; // give it "1" if you want to clear the range condition
			$grouping = $grouping ? " GROUP BY $grouping " : "";
			$orderBy = ($order) ? "ORDER BY $order" : "";
			$join = $join ? $join : "";
			return [
				"mode" => Grid2Controller::G2BC_QUERY_MODE_SQL,
				"sql"  => trim(
					"

							SELECT
								{$keyField} AS id,
								{$valField} AS value
							FROM {$table}
							{$join}
							WHERE {$published} AND ({$where})
							{$grouping} {$unionEmpty} {$orderBy}

						"
				),
			];
		};

		$optionsFromLookup = function($key)
		{
			return [
				"mode"  => Grid2Controller::G2BC_QUERY_MODE_ARRAY,
				"array" => App::getLookup($key,true)
			];
		};

		$optionsFromSQL = function($sql, $plusArray = null)
		{
			$out = [
				"mode" => Grid2Controller::G2BC_QUERY_MODE_SQL,
				"sql"  => $sql,
			];
			if (!is_null($plusArray)) {
				$out["array"] = ["array" => $plusArray];
			}
			return $out;
		};

		$titleFields = explode(";", App::getSetting('employeeDialogTitleFields'));
		$titleClass = [];

		foreach ($titleFields as $f) {
			if ($f) {
				$titleClass[$f] = "dialogTitle";
			}
		}

		$nullElement = ["id" => null, "value" => ""];
		$withinTimeRange_both = "

					('{valid_from}'    BETWEEN `valid_from` and IFNULL(`valid_to`,'" . App::getSetting("defaultEnd") . "')) AND
					('{ec_valid_from}' BETWEEN `valid_from` and IFNULL(`valid_to`,'" . App::getSetting("defaultEnd") . "'))

		";

		$center = ["col_align"=>"center"];
		$break  = ["line_break"=>true];
		$noadd  = ["add"=>false];
		$optYesNo = [
			["id"=>"0", "value"=>Dict::getValue("no")  ],
			["id"=>"1", "value"=>Dict::getValue("yes") ],
		];

		$usedAbsences = [];
		$remainingAbsenceFields = ['ext4_option31','ext4_option34','ext4_option36','ext4_option38','ext4_option40','ext4_option42','ext4_option44','ext4_option46','ext4_option48','ext4_option50'];
		$ptoBalanceForCurrentDay = 'ext4_option32';
		$allYearAbsence = 0;
		$prevYearAbsence = 0;


		$c = false;//AnyCache::get("dialogColumns");
		if($c) {
			mDoing("returning dialogColumns from cache");
			$result = $c;
		}else {
			mDoing("dialogColumns() started", +1);

			$groupValueSQL = $this->groupValue_SQL();

			$ec_onchange = ["onchange" => ["employee_position_id"]];

			$valueField = "cost_name";
			$dialogWidth = 1;

			if (!is_null(App::getSetting("show_cost_id_in_employee_edit"))) {
				if (App::getSetting("show_cost_id_in_employee_edit") == 1) {
					$valueField = "CONCAT(`cost_id`, ' - ',`cost_name`)";
				}
				$dialogWidth = 2; // #see 20190702-224443
			}

			$wizard = $this->wizards();

			/*
			 * Lekérdezi egy vagy több megadott dinamikus tab adott mezőit
			 */
			$dynamicAllFields = null;
			$getTabFields = function($data, $tabId) use (
				$combo, $center, $optionsFromSQL, $nullElement, $field, $result, $editPK, &$dynamicAllFields
			)
			{
				/*
				 * Előkérdezzük a használni kívánt értékeket hogy ne a foreachben
				 * és sql hívásonként kérdezzük le őket
				 *
				 */
				$status = Status::PUBLISHED;
				$language = Dict::getLang();
				$result = null;

				//Megnézzük hogy tömbről van-e szó ha nem akkor false a hívás

				if (is_array($data)) {

					/*
					 * Végigmegyünk a tömbön és közben összeállítjuk a lekérdezendő fieldeket egy implode
					 * segítségével.
					 * */

					foreach ($data as $tab_id => $columns) {

						if (is_array($columns) && !empty($columns)) {
							$fields = implode('","', $columns);
						} else {
							continue;
						}

						$SQL = '
						SELECT
							tab_column.tab_id,
							tab_column.column_id,
							dictionary.dict_value,
							tab_column.type,
							eti.row_id as eti_row_id,
							eti.value
						FROM `tab_column`
						LEFT JOIN dictionary
							ON dictionary.dict_id = tab_column.dict_id
							AND dictionary.lang = "' . $language . '"
						LEFT JOIN employee_tab et
							ON et.tab_id = tab_column.tab_id
							AND et.status = "' . Status::PUBLISHED . '"
							AND CURDATE() BETWEEN et.`valid_from`
							AND IFNULL(et.`valid_to`, "' . App::getSetting("defaultEnd") . '")
							AND et.connect_id = "' . $editPK . '"
						LEFT JOIN employee_tab_item eti
							ON eti.employee_tab_id = et.employee_tab_id
							AND eti.status = "' . Status::PUBLISHED . '"
							AND eti.column_id = tab_column.column_id
						WHERE
							tab_column.`status`= ' . $status . '
							AND `tab_column`.`status`= ' . $status . '
							AND tab_column.tab_id = ' . $tab_id . '
							AND tab_column.column_id IN ("' . $fields . '")
					';

						$columnResults = dbFetchAll($SQL);

						/*
						 * A lekérdezett elemeken ellenőrizzük, hogy van-e közte combo mező.
						 * Ahhoz külön le kell kérdeznünk az adatokat amelyeket majd egy data
						 * elnevezésű értékbe pakolunk bele.
						 * */

						foreach ($columnResults as $key => $column) {

							if ($column['type'] == TabColumn::TYPE_COMBO) {
								$tabColumnSQL = "

								SELECT IFNULL(d.dict_value,t.value) as id, IFNULL(d.dict_value,t.value) as value
								FROM tab_column_combo_item t
								LEFT JOIN dictionary d ON d.dict_id = t.value
								WHERE
									t.status     = '$status'
									AND t.tab_id     = '" . $column['tab_id'] . "'
									AND t.column_id  = '" . $column['column_id'] . "'
									AND IF(d.lang IS NOT NULL, d.lang = '{$language}', 1)

								";


								$dynamicAllFields[$tabId][$column['column_id']] = $combo(
									200, $center + [

										   "label_text"    => $column["dict_value"],
										   "default_value" => TabColumnComboItem::getDefaultValue(
											   $column["tab_id"], $column['column_id']
										   ),
										   "options"       => $optionsFromSQL($tabColumnSQL, $nullElement),
										   'model_name'    => 'EmployeeTabItem',
										   'row_id'        => $column['eti_row_id'],
										   'default_value' => $column['value'],

									   ]
								);

							} else {

								$dynamicAllFields[$tabId][$column['column_id']] = $field(
									200, $center + [

										   'dPicker'       => ($column['type'] === TabColumn::TYPE_DATE),
										   'report_width'  => 20,
										   'dialog_width'  => '1',
										   'label_text'    => $column["dict_value"],
										   'model_name'    => 'EmployeeTabItem',
										   'row_id'        => $column['eti_row_id'],
										   'default_value' => $column['value'],

									   ]
								);
							}
						}
					}
					return $dynamicAllFields[$tab_id];
				}
				return false;
			};


			$getLinkRelatedFields = function($relatedId, $relatedValue, $tabId, $type = 1) use($field)
			{
				$fields = [];
				$modelName = null;

				if( !empty($relatedValue) ){
					$explodeRelatedId = explode('_related',$relatedId);
					$realRelatedId = $explodeRelatedId[0];
					if( $type == 1 ){
						$SQL = "
						SELECT
							lgtt.`related_id`,
							lgtt.`related_value`,
							lgtt.`terminal_id`,
							t.`terminal_name`
						FROM link_group_to_terminal lgtt
						LEFT JOIN `terminal` t ON
								t.`terminal_id` = lgtt.terminal_id
							AND t.`status` = '".$this->published."'
							AND CURDATE() BETWEEN t.`valid_from` AND IFNULL(t.`valid_to`, '".$this->defaultEnd."')
						WHERE
								lgtt.`related_id` = '".$realRelatedId."'
							AND lgtt.`related_value` = '".$relatedValue."'
							AND lgtt.`status` = '".$this->published."'
						GROUP BY
							lgtt.`terminal_id`
						";
						$modelName = 'LinkGroupToTerminal';
					}else if ( $type == 2 ){
						$SQL = "
						SELECT
							'".$relatedId."' as related_value,
							tfcbw.terminal_id
							t.terminal_name
						FROM terminal_for_calc_by_workgroup tfcbw
						LEFT JOIN terminal t
							ON t.terminal_id = tfcbw.terminal_id
							AND t.status = '".$this->published."'
							AND CURDATE() BETWEEN t.valid_from AND IFNULL(t.valid_to, '".$this->defaultEnd."')
						WHERE
							tfcbw.".$relatedId." = '".$relatedValue."'
							AND tfcbw.status = '".$this->published."'
						";
						$modelName = 'TerminalForCalcByWorkgroup';
					}

					$relatedFields = dbFetchAll($SQL);
					$order = 0;
					$i = 0;
					if( $relatedFields ){
						foreach( $relatedFields as $key => $val ){
							$i++;
							$order = ($key + 1);
							$keyName = $relatedId . '_' . $order;
							$lineBreak = false;
							if( /*(($order % 6) == 0) || */$order == 1 ){
								$lineBreak = true;
							}
							$fields[$tabId][$keyName] = $field(
								160, [
								'class'      => '',
								'row_id' => NULL,
								'label_text' => $order.'. Munkaidőmérő-pont',
								'default_value' => $val['terminal_name'],
								'history_save' => 0,
								'show_history' => 0,
								'linkedTo' => NULL,
								'model_name' => $modelName,
								'line_break' => $lineBreak,
								'options' =>[
									'skipSave' => true
								]
							]);
						}
					}
				}

				$maxFieldCount = count($fields);
				if( $maxFieldCount < 35 ){
					$i++;
					for( $i; $i <= 60; $i++ ){
						$order = $order + 1;
						$keyName = $relatedId . '_' . $i;
						$lineBreak = false;
						if( $order == 1 ){
							$lineBreak = true;
						}
						$fields[$tabId][$keyName] = $field(
							160, [
							'class'      => '',
							'row_id' => NULL,
							'label_text' => $i . '. Munkaidőmérő-pont',
							'history_save' => 0,
							'show_history' => 0,
							'linkedTo' => NULL,
							'model_name' => $modelName,
							'line_break' => $lineBreak,
							'options' =>[
								'skipSave' => true
							]
						]);
					}
				}

				return $fields;
			};

			$dynamicTabFields = [];
			$usedTables = [];
			$relatedFields = [];

			foreach ($wizard['dhtmlxGrid'] as $wizardTabName => $wizardTabValue) {
				$wizardValues = $wizardTabValue;

				$wizardTabId = $wizardValues['contentId'];

				$wizardSql = str_replace(
					['{row_id_p1}', '{group_id}'], [$editPK, $groupID], $wizardValues['tabSQL']
				);

				$wizardData = dbFetchRow($wizardSql);

				//Le kell kérdeznünk a tabhoz tartozó sorokat és öszeállítani őket

				$wizardFields = ColumnTabs::getTabColumns($wizardTabId);

				$result[$wizardTabId] = [];

				foreach ($wizardFields as $wFieldKey => $wFieldValue) {

					$fieldName = $wFieldValue['column_id'];
					$type = $wFieldValue['col_type'];
					$width = !is_null($wFieldValue['width']) ? (int)$wFieldValue['width'] : 200;
					$export = $wFieldValue['export'];
					$optionType = $wFieldValue['option_type'];
					$options = $wFieldValue['options'];
					$onchange = json_decode($wFieldValue['onchange'], true);
					$modelName = $wFieldValue['model_name'];
					$class = $wFieldValue['class'];
					$columnRowId = $wFieldValue['column_row_id'];
					$skip = $wFieldValue['skip'];
					$dataMask = $wFieldValue['mask'];
					$dataMaskReverse = !is_null(
						$wFieldValue['mask_reverse']
					) && $wFieldValue['mask_reverse'] == '1' ? 'true' : 'false';
					$saveWithHistory = $wFieldValue['save_with_history'];
					$showHistory = $wFieldValue['show_history'];
					$columnTabClass = $wFieldValue['column_tab_class'];
					$linkedTo = $wFieldValue['linked_to'];
					$height = !is_null($wFieldValue['height']) ? $wFieldValue['height'] : null;

					if( $modelName == 'LinkGroupToTerminal' && !empty($wizardData) ){
						$groupRelatedId = App::getSetting('link_group_to_terminal_related_id');
						$relatedValueKey = $groupRelatedId;
						if( $groupRelatedId != 'NONE' && !empty($groupRelatedId) ){
							if( $groupID == $groupRelatedId ){
								$relatedValueKey = 'group_value';
							}
						}else{
							$groupRelatedId = null;
						}

						$relatedFields = $getLinkRelatedFields($groupRelatedId, $wizardData[$relatedValueKey],
							$wizardTabId);
						continue;
						//Le kell kérdezni a related_id val rendelkező mezőket és csatolni a wizard tabban
						// lévőhöz, a lekérdezéshez használt mezőt meg átlépni.
					}else if ( $modelName == 'TerminalForCalcByWorkgroup' && !empty($wizardData) ){
						$relatedFields = $getLinkRelatedFields (
																'workgroup_id',
																$wizardData['workgroup_id'],
																$wizardTabId,
																2
																);
						continue;
						//Le kell kérdezni a related_id val rendelkező mezőket és csatolni a wizard tabban
						// lévőhöz, a lekérdezéshez használt mezőt meg átlépni.
					}

					if ($type === 'field') {

						$result[$wizardTabId][$fieldName] = $field(
							$width, [
							'class'      => ' ' . $class,
							'model_name' => $modelName,
						]
						);

					} else if ($type === 'docs'){
						$result[$wizardTabId][$fieldName] = $field(
							$width,
							[
								'col_type' => 'docs',
								'image_preview' => true,
								'dialog_width' => '2',
								'report_width' => 20,
								'class'      => ' ' . $class,
								'model_name' => $modelName,
								'image_preview_width' => 600,
								'showDownloadButton' => true,
								'acceptedMemes' => 'application/msword, application/vnd.ms-excel, application/vnd.ms-powerpoint, text/plain, application/pdf, image/*'
							]
						);
					} else if ($type === 'textarea'){
						$result[$wizardTabId][$fieldName] = $text(
							$width,
							[
								'class' => ' ' . $class,
								'model_name' => $modelName,
								'height' => $height
							]
						);
					} else if ($type === 'combo') {

						$optionsDecoded = !is_null($options) && !empty($options) ? json_decode($options, true) : '';
						$comboOptions = [];

						if ($optionType === 'query') {

							$optionTable = isset($optionsDecoded['table']) ? $optionsDecoded['table'] : '';
							$optionKeyField = isset($optionsDecoded['keyField']) ? $optionsDecoded['keyField'] : 0;
							$optionValField = isset($optionsDecoded['valField']) ? $optionsDecoded['valField'] : 0;
							$optionAddEmpty = isset($optionsDecoded['addEmpty']) ? $optionsDecoded['addEmpty'] == 'true' ? true : false : false;
							$optionGrouping = isset($optionsDecoded['grouping']) ? $optionsDecoded['grouping'] : '';
							$optionWhere = isset($optionsDecoded['where']) ? $optionsDecoded['where'] : '';
							$optionOrder = isset($optionsDecoded['order']) ? $optionsDecoded['order'] : 'value';

							$comboOptions = $optionsFromQuery(
								$optionTable, $optionKeyField, $optionValField, $optionAddEmpty, $optionGrouping,
								$optionWhere, $optionOrder
							);

						} else if ($optionType === 'sql') {

							if (isset($optionsDecoded['replace'])) {
								if (isset($optionsDecoded['replace']['search']) && isset($optionsDecoded['replace']['replace']) && isset($optionsDecoded['replace']['subject'])) {
									$search = $optionsDecoded['replace']['search'];
									$replace = $optionsDecoded['replace']['replace'];
									$optionsDecoded['sql'] = str_replace(
										$search[0], $replace[0] == '$groupID' ? $groupID : $replace[0],
										$optionsDecoded['replace']['subject'] == '$groupValueSQL' ? $groupValueSQL : $optionsDecoded['replace']['subject']
									);
								}
							}

							$comboOptions = $optionsFromSQL($optionsDecoded['sql']);
						} else if ($optionType === 'array') {
							if (isset($optionsDecoded['array'])) {
								$dataArray = $optionsDecoded['array'];
								$comboOptions = $optionsFromArray($dataArray);
							}
						} else if ($optionType === 'lookup') {
							$comboOptions = $optionsFromLookup($fieldName);
                            $result[$wizardTabId][$fieldName] = $combo(
                                $width, [
                                    'options'    => $comboOptions,
                                    'model_name' => $modelName,
                                ]
                            );
						} else if ($optionType === 'download'){

							$optionTable = isset($optionsDecoded['table']) ? $optionsDecoded['table'] : '';
							$optionKeyField = isset($optionsDecoded['keyField']) ? $optionsDecoded['keyField'] : 0;
							$optionValField = isset($optionsDecoded['valField']) ? $optionsDecoded['valField'] : 0;
							$optionAddEmpty = isset($optionsDecoded['addEmpty']) ? $optionsDecoded['addEmpty'] == 'true' ? true : false : false;
							$optionGrouping = isset($optionsDecoded['grouping']) ? $optionsDecoded['grouping'] : '';
							$optionWhere = isset($optionsDecoded['where']) ? $optionsDecoded['where'] : '1';
							$optionOrder = isset($optionsDecoded['order']) ? $optionsDecoded['order'] : 'value';
							$optionJoin = isset($optionsDecoded['join']) ? $optionsDecoded['join'] : '';

							if( $optionWhere != '1' ){
								$optionWhere = str_replace(
									['{employee_id}', '{group_id}', '{employee_contract_id}'],
									[$editPK, $groupID,	$employeeContractId],
									$optionWhere
								);
							}

							$comboOptions = $optionsForDownload(
								$optionTable,
								$optionKeyField,
								$optionValField,
								$optionAddEmpty,
								$optionGrouping,
								$optionWhere,
								$optionOrder,
								$optionJoin
							);

							if( $fieldName === 'download_files' ){
								$comboOptions['customJs'] = '
									var value = $(this).val();
									var activeTab = $(".tabContent .selected").prop("id").replace("wizardButton", "");

									$.get("./getFileInfo",{
										fileId: value,
										ajax: 1
									},function (data){
										try{
											var response = $.parseJSON(data);

											if( response != "" ){
												$("#dialogInput_"+activeTab+"_note").val(response["note"]);
												$("#dialogInput_"+activeTab+"_valid_from").val(response["valid_from"]);
												$("#dialogInput_"+activeTab+"_valid_to").val(response["valid_to"]);
											}

										} catch(e){
											console.log(e);
										}
									});

									$("#dialogInput_tab5_fs_file_id").val(value).trigger("change");
									if( value == "" ){
										$(".downloadButton").addClass("disabled");
										$("#dialogInput_"+activeTab+"_note").val()
										$(".downloadButton").off("click");
									}else{
										$(".downloadButton").removeClass("disabled");
										$(".downloadButton").off("click").on("click",function (){
											window.open("'.Yii::app()->request->baseUrl.'/filestorage/showDocsFile?file_id="+value);
										});
									}
								';
								$comboOptions['skipSave'] = 1;
								$comboOptions['showDownloadButton'] = 1;

							}

						} else {
							//Ha más típust is szeretnénk
						}

						$result[$wizardTabId][$fieldName] = $combo(
							$width, [
							'options'    => $comboOptions,
							'model_name' => $modelName,
						]
						);

					} else if ($type === 'dpick') {

						$result[$wizardTabId][$fieldName] = $dpick($width, $class, [$onchange]);

					}  else if ($type === 'dynamic') {

						$optionsDecoded = !is_null($options) && !empty($options) ? json_decode($options, true) : '';
						if (
							is_array(
								$optionsDecoded
							) && isset($optionsDecoded['tab_id']) && !empty($optionsDecoded['tab_id'])
						) {
							$dynamicTabFields[$wizardTabId][$optionsDecoded['tab_id']][] = $fieldName;
						} else {
							continue;
						}
					} else if ( $type === 'table' ){
						$optionsDecoded = !is_null($options) && !empty($options) ? json_decode($options, true) : '';

						if( empty($optionsDecoded) || !is_array($optionsDecoded) || is_null($optionsDecoded) ){
							continue;
						}

						if( $optionType === 'fromTable' ){
							$table = $optionsDecoded['saveTable'];
							$fromTable = $optionsDecoded['fromTable'];

							$join = $optionsDecoded['join'];
							$employeeJoin = $optionsDecoded['employee'];
							$selectedRowId = $optionsDecoded['row_id'];
							$joinValue = '';
							if( isset($wizardData[$employeeJoin]) ){
								$joinValue = $wizardData[$employeeJoin];
							}

							if( empty($joinValue) || is_null($joinValue) ){
								continue;
							}

							$select = '*';

							if( isset($optionsDecoded['select']) && !empty($optionsDecoded['select']) ){
								$select = $optionsDecoded['select'];
							}

							if( isset($optionsDecoded['app_settings']) && !empty($optionsDecoded['app_settings']) ){
								if( isset($optionsDecoded['appSettingsSelect']) && !empty($optionsDecoded['appSettingsSelect']) ){
									$columnSetting = App::getSetting($optionsDecoded['app_settings']);
									if( $columnSetting === '1' && App::getSetting('showAbsencesInHours') == 1 ){
										$select = $optionsDecoded['appSettingsSelect'];
									}
								}
							}

							$leftJoin = '';

							if( isset($optionsDecoded['left_join']) && !empty($optionsDecoded['left_join']) ){
								$leftJoin = $optionsDecoded['left_join']. ' AND '.$table.'.'.$employeeJoin.' = "'.$joinValue.'"';
								$leftJoin .= ' AND CURDATE() BETWEEN '.$table.'.valid_from
								AND IFNULL('.$table.'.valid_to, "'.$this->defaultEnd.'")';
							}

							$columnTableSql = '
								SELECT
								   '.$select.' AS value,
								   '.$table.'.row_id,
								   dict_id
								FROM '.$fromTable.'
								'.$leftJoin.'
								WHERE '.$fromTable.'.'.$join.' = "'.$fieldName.'"
								AND '.$fromTable.'.status = '.$this->published.'
							';

							$tableColumnResult = dbFetchRow($columnTableSql);

							$result[$wizardTabId][$fieldName] = $field(
								$width, [
									'class'      => ' ' . $class,
									'model_name' => $modelName,
								]
							);

							if( !is_null($tableColumnResult) && !empty($tableColumnResult) ){
								$result[$wizardTabId][$fieldName]['default_value'] = $tableColumnResult['value'];
								$result[$wizardTabId][$fieldName]['row_id'] = $tableColumnResult['row_id'];
								$result[$wizardTabId][$fieldName]['label_text'] = Dict::getValue($tableColumnResult['dict_id']);
							}else{
								$result[$wizardTabId][$fieldName]['default_value'] = 0;
								$result[$wizardTabId][$fieldName]['row_id'] = null;
							}

							if($fieldName != 'quantity' && $result[$wizardTabId][$fieldName]['model_name'] === 'EmployeeBaseAbsence'){
								$dateintervallum['startDate'] = date('Y') . '-01-01';
								$dateintervallum['endDate'] = date('Y-m-t');
								$absenceQuantity = 0;

								if( $fieldName == '8d92474d33de2925e59dbeb01e7602ad'){
									$absenceQuantity = AHPEmployeeFunctions::getEmployeeAbsenceUsedByEcID($employeeContractId,$dateintervallum,false,[$fieldName]);
								}
								else if ($fieldName == '953908dee36fd0379103cdb0a2d531dd' ) { #TODO: egyéb felhasznált távollét
									$absenceQuantity = AHPEmployeeFunctions::getEmployeeAbsenceUsedByEcID($employeeContractId,$dateintervallum,false,[$fieldName]);
								}

								$usedAbsences[$fieldName] = $absenceQuantity;


							}

						}
					}

					if ($type != 'dynamic') {
						if( $type != 'table' ){

							$wizardDataRowId = null;
							if( isset($wizardData[$columnRowId]) ){
								$wizardDataRowId = $wizardData[$columnRowId];
							}

							$result[$wizardTabId][$fieldName]['row_id'] = $wizardDataRowId;
							$result[$wizardTabId][$fieldName]['label_text'] = Dict::getValue($fieldName);
						}
						$result[$wizardTabId][$fieldName]['model_name'] = $modelName;
						$result[$wizardTabId][$fieldName]['history_save'] = $saveWithHistory;
						$result[$wizardTabId][$fieldName]['show_history'] = $showHistory;
						$result[$wizardTabId][$fieldName]['linkedTo'] = $linkedTo;

						if (!is_null($dataMask) && !empty($dataMask)) {
							$result[$wizardTabId][$fieldName]['data-mask'] = $dataMask;
							$result[$wizardTabId][$fieldName]['class'] .= ' maskedData';
							$result[$wizardTabId][$fieldName]['data-mask-reverse'] = $dataMaskReverse;

						}
					}

					if( stristr($columnTabClass, 'line-break') ){
						$result[$wizardTabId][$fieldName]['line_break'] = true;
					}

					if( isset($fieldName['model_name']) && !is_null($fieldName['model_name']) && !empty($fieldName['model_name']) ){
						$tableModel = new $fieldName['model_name'];
						$tableName = $tableModel::model()->tableSchema->name;
						if( !in_array($tableName, $usedTables) ){
							$usedTables[] = $tableName;
						}
					}

				} //END FOR

				if( Yang::getParam('customerDbPatchName') != 'bos' ){
					$defaultStart = date('Y-m-d');
					$defaultEnd = $this->defaultEnd;
					if( $wizardTabId == 'tab8' ){
						$defaultStart = date('Y').'-01-01';
						if( strtotime($this->ecValidFrom) > strtotime(date('Y').'-01-01') ){
							$defaultStart = $this->ecValidFrom;
						}
						$defaultEnd = date('Y').'-12-31';
					}
					$result[$wizardTabId]['valid_from'] = $dpick(
						200,
						[],
						$break + [
							"skipRight" => true,
							'default_value' =>	$defaultStart,
						]
					);
					$result[$wizardTabId]['valid_to'] = $dpick(200, [], ["skipRight" => true, 'default_value' =>
						$defaultEnd]);
				}

			}

			$result["importDialog"]["fullname"]            = $field(200,['skipRight' => true]);
			$result["employeeLockDialog"]["lock_date"]     = $dpick(200,"",['default_value'=>date("Y-m-d"), "skipRight" => true]);

			$usedTables[] = 'EmployeeGroup';
			$usedTables = implode(',',$usedTables);

			mDoing("dialogColumns() ended",-1);

			if( !is_null($editPK) && !empty($editPK) ){
				AnyCache::set("dialogColumns",$result,$usedTables);
			}

			mDoing("AnyCache OK");
		}

		//Lekérjük az adatokat a fenti tömb segítségével
		foreach ($dynamicTabFields as $tabs => $fields) {
			$getTabFields($fields, $tabs);
			foreach ($dynamicAllFields[$tabs] as $fieldKey => $fieldValue) {
				$result[$tabs][$fieldKey] = $dynamicAllFields[$tabs][$fieldKey];
			}
		}

		foreach ( $relatedFields as $relatedKey => $fields ){

			$relatedValidFrom = $result[$relatedKey]['valid_from'];
			$relatedValidTo = $result[$relatedKey]['valid_to'];

			unset($result[$relatedKey]['valid_from']);
			unset($result[$relatedKey]['valid_to']);

			foreach ( $fields as $fieldKey => $fieldVal ){
				$result[$relatedKey][$fieldKey] = $fieldVal;
			}

			$result[$relatedKey]['valid_from'] = $relatedValidFrom;
			$result[$relatedKey]['valid_to'] = $relatedValidTo;
		}

		/*
		 * Mivel minden értéket megkaptunk tehát teljes a visszküldendő adatcsomag
		 * ezáltal itt kell eldöntenünk, hogy melyik hibás adat, melyik csak olvasható és melyik írható,
		 * továbbá itt tudunk nekik közös tartalmat is adni bármilyen jellegű is legyen az
		 * */
		mDoing("Check dialogColumns right started",-1);
		$authRoleGroup = App::getRolegroup();
		$x =  AnyCache::get("columns_rights");
		if (!$x) {
			$x = $this->getColumnRightFast('employeecontrol', $authRoleGroup);
			AnyCache::set("columns_rights", $x, "column_rights,auth_rolegroup,user");
		}

		$allYearAbsence		= AHPEmployeeFunctions::getEmployeeAbsenceFrameByEcID($employeeContractId,date('Y')); //#TODO: date('Y') helyett a szűrőből kell az évszám!!!
		$prevYearAbsence	= AHPEmployeeFunctions::getEmployeeAbsenceFrameByEcID($employeeContractId,date('Y'), ['63a1079c69d79753cd623ee2620687a1']);
		if( !empty($editPK) || is_null($editPK) ){
			foreach ($result as $tabName => $tabData) {
				foreach ($tabData as $fieldName => $fieldValue){
					/*
				 * Az App::getColumnRight függvénnyel kérdezzük le az egyes oszlopok jogait (read,write)
				 *
				 * További infókért nézd meg a függvényt!
				 * */

					// $right = App::getColumnRight('employeecontrol', $fieldName, $authRoleGroup);
					$right = $x[$fieldName];

					if (isset($fieldValue['skipRight']) && $fieldValue['skipRight'] == true) {

					} else {
						if ($right == 'read' || is_null($right) || empty($right)) {
							$result[$tabName][$fieldName]['readonly'] = true;
						} else {

							/*
							 * Ha nem rendelkezik még rekorddal sehol ez az oszlop akkor jelöljük
							 * hibásnak, hogy ne tudják kitölteni
							 * */
							if (App::getSetting('showEmployeeHistoryError') == 1 && (is_null($fieldValue['row_id']) || empty($fieldValue['row_id']))) {
								$result[$tabName][$fieldName]['readonly'] = true;
								$result[$tabName][$fieldName]['historyError'] = '';
							}
						}
					}

					/*
					 * Ha a mentése adatbázisban történetiséggel van jelölve, pl flexnél akkor default_value legyen a
					 * legutóbbi elem
					 */
					if( isset($result[$tabName][$fieldName]['history_save']) && $result[$tabName][$fieldName]['history_save'] == "1" ){
						$historySql = "
						SELECT
						    eg.row_id,
							eg.group_value
						FROM employee_group eg
						LEFT JOIN employee_contract ec
						    ON ec.employee_id = '$editPK'
						   	AND ec.status = '$this->published'
							AND '$employeeContractDate' BETWEEN ec.ec_valid_from AND IFNULL(ec.ec_valid_to, '$this->defaultEnd')
						WHERE
						    	eg.employee_contract_id = ec.employee_contract_id
							AND group_id = '".$fieldName."'
						  	AND eg.status = '$this->published'
							AND '$employeeContractDate' BETWEEN eg.valid_from AND IFNULL(eg.valid_to, '$this->defaultEnd')
						";
						$historyResult = dbFetchRow($historySql);

						if( $historyResult ){
							$result[$tabName][$fieldName]['row_id'] = $historyResult['row_id'];
							$result[$tabName][$fieldName]['default_value'] = $historyResult['group_value'];
						}

					}

					if( !isset($result[$tabName][$fieldName]['class']) ){
						$result[$tabName][$fieldName]['class'] = '';
					}

					$result[$tabName][$fieldName]['class'] .= ' employeecontrol-form-fields';
					if( isset($result[$tabName][$fieldName]['show_history']) && $result[$tabName][$fieldName]['show_history'] == "1" ){
						$result[$tabName][$fieldName]['class'] .= ' employeecontrol-history-fields';
					}
					if( $fieldName === 'quantity' && $result[$tabName][$fieldName]['model_name'] === 'EmployeeBaseAbsence' ){
						$result[$tabName][$fieldName]['label_text'] = Dict::getValue('base_absence_type_onleave');
					}

					$linkedTo = $result[$tabName][$fieldName]['linkedTo'];
					if( isset($usedAbsences[$linkedTo]) ){
						if( in_array($fieldName,$remainingAbsenceFields) ){
							if( isset($result[$tabName][$linkedTo]['default_value']) ){
								$result[$tabName][$fieldName]['default_value'] = $result[$tabName][$linkedTo]['default_value'] - $usedAbsences[$linkedTo];
							}
						}else{
							$result[$tabName][$fieldName]['default_value'] = $usedAbsences[$linkedTo];
						}
					}

					if( $fieldName == $ptoBalanceForCurrentDay ){
						$allUsedAbsence = array_sum($usedAbsences);

						$diff = date_diff(date_create(date('Y').'-01-01'), date_create(date('Y-m-d')));
						$employeeDaysInYear = $diff->format("%a")+1;

						$numberOfDaysInYear = date("z", strtotime(date('Y-m-d')))+1;

						$result[$tabName][$fieldName]['default_value'] =
							number_format($prevYearAbsence + ($allYearAbsence / $numberOfDaysInYear * $employeeDaysInYear) - $allUsedAbsence, 2);
					}
				}

			}
		}
		mDoing("Check dialogColumns right ended",-1);

		$result["uploadDialog"] = $this->getUploadDialogColumns($result);

		if( Yang::getParam('customerDbPatchName') == 'bos' ){
			$result['dhtmlxGrid']['history_valid_from'] = $dpick(
				200,
				"valid_from",
				[
					'default_value' =>  date('Y-m-d',strtotime(date('Y-m-d').' +2 days')),
					'skipRight'     => true,
					"label_text"    => Dict::getValue("valid_from"),
				]
			);
		}

		//$result['dhtmlxGrid'] = $allFields;
		if( !$dialogColumns ){
			//unset($result['dhtmlxGrid']['employee_id']);
			unset($result['dhtmlxGrid']['title']);
			unset($result['dhtmlxGrid']['valid_from']);
			unset($result['dhtmlxGrid']['valid_to']);
			unset($result['dhtmlxGrid']['employee_contract_type']);
		}
		return $result;
	}

	/**
	 * A dhtmlxGrid wizard alapján összeállít egy összevont feltöltő formot.
	 * Az SQL-eket és a onchange event-eket a megváltozott felépítés alapján módosítja.
	 * $allValids - ha szükség van az összes tabon megjelenő valid_from, valid_to-ra
	 */
	private function getUploadDialogColumns($baseColumns = [], $allValids = false) {
		$uplDialogColumns = [];

		$wizards = $this->wizards();
		$currentWizards = [];
		foreach ($wizards["dhtmlxGrid"] as $wizard) {
			$currentWizards[] = $wizard["contentId"];
		}

		$validFromChanges = [];

		$baseColumns = $this->rearrangeArrayData($baseColumns,'dhtmlxGrid');

		foreach ($baseColumns as $tabId => $baseColumn) {
			if (in_array($tabId, $currentWizards)) {
				foreach ($baseColumn as $column => $props) {
					/**
					 * Összeszedi a valid_from onchange eseményeit, valamint
					 * valid_from, valid_to oszlopok esetén továbblép a ciklus.
					 */
					if (in_array($column,["valid_from", "valid_to"])) {
						if ($column === "valid_from" && isset($props["onchange"])) {
							foreach ($props["onchange"] as $onchange) {
								$validFromChanges[] = $tabId."__".$onchange;
							}
						}

						if(!$allValids) { continue; }
					}

					/**
					 * Megváltoztatja az oszlopoknak a neveit az onchange tömbben az új elnevezés alapján.
					 */
					if (isset($props["onchange"])) {
						$change = [];
						foreach ($props["onchange"] as $onchange) {
							$change[] = $tabId."__".$onchange;
						}

						$props["onchange"] = $change;
					}

					/**
					 * Minden korábbi valid_from onchange eventjét hozzáadja az
					 * ec_valid_from oszlophoz.
					 */
					if ($tabId === "dhtmlxGrid" && $column === "ec_valid_from") {
						if (!isset($props["onchange"])) $props["onchange"] = [];

						foreach ($validFromChanges as $validFromChange) {
							$props["onchange"][] = $validFromChange;
						}
					}

					/**
					 * SQL módosítások a megváltozott oszlopelnevezések miatt
					 */
					if (isset($props["options"]["sql"])) {
						// replace all valid_from / valid_to to ec_valid_from / ec_valid_to
						$props["options"]["sql"] = str_replace(['{valid_from}','{valid_to}',], ['{ec_valid_from}','{ec_valid_to}',], $props["options"]["sql"]);

						// replace everything between {...} to {$tabId__...}
						$props["options"]["sql"] = preg_replace('/(\{)(.*?)(\})/', '{'.$tabId.'__$2}', $props["options"]["sql"]);

						// all ec_valid_from inputs coming from employeeContractTab
						$props["options"]["sql"] = preg_replace('/(\{)(.*?)(__ec_valid_from\})/', '{employeeContractTab__ec_valid_from}', $props["options"]["sql"]);
						$props["options"]["sql"] = preg_replace('/(\{)(.*?)(__ec_valid_to\})/', '{employeeContractTab__ec_valid_to}', $props["options"]["sql"]);
					}

					$uplDialogColumns[$tabId."__".$column] = $props;
				}
			}
		}

		return $uplDialogColumns;
	}

	/*
	 * A segédfüggvény egy asszociatív tömb tetszőleges elemét a tömb elejére helyezi.
	 * @param array $array
	 * @param string $key_to_rearrange
	 * @return array $modified_array
	 */

	private function rearrangeArrayData($array,$key_to_rearrange)
	{
		$temp_array = [];

		$temp_array[$key_to_rearrange] = $array[$key_to_rearrange];
		unset($array[$key_to_rearrange]);
		$modified_array = array_merge($temp_array,$array);

		return $modified_array;
	}

	private function group_SQL()
	{
		$SQL = "
			SELECT
				employee_group_config.`group_id` AS id,
				gr_dict.`dict_value` AS value
			FROM
				`employee_group_config`
			LEFT JOIN
				`dictionary` gr_dict ON
					gr_dict.`dict_id` = employee_group_config.`dict_id`
						AND gr_dict.`lang` = '" . Dict::getLang() . "'
			WHERE
				employee_group_config.`status` = " . Status::PUBLISHED . "
		";

		if (
			!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight(
				"employee/employeeGroupTab", "company_org_group1"
			)
		) {
			$SQL .= "AND employee_group_config.`group_id`!='company_org_group1_id'
			";
		}
		if (
			!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight(
				"employee/employeeGroupTab", "company_org_group2"
			)
		) {
			$SQL .= "AND employee_group_config.`group_id`!='company_org_group2_id'
			";
		}
		if (
			!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight(
				"employee/employeeGroupTab", "company_org_group3"
			)
		) {
			$SQL .= "AND employee_group_config.`group_id`!='company_org_group3_id'
			";
		}
		if (
			!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight(
				"employee/employeeGroupTab", "workgroup"
			)
		) {
			$SQL .= "AND employee_group_config.`group_id`!='workgroup_id'
			";
		}
		if (!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight("employee/employeeGroupTab", "unit")) {
			$SQL .= "AND employee_group_config.`group_id`!='unit_id'
			";
		}
		if (!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight("employee/employeeGroupTab", "cost")) {
			$SQL .= "AND employee_group_config.`group_id`!='cost_id'
			";
		}
		if (
			!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight(
				"employee/employeeGroupTab", "cost_center"
			)
		) {
			$SQL .= "AND employee_group_config.`group_id`!='cost_center_id'
			";
		}

		if (
			!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight(
				"employee/employeeGroupTab", "employee_position"
			)
		) {
			$SQL .= "AND `employee_group_config`.`group_id` != 'employee_position_id'";
		}

		return $SQL;
	}

	private function groupValue_SQL()
	{
		$workgroupLimit = "";
		if (App::hasRight("employee", "workgroupLimitation") && !App::hasRight("employeecontrol", "noworkgrouplimitation")) {
			$workgroupLimit = "work_order = (SELECT work_order FROM workgroup WHERE workgroup_id = (";
			if (EmployeeGroupConfig::isActiveGroup('workgroup_id')) {
				$workgroupLimit .= "SELECT DISTINCT group_value
										FROM employee_group
										LEFT JOIN employee_contract ON employee_group.employee_contract_id = employee_contract.employee_contract_id
										WHERE group_id = 'workgroup_id' AND employee_group.status = " . Status::PUBLISHED . " AND CURDATE() BETWEEN employee_group.valid_from AND employee_group.valid_to AND ";
			} else {
				$workgroupLimit .= "SELECT workgroup_id FROM employee_contract WHERE ";
			}
			$workgroupLimit .= "employee_id = '{row_id_p1}' AND CURDATE() BETWEEN employee_contract.valid_from AND employee_contract.valid_to AND employee_contract.status = " . Status::PUBLISHED . ")) AND ";
		}

		$groupValueSQL = "
			(
				SELECT
					workgroup.`workgroup_id` AS id,
					workgroup.`workgroup_name` AS value
				FROM
					`workgroup`
				WHERE
					$workgroupLimit
						workgroup.`status` = " . Status::PUBLISHED . "
					AND '{group_id}' = 'workgroup_id'
					AND ('{valid_from}' BETWEEN workgroup.`valid_from` and IFNULL(workgroup.`valid_to`,'" . App::getSetting(
				"defaultEnd"
			) . "'))
			)
			UNION
			(
				SELECT
					`unit_id` AS id,
					`unit_name` AS value
				FROM
					`unit`
				WHERE
						unit.`status` = " . Status::PUBLISHED . "
					AND '{group_id}' = 'unit_id'
					AND ('{valid_from}' BETWEEN `valid_from` and IFNULL(`valid_to`,'" . App::getSetting("defaultEnd") . "'))
			)
			UNION
			(
				SELECT
					`company_org_group_id` AS id,
					`company_org_group_name` AS value
				FROM
					`company_org_group1`
				WHERE
						company_org_group1.`status` = " . Status::PUBLISHED . "
					AND '{group_id}' = 'company_org_group1_id'
					AND ('{valid_from}' BETWEEN `valid_from` and IFNULL(`valid_to`,'" . App::getSetting("defaultEnd") . "'))
			)
			UNION
			(
				SELECT
					`company_org_group_id` AS id,
					`company_org_group_name` AS value
				FROM
					`company_org_group2`
				WHERE
						company_org_group2.`status` = " . Status::PUBLISHED . "
					AND '{group_id}' = 'company_org_group2_id'
					AND ('{valid_from}' BETWEEN `valid_from` and IFNULL(`valid_to`,'" . App::getSetting("defaultEnd") . "'))
			)
			UNION
			(
				SELECT
					`company_org_group_id` AS id,
					`company_org_group_name` AS value
				FROM
					`company_org_group3`
				WHERE
						company_org_group3.`status` = " . Status::PUBLISHED . "
					AND '{group_id}' = 'company_org_group3_id'
					AND ('{valid_from}' BETWEEN `valid_from` and IFNULL(`valid_to`,'" . App::getSetting("defaultEnd") . "'))
			)
			UNION
			(
				SELECT
					`cost_id` AS id,
					`cost_name` AS value
				FROM
					`cost`
				WHERE
						cost.`status` = " . Status::PUBLISHED . "
					AND '{group_id}' = 'cost_id'
					AND ('{valid_from}' BETWEEN `valid_from` and IFNULL(`valid_to`,'" . App::getSetting("defaultEnd") . "'))
			)
			UNION
			(
				SELECT
					`cost_center_id` AS id,
					`cost_center_name` AS value
				FROM
					`cost_center`
				WHERE
						cost_center.`status` = " . Status::PUBLISHED . "
					AND '{group_id}' = 'cost_center_id'
					AND ('{valid_from}' BETWEEN `valid_from` and IFNULL(`valid_to`,'" . App::getSetting("defaultEnd") . "'))
			)
			UNION
			(
				SELECT
					`employee_position_id` AS id,
					`employee_position_name` AS value
				FROM
					`employee_position`
				WHERE
						`employee_position`.`status` = " . Status::PUBLISHED . "
					AND '{group_id}' = 'employee_position_id'
					AND ('{valid_from}' BETWEEN `valid_from` AND IFNULL(`valid_to`,'" . App::getSetting("defaultEnd") . "'))
			)
		";
		return $groupValueSQL;
	}


	public function columns($dialogColumns = false)
	{
		return $this->dialogColumns($dialogColumns);
	}

	public function attributeLabels($dialogAttributeLabels = false)
	{

		return $this->dialogAttributeLabels();
	}

	public function dialogAttributeLabels(): array
    {
        $attributeLabels = [];

        $columns = $this->columns();

        foreach ($columns as $key => $tabColumns)
        {
            if(!isset($attributeLabels[$key])) {
                $attributeLabels[$key] = [];
            }

            foreach ($tabColumns as $columnName => $columnData) {
                if (isset($columnData['label_text'])) {
                    if (!isset($attributeLabels[$key])) {
                        $attributeLabels[$key] = [];
                    }

                    $attributeLabels[$key][$columnName] = $columnData['label_text'];
                }
            }
        }

        return $attributeLabels;
    }

	private function getColumnRightFast($controller, $rolegroup_id = 'ALL')
	{

		$queryColumnRights = "
			SELECT
				column_rights.column_id,
				column_rights.roles
			FROM
				`column_rights`
			LEFT JOIN `auth_rolegroup` ON
					`column_rights`.rolegroup_id = `auth_rolegroup`.rolegroup_id
			LEFT JOIN `user` ON
					`auth_rolegroup`.rolegroup_id = `user`.rolegroup_id
				AND CURDATE() BETWEEN `user`.`valid_from` AND IFNULL(`user`.`valid_to`,'".App::getSetting("defaultEnd")."')
				AND	`user`.`status`=".Status::PUBLISHED."
			WHERE
					`column_rights`.`status`=".Status::PUBLISHED."
				AND (user_id ='".userID()."' OR `column_rights`.rolegroup_id='ALL')
				AND controller_id='".$controller."'
			";
		if( Yang::getUserRoleGroup() == "a5b6bd79e008725744118c7c46e10cda" ){
			$queryColumnRights .= " AND column_rights.rolegroup_id = 'ALL'";
		}else{
			$queryColumnRights .= " AND column_rights.rolegroup_id = '".$rolegroup_id."'";
		}

		$result = dbFetchAll($queryColumnRights);

		foreach($result as $key => $value) {
			$y[$value['column_id']] = $value['roles'];
		}

		return $y;
	}

}