<?php

ini_set('memory_limit', '2048M');
ini_set('max_execution_time', 3600);

/**
 * A dolgozókezelésben a dolgozóhoz tartozó dinamikus adatot megjelenítő kimutatás
 * Ha két ugyanolyan nevű column van különböző táblákban, akkor elhhhhh (c) <PERSON>oli, mivel egy temp táblába rakja össze az adatokat így név ütközés lesz.
 */
class EmployeeExtendedReportController extends Grid2Controller
{
	/**
	 * Alkalmazás szintű konfigur<PERSON><PERSON> be<PERSON>ll<PERSON>ok, egy helyre gyűjtve
	 *
	 * @var array
	 */
	private array $apconf;

	public function __construct() {
		parent::__construct("employeeExtendedReport");
		//parent::enableLAGrid();
		$this->apconf = [
			'ptr_customer' => App::getSetting("ptr_customer"),
			'defaultEnd'   => App::getSetting("defaultEnd"),
			'published'	   => Status::PUBLISHED
		];
	}

	protected function G2BInit() {
		parent::setControllerPageTitleId("page_title_employee_extended_report");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);

		$this->LAGridRights->overrideInitRights("export_xls",		false);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);
		$this->LAGridRights->overrideInitRights("export_pdf_node",	false);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);

		$this->LAGridDB->enableSQLMode();

		parent::setGridProperty("splitColumnEnabled", true);

		if ($this->apconf['ptr_customer'] == "BOS") {
			$cols = 2;
		} else {
			$cols = 1;
		}
		//$cols =  App::getSetting("employeereportSeparateData") > 0 ? 2 : 1;
		parent::setGridProperty("splitColumn", $cols);
		parent::G2BInit();
	}

	/**
	 * Dolgozó neve, emp_idje, dinamikus tabok adatainak kiolvasása
	 * @param array $filter
	 * @param string $gridID
	 * @param bool $forReport
	 * @return string
	 */
	protected function setSQL($filter, $gridID, $forReport = false)
	{
		if (isset($filter["valid_date"])) {
			$filter["valid_from"]	= $filter["valid_date"];
			$filter["valid_to"]		= $filter["valid_date"];
		}

		$edFilter = array('controllerId' => $this->getControllerID(), 'search' => $filter);
		$approverParams = array('processId' => "employeeManagement");

		switch ($this->apconf['ptr_customer']) {
			case 'TEESZTERGOM':
				$useTables = ['employee', 'employee_contract', 'cost'];
				break;
			default:
				$useTables = ['employee', 'employee_contract'];
				break;
		}

		$GetActiveEmployeeData = new GetActiveEmployeeData($edFilter,$approverParams,$useTables, "cal.`date`, employee.`employee_id`, employee_contract.`employee_contract_id`", $this->getControllerID());

		$actieTable = $GetActiveEmployeeData->createTempTable();
		$dinamicTableName = "temp_dinamic_employee_datas";
		$dinamicTable = $this->getDinamicTabs($dinamicTableName, $filter["valid_from"], $filter["valid_to"], $filter['quit_employees'] === 'yes');

		$first_col = Employee::getParam('fullname_with_emp_id_ec_id',["all_data","all_data"])." AS fullname";

		if ($this->apconf['ptr_customer'] == "BOS") {
			$first_col = 'CONCAT(`all_data`.`last_name`, \' \', `all_data`.`first_name`) as fullname, `all_data`.`emp_id` ';
		}

		$SQL = "
				SELECT
					" . $first_col ."
				";
		if ($this->apconf['ptr_customer'] == 'TEESZTERGOM') {
			$SQL .= ",
				cost_name
			";
		}
		if($dinamicTable){
			$SQL .= ",
					$dinamicTableName.*";
		}
		$SQL .= "FROM $actieTable all_data
			";
		if($dinamicTable){
			$SQL .= "
				LEFT JOIN " . $dinamicTableName . " ON $dinamicTableName.connect_id = all_data.employee_id";
		}
		$SQL .= "
				GROUP BY `all_data`.`emp_id`";
		if ($filter['quit_employees'] === 'yes') {
			$SQL .= "
				UNION
			".$this->getQuitEmployeesAllDataSQL($filter, "employeeManagement", $dinamicTable);
		}
		$SQL .= "
				ORDER BY fullname
		";

		return $SQL;
	}

	/**
	 * Keresés paraméterezés
	 */
	public function search() {
		$quit_employees = array(array('id' => 'no', 'value' => Dict::getValue("no")),array('id' => 'yes', 'value' => Dict::getValue("yes")));
		$quit_employees_search = array('quit_employees'	=> array(
										'col_type'	=>'combo',
										'width'		=>'*',
										'label_text'=>Dict::getModuleValue("ttwa-base",'quited_employees_too'),
										'options'	=>	array('mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY, 'array' => $quit_employees),
										'default_value'=>'no',
									)
								);
		return Yang::arrayMerge($quit_employees_search, $this->getPreDefinedSearchFromDb("employeeManagement"));
	}

	/**
	 * Oszlopok beállítása
	 */
	public function columns() {
		$retArr = [];

		$retArr = Yang::arrayMerge($retArr, array(
			'fullname'			=> array('export' => true, 'report_width' => 20, 'col_type' => 'ed', 'width' => 300),
		));

		switch ($this->apconf['ptr_customer']) {
			case 'BOS':
				$retArr = array_slice($retArr, 0, 1) + ["emp_id" => array('export'=> true, 'report_width' => 20, 'col_type' => 'ed','width' => 150) ] + array_slice($retArr, 1, count($retArr)-1);
				break;
			case 'TEESZTERGOM':
				$retArr = Yang::arrayMerge($retArr, [
					'cost_name' => ['export' => true, 'report_width' => 20, 'col_type' => 'ed', 'width' => 100]
				]);
				break;
			}

		/**
		 * Dinamikus tabok oszlopai
		 */
		$dinamics = [];
		$dinamicTabs = Tab::getTabs();
		foreach($dinamicTabs as $tab) {
			if((int)$tab["valid"]) {
				if(App::hasRight("employee/dinamicTab_".$tab["tab_id"], "view")) {

					$dinamicColumns = TabColumn::getColumns($tab["tab_id"]);

					foreach ($dinamicColumns as $column){
						$dinamics[$column['column_id']] = ['export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150];
					}

					$retArr = Yang::arrayMerge($retArr, $dinamics);
				}
			}
		}

		$retArr = $this->columnRights($retArr);

		return $retArr;

	}

	/**
	 * Címkék beállítása
	 */
	public function attributeLabels() {
		$result = array(
			'fullname'					=> Dict::getValue("name"),
			'emp_id'					=> Dict::getValue("emp_id")
		);

		switch ($this->apconf['ptr_customer']) {
			case 'TEESZTERGOM':
				$result['cost_name'] = Dict::getValue("cost_id");
				break;
		}

		/**
		 * Dinamikus tabok oszlopainak nevei
		 */
		$dinamicColumns = TabColumn::getDinamicColumns();
		foreach ($dinamicColumns as $column){
			$result[$column['column_id']] = Dict::getValue($column['column_id']);
		}

		return $result;
	}

	/**
	 * A kilépett dologozó SQL-jét visszaadó függvény
	 * @param array $filter
	 * @param string $process_id
	 * @param string|null $dinamicTable
	 * @return string
	 */
	protected function getQuitEmployeesAllDataSQL($filter,$process_id, $dinamicTable) {
		$quit_employees = $this->getQuitEmployeesTable();

		$gpf = new GetPreDefinedFilter($this->getControllerID(), false,array('company' => 'employee', 'payroll' => 'employee'), false,$filter);
		$where=$gpf->getFilter();

		$art = new ApproverRelatedGroup;
		$gargSQL = $art->getApproverReleatedGroupSQLWithoutCalc("Employee", $process_id, false, "AND", "CurrentDate", $this->getControllerID());
		$dinamicTableName = "temp_dinamic_employee_datas";
		$secondTempTable = "temp_dinamic_employee_datas_cp";

		if($dinamicTable){
			$this->copyTable($secondTempTable, $dinamicTableName);
		}

		$first_col = Employee::getParam('fullname_with_emp_id_ec_id',["employee","employee_contract"])." AS fullname";

		if ($this->apconf['ptr_customer'] == "BOS") {
			$first_col = 'CONCAT(`employee`.`last_name`, \' \', `employee`.`first_name`) as fullname, `employee`.`emp_id` ';
		}

		$SQL="
			SELECT
				".$first_col."
				";
		if ($this->apconf['ptr_customer'] == 'TEESZTERGOM') {
			$SQL .= ",
				cost_name
			";
		}
		if($dinamicTable){
			$SQL .= ",
				$secondTempTable.*";
		}
			$SQL .= "
			FROM $quit_employees qe
			LEFT JOIN `employee` ON
					`employee`.`employee_id`=qe.`employee_id`
				AND `employee`.`status`=".$this->apconf['published']."
			LEFT JOIN `employee_contract` ON
					`employee_contract`.`employee_id`=`employee`.`employee_id`
				AND `employee_contract`.`status`=".$this->apconf['published']."
				AND `employee`.`valid_from` <= IFNULL(`employee_contract`.`valid_to`,'".$this->apconf['defaultEnd']."')
				AND `employee`.`valid_to` >= `employee_contract`.`valid_from`
				AND `employee`.`valid_from` <= IFNULL(`employee_contract`.`ec_valid_to`,'".$this->apconf['defaultEnd']."')
				AND `employee`.`valid_to` >= `employee_contract`.`ec_valid_from`
			";
		if($dinamicTable){
			$SQL .= "
			LEFT JOIN $secondTempTable ON $secondTempTable.connect_id = employee.employee_id";
		}
			$SQL .= "
				WHERE
				employee_contract.`row_id` IS NOT NULL
				AND $where
		";
		if (isset($gargSQL["where"])) {
			$SQL .= $gargSQL["where"];
		}

		$SQL.="GROUP BY employee_contract.employee_contract_id
		";

		return $SQL;
	}

	/**
	 * Kilépett dolgozók alap temp tábla
	 * @return string
	 */
	protected function getQuitEmployeesTable() {
		$table="`quit_employee`";
		$SQL="
			CREATE TEMPORARY TABLE IF NOT EXISTS $table
			ENGINE=MyISAM
			SELECT
				e.`employee_id`,
                MAX(LEAST(IFNULL(e.`valid_to`,'2038-01-01'),
						IFNULL(ec.`valid_to`,'2038-01-01'),
						IFNULL(ec.`ec_valid_to`,'2038-01-01'))) as valid_to
			FROM `employee` e
			LEFT JOIN `employee_contract` ec ON
					ec.`employee_id`=e.`employee_id`
				AND ec.`status`=".$this->apconf['published']."
				AND e.`valid_from` <= IFNULL(ec.`valid_to`,'".$this->apconf['defaultEnd']."')
				AND e.`valid_to` >= ec.`valid_from`
				AND e.`valid_from` <= IFNULL(ec.`ec_valid_to`,'".$this->apconf['defaultEnd']."')
				AND e.`valid_to` >= ec.`ec_valid_from`
			WHERE
					e.`status`=".$this->apconf['published']."
				AND ec.`row_id` IS NOT NULL
			GROUP BY e.`employee_id`
			HAVING valid_to<CURDATE()
		";

		dbExecute($SQL);

		return $table;
	}

	/**
	 * A dinamikus tabokon szereplő adatokat szedi össze és teszi egy sorba dolgozónként
	 * @param string $table
	 * @return string
	 */
	private function getDinamicTabs($table, $validFrom, $validTo, $withQuitEmployees = false) {
		$dinamicTabs = Tab::getTabs();
		$dinamicColumns = TabColumn::getDinamicColumns();
		if (!empty($dinamicColumns)) {
			$SQL = "CREATE TEMPORARY TABLE IF NOT EXISTS `$table`
						(";
						$firstTab = 9999;
						foreach ($dinamicTabs as $tab) {
							if((int)$tab["valid"]) {
								if(App::hasRight("employee/dinamicTab_".$tab["tab_id"], "view")) {
									if ($tab["tab_id"] < $firstTab) {
										$firstTab = $tab["tab_id"];
									}
									foreach ($dinamicColumns as $column) {
										if ($column['tab_id'] === $tab["tab_id"]) {
											$SQL.="`" . $column["column_id"] . "` TEXT,
											";
										}
									}
								}
							}
						}

						$SQL .=	"connect_id VARCHAR(32),
							KEY IDX_connect_id (`connect_id`)
						)
						ENGINE = InnoDB DEFAULT CHARACTER SET = utf8 COLLATE = utf8_unicode_ci
							SELECT
								";
						foreach ($dinamicTabs as $tab) {
							if((int)$tab["valid"]) {
								if(App::hasRight("employee/dinamicTab_".$tab["tab_id"], "view")) {
									foreach ($dinamicColumns as $column){
										if ($column['tab_id'] === $tab["tab_id"]) {
											$SQL.="`" . $column["column_id"] . "`,
											";
										}
									}
								}
							}
						}
						$SQL .= " emp_tab_".$firstTab.".connect_id
							FROM ";
			$query_array = [];

			foreach($dinamicTabs as $tab) {
				if((int)$tab["valid"]) {
					if(App::hasRight("employee/dinamicTab_".$tab["tab_id"], "view")) {
						$subQuery = "(SELECT
								";
						/**
						 * Egy sorba teszi a különböző sorokban lévő egy dolgozóhoz tartozó adatokat
						 */
						foreach ($dinamicColumns as $column) {
							if ($column['tab_id'] === $tab["tab_id"]) {
								$subQuery.="MAX(CASE WHEN eti_".$tab["tab_id"].".`column_id` = '" . $column["column_id"] . "' THEN eti_".$tab["tab_id"].".`value` END) as `" . $column["column_id"] . "`,
								";
							}
						}
						$subQuery.= "	et_".$tab["tab_id"].".connect_id
								FROM
										employee_tab et_".$tab["tab_id"]."
								";

						$subQuery .= "
								LEFT JOIN employee_tab_item eti_".$tab["tab_id"]." ON
									eti_".$tab["tab_id"].".`status` = ".$this->apconf['published']."
									AND eti_".$tab["tab_id"].".employee_tab_id = et_".$tab["tab_id"].".employee_tab_id
								WHERE et_".$tab["tab_id"].".`tab_id`='".$tab["tab_id"]."'
											AND et_".$tab["tab_id"].".`status` = ".$this->apconf['published'];
						if (!$withQuitEmployees) {
							$subQuery .= "
								AND ('{$validFrom}' <= et_".$tab["tab_id"].".`valid_to` AND et_".$tab["tab_id"].".`valid_from` <= '{$validTo}')";
						}
						$subQuery .= "
							GROUP BY et_".$tab["tab_id"].".connect_id
								) emp_tab_".$tab["tab_id"] . ($tab["tab_id"] !== $firstTab ? " ON emp_tab_".$tab["tab_id"].".`connect_id`= emp_tab_".$firstTab.".`connect_id` " : "");

						$query_array[] = $subQuery;
					}
				}
			}

			$SQL .= implode(" LEFT JOIN ", $query_array);

			dbExecute($SQL);

			return $table;
		} else {
			return null;
		}
	}

	/**
	 * Lemásolja a tábla tartalmát egy temp táblába.
	 * Erre azért van szükség, mert az unió mindkét felében szükség van a tábla adatainak lekérdezésére, de az ideiglenes táblát csak egyszer lehet lekérdezni.
	 * @param string $newTable
	 * @param string $oldTable
	 */
	private function copyTable($newTable, $oldTable) {
		$SQL = "CREATE TEMPORARY TABLE IF NOT EXISTS `$newTable`
					(
						KEY IDX_connect_id (`connect_id`)
					)
					ENGINE=MyISAM
					SELECT
						*
					FROM
						$oldTable";

		dbExecute($SQL);
	}
}
?>