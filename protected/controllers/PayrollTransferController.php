<?php #yii2: done

'yii2-only`;

	namespace app\controllers;
	use app\components\Grid2PayrollTransfer\controllers\PayrollTransfer\PTCtrl;
	use Yang;

`/yii2-only';


#yii2: done

Yang::import('application.components.Grid2PayrollTransfer.controllers.PayrollTransfer.PTCtrl');

class PayrollTransferController extends PTCtrl
{
	public function __construct() {
		parent::__construct("payrollTransfer");

		parent::enableLAGrid();
	}

	protected function G2BInit() {
		parent::setControllerPageTitleId("page_title_payroll_transfer","ttwa-base");

		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);

		parent::G2BInit();
	}
}