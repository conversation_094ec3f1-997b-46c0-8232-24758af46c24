<?php //

ini_set('max_execution_time', 3600);
ini_set('memory_limit', '2048M');
define('DS', DIRECTORY_SEPARATOR);

class SqlResultCompareController extends Controller
{
	public function actionIndex()	//http://mias.hu.login/sqlResultCompare/index?key=employee_id
	{
		$key = requestParam("key");
		$path = Yii::app()->basePath . DS . '..';
		$oldFile = $path . DS . "oldSql.sql";
		$newFile = $path . DS . "newSql.sql";
		if (file_exists($oldFile) && file_exists($newFile) && $oldSql == $newSql)
		{
			$oldSql = file_get_contents($oldFile);
			$newSql = file_get_contents($newFile);
			echo "A két lekérdezés megegyezik";
		}
		else
		{
//			$oldRes = dbFetchAll($oldSql, $key);
//			$newRes = dbFetchAll($newSql, $key);
			$oldRes = $this->testArrayOld();	//ez csak a tesztelés miatt kell most, amúgy az sql-es kell majd
			$newRes = $this->testArrayNew();
			if ($oldRes == $newRes)
			{
				echo "A két lekérdezés ereménye megegyezik";
			}
			else
			{
				Miner::showRecords($this->compare($oldRes, $newRes));
			}
		}
	}

	private function compare($oldRes, $newRes)
	{
		//mezőnevek összehasonlítása
		$oldVal = reset($oldRes);	//első eleme az oldRes tömbnek
		$oldFields = array_keys($oldVal); //tömb (sql result) mezőnevei- key-ek
		$newVal = reset($newRes);
		$newFields = array_keys($newVal);
//		$fieldDiff = array_merge(array_diff($oldFields, $newFields), array_diff($newFields, $oldFields));
		//a $fieldDiff-ben vannak azok a mezőnevek, amik az nincsenek mindkét tömbben
		//ha itt van különbség, akkor ez a mező kerültjön bele alapból a diff-be

		$oldKeys = array_keys($oldRes);
		$newKeys = array_keys($newRes);
		$mergedKeys = array_unique(array_merge($oldKeys, $newKeys));	//ebben csak az employee_id-kt vannak - vagyis ami külcsnak van megadva
		$res = [];

		for ($i = 0; $i<count($mergedKeys); $i++)
		{	
			//ezt a fieldDiff-et nem így kéne megoldani
			$res[$mergedKeys[$i]]["fields_in_old_but_not_in_new"] = implode(", ", array_diff($oldFields, $newFields));
			$res[$mergedKeys[$i]]["fields_in_new_but_not_in_old"] = implode(", ", array_diff($newFields, $oldFields));
			if (isset($oldRes[$mergedKeys[$i]]) && isset($newRes[$mergedKeys[$i]]))	//itt megnézzük az egyező key-eket
			{
				//itt meg kell nézni, hogy egyezik-e az egész sor (tömbökre műkdik a = ?), ha igen:
				//itt végigmenni az összes oszlopon és megnézni van-e értéke és az egyezik-e
				$res[$mergedKeys[$i]]["where"] = "both";
				if($oldRes[$mergedKeys[$i]] == $newRes[$mergedKeys[$i]])
				{
					var_dump('egyezik a sor, key:'.$mergedKeys[$i]);
				}
				else
				{
					$res[] = $this->compareFieldsAndValues($oldRes[$mergedKeys[$i]], $newRes[$mergedKeys[$i]]);
					//ha nem fullosan ugyanaz a sor akkor meg végig kell menni,
					// mivel a fieldDiff megvan, csak az azonos oszlopokon kéne 
				}
			}
			else if (isset($oldRes[$mergedKeys[$i]]) && !isset($newRes[$mergedKeys[$i]])) //oldban benne van new-ban nincs
			{
				//ez itt ennyi, itt csak ezt kell kiiratni, hogy ebbe van a másikba meg nincs
				$res[$mergedKeys[$i]]["where"] = "old";
				$res[$mergedKeys[$i]]["diff"] = $mergedKeys[$i];	//kulcs értéke 
			}
			else if (!isset($oldRes[$mergedKeys[$i]]) && isset($newRes[$mergedKeys[$i]])) //new-ban benne van old-ban nincs
			{
				//ez itt ennyi, itt csak ezt kell kiiratni, hogy ebbe van a másikba meg nincs
				$res[$mergedKeys[$i]]["where"] = "new";
				$res[$mergedKeys[$i]]["diff"] = $mergedKeys[$i];
			}
		}
		return $res;
	}
	
	private function compareFieldsAndValues($filteredOldRes, $filteredNewRes)
	{
		//itt kéne egy olyan tömb amiben az azonos mezőnevek vannak
		//itt már csak az étékek kellenek
		//csak a megegyező mezőkön kell végigmenni
		$fields = array_intersect(array_keys($filteredOldRes), array_keys($filteredNewRes));
		$res = [];
		for ($i = 0; $i<count($fields); $i++)
		{
			//var_dump($fields[$i]);	//mező
			//var_dump($filteredNewRes[$fields[$i]]);	//érték
			if ($filteredOldRes[$fields[$i]] !== $filteredNewRes[$fields[$i]])
			{
				$res[$fields[$i]]['oldValue'] = $filteredOldRes[$fields[$i]];
				$res[$fields[$i]]['newValue'] = $filteredNewRes[$fields[$i]];
			}
		}
		return $res;
	}

	private function testArrayOld()
	{
		return [
			'old_10' => ['id' => 'old_10',
						'test_field' => 'test_10',
						'old_field' => 'old_10'],
			'old_22' => ['id' => 'old_22',
						'test_field' => 'test_22',
						'old_field' => 'old_22'],
			'33'	=> ['id' => '33',
						'test_field' => '33',
						'old_field' => '33'
						],
			'66'	=> ['id' => '66',
						'test_field' => 'test_66',
						'old_field' => 'old_66'],
		];
	}
	
	private function testArrayNew()
	{
		return [
			'new_10' => ['id' => 'new_10',
						'test_field' => 'test_10',
						'new_field' => 'new_10'],
			'new_22' => ['id' => 'new_22',
						'test_field' => 'test_22',
						'new_field' => 'new_22'],
			'33'	=> ['id' => '33',
						'test_field' => '33',
						'new_field' => '33'
						],
			'66'	=> ['id' => '66',
						'test_field' => 'new_66',
						'new_field' => 'new_66'],
		];
	}
	
}

