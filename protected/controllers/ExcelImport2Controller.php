<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\EmailSender;
	use app\models\Status;
	use Yang;

`/yii2-only';


#yii2: done

ini_set('max_execution_time', 3600);
ini_set("display_errors", 1);
ini_set("memory_limit", "2048M");

define('DS', DIRECTORY_SEPARATOR);

// adatb<PERSON><PERSON><PERSON> t<PERSON>
define('XLS_SYNC_ABS_TRANSLATOR', 'xls_sync_2_abs_translator');
define('XLS_SYNC_AFTER_LOAD_TMP', 'xls_sync_2_after_load_tmp');
define('XLS_SYNC_CONFIG', 'xls_sync_2_config');
define('XLS_SYNC_CONFIG_IMPORT', 'xls_sync_2_config_import');
define('XLS_SYNC_INSERT_TABLE', 'xls_sync_2_insert_table');
define('XLS_SYNC_TMP_TABLE_ROWS', 'xls_sync_2_tmp_table_rows');
define('XLS_SYNC_CHANGED_DATA', 'xls_sync_2_changed_data');
define('XLS_SYNC_POSTFIX_SQL', 'xls_sync_2_postfix');
define('XLS_SYNC_PREFIX_SQL', 'xls_sync_2_prefix');
define('XLS_SYNC_HASH', 'xls_sync_2_hash');



class ExcelImport2Controller extends Controller{


    private $today; // pl tmp táblák végén
    private $sync_error_log_file; // ebbe a fájlba mentjük a hibákat
    private $full_error_log; // összes hiba.. ha nem üres a végén akkor megy az email küldés
    private $previus_tmp_tables; // ennyi korábbi táblát hagyunk meg a többit töröljük
    private $send_email_to; // neki küldjük a hibákat emailben
    private $sync_config; // xls_sync_config tábla eredménye amit az egész feldolgozás során használunk

    private $processing_error = false; // feldolgozás közben volt-e valami hiba

    private $multi_insert_rows = 2000;

	public $corinthia_valid_from; // corinthiánál a file név a valid_from ezért kellet bevezetni ezt a változót
	public $corinthia_valid_to; // corinthiánál a file név a valid_to ezért kellet bevezetni ezt a változót

	public $sync_error_log_file_place; // ide tudjuk menteni az error log file-t ez csak az elérési utat fogja tárolni (pl: /var/share/akarmi/errors/)

	protected $root_path;

	public function __construct(){

	$this->previus_tmp_tables = 5; // ennyi korábbi táblát tartunk meg.. a többit mindig töröljük

	$this->today = date("Ymd_His");

	$this->send_email_to= '<EMAIL>';

	if(!is_dir(Yang::getAlias('webroot').DS."xlsImport".DS."syncErrors")){
		//$this->checkDir(Yang::getAlias('webroot').DS."xlsImport".DS."syncErrors");
	}

        // archive mappa ellenőrzése és létrehozása (ha szükséges)
        if(!is_dir(Yang::getAlias('webroot').DS."xlsImport".DS."oldFiles".DS.date('Y').DS.date('m'))){
            //$this->checkDir(Yang::getAlias('webroot').DS."xlsImport".DS."oldFiles".DS.date('Y').DS.date('m'));
        }

        $this->sync_error_log_file = "xls_2_error_" . date("Ymd_H") . ".txt";;

        require_once(Yang::getBasePath() . DS . 'extensions' . DS . 'spreadsheet-reader' . DS . 'spreadsheet-reader-master' . DS . 'php-excel-reader' . DS . 'excel_reader2.php');

		require_once(Yang::getBasePath() . DS . 'extensions' . DS . 'spreadsheet-reader' . DS . 'spreadsheet-reader-master' . DS . 'SpreadsheetReader.php');

		$this->root_path = Yang::getAlias('webroot').DS;

    }


    public function actionIndex(){

		if(isset($_GET['temondhogyrablotamadas'])) {
            echo 'start... '.date('H:i:s');
            echo '<br><br>';
        }

        if(isset($_GET['getfolders'])) {
            var_dump('check');
            var_dump(Yang::getAlias('webroot'));
            exit();
        }

        if(isset($_GET['temondhogyrablotamadas'])) {
            var_dump(Yang::getAlias('webroot'));
        }

        // konfiguráció leszedése
        $this->getConfigData();


        if(is_array($this->sync_config)){

			$this->processActions('Start');

            if(isset($_GET['temondhogyrablotamadas'])) {
                echo 'start prefixProcess() .. '.date('H:i:s');
                echo '<br><br>';
            }
            // prefix metódusok futtatása
            $this->prefixProcess();


            if(isset($_GET['temondhogyrablotamadas'])) {
                echo 'start checkFile() .. '.date('H:i:s');
                echo '<br><br>';
            }
            // fáljok lézetésének ellenőrzése
            $this->checkFile();

			// csak ha van feldolgozóandó adat (ha nincs file akkor ez is üres lesz)
			if( (is_array($this->sync_config)) AND (count($this->sync_config)) ){

				if(isset($_GET['temondhogyrablotamadas'])) {
					echo 'start createTmpTable() .. '.date('H:i:s');
					echo '<br><br>';
				}
				// tmp táblák készítése
				$this->createTmpTable();
				if(isset($_GET['temondhogyrablotamadas'])) {
					echo 'start loadDataToTmpTable() .. '.date('H:i:s');
					echo '<br><br>';
				}
				// adatok áttöltése file-ból TMP táblába
				$this->loadDataToTmpTable();


				if(isset($_GET['temondhogyrablotamadas'])) {
					echo 'start afterLoadedTempProcess() .. '.date('H:i:s');
					echo '<br><br>';
				}
				// TEMP tábla betöltése után futó folyamatok
				$this->afterLoadedTempProcess();


				if(isset($_GET['temondhogyrablotamadas'])) {
					echo 'start checkRequireElements() .. '.date('H:i:s');
					echo '<br><br>';
				}
				// kötelező mezők ellenőrzése
				$this->checkRequireElements();


				if(isset($_GET['temondhogyrablotamadas'])) {
					echo 'start searchAndChangedData() .. '.date('H:i:s');
					echo '<br><br>';
				}
				// már meglévő dolgozók adatváltozások keresése és kezelése
				$this->searchAndChangedData();


				if(isset($_GET['temondhogyrablotamadas'])) {
					echo 'start searchDeletedEmployee() .. '.date('H:i:s');
					echo '<br><br>';
				}
				// törölt dolgozók keresése és kezelése
				$this->searchDeletedEmployee();


				if(isset($_GET['temondhogyrablotamadas'])) {
					echo 'start checkHash() .. '.date('H:i:s');
					echo '<br><br>';
				}
				// hash-ek keresése a hash táblában, és ami nincs meg azt megjelöljük
				$this->checkHash();


				if(isset($_GET['temondhogyrablotamadas'])) {
					echo 'start loadDataToSiteTable() .. '.date('H:i:s');
					echo '<br><br>';
				}
				// uj adatok áttöltése tmp táblából SITE táblába
				$this->loadDataToSiteTable();


				if(isset($_GET['temondhogyrablotamadas'])) {
					echo 'start postfixProcess() .. '.date('H:i:s');
					echo '<br><br>';
				}
				// a betöltés végén futó egyedi SQL script(ek), vagy függvények
				$this->postfixProcess();


				// fájlok áthelyezése
				if(!isset($_GET['temondhogyrablotamadas'])) {
					$this->moveFiles();
				}

				// régi táblák törlése
				if(!isset($_GET['temondhogyrablotamadas'])) {
					$this->deleteOldTmpTables();
				}

			}

        }

		$this->processActions('End');

        // email küldése ha volt hiba
        if($this->full_error_log != ''){
            $this->sendLogEmail();
        }

		if(isset($_GET['temondhogyrablotamadas'])) {
            echo 'end... '.date('H:i:s');
            echo '<br><br>';
        }

    }

	public function processActions($type){
		foreach($this->sync_config as $row){
			$this->writeLog($type.' process', $row['sync_error_log_file']);
		}
	}


	public function checkHash(){

        foreach($this->sync_config as $row){

			$this->writeLog('checkHash()', $row['sync_error_log_file']);

            if($row['file_containt'] == 'ad'){

                if($row['sync_process_id'] == 'trigo_antifog'){

                    $sql = "UPDATE ".$row['tmp_table_name'].'_'.$this->today.' xls
                            SET xls.xls_hash_used = "n";';

                    try{

                        dbExecute($sql);

                    }catch (Exception $e){

                        $this->writeLog($e->getMessage(), $row['sync_error_log_file']);

                    }

                    $tol = '';
                    $ig = '';


                    ##### -TOL ADATOK
                    $sql_tol = 'SELECT xls.*
                                FROM '.$row['tmp_table_name']."_".$this->today." xls
                                WHERE xls.xls_attendance_date != ''
                                ORDER BY xls.xls_attendance_date ASC LIMIT 1 ;";

                    try{

                        $tol_array = dbFetchRow($sql_tol);

                    }catch (Exception $e){

                        $this->writeLog($e->getMessage(), $row['sync_error_log_file']);

                    }

                    if(is_array($tol_array)){
                        $tol = $tol_array['xls_attendance_date'];
                    }


                    ##### -IG ADATOK
                    $sql_ig = 'SELECT xls.*
                                FROM '.$row['tmp_table_name']."_".$this->today." xls
                                WHERE xls.xls_attendance_date != ''
                                ORDER BY xls.xls_attendance_date DESC LIMIT 1 ;";

                    try{

                        $ig_array = dbFetchRow($sql_ig);

                    }catch (Exception $e){

                        $this->writeLog($e->getMessage(), $row['sync_error_log_file']);

                    }

                    if(is_array($ig_array)){
                        $ig = $ig_array['xls_attendance_date'];
                    }

                    $tol    = str_replace(".", "-", $tol);
                    $ig     = str_replace(".", "-", $ig);

                    // töröljük azokat a sorokat amiből kaptunk újat
                    if( ($tol != '') AND ($ig != '') ){

                        $sql_del = "DELETE FROM anti_fog_staff_2 WHERE attendance_date BETWEEN '".$tol."' AND '".$ig."'; ";

                        try{
                            dbExecute($sql_del);
                        }catch (Exception $e){
                            $this->writeLog($e->getMessage(), $row['sync_error_log_file']);
                        }

                    }

                }else{


                    // még nem lévő sorok megjeleölése
                    $sql = "UPDATE ".$row['tmp_table_name'].'_'.$this->today.' xls
                            LEFT JOIN '.XLS_SYNC_HASH.' h ON h.`hash` = xls.xls_hash
                            SET xls.xls_hash_used = "n"
                            WHERE  h.`hash` is null;';



                    /*
                    UPDATE xls_userdata_20171124123540 xls
                    -- SELECT xls.id, xls.xls_hash, xls.xls_hash_used, h.row_id, h.`hash`
                    -- FROM xls_userdata_20171124123540 xls
                    LEFT JOIN xls_sync_2_hash h ON h.`hash` = xls.xls_hash
                    SET xls.xls_hash_used = 'n'
                    WHERE  h.`hash` is null
                        */

                    try{

                        dbExecute($sql);

                    }catch (Exception $e){

                        $this->writeLog($e->getMessage(), $row['sync_error_log_file']);

                    }



                    // lekérdezzük a még nem használt sorokat

                    $sql = '';

                    if($row['sync_process_id'] == 'pp'){

                        // patikaplusz betöltés
                        $sql = 'SELECT xls.*, DATE_FORMAT(xls.xls_valid_from, "%Y-%m-%d") as xls_valid_from, DATE_FORMAT(IFNULL(xls.xls_valid_to, NULL), "%Y-%m-%d") as xls_valid_to
                                FROM '.$row['tmp_table_name']."_".$this->today." xls
                                WHERE xls.xls_hash_used = 'n'
                                ORDER BY xls.xls_employee_fullname
                                ;";

                    }else if($row['sync_process_id'] == 'ros_e_abs'){

                        //rosenberger szabadság betöltés
                        $sql = 'SELECT xls.*
                                FROM '.$row['tmp_table_name']."_".$this->today." xls
                                WHERE xls.xls_hash_used = 'n'
                                ORDER BY xls.xls_employee_fullname
                                ;";

					}else if($row['sync_process_id'] == 'eurest_abs'){

                        //eurest szabadság betöltés
                        $sql = 'SELECT xls.*
                                FROM '.$row['tmp_table_name']."_".$this->today." xls
                                WHERE xls.xls_hash_used = 'n'
                                ORDER BY xls.xls_employee_name
                                ;";

                    }else{

                        // minden egyébb
                        $sql = 'SELECT xls.*
                                FROM '.$row['tmp_table_name']."_".$this->today." xls
                                WHERE xls.xls_hash_used = 'n'
                                ;";
                    }

                    if($sql != ''){

                        try{
                            $result = dbFetchAll($sql);
                        }catch (Exception $e){
                            $this->writeLog($e->getMessage(), $row['sync_error_log_file']);
                        }

                    }


                    if( (is_array($result)) AND (count($result)) ){

                        // keresünk rá meglévő adatot

                        foreach ($result as $res) {

                            $changed = false;

                            if($row['sync_process_id'] == 'pp'){

                                //patikaplusz
                                $sql = "SELECT  e.* , ec.*
                                        FROM  employee e

                                        LEFT JOIN employee_contract ec ON ec.employee_id = e.employee_id
                                                AND ec.`status` = 2
                                                AND CURDATE() BETWEEN ec.`valid_from` and IFNULL(ec.`valid_to`,'2038-01-01')

                                        WHERE e.employee_id = '".$res['xls_employee_id']."'
                                        AND e.company_id = '".$res['xls_employee_company_tax']."'
                                        AND e.status = ".Status::PUBLISHED."
                                        AND e.valid_from = '".$res['xls_valid_from']."'
                                        ;";

                            }else if($row['sync_process_id'] == 'ros_e_abs'){

                                //rosenberger
                                $sql = "SELECT  e.emp_id, eba.employee_contract_id, eba.row_id, abst.base_absence_type_id, eba.quantity, eba.valid_from, eba.valid_to, ec.daily_worktime

                                        FROM  employee e

                                        LEFT JOIN employee_contract ec ON ec.employee_id = e.employee_id
                                                AND ec.`status` = 2
                                                AND CURDATE() BETWEEN ec.`valid_from` and IFNULL(ec.`valid_to`,'2038-01-01')
												AND CURDATE() BETWEEN ec.`ec_valid_from` and IFNULL(ec.`ec_valid_to`,'2038-01-01')

                                        LEFT JOIN employee_base_absence eba ON eba.employee_contract_id = ec.employee_contract_id
                                                AND eba.`status` = 2
                                                AND CURDATE() BETWEEN eba.valid_from AND IFNULL(eba.valid_to, '2038-01-01')

                                        LEFT JOIN xls_sync_2_abs_translator abst ON abst.base_absence_type_id = eba.base_absence_type_id

                                        WHERE 1=1
										AND e.tax_number = '".$res['xls_tax_number']."'
                                        AND abst.xls_2_sync_file_value = '".$res['xls_abs_type']."'
                                        AND e.status = ".Status::PUBLISHED."
                                        AND CURDATE() BETWEEN e.`valid_from` and IFNULL(e.`valid_to`,'2038-01-01')
                                        ;";

							}elseif($row['sync_process_id'] == 'ros_e_abs_2'){

								$sql = "SELECT

										e.emp_id,
										eba.employee_contract_id,
										eba.row_id, eba.quantity,
										eba.valid_from,
										eba.valid_to,
										eba.base_absence_type_id,
										d.dict_value,
										xls.id,
										xls.xls_alapszab,
										xls.xls_elozoevi,
										xls.xls_eletkorszerint,
										xls.xls_gyerekutan,
										xls.xls_potszabadsag,
										xls.xls_egyebszab,
										xls.xls_apasagi,
										xls.xls_halaleseti

                                        FROM  employee e

                                        LEFT JOIN employee_contract ec ON ec.employee_id = e.employee_id
												AND ec.`status` = ".Status::PUBLISHED."
												AND CURDATE() BETWEEN ec.`valid_from` and IFNULL(ec.`valid_to`,'2038-01-01')
												AND CURDATE() BETWEEN ec.`ec_valid_from` and IFNULL(ec.`ec_valid_to`,'2038-01-01')

										LEFT JOIN employee_base_absence eba ON eba.employee_contract_id = ec.employee_contract_id
												AND eba.`status` = ".Status::PUBLISHED."
												AND CURDATE() BETWEEN eba.valid_from AND IFNULL(eba.valid_to, '2038-01-01')

										LEFT JOIN base_absence_type bat ON bat.base_absence_type_id = eba.base_absence_type_id

										LEFT JOIN dictionary d ON d.dict_id = bat.dict_id AND d.lang = 'hu'

										LEFT JOIN ".$row['tmp_table_name']."_".$this->today." xls ON xls.xls_tax_number = e.tax_number

                                        WHERE 1=1
										AND e.tax_number = '".$res['xls_tax_number']."'
                                        AND e.status = ".Status::PUBLISHED."
                                        AND CURDATE() BETWEEN e.`valid_from` and IFNULL(e.`valid_to`,'2038-01-01')
                                        ;";

							}else if($row['sync_process_id'] == 'eurest_abs'){

                                //eurest szabi betöltés
                                $sql = "SELECT  e.emp_id, eba.employee_contract_id, eba.row_id, abst.base_absence_type_id, eba.quantity, eba.valid_from, eba.valid_to, ec.daily_worktime

                                        FROM  employee e

                                        LEFT JOIN employee_contract ec ON ec.employee_id = e.employee_id
                                                AND ec.`status` = 2
                                                AND CURDATE() BETWEEN ec.`valid_from` and IFNULL(ec.`valid_to`,'2038-01-01')
												AND CURDATE() BETWEEN ec.`ec_valid_from` and IFNULL(ec.`ec_valid_to`,'2038-01-01')

                                        LEFT JOIN employee_base_absence eba ON eba.employee_contract_id = ec.employee_contract_id
                                                AND eba.`status` = 2
                                                AND CURDATE() BETWEEN eba.valid_from AND IFNULL(eba.valid_to, '2038-01-01')

                                        LEFT JOIN xls_sync_2_abs_translator abst ON abst.base_absence_type_id = eba.base_absence_type_id

                                        WHERE 1=1
										AND e.tax_number = '".$res['xls_employee_tax_number']."'
                                        AND abst.xls_2_sync_file_value = '".$res['xls_abs_type']."'
                                        AND e.status = ".Status::PUBLISHED."
                                        AND CURDATE() BETWEEN e.`valid_from` and IFNULL(e.`valid_to`,'2038-01-01')
                                        ;";

							}

                            try{

								if($row['sync_process_id'] == 'ros_e_abs_2'){
									$result_2 = dbFetchAll($sql);
								}else{
									$result_2 = dbFetchRow($sql);
								}

                            }catch (Exception $e){

                                $this->writeLog($e->getMessage(), $row['sync_error_log_file']);

                            }


                            if(is_array($result_2)){

                                if($row['sync_process_id'] == 'pp'){

                                    if($res['xls_employee_fullname'] != $result_2['nameofbirth']){


                                        // volt változás
                                        $changed = true;

                                        if( ($result_2['employee_id'] != '') AND ($result_2['valid_from'] != '') ){

                                            $sql = "UPDATE employee SET valid_to = (NOW() - INTERVAL 1 DAY)
                                                    WHERE employee_id = '".$result_2['employee_id']."'
                                                    AND valid_from = '".$result_2['valid_from']."'
                                                    AND status = ".Status::PUBLISHED."
                                                    AND company_id = '".$res['xls_employee_company_tax']."'
                                                    ;";

                                            try{

                                                dbExecute($sql);

                                            }catch (Exception $e){

                                                $this->writeLog($e->getMessage(), $row['sync_error_log_file']);
                                            }

                                        }

                                    }


                                    if( ($res['xls_valid_from'] != $result_2['valid_from'])
                                        AND ($result_2['valid_to'] != NULL)
                                      ){


                                        // VÁLTOZÁS VOLT EZÉRT UPDATE
                                        $changed = true;
                                        $sql = "UPDATE employee_contract SET ec_valid_to = (NOW() - INTERVAL 1 DAY)
                                                WHERE employee_id = '".$result_2['employee_id']."'
                                                AND ec_valid_from = '".$result_2['valid_from']."'
                                                AND status = ".Status::PUBLISHED."
                                                ;";

                                        try{
                                            dbExecute($sql);
                                        }catch (Exception $e){
                                            $this->writeLog($e->getMessage(), $row['sync_error_log_file']);
                                        }

                                    }


                                    if($changed == FALSE){

                                        $sql_1 = "UPDATE ".$row['tmp_table_name']."_".$this->today."
                                                    SET xls_hash_used = 'i', duplicate_row = 'y'
                                                    WHERE id = ".$res['id']." ;";

                                        $sql_2 = "INSERT INTO xls_sync_2_hash (`hash`, `sync_process_id`)
                                                    VALUES('".$res['xls_hash']."', '".$row['sync_process_id']."')";


                                        try{

                                            dbExecute($sql_1);

                                            dbExecute($sql_2);

                                        }catch (Exception $e){
                                            $this->writeLog($e->getMessage(), $row['sync_error_log_file']);
                                        }

                                    }

                                }elseif($row['sync_process_id'] == 'ros_e_abs'){

                                    // $result_2 = ellenőrzés

                                    // $res = xls sora
									$napok = $res['xls_abs_day'];

									if( ((int)$napok != $result_2['quantity']) ){

                                            // volt változás
                                            $changed = true;

                                            $sql = "UPDATE employee_base_absence SET
                                                            modified_by = 'xls_sync',
                                                            modified_on = NOW(),
                                                            status = 4
                                                        WHERE row_id = '".$result_2['row_id']."';
                                                    ";

                                                try{
                                                    dbExecute($sql);
                                                }catch (Exception $e){
                                                    $this->writeLog($e->getMessage(), $row['sync_error_log_file']);
                                                }

                                                // update-eljük az xls táblát, hogy az egyéb1 mező is benne legyen az alapszabadság sorban
                                                $sql_1 = "UPDATE ".$row['tmp_table_name']."_".$this->today."
                                                            SET xls_abs_day = '".$napok."'
                                                            WHERE id = ".$res['id']." ;";

                                                try{
                                                    dbExecute($sql_1);
                                                }catch (Exception $e){
                                                    $this->writeLog($e->getMessage(), $row['sync_error_log_file']);
                                                }

                                        }

                                        if($changed == FALSE){

                                            $sql_1 = "UPDATE ".$row['tmp_table_name']."_".$this->today."
                                                        SET xls_hash_used = 'i', duplicate_row = 'y', xls_abs_day = '".$napok."'
                                                        WHERE id = ".$res['id']." ;";

                                            $sql_2 = "INSERT INTO xls_sync_2_hash (`hash`, `sync_process_id`)
                                                        VALUES('".$res['xls_hash']."', '".$row['sync_process_id']."')";


                                            try{

                                                dbExecute($sql_1);

                                                dbExecute($sql_2);

                                            }catch (Exception $e){
                                                $this->writeLog($e->getMessage(), $row['sync_error_log_file']);
                                            }

                                            }

									/*
									// Ez volt a régi verzió, ahol még az Egyéb1 néven érkező szabadságot khozzá kellet tölteni az alapszabadságohz
									// később ez módosítva lett és az Egyéb 1 néven érkező szabadságot Egyéb szabadság néven kell felvenni
                                    if(substr($res['xls_abs_type'], 0, 3) != 'Egy' ){


                                        // ha alpaszabadság, akkor hozzá kell adni az egyéb 1 szabadság értékét is
                                        if($res['xls_abs_type'] == 'Alap szabadság'){

                                            $egyeb_1 = $this->getEgyeb1Szabadsag($res, $row['tmp_table_name']);

                                            if(is_array($egyeb_1)){
                                                $res['xls_abs_day']     = (int)$res['xls_abs_day'] + $egyeb_1['day'];
                                                $res['xls_abs_hour']    = (int)$res['xls_abs_hour'] + $egyeb_1['hour'];

                                            }

                                        }

                                        if( ($res['xls_abs_day'] != $result_2['quantity']) ){
                                            // volt változás
                                            $changed = true;
                                            $sql = "UPDATE employee_base_absence SET
                                                            modified_by = 'xls_sync',
                                                            modified_on = NOW(),
                                                            status = 4
                                                        WHERE row_id = '".$result_2['row_id']."';
                                                    ";
                                                try{
                                                    dbExecute($sql);
                                                }catch (Exception $e){
                                                    $this->writeLog($e->getMessage());
                                                }
                                                // update-eljük az xls táblát, hogy az egyéb1 mező is benne legyen az alapszabadság sorban
                                                $sql_1 = "UPDATE ".$row['tmp_table_name']."_".$this->today."
                                                            SET xls_abs_day = '".$res['xls_abs_day']."', xls_abs_hour = '".$res['xls_abs_hour']."'
                                                            WHERE id = ".$res['id']." ;";

                                                try{
                                                    dbExecute($sql_1);
                                                }catch (Exception $e){
                                                    $this->writeLog($e->getMessage());
                                                }

                                        }
                                        if($changed == FALSE){
                                            $sql_1 = "UPDATE ".$row['tmp_table_name']."_".$this->today."
                                                        SET xls_hash_used = 'i', duplicate_row = 'y', xls_abs_day = '".$res['xls_abs_day']."'
                                                        WHERE id = ".$res['id']." ;";
                                            $sql_2 = "INSERT INTO xls_sync_2_hash (`hash`, `sync_process_id`)
                                                        VALUES('".$res['xls_hash']."', '".$row['sync_process_id']."')";
                                            try{
                                                dbExecute($sql_1);
                                                dbExecute($sql_2);
                                            }catch (Exception $e){
                                                $this->writeLog($e->getMessage());
                                            }
                                        }


                                    }else{

                                        // AZ "Egyéb 1" megnevezésű szabadságokkal nem foglalkozunk külön,
                                        // mert azt az alapszabadsághoz kell hozzáadni

                                        $sql_1 = "UPDATE ".$row['tmp_table_name']."_".$this->today."
                                                    SET xls_hash_used = 'i', duplicate_row = 'y'
                                                    WHERE id = ".$res['id']." ;";
                                        $sql_2 = "INSERT INTO ".XLS_SYNC_HASH." (`hash`, `sync_process_id`)
                                                    VALUES('".$res['xls_hash']."', '".$row['sync_process_id']."')";
                                        try{
                                            dbExecute($sql_1);
                                            dbExecute($sql_2);
                                        }catch (Exception $e){
                                            $this->writeLog($e->getMessage());
                                        }
                                    }
                                    */

								}else if($row['sync_process_id'] == 'ros_e_abs_2'){

									foreach ($result_2 as $r2) {

										$update = '';
										$update_2 = '';

										// Alapszabadság
										if($r2['base_absence_type_id'] == '8d92474d33de2925e59dbeb01e7602ad'){

											if($r2['quantity'] == (int)$r2['xls_alapszab']){

												// nem volt változás, ezért az értéket kiürtjük (hogy ne dolgozza fel később)
												$update =	"UPDATE ".$row['tmp_table_name']."_".$this->today."
																SET xls_alapszab =  '-".$r2['xls_alapszab']."'
																WHERE id = ".$r2['id'].";";

											}else{

												// volt változás ezért az employee_base_absence táblában zárjuk a korábbi értéket
												$update_2 = "UPDATE employee_base_absence SET
                                                            modified_by = 'xls_sync_2',
                                                            modified_on = NOW(),
                                                            status = 4
															WHERE row_id = '".$r2['row_id']."';";
											}

										}

										// Előző évi szabadság
										if($r2['base_absence_type_id'] == '63a1079c69d79753cd623ee2620687a1'){

											if($r2['quantity'] == (int)$r2['xls_elozoevi']){

												// nem volt változás, ezért az értéket kiürtjük (hogy ne dolgozza fel később)
												$update =	"UPDATE ".$row['tmp_table_name']."_".$this->today."
																SET xls_elozoevi =  '-".$r2['xls_elozoevi']."'
																WHERE id = ".$r2['id'].";";

											}else{

												// volt változás ezért az employee_base_absence táblában zárjuk a korábbi értéket
												$update_2 = "UPDATE employee_base_absence SET
                                                            modified_by = 'xls_sync_2',
                                                            modified_on = NOW(),
                                                            status = 4
															WHERE row_id = '".$r2['row_id']."';";
											}

										}

										// Életkor szerinti szabadság
										if($r2['base_absence_type_id'] == '99d171c9188a94a197aa2e8c3f22fb04'){

											if($r2['quantity'] == (int)$r2['xls_eletkorszerint']){

												// nem volt változás, ezért az értéket kiürtjük (hogy ne dolgozza fel később)
												$update =	"UPDATE ".$row['tmp_table_name']."_".$this->today."
																SET xls_eletkorszerint = '-".$r2['xls_eletkorszerint']."'
																WHERE id = ".$r2['id'].";";

											}else{

												// volt változás ezért az employee_base_absence táblában zárjuk a korábbi értéket
												$update_2 = "UPDATE employee_base_absence SET
                                                            modified_by = 'xls_sync_2',
                                                            modified_on = NOW(),
                                                            status = 4
															WHERE row_id = '".$r2['row_id']."';";
											}

										}

										// Gyerekek utáni szabadság
										if($r2['base_absence_type_id'] == '3124666222842d616949b952976b1238'){

											if($r2['quantity'] == (int)$r2['xls_gyerekutan']){

												// nem volt változás, ezért az értéket kiürtjük (hogy ne dolgozza fel később)
												$update =	"UPDATE ".$row['tmp_table_name']."_".$this->today."
																SET xls_gyerekutan = '-".$r2['xls_gyerekutan']."'
																WHERE id = ".$r2['id'].";";

											}else{

												// volt változás ezért az employee_base_absence táblában zárjuk a korábbi értéket
												$update_2 = "UPDATE employee_base_absence SET
                                                            modified_by = 'xls_sync_2',
                                                            modified_on = NOW(),
                                                            status = 4
															WHERE row_id = '".$r2['row_id']."';";
											}

										}

										// Pótszabadság
										if($r2['base_absence_type_id'] == '5cb1f0666d19d9eb290457bebd45d0cd'){

											if($r2['quantity'] == (int)$r2['xls_potszabadsag']){

												// nem volt változás, ezért az értéket kiürtjük (hogy ne dolgozza fel később)
												$update =	"UPDATE ".$row['tmp_table_name']."_".$this->today."
																SET xls_potszabadsag = '-".$r2['xls_potszabadsag']."'
																WHERE id = ".$r2['id'].";";

											}else{

												// volt változás ezért az employee_base_absence táblában zárjuk a korábbi értéket
												$update_2 = "UPDATE employee_base_absence SET
                                                            modified_by = 'xls_sync_2',
                                                            modified_on = NOW(),
                                                            status = 4
															WHERE row_id = '".$r2['row_id']."';";
											}

										}

										// Egyéb szabadság
										if($r2['base_absence_type_id'] == 'e9bb81654931b76510b0118fb2ee183a'){

											if($r2['quantity'] == (int)$r2['xls_egyebszab']){

												// nem volt változás, ezért az értéket kiürtjük (hogy ne dolgozza fel később)
												$update =	"UPDATE ".$row['tmp_table_name']."_".$this->today."
																SET xls_egyebszab = '-".$r2['xls_egyebszab']."'
																WHERE id = ".$r2['id'].";";

											}else{

												// volt változás ezért az employee_base_absence táblában zárjuk a korábbi értéket
												$update_2 = "UPDATE employee_base_absence SET
                                                            modified_by = 'xls_sync_2',
                                                            modified_on = NOW(),
                                                            status = 4
															WHERE row_id = '".$r2['row_id']."';";
											}

										}

										// Apasági szabadság
										if($r2['base_absence_type_id'] == '953908dee36fd0379103cdb0a2d531dd'){

											if($r2['quantity'] == (int)$r2['xls_apasagi']){

												// nem volt változás, ezért az értéket kiürtjük (hogy ne dolgozza fel később)
												$update =	"UPDATE ".$row['tmp_table_name']."_".$this->today."
																SET xls_apasagi = '-".$r2['xls_apasagi']."'
																WHERE id = ".$r2['id'].";";

											}else{

												// volt változás ezért az employee_base_absence táblában zárjuk a korábbi értéket
												$update_2 = "UPDATE employee_base_absence SET
                                                            modified_by = 'xls_sync_2',
                                                            modified_on = NOW(),
                                                            status = 4
															WHERE row_id = '".$r2['row_id']."';";
											}

										}

										// Haláleseti szabadság
										if($r2['base_absence_type_id'] == 'e35b75ed4f59c57f5601951dc0080ad6'){

											if($r2['quantity'] == (int)$r2['xls_halaleseti']){

												// nem volt változás, ezért az értéket kiürtjük (hogy ne dolgozza fel később)
												$update =	"UPDATE ".$row['tmp_table_name']."_".$this->today."
																SET xls_halaleseti = '-".$r2['xls_halaleseti']."'
																WHERE id = ".$r2['id'].";";

											}else{

												// volt változás ezért az employee_base_absence táblában zárjuk a korábbi értéket
												$update_2 = "UPDATE employee_base_absence SET
                                                            modified_by = 'xls_sync_2',
                                                            modified_on = NOW(),
                                                            status = 4
															WHERE row_id = '".$r2['row_id']."';";
											}

										}


										try{

											if($update != ''){
												dbExecute($update);
											}

											if($update_2 != ''){
												dbExecute($update_2);
											}

                                        }catch (Exception $e){
                                            $this->writeLog($e->getMessage(), $row['sync_error_log_file']);
                                        }

									}

								}elseif($row['sync_process_id'] == 'eurest_abs'){

									if( ((int)$res['xls_abs_quantity'] != (int)$result_2['quantity']) ){

										// volt változás
										$changed = true;

										$sql = "UPDATE employee_base_absence SET
														modified_by = 'xls_sync',
														modified_on = NOW(),
														status = 4
													WHERE row_id = '".$result_2['row_id']."';
												";

										try{
											dbExecute($sql);
										}catch (Exception $e){
											$this->writeLog($e->getMessage(), $row['sync_error_log_file']);
										}

                                    }

									if($changed == FALSE){

										$sql_1 = "UPDATE ".$row['tmp_table_name']."_".$this->today."
													SET xls_hash_used = 'i', duplicate_row = 'y'
													WHERE id = ".$res['id']." ;";

										$sql_2 = "INSERT INTO xls_sync_2_hash (`hash`, `sync_process_id`)
													VALUES('".$res['xls_hash']."', '".$row['sync_process_id']."')";

										try{

											dbExecute($sql_1);

											dbExecute($sql_2);

										}catch (Exception $e){
											$this->writeLog($e->getMessage(), $row['sync_error_log_file']);
										}

									}

								}

                            }else{

                                if($row['sync_process_id'] == 'ros_e_abs'){

									if(!is_array($result_2)){

										$sql = "SELECT ec.daily_worktime

												FROM  employee e

												LEFT JOIN employee_contract ec ON ec.employee_id = e.employee_id
														AND ec.`status` = 2
														AND CURDATE() BETWEEN ec.`valid_from` and IFNULL(ec.`valid_to`,'2038-01-01')
														AND CURDATE() BETWEEN ec.`ec_valid_from` and IFNULL(ec.`ec_valid_to`,'2038-01-01')

												WHERE  1=1
												AND e.tax_number = '".$res['xls_tax_number']."'
												AND e.status = ".Status::PUBLISHED."
												AND CURDATE() BETWEEN e.`valid_from` and IFNULL(e.`valid_to`,'2038-01-01')
												;";

										 try{

											$result_2 = dbFetchRow($sql);

										}catch (Exception $e){

											$this->writeLog($e->getMessage(), $row['sync_error_log_file']);

										}

									}


									if( (is_array($result_2)) AND (count($result_2)) ){

										$napok = $res['xls_abs_hour'];

										$sql_1 = "UPDATE ".$row['tmp_table_name']."_".$this->today."
													SET xls_abs_day = '".$napok."', xls_hash_used = 'n'
													WHERE id = ".$res['id']." ;";

										try{
											dbExecute($sql_1);
										}catch (Exception $e){
											$this->writeLog($e->getMessage(), $row['sync_error_log_file']);
										}

									}else{
											$this->writeLog('Hiányzó ember..'.$res['xls_employee_fullname'].' / emp_id:'.$res['xls_emp_id'], $row['sync_error_log_file'] );
									}


									/*
                                    if( $res['xls_abs_type'] == 'Alap szabadság'){

                                        $egyeb_1 = $this->getEgyeb1Szabadsag($res, $row['tmp_table_name']);

                                            if(is_array($egyeb_1)){

                                                $res['xls_abs_day']     = (int)$res['xls_abs_day'] + $egyeb_1['day'];
                                                $res['xls_abs_hour']    = (int)$res['xls_abs_hour'] + $egyeb_1['hour'];

                                                // update-eljük az xls táblát, hogy az egyéb1 mező is benne legyen az alapszabadság sorban
                                                $sql_1 = "UPDATE ".$row['tmp_table_name']."_".$this->today."
                                                            SET xls_abs_day = '".$res['xls_abs_day']."', xls_abs_hour = '".$res['xls_abs_hour']."'
                                                            WHERE id = ".$res['id']." ;";

                                                try{
                                                    dbExecute($sql_1);
                                                }catch (Exception $e){
                                                    $this->writeLog($e->getMessage(), $row['sync_error_log_file']);
                                                }

                                            }

                                    }
									*/

                                }
								/*
								// ez nem biztos hogy kell majd ide

								if($row['sync_process_id'] == 'eurest_abs'){

									if(!is_array($result_2)){

										$sql = "SELECT ec.daily_worktime
												FROM  employee e
												LEFT JOIN employee_contract ec ON ec.employee_id = e.employee_id
														AND ec.`status` = 2
														AND CURDATE() BETWEEN ec.`valid_from` and IFNULL(ec.`valid_to`,'2038-01-01')
														AND CURDATE() BETWEEN ec.`ec_valid_from` and IFNULL(ec.`ec_valid_to`,'2038-01-01')
												WHERE  1=1
												AND e.tax_number = '".$res['xls_tax_number']."'
												AND e.status = ".Status::PUBLISHED."
												AND CURDATE() BETWEEN e.`valid_from` and IFNULL(e.`valid_to`,'2038-01-01')
												;";

										 try{
											$result_2 = dbFetchRow($sql);
										}catch (Exception $e){
											$this->writeLog($e->getMessage(), $row['sync_error_log_file']);
										}

									}


									if( (is_array($result_2)) AND (count($result_2)) ){

										$napok = $res['xls_abs_quantity'];

										$sql_1 = "UPDATE ".$row['tmp_table_name']."_".$this->today."
													SET xls_hash_used = 'n'
													WHERE id = ".$res['id']." ;";
										try{
											dbExecute($sql_1);
										}catch (Exception $e){
											$this->writeLog($e->getMessage(), $row['sync_error_log_file']);
										}

									}else{
											$this->writeLog('Hiányzó ember..'.$res['xls_employee_fullname'].' / emp_id:'.$res['xls_emp_id'], $row['sync_error_log_file'] );
									}

								}
								*/

                            }
                        }
                    }
                }

            }

        }

    }

    /**
     * Prefix SQL, és function-ok meghívása futtatása
     */
    public function prefixProcess(){

        foreach($this->sync_config as $row){

			$this->writeLog('prefixProcess', $row['sync_error_log_file']);

            $prefixes = $this->getPrefixData($row['sync_process_id'], $row['sync_error_log_file']);

            if( (is_array($prefixes)) AND (count($prefixes)) ){

                foreach ($prefixes as $pr){

					// ha egy sql utasítást kell futtatni
                    if($pr['type'] == 'sql'){

                        if($pr['value'] != ''){

							$this->writeLog('prefixProcess SQL run', $row['sync_error_log_file']);

                            try{
                                dbExecute($pr['value']);

                            }catch (Exception $e){

                                $this->writeLog($e->getMessage(), $row['sync_error_log_file']);

                            }

                        }else{

                            $this->writeLog('Az SQL utasítás üres. Nem lehet mit futtatni. ( row_id: '.$pd['row_id'].') ', $row['sync_error_log_file']);

                        }

                    }

                    // ha egy function-t kell meghívni
                    if($pr['type'] == 'function'){

						$this->writeLog('prefixProcess function ['.$pr['value'].'] ', $row['sync_error_log_file']);

                        call_user_func_array( ['ExcelImport2Controller', $pr['value']], [$pr, $row] );


                    }

                }

			}else{
				$this->writeLog('Nincs prefixProcess! ('.$row['sync_process_id'].')', $row['sync_error_log_file']);
			}

        }

    }

    /**
     * Lekérdezük az összes prefix sql,t és folyamatot
     *
     * @param string $sync_process_id
     * @return mixed array|bool
     */
    public function getPrefixData($sync_process_id, $sync_error_log_file){

        $return = false;

        $sql = "SELECT *
                FROM `".XLS_SYNC_PREFIX_SQL."`
                WHERE sync_process_id = '" . $sync_process_id . "'
                AND `status` = " . Status::PUBLISHED . "
                AND `valid_from` < CURDATE()
                AND `valid_to`   >= CURDATE()
                ORDER BY sort
                ";

        try {

            $result = dbFetchAll($sql);

        } catch (Exception $e) {

            $this->writeLog($e->getMessage(), $sync_error_log_file);

        }

        if (count($result)) {
            $return = $result;
        }

        return $return;

    }


    /**
     * Futtatjuk a már meglévő adatokat kereső sql-t.
     */
    public function searchAndChangedData(){


        foreach($this->sync_config as $row){

            if( ($row['sync_process_id'] != 'ros_new_employee') AND ($row['sync_process_id'] != 'eurest_abs') AND ($row['sync_process_id'] != 'corinthia_upload') ){

                if($row['file_containt'] == 'nd'){

                    // elsőnek megjelöljük azokat a sorokat a TMP táblában mik már léteznek a rendszerben

                    // már meglévő userek
                    $current_data = $this->getEmployeesId();

                    if( (is_array($current_data)) AND (count($current_data)) ){

                        foreach($current_data as $cd) {
                            $current[] = $cd['employee_id'];
                        }


                        // TMP táblában lévő adatok
                        $tmp_data = $this->getTmpTableRows($row['tmp_table_name'].'_'.$this->today);

                        if( (is_array($tmp_data)) AND (count($tmp_data)) ){

                            foreach($tmp_data as $td){


                                if(in_array($td['xls_employee_id'], $current)){

                                    $sql = "UPDATE `".$row['tmp_table_name'].'_'.$this->today."`
                                            SET `duplicate_row` = 'y'
                                            WHERE `xls_employee_id` = '".$td['xls_employee_id']."' ";

                                    dbExecute($sql);

                                }

                                //$tmp[] = $td['xls_employee_id'];

                            }

                        }

                    }

					// most megnézzük, hogy melyik adatok változtak meg.
					// csak azokon a sorokon amik duplikátumok
                    $this->checkChangedData($row['sync_process_id'], $row['tmp_table_name'].'_'.$this->today, $row['sync_error_log_file']);



					/*
					$new_sql = "SELECT * FROM `".$row['tmp_table_name'].'_'.$this->today."`
							    WHERE `duplicate_row` = 'y'; ";

					$new_data = FALSE;

					try{
                        $new_data = dbFetchAll($new_sql);
                    }catch (Exception $e){
                        $this->writeLog($e->getMessage());
                    }

					if( (is_array($new_data)) AND ( count($new_data)) ){

						foreach ($new_data as $new_row){

							$old_sql = "SELECT * FROM ";

						}

					}
					*/

                }

            }

        }

    }

    /**
     * Postfix SQL, és function-ok meghívása futtatása
     */
    public function postfixProcess(){

        foreach($this->sync_config as $row){

            $postfix_data = $this->getPostfixData($row['sync_process_id'], $row['sync_error_log_file']);

            if( (is_array($postfix_data)) AND (count($postfix_data)) ){

                foreach ($postfix_data as $pd){


                    // ha egy sql utasítást kell futtatni
                    if($pd['type'] == 'sql'){

                        if($pd['sql'] != ''){

							if( strpos($pd['value'], '##CHANGETABLENAME##') !== FALSE){
								$pd['value'] = str_replace("##CHANGETABLENAME##", $row['tmp_table_name']."_".$this->today, $pd['value']);
							}

                            try{

                                dbExecute($pd['value']);

                            }catch (Exception $e){

                                $this->writeLog($e->getMessage(), $row['sync_error_log_file']);

                            }

                        }else{

                            $this->writeLog('Az SQL utasítás üres. Nem lehet mit futtatni. ( row_id: '.$pd['row_id'].') ', $row['sync_error_log_file']);

                        }

                    }

                    // ha egy function-t kell meghívni
                    if($pd['type'] == 'function'){

                        call_user_func_array( ['ExcelImport2Controller', $pd['value']], [$pd, $row] );

                    }

                }

            }

        }

    }


    /**
     * Lekérdezük az összes postfix sql-t
     *
     * @param string $sync_process_id
     * @return mixed array|bool
     */
    public function getPostfixData($sync_process_id, $sync_error_log_file){

        $return = false;

        $sql = "SELECT *
                FROM `".XLS_SYNC_POSTFIX_SQL."`
                WHERE sync_process_id = '" . $sync_process_id . "'
                AND `status` = " . Status::PUBLISHED . "
                AND `valid_from` < CURDATE()
                AND `valid_to`   >= CURDATE()
                ORDER BY sort
                ";


        try {

            $result = dbFetchAll($sql);

        } catch (Exception $e) {

            $this->writeLog($e->getMessage(), $sync_error_log_file);

        }

        if (count($result)) {
            $return = $result;
        }

        return $return;

    }

    /**
     * Változások lekérdezése, és ellenőrzése
     *
     * @param string $sync_process_id
     * @param string $tmp_table
     */
    public function checkChangedData($sync_process_id, $tmp_table, $sync_error_log_file){

        $tmp_result = false;
        $sql = "SELECT *
                FROM `".XLS_SYNC_CHANGED_DATA."`
                WHERE sync_process_id = '" . $sync_process_id . "'
                AND `status` = " . Status::PUBLISHED . "
                GROUP BY `table`
                ORDER BY sort
                ";

        try {

            $result = dbFetchAll($sql);

        } catch (Exception $e) {

            $this->writeLog($e->getMessage(), $sync_error_log_file);

        }


		if (count($result)) {

            $tables = $result;

            foreach ($tables as $table) {

                $sql = "SELECT *
                        FROM `".XLS_SYNC_CHANGED_DATA."`
                        WHERE sync_process_id = '" . $sync_process_id . "'
                        AND `table` = '" . $table['table'] . "'
                        AND `status` = " . Status::PUBLISHED . "
                        ORDER BY sort2
                        ";

                try {

                    $rows = dbFetchAll($sql);

                } catch (Exception $e) {

                    $this->writeLog($e->getMessage(), $sync_error_log_file);

                }

                if (count($rows)) {

                    $join = '';
                    $and = '';
                    $ec_id = ['employee_base_absence', 'employee_cost', 'employee_salary', 'employee_card'];

                    // employee_base_absence, employee_cost, employee_salary táblák esetében nincs employee_id, így másképp kell a join is (employee_contract_id)
                    if(in_array($table['table'], $ec_id)){

                        $join .= "LEFT JOIN " . $table['table'] . " ON " . $table['table'] . ".employee_contract_id = CONCAT(xls.xls_employee_id, '1')
                                        AND " . $table['table'] . ".`status` = 2
                                        AND " . $table['table'] . ".valid_from <= CURDATE()
                                        AND IFNULL(" . $table['table'] . ".`valid_to`, '2038-01-01') >= CURDATE()
                                        ";

                        if($table['table'] == 'employee_base_absence'){

                            // csak az alapszabadságokat nézzük, mivel a fájlban 1 oszlopvan 1 értékkel
                            $join .= " AND " . $table['table'] . ".base_absence_type_id = '8d92474d33de2925e59dbeb01e7602ad' ";

                        }

                        $and = " AND " . $table['table'] . ".employee_contract_id IS NOT NULL ";

                    }else{

                        $join .= " LEFT JOIN " . $table['table'] . "  ON " . $table['table'] . ".employee_id = xls.`xls_employee_id` ";

                    }


                    $sql = "SELECT xls.* ";

                    $sql .= " FROM " . $tmp_table . " xls ";

                    $sql .= $join;

                    $sql .= "   AND " . $table['table'] . ".`status` = 2

                                AND " . $table['table'] . ".`valid_from` < CURDATE()

                                AND IFNULL(" . $table['table'] . ".`valid_to`, '2038-01-01') >= CURDATE() ";

                    $sql .= " WHERE xls.`duplicate_row` = 'y' AND ( ";

                    foreach ($rows as $row) {

                        if(!is_null($row['tmp_col'])){

                            $sql .= ' ( ' . $row['tmp_col'] . " != " . $row['table'] . "." . $row['col'] . " OR (" . $row['table'] . "." . $row['col'] . " IS NULL AND " . $row['tmp_col'] . "  IS NOT NULL AND " . $row['tmp_col'] . "  != '') ) OR";

                        }

                    }

                    $sql = trim($sql, "OR");

                    $sql .= " )";

                    $sql .= $and;


                    try{

                        $tmp_result = dbFetchAll($sql);

                    }catch (Exception $e){

                        $this->writeLog($e->getMessage(), $sync_error_log_file);

                    }

                    if(count($tmp_result)){

                        foreach($tmp_result as $tr){

                            // korábbi sorok lezárása
                            $and = '';

                            if(in_array($table['table'], $ec_id)){

                                if($table['table'] == 'employee_base_absence'){

                                    $and .= " AND `employee_contract_id` = CONCAT(".$tr['xls_employee_id'].", '1') ";

                                }

                            }else{

                                $and .= " AND `employee_id` = '".$tr['xls_employee_id']."' ";

                            }


							$valid_to = $sync_process_id === 'corinthia_upload' ? $this->corinthia_valid_to : ' CURDATE() - INTERVAL 1 DAY ';

                            $update = "UPDATE `".$table['table']."`
                                        SET `valid_to`    = '".$valid_to."' ,
                                            `modified_by` = 'xls_import',
                                            `modified_on` = NOW()
                                        WHERE 1 = 1
                                        ".$and."
                                        AND status = ".Status::PUBLISHED."
                                        AND `valid_to` > CURDATE(); ";


							try{

								dbExecute($update);

							}catch (Exception $e){

								$this->writeLog($e->getMessage(), $sync_error_log_file);

							}

                            // uj adatok felvitele
                            $values = "";
                            $insert = "INSERT INTO ".$table['table']." (";

                            foreach($rows as $row){

                                if(!is_null($row['tmp_col'])){

                                    if(isset($tr[$row['tmp_col']])){

										$insert .= $row['col'].",";

                                        if( ($sync_process_id === 'corinthia_upload') AND ($row['col'] === 'valid_from') ){
											$values .= "'".$this->corinthia_valid_from."',";
										}else{
											$values .= "'".$tr[$row['tmp_col']]."',";
										}

                                    }
                                }

                            }

                            $values = trim($values, ",");
                            $insert = trim($insert, ",");

                            $insert .=") VALUES (".$values.") ";

                            if($values != ''){

								try{

									dbExecute($insert);

								}catch (Exception $e){

									$this->writeLog($e->getMessage(), $sync_error_log_file);

								}

                            }

                        }

                    }

                }

            }

        }

    }

    /**
     * Megnézzük, hogy került-e kivezetésre dolgozó. Ha igen, akkor történetiséggel lezárjuk.
     */
    public function searchDeletedEmployee(){

        foreach($this->sync_config as $row){

			$skip = ['ros_new_employee', 'eurest_abs', 'corinthia_upload', 'automotive', 'industrie', 'invest'];

			if(!in_array($row['sync_process_id'], $skip)){

            //if( ($row['sync_process_id'] != 'ros_new_employee') AND ($row['sync_process_id'] != 'eurest_abs')  AND ($row['sync_process_id'] != 'corinthia_upload') ){

                if($row['file_containt'] == 'nd'){ //ennek csak akkor kell futni, ha "new data" üzemmódban van a sync_process_id

                    $tmp = [];
                    $current = [];

                    // TMP táblában lévő adatok
                    $tmp_data = $this->getTmpTableRows($row['tmp_table_name'].'_'.$this->today);

                    if( (is_array($tmp_data)) AND (count($tmp_data)) ){

                        foreach($tmp_data as $td){
                            $tmp[] = $td['xls_employee_id'];
                        }

                    }


                    // már meglévő userek
					if(in_array($row['sync_process_id'], ['automotive', 'industrie', 'invest'])){

						$company = ['automotive' => '3', 'invest' => '2', 'industrie' => '1'];
						$current_data = $this->getEmployeesId(" AND e.company_id = '".$company[$row['sync_process_id']]."' ");

					}else{
						$current_data = $this->getEmployeesId();
					}

                    if( (is_array($current_data)) AND (count($current_data)) ){

                        foreach($current_data as $cd){
                            $current[] = $cd['employee_id'];
                        }

                    }



                    $deleted = array_diff($current, $tmp);


                    // ha van törölt user
                    if(count($deleted)){

						$deleted = 0;
                        foreach($deleted as $key => $del){

                            $sql = "UPDATE `employee_contract`
                                    SET `valid_to`    = CURDATE() - INTERVAL 1 DAY,
                                        `ec_valid_to` = CURDATE() - INTERVAL 1 DAY,
                                        `modified_by` = 'xls_importxx',
                                        `modified_on` = NOW()
                                    WHERE `employee_id` = '".$del."'
									AND ( CURDATE() BETWEEN valid_from AND valid_to ) AND  ( CURDATE() BETWEEN ec_valid_from AND ec_valid_to ); ";

                            dbExecute($sql);

							$deleted++;
                        }
						$this->writeLog($deleted.' rows deleted in '.$row['sync_process_id'], $row['sync_error_log_file']);
                    }

                }

            }

        }

    }


    /**
     * Szerződéssel rendelkező dolgozó ID-k lekérdezése
     *
     * @return bool|array
     */
    public function getEmployeesId($and = ''){

        $sql = "SELECT e.row_id, e.employee_id, e.company_id
                FROM employee e
                LEFT JOIN employee_contract ec ON ec.employee_id = e.employee_id AND ec.`status` = 2
                WHERE e.`status` = ".Status::PUBLISHED."
                AND e.valid_from < CURDATE()
                AND IFNULL(e.`valid_to`, '2038-01-01') >= CURDATE()
                AND ec.valid_from < CURDATE()
                AND IFNULL(ec.`valid_to`, '2038-01-01') >= CURDATE()
                AND ec.ec_valid_from < CURDATE()
                AND IFNULL(ec.`ec_valid_to`, '2038-01-01') >= CURDATE()
				".$and."
                ;";

        $return = false;

        try{

            $result = dbFetchAll($sql);

        }catch (Exception $e){

            //$this->writeLog($e->getMessage(), NULL);

        }

        if(count($result)){

            $return = $result;

        }

        return $return;

    }

    /**
     * Adatok áttöltése TMP táblába
     */
    public function loadDataToTmpTable(){

        foreach($this->sync_config as $row){

            switch($row['source_file_type']){

                case 'xls':
                case 'xlsx': $this->parseXlsAndXlsx($row); break;

                case 'csv' : $this->parseCsv($row); break;

            }

            //$sql = "UPDATE  `xls_userdata_20180112133332` SET xls_employee_id =  CONCAT(xls_emp_id, SUBSTR(xls_employee_company_tax, 1, 8)) WHERE 1=1";

        }

    }


    /**
     * XLS, XLSX feldolgozása
     *
     * $param array $data
     */
    public function parseXlsAndXlsx($data){

		$this->writeLog('parseXlsAndXlsx()',  $data['sync_error_log_file']);

        $file = new SpreadsheetReader($data['source_file_path'] . DS . $data['source_file_name']);

        if(count($file)){

            $data_rows = $this->getTmpTableData($data['sync_process_id'], $data['sync_error_log_file']);

            $insert = [];

            $x = [];

            foreach($data_rows as $dr){
                array_push($x, $dr['create_col_name']);
            }

            $insert_num = 0;

            $row_count = 1;

            foreach($file as $key => $row){

                // a kimaradó sorokat nem töltjük be
                if($row_count > $data['source_file_skipped_rows']){

                    /*
                    $rr = '';

                    foreach ($row as $r) {
                        $rr .= $r;
                    }

                    $row[] = hash('sha512', $rr);
                    */

                    // rosinál nem mindíg jött át a valid_to..
                    // ezért ha kevesebb az oszlopok száma akkor felviszünk az üres részeknek egy ''-et
                    if( ($data['sync_process_id'] == 'ros_e_abs') ){
                        $row[4] = (int)$row[4];
						$row[6] = date('Y-m-d', strtotime($row[6]));
						if($row[7] != '' ){ $row[7] = date('Y-m-d', strtotime($row[7])); }

                    }

					if(count($x) != (count($row) +1) ) {

						if( count($x) > (count($row) +1) ){

							$dif = count($x) - (count($row) +1);

							for($i=0; $i<$dif; $i++){
								$row[] = '';
							}

						}

					}

					if($data['sync_process_id'] == 'pyrus'){
						if($row[0] != ''){
							$row[] = substr( md5($row[0].$row[10].$row[11].rand(100, 10000)), 0, 12);
							$name = explode(" ", $row[0]);
							$row[] = $name[0];
							$row[] = $name[1];
						}else{
							$row[] = '';
							$row[] = '';
							$row[] = '';
						}
					}

					if($data['sync_process_id'] == 'leg_empl'){
						$row[6] = substr($row[6], 6, 4)."-".substr($row[6], 0, 2)."-".substr($row[6], 3, 2);
						$row[11] = substr($row[11], 6, 4)."-".substr($row[11], 0, 2)."-".substr($row[11], 3, 2);
					}

                    $row[] = $this->createHash($row, $data['source_file_type'], $data['hash_skipped_col']);


					if( count($x) == count($row) ){

						$insert[] = array_combine($x, $row);

						$insert_num++;

					}else{
						$this->writeLog('Eltérő adatok array_combine! $x = ('. json_encode($x).') $row = ('. json_encode($row).') ', $data['sync_error_log_file']);
					}

                    // a megadott sorok utána után bedobjuk az adatbázisba
                    if($insert_num >= $this->multi_insert_rows){

                        $this->multiInsert($data['tmp_table_name'].'_'.$this->today, $insert);

                        $insert_num = 0;
                        $insert = [];

                    }

                }

                $row_count ++;

            }

            if(count($insert) > 0){
                $this->multiInsert($data['tmp_table_name'].'_'.$this->today, $insert);
            }

        }else{

            $this->writeLog('Nincs adat a megadott xlsx fájlban ('.$data['source_file_path'] . DS . $data['source_file_name'].')', $data['sync_error_log_file']);

        }

    }


    /**
     * CSV feldolgozása
     *
     * @param array $data
     */
    public function parseCsv($data){

		$this->writeLog('parseCsv()',  $data['sync_error_log_file']);
        // $loadedCsv = new  SpreadsheetReader_CSV(, array('Delimiter' => $file['csv_delimiter']));

        $file = new SpreadsheetReader($data['source_file_path'] . DS . $data['source_file_name']);

        if(count($file)){

            $data_rows = $this->getTmpTableData($data['sync_process_id'], $data['sync_error_log_file']);

            $insert = [];

            $x = [];
			$y = [];
            foreach($data_rows as $dr){
                array_push($x, $dr['create_col_name']);
				array_push($y, $dr['sort2']);
            }

            $insert_num = 0;
            $row_count = 1;

            foreach($file as $key => $row){

                // a kimaradó sorokat nem töltjük be
                /*
                if( ($row['source_file_skipped_rows'] != NULL ) AND ($key <= $row['source_file_skipped_rows']) ) {
                    continue;
                }
                */
                if( (is_array($row)) AND ( count($row)) ){

                    if($row_count > $data['source_file_skipped_rows']){

                        foreach($row as $key2 => $r){
//                            $iv = iconv('ISO-8859-1', 'UTF-8', $r);
//                            $row[$key2] = $iv;

							if(in_array($key2, $y)){
								$iv = iconv('ISO-8859-1', 'UTF-8', $r);
								$row[$key2] = $iv;
							}else{
								unset($row[$key2]);
							}

                        }

                        /*
                        $rr = '';
                        // csv miatt kell iconv


                        $row[] =  hash('sha512', $rr);
                        */
						if( ($data['sync_process_id'] != 'trigo_antifog') AND ($data['sync_process_id'] != 'eurest_employee') AND ($data['sync_process_id'] != 'eurest_abs') ){
                            $row[] = $row[0].substr($row[12], 0, 8);
                        }


						if($data['sync_process_id'] == 'leg_reg'){
							if(isset($row[4])){
								unset($row[4]);
							}
						}

                        $row[] = $this->createHash($row, $data['source_file_type'], $data['hash_skipped_col']);

                        if( count($x) == count($row) ){

                            $insert[] = array_combine($x, $row);

                            $insert_num++;

                        }else{
                            $this->writeLog('Eltérő adatok array_combine! $x = ('. json_encode($x).') $row = ('. json_encode($row).') ', $data['sync_error_log_file']);
                        }

                        // a megadott sorok után bedobjuk az adatbázisba
                        if($insert_num >= $this->multi_insert_rows){


                            $this->multiInsert($data['tmp_table_name'].'_'.$this->today, $insert);

                            $insert_num = 0;
                            $insert = [];

                        }


                    }

                    $row_count ++;

                }

            }

            if(count($insert) > 0){
                $this->multiInsert($data['tmp_table_name'].'_'.$this->today, $insert);
            }

        }else{

            $this->writeLog('Nincs adat a megadott csv fájlban ('.$data['source_file_path'] . DS . $data['source_file_name'].')', $data['sync_error_log_file']);

        }

    }


    /**
     * HASH generálása
     *
     * @param array $data adott sor amiből generálunk
     * @param string $source_file_type csv vagy xls,xlsx
     * @param string $skipped_col ezeket az oszlopokat ne vegye bele a generálásba
     * @return string
     */
    public function createHash($data, $source_file_type, $skipped_col=''){

        $skipped_col = str_replace(" ", "", $skipped_col);
        $skipped_array = explode(',', $skipped_col);
        $rr = '';

        foreach ($data as $key => $r){

            if(!in_array($key, $skipped_array)){

                if($source_file_type == 'csv'){
                    $rr .= iconv('ISO-8859-1', 'UTF-8', $r);
                }else{
                    $rr .= $r;
                }

            }

        }

        $return = hash('sha512', $rr);

        return $return;

    }

    /**
     * Kötelező elemek ellenőrzése. Ha hiányzik egy kötelező mező, akkor
     * azt a sort töröljük a feldolgozásból, és hibaüzenetet loggolunk.
     *
     */
    public function checkRequireElements(){

        foreach($this->sync_config as $sys_config){

            $data_rows = $this->getTmpTableData($sys_config['sync_process_id'], $sys_config['sync_error_log_file']);

            $require = [];

            foreach($data_rows as $dr){
                if($dr['require'] == 'i'){
                    array_push($require, $dr['create_col_name']);
                }
            }
            if(count($require)){

                $table_name = $sys_config['tmp_table_name'].'_'.$this->today;

                $tmp_data = $this->getTmpTableRows($table_name, $require);

                if( (is_array($tmp_data)) AND (count($tmp_data)) ){

                    $sql = '';

                    foreach($tmp_data as $tmp_row){


                        foreach($require as $r){

                            if( ($tmp_row[$r] == '') OR (strlen($tmp_row[$r]) == 0) ){

                                $sql .= 'UPDATE '.$table_name.' SET `valid_row` = "n" WHERE id = "'.$tmp_row['id'].'"; ';

								$this->writeLog('Hiányzó adat(ok) a kötelező mező(k)ben! tábla:'.$table_name.' sor: ('.$tmp_row['id'].') adatok: ('.serialize($tmp_row).') ', $sys_config['sync_error_log_file']);

							}

                        }

                    }

                    if($sql != ''){
                        dbExecute($sql);
                    }

                }

            }

        }

    }


    /**
     * TMP táblából lekéri a sorokat. A kötelező mezők ellenérzésénél is ezt használjuk,
     * így a második paraméterben egy tömböt adhatunk át. Ha ezt átadjuk akkor
     * csak ezekre a sorokra szűrve kapjuk meg a találatokat.
     * Ha a teljes tábla tartalom kell,
     * akkor nem kell foglalkozni a második paraméterrel.
     *
     * @param string $table_name
     * @param bool|array $cols
     * @return bool
     */
    public function getTmpTableRows($table_name, $cols = false){

        $and = ' WHERE 1=1 ';

        // require ellenőrzésnél használjuk ezt a részét
        if($cols !== false){

            if( (is_array($cols)) AND (count($cols)) ){

                foreach($cols as $c){

                    // if($and == ''){ $and .= ' WHERE '; }else{ $and .= ' OR '; }

                    $and .= ' OR ( '.$c.' = "" OR '.$c.' IS NULL  )' ;

                }

            }

        }

        $return = false;

        $sql = "SELECT *
                FROM `".$table_name."`
                ".$and. "
                AND valid_row = 'y' ";

        try{

            $result = dbFetchAll($sql);

        }catch (Exception $e){

            //$this->writeLog($e->getMessage(), NULL);

        }

        if(count($result)){

            $return = $result;

        }

        return $return;

    }


    /**
     * Insert data to table
     *
     * @param string $table
     * @param array $data
     */
    public function multiInsert($table='', $data=[]){

        dbMultiInsert($table,$data);

    }


    /**
     * Adatok áttöltése SITE táblába
     */
    public function loadDataToSiteTable(){

        foreach ($this->sync_config as $key => $row) {

			$this->writeLog('loadDataToSiteTable()'.$row['sync_process_id'],  $row['sync_error_log_file']);

            $tmp_table = $row['tmp_table_name'].'_'.$this->today;

			$tmp_table_rows_count = $this->getInsertedRowsCount("SELECT * FROM ".$tmp_table, $row['sync_error_log_file']);

            $import_data = $this->getImportDataById($row['sync_process_id'], $row['sync_error_log_file']); // xls_sync_2_config_import

			if( (is_array($import_data)) AND (count($import_data)) ) {

                foreach ($import_data as $key => $i_d) {

                    if(substr($i_d['sql'], 0, 6) == 'SELECT'){

                        $sql = str_replace('##CHANGETHEREALNAME##', $tmp_table, $i_d['sql']);

                    }else{

                        $sql = "SELECT * FROM ".str_replace('##CHANGETHEREALNAME##', $tmp_table, $i_d['sql']);

                    }

                    $tmp_result = false;

                    try{

                        $tmp_result = dbFetchAll($sql);

                    }catch (Exception $e){

                        $this->writeLog($e->getMessage(), $row['sync_error_log_file']);

                    }

                    $insert = '';

                    $select = '';

                    if( (is_array($tmp_result)) AND ( count($tmp_result)) ){

                        $insert_rows = $this->getInsertRowsById($i_d['row_id'], $row['sync_error_log_file']); // xls_sync_2_insert_table

                        $hash = [];

                        if ((is_array($insert_rows)) AND (count($insert_rows))) {

                            foreach ($insert_rows as $key2 => $ir) {

                                if($insert == '') {
                                    $insert = 'INSERT INTO ' . $ir['target_table_name'] . ' (';
                                }

                                $insert .= '`'.$ir['target_col_name'].'`,';

                                $select .= ''.$ir['value'].' as '.$ir['target_col_name'].',';

                            }



                            $select = trim($select, ",");

                            if(substr($sql, 0, 13) == 'SELECT * FROM'){
                                $hash_sql = $sql;
                            }else{
                                $hash_sql = 'SELECT * '.substr($sql, strpos($sql, 'FROM'));
                            }


                            $sql = str_replace("SELECT * FROM", "SELECT ".$select." FROM", $sql);

                            $insert = trim($insert, ",");
                            $insert .= ') '.$sql.";";

							$inserted_rows = $this->getInsertedRowsCount($sql, $row['sync_error_log_file']);

							if(isset($_GET['temondhogyrablotamadas'])){
								print_r($insert);
								print_r('<br><br>');
								print_r($inserted_rows.' rows inserted ( '.$tmp_table_rows_count.' tmp table rows)');
								print_r('<br><br>');
								print_r('------------<br><br>');
							}

							dbExecute($insert);


                            $insert = '';
                            $sql = '';
                            $select = '';

                            //$this->insertHashToTable($hash_sql, $row['sync_process_id']);

                            $this->insertHashToTable($tmp_table, $row['sync_process_id']);

                        }

                    }

                }

            }else{
                $this->writeLog('Az xls_sync_config_import táblában nem volt semmi. (sync_process_id: '.$row['sync_process_id'].') ', $row['sync_error_log_file']);
            }

        }

    }


	public function getInsertedRowsCount($sql, $sync_error_log_file){

		$return = 0;

		try {

            $result = dbFetchAll($sql);

        } catch (Exception $e) {

            $this->writeLog($e->getMessage(), $sync_error_log_file);

        }


        if(count($result)){

            $return = count($result);

        }

        return $return;

	}


	/**
     * Adatok beírása a hash táblába
     *
     * @param string $atble
     * @param strikg $sync_process_id
     */
    public function insertHashToTable($table, $sync_process_id){

        $insert_sql = " INSERT INTO ".XLS_SYNC_HASH." (`hash`, `sync_process_id`)

                        SELECT xls.xls_hash, '".$sync_process_id."'

                        FROM `".$table."` xls

                        WHERE 1=1
                        AND xls.`valid_row` = 'y'
                        AND xls.`duplicate_row` = 'n'
                        AND xls.xls_hash_used = 'n'";

        try {

            dbExecute($insert_sql);

        } catch (Exception $e) {

            //$this->writeLog($e->getMessage(), NULL);

        }

        /*
        try {
            $rows = dbFetchAll($sql);
        } catch (Exception $e) {
            $this->writeLog($e->getMessage());
        }

        if( (is_array($rows)) AND ( count($rows)) ){

            foreach ($rows as $row) {
                $insert_sql = "INSERT INTO ".XLS_SYNC_HASH." (`hash`, `sync_process_id`)
                                SELECT *
                                FROM (
                                SELECT '".$row['xls_hash']."', '".$sync_process_id."') AS tmp
                                WHERE NOT EXISTS (
                                SELECT `hash`
                                FROM ".XLS_SYNC_HASH."
                                WHERE `hash` = '".$row['xls_hash']."')
                                LIMIT 1;";
                try {
                    dbExecute($insert_sql);
                } catch (Exception $e) {
                    $this->writeLog($e->getMessage());
                }
            }

        }
        */
    }


    /**
     * Azoknak a hash-ek keresése amik még nem léteznek
     *
     * @param string $tmp_table-name
     * @return array
     */
    public function getHashDiff($tmp_table_name){

        $return = false;

        $sql = "SELECT xls.xls_hash
                FROM ".$tmp_table_name.'_'.$this->today." xls
                LEFT JOIN ".XLS_SYNC_HASH." h ON h.`hash` != xls.xls_hash
                WHERE h.`hash` IS NOT NULL";

        try {

            $result = dbFetchAll($sql);

        } catch (Exception $e) {

           // $this->writeLog($e->getMessage(), NULL);

        }


        if(count($result)){

            $return = $result;

        }

        return $return;


    }

    /**
     * SITE táblákba való beszúrások lekérdezése
     *
     * @param int $sync_process_id
     * @return mixed
     */
    public function getInsertRowsById($sync_process_id, $sync_error_log_file){

        $return = false;

        $sql = "SELECT *
                FROM `".XLS_SYNC_INSERT_TABLE."`
                WHERE `status`='" . Status::PUBLISHED . "'
                AND config_import_row_id = '".$sync_process_id."'
                ORDER BY `sort`";

        try{

            $result = dbFetchAll($sql);

        }catch (Exception $e){

            $this->writeLog($e->getMessage(), $sync_error_log_file);

        }

        if(count($result)){

            $return = $result;

        }

        return $return;

    }


    /**
     * Adatok lekérdezése sync_process_id alapján
     *
     * @param string $sync_process_id
     * @return mixed
     */
    public function getImportDataById($sync_process_id, $sync_error_log_file){

        $return = false;

        $sql = "SELECT *
                FROM `".XLS_SYNC_CONFIG_IMPORT."`
                WHERE `status`='" . Status::PUBLISHED . "'
                AND sync_process_id = '".$sync_process_id."'
                ORDER BY `sort`";

        try{

            $result = dbFetchAll($sql);

        }catch (Exception $e){

            $this->writeLog($e->getMessage(), $sync_error_log_file);

        }

        if(count($result)){

            $return = $result;

        }

        return $return;

    }


    /**
     * Lekérjük a config tábla tartalmát (amelyik status = 2, és valid_from valid_to is rendben van)
     *
     */
    public function getConfigData(){

        $sql = "SELECT *
                FROM `".XLS_SYNC_CONFIG."`
                WHERE `status`='" . Status::PUBLISHED . "'
                AND `valid_from` < NOW()
                AND `valid_to` > NOW()";

        try{

            $this->sync_config = dbFetchAll($sql);

        }catch (Exception $e){

           // $this->writeLog($e->getMessage(), NULL);

        }


    }

    /**
     * Ellenőrizzük, hogy a feltöltött fájlok megvannak-e
     */
    public function checkFile(){

        foreach ($this->sync_config as $key => $row) {

			$this->writeLog('checkFile', $row['sync_error_log_file']);

            if ($row['multi_or_single'] == 's') {

                if (!file_exists($row['source_file_path'] . DS . $row['source_file_name'])) {

                    // ha nem létezik ez a file akkor kiszedjük a feldolgozásból
                    unset($this->sync_config[$key]);

                    //és írunk egy hibát róla
                    $this->writeLog('Hiányzó file: ( ' . $row['source_file_path'] . $row['source_file_name'].' )', $row['sync_error_log_file']);

					$this->checkAvalibeFiles($row);

                }

            } else {

                // MULTI ROWS ESETÉN MINDEN KAPCSOLODÓ FILE-T IS MEG KELL VIZSGÁLNI

                if($row['multi_file_row_ids'] > 0){

                    $files = explode(",", $row['multi_file_row_ids']); //

                    $files_ok = true;

                    foreach ($files as $file) {

                        $data = $this->getConfigDataById($file, $row['sync_error_log_file']);

                        if (!file_exists($data['source_file_path'] . DS . $data['source_file_name'])) {

                            $files_ok = false;

                            // Szülő file törlése
                            unset($this->sync_config[$key]);

                        }

                    }

                    // HA valamelyik file hiányzik, akkor az össes sort töröljük a feldolgozásból
                    if($files_ok == false){

                        foreach ($files as $file) {

							// kapcsolat törlése
							unset($this->sync_config[$file]);

                        }

                    }

                }


            }

        }

    }

	/**
	 * Mappában lévő fájlok listázása
	 * Ez azért kell, mert pl rosinál sokszor volt, hogy nem olyan néven, vagy kiterjesztéssel tették fel a fájlt
	 * mint ahogy meg volt beszélve.
	 *
	 * @param array $data
	 * @param string $sync_error_log_file
	 */
	public function checkAvalibeFiles($data){

		$this->writeLog('checkAvalibeFiles()', $data['sync_error_log_file']);

		$this->writeLog('Elvárt file ['.$data['source_file_name'].'] ', $data['sync_error_log_file']);

		$search_extensions = ['csv', 'xls', 'xlsx'];

		foreach ($search_extensions as $extension) {

			$files = glob($data['source_file_path']."*.".$extension);

			if( count($files) > 0 ){

				foreach ($files as $file) {

					$this->writeLog('Elérhető ['.$extension.'] file : ' . $file, $data['sync_error_log_file']);

				}

			}else{

				$this->writeLog('Nincs elérhető ['.$extension.'] file ', $data['sync_error_log_file']);

			}

		}

	}

	/**
     * Konfigurációs beállítás lekérdezése ID alapján
     *
     * @param int $id
     * @return mixed
     */
    public function getConfigDataById($id, $sync_error_log_file){

        $return = false;

        $sql = "SELECT *
                FROM `".XLS_SYNC_CONFIG."`
                WHERE `row_id` = '" . $id . "'
                ";

        try{

            $result =  dbFetchRow($sql);

        }catch (Exception $e){

            $this->writeLog($e->getMessage(), $sync_error_log_file);

        }


        if(count($result)){

            $return = $result;

        }

        return $return;

    }


    /**
     * TMP táblák létrehozása
     */
    public function createTmpTable(){

        foreach($this->sync_config as $row){

            $table_data = $this->getTmpTableData($row['sync_process_id'], $row['sync_error_log_file']);

            if( (is_array($table_data)) AND (count($table_data)) ){

                foreach($table_data as $td) {

                    $sql_tmp = "CREATE TABLE IF NOT EXISTS `" . $row['tmp_table_name'] . '_' . $this->today . "`
                                ( `id` int(11) NOT NULL AUTO_INCREMENT,
                                  `valid_row` CHAR(1) NOT NULL DEFAULT 'y' COLLATE 'utf8_unicode_ci',
                                  `duplicate_row` CHAR(1) NOT NULL DEFAULT 'n' COLLATE 'utf8_unicode_ci',
                                  ";

                    $table_rows = $this->getTmpTableData($row['sync_process_id'], $row['sync_error_log_file'], $td['sort']);

                    if ((is_array($table_data)) AND (count($table_data))) {

                        foreach ($table_rows as $tr) {

                            if (($tr['create_col_name'] != NULL) AND ($tr['create_col_type'] != NULL)) {

                                $sql_tmp .= " `" . $tr['create_col_name'] . "` " . $tr['create_col_type'] . ", ";

                            }

                        }


                        $sql_tmp .= "`xls_hash_used` CHAR(1) NOT NULL DEFAULT 'i' COLLATE 'utf8_unicode_ci',
                                    PRIMARY KEY (`id`)
                                ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COLLATE = utf8_unicode_ci";


                        try {

                            dbExecute($sql_tmp);

                        } catch (Exception $e) {

                            $this->writeLog('Nem sikerült létrehozni a tmp táblát: ' . $row['tmp_table_name'] . ' sync_process_id: ' . $row['sync_process_id'], $row['sync_error_log_file']);

							$this->writeLog($e->getMessage(), $row['sync_error_log_file']);

                        }

                    }else{

                        $this->writeLog('Hiányzó TMP tábla sorok konfiguráció  sort:'.$td['sort'].' sync_process_id: ' . $row['sync_process_id'], $row['sync_error_log_file']);

                    }

                }

            }else{

                $this->writeLog('Hiányzó TMP tábla beállítás a(z) '.$row['sync_process_id'].' nevű sync_process_id-n', $row['sync_error_log_file']);

            }

        }

    }



    /**
     * TMP tábla létrehozásának adatai lekérdezése sync_precess_id alapján
     *
     * @param string $sync_process_id
     * @param array|bool $sort
     *
     * @return bool
     */
    public function getTmpTableData($sync_process_id, $sync_error_log_file, $sort = false){

        $return = false;

        $and = '';

        if($sort != false){
            $and = " AND sort = ".$sort." ";
        }

        $sql = "SELECT *
                FROM `".XLS_SYNC_TMP_TABLE_ROWS."`
                WHERE sync_process_id = '".$sync_process_id."'
                AND status = '".Status::PUBLISHED."'
                ".$and."
                ORDER BY sort, sort2";

        try{

            $result =  dbFetchAll($sql);

        }catch (Exception $e){

            $this->writeLog($e->getMessage(), $sync_error_log_file);

        }

        if(count($result)){

            $return = $result;

        }

        return $return;


    }



	 /**
     * Prefix SQL, és function-ok meghívása futtatása
     */
    public function afterLoadedTempProcess(){

		foreach($this->sync_config as $row){
			$this->writeLog('afterLoadedTempProcess()', $row['sync_error_log_file']);
			$datas = $this->getAfterLoadedTempProcessData($row['sync_process_id'], $row['sync_error_log_file']);

			if( (is_array($datas)) AND (count($datas)) ){

				foreach ($datas as $data){

					// ha egy sql utasítást kell futtatni
					if($data['type'] == 'sql'){

						if($data['value'] != ''){

							if( strpos($data['value'], '##CHANGETABLENAME##') !== FALSE){
								$data['value'] = str_replace("##CHANGETABLENAME##", $row['tmp_table_name']."_".$this->today, $data['value']);
							}

							//$this->writeLog('afterLoadedTemp SQL:('.$data['value'].')', $row['sync_error_log_file']);

							try{

								dbExecute($data['value']);

							}catch (Exception $e){

								$this->writeLog($e->getMessage(), $row['sync_error_log_file']);

							}

						}else{

							$this->writeLog('Az SQL utasítás üres. Nem lehet mit futtatni. ( row_id: '.$data['row_id'].') ', $row['sync_error_log_file']);

						}

					}

					// ha egy function-t kell meghívni
					if($data['type'] == 'function'){

						$this->writeLog('prefixProcess function ('. $data['value'].') ', $row['sync_error_log_file']);

						if(!is_null($data['value'])){
							call_user_func_array( ['ExcelImport2Controller', $data['value']], [$data, $row] );
						}else{
							$this->writeLog('xls_sync_2_after_load_tmp táblában value mezős üres!', $row['sync_error_log_file']);
						}


					}

				}

			}
		}

    }

	/**
	 *
	 * @return type
	 */
	public function getAfterLoadedTempProcessData($sync_process_id, $sync_error_log_file){

		$this->writeLog('getAfterLoadedTempProcessData()', $sync_error_log_file);

		$return = false;

        $sql = "SELECT *
                FROM `".XLS_SYNC_AFTER_LOAD_TMP."`
                WHERE sync_process_id = '" . $sync_process_id . "'
                AND `status` = " . Status::PUBLISHED . "
                AND `valid_from` <= CURDATE()
                AND `valid_to`   >= CURDATE()
                ORDER BY sort
                ";

		//$this->writeLog('SQL:('.$sql.')', $sync_error_log_file);

		$result = FALSE;

        try {

            $result = dbFetchAll($sql);

        } catch (Exception $e) {


            $this->writeLog($e->getMessage(), $sync_error_log_file);


        }

        if (count($result)) {
            $return = $result;
        }

        return $return;

	}

    /**
     * Fájlok átmozgatásas sikeres feldolgozás után
     */
    public function moveFiles(){

		$ros = ['ros_e_abs', 'ros_e_abs_2'];

        foreach($this->sync_config as $row){

			if( !in_array($row['sync_process_id'], $ros) ){

				if(!is_dir($row['source_file_path'] . "oldFiles" . DS . date('Y') . DS . date('m'))){
					self::checkDir($row['source_file_path'] . "oldFiles" . DS . date('Y') . DS . date('m'));
				}

				rename($row['source_file_path'] . $row['source_file_name'], $row['source_file_path'] .'oldFiles' . DS . date('Y') . DS . date('m') . DS . date('Ymd_Hi') . '_' .$row['source_file_name']);

				if(file_exists($row['source_file_path'] . $row['source_file_name'])){
					unlink($row['source_file_path'] . $row['source_file_name']);
				}

			}else{

				if(isset($row['archive_folder'])){

					if($row['archive_folder'] != ''){

						// rosi archive mappa ellenőrzés és létrehozása
						if(!is_dir($row['archive_folder'] . DS . date('Y') . DS . date('m'))){
							self::checkDir($row['archive_folder'] . DS . date('Y') . DS . date('m'));
						}

						copy($row['source_file_path'] . $row['source_file_name'], $row['archive_folder'] . DS . date('Y') . DS . date('m') . DS . date('Ymd_Hi') . '_' .$row['source_file_name']);

					}else{

						$this->writeLog('Nincs konfiguralva az archive_folder oszlop a config tablaban.', $row['sync_error_log_file']);

					}

				}else{

					$this->writeLog('Nincs archive_folder oszlop a config tablaban.',  $row['sync_error_log_file']);

				}

			}

			if(file_exists($row['source_file_path'] . $row['source_file_name'])){
				unlink($row['source_file_path'] . $row['source_file_name']);
			}

        }

    }


    /**
     * Ha van log file elküldjük emailbe
     */
    public function sendLogEmail(){


        if(isset($_GET['temondhogyrablotamadas'])){

            print_r('browser mode...');

            echo "<br>";

            print_r($this->full_error_log);

        }else{

            $messageSubject = 'Hiba a sync feldogozás alatt!';

            $emailSender = new EmailSender();
            $emailSender->sendMail(
                [
                    'addr' => [
                        [
                            'email'	=> $this->send_email_to,
                            'name'  => 'TTWA dev'
                        ],
                    ],
                ],
                $messageSubject,
                $this->full_error_log,
				[],
				'',
				[],
				FALSE,
				"",
				\TRUE

            );

            return;
        }

    }


    /**
     * Korábbi tmp táblák törlése
     */
    public function deleteOldTmpTables(){

        preg_match("/dbname=([^;]*)/", dbConnectionString(), $matches);

        $currentdb = $matches[1];

        foreach($this->sync_config as $row){

            $sql = "SELECT table_name AS name
                      FROM information_schema.tables
                      WHERE table_type = 'BASE TABLE'
                      AND table_schema='$currentdb'
                      AND TABLE_NAME LIKE '".$row['tmp_table_name']."%'
                      ORDER BY create_time";


            $searchDropableTablesSelect = dbFetchAll($sql);

            $dropTableSql = "";

            if(count($searchDropableTablesSelect) > $this->previus_tmp_tables){

                $tables = count($searchDropableTablesSelect);

                foreach ($searchDropableTablesSelect as $value) {

                    if($tables > $this->previus_tmp_tables){

                        $dropTableSql .= "DROP TABLE IF EXISTS  `".$value['name']."`".'; ';

                    }

                    $tables--;

                }

                if($dropTableSql != ''){
                    dbExecute($dropTableSql);
                }

            }

        }

    }


    /**
     * Write log file
     *
     * @param string $msg
     */
    private function writeLog($msg, $log_file=NULL){

		if(!is_null($log_file)){

			if(!is_dir($log_file)){
				self::checkDir($log_file);
			}

			$this->processing_error = true;

			$this->full_error_log .= date('Y-m-d H:i:s').' | ' . $msg . '<br>';

			$file = fopen($log_file.$this->sync_error_log_file, "a+");

			fwrite($file, date('Y-m-d H:i:s').' | '. $msg . PHP_EOL);

			fclose($file);

		}

    }


    /**
     * Könyvtár létrehozása ha még nem létezik
     *
     * @param string $path
     */
    function checkDir($path){

        //trim($path, DS);

        Yang::log($path, 'log', 'checkDir');
        mkdir($path, 0755, TRUE);

        /*
        $path = explode(DS, $path);
        $pre = '';
        foreach($path as $p){
            if (!is_dir($pre.$p)) {
                try{

                    if(isset($_GET['getfolders'])) {
                        var_dump($pre.$p).'<br>';
                        Yang::log($pre.$p, 'create folder', 'ExcelImport2Controller');
                    }

                   // mkdir($pre.$p, 0775);

                } catch (Exception $ex) {
                    Yang::log($ex->getMessage(), 'log', 'ExcelImportController');
                }

            }
            $pre .= $p.DS;
        }
        */
    }


	function akhDate($data_array=[], $config_array=[]){

		$this->writeLog('akhDate()',  $config_array['sync_error_log_file']);

		$sql = "SELECT *
				FROM ".$config_array['tmp_table_name']."_".$this->today."
				;";

		$data = $this->getSqlResult($sql,  $config_array['sync_error_log_file']);

		$update = '';

		if( (is_array($data)) AND ( count($data) > 0) ){

			foreach ($data as $row) {

				$xls_valid_from = str_replace(".", "-", $row['xls_valid_from']);
				$xls_valid_to = '20'.substr(trim($row['xls_valid_to']), -2, 2).'-'.substr(trim($row['xls_valid_to']), 0, 5);
				$xls_abs_valid_from = '20'.substr(trim($row['xls_abs_valid_from']), -2, 2).'-'.substr(trim($row['xls_abs_valid_from']), 0, 5);
				$xls_abs_valid_to = '20'.substr(trim($row['xls_abs_valid_to']), -2, 2).'-'.substr(trim($row['xls_abs_valid_to']), 0, 5);

				$update .= "UPDATE ".$config_array['tmp_table_name']."_".$this->today." SET
							xls_valid_from = '".$xls_valid_from."',
							xls_valid_to = '".$xls_valid_to."',
							xls_abs_valid_from = '".$xls_abs_valid_from."',
							xls_abs_valid_to = '".$xls_abs_valid_to."'
							WHERE id = '".$row['id']."'
							;";

			}
		}


		if($update != ''){
			try{
				dbExecute($update);
			}catch (Exception $e){
				$this->writeLog($e->getMessage(),  $config_array['sync_error_log_file']);
			}
		}

	}

	function cascadeDate($data_array=[], $config_array=[]){

		$this->writeLog('cascadeDate()',  $config_array['sync_error_log_file']);

		$sql = "SELECT *
				FROM ".$config_array['tmp_table_name']."_".$this->today."
				;";

		$data = $this->getSqlResult($sql,  $config_array['sync_error_log_file']);

		$update = '';

		if( (is_array($data)) AND ( count($data) > 0) ){

			foreach ($data as $row) {

				$ut = '20'.substr(trim($row['xls_ut_kiadas']), -2, 2).'-'.substr(trim($row['xls_ut_kiadas']), 0, 5);

				$kov = '20'.substr(trim($row['xls_kov_kiadas']), -2, 2).'-'.substr(trim($row['xls_kov_kiadas']), 0, 5);


				$update .= "UPDATE ".$config_array['tmp_table_name']."_".$this->today." SET
							xls_ut_kiadas = '".$ut."',
							xls_kov_kiadas = '".$kov."'
							WHERE id = '".$row['id']."'
							;";

			}
		}

		if($update != ''){
			try{
				dbExecute($update);
			}catch (Exception $e){
				$this->writeLog($e->getMessage(),  $config_array['sync_error_log_file']);
			}
		}

	}


	/**
	 * EUREST dolgozói betöltésnél
	 * megkeressük a mai file-t
	 */
	public function getLastFileByFileName($data_array=[], $config_array=[]){

		$this->writeLog('getLastFileByFileName()', $config_array['sync_error_log_file']);

		// prod
        $p = $this->root_path.'..'.DS.'ftp'.DS.'eurest_ftp_user'.DS;
		$server = 'prod';

		// stage
		if(strpos($_SERVER['HTTP_HOST'], 'eurest.stage') !== FALSE ){
            $p = $this->root_path.'..'.DS.'ftp'.DS.'eurest'.DS;
            //$p = '/var/www/base.stage-ttwa.login.hu/app/ftp/eurest/';
			$server = 'stage';
        }

		// localhost
		if(strpos($_SERVER['HTTP_HOST'], 'eurest.localhost.hu') !== FALSE){
            $p = $this->root_path.'..'.DS.'ftp'.DS.'eurest'.DS;
            //$p = 'C:\xampp\htdocs\Login\TTWA\ftp\eurest\\';
			$server = 'local';
        }

		if(isset($_GET['temondhogyrablotamadas'])){
			var_dump($p);
			var_dump($server);
		}

		$files = glob($p."*.".$config_array['source_file_type']);

		$last = NULL;
		$prev = NULL;

		if( (is_array($files)) AND (count($files) > 0) ){

			foreach($files as $file){

				// ez a fájl feltöltési ideje alapján veszi a legfrisebbet..
				// mivel azt akarjuk feldolgozini
				// ha ez így nem jó akkor a file nevével kell játszani. (date('Y-m-d', strtotime(substr($pi['filename'], -8, 8)));
				// eurestnél pl ez így néz ki: login_dolgozoi_export_20180517.csv

				$pi = pathinfo($file);
				$file_time = filemtime($file);

				if(substr($pi['filename'], 0, 21) === 'login_dolgozoi_export' ){

					// Ha valamiért nincs filetime akkor a fájl nevéből vesszük hogy mikori
					if( (int)$file_time === 0){
						$file_time = strtotime(substr($pi['filename'], -8, 8));
					}

					if((int)$file_time > 0){

						if($prev === NULL){

							$prev = $file_time;
							$last = $pi['basename'];

						}else{

							if($prev < $file_time){

								$prev = $file_time;
								$last = $pi['basename'];

							}

						}

					}

				}

			}

		}else{

			if(isset($_GET['temondhogyrablotamadas'])){
				var_dump('eurest ... nincs file');
				var_dump($p);
				var_dump($files);
			}

		}

		if(!is_null($last)){

			// töröljük a korábbi file-t a feldolgozás helyéről ha létezik
			if(file_exists($this->root_path.$config_array['source_file_path'].$config_array['source_file_name'])){
				unlink($this->root_path.$config_array['source_file_path'].$config_array['source_file_name']);
			}

			// archive mappa ellenőrzése és létrehozása
			if(!file_exists($this->root_path.$config_array['archive_folder'].date('Y').DS.date('m'))){
				$this->checkDir($this->root_path.$config_array['archive_folder'].date('Y').DS.date('m'));
			}

			// archive mappába rakjuk a file-t
			if(file_exists($p.$last)){
				copy($p.$last, $this->root_path.$config_array['archive_folder'].date('Y').DS.date('m').DS.$config_array['sync_process_id'].'_'.date('Ymd_His').'.'.$config_array['source_file_type']);
			}

			// átnevezzük a feldolgozási névre
			if(file_exists($p.$last)){

				if($server == 'stage'){

					copy($p.$last, $this->root_path.$config_array['source_file_path'].$config_array['source_file_name']);

				}elseif($server == 'prod'){

					copy($p.$last, $this->root_path.$config_array['source_file_path'].$config_array['source_file_name']);

				}else{

					rename($p.$last, $this->root_path.$config_array['source_file_path'].$config_array['source_file_name']);

				}

				// majd töröljük az eredetit
				if(file_exists($p.$last)){
					unlink($p.$last);

				}

			}

		}

	}


	/**
	 * EUREST Szabadság betöltésnél
	 * megkeressük a mai file-t
	 */
	public function getLastAbsFileByFileName($data_array=[], $config_array=[]){

		$this->writeLog('getLastFileByFileName()', $config_array['sync_error_log_file']);

		// prod
		$p = $this->root_path.'..'.DS.'ftp'.DS.'eurest_ftp_user'.DS;
        //$p = '/var/www/ttwa-base.login.hu/app/ftp/eurest_ftp_user/';
		$server = 'prod';

		// stage
		if(strpos($_SERVER['HTTP_HOST'], 'eurest.stage') !== FALSE ){
            $p = $this->root_path.'..'.DS.'ftp'.DS.'eurest'.DS;
            //$p = '/var/www/base.stage-ttwa.login.hu/app/ftp/eurest/';
			$server = 'stage';
        }

		// localhost
		if(strpos($_SERVER['HTTP_HOST'], 'eurest.localhost.hu') !== FALSE){
            $p = $this->root_path.'..'.DS.'ftp'.DS.'eurest'.DS;
            //$p = 'C:\xampp\htdocs\Login\TTWA\ftp\eurest\\';
			$server = 'local';
        }

		if(isset($_GET['temondhogyrablotamadas'])){
			var_dump($p);
			var_dump($server);
		}

		$files = glob($p."*.".$config_array['source_file_type']);

		$last = NULL;
		$prev = NULL;

		if( (is_array($files)) AND (count($files) > 0) ){

			foreach($files as $file){

				// ez a fájl feltöltési ideje alapján veszi a legfrisebbet..
				// mivel azt akarjuk feldolgozini
				// ha ez így nem jó akkor a file nevével kell játszani. (date('Y-m-d', strtotime(substr($pi['filename'], -8, 8)));
				// eurestnél pl ez így néz ki: login_szabadsag_export_20180517.csv

				$pi = pathinfo($file);
				$file_time = filemtime($file);

				if(substr($pi['filename'], 0, 22) === 'login_szabadsag_export' ){

					// Ha valamiért nincs filetime akkor a fájl nevéből vesszük hogy mikori
					if( (int)$file_time === 0){
						$file_time = strtotime(substr($pi['filename'], -8, 8));
					}

					if((int)$file_time > 0){

						if($prev === NULL){

							$prev = $file_time;
							$last = $pi['basename'];

						}else{

							if($prev < $file_time){

								$prev = $file_time;
								$last = $pi['basename'];

							}

						}

					}

				}

			}

		}else{

			if(isset($_GET['temondhogyrablotamadas'])){
				var_dump('eurest ... nincs file');
				var_dump($p);
				var_dump($files);
			}

		}

		if(!is_null($last)){

			// töröljük a korábbi file-t a feldolgozás helyéről ha létezik
			if(file_exists($this->root_path.$config_array['source_file_path'].$config_array['source_file_name'])){
				unlink($this->root_path.$config_array['source_file_path'].$config_array['source_file_name']);
			}

			// archive mappa ellenőrzése és létrehozása
			if(!file_exists($this->root_path.$config_array['archive_folder'].date('Y').DS.date('m'))){
				$this->checkDir($this->root_path.$config_array['archive_folder'].date('Y').DS.date('m'));
			}

			// archive mappába rakjuk a file-t
			if(file_exists($p.$last)){
				copy($p.$last, $this->root_path.$config_array['archive_folder'].date('Y').DS.date('m').DS.$config_array['sync_process_id'].'_'.date('Ymd_His').'.'.$config_array['source_file_type']);
			}

			// átnevezzük a feldolgozási névre
			if(file_exists($p.$last)){

				if($server == 'stage'){

					copy($p.$last, $this->root_path.$config_array['source_file_path'].$config_array['source_file_name']);

				}elseif($server == 'prod'){

					copy($p.$last, $this->root_path.$config_array['source_file_path'].$config_array['source_file_name']);

				}else{

					rename($p.$last, $this->root_path.$config_array['source_file_path'].$config_array['source_file_name']);

				}
				// majd töröljük az eredetit
				if(file_exists($p.$last)){
					unlink($p.$last);

				}

				$sql_del = "DELETE FROM ".XLS_SYNC_HASH." WHERE sync_process_id = 'eurest_abs'; ";

				try{
					dbExecute($sql_del);
				}catch (Exception $e){
					$this->writeLog($e->getMessage(),  $config_array['sync_error_log_file']);
				}

			}

		}

	}


	/**
	 * Legrand username generálás
	 *
	 * @param array $data_array
	 * @param array $config_array
	 */
	function legrandCreateUsername($data_array=[], $config_array=[]){

		$this->writeLog('legrandCreateUsername()',  $config_array['sync_error_log_file']);

		$alter = "ALTER TABLE `".$config_array['tmp_table_name']."_".$this->today."`
					ADD COLUMN `xls_username` VARCHAR(100) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci'
					AFTER `xls_munkarend5`;";

		try{
			dbExecute($alter);
		}catch (Exception $e){
			$this->writeLog($e->getMessage(),  $config_array['sync_error_log_file']);
		}

		// felhasználók lekérdezése
		$sql = "SELECT id, xls_last_name, xls_first_name FROM ".$config_array['tmp_table_name']."_".$this->today." ";

		if($sql != ''){

			try{
				$result = dbFetchAll($sql);
			}catch (Exception $e){
				$this->writeLog($e->getMessage(), $config_array['sync_error_log_file']);
			}

		}

		$update = '';

		// ciklusban legeneráljuk a felhasználóneveket
		if( (is_array($result)) AND (count($result)) ){

			foreach ($result as $row) {

				$username = '';

				if( ($row['xls_first_name'] != '') AND ($row['xls_last_name'] != '') ){

					$username = trim($row['xls_first_name']).'.'.trim($row['xls_last_name']);

					$hu=array('/é/','/É/','/á/','/Á/','/ó/','/Ó/','/ö/','/Ö/','/ő/','/Ő/','/ú/','/Ú/','/ű/','/Ű/','/ü/','/Ü/','/í/','/Í/','/ /');
					$en= array('e','E','a','A','o','O','o','O','o','O','u','U','u','U','u','U','i','I','.');

					$username = preg_replace($hu, $en, $username);

					$username = strtolower($username);

					$update .= "UPDATE ".$config_array['tmp_table_name']."_".$this->today."
								SET xls_username = '".$username."'
								WHERE id = ".$row['id'].";";

				}

			}

			if($update != ''){

				try{
					dbExecute($update);
				}catch (Exception $e){
					$this->writeLog($e->getMessage(),  $config_array['sync_error_log_file']);
				}
			}

		}

	}


	/**
	 * Corinthia részére fejlesztve
	 * @param array $data_array
     * @param array $config_array
	 */
	public function corinthiaSearchFiles($data_array=[], $config_array=[]){

		$this->writeLog('corinthiaSearchFiles()',  $config_array['sync_error_log_file']);

		// prod
        $p = $this->root_path.'..'.DS.'ftp'.DS.'corinthia_data_user'.DS;
        //$p = '/var/www/ttwa-base.login.hu/app/ftp/corinthia_data_user/';

		// stage
		if(strpos($_SERVER['HTTP_HOST'], 'stage') !== FALSE ){
			$p = $this->root_path.'..'.DS.'ftp'.DS.'corinthia'.DS;
            //$p = '/var/www/base.stage-ttwa.login.hu/app/ftp/corinthia/';
        }

		// localhost
		if(strpos($_SERVER['HTTP_HOST'], 'corinthia.hu.login') !== FALSE ){
			$p = $this->root_path.'..'.DS.'ftp'.DS.'corinthia'.DS;
            //$p = 'C:/xampp/htdocs/Login/TTWA/ftp/corinthia/';
        }

		// megnézzük az archive mappák elérhetőségét
        if(!is_dir($this->root_path.$config_array['source_file_path'] . 'oldFiles' . DS . date('Y') . DS . date('m'))){
            self::checkDir($this->root_path.$config_array['source_file_path'] . 'oldFiles' . DS . date('Y') . DS . date('m'));
        }

		$files = glob($p. "*.".$config_array['source_file_type']);

		if( (is_array($files)) AND (count($files) > 0) ){

			$this->writeLog('Van file ('.$p.')',  $config_array['sync_error_log_file']);

			// fájlok átrakása webroot-on belül, és a ftp mappából törlése
			foreach($files as $filename){

				$pi = pathinfo($filename);

				$this->corinthia_valid_from = substr($pi['filename'], 0, 10);
				$this->corinthia_valid_to	= date('Y-m-d', strtotime($this->corinthia_valid_from . ' -1 day') );

				// másolás a feldolgozási helyre
				copy($pi['dirname'] . DS . $pi['basename'], $this->root_path.$config_array['source_file_path'] . $config_array['source_file_name']); // egyedül ez működik stage-en

				// másolás archive mappába
				copy($pi['dirname'] . DS . $pi['basename'], $this->root_path.$config_array['source_file_path'] . 'oldFiles' . DS . date('Y') . DS . date('m') . DS . date('Ymd_Hi') . '_' .str_replace(" ", "", $pi['basename']) );

				// törlés az ftp mappából
				if(file_exists($pi['dirname'].DS.$pi['basename'])){
					if(!isset($_GET['temondhogyrablotamadas'])){
						unlink($pi['dirname'].DS.$pi['basename']);
					}
				}
				/*
				if(strpos($_SERVER['HTTP_HOST'], 'stage') === FALSE ){
					unlink($filename);
				}
				*/
			}

		}else{

			$this->writeLog('Nincs file ('.$p.')',  $config_array['sync_error_log_file']);
			$this->checkAvalibeFiles($config_array);

			// ha nincs feltöltve file akkor kidobjuk ezt a feldolgozást
			foreach ($this->sync_config as $key => $sync){

				if( ($sync['sync_process_id'] === $config_array['sync_process_id']) AND ($sync['row_id'] === $config_array['row_id']) ){
					unset($this->sync_config[$key]);
				}

			}

		}

	}


	/**
	 * Corinthia adatok összehasonlítása
	 *
	 * @param array $data_array
	 * @param array $config_array
	 */
	public function dataComparison($data_array=[], $config_array=[]){

		$this->writeLog('dataComparison()',  $config_array['sync_error_log_file']);

		$new_data = 0;
		$avalibe_data = 0;

		$sql = "SELECT COUNT(`id`) as db FROM ".$config_array['tmp_table_name']."_".$this->today.";";
		$nd = $this->getSqlResult($sql,  $config_array['sync_error_log_file'], TRUE);
		$new_data = $nd['db'];

		$sql = "SELECT COUNT(e.`row_id`) as db
				FROM employee e
				LEFT JOIN employee_contract ec ON ec.employee_id = e.employee_id
					AND ec.`status` = 2
					AND CURDATE() BETWEEN ec.valid_from AND ec.valid_to
					AND CURDATE() BETWEEN ec.ec_valid_from AND ec.ec_valid_to
				WHERE e.company_id = 1
				AND e.`status` = 2
				AND CURDATE() BETWEEN e.valid_from AND e.valid_to;";

		$ad = $this->getSqlResult($sql, $config_array['sync_error_log_file'], TRUE);
		$avalibe_data = $ad['db'];


		$new_empl_sql = "SELECT xls.xls_employee_id, xls.xls_employee_fullname, e.employee_id, concat(e.last_name, ' ', e.first_name) as fullname
							FROM ".$config_array['tmp_table_name']."_".$this->today." xls
							LEFT JOIN employee e ON e.employee_id = xls.xls_employee_id
								AND e.`status` = 2
								AND CURDATE() BETWEEN e.valid_from AND e.valid_to
							WHERE e.row_id IS NULL;";

		$del_empl_sql = "SELECT xls.xls_employee_id, xls.xls_employee_fullname, e.employee_id, concat(e.last_name, ' ', e.first_name) as fullname
							FROM employee e
							LEFT JOIN ".$config_array['tmp_table_name']."_".$this->today." xls ON e.emp_id = xls.xls_employee_id
							WHERE xls.id IS NULL
							AND e.company_id = 1
							AND e.`status` = 2
							AND CURDATE() BETWEEN e.valid_from AND e.valid_to
							;";

		$ne_array = $this->getSqlResult($new_empl_sql, $config_array['sync_error_log_file']);

		$del_empl_array = $this->getSqlResult($del_empl_sql,  $config_array['sync_error_log_file']);

		$this->writeLog('$new_data:'.$new_data.' || $avalibe_data:'.$avalibe_data, $config_array['sync_error_log_file']);

		if($new_data == $avalibe_data){
			// darabra ugyan annyi ember van.. de simán lhet -1, +1 verzió (vagy -x, +x)
			$this->writeLog('darabra ugyan annyi ember van.. de simán lhet -1, +1 verzió (vagy -x, +x)', $config_array['sync_error_log_file']);
		}elseif ($new_data > $avalibe_data) {
			// több új adat van mint régi.. tehát van érkező ember.. de ez nem zárja ki azt hogy valaki elment (pl: +3, -2)
			$this->writeLog('több új adat van mint régi.. tehát van érkező ember.. de ez nem zárja ki azt hogy valaki elment (pl: +3, -2)', $config_array['sync_error_log_file']);

		}elseif ($new_data < $avalibe_data) {
			// keveseb új adat van.. tehát minimum 1 elmenő emberünk van.. de az is lehet hogy pl 3 elment és 2 jött (pl: +2, -3)
			$this->writeLog('keveseb új adat van.. tehát minimum 1 elmenő emberünk van.. de az is lehet hogy pl 3 elment és 2 jött (pl: +2, -3)', $config_array['sync_error_log_file']);

		}

		$this->writeLog('$del_empl_array:'.count($del_empl_array).' || $ne_array:'.count($ne_array), $config_array['sync_error_log_file']);

		if( (count($del_empl_array) === 0) AND (count($ne_array) === 0) ){
			// nincs se uj se törölt dolgozó
			$this->writeLog('nincs se uj se törölt dolgozó', $config_array['sync_error_log_file']);
		}

		if( (count($del_empl_array) === 0) AND (count($ne_array) > 0) ){
			// nincs törölt dolgozó de van új dolgozó
			$this->writeLog('nincs törölt dolgozó de van új dolgozó', $config_array['sync_error_log_file']);

			$tmp_log = '';
			foreach ($ne_array as $ne_row) {
				$tmp_log .= $ne_row['xls_employee_id'].' => '.$ne_row['xls_employee_fullname'].PHP_EOL;
			}

			$this->writeLog('új dolgozó(k)'.PHP_EOL.$tmp_log, $config_array['sync_error_log_file']);

		}

		if( (count($del_empl_array) > 0) AND (count($ne_array) === 0) ){

			// van törölt dolgozó, de nincs új dolgozó
			$this->writeLog('van törölt dolgozó, de nincs új dolgozó', $config_array['sync_error_log_file']);

			$tmp_log = '';
			foreach ($del_empl_array as $del_row) {
				$tmp_log .= $del_row['employee_id'].' => '.$del_row['fullname'].PHP_EOL;
			}

			$this->writeLog('törölt dolgozó(k)'.PHP_EOL.$tmp_log, $config_array['sync_error_log_file']);

		}

		if( (count($del_empl_array) > 0) AND (count($ne_array) > 0) ){

			$this->writeLog('van törölt dolgozó is, és van új dolgozó is', $config_array['sync_error_log_file']);

			$tmp_log = '';
			foreach ($del_empl_array as $del_row) {
				$tmp_log .= $del_row['employee_id'].' => '.$del_row['fullname'].PHP_EOL;
			}

			$this->writeLog('törölt dolgozó(k)'.PHP_EOL.$tmp_log, $config_array['sync_error_log_file']);

			$tmp_log = '';
			foreach ($ne_array as $ne_row) {
				$tmp_log .= $ne_row['xls_employee_id'].' => '.$ne_row['xls_employee_fullname'].PHP_EOL;
			}

			$this->writeLog('új dolgozó(k)' . PHP_EOL . $tmp_log, $config_array['sync_error_log_file']);

		}

	}

	/**
	 *
	 * @param type $sql
	 * @param type $sync_error_log_file
	 * @param type $one_row
	 * @return type
	 */
	public function getSqlResult($sql, $sync_error_log_file, $one_row=FALSE){

		$ret = FALSE;

		try {

			if($one_row === FALSE){
				$result = dbFetchAll($sql);
			}else{
				$result = dbFetchRow($sql);
			}

        } catch (Exception $e) {

            $this->writeLog($e->getMessage(), $sync_error_log_file);

        }

        if (count($result)) {
            $ret = $result;
        }

		return $ret;

	}


	/**
	 * Egyben kapott employee neveket szedjük szét az első space mentén
	 * @param array $data_array
	 * @param array $config_array
	 */
	function splitEmployeeName($data_array=[], $config_array=[]){

		$this->writeLog('splitName()',  $config_array['sync_error_log_file']);

		$alter = "ALTER TABLE `".$config_array['tmp_table_name']."_".$this->today."`
					ADD COLUMN `xls_firstname` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci' AFTER `xls_fullname`,
					ADD COLUMN `xls_lastname` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci' AFTER `xls_firstname`,
					ADD COLUMN `xls_valid_from` VARCHAR(20) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci' AFTER `xls_jogv_valid_to`,
					ADD COLUMN `xls_valid_to` VARCHAR(20) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci' AFTER `xls_valid_from`,
					ADD COLUMN `xls_employee_id` VARCHAR(20) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci' AFTER `xls_emp_id`;
					";

		try{
			dbExecute($alter);
		}catch (Exception $e){
			$this->writeLog($e->getMessage(),  $config_array['sync_error_log_file']);
		}


		$sql = "SELECT id, xls_fullname, xls_emp_id, xls_tax_number, xls_jogv_valid_from, xls_jogv_valid_to
				FROM ".$config_array['tmp_table_name']."_".$this->today."
				;";

		$data = $this->getSqlResult($sql,  $config_array['sync_error_log_file']);

		$update = '';

		if( (is_array($data)) AND ( count($data) > 0) ){

			foreach ($data as $row) {

				$name = $this->splitName($row['xls_fullname']);

				$update .= "UPDATE ".$config_array['tmp_table_name']."_".$this->today." SET
							xls_firstname = '".$name['firstname']."',
							xls_lastname = '".$name['lastname']."',
							xls_valid_from	= '".$row['xls_jogv_valid_from']."',
							xls_valid_to	= '".$row['xls_jogv_valid_to']."',
							xls_employee_id = '".$row['xls_tax_number']."'
							WHERE id = '".$row['id']."'
							;";
			}
		}

		if($update != ''){
			try{
				dbExecute($update);
			}catch (Exception $e){
				$this->writeLog($e->getMessage(),  $config_array['sync_error_log_file']);
			}
		}

	}

	function splitName($fullname){

		$name = explode(" ", $fullname);

		$lastname = trim($name[0]);

		unset($name[0]);

		$firstname = implode(" ", $name);

		return ['lastname' => $lastname, 'firstname' => $firstname];

	}

	/**
	 * Legrand regisztráció dátum összefűzése
	 *
	 * @param array $data_array
	 * @param array $config_array
	 */
	function concatDateTime($data_array=[], $config_array=[]){

		$this->writeLog('splitName()',  $config_array['sync_error_log_file']);

		$alter = "ALTER TABLE `".$config_array['tmp_table_name']."_".$this->today."`
					ADD COLUMN `xls_datetime` VARCHAR(20) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci' AFTER `xls_direction`;";

		try{
			dbExecute($alter);
		}catch (Exception $e){
			$this->writeLog($e->getMessage(),  $config_array['sync_error_log_file']);
		}

		$sql = "SELECT *
				FROM ".$config_array['tmp_table_name']."_".$this->today."
				;";

		$data = $this->getSqlResult($sql,  $config_array['sync_error_log_file']);

		$update = '';

		if( (is_array($data)) AND ( count($data) > 0) ){

			foreach ($data as $row) {

				$dt = str_replace(".", "-", trim($row['xls_date'], '.')). ' '.$row['xls_time'];

				$d = strtotime($dt);

				$datetime = date('Y-m-d H:i:s', $d);

				$update .= "UPDATE ".$config_array['tmp_table_name']."_".$this->today." SET
							xls_datetime = '".$datetime."'
							WHERE id = '".$row['id']."'
							;";

			}
		}

		if($update != ''){
			try{
				dbExecute($update);
			}catch (Exception $e){
				$this->writeLog($e->getMessage(),  $config_array['sync_error_log_file']);
			}
		}

	}

	/**
     * Patikaplusz részére összefűzi a bejövő fájokat, hogy csak 1-et kellejen feldolgozni
     * Utána a fájlokat átrakja archive mappába
     *
     * @param array $data_array
     * @param array $config_array
     */
    static function concatFiles($data_array=[], $config_array=[]){

        // c:\xampp\htdocs\Login\TTWA\ftp\
        // /var/www/base.stage-ttwa.login.hu/ftp/patikaplus
        // /var/www/ttwa-base.login.hu/ftp/patikaplus_data_user

        // webrootban lévő pp mappa ellenőrzése, és létrehozása
        if(!is_dir(Yang::getAlias('webroot') . DS . "xlsImport" . DS . "pp")){
            self::checkDir($this->root_path . "xlsImport" . DS ."pp");
            //self::checkDir(Yang::getAlias('webroot') . DS . "xlsImport" . DS ."pp");
        }

        // prod
		$p = $this->root_path.'..'.DS.'ftp'.DS.'patikaplus_data_user'.DS;
        //$p = '/var/www/ttwa-base.login.hu/ftp/patikaplus_data_user/';

        // ha stage
        if(strpos($_SERVER['HTTP_HOST'], 'stage') != FALSE ){
			$p = $this->root_path.'..'.DS.'ftp'.DS.'patikaplus'.DS;
            //$p = '/var/www/base.stage-ttwa.login.hu/ftp/patikaplus/';
        }


        // fájlok átrakása webroot-on belül, és a ftp mappából törlése
        foreach(glob($p . "*.csv") as $filename){

            $pi = pathinfo($filename);

            if($pi['basename'] != $config_array['source_file_name']){
                //rename($filename, $config_array['source_file_path'] . DS .$pi['basename']); // STAGE-en :
                // rename(/var/www/base.stage-ttwa.login.hu/ftp/patikaplus/12369056_20171214_081423.csv,/var/www/base.stage-ttwa.login.hu/webroot/xlsImport/pp/12369056_20171214_081423.csv): Permission denied (/var/www/base.stage-ttwa.login.hu/protected/controllers/ExcelImport2Controller.php:1895)

                //unlink($filename); // stage-en hibát kaptunk erre
                // unlink(/var/www/base.stage-ttwa.login.hu/ftp/patikaplus/12369056_20171214_081423.csv): Permission denied (/var/www/base.stage-ttwa.login.hu/protected/controllers/ExcelImport2Controller.php:1897)

                copy($filename, $config_array['source_file_path'] . DS .$pi['basename']); // egyedül ez működik stage-en

                if(strpos($_SERVER['HTTP_HOST'], 'stage') == FALSE ){
                    unlink($filename);
                }
                //rename($filename, $config_array['source_file_path'] . DS .$pi['basename']);

            }

        }

        // ha létezik a globális file akkor töröljük
        if(file_exists($this->root_path . $config_array['source_file_path'] . DS . $config_array['source_file_name'])){
            unlink($this->root_path . $config_array['source_file_path'] . DS . $config_array['source_file_name']);
        }

        // létrehozzuk a globális file-t
        $global_file = fopen($this->root_path . $config_array['source_file_path'] . DS . $config_array['source_file_name'], "a+");

        // megnézzük az archiva mappák elérhetőségét
        if(!is_dir($this->root_path . "xlsImport" . DS . "pp" . DS . 'oldFiles' . DS . date('Y') . DS . date('m'))){
            self::checkDir($this->root_path . "xlsImport" . DS . "pp" . DS . 'oldFiles' . DS . date('Y') . DS . date('m'));
        }

        foreach(glob($config_array['source_file_path'] . DS . "*.csv") as $filename){

            $pi = pathinfo($filename);

            if(filesize($pi['dirname'] . DS . $pi['basename']) > 0){

                if($pi['basename'] != $config_array['source_file_name']){

                    $x = file_get_contents($filename);

                    fwrite($global_file, $x);

                    // a bejött fájlok archiválása
                    if(!isset($_GET['temondhogyrablotamadas'])){
                        rename($this->root_path . $config_array['source_file_path'] . DS . $pi['basename'], $this->root_path . $config_array['source_file_path'] . DS .'oldFiles' . DS . date('Y') . DS . date('m') . DS . date('Ymd_his') . '_' .$pi['basename']);
                    }

                }

            }else{

                // Az üres fájlokat is acrhiváljuk
                rename($this->root_path . $config_array['source_file_path'] . DS . $pi['basename'], $this->root_path . $config_array['source_file_path'] . DS .'oldFiles' . DS . date('Y') . DS . date('m') . DS . date('Ymd_His') . '_' .$pi['basename']);

            }

        }

        fclose($global_file);

    }


    /**
     * Rosenberger szabadság update
     *
     */
    public function renameFile($data_array=[], $config_array=[]){

		$sql  = "TRUNCATE `xls_sync_2_hash`;";

		dbExecute($sql);

		return TRUE;

		/*
        $p = '/var/share/nexon_alapszabadsagok/';

        if(strpos($_SERVER['HTTP_HOST'], 'ros.hu.login') !== FALSE){
            $p = 'C:/xampp/htdocs/Login/TTWA/webroot/xlsImport/ros_abs/';
        }
		//Yang::log($p . "archive" . DS . date('Y') . DS . date('m'), 'log', 'renameFile()');

		if(!is_dir($p  . "archive" . DS . date('Y') . DS . date('m'))){
            self::checkDir($p . "archive" . DS . date('Y') . DS . date('m'));
        }

		// átmásoljuk a webroot-ba az FTP mappából a kapott file-t
		copy($config_array['source_file_path'] . $config_array['source_file_name'], $config_array['source_file_path'] . "archive" . DS . date('Y') . DS . date('m') . DS . date('Ymd_Hi').'_'.$pi['basename']); // egyedül ez működik stage-en
		*/

		/*
        foreach(glob($p . "*.".$config_array['source_file_type']) as $filename){

            $pi = pathinfo($filename);

            copy($p.$pi['basename'], $p ."archive" . DS . date('Y') . DS . date('m') . DS . date('Ymd_Hi').'_'.$pi['basename']); // egyedül ez működik stage-en

        }
		*/

    }

	/**
	 * Rosenberger szabadságos fájlok törlése a feldolgozás után
	 */
	public function deleteRosFiles($data_array=[], $config_array=[]){

		return TRUE;

		//unlink($config_array['source_file_path'] . $config_array['source_file_name']);

	}

    /**
     * Resenberger szabadság betöltönél az alapszabadság mellé hozzá kell csapni
     * az "Egyéb 1" néven érkezőket is.
     *
     * @param array $res
     * @return int
     */
    public function getEgyeb1Szabadsag($res, $table_name){

        $ret = FALSE;
        /*
        $sql  = "SELECT IFNULL( ( xls.xls_abs_hour / ec.daily_worktime), 0) as day,
                        IFNULL(xls.xls_abs_hour, 0) as hour
                FROM ".$table_name."_".$this->today."  xls
                LEFT JOIN employee e ON e.emp_id = xls.xls_emp_id
                        AND e.`status` = 2
                        AND CURDATE() BETWEEN e.valid_from AND IFNULL(e.valid_to, '2038-01-01')
                LEFT JOIN employee_contract ec ON ec.employee_id = e.employee_id
                        AND ec.`status` = 2
                        AND CURDATE() BETWEEN ec.valid_from AND IFNULL(ec.valid_to, '2038-01-01')
                WHERE 1=1
                AND e.row_id IS NOT NULL
                AND ec.row_id IS NOT NULL
                AND e.emp_id = '".$res['xls_emp_id']."'
                AND e.tax_number = '".$res['xls_tax_number']."'
                AND xls.xls_abs_type LIKE 'Egyéb%';";

        try{
            $result = dbFetchRow($sql);
        }catch (Exception $e){
            $this->writeLog($e->getMessage(), NULL);
        }

        if( (is_array($result)) AND ( count($result)) ){
            $ret['day']     = $result['day'] != '' ? (int)$result['day'] : 0 ;
            $ret['hour']    = $result['hour'] != '' ? (int)$result['hour'] : 0;
        }
		*/
        return $ret;

    }

    public function deleteDuplicatedRegs()
    {
        $SQL = "
            UPDATE `registration` reg
            JOIN `registration` reg2 ON
                    reg2.`card` = reg.`card`
                AND reg2.`time` = reg.`time`
                AND reg2.`event_type_id` = reg.`event_type_id`
                AND reg2.`status` = " . Status::PUBLISHED . "
                AND reg2.`row_id` > reg.`row_id`
            SET
                reg2.`status` = " . Status::INVALID . ",
                reg2.`modified_by` = 'excelImport2AutoDupRemover',
                reg2.`modified_on` = NOW()
            WHERE
                    reg.`status` = " . Status::PUBLISHED . "
                AND reg.`time` BETWEEN DATE_SUB(NOW(), INTERVAL 7 DAY) AND DATE_ADD(NOW(), INTERVAL 1 DAY);
        ";
        dbExecute($SQL);
    }

    public function createTables(){

        $sql = "CREATE TABLE IF NOT EXISTS `".XLS_SYNC_CONFIG."`  (
                  `row_id` int(11) NOT NULL AUTO_INCREMENT,
                  `sync_process_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `source_file_path` varchar(200) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `source_file_name` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `source_file_type` varchar(10) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `file_containt` ENUM('nd','ad') NULL DEFAULT 'nd' COLLATE 'utf8_unicode_ci',
                  `source_file_skipped_rows` tinyint(4) DEFAULT NULL,
                  `csv_delimiter` varchar(10) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `tmp_table_name` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `insert_mode` varchar(10) COLLATE utf8_unicode_ci DEFAULT 'manual',
                  `multi_or_single` varchar(5) COLLATE utf8_unicode_ci DEFAULT 's',
                  `multi_file_row_ids` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `valid_from` varchar(10) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `valid_to` varchar(10) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `status` tinyint(4) DEFAULT '2',
                  PRIMARY KEY (`row_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;";

        dbExecute($sql);

        // -------------

        $sql = "CREATE TABLE IF NOT EXISTS `".XLS_SYNC_CONFIG_IMPORT."` (
                  `row_id` int(11) NOT NULL AUTO_INCREMENT,
                  `sync_process_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `sql` text COLLATE utf8_unicode_ci,
                  `sort` int(11) DEFAULT NULL,
                  `status` tinyint(4) DEFAULT '2',
                  PRIMARY KEY (`row_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;";

        dbExecute($sql);

        // -------------

        $sql = "CREATE TABLE IF NOT EXISTS `".XLS_SYNC_INSERT_TABLE."` (
                  `row_id` int(11) NOT NULL AUTO_INCREMENT,
                  `sync_process_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `target_table_name` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `target_col_name` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `value` text COLLATE utf8_unicode_ci,
                  `config_import_row_id` int(11) DEFAULT NULL,
                  `sort` int(11) DEFAULT NULL,
                  `select_type` tinyint(4) DEFAULT NULL,
                  `status` tinyint(4) DEFAULT '2',
                  PRIMARY KEY (`row_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;";

        dbExecute($sql);

        // -------------

        $sql = "CREATE TABLE IF NOT EXISTS `".XLS_SYNC_TMP_TABLE_ROWS."` (
                  `row_id` int(11) NOT NULL AUTO_INCREMENT,
                  `sync_process_id` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `create_col_name` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `create_col_type` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
                  `sort` int(11) DEFAULT NULL,
                  `sort2` int(11) DEFAULT NULL,
                  `status` tinyint(4) DEFAULT '2',
                  `require` char(1) COLLATE utf8_unicode_ci DEFAULT 'n',
                  PRIMARY KEY (`row_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;";

        dbExecute($sql);

        // -------------

        $sql = "CREATE TABLE IF NOT EXISTS  `".XLS_SYNC_CHANGED_DATA."` (
                   	`row_id` INT(11) NOT NULL AUTO_INCREMENT,
                        `sync_process_id` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',
                        `table` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',
                        `col` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',
                        `tmp_col` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',
                        `value` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',
                        `sort` TINYINT(4) NULL DEFAULT NULL,
                        `sort2` TINYINT(4) NULL DEFAULT NULL,
                        `status` TINYINT(4) NULL DEFAULT '2',
                        PRIMARY KEY (`row_id`)
                )
                COLLATE='utf8_unicode_ci'
                ENGINE=InnoDB;";

        dbExecute($sql);

        // -------------

        $sql = "CREATE TABLE IF NOT EXISTS  `".XLS_SYNC_POSTFIX_SQL."` (
                    `row_id` INT(11) NOT NULL AUTO_INCREMENT,
                    `sync_process_id` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',
                    `sql` TEXT NULL COLLATE 'utf8_unicode_ci',
                    `sort` SMALLINT(6) NULL DEFAULT NULL,
                    `status` TINYINT(4) NULL DEFAULT '2',
                    `valid_from` DATE NULL DEFAULT NULL,
                    `valid_to` DATE NULL DEFAULT NULL,
                    PRIMARY KEY (`row_id`)
                )
                COLLATE='utf8_unicode_ci'
                ENGINE=InnoDB;";

        dbExecute($sql);

        // -------------

        $sql = "CREATE TABLE IF NOT EXISTS `".XLS_SYNC_PREFIX_SQL."` (
                        `row_id` INT(11) NOT NULL AUTO_INCREMENT,
                        `sync_process_id` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',
                        `type` ENUM('sql','function') NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',
                        `value` TEXT NULL COMMENT 'SQL string or function name' COLLATE 'utf8_unicode_ci',
                        `sort` SMALLINT(6) NULL DEFAULT NULL,
                        `status` TINYINT(4) NULL DEFAULT '2',
                        `valid_from` DATE NULL DEFAULT NULL,
                        `valid_to` DATE NULL DEFAULT NULL,
                        PRIMARY KEY (`row_id`)
                )
                COLLATE='utf8_unicode_ci'
                ENGINE=InnoDB;";

        dbExecute($sql);

        // -------------

        $sql = "CREATE TABLE IF NOT EXISTS `".XLS_SYNC_HASH."` (
                        `row_id` INT(11) NOT NULL AUTO_INCREMENT,
                        `hash` VARCHAR(150) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',
                        `sync_process_id` VARCHAR(50) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',
                        `create_date` DATETIME NULL DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (`row_id`)
                )
                COLLATE='utf8_unicode_ci'
                ENGINE=InnoDB;";

        dbExecute($sql);

        // -------------

        //$sql = "DROP TABLE IF EXISTS `xls_sync_2_abs_translator`;";

        $sql .= "CREATE TABLE IF NOT EXISTS `".XLS_SYNC_ABS_TRANSLATOR."` (
                        `row_id` INT(11) NOT NULL AUTO_INCREMENT,
                        `base_absence_type_id` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',
                        `xls_2_sync_file_value` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',
                        PRIMARY KEY (`row_id`)
                )
                COLLATE='utf8_unicode_ci'
                ENGINE=InnoDB
                AUTO_INCREMENT=8
                ;";

        $sql = "INSERT INTO `".XLS_SYNC_ABS_TRANSLATOR."` (`row_id`, `base_absence_type_id`, `xls_2_sync_file_value`) VALUES
                    (1, '8d92474d33de2925e59dbeb01e7602ad', 'Alap szabadság'),
                    (2, '953908dee36fd0379103cdb0a2d531dd', 'Apának járó pótszabadság'),
                    (3, '8d92474d33de2925e59dbeb01e7602ad', 'Egyéb 1'),
                    (4, '99d171c9188a94a197aa2e8c3f22fb04', 'Életkor szerinti szabadság'),
                    (5, '63a1079c69d79753cd623ee2620687a1', 'Előző évi szabadság'),
                    (6, '3124666222842d616949b952976b1238', 'Gyermekek utáni szabadság'),
                    (7, '5cb1f0666d19d9eb290457bebd45d0cd', 'Pótszabadság');";

        dbExecute($sql);

        // -------------


    }

}

