# Elfelejtett jelszó
A **<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>** oldalon a `user --- forgotten_password` jogosultsággal lehetőség van arra hogy a belépett felhasználó más felhasználóknak "elfelejtett jels<PERSON>" e-mailt küldjön. Ki kell jelölni az érintett felhasználó(ka)t és megnyomni a **<PERSON><PERSON><PERSON><PERSON> viss<PERSON>llítás** gombot (piros hátterű fehér 'kulcs' ikon).

## ForgottenPasswordController
Az e-mailben kiküldött jelszó-visszaállító linken található oldalt jeleníti meg. Ez egy lecsupaszított Grid2Controller-ból származtatott oldal, amelynek a jogosultságkezelése, ill a táblázat-megjelenítés része ki lett véve a kódból (`/Grid2/emmpty` layout).

- `actionIndex()`: Létrehozza az index action-t egy üres view-val. Amennyiben megkapja a korábban generált anonosítót, megkeresi hogy nem volt-e használatban, ill. érvényes-e még (24 óra az érvényesség ideje). Ha évényes a kód, megjeleníti azt az ablakot ahol az új jelszó megadható. Illetve létrejön egy session, amit később a mentéskor ellenőriz a kód. Ezzel együtt a `fpw_used` értékét 1-re állítja, így a link a későbbiekben nem használható már. Ha nem valid a megadott kód, hibaüzenetet ad.

- `actionSave()`: Ellenőrzi, hogy létezik-e az oldal beltöltődésekor lérehozott session. Ha igen, és még nem használták mentésre (`fpw_saved = 0`), akkor hívja az ősosztály mentés funkcióját, ezáltal elmentve az új jelszót.

## UserController
A folyamat indítása a **Felhasználó kezelés** oldalon történik, így a kód első fele is itt található.

A `protected/assets/base/user/js/user.js` fájlban található forgottenPassword() funkció hívja ajax hívással a UserControllerben található `actionForgottenPassword()` oldalt.

- `actionForgottenPassword()`: A `forgottenPassword()` js funkció által ajax-szal meghívott action. A megkapott user sorazonosítókat tömbbe rakja és mindegyikre egyesével hívja a `forgottenPassword()` php funkciót.
- `forgottenPassword()`: Elfelejtett-jelszó folyamatot indít, egyszerhasználható azonosítóval, amelyet egy link részeként kiküld e-mailben. Az azonosítót elmenti a `forgotten_password` adatbázistáblába, a user aktuális jelszavával. Ezzel együtt a user új jelszót kap.