<?php #yii2: done

'yii2-only`;

	namespace app\controllers;
	use app\components\API\WriteUserEvent;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2\Grid2Controller;
	use app\components\wfm\API\GetWorktimePayrollFast;
	use Yang;

`/yii2-only';


#yii2: done

class ReportFieldsController extends Grid2Controller
{
	protected $mezok=[];
	protected $allSum=false; //lista összesen
	protected $frame=false; //keret kezdetétől kellenek az adatok, de csak a lekért intervallum érdekel, kivéve, ha egy adat keretesnek (frame) van jelölve
	protected $allFrame=false; //minden adat a teljes keretre vonatkozik
	protected $sumPart=false; //részösszesen kell
	protected $viewDetails=false; //tételsorok mutatása
	protected $viewInterval=false; //lekért intervallum mutatása
	protected $timeFunction='SEC_TO_TIME_LOGIN_NOSEC';
	protected $validFrom='';
	protected $validTo='';
	protected $variant=1;
	protected $where='';
	protected $groupNumber=0;


	public function __construct($construct="reportFields")
	{
		WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__);
		$this->initFields();
		parent::__construct($construct);
	}

	public function actionSetInitProperties()
	{
		WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__);
		Yang::setSessionValue('report_fields_filters', requestParam('searchInput'));
		
		parent::actionSetInitProperties();
	}

	protected function initFields()
	{
		WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__);
		$filters=Yang::session('report_fields_filters');
		if(is_array($filters) && isset($filters['report_type'])) $this->variant=$filters['report_type'];

		if ($this->variant==1)
		{
			$this->allSum=true;
			$this->viewDetails=false;
			$this->viewInterval=true;
			$this->timeFunction='SEC_TO_TIME_LOGIN_NOSEC';

			$this->mezok = [];

			$this->mezok['osszesen']['view']=false;
			$this->mezok['osszesen']['group']=true;
			$this->mezok['osszesen']['sort']=true;
			$this->mezok['osszesen']['expression']='1';
			$this->mezok['osszesen']['title']='Összesen';

			$this->mezok['workgroup_name']['view']=true;
			$this->mezok['workgroup_name']['group']=false;
			$this->mezok['workgroup_name']['sort']=true;
			$this->mezok['workgroup_name']['title']='Munkacsoport';

			$this->mezok['workgroup_id']['view']=false;
			$this->mezok['workgroup_id']['group']=true;
			$this->mezok['workgroup_id']['sort']=false;

			$this->mezok['unit_name']['view']=true;
			$this->mezok['unit_name']['group']=false;
			$this->mezok['unit_name']['sort']=true;
			$this->mezok['unit_name']['title']='Egység';

			$this->mezok['unit_id']['view']=false;
			$this->mezok['unit_id']['group']=true;
			$this->mezok['unit_id']['sort']=false;

			$this->mezok['fullname']['view']=true;
			$this->mezok['fullname']['group']=false;
			$this->mezok['fullname']['sort']=true;
			$this->mezok['fullname']['title']='Dolgozó neve';

			$this->mezok['emp_id']['view']=true;
			$this->mezok['emp_id']['group']=false;
			$this->mezok['emp_id']['sort']=true;
			$this->mezok['emp_id']['title']='Dolgozó azonosító';

			$this->mezok['employee_contract_id']['view']=false;
			$this->mezok['employee_contract_id']['group']=true;
			$this->mezok['employee_contract_id']['sort']=true;

			$this->mezok['wt_saved']['view']=true;
			$this->mezok['wt_saved']['sum']=true;
			$this->mezok['wt_saved']['time']=true;
			$this->mezok['wt_saved']['title']='Ledolgozott idő';

			$this->mezok['wt_saved_letszam']['view']=true;
			$this->mezok['wt_saved_letszam']['sum']=true;
			$this->mezok['wt_saved_letszam']['exists']=true;
			$this->mezok['wt_saved_letszam']['expression']='wt_saved';
			$this->mezok['wt_saved_letszam']['title']='Nap';
		}
	}

	protected function G2BInit($title="page_title_report_fields")
	{
		WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__);
		parent::setControllerPageTitleId($title);

		$this->LAGridRights->overrideInitRights("paging",				true);
		$this->LAGridRights->overrideInitRights("search",				true);
		$this->LAGridRights->overrideInitRights("search_header",		true);
		$this->LAGridRights->overrideInitRights("select",				true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);

		$this->LAGridRights->overrideInitRights("export_xlsx",		true);

		parent::setReportOrientation("landscape");

		$this->LAGridDB->enableSQLMode();

		parent::G2BInit();

	}

	protected function fetchGridData($filter, $isExport = false, $excelExport = false, $csvString = false, $parent=false)
	{
		if($parent) return parent::fetchGridData($filter);
		WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__);
		$this->G2BInit();

		if(!empty($filter))
		{
			$this->validFrom	= $filter['valid_from'];
			$this->validTo		= $filter['valid_to'];
		}

		$sql = $this->getSQL($filter);

		$this->LAGridDB->setSQLSelection($sql, "row_id");

		return parent::fetchGridData($filter, true);
	}

	protected function getSQL($filters)
	{
		$sql			= "SELECT 0 AS last_rec_for_order, date AS datum";
		$union			= " UNION SELECT 1 AS last_rec_for_order, '' AS datum";
		$order			= " ORDER BY last_rec_for_order";
		$orderPrefix	= ", ";
		$sqlPrefix		= ", ";
		$groupNumber	= 0;
		if($this->viewInterval)
		{
			$sql   .= $sqlPrefix."'".  $this->validFrom."' AS valid_from";
			$sqlPrefix   =', ';
			$union .= $sqlPrefix."'".  $this->validFrom."' AS valid_from";
			$sql   .= $sqlPrefix."'".  $this->validTo."' AS valid_to";
			$union .= $sqlPrefix."'".  $this->validTo."' AS valid_to";
		}
		foreach ($this->mezok as $mezonev => $mezotomb)
		{
			$expression=(isset($mezotomb['expression'])) ? $mezotomb['expression'] : $mezonev;
			$filter=(isset($mezotomb['filter'])) ? $mezotomb['filter'] : 1;
			if((!$this->allFrame) && $this->frame)
			{
				$frame=isset($mezotomb['frame']) && $mezotomb['frame'];
				if(!$frame) $filter = "(date BETWEEN '".  $this->validFrom."' AND '".$this->validTo."') AND ($filter)";
			}
			if(isset($mezotomb['exists']) && $mezotomb['exists'])
			{
				$sql   .= $sqlPrefix."IF($filter, IF($expression != 0, 1, 0) ,0) AS ".$mezonev."_original";
				$union .= $sqlPrefix."0 AS ".$mezonev."_original";
				$sqlPrefix=", ";
			}
			elseif(
				   (isset($mezotomb['sum']) && $mezotomb['sum'])
				|| (isset($mezotomb['time']) && $mezotomb['time'])
				)
			{
				$sql   .= $sqlPrefix."IF($filter, $expression, 0) AS ".$mezonev."_original";
				$union .= $sqlPrefix."0 AS ".$mezonev."_original";
				$sqlPrefix=", ";
			}
			else
			{
				$sql   .= $sqlPrefix."$expression AS $mezonev"."_original";
				$union .= $sqlPrefix."'' AS ".$mezonev."_original";
				$sqlPrefix=", ";
			}

			if(isset($mezotomb['group']) && $mezotomb['group'])
			{
				$groupNumber++;
				$order .= $orderPrefix.$mezonev."_original";
				$orderPrefix=", ";
			}
		}
		$this->groupNumber=$groupNumber;
		$sql .= " FROM (";
		$sql .= $this->getCommonSQL($filters);
		if(!empty($this->where))
		{
			$sql .= " WHERE ".$this->where;
		}
		$sql .= ") a";
		$sql .= $union;
		$sql  = "SELECT * FROM ($sql) s";
		$sql .= $order.", datum";
		$bsql =$sql;

		$sql			= "SELECT *, ";
		$change			= "";
		$changeSuffix	= "";
		$groupFields	= "";
		$varFields		= "";
		$varPrefix		= "@record_number_pr:=0, ";
		$i				= $groupNumber;

		foreach ($this->mezok as $mezonev => $mezotomb)
		{
			$sql .= "@$mezonev AS $mezonev"."_elozo_rek, ";
			if(isset($mezotomb['group']) && $mezotomb['group'])
			{
				$change			.= "IF(IF(@$mezonev='', $mezonev"."_original, @$mezonev COLLATE utf8_unicode_ci) != $mezonev"."_original, $i, ";
				$changeSuffix	.= ")";
				$i--;
			}
			$groupFields	.= ", @$mezonev:=$mezonev"."_original";
			$varFields		.= $varPrefix."@$mezonev:=''";
			$varPrefix		 = ", ";
		}

		$change .= $groupNumber+1;
		$change .= $changeSuffix;
		$change .= " AS akt_group_for_pr, @record_number_pr AS record_number_pr, @record_number_pr:=@record_number_pr+1";
		$sql	.= $change.$groupFields. " FROM ($bsql) s";
		$sql	.= " LEFT JOIN (SELECT $varFields) v ON 1";

		$bsql=$sql;

		$sql = "SELECT * FROM ($sql) s";
		$union = "";
		$mi = " LEFT JOIN (";
		$j=  $groupNumber+1;
		for ($i=1; $i<=$j; $i++)
		{
			$union .= $mi."SELECT $i AS group_for_pr";
			$mi = " UNION ";
		}
		if(!empty($union)) $union .= ") u ON 1";
		$sql .= $union;

		$i=$groupNumber;
		$j=$i+1;
		$sql .= " WHERE (u.group_for_pr=$j AND last_rec_for_order=0)"
			. " OR (record_number_pr>0 AND s.akt_group_for_pr BETWEEN u.group_for_pr AND $i)";

		$mit = " ORDER BY last_rec_for_order, ";
		$i=$this->groupNumber;
		$j=$i+1;

		foreach ($this->mezok as $mezonev => $mezotomb)
		{
			if(isset($mezotomb['group']) && $mezotomb['group'])
			{
				$sql .= $mit."IF(group_for_pr=$j, $mezonev"."_original, $mezonev"."_elozo_rek), IF($i=group_for_pr, 1, 0)";
				$mit=", ";
				$i--;
			}
		}

		$sql .= $mit." datum";
		$bsql=$sql;

		$sql = "SELECT group_for_pr";
		$mi=", ";
		$vars="";
		$vmi=" LEFT JOIN (SELECT ";
		$i=$groupNumber;
		$j=$i+1;
		if($this->viewInterval)
		{
			$sql .= $mi."'".  $this->validFrom."' AS valid_from";
			$mit=', ';
			$sql .= $mi."'".  $this->validTo."' AS valid_to";
		}
		foreach ($this->mezok as $mezonev => $mezotomb)
		{
			$timeFunction=(isset($mezotomb['time_function']) && !empty($mezotomb['time_function']))?$mezotomb['time_function']:$this->timeFunction;
			if(
				   (isset($mezotomb['sum']) && $mezotomb['sum'])
				|| (isset($mezotomb['time']) && $mezotomb['time'])
				|| (isset($mezotomb['exists']) && $mezotomb['exists'])
				)
			{
				$mezo = "IF(group_for_pr=$j, $mezonev"."_original, ";
				$mezov=")";
				$mezo_sec = "";
				$secmi = "IF(group_for_pr=$j, $mezonev"."_original, ";
				$mezo_secv=")";
				for ($v=1; $v<$j; $v++)
				{
					$mezo .= "IF(group_for_pr=$v, @$mezonev"."_group_for_pr_$v, ";//)";
					$mezov .= ")";
					if(isset($mezotomb['sumpart']) && $mezotomb['sumpart'])
					{
						$mezo_sec .= $secmi."IF(group_for_pr=$v, @$mezonev"."_group_for_pr_$v, ";//)";
						$mezo_secv .= ")";
						$secmi="";
					}
					$vars .= $vmi."@$mezonev"."_group_for_pr_$v := 0";
					$vmi= ", ";
				}
				$mezo .= "0".$mezov;
				if(!empty($mezo_sec))
				{
					$mezo_sec .= "0".$mezo_secv;
				}
				if(isset($mezotomb['time']) && $mezotomb['time'])
				{
					$mezo = $timeFunction."($mezo)";
				}
				$sql .= $mi.$mezo." AS $mezonev";
				$mi=", ";
				if(!empty($mezo_sec))
				{
					$sql .= $mi.$mezo_sec." AS $mezonev"."_sec";
					$mi=", ";
				}
				for ($v=1; $v<$j; $v++)
				{
					$sql .= $mi."@$mezonev"."_group_for_pr_$v := IF(group_for_pr=$v, 0, @".$mezonev."_group_for_pr_$v+IF(group_for_pr=$j, $mezonev"."_original, 0))";
				}
			}
			else
			{
				$sql .= $mi."IF(group_for_pr=$j, $mezonev"."_original, IF(group_for_pr<=$i, $mezonev"."_elozo_rek, '')) AS $mezonev";
				$mi=", ";
			}
			if(isset($mezotomb['group']) && $mezotomb['group'])
			{
				$i--;
			}
		}
		$sql .= " FROM ($bsql) b";
		if(!empty($vars))
		{
			$sql .= $vars.") v ON 1";
		}

		$sql = "SELECT * FROM ($sql) s";

		$mit = " ORDER BY ";
		$i=$this->groupNumber;
		$j=$i+1;

		foreach ($this->mezok as $mezonev => $mezotomb)
		{
			if(isset($mezotomb['group']) && $mezotomb['group'])
			{
				$sql .= $mit."$mezonev, IF($i=group_for_pr, 1, 0)";
				$mit=", ";
				$i--;
			}
			elseif(isset($mezotomb['sort']) && $mezotomb['sort'])
			{
				$sql .= $mit."$mezonev, IF($i=group_for_pr, 1, 0)";
				$mit=", ";
			}
		}

		$bsql=$sql;

		$sql = "SELECT *";
		$mi=", ";
		$i=$groupNumber;
		$j=$i+1;
		foreach ($this->mezok as $mezonev => $mezotomb)
		{
			$timeFunction=(isset($mezotomb['time_function']) && !empty($mezotomb['time_function']))?$mezotomb['time_function']:$this->timeFunction;
			if(
				   (isset($mezotomb['sum']) && $mezotomb['sum'])
				|| (isset($mezotomb['time']) && $mezotomb['time'])
				|| (isset($mezotomb['exists']) && $mezotomb['exists'])
				)
			{
				$mezo_part="";
				$mezo_partv="";
				for ($v=1; $v<$j; $v++)
				{
					if(isset($mezotomb['sumpart']) && $mezotomb['sumpart'])
					{
						$p=$v-1;
						if($p==0)
						{
							$mezo_part .= "IF(group_for_pr=$j, @$mezonev"."_group_for_pr_$v+$mezonev"."_sec, ";//)";
						}
						else
						{
							$mezo_part .= "IF(group_for_pr=$p, @$mezonev"."_group_for_pr_$v, ";//)";
						}
						$mezo_partv .= ")";
					}
				}
				if(!empty($mezo_part))
				{
					$mezo_part .= "0".$mezo_partv;
				}
				if(isset($mezotomb['time']) && $mezotomb['time'])
				{
					if(!empty($mezo_part))
					{
						$mezo_part = $timeFunction."($mezo_part)";
					}
				}
				if(!empty($mezo_part))
				{
					$sql .= $mi.$mezo_part." AS $mezonev"."_group_for_pr";
					$mi=", ";
					for ($v=1; $v<$j; $v++)
					{
						$sql .= $mi."@$mezonev"."_group_for_pr_$v := IF(group_for_pr=$v, 0, @".$mezonev."_group_for_pr_$v+IF(group_for_pr=$j, $mezonev"."_sec, 0))";
					}
				}
			}
		}

		$sql .= " FROM ($bsql) b";

		if(!empty($vars))
		{
			$sql .= $vars.") v ON 1";
		}

		if (!$this->viewDetails)
		{
			$j=  $groupNumber+1;
			$sql = "SELECT * FROM ($sql) s WHERE group_for_pr != $j";
		}

		return $sql;
	}

	protected function getCommonSQL($filter, $frame = false)
	{
		WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__);
		$defaultEnd	= App::getSetting("defaultEnd");
		$bp = requestParam('referrer');

		$sql = "
		SELECT
				";
			$sql .= $this->getFrameBasePrefixTS();
			$sql .= "
		FROM
		(
			SELECT
				";
			$sql .= $this->getOtherBasePrefixTS();
			$sql .= "
			FROM
			(
				SELECT
					";
			$sql .= $this->getBasePrefixTS();
			$sql .= "
				FROM
				(
				";
				$sql .= $this->getBaseTimeSheet($frame);
				$sql .= "
				) frame
				";
				$sql .= $this->getBaseSuffixTS();
				$sql .= "
			) a
				";
				$sql .= $this->getOtherBaseSuffixTS();
				$sql .= "
		)r
				";
				$sql .= $this->getFrameBaseSuffixTS();
		return $sql;
	}

	protected function getBaseTimesheet($frame=false)
	{
		WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__);
		$curdate=date('Y-m-d');
		$filter =	array(	"interval"	=> array(	"valid_from"			=> !($this->frame || $this->allFrame) ? "{valid_from}" : "frame_begin",
													"valid_to"				=> "{valid_to}"
												),
							"employee"	=> array(	"company_id"			=> "{company}",
													"payroll_id"			=> "{payroll}"
												),
							"unit"		=> array(	"unit_id"				=> "{unit}"
												),
							"workgroup"	=> array(	"workgroup_id"			=> "{workgroup}"
												),
							"employee_contract"	=> array(
															"employee_contract_ids"	=> "{employee_contract}"
													),
					);

		$filter['filter_date'] = "{valid_to}";
		$filter['approver_date'] = "$curdate";

		$gwt = new GetWorktimePayrollFast();
		return $gwt->getWorktimeSql();
	}

	protected function search()
	{
		$searchArray = $this->getPreDefinedSearch(Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_FROM_TO, Grid2Controller::G2BC_SEARCH_WITH_DEFAULT_GROUP_FILTER, "workForce");
		$submit=array('submit' => $searchArray['submit']);
		unset($searchArray['submit']);

		$reportArray=[];
		$reportArray[]=array('id'=> '1', 'value' => 'Total/WG/Unit/Emp - WT, Nap'); //Dict::getValue("work_type_flexible")),
		$reportArray[]=array('id'=> '2', 'value' => 'Total/WG/Unit/Date - WT, Nap, Túlóra'); //Dict::getValue("work_type_flexible")),
		$reportArray[]=array('id'=> '3', 'value' => 'Total/Date/WG - WT, Nap, Túlóra'); //Dict::getValue("work_type_flexible")),
		$reportArray[]=array('id'=> '5', 'value' => 'Total/Date/WG - WT, Nap, Túlóra-szűrve WORKDAY-re'); //Dict::getValue("work_type_flexible")),
		$reportArray[]=array('id'=> '6', 'value' => 'Total/Date/WG - WT, Nap, Túlóra, lista szűrve WORKDAY-re'); //Dict::getValue("work_type_flexible")),
		$reportArray[]=array('id'=> '7', 'value' => 'Total/Date - WT, Nap, Túlóra, lista szűrve WORKDAY-re'); //Dict::getValue("work_type_flexible")),
		$reportArray[]=array('id'=> '8', 'value' => 'Date/Naptípus - Különböző ledolgozott idők'); //Dict::getValue("work_type_flexible")),
		$reportArray[]=array('id'=> '9', 'value' => 'Date/Dolgozó - Különböző ledolgozott idők'); //Dict::getValue("work_type_flexible")),
		$reportArray[]=array('id'=> '10', 'value' => 'Dolgozó/Date - Különböző ledolgozott idők'); //Dict::getValue("work_type_flexible")),

		$reportType = array(
			'report_type'	=>	array(
									'label_text'=>Dict::getValue("report_type"),
									'col_type'	=> 'combo',
									'options'	=>	array(
													'mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,
													'array'=> $reportArray,
												),
								),
		);

		$searchArray = Yang::arrayMerge($searchArray, $reportType);
		$submit = array
		(
			'submit'	=>	array(
								'col_type'		=>	'searchBarReinitGrid',
								'width'			=>	'*',
								'label_text'	=>	''
								),
		);
		$searchArray = Yang::arrayMerge($searchArray, $submit);
		return $searchArray;

	}

	public function columns()
	{
		$cols=[];
		if($this->viewInterval)
		{
			$cols['valid_from'] = array('export'=> true, 'report_width' => 5, 'col_type'=>'ro', 'edit' => false, 'align'=>'center');
			$cols['valid_to']	= array('export'=> true, 'report_width' => 5, 'col_type'=>'ro', 'edit' => false, 'align'=>'center');
		}
		foreach ($this->mezok as $mezonev => $mezotomb)
		{
			if(isset($mezotomb['view']) && $mezotomb['view'])
			{
				$center=(isset($mezotomb['sum']) && $mezotomb['sum']) ? 'center' : 'left';
				$cols[$mezonev] = array('export'=> true, 'report_width' => 5, 'col_type'=>'ro', 'edit' => false, 'align'=>$center);
			}
			if(isset($mezotomb['sumpart']) && $mezotomb['sumpart'])
			{
				$center=(isset($mezotomb['sum']) && $mezotomb['sum']) ? 'center' : 'left';
				$cols[$mezonev."_group_for_pr"] = array('export'=> true, 'report_width' => 5, 'col_type'=>'ro', 'edit' => false, 'align'=>$center);
			}
		}
		return $cols;
	}

	public function attributeLabels()
	{
		WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__);
		$cols=[];
		$cols['valid_from'] = 'Mettől';
		$cols['valid_to']	= 'Meddig';
		foreach ($this->mezok as $mezonev => $mezotomb)
		{
			if (isset($mezotomb['title']) && !empty($mezotomb['title']))
			{
				$cols[$mezonev] = $mezotomb['title'];
			}
			if(isset($mezotomb['sumpart']) && $mezotomb['sumpart'])
			{
				$cols[$mezonev."_group_for_pr"] = $mezotomb['title']."(össz.)";
			}
		}
		return $cols;
	}
}

?>