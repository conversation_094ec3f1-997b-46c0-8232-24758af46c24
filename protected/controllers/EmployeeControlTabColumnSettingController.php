<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use app\controllers\wfm\ExportTabController;
	use app\models\ColumnRights;
	use app\models\ColumnTabColumns;
	use app\models\Status;
	use Yang;

`/yii2-only';


/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy be<PERSON>, hogy a dolgozó kezelés felületen egy dolgozó kiválasztásánál a
 * felugró ablakon milyen mezők jelennek meg egy adott fülön.
 */
class EmployeeControlTabColumnSettingController extends ExportTabController
{
	/**
	 * Connects page url with this controller.
	 * Provides column header names.
	 * 
	 * @return void 
	 */
    public function __construct()
    {
        $this->baseControllerUrl = sprintf('%s/employeeControlTabColumnSetting', Yii::app()->request->baseUrl);
        $this->columnTitle1 = Dict::getModuleValue("ttwa-base", "associated_columns_to_tabs");
        $this->columnTitle2 = Dict::getModuleValue("ttwa-base", "available_columns_to_tabs");
    }

	/**
	 * Provides the index file which is rendered on the given url.
	 * Provides the name of the page.
	 *
	 * @return void
	 */
    public function actionIndex()
    {
        if (!App::hasRight('EmployeeControlTabSetting', 'view')) {
            $this->redirect(array(Yii::app()->params['permdeniedUrl']));
        }

        $this->layout = '//Grid2/layouts/indexLayout';
        $this->pageTitle = Dict::getModuleValue("ttwa-base", Yii::app()->name) . " - " . Dict::getModuleValue("ttwa-base", "page_title_employee_control_tab_column_setting");

        $this->render('/employeeControlTabColumnSetting/index', array(
            'title' => Dict::getModuleValue("ttwa-base", "page_title_employee_control_tab_column_setting"),
            'modify' => 0,
            'modifyFields' => 1,
        ));
    }

	/**
	 * Creates the xml for the tabs, rolegroups and their associated fields.
	 * It is rendered in the left column of the page.
	 *
	 * @return void
	 */
    public function actionGenerateTreeViewXML()
	{
		$companyName = $this->getCompanyName();

		if (stristr($_SERVER["HTTP_ACCEPT"], "application/xhtml+xml")) {
			header("Content-type: application/xhtml+xml");
		} else {
			header("Content-type: text/xml");
		}
		echo('<?xml version="1.0" encoding="utf-8" ?>' . "\n");
		echo('<tree id="0">' . "\n");

		$_SESSION['parentIds'] = array();

		$this->getParentItems();

		echo("</tree>\n");
    }
	
	/**
	 * Gets the tabs (parents) and renders them on the page in the left column.
	 *
	 * @return void
	 */
    protected function getParentItems (){
		$sql = "
            SELECT 
                `column_tabs`.`tab_id` as item_id, 
                `dictionary`.`dict_value` as item_text 
            FROM `column_tabs` 
            LEFT JOIN `dictionary`
                ON `column_tabs`.`dict_id` = `dictionary`.`dict_id`
                AND `dictionary`.`lang` = '" .Dict::getLang(). "' 
            WHERE `column_tabs`.`status` = " .$this->published;
		$rows = Yii::app()->db->createCommand($sql)->queryAll();

		if(is_array($rows) && count($rows) > 0) {
			foreach($rows as $row) {
				$itemId = $row['item_id'];
				$tags = ' im0="folderClosed.gif" im1="folderOpen.gif" im2="folderClosed.gif"';

				print("<item id='".$itemId."' text=\"". str_replace(['"', '&'],["&quot;", "&amp;"],$row['item_text']) ."\"$tags>\n");

				print $this->getRolegroupItems($itemId);

				print("</item>\n");
			}
		}
	}
	
	/**
	 * Gets the rolegroups and renders them in the tree under the selected tab folder in the left column
	 *
	 * @param string $selectedTabId	the id of the selected tab
	 * @return string $itemXML		the xml for the rolegroups to be rendered
	 */
	protected function getRolegroupItems($selectedTabId)
	{
		$itemXML = '';
		/*$sql = "
			SELECT 
				column_tab_rights.rolegroup_id as item_id,
				auth_rolegroup.rolegroup_name as item_text
			FROM column_tab_rights
			LEFT JOIN auth_rolegroup
				ON column_tab_rights.rolegroup_id = auth_rolegroup.rolegroup_id
			WHERE column_tab_rights.tab_id = '".$selectedTabId."'
				AND column_tab_rights.status = ".$this->published."
				AND auth_rolegroup.visibility = 1
		";*/
		$sql = "
		SELECT 
			auth_rolegroup.rolegroup_id as item_id,
			auth_rolegroup.rolegroup_name as item_text
		FROM auth_rolegroup
		WHERE auth_rolegroup.visibility = 1
		";
		$rows = Yii::app()->db->createCommand($sql)->queryAll();

		if(is_array($rows) && count($rows) > 0) {
			foreach($rows as $row) {
				$itemId = $row['item_id'];
				$tags = ' im0="folderClosed.gif" im1="folderOpen.gif" im2="folderClosed.gif"';

				$itemXML .= "<item id='".$selectedTabId."|".$itemId."' text=\"". str_replace(['"', '&'],["&quot;", "&amp;"],$row['item_text']) ."\"$tags>\n";

				$itemXML .= $this->getChildrenItems($selectedTabId, $itemId);
				$itemXML .= "</item>\n";
			}
		}

		return $itemXML;
	}
	
	/**
	 * Gets the columns (children) which are associated to the selected rolegroup and tab.
	 * Renders them under the folder of the selected rolegroup and tab.
	 *
	 * @param string $selectedTabId			the selected tab
	 * @param string $selectedRolegroupId	the selected rolegroup
	 * @return string $itemXML				the xml for the columns to be rendered
	 */
    protected function getChildrenItems($selectedTabId, $selectedRolegroupId)
	{
		$itemXML = "";
		$sql = "
                SELECT
                    column_tab_columns.column_id as item_id,
                    dictionary.dict_value as item_text
				FROM column_tab_columns
				LEFT JOIN column_rights
					ON column_tab_columns.column_id = column_rights.column_id
					AND column_rights.controller_id = 'employeecontrol'
					AND column_rights.status = ".$this->published."
					AND column_rights.rolegroup_id = '".$selectedRolegroupId."'
                LEFT JOIN `dictionary`
                    ON `column_tab_columns`.`column_id` = `dictionary`.`dict_id`
                    AND `dictionary`.`lang` = '" .Dict::getLang(). "' 
                    AND `dictionary`.`module` = 'ttwa-base' 
                WHERE column_tab_columns.status = ".$this->published." 
					AND column_tab_columns.tab_id = '".$selectedTabId."'
					AND column_rights.roles IS NOT NULL
                ";
		$rows = Yii::app()->db->createCommand($sql)->queryAll();

		if(is_array($rows) && count($rows) > 0) {
			foreach($rows as $row) {
				$itemId = $row['item_id'];
				$tags = ' im0="iconText.gif" im1="iconText.gif" im2="iconText.gif"';

				$itemXML .= "<item id='".$selectedTabId."|".$selectedRolegroupId."|".$itemId."' text=\"". str_replace(['"', '&'],["&quot;", "&amp;"],$row['item_text']) ."\"$tags>\n";

				$itemXML .= "</item>\n";
			}
		}
		
		return $itemXML;
    }
    
    /**
	 * Gets the columns and the associated columns to rolegroups and tabs.
	 * Filters the columns according to the search parameter provided by the user.
	 * 
     * @return void
    */
    public function actionLoadSortableContents()
    {
        $selectedTreeItem = Yii::app()->request->getParam('selectedTreeItem');
        $sortable1_filter = Yii::app()->request->getParam('sortable1_filter');
        $sortable2_filter = Yii::app()->request->getParam('sortable2_filter');

        $sortable1_filter = str_replace(" ", "%%", $sortable1_filter);
        $sortable2_filter = str_replace(" ", "%%", $sortable2_filter);

		$idArr = explode("|", $selectedTreeItem);
		$tabId = $idArr[0];
		$rolegroupId = $idArr[1];
		$columnId = $idArr[2];

		$usedFieldsSql = "
			SELECT 
                column_tab_columns.column_id as field_id,
				IFNULL(dictionary.dict_value,dict.dict_value) as field_name,
				column_rights.roles as role
			FROM column_tab_columns
			LEFT JOIN column_rights
				ON column_tab_columns.column_id = column_rights.column_id
				AND column_rights.controller_id = 'employeecontrol'
				AND column_rights.status = ".$this->published."
				AND column_rights.rolegroup_id = '".$rolegroupId."'
            LEFT JOIN dictionary
				ON column_tab_columns.column_id = dictionary.dict_id
                AND dictionary.lang = '" .Dict::getLang(). "' 
            LEFT JOIN base_absence_type
				ON base_absence_type.base_absence_type_id = column_tab_columns.column_id
				AND base_absence_type.status = 2
			LEFT JOIN dictionary dict
				ON base_absence_type.dict_id = dict.dict_id
			AND dict.lang = 'hu'
            WHERE column_tab_columns.status = ".$this->published."
                AND column_tab_columns.tab_id = '".$tabId."'
				AND (
					LOWER(dictionary.dict_value) LIKE '".mb_strtolower($sortable1_filter)."%' OR
					LOWER(dict.dict_value) LIKE '".mb_strtolower($sortable1_filter)."%'
				)
			GROUP BY column_tab_columns.column_id
			ORDER BY column_tab_columns.column_order
		";

		$availableFieldsSql = "
			SELECT
				columns.column_id as field_id,
				dictionary.dict_value as field_name
			FROM columns
			LEFT JOIN dictionary
				ON columns.column_id = dictionary.dict_id
                AND dictionary.lang = '" .Dict::getLang(). "' 
                AND dictionary.module = 'ttwa-base'
			WHERE LOWER(dictionary.dict_value) LIKE '".mb_strtolower($sortable2_filter)."%'
				AND columns.status = ".$this->published."
				AND columns.column_id NOT IN (".$this->getUsedColumnIds($usedFieldsSql).")
		";

        $this->loadSortableContents($usedFieldsSql, $availableFieldsSql);
    }

    /**
	 * When user drags one item from the available column (the right column) and drops it in the associated column
	 * (the middle column), the item is saved and connected to the rolegroup and tab which is selected in the tree (the left column).
	 * Vica versa the item is deleted from the connected rolegroup and tab, when the item is removed from the associated column.
	 * 
     * @return void
     */
    public function actionSaveReportFieldAssign()
    {
		$modelForTabColumns = new ColumnTabColumns();
		$modelForColumnRights = new ColumnRights();
        $from = Yii::app()->request->getParam('from');
		$to = Yii::app()->request->getParam('to');
		$id = Yii::app()->request->getParam('id');
		$columnsInOrder = Yii::app()->request->getParam('columnsInOrder');
		$user_id = Yii::app()->user->getId();

		$response = [
			'hasLoggedUser' => (int)!empty($user_id),
			'status' => 1,
			'id' => $id,
			'class' => get_class($modelForTabColumns),
        ];

        $this->checkIsEmptyParameter($response);
		$this->checkIncorrectParameterKeys($response);
		$explodedId = explode("|", $id, 3);
		
		if (count($explodedId) === 3) {
			$exp = explode("|", $id, 3);
			$exp = array_filter($exp);
			$exp = array_values($exp);
	
			$tabId = $exp[0];
			$rolegroupId = $exp[1];
			$columnId = $exp[2];
	
			if (!empty($user_id)) {
				// Add item to list
				if ($from === "sortable2" && $to === "sortable1") {
					$this->saveNewRecordIntoColumnTabColumns($columnId, $tabId, $user_id, $response, $id, $columnsInOrder);
					$this->saveNewRecordIntoColumnRights($columnId, $rolegroupId, $user_id, $response);
				}
				// Change items order
				elseif ($from === "sortable1" && $to === "sortable1") {
					$this->saveColumnsOrderInDatabase($columnsInOrder, $modelForTabColumns);
				}
				// Remove item from list
				else {
					$modelForTabColumns = $modelForTabColumns::model()->findByAttributes(array(
						'tab_id' => $tabId,
						'column_id' => $columnId,
						'status' => Status::PUBLISHED,
					));
	
					if ($modelForTabColumns) {
						$modelForTabColumns->delete();
						$this->saveColumnsOrderInDatabase($columnsInOrder, $modelForTabColumns);
					}
				}
			}
		} else {
			$response["status"] = 0;
		}
		
		$this->resultJson($response);
    }

	/**
	 * Saves the order of the columns which appear in the middle column of the page
	 * (The user can modify the order by dragging and dropping the elements)
	 *
	 * @param [type] $columnsInOrder
	 * @param [type] $modelForTabColumns
	 * @return void
	 */
    private function saveColumnsOrderInDatabase($columnsInOrder, $modelForTabColumns)
	{
		foreach ($columnsInOrder as $order => $column) {
			$modelForTabColumns = $modelForTabColumns::model()->findByAttributes(array(
				'tab_id' => explode("|", $column, 3)[0],
				'column_id' => explode("|", $column, 3)[2],
				'status' => Status::PUBLISHED,
			));
			$modelForTabColumns->column_order = $order;
			$modelForTabColumns->update();
		}
	}
	
	/**
	 * Saves new row into column_rights table in order to save the read or write right of the current column
	 *
	 * @param string $columnId		the id of the selected column
	 * @param string $rolegroupId	the id of the selected rolegroup
	 * @param string $user_id		the id of the logged user
	 * @param array $response		the response to be sent to frontend
	 * @return void
	 */
	private function saveNewRecordIntoColumnRights($columnId, $rolegroupId, $user_id, &$response)
	{
		$modelForColumnRights = new ColumnRights();
		$selectedModel = $modelForColumnRights::model()->findByAttributes(array(
			'controller_id' => 'employeecontrol',
			'column_id' => $columnId,
			'rolegroup_id' => $rolegroupId,
			'status' => Status::PUBLISHED,
		));

		if (is_null($selectedModel)) {
			$modelForColumnRights->controller_id = 'employeecontrol';
			$modelForColumnRights->column_id = $columnId;
			// $modelForColumnRights->model_name = '';
			$modelForColumnRights->rolegroup_id = $rolegroupId;
			$modelForColumnRights->status = Status::PUBLISHED;
			$modelForColumnRights->roles = 'read';
			$modelForColumnRights->created_by = $user_id;
			$modelForColumnRights->created_on = date("Y-m-d H:i:s");

			try {
				$response['result'] = (int) $modelForColumnRights->save();
				$response['message'] = $modelForColumnRights->getErrors();
			} catch (Exception $ex) {
				$response['message'] = $ex->getMessage();
			}
		} else {
			$response['message'] = 'Record already exists in column_rights table!';
		}
	}

	/**
	 * Saves new row into column_tab_columns table when new columns is associated to a tab
	 *
	 * @param string $columnId		the id of the selected column
	 * @param string $tabId			the id of the selected tab
	 * @param string $user_id		the id of the logged user
	 * @param array $response		the response to be sent to frontend
	 * @param string $id			the selected column under the selected rolegroup and tab
	 * @param array $columnsInOrder	the order of the columns appearing on the page
	 * @return void
	 */
	private function saveNewRecordIntoColumnTabColumns($columnId, $tabId, $user_id, &$response, $id, $columnsInOrder)
	{
		$modelForTabColumns = new ColumnTabColumns();
		$modelForTabColumns->tab_id = $tabId;
		$modelForTabColumns->column_id = $columnId;
		$modelForTabColumns->column_order = array_search($id, $columnsInOrder);
		$modelForTabColumns->status = Status::PUBLISHED;

		try {
			$response['result'] = (int) $modelForTabColumns->save();
			$response['message'] = $modelForTabColumns->getErrors();
			$this->saveColumnsOrderInDatabase($columnsInOrder, $modelForTabColumns);
		} catch (Exception $ex) {
			$response['message'] = $ex->getMessage();
		}
	}

	/**
	 * Takes the column ids and column names in an array and returns the column ids as strings
	 *
	 * @param string $usedFieldsSql	sql query to get the columns used by the selected tab
	 * @return string column ids as strings
	 */
	private function getUsedColumnIds($usedFieldsSql)
	{
		$usedColumns = Yii::app()->db->createCommand($usedFieldsSql)->queryAll();
		foreach ($usedColumns as $column) {
			$columnIds[] = $column['field_id'];
		}
		return "'".implode("','",$columnIds)."'";
	}

	/**
	 * Saves the read or write right of the selected column
	 *
	 * @return void
	 */
	public function actionSaveColumnRight()
	{

		$id = Yii::app()->request->getParam('id');
		$role = Yii::app()->request->getParam('role');
		$user_id = Yii::app()->user->getId();

		$exp = explode("|", $id, 3);
		$exp = array_filter($exp);
		$exp = array_values($exp);

		$rolegroupId = $exp[1];
		$columnId = $exp[2];

        if (!empty($user_id)) {
			$model = ColumnRights::model()->findByAttributes(array(
				'controller_id' => 'employeecontrol',
				'column_id' => $columnId,
				'status' => Status::PUBLISHED,
				'rolegroup_id' => $rolegroupId
			));

			if( is_null($model) ){
				$model = new ColumnRights();
				$attributes = [
					'controller_id' => 'employeecontrol',
					'column_id' => $columnId,
					'rolegroup_id' => $rolegroupId,
					'status' => Status::PUBLISHED,
					'created_by' => $user_id,
					'created_on' => date('Y-m-d H:i:s')
				];
				$model->attributes = $attributes;
			}else{
				$model->modified_on = date('Y-m-d H:i:s');
				$model->modified_by = $user_id;
			}

			$model->roles = $role;

			if($model->save()){
				$response = [
					'hasLoggedUser' => (int)!empty($user_id),
					'status' => 1,
					'id' => $id,
					'class' => get_class($model),
					'message' => Dict::getValue('successful_save')
				];
			}else{
				$response = [
					'hasLoggedUser' => (int)!empty($user_id),
					'status' => 2,
					'id' => $id,
					'class' => get_class($model),
					'message' => Dict::getValue('unsuccessful_save')
				];
			}
		}



		$this->resultJson($response);
	}
}
