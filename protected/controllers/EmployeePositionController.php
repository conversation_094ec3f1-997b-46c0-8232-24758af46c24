<?php

class EmployeePositionController extends Grid2Controller
{
	public $layout = '//layouts/main';
	private $user;
	private $parentTableName;
	private $parentIdName;
	private $parentValueName;
	private $groupHierarchy;

	public function __construct()
	{
		parent::__construct("employeePosition");
		$this->user    = userID();
		$this->groupHierarchy = App::getSetting('group_hierarchy');

		$groupHierarchy = explode(';', $this->groupHierarchy);
		$parentIndex = array_search('employee_position', $groupHierarchy)-1;
		if($parentIndex >= 0) {
			$this->parentTableName = $groupHierarchy[$parentIndex];
			$this->parentIdName = strpos($this->parentTableName, 'company_org_group') === 0 ? 'company_org_group_id' : $this->parentTableName.'_id';
			$this->parentValueName = strpos($this->parentTableName, 'company_org_group') === 0 ? 'company_org_group_name' : $this->parentTableName.'_name';
		}
	}

	protected function G2BInit() {
		$this->LAGridDB->setModelName("EmployeePosition");

		parent::setControllerPageTitleId("page_title_employee_position");
		parent::setExportFileName(Dict::getValue("export_file_employee_position"));

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		true);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);
		$this->LAGridRights->overrideInitRights("add",				true);
		$this->LAGridRights->overrideInitRights("modify",			true);
		$this->LAGridRights->overrideInitRights("delete",			true);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);

		$u = new EmployeePosition;
		$c = new CDbCriteria();
		$c->condition= "('{date}' BETWEEN `valid_from` AND default_end(`valid_to`)) AND `status`=".Status::PUBLISHED;
		$c->order = "employee_position_name ASC";

		$this->LAGridDB->setModelSelection($u, $c);

		parent::G2BInit();
	}

	public function search()
	{
		return array(
			'date' => array(
								'label_text' => Dict::getValue('date'),
								'default_value' => date("Y-m-d"),
								'col_type' => 'ed',
								'dPicker' => true
							),
			'submit'		=> array('col_type'=>'searchBarReloadGrid', 'width'=>'*', 'label_text'=>''),
		);
	}

	public function columns()
	{
		$id = ($this->user == '6acd9683761b153750db382c1c3694f6') ?
				['employee_position_id'		=> ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'edit'=>false],]:
				[];
		$column = [
			'employee_position_name'	=> array('export'=> true , 'window'=>true, 'col_type'=>'ed', 'width'=>'300'),
			'note'						=> array('export'=> true , 'window'=>true, 'col_type'=>'ed', 'width'=>'300'),
			'valid_from'				=> array('export'=> true , 'window'=>true, 'col_type'=>'ed', 'dPicker'=>true, 'col_align'=>'center', 'width'=>'100'),
			'valid_to'					=> array('export'=> true , 'window'=>true, 'col_type'=>'ed', 'dPicker'=>true, 'col_align'=>'center', 'width'=>'100'),
		];

		if($this->parentTableName) {
			$column['parent'] =
			[
				'export'=>true,'grid'=>true,'window'=>true ,'col_type'=>'combo', 'width'=>'*',
				'options' =>
				[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "SELECT 
									". $this->parentTableName . "." . $this->parentIdName . " as id,
									". $this->parentTableName . "." . $this->parentValueName . " as value
								FROM
									" . $this->parentTableName . "
								WHERE
								" . $this->parentTableName . ".`status` = " . Status::PUBLISHED . " AND ".$this->parentTableName.".`$this->parentValueName` LIKE '%%{search}%%'
								ORDER BY
									".$this->parentTableName.".`" . $this->parentValueName . "` ASC",
				],
			];
		}
		return Yang::arrayMerge($id,$column);
	}

}