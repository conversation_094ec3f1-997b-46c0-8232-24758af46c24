<?php

class ForgottenPasswordController extends Grid2Controller
{
	public function __construct() {
		date_default_timezone_set('UTC');
		parent::__construct("forgottenPassword");
	}

	protected function G2BInit() {
		$this->LAGridDB->setModelName("User");
	}

	public function actionIndex($layout = '//Grid2/layouts/indexLayout', $view = '/Grid2/empty', $params = array())
	{
		$this->layout = $layout;

		$id = requestParam('id');
		$lang = requestParam('lang');
		if (is_null($id) || is_null($lang) || strlen($lang) != 2 || strlen($id) != 128) { return; }
		$fpw = null;
		if (!empty($id))
		{
			$timeOut = 24 * 3600;
			$fpw_check = ForgottenPassword::model()->findByAttributes(["fpw_id" => $id, "fpw_used" => 0,]);
			// expired...
			if ($fpw_check && strtotime($fpw_check->created_on." +$timeOut second") < strtotime(date("Y-m-d H:i:s"))) {
				$fpw_check->fpw_used = 1;
				$fpw_check->save();
			}

			$fpw = ForgottenPassword::model()->findByAttributes(["fpw_id" => $id, "fpw_used" => 0,]);
		}

		if (!$fpw)
		{
			if (Yang::session('mobileView', 0) && Yang::session('mobileBrowser', 0)) {
				Yang::setSessionValue('errorMessage', Dict::getValueWithLang("fpw_expired", $lang));
				$this->redirect('/login/login');
            } else {
				$params["content"] = "
					<script type=\"text/javascript\">
						<!--
							notifidialog('".Dict::getValueWithLang("fpw_expired", $lang)."','".Dict::getValueWithLang("fpw_expired", $lang)."',true);
						-->
					</script>
				";
			}
		} else {
			$_SESSION["tiptime"]["fpw_id"] = $fpw->fpw_id;

			$user = new User;
			$crit = new CDbCriteria();
			$crit->condition = "`user_id` = '{$fpw->user_id}' AND `status` = 2 AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`,'".App::getSetting("defaultEnd")."')";
			$result = $user->find($crit);

			if ($result) {
				$params["content"] = "
					<script type=\"text/javascript\">
						<!--
							G2BAddModDialog('dhtmlxGrid', 'dhtmlxGrid', '{$result->row_id}' /*selected_ids*/, 1 /* 0: add, 1: mod, 2: details (not implemented) */, './dialog','./save', './gridData', null, '".Dict::getValueWithLang("fpw_forgotten_password", $lang)." - ".$result->username."',null, 'auto', 'auto', '".$lang."');

							function dialogSaveCallback(generateFrom, data, resp) {
								window.location.href = '/login/login';
							}
						-->
					</script>
				";
			}
		}

		if (Yang::session('mobileView', 0) && Yang::session('mobileBrowser', 0)) {
			$this->beginContent('//mobile/mobile.basic', ['error' => '', 'errorTitle' => Dict::getValue("an_error_occured")]);
			$this->renderPartial('//mobile/base/forgottenPassword', ["rowId" => $result->row_id, "title" => Dict::getValueWithLang("fpw_forgotten_password", $lang) . " - " . $result->username, "lang" => $lang]);
			$this->endContent();
		} else {
			$this->render($view, $params);
		}
	}

	public function columns() {
		$columns = [];

		$columns['password'] = array('grid' => false, 'export'=> false, 'col_type'=>'pw', 'line_break'=>true,);

		return $columns;
	}

	public function actionSave($data = array(), $modelName = null, $pk = null, $vOnly = false, $ret = false, $contentId = null)
	{
		$fpw_id = isset($_SESSION["tiptime"]["fpw_id"]) ? $_SESSION["tiptime"]["fpw_id"] : null;
		if (is_null($fpw_id) && !empty(requestParam("fpwId"))) { $fpw_id = requestParam("fpwId"); }

		if ($fpw_id)
		{
			$fpw = ForgottenPassword::model()->findByAttributes(["fpw_id" => $fpw_id, "fpw_used" => 0, "fpw_saved" => 0,]);

			if ($fpw)
			{
				$fpw->fpw_saved = 1;
				$fpw->fpw_used = 1;
				$success = parent::actionSave($data, $modelName, $pk, $vOnly, $ret);
                if (Yang::session('mobileView', 0) && Yang::session('mobileBrowser', 0)) {
					if (Yang::session('successMessage') === 1) {
						$fpw->save();
						$this->redirect('/login/login');
					}
				} else {
					if ($success) { $fpw->save(); }
				}
				return true;
			}
		}
        $lang = Dict::getLang();
		$status = array(
			'status'	=> 0,
			'pkSaved'	=> null,
			'error'		=> Dict::getValueWithLang("fpw_expired", $lang),
		);

        if (Yang::session('mobileView', 0) && Yang::session('mobileBrowser', 0)) {
			$this->redirect('/login/login');
		} else {
			echo json_encode($status);
		}
	}
}