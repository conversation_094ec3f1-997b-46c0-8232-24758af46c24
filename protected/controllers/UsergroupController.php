<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\Dict;
	use Yang;

`/yii2-only';


#yii2: done

class UsergroupController extends GridController
{
	public $layout = '//layouts/main';

	public function __construct()
	{
		parent::__construct("usergroup");
		$this->setModelName("Usergroup");
		parent::setTitle(Dict::getValue("page_title_usergroup"));
		parent::enableSubgrid(true);
		parent::exportSettings(Dict::getValue("export_file_usergroup"));
	}

	public function columns()
	{
		return array(
			'usergroup_id'				=> array('export'=> false, 'col_type'=>'ro'),
			'usergroup_name'			=> array('export'=> true , 'col_type'=>'ed'),
		);
	}
}
?>
