<?php

'yii2-only`;

	namespace app\controllers;
	use Yang;

`/yii2-only';


#yii2: done

class CompanyOrgGroup2Controller extends CompanyOrgGroupController
{
	public function __construct()
	{
		parent::__construct( 'CompanyOrgGroup2');
	}

	public function columns()
	{
		$column = parent::columns();
		if(App::getSetting('specialFrameDaysGroup') == 'company_org_group2')
		{
			// fel kell venni az app_lookup értékeket, ha majd hasz<PERSON>lni kell (sinia keretegyenleg kezelés miatt 1558)
			$column['special_frameday_option']	= [
				'grid'		=> true,
				'window'	=> true ,
				'col_type'	=> 'combo',
				'width'		=> '300',
				'options'	=> [
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> "SELECT 
										`lookup_value` as id,
										`dict_value` as value
									FROM
										`app_lookup`
									LEFT JOIN `dictionary` ON
											`app_lookup`.`dict_id` 	= `dictionary`.`dict_id`
										AND `dictionary`.`valid`	= 1
										AND `dictionary`.`lang`		= '" . Dict::getLang() . "'
									WHERE
											`app_lookup`.`valid`		= 1
										AND `app_lookup`.`lookup_id`	= 'yes_no'
									ORDER BY
										`dict_value` ASC",
					],
			];
		}

		if ($this->usedGroup == 'company_org_group2' || strpos( $this->heatmapGroups, 'company_org_group2') !== false) {
			$column["minimal_group_count"]['grid'] = true;
			$column["minimal_group_count"]['window'] = true;
			$column["minimal_group_count"]['export'] = true;
		}
		return $column;
	}
}
