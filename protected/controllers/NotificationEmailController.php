<?php

/**
 * Értesítő email kiküldő controller
 */
class NotificationEmailController extends Controller
{
	private $path = '';
	private $defaultEnd;
	private $published = Status::PUBLISHED;

	/**
	 * A paramétereket tartalmazó tábla alapján lekéri adatbázisból a szükséges adatokat és a view fileoknak megfelelően elküldi emailben a megadott címekre.
	 * Az url-ben egy kö<PERSON> (pid) és egy opcionális (debug) paramétert vár.
	 * pid: a notification_email_config process_id oszlopában található érték, ez határozza meg, hogy melyik lekérdezés fusson
	 * debug: ha 1-es értéket kap, akkor kiírja, hogy milyen adatok jöttek az adatbázisból és milyen címekre küldte el, különben nem ír ki semmit
	 */
	public function actionIndex()
	{
		$pid = requestParam('pid');
		$debug = requestParam('debug') && requestParam('debug') === '1';
		$output = '';
		$this->defaultEnd = App::getSetting("defaultEnd");

		if (isset($pid) && !empty($pid))
		{
			$config = dbFetchRow("SELECT * FROM `notification_email_config` WHERE process_id = '{$pid}' AND status = '" . $this->published . "'");

			if (isset($config['sql']) && !empty($config['sql']))
			{
				$params = $this->getParams($config);

				$sql = str_replace(array_keys($params), array_values($params), $config['sql']);
				Yang::log($sql, 'log', 'system.SOAD');
				$sql_array = explode(";", $sql);

				$content = [];
				$empty = \TRUE;
				$csv = "";
				$empComp = [];
				$apprContent = [];
				$approvers = [];
				$empIds = [];
				foreach ($sql_array as $mail_sql)
				{
					$res = dbFetchAll($mail_sql);

					if ($config['data_to_csv']) {
						$csv .= ArrayToCsv::getCsvText($res);
					}

					if (!empty($res))
					{
						if (App::getSetting('sendEmptyNotificationEmail') == 0)
						{
							$empty = \FALSE;
						}
						$content[] = $res;

						if($debug){
							echo '<pre>';
							var_dump($content);
							echo '</pre>';
						}
					}

					if ($config['sendToEmployees'] == 1)
					{
						foreach ($res as $row)
						{
							$empComp[$row['email']]['expiring_thing'][] = ['expiring_thing' => $row['expiring_thing'],
																		'expiring_date' => $row['expiring_date']];
							$empComp[$row['email']]['fullname']		= $row['fullname'];
						}
					}

				}

				if ($config['data_to_csv'])
				{
					$this->path = Yang::getAlias('webroot').DIRECTORY_SEPARATOR."upload".DIRECTORY_SEPARATOR."PayrollTransfer".
							DIRECTORY_SEPARATOR.Yang::getParam('customerDbPatchName').DIRECTORY_SEPARATOR.date('YmdHis');
					if (!is_dir($this->path)){
						mkdir($this->path, 0777, true);
					}

					$fgID = md5(__CLASS__.date('YmdHis').userID().rand(0,1000000));

					$fileName = "expiringCompetencyMail.csv";
					$csv = iconv("UTF-8", "ISO-8859-2", $csv);
					$fs = new FS;
					$fs->disableMySQLStore();
					$fs->setFileGroupID($fgID);
					$fs->uploadTextFileFromContent($fileName, $csv, "text/csv");

					$file = fopen($this->path.DIRECTORY_SEPARATOR.$fileName, 'w');
					fwrite($file, $csv);
					fclose($file);
				}

				// ki kell küldeni az üres leveleket is 18.12.04 14:00
				if(!$empty || $config['sendEmptyNotificationEmail'] == 1){
					$addressee = explode(';', $config['addresses']);
					$roleGroups = str_replace(";","','", $config['rolegroup_ids']);
					if (!empty($roleGroups))
					{

						foreach ($res as $row)
						{
							if (!in_array($row['emp_id'], $empIds))
							{
								$empIds[] = $row['emp_id'];
								$actualApprovers = $this->getApproverUsersByEmpID($row['emp_id'], $roleGroups);

								foreach ($actualApprovers as $actualApprover)
								{
									$approvers[$actualApprover['email']]['empId'][]	= $actualApprover['empId'];
									$approvers[$actualApprover['email']]['fullname'] = $actualApprover['fullname'];
								}
							}
						}

						foreach ($approvers as $key=>$approver)
						{
							foreach ($res as $row)
							{
								foreach ($approver['empId'] as $empId)
								{
									if ($empId === $row['emp_id'])
									{
										$apprContent[$key][] = $row;
									}
								}
							}
						}
					}

					foreach ($addressee as $address)
					{
						$employee_name = '';
						if (!empty($address)) {
							$addr = $this->getEmployeeName($address);

							if($addr){
								$employee_name = (empty($addr['fullname']) ? strstr($addr['email'], '@', true) : $addr['fullname']);
							} else {
								$employee_name = strstr($address, '@', true);
							}

							if($this->sendMail($address, $config, $employee_name, $content, $params)) {
								$output .= 'Mail sent to ' . $address . '<br/>';
							} else {
								$output .= 'Something went wrong with sending to ' . $address . '<br/>';
							}
						}
					}

					foreach ($empComp as $key=>$value)
					{
						$employee_name = '';
						if (!empty($value)) {
								if($this->sendMail($key, $config, $value['fullname'], $value, $params)) {
								$output .= 'Mail sent to ' . $key . '<br/>';
							} else {
								$output .= 'Something went wrong with sending to ' . $key . '<br/>';
							}
						}
					}

					foreach ($apprContent as $userAddress=>$value)
					{
						$employee_name = '';

						if (filter_var($userAddress, FILTER_VALIDATE_EMAIL))
						{
							$addr = $this->getEmployeeName($userAddress);
							if($addr){
								$employee_name = (empty($addr['fullname']) ? strstr($addr['email'], '@', true) : $addr['fullname']);
							} else {
								$employee_name = strstr($address, '@', true);
							}

							if($this->sendMail($userAddress, $config, $employee_name, [$value], $params)) {
								$output .= 'Mail sent to ' . $userAddress . '<br/>';
							} else {
								$output .= 'Something went wrong with sending to ' . $userAddress . '<br/>';
							}
						}
					}
				} else {
					$output .= 'Empty result!';
				}
			} else {
				$output .= 'Missing SQL query!';
			}
		} else {
			$output .= 'Missing process id!';
		}

		if($debug){
			echo $output;
		}
	}

	/**
	 * Viszaadja azokat az értékeket, amikre le kell cserélni az adatbázis lekérdezés bizonyos elemeit, mert vagy
	 * nem tudjuk még a megíráskor, vagy egyszerűbb így
	 * @param array $config - a pid alapján lekérdezett adatbázis sor
	 * @return array
	 */
	private function getParams($config)
	{
		return [
					'{full_name}' => Employee::getParam('fullname', 'e'),
					'{end_date}' => date('Y-m-d', strtotime(date('Y-m-d') . ' + ' . $config['day_before_event'] . ' days')),
				];
	}

	/**
	 * A kapott email cím alapján visszaadja a hozzá tartozó dologzó nevét
	 * @param string $address - email cím
	 * @return array
	 */
	private function getEmployeeName($address)
	{
		$SQL = "
			SELECT
				email,
				IF(u.`employee_id` IS NULL OR u.`employee_id` = '', u.`username`, " . Employee::getParam('fullname', 'e') . ") as fullname
			FROM user u
			LEFT JOIN employee e ON
					u.employee_id = e.employee_id
				AND e.`status` = " . $this->published . "
				AND NOW() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '" . $this->defaultEnd ."')
			WHERE
					u.email = '{$address}'
				AND u.status = " . $this->published . " AND NOW() BETWEEN u.valid_from AND u.valid_to";

		return dbFetchRow($SQL);
	}

	/**
	 * Kiküldi az emailt a paramétereknek megfelelő címre és formában
	 * @param string $address - email cím
	 * @param array $config - a pid alapján lekérdezett adatbázis sor
	 * @param string $employee_name - dolgozó neve
	 * @param array $content - az email tartalma
	 * @return boolean
	 */
	private function sendMail($address, $config, $employee_name, $content, $params)
	{
		$attachmentPath = $config['data_to_csv'] ? $this->path.DIRECTORY_SEPARATOR.'expiringCompetencyMail.csv' : "";
		$attachmentFileName = $config['data_to_csv'] ? 'expiringCompetencyMail.csv' : "";
		$es = new EmailSender($attachmentPath, $attachmentFileName);
		return $es->sendMail(
							/* $addresses */
							[
								'addr' => [
									[
										'email'	=> $address,
									],
								],
							],
							/* $subject */
							Dict::getValue($config['subject']) ? : $config['subject'],
							/* $message */
							'',
							/* $view */
							['application.views.notification', $config['file_name']],
							/* $vars */
							['employee_name' => $employee_name, 'mailContent' => $content, 'params' => $params, 'messageText' => $config['message_text_dict_id']],
							/* $images */
							[],
							/* $iCal */
							false,
							/* $iCalString */
							"",
							/* $notSkipAppSettings */
							\TRUE
						);
	}

	public function getApproverUsersByEmpID($empId, $roleGroups)	//TODO
	{
		$art = new ApproverRelatedGroup;
		if (weHaveModule('ttwa-csm')) {
			$geaSQL = $art->getEmployeeApprovers("Employee", "competency", "CURDATE()");
		} else {
			$geaSQL = $art->getEmployeeApprovers("Employee", "employeeManagement", "CURDATE()");
		}
		$SQL  = "";

		$SQL .= "
			SELECT
				DISTINCT `user`.`email`,
				" . Employee::getParam('fullname', 'e') ." AS fullname,
				`employee`.`emp_id` AS empId
			FROM `employee_contract`
			LEFT JOIN
				`employee`
				ON `employee_contract`.`employee_id` = `employee`.`employee_id`
					AND CURDATE() BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '" . $this->defaultEnd . "')
					AND `employee`.`status` = " . $this->published . "
			LEFT JOIN
				`user`
				ON CURDATE() BETWEEN `user`.`valid_from` AND IFNULL(`user`.`valid_to`, '" . $this->defaultEnd . "')
					AND `user`.`status` = " . $this->published . "
					AND `user`.`rolegroup_id`  IN ('$roleGroups')
			LEFT JOIN
				`employee` e
				ON e.`employee_id` = `user`.`employee_id`
					AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '" . $this->defaultEnd . "')
					AND e.`status` = " . $this->published . "
		";

		if (weHaveModule('ttwa-csm'))
		{
			$SQL .= " LEFT JOIN `employee_competency` AS employee_competency
						ON employee_competency.`employee_contract_id` = employee_contract.`employee_contract_id`
							AND employee_competency.`status` = " . $this->published . "
				";
		}

		if (EmployeeGroupConfig::isActiveGroup('unit_id')) {
			$SQL .= EmployeeGroup::getLeftJoinSQL("unit_id","employee_contract", "CURDATE()");
		}

		if (EmployeeGroupConfig::isActiveGroup('workgroup_id')) {
			$SQL .= EmployeeGroup::getLeftJoinSQL("workgroup_id","employee_contract", "CURDATE()");
		}

		if (EmployeeGroupConfig::isActiveGroup('company_org_group1_id')) {
			$SQL .= EmployeeGroup::getLeftJoinSQL("company_org_group1_id","employee_contract", "CURDATE()");
		}

		if (EmployeeGroupConfig::isActiveGroup('company_org_group2_id')) {
			$SQL .= EmployeeGroup::getLeftJoinSQL("company_org_group2_id","employee_contract", "CURDATE()");
		}

		if (EmployeeGroupConfig::isActiveGroup('company_org_group3_id')) {
			$SQL .= EmployeeGroup::getLeftJoinSQL("company_org_group3_id","employee_contract", "CURDATE()");
		}

		$SQL .= "
				LEFT JOIN `company` ON
						`company`.`company_id` = `employee`.`company_id`
					AND `company`.`status` = {$this->published}
					AND CURDATE() BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `payroll` ON
						`payroll`.`payroll_id` = `employee`.`payroll_id`
					AND `payroll`.`status` = {$this->published}
					AND CURDATE() BETWEEN `payroll`.`valid_from` AND IFNULL(`payroll`.`valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `workgroup`
					ON `workgroup`.`workgroup_id` = " . EmployeeGroup::getActiveGroupSQL("workgroup_id","employee_contract") . "
						AND `workgroup`.`status` = " . $this->published . "
						AND CURDATE() BETWEEN `workgroup`.`valid_from` AND IFNULL(`workgroup`.`valid_to`, '" . $this->defaultEnd . "')
				LEFT JOIN `unit`
					ON `unit`.`status` = " . $this->published . "
						AND `unit`.`unit_id` = " . EmployeeGroup::getActiveGroupSQL("unit_id","employee") . "
						AND CURDATE() BETWEEN `unit`.`valid_from` AND IFNULL(`unit`.`valid_to`, '" . $this->defaultEnd . "')
				LEFT JOIN `company_org_group1`
					ON `company_org_group1`.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL('company_org_group1_id','employee') . "
						AND `company_org_group1`.`status` = " . $this->published . "
						AND CURDATE() BETWEEN `company_org_group1`.`valid_from` AND IFNULL(`company_org_group1`.`valid_to`, '" . $this->defaultEnd . "')
				LEFT JOIN `company_org_group2`
					ON `company_org_group2`.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL('company_org_group2_id','employee') . "
						AND `company_org_group2`.`status` = " . $this->published . "
						AND CURDATE() BETWEEN `company_org_group2`.`valid_from` AND IFNULL(`company_org_group2`.`valid_to`, '" . $this->defaultEnd . "')
				LEFT JOIN `company_org_group3`
					ON `company_org_group3`.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL('company_org_group3_id','employee') . "
						AND `company_org_group3`.`status` = " . $this->published . "
						AND CURDATE() BETWEEN `company_org_group3`.`valid_from` AND IFNULL(`company_org_group3`.`valid_to`, '" . $this->defaultEnd . "')
				";

		if (isset($geaSQL["JOIN"])) {
			$SQL .= $geaSQL["JOIN"];
		}

		$SQL .= "
			WHERE
				`employee`.`emp_id` LIKE '$empId'
				AND `user`.`receive_email` = 1
				AND CURDATE() BETWEEN employee_contract.`valid_from`
				AND IFNULL(employee_contract.`valid_to`, '" . $this->defaultEnd . "')
				AND CURDATE() BETWEEN employee_contract.`ec_valid_from`
				AND IFNULL(employee_contract.`ec_valid_to`, '".$this->defaultEnd."')
				AND employee_contract.`status` = " . $this->published . "
				AND `user`.`email` IS NOT NULL
                AND `user`.`email` <> ''
		";

		$results = dbFetchAll($SQL);

		return $results;
	}
}