<?php 

class DocumentTemplateManagementController extends Grid2HistoryController
{
	private string $publishedStatus;
	private string $defaultEnd;
	private string $lang;

	public function __construct()
	{
		parent::__construct("documentTemplateManagement");

		$this->publishedStatus = Status::PUBLISHED;
		$this->defaultEnd = App::getSetting('defaultEnd');
		$this->lang = Dict::getLang();
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("DocumentTemplate");

		parent::setControllerPageTitleId("page_title_document_template_management");

		$this->LAGridRights->overrideInitRights("reload",			true);
		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("init_open_search",	false);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("add", 				true);
		$this->LAGridRights->overrideInitRights("modify", 			true);
		$this->LAGridRights->overrideInitRights("delete", 			true);

		$multiSelectConditions = $this->getMultiSelectConditions();

		$model = new DocumentTemplate;
		$crit = new CDbCriteria();
		$crit->condition = "
                (`template_category_id` IN ('" . $multiSelectConditions["templateCategoryIds"] . "') OR 'ALL' IN ('" . $multiSelectConditions["templateCategoryIds"] . "')) AND "
			. "(`template_name` IN ('" . $multiSelectConditions["templateNames"] . "') OR 'ALL' IN ('" . $multiSelectConditions["templateNames"] . "')) AND "
			. "(`template_type` IN ('" . $multiSelectConditions["templateTypes"] . "') OR 'ALL' IN ('" . $multiSelectConditions["templateTypes"] . "')) AND "
			. "('{valid_date}' = '' OR ('{valid_date}' BETWEEN `valid_from` AND default_end(`valid_to`))) AND "
			. " `status`= {$this->publishedStatus}";
		$crit->order = "`template_type`";
		$this->LAGridDB->setModelSelection($model, $crit, "dhtmlxGrid");
		parent::G2BInit();
	}

	protected function search()
	{
		$searchFields = $this->getPreDefinedSearchFromDb();

		unset($searchFields["submit"]);

		$searchFields["valid_date"]["default_value"] = date("Y-m-d");

		$searchFields["template_category_id"] 	= 	[
													'col_type'		=> 'combo',
													'label_text'	=> Dict::getValue("temporary_category_id"),
													'multiple'		=> 1,
													'class'			=> 'customSelect2Class',
													'default_value' => 'ALL',
													'options'		=>	[
																			'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																			'sql'	=> $this->getTemplateCategorySQL(),
																			'array' => [["id"=>"ALL","value"=>Dict::getValue("all")]]
																		],
													];

		$searchFields["template_type"] 			= 	[
													'col_type'		=> 'combo',
													'label_text'	=> Dict::getValue("template_type"),
													'multiple'		=> 1,
													'class'			=> 'customSelect2Class',
													'default_value' => 'ALL',
													'options'		=>	[
																			'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																			'sql'	=> $this->getTemplateTypeSQL(),
																			'array'	=> [["id"=>"ALL","value"=>Dict::getValue("all")]]
																		],
													];

		$searchFields["template_name"] 			= 	[
													'col_type'		=> 'combo',
													'label_text'	=> Dict::getValue("template_name"),
													'multiple'		=> 1,
													'class'			=> 'customSelect2Class',
													'default_value' => 'ALL',
													'options'		=>	[
																			'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																			'sql'	=> $this->getTemplateNameSQL(),
																			'array'	=> [["id"=>"ALL","value"=>Dict::getValue("all")]]
																		],
													];
		$searchFields["submit"]			= ['col_type' => 'searchBarReloadGrid', 'width' => '*', 'label_text' => ''];

		return $searchFields;
	}

	public function columns()
	{
		return
		[
			'template_type' 		=> 	[
											'grid'			=>	true,
											'window'		=>	true,
											'col_type'		=>	'ed',
											'align'			=>	'center',
											'dialog_width'	=>  '2',
											'width'			=> 	'*'
										],
			'template_category_id'	=> 	[
											'grid'			=>	true,
											'window'		=>	true,
											'col_type'		=>	'combo',
											'align'			=>	'center',
											'dialog_width'	=>  '2',
											'width'			=> 	'*',
											'options'		=>	[
																'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																'sql'	=> $this->getTemplateCategorySQL(),		
																'array'	=> [["id" => "","value" => Dict::getValue("choose_option")]],
																]
										],
			'template_name'			=>	[
											'grid'			=>	true,
											'window'		=>	false,
											'col_type'		=>	'ed',
											'align'			=>	'center',
											'width'			=> 	'650'
										],
			'valid_from'			=>	['grid'=> true, 'col_type'=>'ed', 'align' => 'center', 'window' => true, 'dPicker' => true, 'width' => '*'],
			'valid_to'				=>	['grid'=> true, 'col_type'=>'ed', 'align' => 'center', 'window' => true, 'dPicker' => true, 'width' => '*'],
			'fs_file_id'			=> 	[
											'grid'						=> false,
											'window'					=> true,
											'dialog_width'				=> '2',
											'col_type'					=> 'templateUpload',
											'usePlaceholderValidator' 	=> true,
										]
		];
	}

	public function attributeLabels()
	{
		return array(
			'template_category_id'	=> Dict::getValue("templateCategory"),
			'template_type'			=> Dict::getValue("templateType"),
			'template_name'			=> Dict::getValue("templateName"),
			'fs_file_id'			=> Dict::getValue("file"),
			'valid_from'			=> Dict::getValue("valid_from"),
			'valid_to'				=> Dict::getValue("valid_to")
		);
	}

	private function getMultiSelectConditions()
	{
		$filter = requestParam('searchInput');

		return [
			"templateCategoryIds" 	=> $this->checkMultiselectConditionType($filter["template_category_id"]),
			"templateTypes" 		=> $this->checkMultiselectConditionType($filter["template_type"]),
			"templateNames" 		=> $this->checkMultiselectConditionType($filter["template_name"])
		];
	}

	private function getTemplateCategorySQL()
	{
		$SQL = "
			SELECT	
				al.`lookup_value` as id,
				d.`dict_value` as value
			FROM `app_lookup` al
			LEFT JOIN `dictionary` d ON
					d.`dict_id` = al.`dict_id`
				AND d.`lang` = '{$this->lang}'
				AND d.`valid` = 1
			WHERE
					al.`lookup_id` = 'document_template_category'
				AND al.`valid` = 1
			ORDER BY value ASC
		";

		return $SQL;
	}

	private function getTemplateTypeSQL()
	{
		$SQL = "
			SELECT 
				`template_type` as id,
				`template_type` as value
			FROM `document_template`
			WHERE `status` = {$this->publishedStatus}
			ORDER BY value
		";

		return $SQL;
	}

	private function getTemplateNameSQL()
	{
		$SQL = "
			SELECT 
				`template_name` as id,
				`template_name` as value
			FROM `document_template`
			WHERE `status` = {$this->publishedStatus}
			ORDER BY value
		";

		return $SQL;
	}

	private function checkMultiselectConditionType($searchInputField) 
	{
		return is_array($searchInputField) ? implode("','", $searchInputField) : "'" . $searchInputField . "'";
	}
}
