<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\models\Status;
	use Yang;

`/yii2-only';


#yii2: done

class GenerateActiveEmployeeCsvController extends Controller
{
	public $published = Status::PUBLISHED;
	public $defaultEnd;
	public $filename = 'sample.csv';
	public $folder;

	public function __construct()
	{
		$this->defaultEnd = App::getSetting('defaultEnd');
		if( is_null($this->folder) ){
			/*$this->folder = Yang::getAlias('ftp.nemak_user');*/
			$this->folder = __DIR__.'/../../ftp/nemak_user';
		}

		$this->filename = 'reports_'.date('YmdHis').'.csv';

	}

	public function actionIndex()
	{
		$SQL = "
		SELECT 
		    CONCAT(e.last_name, ' ', e.first_name) AS nev,
		   	IFNULL(cc.cost_center_name,'') AS koltseghely,
		   	IFNULL(ep.employee_position_name, '') AS munkakor,
			IFNULL(ecard.card, '') AS belepokartya,
		   	ec.ec_valid_from AS allomanybalepes_datuma,
			IFNULL(e.emp_id, '') AS dolgozo_azonositoja
		FROM employee e
		LEFT JOIN employee_contract ec 
		    ON ec.employee_id = e.employee_id 
		   	AND ec.ec_valid_from BETWEEN e.valid_from AND IFNULL(e.valid_to, ".$this->defaultEnd.")
		   	AND ec.status = ".$this->published." 
		LEFT JOIN employee_cost eco 
			ON eco.employee_contract_id = ec.employee_contract_id 
			AND eco.status = ".$this->published."
			AND CURDATE() BETWEEN ec.valid_from AND IFNULL(ec.valid_to, ".$this->defaultEnd.")
		LEFT JOIN cost_center cc
			ON cc.cost_center_id = eco.cost_center_id
			AND cc.status = ".$this->published."
			AND CURDATE() BETWEEN ec.valid_from AND IFNULL(ec.valid_to, ".$this->defaultEnd.") 
		LEFT JOIN employee_position ep
			ON ep.employee_position_id = ec.employee_position_id
			AND ep.status = ".$this->published."
			AND CURDATE() BETWEEN ec.valid_from AND IFNULL(ec.valid_to, ".$this->defaultEnd.")
		LEFT JOIN employee_card ecard
			ON ecard.employee_contract_id = ec.employee_contract_id
			AND ecard.status = ".$this->published."
			AND CURDATE() BETWEEN ec.valid_from AND IFNULL(ec.valid_to, ".$this->defaultEnd.")
		WHERE 
		e.status = ".$this->published."
		AND CURDATE() BETWEEN ec.valid_from AND IFNULL(ec.valid_to, ".$this->defaultEnd.")
		AND CURDATE() BETWEEN e.valid_from AND IFNULL(e.valid_to, ".$this->defaultEnd.")
		GROUP BY e.emp_id
		ORDER BY e.last_name ASC
		";

		$results = dbFetchAll($SQL);

		if( !file_exists($this->folder) ){
			if (!mkdir($this->folder, 0777, true) && !is_dir($this->folder)) {
				throw new Exception('There\'s no such file or directory and cannot make it: '.$this->folder);
				return false;
			}
		}

		$fp = fopen($this->folder.'/'.$this->filename, 'wb');

		$head = [
			'Dolgozó neve',
			'Dolgozó költséghelye',
			'Dolgozó munkakörének megnevezése',
			'Belépő kártyájának azonosítószáma',
			'Állományba lépésének dátuma',
			'Dolgozói azonosítószáma',
		];

		fputcsv($fp, $head, ',');

		foreach ( $results as $result ){
			fputcsv($fp, $result, ',');
		}

		fclose($fp);
	}

}

?>