<?php #yii2: done

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2\Grid2Controller;
	use app\models\Status;
	use Yang;

`/yii2-only';


#yii2: done

class WorkActivityController extends Grid2Controller
{
	private $_tableName = '';
	private $_modelId = '';
	private $_modelValue = '';
	private $_grandParentTableName = '';
	private $_grandParentModelId = '';
	private $_grandParentModelValue = '';
	private $_hasParent = false;
	private $_hasGrandParent = false;
	private $_groupHierarchy = [];
	private $_currentTableIndex = 0;
	private $_defaultEnd;
	private $_showWorkActivityId;
	
	public function __construct()
	{
		$this->_defaultEnd = App::getSetting("defaultEnd");
		$this->_groupHierarchy = explode(';', App::getSetting("group_hierarchy"));
		$this->_showWorkActivityId = App::getSetting("workactivitycontroller_show_work_activity_id");
		parent::__construct("workActivity");

		parent::enableLAGrid();
		
		$this->_currentTableIndex = array_search('work_activity', $this->_groupHierarchy);
		if (!is_null($this->_currentTableIndex) && $this->_currentTableIndex > 0)
		{
			$this->_hasParent = true;
			if ($this->_currentTableIndex > 1) {
				$this->_hasGrandParent = true;
			}
		}
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("WorkActivity");

		parent::setControllerPageTitleId("page_title_work_activity");

		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		true);

		$this->LAGridRights->overrideInitRights("add",				true);
		$this->LAGridRights->overrideInitRights("modify",			true);
		$this->LAGridRights->overrideInitRights("delete",			true);
		$this->LAGridRights->overrideInitRights("init_open_search",	false);

		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);

		if ($this->_hasParent) {
			$this->_tableName = $this->_groupHierarchy[$this->_currentTableIndex-1];
			$this->_modelId = strpos($this->_tableName, 'company_org_group') === 0 ? 'company_org_group_id' : $this->_tableName.'_id';
			$this->_modelValue = strpos($this->_tableName, 'company_org_group') === 0 ? 'company_org_group_name' : $this->_tableName.'_name';
		}
		if ($this->_hasGrandParent) {
			$this->_grandParentTableName = $this->_groupHierarchy[$this->_currentTableIndex-2];
			$this->_grandParentModelId = strpos($this->_tableName, 'company_org_group') === 0 ? 'company_org_group_id' : $this->_tableName.'_id';
			$this->_grandParentModelValue = strpos($this->_tableName, 'company_org_group') === 0 ? 'company_org_group_name' : $this->_tableName.'_name';
		}
		$sql = "
			SELECT
				work_activity.`row_id`,
				work_activity.`work_activity_id`,
				work_activity.`work_activity_name`,
				work_activity.`work_activity_category_id`
				" . ($this->_hasParent ? ", `connectedTable`.`".$this->_modelValue."` AS `work_activity_category_name`" : "") .
				($this->_hasGrandParent ? ", `connected2Table`.`".$this->_grandParentModelValue."` AS `" . $this->_grandParentModelValue . "`" : "") .
			"
			FROM
				`work_activity`
			";
		if ($this->_hasParent)
		{
			$sql .= "LEFT JOIN `".$this->_tableName."` connectedTable ON
							`connectedTable`.`".$this->_modelId."`=`work_activity`.`work_activity_category_id`
						AND `connectedTable`.`status` = ".Status::PUBLISHED."
						AND CURDATE() BETWEEN `connectedTable`.`valid_from` AND IFNULL(`connectedTable`.`valid_to`, '" . $this->_defaultEnd . "')
					";
		}
		if ($this->_hasGrandParent)
		{
			$sql .= "LEFT JOIN `" . $this->_grandParentTableName . "` connected2Table ON
							`connected2Table`.`" . $this->_grandParentModelId . "`=`connectedTable`.`parent`
						AND `connected2Table`.`status` = " . Status::PUBLISHED . "
						AND CURDATE() BETWEEN `connected2Table`.`valid_from` AND IFNULL(`connected2Table`.`valid_to`, '" . $this->_defaultEnd . "')
					";
		}
		
		$sql .= "WHERE
					work_activity.`status` = ".Status::PUBLISHED."
				ORDER BY
					work_activity.`work_activity_name`";

		$this->LAGridDB->enableSQLMode();
		$this->LAGridDB->setSQLSelection($sql, "row_id", "dhtmlxGrid");

		parent::G2BInit();
	}

	public function columns() {
		
		$id=($this->_showWorkActivityId)
				? ['work_activity_id'			=> ['grid'=>true, 'width'=>200, 'window'=> true, 'export'=> true, 'col_type'=>'ed',],]
				: [];
		
		$columns['work_activity_name'] = ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'ed',];
		if ($this->_hasParent)
		{
			$columns['work_activity_category_id'] = ['grid'=>false, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'combo',
														'options'	=> [
																			'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																			'sql'	=> "SELECT 
																							" . $this->_modelId . " as id,
																							" . $this->_modelValue . " as value
																						FROM
																							" . $this->_tableName . "
																						WHERE
																								`status` = " . Status::PUBLISHED . "
																							AND `" . $this->_modelValue . "` LIKE '%%{search}%%'
																						ORDER BY
																							`" . $this->_modelValue . "` ASC",
																		],
													];
		}
		$columns['work_activity_category_name']	= ['grid'=>true, 'width'=>200, 'window'=>false, 'export'=> true, 'col_type'=>'ed',];
		if ($this->_hasGrandParent)
		{
			$columns[$this->_grandParentModelValue] = ['grid'=>true, 'width'=>200, 'window'=>false, 'export'=> true, 'col_type'=>'ed',];
		}

		return Yang::arrayMerge($id,$columns);
	}
	
	public function attributeLabels()
	{
		$cols = [];
		
		$id=($this->_showWorkActivityId)
				? ['work_activity_id'			=> Dict::getValue("work_activity_id")]
				: [];
		
		$cols['work_activity_name']				= Dict::getValue("work_activity_name");
		$cols['work_activity_category_name']	= Dict::getValue("work_activity_category");
		if ($this->_hasGrandParent) {
			$cols[$this->_grandParentModelValue]	= Dict::getValue($this->_grandParentModelValue);
		}
		
		return Yang::arrayMerge($id,$cols);
	}

}