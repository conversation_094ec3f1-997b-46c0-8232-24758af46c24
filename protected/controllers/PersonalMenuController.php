<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Helpers\AnyCache;
	use app\models\Status;
	use app\models\User;
	use app\models\UserProfile;
	use Yang;

`/yii2-only';


class PersonalMenuController extends Controller
{
	public function actionIndex()
	{
		$this->render('index', array());
	}

	public function actionSave($property, $model = null, $value_integer = null, $value_string = null, $value_boolean = null)
	{
		$user_id = userID();

		if (isset($user_id) && isset($property) && ( !empty($value_integer) || !empty($value_string) || $value_boolean != null ))
		{
			$find_property = new UserProfile;
			$criteria = new CDbCriteria();
			$criteria->condition = "`user_id` = '$user_id' AND `property` = '$property'";
			if (isset($model))
			{
				$criteria->condition .= " AND `model` = '$model'";
			}
			$res_find_property = $find_property->find($criteria);

			if ($res_find_property == null) // insert
			{
				$user_profile = new UserProfile;

				$user_profile->user_id = $user_id;
				$user_profile->property = $property;

				if (isset($value_integer))
				{
					$user_profile->value_integer = $value_integer;
				}
				if (isset($value_string))
				{
					$user_profile->value_string = $value_string;
				}
				if (isset($value_boolean))
				{
					$user_profile->value_boolean = $value_boolean;
				}
				if (isset($model))
				{
					$user_profile->model = $model;
				}

				$user_profile->created_by = userID();
				$user_profile->created_on = date('Y-m-d H:i:s');

				$v = $user_profile->validate();
				$e = $user_profile->getErrors();

				$user_profile->save();
			}
			else //update
			{
				if (isset($value_integer))
				{
					$res_find_property->value_integer = $value_integer;
				}
				if (isset($value_string))
				{
					$res_find_property->value_string = $value_string;
				}
				if (isset($value_boolean))
				{
					$res_find_property->value_boolean = $value_boolean;
				}
				if (isset($model))
				{
					$res_find_property->model = $model;
				}

				$res_find_property->modified_by = userID();
				$res_find_property->modified_on = date('Y-m-d H:i:s');

				$v = $res_find_property->validate();
				$e = $res_find_property->getErrors();

				$res_find_property->save();
			}
		}
	}

	public function actionDeleteCurrentUser()
	{
		$user_id = userID();

		$criteria = new CDbCriteria();
		$criteria->condition = "`user_id` = '$user_id'";

		$user = User::model()->findAll($criteria);

		foreach ($user as $u)
		{
			$u->status = 4;

			$u->save();
		}
	}

	public function actionChangeLanguage()
	{
		AnyCache::clear();
		unset($_COOKIE['tiptime_language']);
		$lang_id = requestParam('id');
		$user_id = userID();
		$stPublished = Status::PUBLISHED;
		$defaultEnd  = App::getSetting('defaultEnd');
		$resp = [ 'status' => 'false' ];

		$user = new User;
		$crit = new CDbCriteria();
		$crit->condition = " `status` = $stPublished
							AND '" . date("Y-m-d") . "' BETWEEN `valid_from` AND IFNULL(`valid_to`, '$defaultEnd')
							AND `user_id` = '$user_id'";
		$user = $user->find($crit);

		if (!empty($user)) {
			$user->lang = $lang_id;
			$valid = $user->validate();
		}

		if ($lang_id) {
			if (!$valid) {
				echo json_encode($resp);
			} else {
				$user->save();
				unset($_SESSION['tiptime']['dict']);
				$cn = 'tiptime_language';
				Yang::setCookie($cn, new CHttpCookie($cn, $lang_id));

				$resp['status'] = 'true';
				echo json_encode($resp);
			}
		} else {
			echo json_encode($resp);
		}
	}

	public function actionReceiveEmail()
	{
		$checked=requestParam('checked');

		$used=0;
		if($checked)
		{
			$used=1;
		}
		$SQL="UPDATE `user` SET receive_email=$used WHERE user_id='".userID()."'";

		dbExecute($SQL);

		echo "1";
	}

	public function actionGoogleTwoFactor() :void
	{
		$checked	= requestParam('checked');
		$used		= 0;
		if ((int)$checked)
		{
			$used	= 1;
			$SQL	= "UPDATE `user` SET `google_two_fa` = {$used} WHERE `user_id` = '" . userID() . "' AND `status` = " . Status::PUBLISHED . " AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '" . App::getSetting("defaultEnd") . "')";
			dbExecute($SQL);
			$status	= [
				'status'	=> 1,
				'title'		=> Dict::getValue("successful_save"),
				'message'	=> Dict::getValue("two_fa_turned_on")
			];
		} else {
			$status = [
				'status'	=> 0,
				'message'	=> Dict::getValue("two_fa_cannot_be_turned_off")
			];
		}

		echo json_encode($status);
	}
}

?>
