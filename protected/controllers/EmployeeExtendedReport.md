# Dolgo<PERSON><PERSON> kiterjesztett kimutatás megjelenítése


### Működés
A kód teljes egészében ttwa alapokra (G2B) épül.
SQL-ből kéri le a dolgozó adatait.
A korábbi dolgozói kimutatás féle TEMP táblákra épül, ez<PERSON>rt azokat is legenerálja a futás elején.



### Funkciók


#### `G2BInit()`
Paraméter | Type | Leírás
---: | --- | ---
*@return* |  | 

Grid inicializálás.

-----

#### `setSQL($filter, $gridID, $forReport = false)`
Paraméter | Type | Leírás
---: | --- | ---
*$filter* | array | szűrőből post-on érkező adatok
*$gridID* | string | gridID
*$forReport* | array | reporthoz
*@return* | | 

Adatbá<PERSON>s le<PERSON>, temp táblák generálásának meghívása.

-----

#### `search()`
Paraméter | Type | Leírás
---: | --- | ---
*@return* |  | 

Szűrő generálása

-----

#### `columns()`
Paraméter | Type | Leírás
---: | --- | ---
*@return* | array | 

GRID-ben megjelenő oszlopok definicíója.

------

#### `attributeLabels()`
Paraméter | Type | Leírás
---: | --- | ---
*@return* | array | 

Oszlopnevek nyelvi elemei

-----

#### `getQuitEmployeesAllDataSQL($filter, $process_id, $dinamicTable = false)`
Paraméter | Type | Leírás
---: | --- | ---
*$filter* | array | szűrőből post-on érkező adatok
*$process_id* | type | 
*$dinamicTable* | bool | létezik-e a temp tábla
*@return* | string | SQL kód

Kilépett dolgozók adatainak lekérdezése.

-----

#### `getQuitEmployeesTable()`
Paraméter | Type | Leírás
---: | --- | ---
*@return* | string | temp tábla neve

Kilépett dolgozók alap TEMP tábla létrehozása.

-----

#### `getDinamicTabs($table)`
Paraméter | Type | Leírás
---: | --- | ---
*$table* | string | temp tábla neve
*@return* | string | tábla neve vagy null

Dolgozók kiterjesztett adatainak új TEMP táblába való létrehozása.

-----

#### `copyTable($newTable, $oldTable)`
Paraméter | Type | Leírás
---: | --- | ---
*$newTable* | string | új temp tábla neve
*$oldTable* | string | régi tábla neve
*@return* | string | tábla neve vagy null

Lemásolja a régi tábla tartalmát egy új TEMP táblába.

-----