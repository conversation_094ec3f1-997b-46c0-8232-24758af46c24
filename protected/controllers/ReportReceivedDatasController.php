<?php #yii2: done

'yii2-only`;

	namespace app\controllers;
	use app\components\Dict;
	use app\components\Grid2\Grid2Controller;
	use Yang;

`/yii2-only';


#yii2: done

class ReportReceivedDatasController extends Grid2Controller
{
	public function __construct()
	{
		parent::__construct("reportReceivedDatas");
	}
	
	protected function G2BInit()
    {
		parent::setControllerPageTitleId("page_title_report_received_datas");

		$this->LAGridRights->overrideInitRights("paging",				true);
		$this->LAGridRights->overrideInitRights("search",				true);
		$this->LAGridRights->overrideInitRights("search_header",		true);
		$this->LAGridRights->overrideInitRights("select",				true);
		$this->LAGridRights->overrideInitRights("multi_select",			false);
		$this->LAGridRights->overrideInitRights("column_move",			true);
		$this->LAGridRights->overrideInitRights("reload_sortings",		true);
		$this->LAGridRights->overrideInitRights("details",				false);
		$this->LAGridRights->overrideInitRights("init_open_search",		true);
		$this->LAGridRights->overrideInitRights("export_xlsx",			true);
		
		parent::setGridProperty("splitColumnEnabled", true,	 "dhtmlxGrid");
		parent::setGridProperty("splitColumn", 3,	 "dhtmlxGrid");

		$this->LAGridDB->enableSQLMode();
		
		$SQL="
			SELECT
				*
			FROM
				`received_sap_employee_data`
			WHERE 
				(`created_on` = '{created_on}' OR '{created_on}'='' OR '{created_on}'='ALL')
				AND (`employee_name_and_id` = '{employee_name_and_id}' OR '{employee_name_and_id}'='' OR '{employee_name_and_id}'='ALL')
				AND (`type` = '{type}' OR '{type}'='' OR '{type}'='ALL')
			ORDER BY
				created_on DESC";
		
		$this->LAGridDB->setSQLSelection($SQL, "row_id");
		
		// $this->LAGridDB->setSQLSelectionForReport($SQL);
		
		parent::G2BInit();
    }
	
	protected function search()
	{
		return array(
			'type'		=> array(
											'col_type'			=> 'combo',
											'options'			=>	array(
																		'mode'			=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																		'sql'			=> "SELECT `type` as id, `type` as value"
																								. " FROM `received_sap_employee_data`"
																								. " ORDER BY `type` DESC",
																		'array'			=> array(array("id"=>"ALL","value"=>Dict::getValue("all"))),
																	),
											'label_text'=>Dict::getValue("type"),
											'default_value'=>'',
										),
			'created_on'		=> array(
											'col_type'			=> 'combo',
											'options'			=>	array(
																		'mode'			=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																		'sql'			=> "SELECT `created_on` as id, `created_on` as value"
																								. " FROM `received_sap_employee_data`"
																								. " ORDER BY `created_on` DESC",
																		'array'			=> array(array("id"=>"ALL","value"=>Dict::getValue("all"))),
																	),
											'label_text'=>Dict::getValue("created_on"),
											'default_value'=>'',
										),
			'employee_name_and_id'		=> array(
											'col_type'			=> 'auto',
											'options'			=>	array(
																		'mode'			=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																		'sql'			=> "SELECT `employee_name_and_id` as id, `employee_name_and_id` as value"
																								. " FROM `received_sap_employee_data`"
																								. " WHERE  (employee_name_and_id LIKE '%%{search}%%')"
																								. " ORDER BY `employee_name_and_id`",
																		'array'			=> array(array("id"=>"ALL","value"=>Dict::getValue("all"))),
																	),
											'label_text'=>Dict::getValue("employee_id"),
											'default_value'=>'',
										),

			'submit'		=> array('col_type'=>'searchBarReloadGrid', 'width'=>'*', 'label_text'=>''),
		);
	}
	
	public function columns()
	{
		return array(
			'employee_name_and_id'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 300),
			'type'					=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 100),
			'missing_datas'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 300),
			'created_on'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 150, 'export_as' => 'date'),
			'BEGDA00'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 60),
			'ENDDA00'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 60),
			'KERNEV'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'VEZNEV'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'SZTSZ'					=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'NEME'					=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 50),
			'ADOSZAM'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'BELEPES'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'KILEPES'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'BEGDA01'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'ENDDA01'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'SZERVEGYS'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'SZERVNEV'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'VALLKOD'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 100),
			'VALLALAT'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 300),
			'SZEMCSOP'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 70),
			'SZEMCSOPNEV'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 100),
			'IRSZ01'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 50),
			'HELYSEG01'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 250),
			'KOZTER01'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 250),
			'KOZTERJELL01'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 100),
			'HAZSZAM01'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 50),
			'EPULET01'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 50),
			'LEPCSOHAZ01'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 50),
			'EMELET01'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 50),
			'AJTO01'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 50),
			'IRSZ02'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 50),
			'HELYSEG02'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 250),
			'KOZTER02'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 250),
			'KOZTERJELL02'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 100),
			'HAZSZAM02'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 50),
			'EPULET02'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 50),
			'LEPCSOHAZ02'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 50),
			'EMELET02'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 50),
			'AJTO02'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 50),
			'BEGDA02'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'ENDDA02'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'EMAIL'					=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 250),
			'TAJSZAM'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'ISKKOD'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 50),
			'ISKVEG'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'WRKSEN'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 50),
			'KOLCSCEG'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 100),
			'KOLCSNEV'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'PROBAIDOVEGE'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'SZERZKOD'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 50),
			'SZERZODES'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'NAPIMUNKAIDO'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 50),
			'ALKVISZKOD'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 50),
			'ALKVISZ'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'BEGDA03'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'ENDDA03'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'SZABADSAG'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'FKERET'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'BEGDA04'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'ENDDA04'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'UZLETAGKOD'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 50),
			'UZLETAG'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'MRENDSZABALY'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'MRENDSZABALYNEVE'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'BEGDA05'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'ENDDA05'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'KOLTSEGHELY'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'KOLTSEGHELYNEVE'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'BEGDA06'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'ENDDA06'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'MUNKAKOR'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'MUNKAKORNEVE'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'MUNKAKORNEVE_EN'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'BEGDA07'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'ENDDA07'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'N1MANKOD'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'N1MAN'					=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'N2MANKOD'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'N2MAN'					=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'FUNMANKOD'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'FUNMAN'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'HRMANKOD'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'HRMAN'					=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'KARTYASZAM'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'BEGDA08'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),
			'ENDDA08'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 120),				
		);
	}
	
	public function attributeLabels()
	{
		return array(
			'employee_name_and_ids'		=> Dict::getValue("employee_id"),
			'type'						=> Dict::getValue("type"),
			'missing_datas'				=> Dict::getValue("missing_datas"),
			'created_on'				=> Dict::getValue("created_on"),
		);
	}
}
?>