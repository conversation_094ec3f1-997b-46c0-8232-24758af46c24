<?php

'yii2-only`;

	namespace app\controllers;
	use <PERSON>;

`/yii2-only';


#yii2: done
ini_set('max_execution_time', 3000);
ini_set("display_errors", 1);

/**
 *  PHP INI SETTINGS
 *  extension=php_pdo_odbc.dll
 *  extension=php_odbc.dll
 */
class AccessSyncController extends Controller{

    private $acces_db_username, $access_db_password, $acces_db, $command, $start_date, $tmp_table, $registration_table, $log;
	private $previus_tmp_tables; // ennyi korábbi táblát hagyunk meg a többit töröljük

    public function __construct(){

        //parent::__construct();

        $this->access_db_password = 'A1BB807030C81';

        //$this->acces_db = 'C:\\xampp\htdocs\Login\ACTIVITY.MDB'; // adatbázis file
        $this->acces_db = 'C:\\Handnet\ACTIVITY.MDB';
        //$this->acces_db =  Yii :: app()->params['activity_mdb']; // adatbázis file helye

        $this->command = Yii::app()->db->createCommand(); // #see https://innote.login.hu/n-7wpd5adl

        $this->start_date = '2016-01-01 00:00:01'; // csak ezen időpont utáni adatokat akarunk kapni

        $this->tmp_table = 'access_import_tmp_'.date('Ymd_Hi');

        $this->registration_table = 'registration';  // végleges verzióban registration

		$this->log = '';

		$this->previus_tmp_tables = 5;

    }


    public function actionIndex() {

		$this->writeLog('Process Start');

        if(isset($_GET['temondhogyrablotamadas'])) {
            //var_dump('start: '.date('H:i:s'));
        }

        // access file check
        if(!file_exists($this->acces_db)){

			$this->writeLog('Access database file not found!');
            Yang::log('Access database file not found!', 'log', 'controllers.AccessSyncController');
            die();

        }

        try{

            $this->createTable();

            // truncate table (drop previous data)
            $this->truncateTable();


            // get last sync id
            $sync_id = $this->getLastSyncId();

            $last_sync_id = $sync_id == (NULL OR FALSE) ? 0 : $sync_id;

			$this->writeLog('clast sync ID:'.$last_sync_id);

			// --- START

			// odbc connect
			$conn  = odbc_connect("Driver={Microsoft Access Driver (*.mdb)};Dbq=$this->acces_db", $this->acces_db_username, $this->access_db_password);

			if(!$conn) {

				$this->writeLog('ODBC error');

				Yang::log('Access Database connection could not be established.', 'log', 'controllers.AccessSyncController');
				die();
			}


			// get rows
			$sql  = "SELECT *
					  FROM Activity
					  WHERE `Time` > {ts '$this->start_date'}
					  AND `ID` > ".(int)$last_sync_id."
					  AND `Data2` IS NOT NULL ";


			$result = odbc_exec($conn, $sql);

			$insert = [];

			$insert_num = 0;

			// if we have result
			if( odbc_num_rows ($result) ){

				while ($row = odbc_fetch_array($result)){

					$insert[] = [  'id'           => $row['ID'],
								   'acknowledged' => iconv('ISO-8859-1', 'UTF-8', $row['Acknowledged']),
								   'importance'   => $row['Importance'],
								   'time'         => $row['Time'],
								   'site'         => $row['Site'],
								   'address'      => $row['Address'],
								   'message'      => $row['Message'],
								   'data1'        => $row['Data1'],
								   'data2'        => $row['Data2'],
								   'userid'       => iconv('ISO-8859-1', 'UTF-8', $row['User ID']),
								   'info'         => iconv('ISO-8859-1', 'UTF-8', $row['Info'])
							   ];


					$insert_num++;

					if($insert_num >= 200){

						$this->multiInsert($this->tmp_table, $insert);

						$insert_num = 0;
						$insert = [];

					}

				}

				$this->multiInsert($this->tmp_table, $insert);

			}

			// --- END

            $this->copyTmpTableDataToRegistrationTable();


        }catch (Exception $e){

			$this->writeLog($e->getMessage());

            Yang::log($e->getMessage(), 'log', 'controllers.AccessSyncController');

        }

		// régi tmp táblák törlése
		if(!isset($_GET['temondhogyrablotamadas'])) {
			$this->deleteOldTmpTables();
		}

		$this->writeLog('Process End');

        if(isset($_GET['temondhogyrablotamadas'])) {
            echo $this->log;
        }

    }


    /**
     * TMP table data copy to registration table
     */
    public function copyTmpTableDataToRegistrationTable(){

		$this->writeLog('copyTmpTableDataToRegistrationTable()');

        try{

            //get data
            $rows = $this->getDataFromTmpTable();
            $this->writeLog('rows: ('. count($rows).')');

            $insert_num = 0;

            $insert =[];

            foreach($rows as $row){

                $insert[] = [   'terminal_id'   => '0',
                                'reader_id'     => '',
                                'card'          => $row['userid'],
                                'time'          => $row['time'],
                                'event_type_id' => $row['event_type_id'],
                                'cost_id'       => NULL,
                                'sync'          => $row['id'],
                                'cal_status'    => '',
                                'reg_status'    => '',
                                'random_select' => '',
                                'note'          => '',
                                'emp_note'      => '',
                                'status'        => 2,
                                'created_by'    => 'access_db_sync',
                                'created_on'    => $row['created_on'],
                                'pre_row_id'    => ''
                ];

                $insert_num++;

                if($insert_num >= 200){

                    $this->multiInsert($this->registration_table, $insert);

                    $insert_num = 0;
                    $insert = [];
                }

            }

            $this->multiInsert($this->registration_table, $insert);


            // finally we drop the tmp table
            //$this->dropTable();

        }catch (Exception $e){

			$this->writeLog($e->getMessage());

            Yang::log($e->getMessage(), 'log', 'controllers.AccessSyncController');

        }

    }


    /**
     * Get data from tmp table
     *
     * @return array
     */
    public function getDataFromTmpTable(){

		$this->writeLog('getDataFromTmpTable()');
        $tmpTable = $this->tmp_table;

        return dbFetchAll("

            SELECT  *,
                    CASE SUBSTRING(HEX(`data2`), 1, 2)
                        WHEN '10' THEN 'NMB'
                        WHEN '30' THEN 'NMK'
                        ELSE SUBSTRING(HEX(`data2`),1,2)
                    END as 'event_type_id',
                    NOW() AS created_on
            FROM    `$tmpTable`
            WHERE   1
                    AND `userid` != ''
                    AND `message` = 7
            ;

        ");

    }


    /**
     * Create tmp table
     */
    public function createTable(){

		$this->writeLog('createTable()');
        $tmpTable = $this->tmp_table;

        $drop   = "DROP TABLE IF EXISTS `$tmpTable`";
        $create = "

            CREATE TABLE IF NOT EXISTS `$tmpTable` (

                `id`            INT(11)                 NOT NULL AUTO_INCREMENT,
                `acknowledged`  TINYINT(1)              NULL DEFAULT NULL,
                `importance`    TINYINT(3) UNSIGNED     NULL DEFAULT NULL,
                `time`          DATETIME                NULL DEFAULT NULL,
                `site`          INT(11)                 NULL DEFAULT NULL,
                `address`       TINYINT(3) UNSIGNED     NULL DEFAULT NULL,
                `message`       TINYINT(3) UNSIGNED     NULL DEFAULT NULL,
                `data1`         LONGBLOB                NULL,
                `data2`         LONGBLOB                NULL,
                `userid`        VARCHAR(10)             NULL DEFAULT NULL,
                `info`          VARCHAR(255)            NULL DEFAULT NULL,

                PRIMARY KEY (`id`)

            )
            COLLATE='utf8_general_ci'
            ENGINE=InnoDB

        ";

        try {

            dbExecute($drop);
            dbExecute($create);

        } catch (Exception $e) {

			$this->writeLog($e->getMessage());
            Yang::log($e->getMessage(), 'log', 'controllers.AccessSyncController');

        }

    }


    /**
     * Truncate tmp table
     */
    public function truncateTable(){

		$this->writeLog('truncateTable');

        $sql = "TRUNCATE `".$this->tmp_table."` ";

        dbExecute($sql);

    }


    /**
     * Drop tmp table
     */
    public function dropTable(){

		$this->writeLog('dropTable');

        $sql = "DROP TABLE `".$this->tmp_table."` ";

        dbExecute($sql);

    }


    /**
     * Get Last sync ID
     *
     * @return int
     */
    public function getLastSyncId(){

		$this->writeLog('getLastSyncId');

        $this->command->reset();

        $last_sync_id = $this->command
                            ->select('sync')
                            ->from($this->registration_table)
                            ->where('sync IS NOT NULL')
                            ->order('sync desc')
                            ->limit('1')
                            ->queryScalar();

        $this->command->execute();

        $this->command->reset();

        return $last_sync_id;

    }


    /**
     * Insert data to table
     *
     * @param $data array
     */
    public function multiInsert($table='', $data=[]){

		$this->writeLog('multiInsert (size:'. count($data).')');
        dbMultiInsert($table,$data);

    }


	/**
     * Write log file
     *
     * @param string $msg
     */
    private function writeLog($msg){

		$this->log .= date('Y-m-d H:i:s').' | '. $msg .'<br>';

		/*
		if( (!is_null($this->log_file_folder)) AND (!is_null($this->log_file_name)) AND ( strlen($msg) > 0) ){

			$file = fopen($this->log_file_folder . $this->log_file_name, "a+");

			fwrite($file, date('Y-m-d H:i:s').' | '. $msg . PHP_EOL);

			fclose($file);

		}

		if( (isset($this->get)) AND (!is_null($this->get)) ){
			$this->log .= date('Y-m-d H:i:s').' | '. $msg .'<br>';
		}
		*/

    }


	/**
     * Korábbi tmp táblák törlése
     */
    public function deleteOldTmpTables(){

		$this->writeLog('deleteOldTmpTables()');

        preg_match("/dbname=([^;]*)/", dbConnectionString(), $matches);

        $currentdb = $matches[1];

		$sql = "

            SELECT      table_name AS name
			FROM        information_schema.tables
            WHERE       1
                        AND table_type = 'BASE TABLE'
            			AND table_schema = '$currentdb'
            			AND TABLE_NAME LIKE 'access_import_tmp_%'

			ORDER BY    create_time;

        ";

        $searchDropableTablesSelect = dbFetchAll($sql);

        $dropTableSql = "";

		if(count($searchDropableTablesSelect) > $this->previus_tmp_tables){

			$tables = count($searchDropableTablesSelect);

			foreach ($searchDropableTablesSelect as $value) {

				if($tables > $this->previus_tmp_tables){

					$dropTableSql .= "DROP TABLE IF EXISTS  `".$value['name']."`".'; ';

				}

				$tables--;

			}

        }

		if($dropTableSql != ''){
			dbExecute($dropTableSql);
		}

    }


}

