<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2\Grid2Controller;
	use app\models\Employee;
	use app\models\Status;
	use Yang;

`/yii2-only';


class ApproverlistController extends GridController
{
	public $layout = '//layouts/main';

	public function __construct()
	{
		if(isYii2()) $this->layout = '@app/views/layouts/main';

		parent::__construct("approverlist");
		parent::setMode("SQL", "all_pk"/*, MyDb2ActiveRecord::getDb2Connection()*/);

		parent::setTitle(Dict::getValue("page_title_aproverlist"));
		parent::exportSettings(Dict::getValue("export_file_aproverlist"));
		//parent::setRights(/*add*/false,/*mod*/false,/*imod*/false,/*del*/false,/*exp*/false,/*$sel*/false,/*$msel*/false,/*$details*/false);
		parent::setRights(false,false,false,false,false,false,false,false,false,true);

		$defaultEnd	= App::getSetting("defaultEnd");
		$today		= date('Y-m-d');
		parent::selectConditions("
			SELECT
				a.row_id									as approver_row_id,
				a.process_id,
				IFNULL(".Employee::getParam('fullname', 'eu').",u.username)	as approver_employeename,
				u.user_id									as approver_userid,
				u.employee_id								as approver_employeid,
				a.related_model,
				IF(	a.`related_value` = 'ALL', 
					'ALL', 
					CASE a.related_model
						WHEN 'Employee'			THEN ".Employee::getParam('fullname', 'e')."
						WHEN 'EmployeeContract'	THEN ".Employee::getParam('fullname', 'e_ec')."
						ELSE 'else'
					END
				)											as value
			FROM	`approver` a
			LEFT JOIN	`user` u ON  
					u.`user_id` = a.`approver_user_id`
				AND u.`status` = 2
				AND '$today' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '$defaultEnd')
			LEFT JOIN employee eu ON 
					eu.`employee_id` = u.`employee_id`
				AND eu.`status` = 2
				AND '$today' BETWEEN eu.`valid_from` AND IFNULL(eu.valid_to, '$defaultEnd')
			LEFT JOIN employee e ON 
					e.employee_id = IF(a.`related_model`='Employee', a.`related_value`, null)
				AND e.`status` = 2
				AND '$today' BETWEEN e.`valid_from` AND IFNULL(e.valid_to, '$defaultEnd')
			LEFT JOIN employee_contract ec ON
					ec.`employee_contract_id` = IF(a.`related_model`='EmployeeContract', a.`related_value`, null)
				AND ec.`status` = 2
				AND '$today' BETWEEN ec.`valid_from` AND IFNULL(ec.valid_to, '$defaultEnd')
				AND '$today' BETWEEN ec.`ec_valid_from` AND IFNULL(ec.ec_valid_to, '$defaultEnd')
			LEFT JOIN employee e_ec ON 
					e_ec.employee_id = ec.employee_id
				AND e_ec.`status` = 2
				AND '$today' BETWEEN e_ec.`valid_from` AND IFNULL(e_ec.valid_to, '$defaultEnd')
			WHERE 
					a.`status` = ".Status::PUBLISHED." 
				AND '$today' BETWEEN a.`valid_from` AND IFNULL(a.`valid_to`, '$defaultEnd')
			ORDER BY approver_employeename"
			);

	}

	public function columns()
	{
		$colums_array =  array(
			'approver_row_id'			=> array('width' => 100, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center'),
			'process_id'				=> array('width' => 200, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center'),
			'related_model'				=> array('width' => 200, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center'),
			'approver_employeename'		=> array('width' => 400, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center'),
			'value'						=> array('width' => 400, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center'),
		);

		$more_colums_array = array(
			'approver_userid'			=> array('width' => 250, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center'),
			'approver_employeid'		=> array('width' => 250, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center'),
		);

		if (App::getRight("approverlist","more_column")==1)
		{
			$colums_array = $colums_array + $more_colums_array;
		}

		return $colums_array;
	}

	public function attributeLabels()
	{
		return array(
			'approver_row_id'			=> Dict::getValue("emp_id"),
			'process_id'				=> Dict::getValue("employeename"),
			'related_model'				=> Dict::getValue("approver_username"),
			'approver_employeename'		=> Dict::getValue("approver_employeename"),
			'value'						=> Dict::getValue("approver_employeename"),
			'approver_userid'			=> Dict::getValue("approver_userid"),
			'approver_employeid'		=> Dict::getValue("approver_employeid"),
		);
	}

}
?>
