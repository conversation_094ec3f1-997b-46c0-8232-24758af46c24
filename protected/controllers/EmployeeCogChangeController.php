<?php

class EmployeeCogChangeController extends Grid2Controller
{

	public function __construct()
	{
		parent::__construct("employeeCogChange");
	}

	protected function G2BInit()
	{
		parent::enableMultiGridMode();
		parent::setControllerPageTitleId("page_title_employee_cog_change");

		$this->LAGridRights->overrideInitRights("paging",			true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search",			true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search_header",	true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("select",			true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("multi_select",		false,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("column_move",		true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("col_sorting",		true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload_sortings",	true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload",			true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("details",			false,	"dhtmlxGrid");

		$this->LAGridRights->overrideInitRights("export_xls",		false,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_xlsx",		true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_pdf_node",	false,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("init_open_search",	true,	"dhtmlxGrid");

		$this->LAGridDB->enableSQLMode();

		parent::setGridProperty("splitColumnEnabled",    true,	"dhtmlxGrid");
		parent::setGridProperty("splitColumn",            2,	"dhtmlxGrid");
		parent::G2BInit();
	}

	public function actionIndex($layout = '//Grid2/layouts/indexLayout', $view = '/Grid2/index', $params = array())
    {
        $assetsPath = Yang::addAsset(Yang::getAlias('application.assets.base'), false, -1, true);
        Yang::registerScriptFile($assetsPath.'/EmployeeCogChange/js/employeeCogChange.js');

        parent::actionIndex($layout,$view,$params);
    }

	public function actionSetInitProperties()
	{
		$this->layout = "//layouts/ajax";

		Yang::setSessionValue('employee_cog_change_filter', requestParam('searchInput'));
		Yang::setSessionValue('employee_cog_change_cog',null);
		Yang::setSessionValue('employee_cog_change_errorDialog',\FALSE);

		parent::actionSetInitProperties();
	}

	protected function setSQL($filter, $gridID, $forReport = false)
	{
		$gpf=new GetPreDefinedFilter($this->getControllerID(),\FALSE,array('company'=> "employee",'payroll'=> "employee",'employee_ext'=>'ee','employee_ext2'=>'ee2'));
		$SQLfilter=$gpf->getFilter();

		$SQLfilter=App::replaceSQLFilter($SQLfilter,$filter);

		$AllData=new GetActiveEmployeeAllData($filter["valid_date"],$SQLfilter,array("employeeManagement","cogChange"));
		$table=$AllData->getTableName();

		$type=App::getSetting("bulk_group_change_by_cog");

		$groups=BulkGroupChangeByCogConfig::getData();

		$SQL="
			SELECT
				all_data.`employee_contract_id`,
				".Employee::getParam('fullname',"all_data")." AS fullname,
				all_data.emp_id,
				ccog.company_org_group_name as current_cog,
				rcog.company_org_group_name as request_cog,
				bgcbcr.valid_from as request_valid_from,
			";
		for($i=0;$i<count($groups);++$i)
		{
			$SQL.="request_".$groups[$i]['to_column'].".`to_value` as ".$groups[$i]['to_column'].",
			";
		}
		$SQL.="ccog.company_org_group_name as new_cog
			FROM $table all_data
			LEFT JOIN `$type` ccog ON
					ccog.`company_org_group_id`=all_data.".$type."_id
				AND ccog.`status`=".Status::PUBLISHED."
				AND '{valid_date}' BETWEEN ccog.`valid_from` AND IFNULL(ccog.`valid_to`, '".App::getSetting("defaultEnd")."')
			LEFT JOIN `bulk_group_change_by_cog_request` bgcbcr ON
					bgcbcr.`employee_contract_id`=all_data.`employee_contract_id`
				AND bgcbcr.`status`=".Status::DRAFT."
			LEFT JOIN `$type` rcog ON
					rcog.`company_org_group_id`=bgcbcr.`cog_id`
				AND rcog.`status`=".Status::PUBLISHED."
				AND '{valid_date}' BETWEEN rcog.`valid_from` AND IFNULL(rcog.`valid_to`, '".App::getSetting("defaultEnd")."')
			";
		for($i=0;$i<count($groups);++$i)
		{
			$SQL.="LEFT JOIN `bulk_group_change_by_cog_request_item` request_".$groups[$i]['to_column']." ON
						request_".$groups[$i]['to_column'].".`request_id`=bgcbcr.`request_id`
					AND request_".$groups[$i]['to_column'].".`to_column`='".$groups[$i]['to_column']."'
					AND request_".$groups[$i]['to_column'].".`status`=".Status::DRAFT."
			";
		}
		$SQL.="ORDER BY fullname
		";

		$this->LAGridDB->setSQLSelection($SQL, "employee_contract_id",	"dhtmlxGrid");
		return $SQL;
	}

	public function search()
	{
		$search=$this->getPreDefinedSearchFromDb(array("employeeManagement","cogChange"));
		unset($search['submit']);
		$search['submit']=array('col_type'=>'searchBarReinitGrid', 'width'=>'*', 'label_text'=>'');

		return $search;
	}

	public function columns()
	{
		$type=App::getSetting("bulk_group_change_by_cog");
		$filter=Yang::session('employee_cog_change_filter');

		$columns=array();
        if (empty($filter)) { return $columns; }
		$columns['dhtmlxGrid']=array(
			'fullname'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 200),
			'emp_id'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 100),
			'current_cog'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 250),
			'request_cog'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 250),
			'request_valid_from'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 150),
		);

		$columns['dhtmlxGrid'] = $this->columnRights($columns['dhtmlxGrid']);

		$groups=BulkGroupChangeByCogConfig::getData();

		for($i=0;$i<count($groups);++$i)
		{

			if($groups[$i]['mode']==="SQL")
			{
				$columns['dhtmlxGrid'][$groups[$i]['to_column']]=array('export'=> true, 'report_width' => 20, 'col_type'=>'combo','width' => 250,
											'options'	=>	array(
																	'mode'			=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																	'sql'			=> "
																						SELECT DISTINCT `".$groups[$i]['from_column']."` as `id`, ".$groups[$i]['from_value']." as `value`
																						FROM `".$groups[$i]['from_table']."`
																						WHERE
																								`status`=".Status::PUBLISHED."
																							AND '".$filter['valid_date']."' BETWEEN `valid_from` AND IFNULL(`valid_to`, '".App::getSetting("defaultEnd")."')
																						ORDER BY `value`
																					",
																	'array'			=> array(array("id"=>"","value"=>"")))
											);
			}
			else
			{
				$columns['dhtmlxGrid'][$groups[$i]['to_column']]=array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 250);
			}
		}

		$columns['addDialog']=array(
			'new_cog'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'combo','width' => 250, 'label_text'=>Dict::getValue(App::getSetting("bulk_group_change_by_cog")),
										'options'	=>	array(
																'mode'			=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																'sql'			=> "
																					SELECT `company_org_group_id` as `id`, `company_org_group_name` as `value`
																					FROM `$type`
																					WHERE
																							`status`=".Status::PUBLISHED."
																						AND '".$filter['valid_date']."' BETWEEN `valid_from` AND IFNULL(`valid_to`, '".App::getSetting("defaultEnd")."')
																					ORDER BY `value`
																				",
																'array'			=> array(array("id"=>"","value"=>""))),
										),
			'valid_from'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 150, 'dPicker'=>true, 'default_value' =>$filter['valid_date'], 'label_text'=>Dict::getValue("valid_from")),
		);

		if(!empty(Yang::session('employee_cog_change_cog')))
		{
			$multiTypes=Yang::session('employee_cog_change_cog');

			$columns['addMultiDialog']=array();

			foreach($multiTypes as $id => $values)
			{
				$tableDatas = BulkGroupChangeByCogConfig::getDataByColumn($id);
				$options=array();

				if($tableDatas['mode']==="SQL")
				{
					$where=$values['ids']==="ALL"?'':"AND $id IN ('". implode("', '", $values['ids'])."')";
					$options=array(
								'mode'			=> Grid2Controller::G2BC_QUERY_MODE_SQL,
								'sql'			=> "
													SELECT DISTINCT $id as `id`, ".$tableDatas['from_value']." as `value`
													FROM `".$tableDatas['from_table']."`
													WHERE
															`status`=".Status::PUBLISHED."
														AND '".$filter['valid_date']."' BETWEEN `valid_from` AND IFNULL(`valid_to`, '".App::getSetting("defaultEnd")."')
													$where
													ORDER BY `value`
												",
								'array'			=> array(array("id"=>"","value"=>""))
								);
				}
				else
				{
					$array=array(array('id' => NULL, 'value' => ''));
					for($i=0;$i<count($values['ids']);++$i)
					{
						$array[]=array('id' => $values['ids'][$i], 'value' => $values['ids'][$i]);
					}
					$options=array(
								'mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,
								'array'=>$array
								);
				}

				$columns['addMultiDialog'][$id]=array('export'=> true, 'report_width' => 20, 'col_type'=>'combo','width' => 250,
											'label_text'=>Dict::getModuleValue('ttwa-base',$tableDatas['dict_id']),
											'options'	=>	$options,
											'default_value' =>$values['default']
										);
			}
		}

		return $columns;
	}

	public function attributeLabels()
	{
		$labels=array();

		$labels['dhtmlxGrid']=array(
			'fullname'				=> Dict::getValue("name"),
			'emp_id'				=> Dict::getValue("emp_id"),
			'current_cog'			=> Dict::getValue("current")." ".Dict::getValue(App::getSetting("bulk_group_change_by_cog")),
			'request_cog'			=> Dict::getValue("states_draft")." ".Dict::getValue(App::getSetting("bulk_group_change_by_cog")),
			'request_valid_from'	=> Dict::getValue("states_draft")." ".Dict::getValue("valid_from"),
			'new_cog'				=> Dict::getValue(App::getSetting("bulk_group_change_by_cog")),
			'valid_from'			=> Dict::getValue("valid_from"),
		);

		$groups=BulkGroupChangeByCogConfig::getData();

		for($i=0;$i<count($groups);++$i)
		{
			$labels['dhtmlxGrid'][$groups[$i]['to_column']]=Dict::getValue("states_draft")." ".Dict::getModuleValue('ttwa-base',$groups[$i]['dict_id']);
		}

		return $labels;
	}

	protected function getStatusButtons($gridID = null)
	{
		$buttons=array();

		if($gridID==="dhtmlxGrid")
		{
			$buttons["openAddDialog"] = array(
								"type" => "button",
								"id" => "openAddDialog",
								"class" => "openAddDialog",
								"name" => "openAddDialog",
								"img" => "/images/status_icons/st_add.png",
								"label" => Dict::getValue("request")."/".Dict::getValue("modify"),
								"onclick" => "openAddDialogCogChange();",
							);
			$buttons["openDeleteDialog"] = array(
								"type" => "button",
								"id" => "openDeleteDialog",
								"class" => "openDeleteDialog",
								"name" => "openDeleteDialog",
								"img" => "/images/status_icons/st_del.png",
								"label" => Dict::getValue("delete_request"),
								"onclick" => "deleteDialogCogChange();",
							);
			$buttons["openApproveDialog"] = array(
								"type" => "button",
								"id" => "openApproveDialog",
								"class" => "openApproveDialog",
								"name" => "openApproveDialog",
								"img" => "/images/status_icons/st_accept.png",
								"label" => Dict::getValue("operation_approval"),
								"onclick" => "approveDialogCogChange();",
							);
			$buttons[] = array(
								"type" => "selector",
							);

		}
		return Yang::arrayMerge($buttons,parent::getStatusButtons($gridID));
	}

	function actionAddDialogCogChange()
	{
		$id = requestParam('selected_id');
		$filter=Yang::session('employee_cog_change_filter');
		$result=array('message'=>Dict::getValue("please_select_line"),
					'status'=>0,
					'url' => baseURL()."/".$this->getControllerID());

		if($id!=="null")
		{
			if(BulkGroupChangeByCogRequest::haveBulkGroupChangeByCogRequest($id))
			{
				$result['message']=Dict::getValue("error_draft");
			}
			else
			{
				$name=Employee::getEmployeeFullnameByEcID($id,"'".$filter['valid_date']."'");

				$result['message']=Dict::getValue("company_org_group_id")." ";
				$result['message'].=ApproverRelatedGroup::isApproverOfEcID("cogChange",$id,$filter['valid_date'])?
						Dict::getValue("modify"):Dict::getValue("request");
				$result['message'].=" - ".$name;
				$result['status']=1;
			}
		}
		echo json_encode($result);
	}

	function actionAddCogChange()
	{
		$id = requestParam('selected_id');
		$input=requestParam('dialogInput_addDialog');
		$filter=Yang::session('employee_cog_change_filter');

		$result=array('status'=>1,'error'=>'');

		$SQL="
			SELECT
				e.`row_id`
			FROM `employee` e
			LEFT JOIN `employee_contract` ec ON
					ec.`employee_id`=e.`employee_id`
				AND ec.`status`=".Status::PUBLISHED."
				AND '".$input['valid_from']."' BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '".App::getSetting("defaultEnd")."')
				AND '".$input['valid_from']."' BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '".App::getSetting("defaultEnd")."')
			WHERE
					e.`status`=".Status::PUBLISHED."
				AND '".$input['valid_from']."' BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."')
		";
		$activeValid = dbFetchRow($SQL);

		if(!empty($activeValid))
		{
			$today=date("Y-m-d");
			if($today>$input['valid_from'] && !App::hasRight("employeeCogChange", "request_in_past"))
			{
				$result['status']=0;
				$result['error']=Dict::getValue("can_not_request_modify_retroactively");
			}
			else
			{
				if(empty($input['new_cog']))
				{
					$result['status']=0;
					$result['error']=Dict::getValue("error_field_required",array('attribute'=>Dict::getValue(App::getSetting("bulk_group_change_by_cog"))));
				}
				else
				{
					$missData=BulkGroupChangeByCog::getMissAttributeInDB($input['new_cog']);
					if(!empty($missData))
					{
						$result['status']=0;
						$result['error']=Dict::getValue("missing_attributes_in_the_schema",array('attribute'=>implode(", ", $missData)));
					}
				}
			}
		}
		else
		{
			$result['status']=0;
			$result['error']=Dict::getValue("no_valid_contract");
		}

		Yang::setSessionValue('employee_cog_change_errorDialog',($result['status']===0));

		echo json_encode($result);
	}

	function actionOpenMultiChange()
	{
		$id = requestParam('selected_id');
		$input=requestParam('dialogInput_addDialog');
		$result=array('status'=>0,
					'url' => baseURL()."/".$this->getControllerID(),
					'title'=>Dict::getValue("data_fix"),
					'cog'=>$input['new_cog'],
					'valid_from'=>$input['valid_from']);

		if(!Yang::session('employee_cog_change_errorDialog'))
		{
			$column=BulkGroupChangeByCog::HaveMoreDataByCog($input['new_cog']);

			if(count($column)>0)
			{
				$result['status']=1;
				Yang::setSessionValue('employee_cog_change_cog',$column);
			}
			else
			{
				$this->requestCogChange($id,$input['new_cog'],$input['valid_from']);
			}
		}

		echo json_encode($result);
	}

	function actionAddMultiChange()
	{
		$ec_id = requestParam('selected_id');
		$input=requestParam('dialogInput_addMultiDialog');
		$cog = requestParam('cog');
		$valid_from = requestParam('valid_from');

		$result=array('status'=>1);
		$error=array();

		foreach($input as $id => $value)
		{
			if(empty($value))
			{
				if(!BulkGroupChangeByCog::isContain($cog,$id,$value))
				{
					$error[]=$id;
				}
			}
		}

		if(count($error)>0)
		{
			$result['status']=0;
			$result['error']='';
			for($i=0;$i<count($error);++$i)
			{
				$tableDatas = BulkGroupChangeByCogConfig::getDataByColumn($error[$i]);
				$result['error'].=Dict::getValue("error_field_required",array('attribute'=>Dict::getValue($tableDatas['dict_id'])))."</br>";
			}
		}
		else
		{
			$this->requestCogChange($ec_id,$cog,$valid_from,$input);
		}

		echo json_encode($result);
	}

	function actionApproveDialogCogChange()
	{
		$id = requestParam('selected_id');
		$filter=Yang::session('employee_cog_change_filter');
		$result=array('message'=>Dict::getValue("please_select_line"),'status'=>0);

		if($id!=="null")
		{
			$request=BulkGroupChangeByCogRequest::haveBulkGroupChangeByCogRequest($id);
			if($request)
			{
				if(ApproverRelatedGroup::isApproverOfEcID("cogChange",$id,$filter['valid_date']))
				{
					$result['message']=Dict::getValue("approved_cog_change_request",
								array("employee"=>Employee::getEmployeeFullnameByEcID($id)));
					$result['status']=1;
				}
				else
				{
					$result['message']=Dict::getValue("not_approver");
				}
			}
			else
			{
				$result['message']=Dict::getValue("not_request");
			}
		}

		echo json_encode($result);
	}

	function actionAcceptCogChange()
	{
		$id = requestParam('selected_id');
		$bgcbcr=BulkGroupChangeByCogRequest::model()->findByAttributes(array('employee_contract_id'=>$id,'status'=>Status::DRAFT));
		if(!empty($bgcbcr))
		{
			$bgcbcr->acceptRequest();

			$user_id = Employee::getEmployeeUserIDByEcID($bgcbcr->employee_contract_id);

			$senderId=userID();
			$recipientIds=[$user_id];

			$type=App::getSetting("bulk_group_change_by_cog");

			$cogName = CompanyOrgGroup::getName($type,$bgcbcr->cog_id,"'".$bgcbcr->valid_from."'");

			$messaging = new Messaging();
			$messaging->findThreadByParams("cogChangeReq", "BulkGroupChangeByCogRequest", $bgcbcr->request_id, $senderId);

			$messaging->sendMessage($senderId, $recipientIds,
							["employee_name" => Employee::getEmployeeFullnameByEcID($bgcbcr->employee_contract_id),
							"cog" => $cogName, "valid_from" => $bgcbcr->valid_from], "acc_add", "cogChange", "");

			$messaging->closeThread();
		}

		echo json_encode(array('status'=>1));
	}

	function actionRejectCogChange()
	{
		$id = requestParam('selected_id');
		$bgcbcr=BulkGroupChangeByCogRequest::model()->findByAttributes(array('employee_contract_id'=>$id,'status'=>Status::DRAFT));
		if(!empty($bgcbcr))
		{
			$bgcbcr->rejectRequest();

			$user_id = Employee::getEmployeeUserIDByEcID($bgcbcr->employee_contract_id);

			$senderId=userID();
			$recipientIds=[$user_id];

			$type=App::getSetting("bulk_group_change_by_cog");

			$cogName = CompanyOrgGroup::getName($type,$bgcbcr->cog_id,"'".$bgcbcr->valid_from."'");

			$messaging = new Messaging();
			$messaging->findThreadByParams("cogChangeReq", "BulkGroupChangeByCogRequest", $bgcbcr->request_id, $senderId);

			$messaging->sendMessage($senderId, $recipientIds,
							["employee_name" => Employee::getEmployeeFullnameByEcID($bgcbcr->employee_contract_id),
							"cog" => $cogName, "valid_from" => $bgcbcr->valid_from], "rej_add", "cogChange", "");

			$messaging->closeThread();
		}

		echo json_encode(array('status'=>1));
	}

	function requestCogChange($ec_id,$cog,$valid_from,$selected=array())
	{
		$filter=Yang::session('employee_cog_change_filter');

		$right=ApproverRelatedGroup::isApproverOfEcID("cogChange",$ec_id,$filter['valid_date']);

		$bgcbcr=new BulkGroupChangeByCogRequest;
		$bgcbcr->request_id= md5(date("Y-m-d H:i:s").$ec_id.$valid_from);
		$bgcbcr->employee_contract_id=$ec_id;
		$bgcbcr->cog_id=$cog;
		$bgcbcr->valid_from=$valid_from;
		$bgcbcr->status=$right?Status::PUBLISHED:Status::DRAFT;

		$SQL="
			SELECT
				to_column,
				to_value
			FROM `bulk_group_change_by_cog`
			WHERE
					`status`=".Status::PUBLISHED."
				AND `cog_id`='$cog'
			GROUP BY to_column
		";

		$validate=$bgcbcr->validate();

		$types=dbFetchAll($SQL);
		$requestItems=array();

		for($i=0;$i<count($types);++$i)
		{
			$requestItems[$i]=new BulkGroupChangeByCogRequestItem;
			$requestItems[$i]->request_id=$bgcbcr->request_id;
			$requestItems[$i]->to_column=$types[$i]['to_column'];
			$requestItems[$i]->to_value=isset($selected[$types[$i]['to_column']])?
					$selected[$types[$i]['to_column']]:$types[$i]['to_value'];
			$requestItems[$i]->status=$right?Status::PUBLISHED:Status::DRAFT;

			$validate=$validate && $requestItems[$i]->validate();
		}

		if($validate)
		{
			$bgcbcr->save();
			for($i=0;$i<count($requestItems);++$i)
			{
				$requestItems[$i]->save();
			}

			if($right)
			{
				$bgcbcr->updateEmployee();
			}

			$sender=userID();
			$user_id = Employee::getEmployeeUserIDByEcID($ec_id);

			$event=$right?"add":"req_add";
			$senderId=$sender;
			$fromProcces_id=$ec_id;
			$recipientIds=$right?[$user_id]:[];

			$type=App::getSetting("bulk_group_change_by_cog");

			$cogName = CompanyOrgGroup::getName($type,$cog,"'".$valid_from."'");

			$messaging = new Messaging("cogChangeReq", "BulkGroupChangeByCogRequest", $bgcbcr->request_id);
			$messaging->sendMessage($senderId, $recipientIds,
							["employee_name" => Employee::getEmployeeFullnameByEcID($ec_id),
							"cog" => $cogName, "valid_from" => $valid_from], $event, "cogChange", "",false,$fromProcces_id);
		}
	}

	function actionDeleteDialogCogChange()
	{
		$id = requestParam('selected_id');
		$filter=Yang::session('employee_cog_change_filter');
		$result=array('message'=>Dict::getValue("please_select_line"),'status'=>0);

		if($id!=="null")
		{
			$request=BulkGroupChangeByCogRequest::haveBulkGroupChangeByCogRequest($id);
			if($request)
			{
				$result['message']= Dict::getValue("confirm_delete_request");
				$result['status']=1;
			}
			else
			{
				$result['message']=Dict::getValue("not_request");
				$result['status']=0;
			}
		}

		echo json_encode($result);
	}

	function actionDeleteCogChange()
	{
		$id = requestParam('selected_id');
		$bgcbcr=BulkGroupChangeByCogRequest::model()->findByAttributes(array('employee_contract_id'=>$id,'status'=>Status::DRAFT));
		if(!empty($bgcbcr))
		{
			$bgcbcr->rejectRequest();
		}

		echo json_encode(array('status'=>1));
	}

}
?>