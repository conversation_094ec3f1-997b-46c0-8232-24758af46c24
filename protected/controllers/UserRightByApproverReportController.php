<?php

class UserRightByApproverReportController extends Grid2Controller
{
    private $statusPublished;
	private $defaultEnd;
	private $isRoot;
	private $approverRuleGroupLinkType;
	private $rightsOptions;
	public function __construct($controllerID = "userRightByApproverReport")
	{
		parent::__construct($controllerID);

        $this->statusPublished = Status::PUBLISHED;
		$this->defaultEnd = App::getSetting("defaultEnd");
		$this->isRoot = App::isRootSessionEnabled();
		(string)$this->approverRuleGroupLinkType = App::getSetting("approver_rule_group_link_type");
		$this->rightsOptions = App::getLookup('approver_process_ids', false, null, [], true);
	}

	protected function G2BInit()
	{        
		parent::setControllerPageTitleId("page_title_user_rights_by_approver_report");
		parent::setExportFileName(Dict::getValue("page_title_user_rights_by_approver_report"));

		$this->LAGridRights->overrideInitRights("paging", true);
        $this->LAGridRights->overrideInitRights("search", true);
        $this->LAGridRights->overrideInitRights("search_header", true);
        $this->LAGridRights->overrideInitRights("select", true);
        $this->LAGridRights->overrideInitRights("multi_select", false);
        $this->LAGridRights->overrideInitRights("column_move", true);
        $this->LAGridRights->overrideInitRights("reload_sortings", true);
        $this->LAGridRights->overrideInitRights("details", false);
        $this->LAGridRights->overrideInitRights("init_open_search", true);
        $this->LAGridRights->overrideInitRights("export_xlsx", true);

		$this->LAGridDB->enableArrMode();
		$this->LAGridDB->setPrimaryKey('row_id');

		parent::G2BInit();
	}

    public function search()
	{
		$rootDisableSQL = "AND 1";
		if (!$this->isRoot) {
			$rootDisableSQL = "AND user.`user_id` <> '6acd9683761b153750db382c1c3694f6'";
		}

		$rightsOptions = App::getLookup('approver_process_ids', false, null, [], true);
		$searchFields['date'] =
			[
				'col_type'		=> 'ed',
				'dPicker'		=> true,
				'width'			=> '*',
				'label_text'	=> Dict::getValue("date"),
				'default_value'	=> date('Y-m-d'),
			];

		$searchFields['process_id'] =
			[
				'col_type'		=> 'combo',
				'options'      	=> [
					'mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY,
					'array'=> Yang::arrayMerge([["id" => "ALL", "value" => Dict::getValue("all")]], $rightsOptions)],
				'label_text'	=> Dict::getValue("process_id"),
				'default_value'	=> 'ALL',
			];

		$searchFields['employee_contract'] =
			[
				'col_type'		=> 'auto',
				'options'		=>	[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							`employee_contract`.`employee_contract_id` AS id,
							" . Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]) . " AS value
						FROM `approver`
						LEFT JOIN user ON
							`user`.`user_id` = `approver`.`approver_user_id`
						AND '{date}' BETWEEN `user`.`valid_from` AND IFNULL(`user`.`valid_to`, '{$this->defaultEnd}')
						AND `user`.`status` = {$this->statusPublished}
						LEFT JOIN `employee` ON
							`employee`.`status` = {$this->statusPublished}
						AND `employee`.`employee_id` = `user`.`employee_id`
						AND '{date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->defaultEnd}')
						LEFT JOIN `employee_contract` ON
								`employee_contract`.`employee_id` = `employee`.`employee_id`
							AND `employee_contract`.`status` = {$this->statusPublished}
							AND '{date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->defaultEnd}')
							AND '{date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->defaultEnd}')
							AND `employee_contract`.`row_id` IS NOT NULL
						WHERE
							`approver`.`status` = {$this->statusPublished}
						AND '{date}' BETWEEN `approver`.`valid_from` AND IFNULL(`approver`.`valid_to`, '{$this->defaultEnd}')
						AND " . Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]) . " LIKE '%%{search}%%'
						ORDER BY value
					",
					'array'	=> [["id"=>"", "value"=>""]]
				],
				'label_text'	=> Dict::getValue("approver_employeename"),
				'default_value'	=> '',
			];

			$searchFields['username'] =
			[
				'col_type'		=> 'combo',
				'options'		=>	[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							user.`user_id` AS id,
							user.`username` AS value
						FROM approver
						LEFT JOIN user ON
							user.`user_id` = approver.`approver_user_id`
						AND user.`status` = ".Status::PUBLISHED."
						AND '{date}' BETWEEN user.`valid_from` AND IFNULL(user.`valid_to`, '{$this->defaultEnd}')
						WHERE
							user.`status` = ".Status::PUBLISHED."
						AND '{date}' BETWEEN user.`valid_from` AND IFNULL(user.`valid_to`, '{$this->defaultEnd}')
						{$rootDisableSQL}
						ORDER BY `user`.username
					",
					'array'	=> [["id"=>"", "value"=>""]]
				],
				'label_text'	=> Dict::getValue("approver_username"),
			];

        $submit = ['submit' => ['col_type'=>'searchBarReinitGrid', 'width'=>'*', 'label_text'=>'']];

		$result = Yang::arrayMerge($searchFields, $this->getPreDefinedSearchFromDb("userRightByApproverReportController"), $submit);
		
        return $result;
	}
	
	protected function dataArray($gridID, $filter)
	{
		$results = [];
		
		//TODO: Ideiglenes megoldás - ne lehessen mindenkit lekérni!
		if((empty($filter['employee_contract']) && empty($filter['username'])) || ($filter['employee_contract'] === 'ALL' && $filter['username'] === 'ALL')) {
			$results[] = [
				'approver_fullname' => Dict::getValue('filter_error_usernameWithEmployee')
			];

			return $results;
		}
		
		$rootDisableSQL = "AND 1";
		if (!$this->isRoot) {
			$rootDisableSQL = "AND user.`user_id` <> '6acd9683761b153750db382c1c3694f6'";
		}

        $searchBar = requestParam('searchInput');
        $date = $searchBar['date'];
		$processID = $searchBar['process_id'];
		$ecID = $searchBar['employee_contract'];
		$username = $searchBar['username'];

		$listProcessID = !isset($processID) || $processID === 'ALL' ? array_column($this->rightsOptions, 'id') : [$processID];

		$getApprovers = [];
		$getApproversSQL = "
			SELECT
				DISTINCT user.`username` AS approver_username,		
				".Employee::getParam('fullname', 'employee')." AS approver_fullname,
				approver.`approver_user_id`
			FROM approver
			INNER JOIN user ON
				user.`user_id` = approver.`approver_user_id`
			AND	'".$date."' BETWEEN user.`valid_from` AND IFNULL(user.`valid_to`, '".$this->defaultEnd."')
			AND	user.`status` = ".$this->statusPublished."
			LEFT JOIN employee ON
				employee.`employee_id` = user.`employee_id`
			AND '".$date."' BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`, '".$this->defaultEnd."')
			AND employee.`status` = ".$this->statusPublished."
			LEFT JOIN employee_contract ON
				employee_contract.`employee_id` = employee.`employee_id`
			AND '".$date."' BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`, '".$this->defaultEnd."')
			AND '".$date."' BETWEEN employee_contract.`ec_valid_from` AND IFNULL(employee_contract.`ec_valid_to`, '".$this->defaultEnd."')
			AND employee_contract.`status` = ".Status::PUBLISHED."
			WHERE
				'".$date."' BETWEEN approver.`valid_from` AND IFNULL(approver.`valid_to`, '".$this->defaultEnd."')
			AND	approver.`status` = ".$this->statusPublished."
			AND (approver.`process_id` LIKE '{$processID}' OR '{$processID}' LIKE 'ALL' OR '{$processID}' = '')
			AND (employee_contract.`employee_contract_id` LIKE '{$ecID}' OR '{$ecID}' LIKE 'ALL' OR '{$ecID}' = '')
			AND (user.`user_id` LIKE '{$username}' OR '{$username}' LIKE 'ALL' OR '{$username}' = '')
			{$rootDisableSQL}
			ORDER BY approver.`process_id`, approver_fullname, approver_username;
		";
		$getApprovers = dbFetchAll($getApproversSQL);

		$filters =	array(	"interval"	=> array(	"valid_from"	=> $searchBar["date"],
													"valid_to"		=> $searchBar["date"],
													),
							);
		
		foreach ($getApprovers as $approvers) {
			foreach ($listProcessID as $process) {
				$gae = new GetActiveEmployees($filters, $process, $this->getControllerID(), $approvers['approver_user_id']);
				$activeEmployees = $gae->getEmployees();
				foreach ($activeEmployees as $employees) {
					$results[] = [
						'approver_fullname' => $approvers['approver_fullname'],
						'approver_username' => $approvers['approver_username'],
						'process_id' => $process,
						'employee_name' => $employees['fullname'],
						'emp_id' => $employees['emp_id'],
						'company_name' => $employees['company_name'],
						'payroll_name' => $employees['payroll_name'],
						'workgroup_name' => $employees['workgroup_name'],
						'unit_name' => $employees['unit_name'],
						'company_org_group1_name' => $employees['company_org_group1_name'],
						'company_org_group2_name' => $employees['company_org_group2_name'],
						'company_org_group3_name' => $employees['company_org_group3_name'],
					];
				}
			}
		}

        return $results;
	}

	public function columns()
	{
		$columns = [
			'approver_fullname' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
			'approver_username' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
			'process_id' => [
				'export'		=> true,
				'col_type'		=> 'combo',
				'width' 		=> 250,
				'options'		=>	[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
					'array'	=> $this->rightsOptions
				],
			],
			'employee_name' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
			'emp_id' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
			'company_name' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
			'payroll_name' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
			'workgroup_name' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
			'unit_name' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
			'company_org_group1_name' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
			'company_org_group2_name' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
			'company_org_group3_name' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
		];

		$columns = $this->columnRights($columns);

		return $columns;
	}

	public function attributeLabels()
	{
        $attributeLabels =
		[
            'approver_fullname' => Dict::getValue("approver_employeename"),
			'approver_username' => Dict::getValue("approver_username"),
			'process_id' => Dict::getValue("process_id"),
			'employee_name' => Dict::getValue("employee"),
			'emp_id' => Dict::getValue("emp_id"),
			'company_name' => Dict::getValue("company_name"),
			'payroll_name' => Dict::getValue("payroll_name"),
			'workgroup_name' => Dict::getValue("workgroup_name"),
			'unit_name' => Dict::getValue("unit_name"),
			'company_org_group1_name' => Dict::getValue("company_org_group1"),
			'company_org_group2_name' => Dict::getValue("company_org_group2"),
			'company_org_group3_name' => Dict::getValue("company_org_group3"),
		];

		return $attributeLabels;
	}
}

?>