<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\API\GetPreDefinedFilter;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2\Grid2Controller;
	use app\models\Employee;
	use app\models\EmployeeGroup;
	use app\models\Status;
	use app\models\WorkActivity;
	use Yang;

`/yii2-only';


#yii2: done

class UserInWorkActivityReportController extends Grid2Controller
{
	private $tableName = '';
	private $modelId = '';
	private $modelValue = '';
	
	public function __construct() {
		parent::__construct("userInWorkActivityReport");

		parent::enableLAGrid();
	}

	protected function G2BInit() {
		parent::setControllerPageTitleId("page_title_user_in_work_activity_report");

		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		true);

		$this->LAGridRights->overrideInitRights("init_open_search",	true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true,	"dhtmlxGrid");

		$this->LAGridDB->enableSQLMode();
		//$this->LAGridDB->setSQLSelection($SQL, "pk", "dhtmlxGrid");
		
		$groupHierarchy = explode(';', App::getSetting('group_hierarchy'));
		
		$this->tableName = $groupHierarchy[array_search('work_activity', $groupHierarchy)-1];
		$this->modelId = strpos($this->tableName, 'company_org_group') === 0 ? 'company_org_group_id' : $this->tableName.'_id';
		$this->modelValue = strpos($this->tableName, 'company_org_group') === 0 ? 'company_org_group_name' : $this->tableName.'_name';

		parent::G2BInit();
	}

	protected function setSQL($filter, $gridID, $forReport = false) {
		$gpf = new GetPreDefinedFilter($this->getControllerID(),\FALSE,array('company' => "employee", 'payroll' => "employee",));
		$SQLfilter = $gpf->getFilter();
		$SQLfilter = App::replaceSQLFilter($SQLfilter, $filter);
		$validDate = $filter['valid_date'];
		
		$SQL = "
			SELECT
				".Employee::getParam('fullname', 'employee')." AS `fullname`,
				`employee`.`emp_id`,
				`connectedTable`.`".$this->modelValue."` AS`connectedTable_name`,
				`work_activity`.`work_activity_name`,
				`work_activity_schedule`.`from_time`,
				`work_activity_schedule`.`to_time`
			FROM `work_activity_schedule`
			LEFT JOIN `employee_contract` ON
				`work_activity_schedule`.`employee_contract_id` = `employee_contract`.`employee_contract_id`
				AND employee_contract.`status` = ".Status::STATUS_PUBLISHED."
				AND '{valid_date}' BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`, '".App::getSetting("defaultEnd")."')
				AND '{valid_date}' BETWEEN employee_contract.`ec_valid_from` AND IFNULL(employee_contract.`ec_valid_to`, '".App::getSetting("defaultEnd")."')
			LEFT JOIN `employee` ON
				`employee`.`employee_id` = `employee_contract`.`employee_id`
				AND `employee`.`status` = ".Status::PUBLISHED."
				AND '{valid_date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '".App::getSetting("defaultEnd")."')
			LEFT JOIN `work_activity` ON
				`work_activity`.`work_activity_id` = `work_activity_schedule`.`work_activity_id`
				AND `work_activity`.`status` = ".Status::PUBLISHED."
			";
		// szűrés miatt kell
		$SQL .= EmployeeGroup::getAllActiveLeftJoinSQL("employee_contract", "$validDate");

		$SQL .= "
			LEFT JOIN `workgroup` ON
				workgroup.`workgroup_id` = ".EmployeeGroup::getActiveGroupSQL("workgroup_id","employee_contract")."
				AND `workgroup`.`status` = ".Status::PUBLISHED."
				AND '{valid_date}' BETWEEN `workgroup`.`valid_from` AND IFNULL(`workgroup`.`valid_to`, '".App::getSetting("defaultEnd")."')
			LEFT JOIN `unit` ON
				unit.`unit_id` = ".EmployeeGroup::getActiveGroupSQL("unit_id","employee")."
				AND `unit`.`status` = ".Status::PUBLISHED."
				AND '{valid_date}' BETWEEN `unit`.`valid_from` AND IFNULL(`unit`.`valid_to`, '".App::getSetting("defaultEnd")."')
			LEFT JOIN `".$this->tableName."` connectedTable ON
				`connectedTable`.".$this->modelId."=".EmployeeGroup::getActiveGroupSQL($this->tableName.'_id',"employee")."
				AND `connectedTable`.`status` = ".Status::PUBLISHED."
				AND '{valid_date}' BETWEEN `connectedTable`.`valid_from` AND IFNULL(`connectedTable`.`valid_to`, '".App::getSetting("defaultEnd")."')
			WHERE
				`work_activity_schedule`.`status` = ".Status::PUBLISHED."
				AND '{valid_date}' BETWEEN DATE_FORMAT(`work_activity_schedule`.`from_time`, '%Y-%m-%d') AND DATE_FORMAT(`work_activity_schedule`.`to_time`, '%Y-%m-%d')
				AND ('{work_activity}' = '' OR '{work_activity}' = `work_activity_schedule`.`work_activity_id`)
				AND ('{connectedTable}' = '' OR '{connectedTable}' = `work_activity`.`work_activity_category_id`)
				AND $SQLfilter
			ORDER BY
				`work_activity_schedule`.`from_time`,
				`work_activity_schedule`.`to_time`,
				".Employee::getParam('fullname', 'employee').",
				`work_activity`.`work_activity_name`
		";
		
		return $SQL;
	}

	public function columns() {
		$columns = [
			'fullname'					=> ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=>true, 'col_type'=>'ed',],
			'emp_id'					=> ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=>true, 'col_type'=>'ed',],
			'connectedTable_name'		=> ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=>true, 'col_type'=>'ed',],
			'work_activity_name'		=> ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=>true, 'col_type'=>'ed',],
			'from_time'					=> ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=>true, 'col_type'=>'ed',],
			'to_time'					=> ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=>true, 'col_type'=>'ed',],
		];

		return $columns;
	}

	public function attributeLabels() {
		$ret = [
			'fullname'					=> Dict::getValue("fullname"),
			'emp_id'					=> Dict::getValue("emp_id"),
			'connectedTable_name'		=> Dict::getValue("work_activity_category"),
			'work_activity_name'		=> Dict::getValue("work_activity_name"),
			'from_time'					=> Dict::getValue("from"),
			'to_time'					=> Dict::getValue("to"),
		];

		return $ret;
	}

	protected function search() {
		$search = $this->getPreDefinedSearchFromDb();
		$submit = $search['submit'];
		unset($search['submit']);

		$sql = 
			"SELECT 
				".$this->modelId." as id,
				".$this->modelValue." as value
			FROM
				".$this->tableName."
			WHERE
				`status` = ".Status::PUBLISHED." AND `$this->modelValue` LIKE '%%{search}%%'
			ORDER BY
				`".$this->modelValue."` ASC";

		$search['connectedTable'] = [
			'label_text'	=> Dict::getValue("work_activity_category"),
			'col_type'		=> 'auto',
			'options'		=> array(
									'mode'		=> Grid2Controller::G2BC_QUERY_MODE_SQL,
									'sql'		=> $sql,
								),
		];


		$mdl = new WorkActivity;
		$crt = new CDbCriteria();
		$crt->condition = "`status` = ".Status::PUBLISHED." AND `work_activity_name` LIKE '%%{search}%%'";
		$crt->order = "`work_activity_name` ASC";
		$id = "work_activity_id";
		$value = "work_activity_name";

		$search['work_activity'] = [
			'label_text'	=> Dict::getValue("work_activity_name"),
			'col_type'		=> 'auto',
			'options'		=> array(
									'mode'					=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
									'modelSelectionModel'	=> $mdl,
									'modelSelectionCriteria'=> $crt,
									'comboId'				=> $id,
									'comboValue'			=> $value,
								),
		];

		$search['submit'] = $submit;

		return $search;
	}
}