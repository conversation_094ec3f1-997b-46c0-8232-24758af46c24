<?php

class ApproverByCog1Controller extends Grid2Controller
{
	private $statusPublished;

	public function __construct()
	{
		parent::__construct("approverByCog1");
		$this->statusPublished = Status::PUBLISHED;
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("ApproverByCog1");

		parent::setControllerPageTitleId("page_title_approver_by_cog1");

		$this->LAGridRights->overrideInitRights("paging", true);
		$this->LAGridRights->overrideInitRights("search", true);
		$this->LAGridRights->overrideInitRights("search_header", true);
		$this->LAGridRights->overrideInitRights("select", true);
		$this->LAGridRights->overrideInitRights("column_move", true);
		$this->LAGridRights->overrideInitRights("reload_sortings", true);
		$this->LAGridRights->overrideInitRights("details", false);
		$this->LAGridRights->overrideInitRights("column_move", true);
		$this->LAGridRights->overrideInitRights("col_sorting", true);
		$this->LAGridRights->overrideInitRights("add", true);
		$this->LAGridRights->overrideInitRights("modify", true);
		$this->LAGridRights->overrideInitRights("delete", true);
		$this->LAGridDB->enableSQLMode();

		$SQL = "
			SELECT *
			FROM approver_by_cog1
			WHERE `status` = " . $this->statusPublished . "
		";
		$this->LAGridDB->setSQLSelection($SQL, "row_id");

		parent::G2BInit();
	}

	public function search()
	{
		return [];
	}

	/**
	 * @return array customized grid columns (name=>params)
	 */
	public function columns()
	{
		$cog1SQL = "
				SELECT
					company_org_group_id AS id,
					company_org_group_name AS value
				FROM
					company_org_group1
				WHERE
						status = " . $this->statusPublished . "
					AND CURDATE() BETWEEN valid_from AND valid_to
				ORDER BY value";

		$approverMaxLevel = App::getSetting('approver_max_level');
		$levelArray = [];
		for ($i = 1; $i <= $approverMaxLevel; $i++) {
			$levelArray[] = ['id' => $i, 'value' => $i];
		}

		$unitArray = [
			['id' => 'own', 'value' => Dict::getValue('own')],
			['id' => 'all', 'value' => Dict::getValue('all')],
			['id' => 'student', 'value' => Dict::getValue('student')]
		];

		$units = dbFetchAll("SELECT
									unit_id,
									unit_name
								FROM unit 
								WHERE
										status = " . $this->statusPublished . "
									AND CURDATE() BETWEEN valid_from AND valid_to
								ORDER BY unit_name", 
								"unit_id", "unit_name");
		foreach ($units as $id => $name) {
			$unitArray[] = ['id' => $id, 'value' => $name];
		}

		$companyArray = [
			['id' => 'own', 'value' => Dict::getValue('own')],
			['id' => 'all', 'value' => Dict::getValue('all')],
			['id' => 'only_store', 'value' => Dict::getValue('only_store')]
		];

		$companies = dbFetchAll("SELECT
									company_id,
									company_name
								FROM company 
								WHERE
										status = " . $this->statusPublished . "
									AND CURDATE() BETWEEN valid_from AND valid_to
								ORDER BY company_name", 
								"company_id", "company_name");
		foreach ($companies as $id => $name) {
			$companyArray[] = ['id' => $id, 'value' => $name];
		}

		$payrollSql = "SELECT
							payroll_id AS id,
							payroll_name AS value
						FROM payroll
						WHERE 
								status = " . $this->statusPublished . "
							AND CURDATE() BETWEEN valid_from AND valid_to
						ORDER BY payroll_name";

		return [
			'approver_company_org_group1_id' => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'combo',
															'options'	=>	[
																'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																'sql'	=> $cog1SQL,
																'array'	=> []
															],
												],
			'level' => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'combo',
									'options'	=>	[
										'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
										'array'	=> $levelArray
									],
						],
			'process_id' => [
							'export'		=> true,
							'report_width'	=> 20,
							'col_type'		=> 'combo',
							'width' 		=> "*",
							'options'		=>	[
								'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
								'array'	=> App::getLookup('approver_process_ids', false, null, [], true)
							]
						],
			'company_org_group1_id' => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'combo',
													'options'	=>	[
														'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
														'sql'	=> $cog1SQL,
														'array'	=> [['id' => 'ALL', 'value' => Dict::getValue("company_org_group1")		. " " . Dict::getValue("all")]]
													],
										],
			'company' => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'combo',
									'options'	=>	[
										'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
										'array'	=> $companyArray
									],
						],
			'unit_id' => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'combo',
									'options'	=>	[
										'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
										'array'	=> $unitArray
									],
						],
			'payroll_id' => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'combo',
									'options'	=>	[
										'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
										'sql'	=> $payrollSql,
										'array'	=> [['id' => 'ALL', 'value' => Dict::getValue("payroll_id") . " " . Dict::getValue("all")]]
									],
						],
		];
	}
}
