<?php

'yii2-only`;

	namespace app\controllers;
	use app\models\Status;
	use Yang;

`/yii2-only';


#yii2: done

ini_set('max_execution_time', 3600);
ini_set('memory_limit', '2048M');
ini_set("display_errors", 1);

define('DS', DIRECTORY_SEPARATOR);

class ExcelImportController extends Controller {
	private $sync_config;
	private $sync_config_fields;
	private $sync_config_import;
	private $sync_config_import2;
	private $echoImport;
	private $datenow;
	private $tempTableDate;
	private $today;
//	private $choose_insert_mode;
//	private $moveTheInsertedFiles;
	private $updateDb;

	public function __construct() {
		$this->datenow = date("Y-m-d H:i:s");
		$this->tempTableDate = date("Ymd_His");
		$this->today = date("YmdHms");

		require_once(Yang::getBasePath().''.DS.'extensions'.DS.'spreadsheet-reader'.DS.'spreadsheet-reader-master'.DS.'php-excel-reader'.DS.'excel_reader2.php');
		require_once(Yang::getBasePath().''.DS.'extensions'.DS.'spreadsheet-reader'.DS.'spreadsheet-reader-master'.DS.'SpreadsheetReader.php');
		require_once(Yang::getBasePath().''.DS.'extensions'.DS.'spreadsheet-reader'.DS.'ExcludeRowsFilter.php');
		require_once(Yang::getBasePath().''.DS.'extensions'.DS.'spreadsheet-reader'.DS.'spreadsheet-reader-master'.DS.'SpreadsheetReader_CSV.php');
		require_once(Yang::getBasePath().''.DS.'extensions'.DS.'spreadsheet-reader'.DS.'spreadsheet-reader-master'.DS.'SpreadsheetReader_XLSX.php');

	}

		public function actionIndex() {

		echo date("Y-m-d H:i:s").' - The import begin to run';
		echo '<br/>';
		echo "Loading sync config tables...";

		// Visszaadja azokat a temp tábla neveket, amiket a végén a checkAndClearUnusedTempTables() funkcióval használjuk
		$sql = "SELECT temp_table_name FROM `xls_sync_config`
							   WHERE `status`='".Status::PUBLISHED."' group by `temp_table_name`";
		$this->sync_config = dbFetchAll($sql);

		// A temp tábla készítéshez szükséges adatok a sync_config_fields táblából
		$sql = "SELECT *
				FROM `xls_sync_config_fields`
				WHERE `status`='".Status::PUBLISHED."'
				AND `import_or_insert`='S'
				ORDER BY `sort2`";
		try{
			$this->sync_config_fields = dbFetchAll($sql);
		}catch (Exception $e){
			$this->writeLog($e->getMessage());
		}

		// Az importhoz szükséges adatok, amikor nem adnak konkrét id-t az adott táblához, ezért mi generáljuk azokat
		$sql = "SELECT *
				FROM `xls_sync_config_fields`
				WHERE `status`='".Status::PUBLISHED."'
				AND `import_or_insert`='F'
				AND `select_type` = '1'
				ORDER BY `sort`";
		try{
			$this->sync_config_import = dbFetchAll($sql);
		}catch (Exception $e){
			$this->writeLog($e->getMessage());
		}

		// Az importhoz szükséges adatok, amikor a táblához minden adat rendelkezésre áll
		$sql = "SELECT *
				FROM `xls_sync_config_fields`
				WHERE `status`='".Status::PUBLISHED."'
				AND `import_or_insert`='F'
				AND `select_type` = '0'
				ORDER BY `sort`, `sort2`";
		try{
			$this->sync_config_import2 = dbFetchAll($sql);
		}catch (Exception $e){
			$this->writeLog($e->getMessage());
		}

		// Ez írja ki, hogy mely táblákba fogunk importálni
		$sql = "SELECT * FROM `xls_sync_config_import`
				WHERE `status`='2'
				ORDER BY `sort`";
		$this->echoImport = dbFetchAll($sql);


		// A temp tábla betöltése után lefutó sql lekérdezése
		$sql = "SELECT * FROM `xls_sync_config`
				WHERE `status`='2' AND `db_update_post_action` IS NOT NULL";
		$this->updateDb = dbFetchAll($sql);

		if(empty($this->sync_config_fields || $this->sync_config)){
			echo "The sync config table is empty.";
			$this->writeLog("The sync config table is empty.");
			exit;
		} else {
			echo "Sync config tables loaded.".'<br/>';
			echo '<br/>';

			$this->checkAndClearUnusedTempTables();
			echo 'Tables for import: '.'<br/>';
			for($i=0; $i<count($this->echoImport); $i++){
				echo "<div style =\"font:14px Arial,tahoma,sans-serif;text-align: justify;color:#ff0000\">".$this->echoImport[$i]['target_table_name']."</div>";
				echo '<br/>';
			}

			if(!is_dir(Yang::getAlias('webroot').DS."xlsImport".DS."syncErrors")){
				mkdir(Yang::getAlias('webroot').DS."xlsImport".DS."syncErrors");
			}
		$this->runTheImport();

		} // end of if empty($this->sync_config_fields
	} // end of function actionIndex

		private function runTheImport(){
			echo '<br>'.date("Y-m-d H:i:s").' - Prepare the insert';
			$sql = "SELECT * FROM xls_sync_config
					LEFT JOIN xls_sync_config_import
					ON xls_sync_config.sync_process_id = xls_sync_config_import.sync_process_id
					WHERE xls_sync_config.status = '2'
					GROUP BY xls_sync_config.row_id
					ORDER BY xls_sync_config.row_id";
			try{
				$settingsForImport = dbFetchAll($sql);
			} catch (Exception $e){
				$this->writeLog($e->getMessage());
			}

			$tempTableName = NULL;

			foreach ($settingsForImport as $valuesInExcel) {
				$processId = $valuesInExcel['sync_process_id'];
				$xlsOrCsv = $valuesInExcel['source_file_type'];
				$csvFirstRow = $valuesInExcel['source_file_skipped_rows'];
				$commaOrSemicolon = $valuesInExcel['csv_delimiter'];
				$manualOrautomatic = $valuesInExcel['insert_mode'];
				$after_insert_to_temp = $valuesInExcel['temp_table_post_action'];
				$sourcePath = $valuesInExcel['source_file_path'];
				$sourceName = str_replace("#ACTUALDATE#", date("Ymd"), $valuesInExcel['source_file_name']);

				$loadedDirectoryPath = Yang::getAlias('webroot').DS;
				eval("\$loadedDirectoryPath .= \"$sourcePath\";");
				if($manualOrautomatic == 'manual'){
					$filesFromSource = scandir($sourcePath);
				} elseif($manualOrautomatic == 'automatic'){
					$filesFromSource = scandir($loadedDirectoryPath);
				} else{
					echo 'There is no value in table xls_sync_config in field insert_mode. Please fill this field with manual or automatic';
					$this->writeLog('There is no value in table xls_sync_config in field insert_mode. Please fill this field with manual or automatic');
					exit;
				}

				foreach ($filesFromSource as $fileName) {
					if(strpos($fileName, $sourceName) !== false) {
						echo '<br>'.date("Y-m-d H:i:s").' - Start to create temptable'.'<br>';
						$tempTableName = $this->createTempTable($processId, $valuesInExcel, $xlsOrCsv, $sourcePath, $fileName, $commaOrSemicolon, $csvFirstRow, $manualOrautomatic, $after_insert_to_temp);
						 if(!$tempTableName){
							 echo  "Can not create temp table! (".$tempTableName.")";
							 $this->writeLog("Can not create temp table! (".$tempTableName.")");
						 }
						 if( ($processId === 'syncregistration') AND (strpos($sourcePath, 'ftp/uti') !== false) ) {
							 $this->deleteUTIEmaptyRows($tempTableName);
						 }
					}
				}
			}


			$sql = "SELECT *
					FROM `xls_sync_config`
					LEFT JOIN `xls_sync_config_import` ON
						`xls_sync_config`.sync_process_id = `xls_sync_config_import`.sync_process_id
					WHERE xls_sync_config_import.status = '2'
					AND xls_sync_config.status = '2'
					ORDER BY `xls_sync_config_import`.sort";
			try {
				$settingsForInsert = dbFetchAll($sql);
			} catch (Exception $e){
				$this->writeLog($e->getMessage());
			}


			foreach ($settingsForInsert as $targetTable) {
				$chooseTargetTable = $targetTable['target_table_name'];
				$chooseInsertMode = $targetTable['insert_mode'];
				$chooseSqlFromConfig = $targetTable['insert_sql_parameters'];
				$chooseUpdateSqlFromConfig = $targetTable['db_update_post_action'];
				$parameterForWhereClause = $targetTable['sql_parameter_for_temp_insert'];
				$chooseTempTable = $targetTable['temp_table_id'];
				$processIdForInsert = $targetTable['sync_process_id'];
				$sourcePath = $targetTable['source_file_path'];
				$sourceName = $targetTable['source_file_name'];
				$updateTablePostAction = $targetTable['table_update_post_action'];
				$updatenew = $targetTable['update_sql'];
				$loadedDirectoryPath = Yang::getAlias('webroot').DS;
				$button = FALSE;
				if($chooseInsertMode == "automatic"){
			//		$this->updateDb($chooseTargetTable, $chooseTempTable, $chooseUpdateSqlFromConfig, $chooseSourceType);
					eval("\$loadedDirectoryPath .= \"$sourcePath\";");
					$filesFromSource = scandir($loadedDirectoryPath);
					if(!empty($tempTableName)){
						$this->UpdateDb($chooseTargetTable, $updatenew, $chooseInsertMode, $tempTableName, $chooseTempTable);
						$this->insertIntoDb($chooseTargetTable, $tempTableName, $chooseSqlFromConfig, $processIdForInsert, $sourcePath, $sourceName, $chooseInsertMode, $parameterForWhereClause, $updateTablePostAction);
					//	$this->writeLog("The inserted file name is ".$filesFromSource." for table: ".$chooseTargetTable);
					}

				} else {

					if (isset($_GET['get_users'])) {
						$noDelete = NULL;
						$filesFromSource = scandir($sourcePath);
						$this->UpdateDb($chooseTargetTable, $updatenew, $chooseInsertMode, $tempTableName, $chooseTempTable);
						$this->insertIntoDb($chooseTargetTable, $chooseTempTable, $chooseSqlFromConfig, $processIdForInsert, $noDelete, $sourceName, $chooseInsertMode, $parameterForWhereClause, $updateTablePostAction);
					//	$this->writeLog("The inserted file name is ".$filesFromSource." for table: ".$chooseTargetTable);
					}
				$button = TRUE;

				} // end of else (automatic)
			}

			if ($button == TRUE){
				echo '<br/>';
				echo 'Are you sure to insert the excel?'.'<br/>';
				echo '<form method="get">
						<input type="hidden" name="get_users">
						<input type="submit">
						</form>';
			}

		} // end of function run2

	public function deleteUTIEmaptyRows($temp_table)
	{
		echo '<br>'.date("Y-m-d H:i:s").' - deleteUTIEmaptyRows()'.'<br>';
		$sql = "DELETE FROM `".$temp_table."` WHERE xls_card IS NULL OR xls_card = 'NULL';";

		try{
			dbExecute($sql);
		}catch (Exception $e){
			$this->writeLog($e->getMessage());
		}

	}

	 	 /**
		 * Ez a funkció készíti el a temp táblát a nevében a pontos dátumidővel,
		 * majd a betöltendő file típus alapján meghívja azt a funkciót, ami betölti
		 * az elékészített temp táblába az adatokat.
		 * A funkció végén pedig lefuttaja azt az sql-t, amit az xls_sync_config táblába
		 * beírtunk.
		 * @param string $procId
		 * @param string $ValuesForImport
		 * @param string $xlOrCs
		 * @param string $sourceOfPath
		 * @param string $nameOfFile xls_sync_config tábla source_file_name mező
		 * @param string $delimiterForCsv
		 * @param string $csvHeader
		 * @param string $manualOrAutomatic
		 * @param string $after_insert
		 * @return string Visszaadja az elkészült temp tábla nevét
		 */
		private function createTempTable($procId, $ValuesForImport, $xlOrCs, $sourceOfPath, $nameOfFile, $delimiterForCsv, $csvHeader, $manualOrAutomatic, $after_insert){

			$tempTableForImportString = "CREATE TABLE IF NOT EXISTS `"
								.$ValuesForImport['temp_table_name'].$this->tempTableDate."` (
								`id` int(11) NOT NULL AUTO_INCREMENT,";

			for($i=0; $i<count($this->sync_config_fields); $i++) {
				if($procId == $this->sync_config_fields[$i]['sync_process_id']){
					$tempTableForImportString .= "`".$this->sync_config_fields[$i]['create_column_name']
						."` ".$this->sync_config_fields[$i]['create_column_type'].",";
				}
			}
			$tempTableForImportString .= "PRIMARY KEY (`id`)
				 ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COLLATE = utf8_unicode_ci";

			try{
				dbExecute($tempTableForImportString);
			}catch (Exception $e){
				$this->writeLog($e->getMessage());
			}


		//	$loadedFileName = $ValuesForImport['source_file_name']; // Az excelnél figyelni kell nagyon a cellák formátumára! Például ne legyen benne tizedes választó szám formátumnál, stb.
			$tempTableName = $ValuesForImport['temp_table_name'].$this->tempTableDate;
			$loadedFileName = $nameOfFile;
			$loadedExcelDirectoryPath = Yang::getAlias('webroot').DS;


			if($manualOrAutomatic == 'manual'){
				$loadedExcelDirectoryPath = Yang::getAlias('webroot').DS
										."xlsImport".DS.$loadedFileName;

			} elseif($manualOrAutomatic == 'automatic'){
				eval("\$loadedExcelDirectoryPath .= \"$sourceOfPath\";");

			} else{
				echo 'There is no value in table xls_sync_config in field insert_mode. Please fill this field with manual or autmatic';
				$this->writeLog('There is no value in table xls_sync_config in field insert_mode. Please fill this field with manual or autmatic');
				exit;
			}

//             $loadedExcelDirectoryPath = Yang::getAlias('webroot').DS
//										."xlsImport".DS.$loadedFileName;
			echo "<strong>"."Name of the inserted file: ".(string)$loadedFileName."</strong>".'<br/>';
			$this->writeLog("Name of the inserted file is: ".(string)$loadedFileName);
			echo '<br/>';

			if( ( in_array($xlOrCs, ['xls', 'xlsx'])) && file_exists($loadedExcelDirectoryPath)){
				$this->insertTheXlsOrXlsxFromTempTable($procId, $ValuesForImport, $loadedExcelDirectoryPath, $tempTableName);

			} elseif ($xlOrCs == "csv" && file_exists($loadedExcelDirectoryPath)){
				$this->insertTheCsvToTempTable($procId, $ValuesForImport, $delimiterForCsv, $csvHeader, $loadedExcelDirectoryPath, $tempTableName);

			} // end of elseif

			$lastTempTableName = $ValuesForImport['temp_table_name'].$this->tempTableDate;
			if(!empty($after_insert)){
				$after_insert_sql = str_replace("##CHANGETABLENAME##", $lastTempTableName, $after_insert);
				echo '<br/>';
				echo "UPDATE the temp table after create: ";
				echo "<div style =\" 'color:blue'; font-size: 14px ;text-align: justify; background-color: #e6ffff;border: 1px solid black;padding: 4px; \">".$after_insert_sql."</br>"."</div>";
				echo '<br/>';

				try{
					dbExecute($after_insert_sql);
				}
				catch (Exception $e){
					$this->writeLog($e->getMessage());
				}
			}
			echo '<br>'.date("Y-m-d H:i:s").' - Temptable '.$ValuesForImport['temp_table_name'].$this->tempTableDate.' created!';
			return $ValuesForImport['temp_table_name'].$this->tempTableDate;
		} // end of function createTempTable


		/**
		 * Ez a funkció elkészíti az INSERT parancsot, ami betölt a temp táblából az éles adatbázisba
		 * az adatokat és lefuttatja ezt. A betöltés végén, amennyiben automatikus a futás és van értéke
		 * a $sourcePathForDelete paraméternek, akkor meghívja a moveTheInsertedFiles() funkciót.
		 * @param string $targetTable
		 * @param string $tempTableId
		 * @param string $targetFields
		 * @param string $syncProcId
		 * @param string $sourcePathForDelete
		 * @param string $sourceNameForDelete
		 * @param string $manOrauto
		 * @param string $parameterForWhereClause
		 * @param string $chooseUpdateSqlFromConfig
		 */
		private function insertIntoDb($targetTable, $tempTableId, $targetFields, $syncProcId, $sourcePathForDelete, $sourceNameForDelete, $manOrauto, $parameterForWhereClause, $updateTablePostAction){
		// $syncInsertErrors = Yang::getAlias('webroot').DS."xlsImport".DS."syncErrors".DS."syncInsertErrorFile_".date("YmdHis").".txt";
			$targetFieldsString = $this->createSql($targetTable, $syncProcId);

			if($manOrauto == 'automatic'){
				$targetFieldsString .= str_replace("##CHANGETHEREALNAME##", $tempTableId, $targetFields);
				$targetFieldsString .= " ".$parameterForWhereClause;
			} elseif($manOrauto == 'manual'){
				$targetFieldsString .= str_replace("##CHANGETHEREALNAME##", $this->lastTempTable($tempTableId), $targetFields);
				$targetFieldsString .= " ".$parameterForWhereClause;
			} else{
				echo 'There is no value in table xls_sync_config in field insert_mode. Please fill this field with manual or automatic';
				$this->writeLog('There is no value in table xls_sync_config in field insert_mode. Please fill this field with manual or automatic');
				exit;
			}

			echo "<pre>";
			echo "INSERT??:  -> (".$targetTable.")  ";
			echo "<div style =\" 'color:#2E8B57'; text-align: justify;border: 1px solid black; padding: 4px; background-color: #ffffcc\">".$targetFieldsString."</br>"."</div>";
			echo "</pre>";

			if($targetFieldsString){

				if(!empty($targetFieldsString)){
					$result = dbFetchAll($targetFieldsString);
				} else {
					echo "Missing 'insert_sql_parameters' in xls_sync_config_import table"."</br>";
					$this->writeLog("Missing 'insert_sql_parameters' in xls_sync_config_import table");
				}
				try {

					if(count($result)>0){


						$fieldName = (array_keys($result[0]));
						echo '<br>'.date("Y-m-d H:i:s").' - Start to insert to database!';
						echo '<br>'."There are ".count($result)." rows to insert";


							for($i = 0; $i<count($result); $i++) {
								$count = $i;
								$sql = "INSERT INTO `".$targetTable."` (";
									for($j=0; $j<count($fieldName); $j++){
										$sql .= "`".$fieldName[$j]."`,";
									}
								$sql = trim($sql, ",");
								$sql .= ") VALUES (";
								for($k=0; $k<count($fieldName); $k++){
									if($result[$i][$fieldName[$k]]==="" || $result[$i][$fieldName[$k]]===NULL){
										$sql .= "NULL,";
									}
									else {
										$sql .= "'".$result[$i][$fieldName[$k]]."',";
									}
								}
								$sql = trim($sql, ",");
								$sql .= ")";
								echo "<pre>";
								echo "<div style =\" 'color:red'; text-align: justify;font-family: Arial,Helvetica,sans-serif border: padding: 4px; 1px solid black;\">INSERT!!: ".$sql."</br>"."</div>";
								echo "</pre>";

								dbExecute($sql);

								echo ($count +1).". row(s) inserted";

							} // end of for ($i)
							echo '<br>'.date("Y-m-d H:i:s").' - End of inserting to database!';
			//				$transaction->commit();

						if(isset($updateTablePostAction)){
							$this->updateTableAfterInsert($updateTablePostAction);
						}

						if($sourcePathForDelete != NULL){
							$this->moveTheInsertedFiles($sourcePathForDelete, $sourceNameForDelete);
						}

					} // end of if(count)

				} catch (Exception $e){
					echo "\r\n";
					echo "<div style =\" 'color:#FF00FF'; text-align: justify;background-color: #ff9933;border: 1px solid black;padding: 4px; \">"."The problem is: ".$e->getMessage()."</div>";
			//		$transaction->rollBack();

				} // end of if
			} else {echo "The ".$targetFieldsString." has a failure";}
		} // end of function insertIntoDb2


		/**
		 * Ez a funkció meghívja azt az sql parancsot, ami az xls_sync_config táblában az db_update_post_action oszlopban
		 * van és lefuttaja azt. Ezzel a már betöltött adatokat tudjuk felülírni, amennyiben a forrás file-ban
		 * lévő sorok adatai eltérnek az adatbázisban lévő adatokról.
		 * @param string $chooseTheTargetTable
		 * @param string $updateSql
		 * @param string $manOrauto
		 * @param string $tempTableId
		 * @param string $chooseTempTableID
		 */
		private function updateDb($chooseTheTargetTable, $updateSql, $manOrauto, $tempTableId, $chooseTempTableID){

			echo '<br>'.date("Y-m-d H:i:s").' - Update the database!';
			$updateSqlString = $updateSql;
			echo "\r\n";
			if($manOrauto == 'automatic'){
				$updateSqlString = str_replace("##CHANGETABLENAME##", $tempTableId, $updateSqlString);

			} elseif($manOrauto == 'manual'){
				$updateSqlString = str_replace("##CHANGETABLENAME##", $this->lastTempTable($chooseTempTableID), $updateSqlString);

			} else{
				echo 'There is no value in table xls_sync_config in field insert_mode. Please fill this field with manual or automatic';
				$this->writeLog('There is no value in table xls_sync_config in field insert_mode. Please fill this field with manual or autmatic');
				exit;
			}

			if(!empty($updateSqlString) && strlen($updateSqlString)> 2){
				echo "Update the ".$chooseTheTargetTable." table!!:"."</br>";
				echo "\r\n";
				print_r("<div style =\" 'color:#1E90FF'; text-align: justify; border: 1px solid black; background-color: red; padding: 4px; \">".$updateSqlString."</br>"."</div>");
				try{
					dbExecute($updateSqlString);
				}catch (Exception $e){
					$this->writeLog($e->getMessage());
				}
			}

		} // end of function updateDb


		private function lastTempTable($chooseTempTable){
			$currentdb  = preg_match("/dbname=([^;]*)/", dbConnectionString(), $matches);
			$currentdb = $matches[1];
			$lastTempTableSelect = "SELECT table_name AS name
									FROM information_schema.tables
									WHERE table_type = 'BASE TABLE'
									AND table_schema='$currentdb'
									AND TABLE_NAME LIKE '%".$chooseTempTable."%'
									ORDER BY table_name DESC LIMIT 1";
			$result=dbFetchRow($lastTempTableSelect);

			return $result['name'];
		}

		/**
		 * Ez a funkció készíti el az sql-nek a FROM-ig tartó részét az insertIntoDb funkció számára,
		 * ami alapján betölti majd az adatokat az adatbázisba.
		 * @param string $targetTable2
		 * @param string $syncId
		 * @return string Egy SELECT-et ad vissza a FROM-ig bezárólag
		 */
		private function createSql($targetTable2, $syncId){
			$targetFieldsString = "SELECT ";
				for($i=0; $i<count($this->sync_config_import); $i++){

					if( $this->sync_config_import[$i]['target_table_name'] == $targetTable2 &&
						$this->sync_config_import[$i]['select_type'] == 1 &&
						$this->sync_config_import[$i]['sync_process_id'] == $syncId){
							$targetFieldsString .= "".$this->sync_config_import[$i]['create_column_name']." AS ";
							$targetFieldsString .= "`".$this->sync_config_import[$i]['target_field_name']."`, ";
							$targetFieldsString .= "a.* FROM (SELECT ";
					}
				}
				for($j=0; $j<count($this->sync_config_import2);$j++){
					if($this->sync_config_import2[$j]['target_table_name'] == $targetTable2 &&
						($this->sync_config_import2[$j]['target_field_name'] != "üres" || $this->sync_config_import2[$j]['target_field_name'] != NULL) && $this->sync_config_import2[$j]['sync_process_id'] == $syncId){
							$targetFieldsString .= "".$this->sync_config_import2[$j]['create_column_name']." AS ";
							$targetFieldsString .= "`".$this->sync_config_import2[$j]['target_field_name']."`, ";
					} elseif($this->sync_config_import2[$j]['target_table_name'] == $targetTable2 &&
							($this->sync_config_import2[$j]['target_field_name'] == "table_update_post_action" || $this->sync_config_import2[$j]['target_field_name'] == NULL) && $this->sync_config_import2[$j]['sync_process_id'] == $syncId){
								$targetFieldsString .= "".$this->sync_config_import2[$j]['create_column_name'].", ";
					}
				}
				$targetFieldsString = substr($targetFieldsString, 0, -2);
				$targetFieldsString .= " FROM ";

				return $targetFieldsString;
		}


		/**
		 * Ez a funkció lellenőrzi, hogy az adott típusú temp táblából hány darab létezik az adatbázisban.
		 * Amennyiben 5-nél több, akkor a legrégebbit törli.
		 *
		 */
		private function checkAndClearUnusedTempTables(){
			$currentdb  = preg_match("/dbname=([^;]*)/", dbConnectionString(), $matches);
			$currentdb = $matches[1];
			echo "Using database: "."<strong>".$currentdb."</strong>".'<br/>';

			for($j=0 ; $j<count($this->sync_config); $j++){
				$countTempTables = "SELECT COUNT(*) AS SUM FROM information_schema.TABLES
									WHERE TABLE_SCHEMA='$currentdb'
									AND table_name LIKE '".$this->sync_config[$j]['temp_table_name']."%'";
				$countTempTablesSelect = dbFetchAll($countTempTables);
				if($countTempTablesSelect[0]['SUM'] <= 4){
					echo 'There is not any deletable temp table.'.'<br/>';
					echo '<br/>';
					return;
				}

			$countNumberOfTablesSelect = "SELECT COUNT(*) AS no FROM information_schema.tables
										 WHERE table_type = 'BASE TABLE' AND table_schema='$currentdb'
										 AND table_name LIKE '".$this->sync_config[$j]['temp_table_name']."%';";
			$countNumberOfTables = dbFetchAll($countNumberOfTablesSelect);
			if(!isset($countNumberOfTables[0]['no'])){
				echo 'Nincs ilyen érték.';
				return;
			}

			$searchDropableTables = "SELECT table_name AS name
									FROM information_schema.tables
									WHERE table_type = 'BASE TABLE'
									AND table_schema='$currentdb'
									AND TABLE_NAME LIKE '".$this->sync_config[$j]['temp_table_name']."%'
									ORDER BY table_name DESC LIMIT ".($countNumberOfTables[0]['no']-4)." OFFSET 4";
			$searchDropableTablesSelect = dbFetchAll($searchDropableTables);
			$dropTableSql = "DROP TABLE IF EXISTS ";
			foreach ($searchDropableTablesSelect as $value) {
				$dropTableSql .= "`".$value['name']."`".',';
			}
			$dropTableSql = trim($dropTableSql, ",");
			$dropTableSql .= ";";
			dbExecute($dropTableSql);
			} // end of for
		} // end of checkAndClearUnusedTempTables


		/**
		 * Ez a funkció áthelyezi az adott file-t egy könyvtárba és törli azt az eredeti helyéről.
		 * Ha nem talál könyvtárat, akkor készít egyet.
		 * @param string $path
		 * @param string $fileName
		 */
		private function moveTheInsertedFiles($path, $fileName){

			$currentdb  = preg_match("/dbname=([^;]*)/", dbConnectionString(), $matches);
			$currentdb = $matches[1];

			$source = $path;
			list($sourceToCheck) = explode('/$', $source);

			if (is_dir($sourceToCheck)) {
				$files = scandir($sourceToCheck);
   			} else {
				echo '<br/>';
				echo "No such directory for move the deletable file(s)";
				$this->writeLog("No such directory for move the deletable file(s)");
			}
		//	$files = scandir($source);

			$destination = Yang::getAlias('webroot').DS
											 ."xlsImport".DS.$currentdb.DS;
			if(!is_dir($destination)){
				mkdir($destination, 0755);
			}
			if(!empty($files)){
				foreach ($files as $file) {
					if(is_file($sourceToCheck.DS.$file) && strpos($file, $fileName) !== false){
						if (in_array($file, array(".",".."))) {
							continue;
						}
						// If we copied this successfully, mark it for deletion
						if (copy($sourceToCheck.DS.$file, $destination.$this->today."_".$file)) {
							unlink($sourceToCheck.DS.$file);
							echo "Move and delete the ".$file." file.";
						}
					}
				}
			}
		} // end of moveTheInsertedFiles()


		/**
		 * Ez a funkció írja be a hiba logokat a megadott file-ba
		 * @param string $sqlException
		 */
		private function writeLog($sqlException){
			$syncInsertErrors = Yang::getAlias('webroot').DS."xlsImport".DS."syncErrors".DS."syncInsertErrorFile_".date("Ymd").".txt";
			$file = fopen($syncInsertErrors, "a+");
			fwrite($file, date("Y-m-d H:m:s").": ".$sqlException.PHP_EOL);
			print_r("Error during execute sql : ".$sqlException.PHP_EOL);
			fclose($file);
		}


		/**
		 * Ez a funkció behívja az adott elérési úton lévő xls vagy xlsx file-t egy tömbbe, majd
		 * a config alapján betölti az adatokat a már létrehozott temp táblába
		 * @param string $procId
		 * @param string $ValuesForImport
		 * @param string $loadedExcelDirectoryPath
		 * $param string $tempTableName
		 */
		private function insertTheXlsOrXlsxFromTempTable($procId, $ValuesForImport, $loadedExcelDirectoryPath, $tempTableName) {
			$loadedExcel = new \SpreadsheetReader($loadedExcelDirectoryPath);

				$skip_rows = explode(',', $ValuesForImport['source_file_skipped_rows']);
				$loadedExcelFiltered = new ExcludeRowsFilter($loadedExcel, $skip_rows); // How many rows will skip in the sheet
				$loadedExcelFiltered -> ChangeSheet($ValuesForImport['source_file_sheet_nr']);  // Change to given Sheet (Number of the sheet from zero)

				$Sheets = $loadedExcelFiltered -> Sheets();

				echo '<ul type="square" style = "color:black">'.'Sheet name: '.utf8_encode($Sheets[0].$Name). '<br/>';
				$sheet1_row_number = $loadedExcelFiltered ->count();
				print_r('Number of rows: '.$sheet1_row_number.'<br/>');
				echo date("Y-m-d H:i:s")." - Insert data from ".$loadedExcelDirectoryPath." to temp table... "."</br>";

					if($sheet1_row_number > 0){
						$arr = [];
						$tomb1 = [];
						$insert_num = 0;

						foreach ($loadedExcelFiltered as $Row){
							if(count($Row)>0){
								for($i=0; $i<count($this->sync_config_fields); $i++){
									if($procId == $this->sync_config_fields[$i]['sync_process_id']){
								//		$name .= "`".$this->sync_config_fields[$i]['create_column_name']."`,";

										$tomb1[] = $this->sync_config_fields[$i]['create_column_name'];
									}
								}

								for($i=0; $i<count($this->sync_config_fields); $i++){
									if($procId == $this->sync_config_fields[$i]['sync_process_id']){
										if($this->sync_config_fields[$i]['target_field_type'] == "date"){
											$Row[$this->sync_config_fields[$i]['sort2']]= strtotime($Row[$this->sync_config_fields[$i]['sort2']]) ? date("Y-m-d",strtotime($Row[$this->sync_config_fields[$i]['sort2']])) : $Row[$this->sync_config_fields[$i]['sort2']];
										}
											$Row[$this->sync_config_fields[$i]['sort2']]=(empty($Row[$this->sync_config_fields[$i]['sort2']])) ? "NULL" : $Row[$this->sync_config_fields[$i]['sort2']];

									$value[] .= mb_convert_encoding($Row[$this->sync_config_fields[$i]['sort2']], "UTF-8");

										}
									}
								$arr[] = array_combine($tomb1, $value);
								$insert_num++;

								if($insert_num >= 200){
									$this->multiInsert($tempTableName, $arr);
									$insert_num = 0;
									$arr = [];
										echo date("Y-m-d H:i:s")." - Insert the 200!";
								} // end of if
							} // end of if

						} // end of foreach
						if (count($arr)) {
						$this->multiInsert($tempTableName, $arr);
						}
//						try {
//								$builder = Yii  app()  db  schema  commandBuilder;
//								$command = $builder->createMultipleInsertCommand($tempTableName, $arr);
//					            $command->execute();
//
//								}catch (Exception $e){
//									$this->writeLog($e->getMessage());
//								}
					} // end of if
					echo '<br>'.date("Y-m-d H:i:s").' - End of insert xls(x) to temp table!';
		}


		/**
		 * Ez a funkció behívja az adott elérési úton lévő csv file-t egy tömbbe, majd
		 * a config alapján betölti az adatokat a már létrehozott temp táblába
		 * @param type $procId
		 * @param type $ValuesForImport
		 * @param type $delimiterForCsv
		 * @param type $csvHeader
		 * @param type $loadedExcelDirectoryPath
		 */
		public function insertTheCsvToTempTable($procId, $ValuesForImport, $delimiterForCsv, $csvHeader, $loadedExcelDirectoryPath, $tempTableName) {

			//	$loadedCsv = new  SpreadsheetReader_CSV($loadedExcelDirectoryPath, array('Delimiter' => $delimiterForCsv));
				$loadedCsv = new  SpreadsheetReader($loadedExcelDirectoryPath);

				echo date("Y-m-d H:i:s")." - Insert data from ".$loadedExcelDirectoryPath." to temp table... "."</br>";
				$rowNumber=0;
				while (!empty($loadedCsv->next())) {
					$Row=$loadedCsv->current();
					++$rowNumber;
					if($rowNumber>$csvHeader) {
						if(count($Row)>0 && ($Row[0] != "" || $Row[0] != null)) {
							$sql = "INSERT INTO `".$ValuesForImport['temp_table_name'].$this->tempTableDate."` (";
								for($i=0; $i<count($this->sync_config_fields); $i++){
									if($procId == $this->sync_config_fields[$i]['sync_process_id']){
										$sql .= "`".$this->sync_config_fields[$i]['create_column_name']."`,";
									}
								}
							$sql = trim($sql, ",");
							$sql .= ") VALUES (";
							for($i=0; $i<count($this->sync_config_fields); $i++) {
									if($procId == $this->sync_config_fields[$i]['sync_process_id']) {
										if($this->sync_config_fields[$i]['target_field_type'] == "date") {
											$Row[$this->sync_config_fields[$i]['sort2']]= strtotime($Row[$this->sync_config_fields[$i]['sort2']]) ? date("Y-m-d",strtotime($Row[$this->sync_config_fields[$i]['sort2']])) : $Row[$this->sync_config_fields[$i]['sort2']];
										}
										$Row[$this->sync_config_fields[$i]['sort2']]=(empty($Row[$this->sync_config_fields[$i]['sort2']])) ? "NULL" : "'".trim($Row[$this->sync_config_fields[$i]['sort2']],"'")."'";
										$sql .= utf8_encode($Row[$this->sync_config_fields[$i]['sort2']]).",";
									}
								}
							$sql = trim($sql, ",");
							$sql .= ")";

							try {
								$insertToTemp = dbExecute($sql);
							} catch (Exception $e){
								echo ("Can not insert the source file to temp table: ".$e->getMessage());
								$this->writeLog($e->getMessage());
							}

						} // end of if
					}
				} // end of foreach
				echo '<br>'.date("Y-m-d H:i:s").' - End of insert csv to temp table!';
		} // end of function insertTheCsvToTempTable


		/**
		 * Ez a funkció tölti be a tömbben tárolt oszlop értékeket és a hozzájuk tartozó értékeket
		 * @param type $tableName
		 * @param type $data
		 */
		public function multiInsert($tableName='', $data=[]){

	        $result = dbMultiInsert($tableName,$data);
	        if($result<>"OK") $this->writeLog($result);

		} // end of function multiInsert

		public function updateTableAfterInsert($sqlForUpdate){

			$updateSqlStringPostAction = $sqlForUpdate;

			if (isset($updateSqlStringPostAction)){
				echo '<br>'.date("Y-m-d H:i:s").' - Update the temp table!';
				echo "</br>";
				print_r("<div style =\" 'color:black'; text-align: justify;background-color: pink; border: 1px solid black;padding: 4px; \">".$updateSqlStringPostAction."</br>"."</div>");
				try{
					dbExecute($updateSqlStringPostAction);
				}catch (Exception $e){
					$this->writeLog($e->getMessage());
				}
			}
		}
} // end of class
