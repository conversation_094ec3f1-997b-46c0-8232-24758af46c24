<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\MyActiveForm;
	use app\models\Employee;
	use app\models\Registration;
	use app\models\Status;
	use app\models\Terminal;
	use Yang;

`/yii2-only';


#yii2: done

class RgnetDataProviderController extends Controller
{
	public function actionIndex() {
		$this->layout = "empty";

		$command = requestParam('command');

		switch ($command) {
			case "getTime":
				echo date("Y-m-d H:i:s");
				break;
			case "getUser":
				$emp_id = requestParam('person_id');
				$terminal_id = requestParam('terminal_id');
				$resp = null;
				echo $this->isEmployeeValid($emp_id, true, $terminal_id, $resp);
				break;
			case "setUser":
				$emp_id = requestParam('person_id');
				$terminal_id = requestParam('terminal_id');
				$event_type_id = requestParam('event');
				$withoutEmployeeCheck = ((int)requestParam('withoutEmployeeCheck') === 1);
				echo $this->registerEmployee($emp_id, $event_type_id, $terminal_id, $withoutEmployeeCheck);
				break;
			default:
				break;
		}
	}

	private function registerEmployee($emp_id, $event_type_id, $terminal_id, $withoutEmployeeCheck = false) {
		$resp = array();

		$ev = "";
		switch ($event_type_id) {
			case "login":
				$ev = "NMB";
				break;
			case "logout":
				$ev = "NMK";
				break;
		}

		$data = array();
		if (!$withoutEmployeeCheck) {
			$this->isEmployeeValid($emp_id, true, $terminal_id, $data);
		}

		if ((isset($data["card"]) && !empty($data["card"])) || $withoutEmployeeCheck) {
			$t = Terminal::model()->findByAttributes(array("terminal_id"=>$terminal_id));

			if ($withoutEmployeeCheck) {
				$data["emp_id"] = $emp_id;
				$data["card"] = $emp_id;
			}
			
			if ($t) {
				$r = new Registration;
				$r->terminal_id = $t->terminal_id;
				$r->card = $data["card"];
				$r->time = date("Y-m-d H:i:s");
				$r->event_type_id = $ev;
				$r->status = 2;
				
				$r->created_by = "rgnet";
				$r->created_on = date("Y-m-d H:i:s");
				
				$error = MyActiveForm::_validate($r);
				
				$r->save();

				if ($r->row_id) {
					$resp[] = "ok";
					$resp[] = $data["emp_id"];
					if (!$withoutEmployeeCheck) {
						$resp[] = $data["fullname"];
					}
				} else {
					$resp[] = "error";
					$resp[] = $emp_id;
					$resp[] = "reg_save_error";
				}
			} else {
				$resp[] = "error";
				$resp[] = $emp_id;
				$resp[] = "terminal_not_found";
			}
		} else {
			$resp[] = "error";
			$resp[] = $emp_id;
			$resp[] = "employee_not_found";
		}

		return implode("^",$resp);
	}

	private function isEmployeeValid($emp_id, $reg_check, $terminal_id, &$data) {
		$resp = array();
		$data = array();

		if (empty($emp_id)) {
			$resp[] = "error";
			$resp[] = $emp_id;
			$resp[] = "employee_not_found";

			return implode("^",$resp);
		}

		$nowDate = date("Y-m-d");

		$fullname = Employee::getParam('fullname', 'e');

		$SQL = "
			SELECT
				e.`emp_id`, ecard.`card`,
				$fullname AS fullname
			FROM
				`employee` e
			LEFT JOIN
				`employee_contract` ec ON ec.`employee_id` = e.`employee_id`
				AND ('$nowDate' BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`,'".App::getSetting("defaultEnd")."'))
				AND e.`status` = 2
			LEFT JOIN
				`employee_card` ecard ON ecard.`employee_contract_id` = ec.`employee_contract_id`
				AND ('$nowDate' BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`,'".App::getSetting("defaultEnd")."'))
				AND e.`status` = 2
			WHERE
				e.`emp_id` = '$emp_id' AND ('$nowDate' BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`,'".App::getSetting("defaultEnd")."'))
				AND e.`status` = 2
		";

//     $results = dbFetchAll($SQL); // query all results
		$results = dbFetchRow($SQL); // query only first row

		if ($results) {
			$resp[] = "ok";
			$resp[] = $results["emp_id"];

			$data["emp_id"] = $results["emp_id"];
			$data["card"] = $results["card"];
			$data["fullname"] = $results["fullname"];

			if ($reg_check) {
				$card = $results["card"];

				$r = new Registration;
				$crit = new CDbCriteria();
				$crit->alias = "r";
				$crit->join = "LEFT JOIN `terminal` t ON t.`terminal_id` = r.`terminal_id` AND t.`status`=".Status::PUBLISHED;
				$crit->condition = "r.`card` = '$card' AND r.`event_type_id` IN ('NMB','NMK') AND r.`status` = 2 AND t.`terminal_id` = '$terminal_id'";
				$crit->order = "r.`time` DESC";
				$res_r = $r->find($crit);

				if (count($res_r)) {
					if ($res_r->event_type_id === "NMB") { // kint
						$resp[] = "logged";
					} else { // bennt
						$resp[] = "not_logged";
					}

					$resp[] = $results["fullname"];
				} else {
					$r = new Registration;
					$crit = new CDbCriteria();
					$crit->alias = "r";
					$crit->condition = "r.`card` = '$card' AND r.`event_type_id` IN ('NMB','NMK') AND r.`status` = 2";
					$crit->order = "r.`time` DESC";
					$res_r = $r->find($crit);

					if (count($res_r)) {
						$resp[] = "not_found_in_terminal";
						$resp[] = $results["fullname"];
					} else {
						$resp[] = "not_logged";
						$resp[] = $results["fullname"];
					}
				}
			}
		} else {
			$resp[] = "error";
			$resp[] = $emp_id;
			$resp[] = "employee_not_found";
		}

		return implode("^",$resp);
	}
}

?>