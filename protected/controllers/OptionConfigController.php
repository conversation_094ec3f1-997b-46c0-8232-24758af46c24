<?php

class OptionConfigController extends Grid2Controller
{
	public function __construct()
	{
		parent::__construct("optionConfig");
	}
	
	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("OptionConfig");

		parent::setControllerPageTitleId("page_title_option_config");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			false);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);

		$this->LAGridRights->overrideInitRights("export_xls",		false);
		$this->LAGridRights->overrideInitRights("export_xlsx",		false);
		$this->LAGridRights->overrideInitRights("export_pdf_node",	false);
		$this->LAGridRights->overrideInitRights("init_open_search",	false);

		$this->LAGridDB->enableSQLMode();

		$SQL ="
			SELECT
				oc.row_id,
				d.`dict_value` as name,
				`type`
			FROM `option_config` oc
			LEFT JOIN `dictionary` d ON
					d.`dict_id`=option_id
				AND d.`valid`=1
				AND d.`lang`='".Dict::getLang()."'
			WHERE
					oc.`status`=".Status::PUBLISHED."
			ORDER BY option_id
			";

		$this->LAGridDB->setSQLSelection($SQL, "row_id");
		parent::G2BInit();
	}
	
	public function columns()
	{
		$types=array(array('id' => OptionConfig::TYPE_ED, 'value' => Dict::getValue("filling_out")),
					array('id' => OptionConfig::TYPE_COMBO, 'value' => Dict::getValue("drop_down")));
		
		return array(
			'name'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 300, 'edit'=>false),
			'type'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'combo','width' => 300,
				'options'		=>	array('mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,'array'=>$types)),
		);
	}

	public function attributeLabels()
	{
		return array(
			'name' => Dict::getValue("name"),
			'type' => Dict::getValue("type"),
		);
	}
}
?>
