<?php #yii2: done

'yii2-only`;

	namespace app\controllers;
	use <PERSON>;

`/yii2-only';


trait EmployeeControllerData
{
	protected function fetchGridData($filter, $isExport = false, $excelExport = false, $csvString = false) {
		$gridID = requestParam('gridID');

		$data = parent::fetchGridData($filter);
		Yang::setSessionValue('employeeControllerFilter', $filter);

		if ($this->employeeDemoMode) {
			$data = $this->generateFakeData($data);
		}

		return $data;
	}
}