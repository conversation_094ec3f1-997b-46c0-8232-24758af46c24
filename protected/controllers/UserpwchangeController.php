<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\Dict;
	use Yang;

`/yii2-only';


#yii2: done

class UserpwchangeController extends GridController
{
	public $layout = '//layouts/main';

	public function __construct()
	{
		parent::__construct("userpwchange");
		$this->setModelName("User");
//		parent::setTitle(Dict::getValue("page_title_user_management"));
//		parent::enableSubgrid(true);
//		parent::setRights(/*add*/false,/*mod*/true,/*imod*/false,/*del*/false,/*exp*/false,/*$sel*/false,/*$msel*/false,/*$details*/false);
//		parent::exportSettings(Dict::getValue("export_file_user_management"));

        $js = '
            function amdCallback()
            {
                var timeout = 150;

                if ($("#personalMenu").css("visibility") === "visible")
                {
                    $("#personalMenu").fadeOut(timeout);
                    setTimeout(function () {
                        $("#personalMenu").css("visibility","hidden");
                    }, timeout);
                }
            }';
        $this->setMoreJavaScript($js);
	}

	public function getColumns()
	{
		return array(
			'password' => array('window'=>true, 'col_type'=>'pw', 'options'=>array('prev_password'=>true)),
		);
	}
}
?>
