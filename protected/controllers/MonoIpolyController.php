<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\MyActiveForm;
	use app\models\Employee;
	use app\models\EmployeeCard;
	use app\models\EmployeeContract;
	use app\models\Status;
	use Yang;

`/yii2-only';


class MonoIpolyController extends Controller
{
	// MONO Ipolyfabric
	public function actionAddEmployee() {
		$resp = [
            "status" => 1,
		];

        $created_by = "MonoIpoly/AddEmployee";
        $created_on = date("Y-m-d H:i:s");

		$emp_id    = requestParam('emp_id');
		$card_id   = requestParam('card_id');
		$first_name = requestParam('first_name');
		$last_name  = requestParam('last_name');
		$valid_from = requestParam('valid_from');
		$valid_to  = requestParam('valid_to');

        if (empty($emp_id) || empty($card_id) || empty($first_name) || empty($last_name) || empty($valid_from)) {
            $resp = [
                "status" => 0,
                "msg" => "param_error",
            ];

	    	echo json_encode($resp);

            return false;
        }

        if (empty($valid_to)) {
            $valid_to = App::getSetting("defaultEnd");
        }

        if (!$this->validateDate($valid_from) || !$this->validateDate($valid_to)) {
            $resp = [
                "status" => 0,
                "msg" => "date_format",
            ];

	    	echo json_encode($resp);

            return false;
        }

        $employee_id = md5($emp_id.$valid_from.$valid_to.$first_name.$last_name.date("YmdHis"));
        $employee_contract_id = $employee_id;

        $e = new Employee;
        $e->employee_id = $employee_id;
        $e->company_id = 1;
        $e->emp_id = $emp_id;
        $e->first_name = $first_name;
        $e->last_name = $last_name;
        $e->valid_from = $valid_from;
        $e->valid_to = $valid_to;
        $e->status = 2;
        $e->created_by = $created_by;
        $e->created_on = $created_on;

		$e_valid = $e->validate();

        $ec = new EmployeeContract;
        $ec->employee_contract_id = $employee_contract_id;
        $ec->employee_id = $employee_id;
        $ec->employee_contract_number = 1;
        $ec->ec_valid_from = $valid_from;
        $ec->ec_valid_to = $valid_to;
        $ec->valid_from = $valid_from;
        $ec->valid_to = $valid_to;
        $ec->status = 2;
        $ec->created_by = $created_by;
        $ec->created_on = $created_on;

        $ec_valid = $ec->validate();

        $ecard = new EmployeeCard;
        $ecard->employee_contract_id = $employee_contract_id;
        $ecard->card = $card_id;
        $ecard->valid_from = $valid_from;
        $ecard->valid_to = $valid_to;
        $ecard->status = 2;
        $ecard->acl = "001";    //Dolgozói
        $ecard->created_by = $created_by;
        $ecard->created_on = $created_on;

        $ecard_valid = $ecard->validate();

        if ($e_valid && $ec_valid && $ecard_valid) {
            $e->save();
            $ec->save();
            $ecard->save();
		} else {
            $errors = "";

            if (!$e_valid) {
			    $errors .= $this->getErrors($e);
            }
            if (!$ec_valid) {
			    $errors .= $this->getErrors($ec);
            }
            if (!$ecard_valid) {
			    $errors .= $this->getErrors($ecard);
            }
			
			$display_errors = "";

			if (strpos($errors, "error_interval_collision") !== \FALSE) {
				$display_errors .= strlen($display_errors) ? ";" : "";
				$display_errors .= "interval_error";
			}

			if (strpos($errors, "error_card_use") !== \FALSE) {
				$display_errors .= strlen($display_errors) ? ";" : "";
				$display_errors .= "card_duplicate";
			}
           
            $resp = [
                "status" => 0,
                "msg" => $display_errors,
            ];
		}

		echo json_encode($resp);
	}

	public function actionModifyEmployee() {
		$resp = [
            "status" => 1,
		];

        $modified_by = "MonoIpoly/ModEmployee";
        $modified_on = date("Y-m-d H:i:s");

		$emp_id        = requestParam('emp_id');
		$new_emp_id    = requestParam('new_emp_id');
		$new_card_id   = requestParam('new_card_id');
		$new_first_name = requestParam('new_first_name');
		$new_last_name  = requestParam('new_last_name');
		$new_valid_from = requestParam('new_valid_from');
		$new_valid_to  = requestParam('new_valid_to');

        if (empty($emp_id) || empty($new_valid_from)) {
            $resp = [
                "status" => 0,
                "msg" => "param_error",
            ];

	    	echo json_encode($resp);

            return false;
        }

        if (empty($new_valid_to)) {
            $new_valid_to = App::getSetting("defaultEnd");
        }

        if (!$this->validateDate($new_valid_from)) {
            $resp = [
                "status" => 0,
                "msg" => "date_format",
            ];

	    	echo json_encode($resp);

            return false;
        }

        $SQL = "
            SELECT
                e.`row_id` AS e_row_id,
                ec.`row_id` AS ec_row_id,
                ecard.`row_id` AS ecard_row_id,
                e.`employee_id`,
                ec.`employee_contract_id`
            FROM
                `employee` e
			LEFT JOIN
                `employee_contract` ec ON 
					ec.`status` = ".Status::PUBLISHED." 
				AND ec.`employee_id` = e.`employee_id`
				AND '$new_valid_from' BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '".App::getSetting("defaultEnd")."')
				AND '$new_valid_from' BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '".App::getSetting("defaultEnd")."')
            LEFT JOIN
                `employee_card` ecard ON
                    ecard.`status` = ".Status::PUBLISHED." 
 				AND ecard.`employee_contract_id` = ec.`employee_contract_id`
 				AND '$new_valid_from' BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`, '".App::getSetting("defaultEnd")."')
            WHERE
                e.`emp_id` = '$emp_id'
				    AND e.`status` = ".Status::PUBLISHED." 
				    AND '$new_valid_from' BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."')
            LIMIT 1
        ";

        $results = dbFetchAll($SQL);

        if (isset($results[0])) {
            $before_new_valid_from = date('Y-m-d', strtotime('-1 day', strtotime($new_valid_from)));

            if (!empty($results[0]["e_row_id"])) {
                $employee = Employee::model()->findByPk($results[0]["e_row_id"]);
				$old_valid_to = !empty($employee->valid_to)?$employee->valid_to:App::getSetting("defaultEnd");
                $employee->valid_to = $before_new_valid_from;
                if ($employee->validate()) {
                    $employee->save();
                } else if (strtotime($employee->valid_to) < strtotime($employee->valid_from)) {
					$employee->valid_to = $old_valid_to;
					$employee->status = Status::DELETED;
					$employee->clearErrors();
					$employee->clearGenericErrors();

					if ($employee->validate()) {
						$employee->save();
					}
				}
            }
            if (!empty($results[0]["ec_row_id"])) {
                $employee_contract = EmployeeContract::model()->findByPk($results[0]["ec_row_id"]);
				$old_valid_to = !empty($employee_contract->valid_to)?$employee_contract->valid_to:App::getSetting("defaultEnd");
                $employee_contract->valid_to = $before_new_valid_from;
                if ($employee_contract->validate()) {
                    $employee_contract->save();
                } else if (strtotime($employee_contract->valid_to) < strtotime($employee_contract->valid_from)) {
					$employee_contract->valid_to = $old_valid_to;
					$employee_contract->status = Status::DELETED;
					$employee_contract->clearErrors();
					$employee_contract->clearGenericErrors();

					if ($employee_contract->validate()) {
						$employee_contract->save();
					}
				}
            }
            if (!empty($results[0]["ecard_row_id"])) {
                $employee_card = EmployeeCard::model()->findByPk($results[0]["ecard_row_id"]);
				$old_valid_to = !empty($employee_card->valid_to)?$employee_card->valid_to:App::getSetting("defaultEnd");
                $employee_card->valid_to = $before_new_valid_from;
                if ($employee_card->validate()) {
                    $employee_card->save();
                } else if (strtotime($employee_card->valid_to) < strtotime($employee_card->valid_from)) {
					$employee_card->valid_to = $old_valid_to;
					$employee_card->status = Status::DELETED;
					$employee_card->clearErrors();
					$employee_card->clearGenericErrors();

					if ($employee_card->validate()) {
						$employee_card->save();
					} else {
						$err = $this->getErrors($employee_card);
					}
				}
            }

            $e_valid = $ec_valid = $ecard_valid = true;

            if (!empty($results[0]["e_row_id"])) {
                $employee                       = Employee::model()->findByPk($results[0]["e_row_id"]);
                $employee->isNewRecord          = true;
                $employee->row_id               = null;
                if (!empty($new_emp_id)) {
                    $employee->emp_id               = $new_emp_id;
                }
                if (!empty($new_first_name)) {
                    $employee->first_name           = $new_first_name;
                }
                if (!empty($new_last_name)) {
                    $employee->last_name            = $new_last_name;
                }
                $employee->valid_from           = $new_valid_from;
                $employee->valid_to             = $new_valid_to;
				$employee->status = Status::PUBLISHED;
                $e_valid = $employee->validate();
            }

            if (!empty($results[0]["ec_row_id"])) {
                $employee_contract              = EmployeeContract::model()->findByPk($results[0]["ec_row_id"]);
                $employee_contract->isNewRecord = true;
                $employee_contract->row_id      = null;
                $employee_contract->valid_from  = $new_valid_from;
                $employee_contract->valid_to    = $new_valid_to;
                $employee_contract->ec_valid_from  = $new_valid_from;
                $employee_contract->ec_valid_to    = $new_valid_to;
 				$employee_contract->status = Status::PUBLISHED;
                $ec_valid = $employee_contract->validate();
            }

            if (!empty($results[0]["ecard_row_id"])) {
                $employee_card                  = EmployeeCard::model()->findByPk($results[0]["ecard_row_id"]);
                $employee_card->isNewRecord     = true;
                $employee_card->row_id          = null;
                $employee_card->valid_from      = $new_valid_from;
                $employee_card->valid_to        = $new_valid_to;
 				$employee_card->status = Status::PUBLISHED;
                if (!empty($new_card_id)) {
                    $employee_card->card            = $new_card_id;
                }
                $ecard_valid = $employee_card->validate();
            }

            if (isset($employee) && isset($employee_contract) && isset($employee_card) && $e_valid && $ec_valid && $ecard_valid) {
                $employee->save();
                $employee_contract->save();
                $employee_card->save();
            } else {
                $errors = "";

                if (isset($employee) && !$e_valid) {
                    $errors .= $this->getErrors($employee);
                }
                if (isset($employee_contract) && !$ec_valid) {
                    $errors .= $this->getErrors($employee_contract);
                }
                if (isset($employee_card) && !$ecard_valid) {
                    $errors .= $this->getErrors($employee_card);
                }

				$display_errors = "";

				if (strpos($errors, "error_interval_collision") !== \FALSE) {
					$display_errors .= strlen($display_errors) ? ";" : "";
					$display_errors .= "interval_error";
				}

				if (strpos($errors, "error_card_use") !== \FALSE) {
					$display_errors .= strlen($display_errors) ? ";" : "";
					$display_errors .= "card_duplicate";
				}

                $resp = [
                    "status" => 0,
                    "msg" => $display_errors,
                ];

                echo json_encode($resp);

                return false;
            }
        }

        $resp = [
            "status" => 1,
        ];

        echo json_encode($resp);
    }

	public function actionDeleteEmployee() {
		$resp = [
            "status" => 1,
		];

        $emp_id    = requestParam('emp_id');
		$lock_date     = requestParam('lock_date');

        if (empty($emp_id)) {
            $resp = [
                "status" => 0,
                "msg" => "param_error",
            ];

	    	echo json_encode($resp);

            return false;
        }

        /*if (!$this->validateDate($lock_date)) {
            $resp = [
                "status" => 0,
                "msg" => "date_format",
            ];

	    	echo json_encode($resp);

            return false;
        }*/

        $SQL = "
            SELECT
                e.`row_id` AS e_row_id,
                ec.`row_id` AS ec_row_id,
                ecard.`row_id` AS ecard_row_id
            FROM
                `employee` e
			LEFT JOIN
                `employee_contract` ec ON 
					ec.`status` = ".Status::PUBLISHED." 
				AND ec.`employee_id` = e.`employee_id`

				AND (
					(ec.`valid_from` BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."'))
						OR
					(ec.`valid_to` BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."'))
						OR
					(e.`valid_from` BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '".App::getSetting("defaultEnd")."') AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."') BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '".App::getSetting("defaultEnd")."'))
				)

				AND (
					(ec.`ec_valid_from` BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."'))
						OR
					(ec.`ec_valid_to` BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."'))
						OR
					(e.`valid_from` BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '".App::getSetting("defaultEnd")."') AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."') BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '".App::getSetting("defaultEnd")."'))
				)
            LEFT JOIN
                `employee_card` ecard ON
                    ecard.`status` = ".Status::PUBLISHED." 
 				AND ecard.`employee_contract_id` = ec.`employee_contract_id`

				AND (
					(ecard.`valid_from` BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."'))
						OR
					(ecard.`valid_to` BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."'))
						OR
					(e.`valid_from` BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`, '".App::getSetting("defaultEnd")."') AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."') BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`, '".App::getSetting("defaultEnd")."'))
				)
            WHERE
                e.`emp_id` = '$emp_id'
				    AND e.`status` = ".Status::PUBLISHED." 
				    /*AND NOW() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."')
            LIMIT 1*/
        ";

        $results = dbFetchAll($SQL);

		foreach ($results as $res) {
			Employee::model()->updateAll(['status' => Status::DELETED,], "`row_id` = '".$res["e_row_id"]."'");
			EmployeeContract::model()->updateAll(['status' => Status::DELETED,], "`row_id` = '".$res["ec_row_id"]."'");
			EmployeeCard::model()->updateAll(['status' => Status::DELETED,], "`row_id` = '".$res["ecard_row_id"]."'");
		}

        /*if (isset($results[0])) {
            if (!empty($results[0]["e_row_id"])) {
                Employee::model()->updateAll(['valid_to' => $lock_date,], "`row_id` = '".$results[0]["e_row_id"]."'");
            }
            if (!empty($results[0]["ec_row_id"])) {
                EmployeeContract::model()->updateAll(['valid_to' => $lock_date,], "`row_id` = '".$results[0]["ec_row_id"]."'");
            }
            if (!empty($results[0]["ecard_row_id"])) {
                EmployeeCard::model()->updateAll(['valid_to' => $lock_date,], "`row_id` = '".$results[0]["ecard_row_id"]."'");
            }
        }*/

        echo json_encode($resp);
	}

    public function actionGetEmployee() {
		$resp = [
            "status" => 1,
		];

		$emp_id    = requestParam('emp_id');
		$date      = requestParam('date');

        if (empty($emp_id)) {
            $resp = [
                "status" => 0,
                "msg" => "param_error",
            ];

	    	echo json_encode($resp);

            return false;
        }

        if (empty($date)) {
            $date = date('Y-m-d');
        }

        if (!$this->validateDate($date)) {
            $resp = [
                "status" => 0,
                "msg" => "date_format",
            ];

	    	echo json_encode($resp);

            return false;
        }

        $SQL = "
            SELECT
                e.`row_id` AS e_row_id,
                ec.`row_id` AS ec_row_id,
                ecard.`row_id` AS ecard_row_id,

                e.`emp_id`,
                ecard.`card` AS card_number,
                e.`valid_from` AS emp_valid_from,
                IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."') AS emp_valid_to,
                e.`first_name`,
                e.`last_name`,
                ec.`valid_from` AS contr_valid_from,
                IFNULL(ec.`valid_to`, '".App::getSetting("defaultEnd")."') AS contr_valid_to,
                ecard.`valid_from` AS card_valid_from,
                IFNULL(ecard.`valid_to`, '".App::getSetting("defaultEnd")."') AS card_valid_to
            FROM
                `employee` e
			LEFT JOIN
                `employee_contract` ec ON 
					ec.`status` = ".Status::PUBLISHED." 
				AND ec.`employee_id` = e.`employee_id`
				AND '$date' BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '".App::getSetting("defaultEnd")."')
				AND '$date' BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '".App::getSetting("defaultEnd")."')
            LEFT JOIN
                `employee_card` ecard ON
                    ecard.`status` = ".Status::PUBLISHED." 
 				AND ecard.`employee_contract_id` = ec.`employee_contract_id`
 				AND '$date' BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`, '".App::getSetting("defaultEnd")."')
            WHERE
                e.`emp_id` = '$emp_id'
				    AND e.`status` = ".Status::PUBLISHED." 
				    AND '$date' BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."')
            LIMIT 1
        ";

        $results = dbFetchAll($SQL);

        if (isset($results[0])) {
            $resp = [
                "status"        => 1,
                "emp_id"        => $results[0]["emp_id"],
                "card_number"   => $results[0]["card_number"],
                "first_name"    => $results[0]["first_name"],
                "last_name"     => $results[0]["last_name"],
                "emp_valid_from"  => $results[0]["emp_valid_from"],
                "emp_valid_to"    => $results[0]["emp_valid_to"],
                "contr_valid_from"  => $results[0]["contr_valid_from"],
                "contr_valid_to"    => $results[0]["contr_valid_to"],
                "card_valid_from"  => $results[0]["card_valid_from"],
                "card_valid_to"    => $results[0]["card_valid_to"],
            ];
        } else {
			$SQL = "
				SELECT
					e.`row_id` AS e_row_id,
					ec.`row_id` AS ec_row_id,
					ecard.`row_id` AS ecard_row_id,

					e.`emp_id`,
					ecard.`card` AS card_number,
					e.`valid_from` AS emp_valid_from,
					IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."') AS emp_valid_to,
					e.`first_name`,
					e.`last_name`,
					ec.`valid_from` AS contr_valid_from,
					IFNULL(ec.`valid_to`, '".App::getSetting("defaultEnd")."') AS contr_valid_to,
					ecard.`valid_from` AS card_valid_from,
					IFNULL(ecard.`valid_to`, '".App::getSetting("defaultEnd")."') AS card_valid_to
				FROM
					`employee` e
				LEFT JOIN
					`employee_contract` ec ON 
						ec.`status` = ".Status::PUBLISHED." 
					AND ec.`employee_id` = e.`employee_id`

					AND (
						(ec.`valid_from` BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."'))
							OR
						(ec.`valid_to` BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."'))
							OR
						(e.`valid_from` BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '".App::getSetting("defaultEnd")."') AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."') BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '".App::getSetting("defaultEnd")."'))
					)

					AND (
						(ec.`ec_valid_from` BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."'))
							OR
						(ec.`ec_valid_to` BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."'))
							OR
						(e.`valid_from` BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '".App::getSetting("defaultEnd")."') AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."') BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '".App::getSetting("defaultEnd")."'))
					)
				LEFT JOIN
					`employee_card` ecard ON
						ecard.`status` = ".Status::PUBLISHED." 
					AND ecard.`employee_contract_id` = ec.`employee_contract_id`

					AND (
						(ecard.`valid_from` BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."'))
							OR
						(ecard.`valid_to` BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."'))
							OR
						(e.`valid_from` BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`, '".App::getSetting("defaultEnd")."') AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."') BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`, '".App::getSetting("defaultEnd")."'))
					)
				WHERE
					e.`emp_id` = '$emp_id'
						AND e.`status` = ".Status::PUBLISHED."
				ORDER BY
					e.`valid_from`,
						ec.`valid_from`,
							ecard.`valid_from`
				LIMIT 1
			";

			$results = dbFetchAll($SQL);

			if (isset($results[0])) {
				$resp = [
					"status"        => 0,
					"msg"			=> "employee_expired",
					"emp_id"        => $results[0]["emp_id"],
					"card_number"   => $results[0]["card_number"],
					"first_name"    => $results[0]["first_name"],
					"last_name"     => $results[0]["last_name"],
					"emp_valid_from"  => $results[0]["emp_valid_from"],
					"emp_valid_to"    => $results[0]["emp_valid_to"],
					"contr_valid_from"  => $results[0]["contr_valid_from"],
					"contr_valid_to"    => $results[0]["contr_valid_to"],
					"card_valid_from"  => $results[0]["card_valid_from"],
					"card_valid_to"    => $results[0]["card_valid_to"],
				];
			} else {
				$resp = [
					"status" => 0,
					"msg" => "missing_employee",
				];
			}
        }

        echo json_encode($resp);
    }

    private function getErrors($m) {
        $error = MyActiveForm::_validate($m, true, true);

		$arr = (array) json_decode($error);
		$msg = "";

		foreach ($arr as $key => $value) {
			$msg .= $key . ":";
			foreach ($value as $val) {
				$msg .= $val . ";";
			}
		}

        return $msg;
    }

    private function validateDate($date, $format = 'Y-m-d') {
        $d = DateTime::createFromFormat($format, $date);
        return $d && $d->format($format) == $date;
    }
}