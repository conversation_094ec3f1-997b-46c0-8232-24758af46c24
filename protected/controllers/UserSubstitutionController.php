<?php

class UserSubstitutionController extends Grid2Controller
{
	private $defaultEnd;
	private $statusPublished = Status::PUBLISHED;

	public function __construct() {
		parent::__construct("userSubstitution");
		$this->defaultEnd = App::getSetting("defaultEnd");
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("UserSubstitution");
		parent::setControllerPageTitleId("page_title_user_substitution");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);
		$this->LAGridRights->overrideInitRights("export_xls",		false);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);
		$this->LAGridRights->overrideInitRights("export_pdf_node",	false);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);

		$this->LAGridDB->enableSQLMode();
		$this->LAGridDB->setSQLSelection($this->getDataSQL(), "row_id");
		parent::setExportFileName(Dict::getValue("page_title_user_substitution"));
		parent::G2BInit();
	}

	/**
	 * Kereső értékek átadása sessionnek az oszlopok felépítéséhez
	 * @return void
	 */
	public function actionSetInitProperties() {
		Yang::setSessionValue('user_sub_filters', requestParam('searchInput'));
		parent::actionSetInitProperties();
	}


	/**
	 * Grid adat tartalmat lekérő SQL
	 * @return string
	 */
	private function getDataSQL()
	{
		$SQL = "
			SELECT
				`row_id`,
				`user_id`,
				`user_id` AS grid_user_id,
				`note`,
				`valid_from`,
				IFNULL(`valid_to`, '{$this->defaultEnd}') AS valid_to
			FROM `user_substitution`
			WHERE
					`status`= {$this->statusPublished}
				AND `user_substitution_id`= '" . userID() . "'
				AND '{date}' BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->defaultEnd}')
		";
		return $SQL;
	}

	public function search()
	{
		return [
			'date' =>
			[
				'export'		=> true,
				'report_width'	=> 20,
				'dPicker'		=> true,
				'col_type'		=> 'ed',
				'default_value'	=> date('Y-m-d'),
				'label_text'	=> Dict::getValue("date")
			],
			'submit' => ['col_type' => 'searchBarReinitGrid', 'gridID' => 'dhtmlxGrid', 'width' => '*', 'label_text' => ''],
		];
	}

	public function columns()
	{
		$filters = Yang::session('user_sub_filters', []);
		if (!isset($filters["date"])) { return []; }
		return
		[
			'grid_user_id'	=> [
				'export'		=> true,
				'grid'			=> true,
				'window'		=> false,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width'			=> 250,
				'edit'			=> false,
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> $this->getUserDropdownSQL($filters["date"], false)
				]
			],
			'user_id' 		=> [
				'export'		=> false,
				'grid'			=> false,
				'window'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width'			=> 250,
				'edit'			=> false,
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> $this->getUserDropdownSQL($filters["date"]),
					'array'	=> [["id" => "", "value" => ""]]
				]
			],
			'note'			=> ['export' => true, 'report_width' => 20, 'col_type' => 'ed', 'width' => 250],
			'valid_from'	=> ['export' => true, 'report_width' => 20, 'onchange' => ['user_id'], 'dPicker' => true, 'col_type' => 'ed', 'width' => 150],
			'valid_to'		=> ['export' => true, 'report_width' => 20, 'onchange' => ['user_id'], 'dPicker' => true, 'col_type' => 'ed', 'width' => 150, 'default_value' => date("Y-m-d")]
		];
	}

	/**
	 * Visszaadja a user dropdown SQL-t
	 * @param string $date
	 * @param boolean $onchange
	 * @return string
	 */
	private function getUserDropdownSQL($date, $onchange = true)
	{
		// Company jog figyelés (Velux kérés csak olyan cégből választhat helyettest amihez joga van DEV-14660)
		$useCompanyAndPayrollRights = (int)App::getSetting("useCompanyAndPayrollRights");
		if ($useCompanyAndPayrollRights) {
			$art		= new ApproverRelatedGroup;
			$gargSQL	= $art->getApproverReleatedGroupSQL("Company", ["companyMainData"], userID(), "'" . $date . "'", "AND", "allDate");
			$gargSQL	= $gargSQL["where"] ?? "";
		} else {
			$gargSQL	= "";
		}

		// Onchange
		if ($onchange) {
			$onchangeSQL = "
				AND ('{valid_from}' BETWEEN u2.`valid_from` AND IFNULL(u2.`valid_to`, '{$this->defaultEnd}') OR '{valid_to}' BETWEEN u2.`valid_from` AND IFNULL(u2.`valid_to`, '{$this->defaultEnd}'))
			";
		} else {
			$onchangeSQL = "";
		}

		$SQL = "
			SELECT
				u2.`user_id` AS id,
				IF(" . Employee::getParam('fullname_with_emp_id_ec_id', ["e", "ec"]) . " is NULL, u2.`username`, " . Employee::getParam('fullname_with_emp_id_ec_id', ["e", "ec"]) . ") AS value
			FROM `user` u
			LEFT JOIN `user_substitution_rolegroup` usr ON
					usr.`rolegroup_substitution_id` = u.`rolegroup_id`
				AND usr.`status` = {$this->statusPublished}
				AND '{$date}' BETWEEN usr.`valid_from` AND IFNULL(usr.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `user` u2 ON
					(u2.`rolegroup_id` = usr.`rolegroup_id` OR u.`rolegroup_id` = 'a5b6bd79e008725744118c7c46e10cda')
				AND u2.`status` = {$this->statusPublished}
				AND '{$date}' BETWEEN u2.`valid_from` AND IFNULL(u2.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `employee` e ON
					e.`employee_id` = u2.`employee_id`
				AND e.`status`= {$this->statusPublished}
				AND '{$date}' BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `employee_contract` ec ON
					ec.`employee_id` = e.`employee_id`
				AND ec.`status` = {$this->statusPublished}
				AND '{$date}' BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '{$this->defaultEnd}')
				AND '{$date}' BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `company` ON
					`company`.`company_id` = e.`company_id`
				AND `company`.`status` = {$this->statusPublished}
				AND '{$date}' BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$this->defaultEnd}')
			WHERE
					u.`status` = {$this->statusPublished}
				{$gargSQL}
				AND '{$date}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$this->defaultEnd}')
				AND u.`user_id` = '" . userID() . "'
				{$onchangeSQL}
			ORDER BY value
		";

		return $SQL;
	}

	public function attributeLabels()
	{
		return [
			'user_id'		=> Dict::getValue("user"),
			'grid_user_id'	=> Dict::getValue("user"),
			'note'			=> Dict::getValue("note"),
			'valid_from'	=> Dict::getValue("valid_from"),
			'valid_to'		=> Dict::getValue("valid_to")
		];
	}
}
?>