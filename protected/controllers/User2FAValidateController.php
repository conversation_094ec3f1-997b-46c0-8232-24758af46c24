<?php

class User2FAValidateController extends Grid2Controller
{
	public function __construct() {
		parent::__construct("user2FAValidate");
	}

	protected function G2BInit() :void {
		$this->LAGridDB->setModelName("User");
	}

	public function columns() :array
	{
		return [
            'twoFactorKey'              => ['grid' => false, 'export' => false, 'col_type' => 'ed'],
            'qr_form_username'          => ['grid' => false, 'export' => false, 'col_type' => 'hidden', 'default_value' => Yang::session('qr_form_user', '')],
            'qr_form_pass'              => ['grid' => false, 'export' => false, 'col_type' => 'hidden', 'default_value' => Yang::session('qr_form_pass', '')]
		];
	}

    public function attributeLabels() :array
    {
        return [
            'twoFactorKey' => Dict::getValue("google_two_fa_secret_key")
        ];
    }

    /**
     * Belépés
     * @param array $data
     * @param string $modelName
     * @param string $pk
     * @param boolean $vOnly
     * @param boolean $ret
     * @param string $contentId
     * @return void
     */
    public function actionSave($data = array(), $modelName = null, $pk = null, $vOnly = false, $ret = false, $contentId = null) :void
    {
        $logrc          = new ReflectionClass("LoginController");
        $log            = $logrc->newInstanceWithoutConstructor();
        $input          = requestParam("dialogInput_dhtmlxGrid") ?? [];
        $login          = new LoginForm;
		$login->ttwaun  = $input["qr_form_username"] ?? "";
		$login->ttwapw  = $input["qr_form_pass"] ?? "";
        $login->setTwoFactorKey(($input["twoFactorKey"] ?? ""));
		$log->validateAndLoginUser($login, "2FA");
    }
}