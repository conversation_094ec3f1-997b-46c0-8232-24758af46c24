<?php

class RolegroupSubstitutionController extends Grid2Controller
{
	private $defaultEnd;
	private $statusPublished = Status::PUBLISHED;
	private $useCompanyAndPayrollRights;

	public function __construct() {
		parent::__construct("rolegroupSubstitution");
		$this->defaultEnd = App::getSetting("defaultEnd");
		$this->useCompanyAndPayrollRights = (int)App::getSetting("useCompanyAndPayrollRights");
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("UserSubstitutionRolegroup");
		parent::setControllerPageTitleId("page_title_rolegroup_substitution");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);
		$this->LAGridRights->overrideInitRights("export_xls",		false);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);
		$this->LAGridRights->overrideInitRights("export_pdf_node",	false);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);

		$this->LAGridDB->enableSQLMode();

		if ($this->useCompanyAndPayrollRights) {
			$art = new ApproverRelatedGroup;
			$gargSQL = $art->getApproverReleatedGroupSQL("AuthRolegroup", ["companyMainData"], userID(), "CURDATE()", "AND", "CurrentDate");
		} else {
			$gargSQL = ["where" => ""];
		}

		$SQL ="
			SELECT
				`user_substitution_rolegroup`.`row_id`,
				`user_substitution_rolegroup`.`rolegroup_substitution_id`,
				`user_substitution_rolegroup`.`rolegroup_id`,
				`user_substitution_rolegroup`.`note`,
				`user_substitution_rolegroup`.`valid_from`,
				`user_substitution_rolegroup`.`valid_to`
			FROM `user_substitution_rolegroup`
			LEFT JOIN `auth_rolegroup` ON
					`auth_rolegroup`.`rolegroup_id` = `user_substitution_rolegroup`.`rolegroup_id`
				AND `auth_rolegroup`.`visibility` = 1
			WHERE
					`user_substitution_rolegroup`.`status` = {$this->statusPublished}
				AND '{date}' BETWEEN `user_substitution_rolegroup`.`valid_from` AND IFNULL(`user_substitution_rolegroup`.`valid_to`, '{$this->defaultEnd}')
				AND (`user_substitution_rolegroup`.`rolegroup_substitution_id` = '{rolegroup_substitution_id}' OR '{rolegroup_substitution_id}' = 'ALL')
				AND (`user_substitution_rolegroup`.`rolegroup_id` = '{rolegroup_id}' OR '{rolegroup_id}'='ALL')
				{$gargSQL["where"]}
		";

		$this->LAGridDB->setSQLSelection($SQL, "row_id");
		parent::G2BInit();
	}

	public function search()
	{
		if ($this->useCompanyAndPayrollRights) {
			$art = new ApproverRelatedGroup;
			$gargSQL = $art->getApproverReleatedGroupSQL("AuthRolegroup", ["companyMainData"], userID(), "CURDATE()", "AND", "CurrentDate");
		} else {
			$gargSQL = ["where" => ""];
		}

		return
		[
			'date'						=> [
				'export'		=> true,
				'report_width' 	=> 20,
				'dPicker'		=> true,
				'col_type'		=>'ed',
				'default_value'	=> date('Y-m-d'),
				'label_text'	=> Dict::getValue("date")
			],
			'rolegroup_substitution_id'	=> [
				'col_type'		=> 'combo',
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							`rolegroup_id` AS id,
							`rolegroup_name` AS value
						FROM `auth_rolegroup`
						WHERE `visibility` = 1 {$gargSQL["where"]}
						ORDER BY value
					",
					'array'	=> [["id" => "ALL", "value" => Dict::getValue("all")]]
				],
				'label_text'	=> Dict::getValue("rolegroup"),
				'default_value'	=>''
			],
			'rolegroup_id'				=> [
				'col_type'		=> 'combo',
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							`rolegroup_id` AS id,
							`rolegroup_name` AS value
						FROM `auth_rolegroup`
						WHERE `visibility` = 1 {$gargSQL["where"]}
						ORDER BY value
					",
					'array'	=> [["id" => "ALL", "value" => Dict::getValue("all")]]
				],
				'label_text'	=> Dict::getValue("substitute"),
				'default_value'	=> ''
			],
			'submit'					=> ['col_type' => 'searchBarReinitGrid', 'gridID' => 'dhtmlxGrid', 'width' => '*', 'label_text' => '']
		];
	}

	public function columns()
	{
		if ($this->useCompanyAndPayrollRights) {
			$art = new ApproverRelatedGroup;
			$gargSQL = $art->getApproverReleatedGroupSQL("AuthRolegroup", ["companyMainData"], userID(), "CURDATE()", "AND", "CurrentDate");
		} else {
			$gargSQL = ["where" => ""];
		}

		$ret =
		[
			'rolegroup_substitution_id'	=> [
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width' 		=> 250,
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							`rolegroup_id` AS id,
							`rolegroup_name` AS value
						FROM `auth_rolegroup`
						WHERE `visibility` = 1 {$gargSQL["where"]}
						ORDER BY value
					",
					'array'	=> [["id" => "", "value" =>""]]
				]
			],
			'rolegroup_id' => [
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width'			=> 250,
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							`rolegroup_id` AS id,
							`rolegroup_name` AS value
						FROM `auth_rolegroup`
						WHERE `visibility` = 1 {$gargSQL["where"]}
						ORDER BY value
					",
					'array'	=> [["id" => "", "value" => ""]]
				]
			],
			'note'		=> ['export' => true, 'report_width' => 20, 'col_type' => 'ed', 'width' => 250],
			'valid_from'=> ['export' => true, 'report_width' => 20, 'dPicker' => true, 'col_type' => 'ed', 'width' => 150],
			'valid_to'	=> ['export' => true, 'report_width' => 20, 'dPicker' => true, 'col_type' => 'ed', 'width' => 150]
		];

		return $ret;
	}

	public function attributeLabels()
	{
		return [
			'rolegroup_substitution_id'	=> Dict::getValue("rolegroup"),
			'rolegroup_id'				=> Dict::getValue("substitute"),
			'note'						=> Dict::getValue("note"),
			'valid_from'				=> Dict::getValue("valid_from"),
			'valid_to'					=> Dict::getValue("valid_to")
		];
	}
}
?>
