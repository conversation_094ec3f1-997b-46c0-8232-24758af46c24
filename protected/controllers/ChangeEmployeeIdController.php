<?php

'yii2-only`;

	namespace app\controllers;
	use <PERSON>;

`/yii2-only';


#yii2: done

ini_set('max_execution_time', 2400);
ini_set("display_errors", 1);

define('DS', DIRECTORY_SEPARATOR);

// adatbázis amelyiken végezzűk a műveletet
define('DATABASE', 'patikap_stage_ttwa');


class ChangeEmployeeIdController extends Controller{

    private $processing_error = false;
    private $full_error_log; // összes hiba.. ha nem üres a végén akkor megy az email küldés
    private $error_log_file; // ebbe a fájlba mentjük a hibákat
    private $data = [];


    public function __construct(){
        
        if(!is_dir(Yang::getAlias('webroot') . DS . "xlsImport" . DS . "eicef")){
            $this->checkDir(Yang::getAlias('webroot'). DS . "xlsImport" . DS . "eicef");
        }
        
        $this->error_log_file = Yang::getAlias('webroot') . DS . "xlsImport". DS . "eicef" . DS . "EmployeeIdChangeErrorFile_" . date("Ymd") . ".txt";;

        $this->data = [  
                        ['old' => '18314084425','new' => '8314084425'],
                        ['old' => '18352571753','new' => '8352571753'],
                        ['old' => '18355181417','new' => '8355181417'],
                        ['old' => '18358922477','new' => '8358922477'],
                        ['old' => '18381324556','new' => '8381324556'],
                        ['old' => '18418200812','new' => '8418200812'],
                        ['old' => '18430303499','new' => '8430303499'],
                        ['old' => '18438002994','new' => '8438002994'],
                        ['old' => '18443431814','new' => '8443431814'],
                        ['old' => '18444872318','new' => '8444872318'],
                        ['old' => '28321152740','new' => '8321152740'],
                        ['old' => '28335741859','new' => '8335741859'],
                        ['old' => '28336762221','new' => '8336762221'],
                        ['old' => '28337353481','new' => '8337353481'],
                        ['old' => '28340812289','new' => '8340812289'],
                        ['old' => '28348593063','new' => '8348593063'],
                        ['old' => '28351352330','new' => '8351352330'],
                        ['old' => '28352453254','new' => '8352453254'],
                        ['old' => '28358511965','new' => '8358511965'],
                        ['old' => '28382042341','new' => '8382042341'],
                        ['old' => '28394513042','new' => '8394513042'],
                        ['old' => '28401702569','new' => '8401702569'],
                        ['old' => '28406102411','new' => '8406102411'],
                        ['old' => '28412814312','new' => '8412814312'],
                        ['old' => '28413913942','new' => '8413913942'],
                        ['old' => '28426191134','new' => '8426191134'],
                        ['old' => '28438342507','new' => '8438342507'],
                        ['old' => '28441312184','new' => '8441312184'],
                        ['old' => '28446843390','new' => '8446843390'],
                        ['old' => '38314084425','new' => '8426550185'],
                        ['old' => '38352571753','new' => '8343373197'],
                        ['old' => '38355181417','new' => '8372951896'],
                        ['old' => '38358922477','new' => '8362763663'],
                        ['old' => '38381324556','new' => '8363192287'],
                        ['old' => '38418200812','new' => '8451880339'],
                        ['old' => '38430303499','new' => '8352071378'],
                        ['old' => '38438002994','new' => '8403096151'],
                        ['old' => '38443431814','new' => '8366532119'],
                        ['old' => '38444872318','new' => '8374414022']
                       ];
    }
    
    
    public function actionIndex(){
    
        if(isset($_GET['hetkicsitorpe'])){
            
            $tables = $this->getTables("'employee_id'");
            
            $update = '';
            
            if( (is_array($tables)) AND ( count($tables)) ){
                
                foreach($this->data as $row){
                    
                    foreach ($tables as $table) {
                    
                        $update .= "UPDATE ".DATABASE.".".$table['TABLE_NAME']." 
                                    SET ".DATABASE.".".$table['TABLE_NAME'].".employee_id = '".$row['new']."' 
                                    WHERE ".DATABASE.".".$table['TABLE_NAME'].".employee_id = '".$row['old']."'; ";
                       
                    }
                    
                }
                
                if($update != ''){
                    
                    try {

                        dbExecute($update);

                    } catch (Exception $e) {

                        $this->writeLog($e->getMessage());

                    }
                    
                }
                        
            }
            
        }
        
        if($this->full_error_log != ''){
            echo 'errors:<br>';
            print_r($this->full_error_log);
        }
        
        echo 'done..';
        exit();
        
    }
    
    /**
     * 
     * @param string $cols
     * @return mixed bool|array
     */
    public function getTables($cols=''){
        
        $return = FALSE;
                
        $sql = "SELECT DISTINCT TABLE_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE COLUMN_NAME IN (".$cols.")
                AND TABLE_SCHEMA='".DATABASE."';";

        try {

            $result = dbFetchAll($sql);

        } catch (Exception $e) {

            $this->writeLog($e->getMessage());

        }

        if (count($result)) {
            $return = $result;
        }

        return $return;
                       
                
    }
    
    
    /**
     * Write log file
     *
     * @param string $msg
     */
    private function writeLog($msg){

        $this->processing_error = true;

        $this->full_error_log .= date('Y-m-d H:i:s').' : <br>' . $msg . '<br>'.'-------------<br>';

        $file = fopen($this->error_log_file, "a+");

        fwrite($file, date('Y-m-d H:i:s').' : '. PHP_EOL . $msg . PHP_EOL.'-------------'.PHP_EOL);

        fclose($file);

    }
    
    /**
     * Könyvtár létrehozása ha még nem létezik
     *
     * @param string $path
     */
    function checkDir($path){
        
        mkdir($path, 0755, TRUE);
    
    }

    
}
