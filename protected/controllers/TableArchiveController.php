<?php

'yii2-only`;

	namespace app\controllers;
	use Yang;

`/yii2-only';


#yii2: done

/**
 * Controller archives given records from a table, based on created_on or date columns to be before a given date. Given date is current day - daysBack parameter.
 * It creates a new archive table, and deletes those records from the original table.
 */

class TableArchiveController extends Controller
{
	/**
	 * Basic function: archive table before given date to a new table and delete rows from original
	 * @return void
	 */
	public function actionIndex()
	{
		$tableName = requestParam('tableName');
		$daysBack = requestParam('daysBack');

		$today = new DateTime("now");
		$todayFormat = $today->format('Ymd');
		$date = new DateTime("now");
		$date->modify('-'.$daysBack.' day');
		$dateTil = $date->format('Y-m-d');

		$tables = dbGetTableNames();

		if (in_array($tableName, $tables) && !is_null($daysBack) && $daysBack != "")
		{
			$tableCols = dbGetTableColumns($tableName);
			if (array_key_exists("created_on", $tableCols))
			{
				$archiveSQL = "CREATE TABLE {$tableName}_arch_{$todayFormat} AS SELECT * FROM `{$tableName}` WHERE `created_on` <= '{$dateTil}';";
				dbExecute($archiveSQL);
				$truncateSQL = "DELETE FROM `{$tableName}` WHERE `created_on` <= '{$dateTil}';";
				dbExecute($truncateSQL);
				echo 'Sikeres archiválás created_on alapján.';
			} else if (array_key_exists("date", $tableCols))
			{
				$archiveSQL = "CREATE TABLE {$tableName}_arch_{$todayFormat} AS SELECT * FROM `{$tableName}` WHERE `date` <= '{$dateTil}';";
				dbExecute($archiveSQL);
				$truncateSQL = "DELETE FROM `{$tableName}` WHERE `date` <= '{$dateTil}';";
				dbExecute($truncateSQL);
				echo 'Sikeres archiválás date alapján.';
			} else {
				echo 'Az adott táblában nincs definiálva created_on vagy date mezője!';
			}
		} else {
			echo 'Az adott tábla nem létezik az adatbázisban, vagy nem adta meg hány napra visszamenőleg archiváljunk!';
		}
	}
}