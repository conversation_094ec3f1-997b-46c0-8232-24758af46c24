<?php

Yang::loadComponentNamespaces('Employee');

use Components\Employee\Enum\EmployeeControllerEnum;

trait EmployeeControllerColumns
{
	public function search()
	{
		return $this->getPreDefinedSearchFromDb('employeeManagement');
	}

	/**
	 * Visszaadja a dhtmlxGrid és az összes dialog oszlopait, valamint azok konfigurációját
	 */
	public function baseColumns($demo = false)
	{

		$c = AnyCache::get('baseColumns');
		if($c) return $c;
			$employeeDemoMode = (bool)((int)App::getSetting('employeeDemoMode'));
		//  local helpers

			$field = function($width,$otherProps=[]) {
				$a = ['width' =>$width, 'export' =>true, 'col_type' => 'ed'];
				foreach((array)$otherProps as $p=>$v) $a[$p]=$v;
				return $a;
			};
			$combo = function($width,$otherProps=[]) {
				$a = ['width' =>$width, 'export' =>true, 'col_type' => 'combo'];
				foreach((array)$otherProps as $p=>$v) $a[$p]=$v;
				return $a;
			};
			$dpick = function($width,$class= '',$otherProps=[]) {
				$a = [
					'width' =>$width,
                    'export' =>true,
                    'col_type' => 'ed',
					'report_width' =>20,
                    'dPicker' =>true,
                    'col_align' => 'center',
                    'dialog_width' => '1',
				];
				if($class) $a['class'] = $class;
				foreach((array)$otherProps as $p=>$v) $a[$p]=$v;
				return $a;
			};
			$printButton = function($otherProps=[]) {
				$a = ['col_type' => 'printButton'];
				foreach((array)$otherProps as $p=>$v) $a[$p]=$v;
				return $a;
			};
			$isSchenker = (Yang::getParam('customerDbPatchName') == 'schenker');
			$center = ['col_align' => 'center'];
			$break  = ['line_break' =>true];
			$noadd  = ['add' =>false];
			$optYesNo = [
				['id' => '0', 'value' =>Dict::getValue('no')  ],
				['id' => '1', 'value' =>Dict::getValue('yes') ],
			];

			$optionsFromArray = function($source) {
				return [
					'mode' =>Grid2Controller::G2BC_QUERY_MODE_ARRAY,
					'array' =>$source
				];
			};
			$optionsFromLookup = function($key, $one_dim_array = false, $default_value = null, $filter = [], $order = false, $module = null) {
				return [
					'mode' =>Grid2Controller::G2BC_QUERY_MODE_ARRAY,
					'array' =>App::getLookup($key,$one_dim_array, $default_value, $filter, $order, $module)
				];
			};
			$validRange	= function($tableName) {
				return "AND (('{valid_from}' BETWEEN {$tableName}.valid_from AND default_end({$tableName}.valid_to))
								OR  ({$tableName}.`valid_from` BETWEEN '{valid_from}' AND '{valid_to}')
							) ";
			};
			$getDependSQL = function($dependColumn) {
				if (App::getSetting('useCompanyAndPayrollRights')) {
					return "
						AND (`{$dependColumn}` = '{{$dependColumn}}' OR 'ALL' = '{{$dependColumn}}' OR `{$dependColumn}` = 'ALL')";
				}
				return '';
			};
			$optionFromModel = function($modelName, $dependColumns = []) use ($getDependSQL) {
				//$filters = Yang::session('employeeManagement_filters',[]);
				$art = new ApproverRelatedGroup();
				$gargSQL = $art->getApproverReleatedGroupSQL($modelName, 'employeeManagement', false, "'{valid_from}'");

				$model = new $modelName();
				$tableName = $model->tableName();

				$modelCriteriaGridMode = $model->getColumnGridCriteria($gargSQL['where'], '{date}');
				$modelCriteriaDialog = $model->getColumnDialogCriteria($gargSQL['where'], 'valid_from', 'valid_to');
				if (!empty($dependColumns) && App::getSetting('useCompanyAndPayrollRights')) {
					foreach ($dependColumns as $dependColumn) {
						$modelCriteriaDialog->condition .= $getDependSQL($dependColumn);
					}
				}
				$modelCriteriaDialog->condition = trim($modelCriteriaDialog->condition);
				return [
					'mode'					            => Grid2Controller::G2BC_QUERY_MODE_MODEL,
					'modelSelectionModel'	            => $modelName,
					'modelSelectionCriteriaGridMode'    => serialize($modelCriteriaGridMode),
					'modelSelectionCriteriaDialogMode'  => serialize($modelCriteriaDialog),
					'comboId'				            => "{$tableName}_id",
					'comboValue'			            => "{$tableName}_name",
				];
			};
			$optionsFromQuery = function($table,$keyField=0,$valField=0,$addEmpty=false,$grouping= '',$where= '',$order= 'value'
            ) {
				$keyField	= $keyField ?: "{$table}_id";
				$valField	= $valField ?: "{$table}_name";
				$published	= sprintf(' `status`=%s ',Status::PUBLISHED);
				$defaultEnd	= App::getSetting('defaultEnd');
				$validRange1	= "'{valid_from}' BETWEEN `valid_from` and IFNULL(`valid_to`, '{$defaultEnd}')";
				$unionEmpty	= ($addEmpty ? " UNION SELECT null as id, '' as value ": '');
				$where		= $where ?: $validRange1; // give it "1" if you want to clear the range condition
				$grouping	= $grouping ? " GROUP BY $grouping " : '';
				$orderBy	= ($order) ? "ORDER BY $order" : '';
				$where		= trim($where);
				$where		= preg_match('/^AND/', $where)?preg_replace('/^AND/', '', $where):$where;
				return [
					'mode' =>Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql' =>trim("

						SELECT
							{$keyField} AS id,
							{$valField} AS value
						FROM {$table}
						WHERE {$published} AND ({$where})
						{$grouping} {$unionEmpty} {$orderBy}

					"),
				];
			};
			$optionsFromSQL = function($sql,$plusArray=null) {
				$out = [
					'mode' =>Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql' =>$sql,
				];
				if(!is_null($plusArray)) $out['array'] = ['array' =>$plusArray];
				return $out;
			};

			$removeKeysFrom = function(&$a,$keyList) {
				$keys = linesOf($keyList);
				foreach($keys as $k) unset($a[$k]);
			};

			$optionResults = [];
			$optConf = OptionConfig::getAllData();

			foreach($optConf as $options) {
				$getOptionsParts = explode('_', $options['option_id']);

				if (count($getOptionsParts) == 2) {
					$optionResults[$getOptionsParts[0]][$getOptionsParts[1]]['type'] = $options['type'];
					$optionResults[$getOptionsParts[0]][$getOptionsParts[1]]['settings'] = $options['settings'];
				}
				else
				{
					$optionResults['ext'][$getOptionsParts[0]]['type'] = $options['type'];
					$optionResults['ext'][$getOptionsParts[0]]['settings'] = $options['settings'];
				}
			}

			$lang			= Dict::getLang();
			$need			= function($name) {return !EmployeeGroupConfig ::isActiveGroup($name);};
			$titleFields	= explode(';',App::getSetting('employeeDialogTitleFields'));
			$titleClass		= [];foreach($titleFields as $f) if($f) $titleClass[$f] = 'dialogTitle';
			$emptyElement	= ['id' => '', 'value' => ''];
			$nullElement	= ['id' =>null , 'value' => ''];
			$stPublished	= Status::PUBLISHED; // asszem úgy ejtik, hogy "kettő" :)
			$isPublished	= " `status`={$stPublished} ";
			$defaultEnd		= App::getSetting('defaultEnd');
			$eSQL = "

				SELECT `employee_id` AS id,`emp_id` AS value
				FROM `employee`
				WHERE `employee_id` = '{row_id_p1}' AND {$isPublished}
				AND valid_from <= '{valid_to}' AND IFNULL(`valid_to`,'{$defaultEnd}') >= '{valid_from}'

			";

			$ecSQL = "

				SELECT `employee_contract_id` AS id,`employee_contract_number` AS value
				FROM `employee_contract`
				LEFT JOIN `employee`
					ON employee.`employee_id` = employee_contract.`employee_id`
					AND employee.`status` = {$stPublished}
				WHERE employee.`employee_id` = '{row_id_p1}'
					AND employee_contract.`status` = {$stPublished}

			";

			$aclSQL = "
				SELECT
					`ac_access_level_id` AS id,
					`ac_access_level_name` AS value
				FROM `ac_access_level`
				WHERE
						`status` = {$stPublished}"
					.	(class_exists('AcAccessLevelRightController') ? ' AND ' .
							AcAccessLevelRightController::getAvailableAccessLevelIDSQL(
                                'EMPLOYEE',
                                '`ac_access_level_id`'
                            ) :
                    ''
						) . '
				ORDER BY value
			';

		//;


		$columns = [];
		$part = &$columns['dhtmlxGrid'];

		$part['fullname'] = $field(200);
		$part['emp_id'] = $field(150,$center);

		if(App::getSetting('employee_controller_competency_columns')){
			$part['reassignment'] = $combo(200,['col_align'=>'center']);
			$part['position_matching'] = $field(200,['col_align'=>'center']);
		}

		if ($employeeDemoMode) {
			$part['risk_of_term']       = $field(150,$center);
			$part['time_of_term']       = $field(150,$center);
			$part['cause_of_term']      = $field(150,$center);
			$part['ssc_index']          = $field(150,$center);
			$part['mood_index']         = $field(150,$center);
			$part['role_in_tem_comm']   = $field(150,$center);
			$part['absenteeism']        = $field(150,$center);
			$part['time_prop_abs']      = $field(150,$center);
			$part['delays']             = $field(100,$center);
			$part['overtime']           = $field(100,$center);
			$part['cond_eval']          = $field(200,$center);
			$part['subs_eval']          = $field(200,$center);
			$part['cowrs_eval']         = $field(200,$center);
		}

		$part['company_name']               = $field(200);
		$part['payroll_name']               = $field(200);
		$part['workgroup_name']             = $field(200);
		$part['unit_name']                  = $field(200);
		$part['cost_center_name']           = $field(200);
		$part['company_org_group1_name']    = $field(200);
		$part['company_org_group2_name']    = $field(200);
		$part['company_org_group3_name']    = $field(200);

		if (App::hasRight('employee', 'view_employee_position_column')) {
			$part['employee_position_name'] = $field(200);
		}

		$part['employee_contract_number']   = $field(200);
		$part['cost_name']  				= $field(200);
        if (App::hasRight("employee/employeeCardTab", "view")) {
            $part['card']                       = $field(250,$center);
        }
        $part['tax_number']                 = $field(250,$center);

		if (!$employeeDemoMode) {
			$part = $this->columnRights($part);
		}




		//------------------------------------------------------------------------------------------------------------  employeeTab
			$part = &$columns['employeeTab'];

			$onchange = [
                'onchange' =>linesOf(
                '

				company_id
				payroll_id
				unit_id
				company_org_group1_id
				company_org_group2_id
				company_org_group3_id

			'
            )];
			$onchangeCompany = [
                'onchange' =>linesOf(
                '

	                payroll_id
	                unit_id
	                company_org_group1_id
	                company_org_group2_id
	                company_org_group3_id
	        '
            )];
			$onchangePayroll = [
                'onchange' =>linesOf(
                '

	                unit_id
	                company_org_group1_id
	                company_org_group2_id
	                company_org_group3_id
	        '
            )];
			$part['valid_from'	] = $dpick(200, 'valid_from',$onchange);
			$part['valid_to'	] = $dpick(200, 'valid_to', $onchange);
			$part['title'		] = $field(250,['line_break'=>true]);
			$part['last_name'	] = $field(250,['class'=>'dialogTitle']);
			$part['first_name'	] = $field(250,['class'=>'dialogTitle']);
			$part['nameofbirth'	] = $field(250,['class'=>$titleClass['nameofbirth']]);
			if(!App::hasRight('employee/employeeTab', 'tax_number_hide')){
				$part['tax_number'	] = $field(250,['class'=>$titleClass['tax_number']]);
			}
			$part['gender'		] = $combo(250,['class'=>$titleClass['gender'], 'options'=>$optionsFromLookup(
                'gender'
            ) ]);
			$part['emp_id'		] = $field(200,['class'=>$titleClass['emp_id'], 'generatable' => true,'col_align'=>'center']);

			$unitWhere = $validRange('unit').$getDependSQL('company_id').$getDependSQL('payroll_id');

			//TODO: DEV-11455 amíg nem jövök rá, a megoldásra addig ez egy ideiglenes megoldás a BOS -nek
            $customersBigform = ['bos', 'danubius'];
			if (in_array(Yang::getParam('customerDbPatchName'), $customersBigform))
			{
				$part['company_id'	] = $combo(200,['options'=> $optionsFromQuery('company',0,0,false),'line_break'=>true]);
				$part['payroll_id'	] = $combo(200,['options'=> $optionsFromQuery('payroll',0,0,true)]);
				if ($need('unit_id' )) {$part['unit_id'] = $combo(200,['options'=> $optionsFromQuery(
                    'unit',
                    'unit_id',
                    'unit_name',true,
                    '', $unitWhere)]);}
			}
			elseif (Yang::getParam('customerDbPatchName') == 'kuehnenagel')
			{
				$part['company_id'	] = $combo(200,['options'=> $optionsFromQuery('company',0,0,false),'line_break'=>true]);
				$part['payroll_id'	] = $combo(200,['options'=> $optionsFromQuery('payroll',0,0,true)]);
				if ($need('unit_id' )) {$part['unit_id'] = $combo(200,['options'=> $optionsFromQuery(
                    'unit',
                    'unit_id',
                    'unit_name',true,
                    '', $unitWhere)]);}
				$part['emp_id'		] = $field(200,['class'=>$titleClass['emp_id'], 'generatable' => false, 'col_align'=>'center']);
			}
			else
			{
				$part['company_id'	] = $combo(200, Yang::arrayMerge(['options'=> $optionFromModel('Company'),'line_break'=>true], $onchangeCompany));
				$part['payroll_id'	] = $combo(200, Yang::arrayMerge(['options'=> $optionFromModel('Payroll', ['company_id'])], $onchangePayroll));
				if ($need('unit_id' )) {
                    $part['unit_id'] = $combo(
                        200,
                        [
                            'options'=> $optionFromModel(
                                'Unit',
                                [
                                    'company_id',
                                    'payroll_id'
                                ]
                            )
                        ]
                    );
                }
			}

			$cog1Where = $validRange('company_org_group1').$getDependSQL('company_id').$getDependSQL('payroll_id');
			$cog2Where = $validRange('company_org_group2').$getDependSQL('company_id').$getDependSQL('payroll_id');
			$cog3Where = $validRange('company_org_group3').$getDependSQL('company_id').$getDependSQL('payroll_id');

			if ($need('company_org_group1_id')) {
                $part['company_org_group1_id'] = $combo(
                    200,
                    [
                        'options'=> $optionsFromQuery(
                                'company_org_group1',
                                'company_org_group_id',
                                'company_org_group_name',
                                true,
                                '',
                                $cog1Where
                        ),
                        'line_break'=>true]
                );
            }
			if ($need('company_org_group2_id')) {
                $part['company_org_group2_id'] = $combo(
                    200,
                    [
                        'options' => $optionsFromQuery(
                                'company_org_group2',
                                'company_org_group_id',
                                'company_org_group_name',true,
                                '', $cog2Where
                        )
                    ]
                );
            }
			if ($need('company_org_group3_id')) {
                $part['company_org_group3_id'] = $combo(
                    200,
                    [
                        'options' => $optionsFromQuery(
                                'company_org_group3',
                                'company_org_group_id',
                                'company_org_group_name',true,
                                '', $cog3Where
                        )
                    ]
                );
            }
			if (App::hasRight('employee/employeeTab', 'employee_image')) {
				$part['employee_image'] = $field(200,['col_type'=>'image', 'report_width' => 20, 'image_preview' => true, 'dialog_width'=>'1']);
			}

			if ((int)App::getSetting('employee_blacklist_check')) {
				$part['blacklisted'    ] = $combo(250,[
                    'add' =>false,
                    'line_break' =>true,
                    'options' =>$optionsFromArray($optYesNo)]);
				$part['blacklist_note' ] = $field(250,['add' =>false,'col_type'=>'textarea']);
			}


        //------------------------------------------------------------------------------------------------------------  employeeTabHistory
			$columns['employeeTabHistory'] = $columns['employeeTab'];
			$part = &$columns['employeeTabHistory'];
			$removeKeysFrom($part,'

				employee_image
				nameofbirth
				gender
				valid_from
				valid_to
				' . (!App::hasRight('employee/employeeTab', 'tax_number_hide')) ? '' : 'tax_number' . '

			');
			$part['valid_from' ] = $dpick(200, 'valid_from');
			$part['valid_to'   ] = $dpick(200, 'valid_to');


        //------------------------------------------------------------------------------------------------------------  employeeContractTab
			$part = &$columns['employeeContractTab'];

			$onchange = [
                'onchange' =>linesOf(
                '


				workgroup_id
				employee_position_id

			'
            )];
			$part['ec_valid_from'				] = $dpick(200, 'valid_from',$onchange);
			$part['ec_valid_to'					] = $dpick(200, 'valid_to');
			$part['valid_from'					] = $dpick(200, 'valid_from',$onchange);
			$part['valid_to'					] = $dpick(200, 'valid_to');
			$part['employee_contract_number'	] = $field(200,$center + $break + ['class'=>$titleClass['employee_contract_number']]);
			$part['employee_contract_type'		] = $combo(200,['options'=>$optionsFromLookup('employee_contract_type', false, null, [], true)]);

			$withinTimeRange_any = "

				('{valid_from}'    BETWEEN `valid_from` and IFNULL(`valid_to`,'".App::getSetting('defaultEnd')."')) OR
				('{ec_valid_from}' BETWEEN `valid_from` and IFNULL(`valid_to`,'".App::getSetting('defaultEnd')."'))

			";

			$withinTimeRange_only = $withinTimeRange_both = " ('{valid_from}'    BETWEEN `valid_from` and IFNULL(`valid_to`,'" . App::getSetting(
                    'defaultEnd'
                ) . "'))";

			if (App::getSetting('ecValidOnChangeOnEmployeePosition') == 0)
			{
				$withinTimeRange_both .= " AND  ('{ec_valid_from}' BETWEEN `valid_from` and IFNULL(`valid_to`,'" . App::getSetting(
                        'defaultEnd'
                    ) . "'))";
			}

			$wgWhere = App::getSetting('useCompanyAndPayrollRights') ?
                       " AND (company_id = '{company_id}' OR company_id = 'ALL' OR company_id = '' 
			                    OR '{company_id}' = 'ALL' OR '{company_id}' = '')
						 AND (payroll_id = '{payroll_id}' OR payroll_id = 'ALL' OR payroll_id = '' 
						        OR '{payroll_id}' = 'ALL' OR '{payroll_id}' = '')
					   " :
                       '';

			if ($need('workgroup_id'))
			{
				$workGroupWithInTimeRange = App::getSetting('workGroupWithInTimeRange');
				if ($workGroupWithInTimeRange === 'only') {
					$workGroupWithInTimeRangeParam = $withinTimeRange_only;
				} elseif ($workGroupWithInTimeRange === 'both') {
					$workGroupWithInTimeRangeParam = $withinTimeRange_both;
				} else {
					$workGroupWithInTimeRangeParam = $withinTimeRange_any;
				}

				$part['workgroup_id'] = $combo(
                    200,
                    [
                        'options' => $optionsFromQuery(
                            'workgroup',
                            0,
                            0,
                            true,
                            'id',
                            '(' .$workGroupWithInTimeRangeParam. ')' . $wgWhere
                        )
                    ]
                );
			}

			if ($need('employee_position_id') && !App::hasRight('employee/employeeContractTab', 'employee_position')) {
                $part['employee_position_id'] = $combo(200,[
                'options' =>$optionsFromQuery('employee_position',0,0,true,
                '',$withinTimeRange_both)]);
            }

			$part['wage_type'] = $combo(200,['options' =>$optionsFromLookup('wage_type'), 'line_break' =>true]);

			// holtartás 20190628-170310

			$part['daily_worktime'	] = $field(200,$center);
			$part['note'			] = $field(200,$center);

			if(App::getSetting('ec_end_on_employeecontracttab') == 1){
				$ecEndTypeArray = $optionsFromLookup('ec_end_type');
				array_unshift($ecEndTypeArray['array'], '');
				$part['ec_end_reason'	] = $field(200,$center);
				$part['ec_end_type'		] = $combo(200,['options' =>$ecEndTypeArray]);
			}

			//------------------------------------------------------------------------------------------------------------  employeeContractTabHistory
			$columns['employeeContractTabHistory'] = $columns['employeeContractTab'];
			$part = &$columns['employeeContractTabHistory'];
			$removeKeysFrom($part,'

				valid_from
				valid_to
				ec_valid_from
				ec_valid_to

			');

			$part['ec_valid_from'	] = $dpick(200, 'valid_from');
			$part['ec_valid_to'		] = $dpick(200, 'valid_to');
			$part['valid_from'		] = $dpick(200, 'valid_from');
			$part['valid_to'		] = $dpick(200, 'valid_to');


        //------------------------------------------------------------------------------------------------------------  employeeSalaryTab
			$part = &$columns['employeeSalaryTab'];

			$part['valid_from'				] = $dpick(200, 'valid_from');
			$part['valid_to'				] = $dpick(200, 'valid_to');
			$part['employee_contract_id'	] = $combo(200,$break + ['add' =>false, 'options' =>$optionsFromSQL($ecSQL)]);
			$part['personal_month_salary'	] = $field(200,$center + $break + [
																				'class' => 'maskedData',
																				'data-mask' => '###.###.###.###',
																				'data-mask-reverse' => 'true'
																				]
														);
			$part['personal_hour_salary'	] = $field(200,$center + [
																		'class' => 'maskedData',
																		'data-mask' => '###.###.###.###',
																		'data-mask-reverse' => 'true'
																		]
														);
			$part['shift'					] = $combo(200,$center + $break + ['options' =>$optionsFromLookup('yes_no')]);
			if (!App::hasRight('employee/employeeSalaryTab', 'shift_bonus_hide'))
			{
				$part['shift_bonus_in_percent'	] = $field(200,$center);
			}

			$stPublished = Status::PUBLISHED;
			$sqlTemplate = "
				SELECT `option_value` AS id,
					`option_value` AS value
				FROM `option`
				WHERE
					`status` = {$stPublished}
					AND `option_id` = 'es_option%s'
			";
			$idValueArray	= [
				'array' => [$emptyElement]
				];

			foreach ($optionResults['es'] as $fieldName => $fieldData)
			{
				if ($fieldData['type'] === null) { continue; }
				
				$settings = [];
	
				if (isset($fieldData['settings'])) {
					$settings = json_decode($fieldData['settings'], true);
				}
	
				$settingLineBreak = isset($settings['line_break']) && $settings['line_break'];
	
				$features = [
					'dPicker' => $fieldData['type'] === 'dPicker',
					'col_type' => ($fieldData['type'] === 'dPicker') ? 'dPicker' : 'ed',
					'col_align' => 'center',
					'line_break' => $settingLineBreak,
					'options' => $optionsFromSQL(sprintf($sqlTemplate, $fieldName)) + $idValueArray,
				];
				if (("es_$fieldName" == 'es_option1' || "es_$fieldName" == 'es_option2') && Yang::getParam('customerDbPatchName') == 'schrack')
				{
					$features['class'] = 'maskedData';
					$features['data-mask'] = '###.###.###.###';
					$features['data-mask-reverse'] = 'true';
				}
				if ($fieldData['type'] === 'combo')
				{
					$esOptionArray = $optionsFromLookup("es_$fieldName", false, null, [], true);
					array_unshift($esOptionArray['array'], '');
					$part["es_$fieldName"] = $combo(200, $center + ['line_break' => $settingLineBreak, 'options' => $esOptionArray]);
				}
				elseif ($fieldData['type'] === 'dPicker')
				{
					$part["es_$fieldName"] = $dpick(200, $features, ['line_break' => $settingLineBreak]);
				}
				else
				{
					$part["es_$fieldName"] = $field(200, $features);
				}
			}

			$part['note'] = $field(200,$center);


        //------------------------------------------------------------------------------------------------------------  employeeSalaryTabHistory
			$part = &$columns['employeeSalaryTabHistory'];
			$part = ['employee_contract_number' =>$field(200,$center)];
			$part = Yang::arrayMerge($part,$columns['employeeSalaryTab']);
			$removeKeysFrom($part,'

				employee_contract_id
				valid_from
				valid_to

			');
			$part['valid_from'	] = $dpick(200, 'valid_from');
			$part['valid_to'	] = $dpick(200, 'valid_to');


        //------------------------------------------------------------------------------------------------------------  employeeCafeteriaTab
			$part = &$columns['employeeCafeteriaTab'];
			$onchange = [
                'onchange' =>linesOf(
                '

				cafeteria_id

			'
            )];
			$part['valid_from'							] = $dpick(200, 'valid_from',$onchange);
			$part['valid_to'							] = $dpick(200, 'valid_to');
			$part['employee_contract_id'				] = $combo(200,$break + [
                    'add' =>true,
                    'options' =>$optionsFromSQL($ecSQL)]);
			$part['cafeteria_id'						] = $combo(200,$break + $center + [
                    'add' =>true,
                    'options' => $optionsFromQuery(
                    'cafeteria',0,0)]);
			$part['note'								] = $field(200,$center);
			if(Yang::getParam('customerDbPatchName') == 'schenker') {
				$part['cafeteria'						] = $field(200,$center);
				$part['benefit_groups'					] = $combo(200,$center + [
                        'options' =>$optionsFromLookup(
                        'benefit_groups'
                    )]);
				$part['account_number_accommodation'	] = $field(200,$center);
				$part['account_number_hospitality'		] = $field(200,$center);
				$part['account_number_leisure'			] = $field(200,$center);
			}


        //------------------------------------------------------------------------------------------------------------  employeeCafeteriaTabHistory
			$part = &$columns['employeeCafeteriaTabHistory'];
			$part = ['employee_contract_number' =>$field(200,$center)];
			$part = Yang::arrayMerge($part,$columns['employeeCafeteriaTab']);
			if(Yang::getParam('customerDbPatchName') == 'schenker') {
				$part['benefit_groups_name'						] = $field(200,$center);
				$removeKeysFrom($part,'benefit_groups');
			}

			$removeKeysFrom($part,'

				employee_contract_id
				valid_from
				valid_to

			');
			$part['valid_from'	] = $dpick(200, 'valid_from');
			$part['valid_to'	] = $dpick(200, 'valid_to');


        //------------------------------------------------------------------------------------------------------------  employeeAddressTab
			$part = &$columns['employeeAddressTab'];
			$employee_address=App::getSetting('employee_address');
			$showWholes = ($employee_address===EmployeeAddress::SETTING_FULL);
			$showPieces = ($employee_address===EmployeeAddress::SETTING_PIECE);
			if($employee_address===EmployeeAddress::SETTING_ALL) {
				$showWholes = true;
				$showPieces = true;
			}

			$part['valid_from'			] = $dpick(200, 'valid_from');
			$part['valid_to'			] = $dpick(200, 'valid_to');
			$part['employee_id'			] = $combo(200,$center + $break + [
                    'add' =>false,
                    'options' =>$optionsFromSQL($eSQL)]);
            if(!App::hasRight('employee/employeeAddressTab', 'address_card_number_hide')){
                $part['address_card_number'	] = $field(250,$center + $break);
            }

			if($showWholes) {$part['full_address'] = $field(250,$center + $break);}
			if($showPieces)
			{
				$part['zip_code']              = $field(200,$center + $break);
				$part['country']               = $field(250,$center);
				$part['city']                  = $field(250,$center);
				$part['district']              = $field(200,$center);
				$part['public_place_name']     = $field(250,$center);
				$part['public_place_type']     = $field(200,$center);
				$part['house_number']          = $field(200,$center);
                $part['building']              = $field(200,$center);
                $part['staircase']             = $field(200,$center);
				$part['floor']                 = $field(200,$center);
				$part['door']                  = $field(200,$center);
			}

			if($showWholes) {$part['res_full_address'] = $field(250,$center + $break);}
			if($showPieces)
			{
				$part['res_zip_code']          = $field(200,$center + $break);
				$part['res_country']           = $field(250,$center);
				$part['res_city']              = $field(250,$center);
				$part['res_district']          = $field(200,$center);
				$part['res_public_place_name'] = $field(250,$center);
				$part['res_public_place_type'] = $field(200,$center);
				$part['res_house_number']      = $field(200,$center);
                $part['res_building']          = $field(200,$center);
                $part['res_staircase']         = $field(200,$center);
				$part['res_floor']             = $field(200,$center);
				$part['res_door']              = $field(200,$center);
				$part['note']                  = $field(250,$center + $break);
			}


        //------------------------------------------------------------------------------------------------------------  employeeAddressTabHistory
			$part = &$columns['employeeAddressTabHistory'];
			$part = ['emp_id' =>$field(200,$center)];
			$part = Yang::arrayMerge($part,$columns['employeeAddressTab']);
			$removeKeysFrom($part,'

				valid_from
				valid_to
				employee_id

			');
			$part['valid_from'	] = $dpick(200, 'valid_from');
			$part['valid_to'	] = $dpick(200, 'valid_to');


        //------------------------------------------------------------------------------------------------------------  employeeExtTab
			$part = &$columns['employeeExtTab'];
			$part['valid_from'				] = $dpick(200, 'valid_from', ['onchange' =>['employee_id']]);
			$part['valid_to'				] = $dpick(200, 'valid_to');
			$part['employee_id'				] = $combo(200,$center + $break + [
                    'add' =>false,
                    'options' =>$optionsFromSQL($eSQL)]);

            if(!App::hasRight('employee/employeeExtTab', 'place_of_birth')) {
                $part['place_of_birth'] = $field(200, $center + $break);
            }
			if(!App::hasRight('employee/employeeExtTab', 'date_of_birth_hide')) {
				$part['date_of_birth'] = $field(200,$center + ['dPicker'=>true]);
			}
            if(!App::hasRight('employee/employeeExtTab', 'mothers_name')) {
                $part['mothers_name'] = $field(200, $center);
            }
			if (!in_array(Yang::getParam('customerDbPatchName'), ['nemak', 'hanonalba'])) {
				$part['ssn'] = $field(200,$center + $break);
			}

            if (!App::hasRight('employee/employeeExtTab', 'personal_id_card_number')) {
                $part['personal_id_card_number'] = $field(200, $center);
            }

            if (!App::hasRight('employee/employeeExtTab', 'passport_number')) {
                $part['passport_number'] = $field(200,$center);
            }
			if (Yang::getParam('customerDbPatchName') == 'eisberg') {
				$part['passport_number'] = $combo(200,$center + [
                        'options' => $optionsFromArray([[
                        'id' => '0', 'value' => Dict::getValue(
                        'no'
                    )], [
                        'id' => '1', 'value' => Dict::getValue(
                        'yes'
                    )]])]);
			}

			$stPublished = Status::PUBLISHED;
			$sqlTemplate = "

				SELECT `option_value` as id,`option_value` as value
				FROM `option`
				WHERE `status`={$stPublished} AND `option_id`='option%s'

			";
			$idValueArray	= ['array' =>[$emptyElement]];

			foreach ($optionResults['ext'] as $fieldName => $fieldData)
			{
				$width = 200;
				if (("$fieldName" == 'option3' || "$fieldName" == 'option6') && Yang::getParam('customerDbPatchName') == 'schrack' ){
					$width = 300;
				}

				if ($fieldData['type'] === null) { continue; }
				
				$settings = [];
	
				if (isset($fieldData['settings'])) {
					$settings = json_decode($fieldData['settings'], true);
				}
	
				$settingLineBreak = isset($settings['line_break']) && $settings['line_break'];
	
				$features = [
					'dPicker' => $fieldData['type'] === 'dPicker',
					'col_type' => ($fieldData['type'] === 'dPicker') ? 'dPicker' : 'ed',
					'col_align' => 'center',
					'line_break' => $settingLineBreak,
					'options' => $optionsFromSQL(sprintf($sqlTemplate, $fieldName)) + $idValueArray,
				];
			
				if ($fieldData['type'] === 'combo')
				{
					if (Yang::getParam('customerDbPatchName') == 'carrier')
					{
						$sql = '
								SELECT
									`option_value` as id,
									`option_value` as value
								FROM
									`option`
								WHERE
										`status` = ' . $stPublished . "
									AND `option_id` = '$fieldName';";

                    	$part["$fieldName"] = $combo($width, $center + ['options' => $optionsFromSQL($sql)]);
					}
					else
					{
						$exOptionArray = $optionsFromLookup("$fieldName", false, null, [], true);
						array_unshift($exOptionArray['array'], '');
						$part["$fieldName"] = $combo($width, $center + ['line_break' => $settingLineBreak, 'options' => $exOptionArray]);
					}
				}
				elseif ($fieldData['type'] === 'dPicker')
				{
					$part["$fieldName"] = $dpick($width, $features, ['line_break' => $settingLineBreak]);
				}
				else
				{
					$part["$fieldName"] = $field($width, $features);
				}
			}

			$part['note'] = $field(250,$center);


        //------------------------------------------------------------------------------------------------------------  employeeExtTabHistory
			$part = &$columns['employeeExtTabHistory'];
			$part = ['emp_id' =>$field(200,$center)];
			$part = Yang::arrayMerge($part,$columns['employeeExtTab']);
			$removeKeysFrom($part,'

				valid_from
				valid_to
				employee_id

			');
			$part['valid_from'	] = $dpick(200, 'valid_from');
			$part['valid_to'	] = $dpick(200, 'valid_to');


        //------------------------------------------------------------------------------------------------------------  employeeExt2Tab
			$part = &$columns['employeeExt2Tab'];
            $part['valid_from'				] = $dpick(200, 'valid_from', ['onchange' =>['employee_id']]);
			$part['valid_to'				] = $dpick(200, 'valid_to');
			$part['employee_id'				] = $combo(200,$center + $break + [
                    'add' =>false,
                    'options' =>$optionsFromSQL($eSQL)]);

			$stPublished = Status::PUBLISHED;
			$sqlTemplate = "

				SELECT `option_value` as id,`option_value` as value
				FROM `option`
				WHERE `status`={$stPublished} AND `option_id`='ext2_option%s'

			";
			$emptyElement = ['id' => '', 'value' => ''];
			$idValueArray = ['array' =>[$emptyElement]];

			foreach ($optionResults['ext2'] as $fieldName => $fieldData)
			{
				if ($fieldData['type'] === null) { continue; }
				
				$settings = [];

				if (isset($fieldData['settings'])) {
					$settings = json_decode($fieldData['settings'], true);
				}
	
				$settingLineBreak = isset($settings['line_break']) && $settings['line_break'];
	
				$features = [
					'dPicker' => $fieldData['type'] === 'dPicker',
					'col_type' => ($fieldData['type'] === 'dPicker') ? 'dPicker' : 'ed',
					'col_align' => 'center',
					'line_break' => $settingLineBreak,
					'options' => $optionsFromSQL(sprintf($sqlTemplate, $fieldName)) + $idValueArray,
				];
			
				if ($fieldData['type'] === 'combo')
				{
					$part["ext2_$fieldName"] = $combo(200, $center + ['line_break' => $settingLineBreak, 'options' => $optionsFromLookup("ext2_$fieldName", false, null, [], true)]);
				}
				elseif ($fieldData['type'] === 'dPicker')
				{
					$part["ext2_$fieldName"] = $dpick(200, $features, ['line_break' => $settingLineBreak]);
				}
				else
				{
					$part["ext2_$fieldName"] = $field(200, $features);
					if($isSchenker) {
						$features['default_value'] = Dict::getValue('ext2_option2_default_value');
						$part['ext2_option2'] = $field(200,$features);
						$features['default_value'] = Dict::getValue('ext2_option3_default_value');
						$part['ext2_option3'] = $field(200,$features);
						$features['default_value'] = Dict::getValue('ext2_option4_default_value');
						$part['ext2_option4'] = $field(200,$features);
					}
				}
			}


        //------------------------------------------------------------------------------------------------------------  employeeExt2TabHistory
		$part = &$columns['employeeExt2TabHistory'];
		$part = ['emp_id' =>$field(200,$center)];
		$part = Yang::arrayMerge($part,$columns['employeeExt2Tab']);
		$removeKeysFrom($part,'

			valid_from
			valid_to
			employee_id

		');
		$part['valid_from'	] = $dpick(200, 'valid_from');
		$part['valid_to'	] = $dpick(200, 'valid_to');



		//------------------------------------------------------------------------------------------------------------  employeeExt3Tab
		$part = &$columns['employeeExt3Tab'];
        $part['valid_from'				] = $dpick(200, 'valid_from', ['onchange' =>['employee_id']]);
		$part['valid_to'				] = $dpick(200, 'valid_to');
		$part['employee_id'				] = $combo(200,$center + $break + [
                'add' =>false,
                'options' =>$optionsFromSQL($eSQL)]);

		$stPublished = Status::PUBLISHED;
		$sqlTemplate = "

				SELECT `option_value` as id,`option_value` as value
				FROM `option`
				WHERE `status`={$stPublished} AND `option_id`='ext3_option%s'

			";
		$emptyElement = ['id' => '', 'value' => ''];
		$idValueArray = ['array' =>[$emptyElement]];

		foreach ($optionResults['ext3'] as $fieldName => $fieldData)
		{
			if ($fieldData['type'] === null) { continue; }
			
			$settings = [];

			if (isset($fieldData['settings'])) {
				$settings = json_decode($fieldData['settings'], true);
			}

			$settingLineBreak = isset($settings['line_break']) && $settings['line_break'];

			$features = [
				'dPicker' => $fieldData['type'] === 'dPicker',
				'col_type' => ($fieldData['type'] === 'dPicker') ? 'dPicker' : 'ed',
				'col_align' => 'center',
				'line_break' => $settingLineBreak,
				'options' => $optionsFromSQL(sprintf($sqlTemplate, $fieldName)) + $idValueArray,
			];
		
			if ($fieldData['type'] === 'combo')
			{
				$ex3OptionArray = $optionsFromLookup("ext3_$fieldName", false, null, [], true);
				array_unshift($ex3OptionArray['array'], '');
                $part["ext3_$fieldName"] = $combo(200, $center + ['line_break' => $settingLineBreak, 'options' => $ex3OptionArray]);
			}
			elseif ($fieldData['type'] === 'dPicker')
			{
				$part["ext3_$fieldName"] = $dpick(200, $features, ['line_break' => $settingLineBreak]);
			}
			else
			{
				$part["ext3_$fieldName"] = $field(200, $features);
			}
		}


        //------------------------------------------------------------------------------------------------------------  employeeExt3TabHistory
		$part = &$columns['employeeExt3TabHistory'];
		$part = ['emp_id' =>$field(200,$center)];
		$part = Yang::arrayMerge($part,$columns['employeeExt3Tab']);
		$removeKeysFrom($part,'

				valid_from
				valid_to
				employee_id

			');
		$part['valid_from'	] = $dpick(200, 'valid_from');
		$part['valid_to'	] = $dpick(200, 'valid_to');



		//------------------------------------------------------------------------------------------------------------  employeeExt4Tab
		$part = &$columns['employeeExt4Tab'];
        $part['valid_from'				] = $dpick(200, 'valid_from', ['onchange' =>['employee_id']]);
		$part['valid_to'				] = $dpick(200, 'valid_to');
		$part['employee_id'				] = $combo(200,$center + $break + [
                'add' =>false,
                'options' =>$optionsFromSQL($eSQL)]);

		$stPublished = Status::PUBLISHED;
		$sqlTemplate = "

				SELECT `option_value` as id,`option_value` as value
				FROM `option`
				WHERE `status`={$stPublished} AND `option_id`='ext4_option%s'

			";
		$emptyElement = ['id' => '', 'value' => ''];
		$idValueArray = ['array' =>[$emptyElement]];

		foreach ($optionResults['ext4'] as $fieldName => $fieldData)
		{
			if ($fieldData['type'] === null) { continue; }
			
			$settings = [];

			if (isset($fieldData['settings'])) {
				$settings = json_decode($fieldData['settings'], true);
			}

			$settingLineBreak = isset($settings['line_break']) && $settings['line_break'];

			$features = [
				'dPicker' => $fieldData['type'] === 'dPicker',
				'col_type' => ($fieldData['type'] === 'dPicker') ? 'dPicker' : 'ed',
				'col_align' => 'center',
				'line_break' => $settingLineBreak,
				'options' => $optionsFromSQL(sprintf($sqlTemplate, $fieldName)) + $idValueArray,
			];
		
			if ($fieldData['type'] === 'combo')
			{
                $ex4OptionArray = $optionsFromLookup("ext4_$fieldName", false, null, [], true);
				array_unshift($ex4OptionArray['array'], '');
				$part["ext4_$fieldName"] = $combo(200, $center + ['line_break' => $settingLineBreak, 'options' => $ex4OptionArray]);
			}
			elseif ($fieldData['type'] === 'dPicker')
			{
				$part["ext4_$fieldName"] = $dpick(200, $features, ['line_break' => $settingLineBreak]);
			}
			else
			{
				$part["ext4_$fieldName"] = $field(200, $features);
			}
		}


        //------------------------------------------------------------------------------------------------------------  employeeExt4TabHistory
		$part = &$columns['employeeExt4TabHistory'];
		$part = ['emp_id' =>$field(200,$center)];
		$part = Yang::arrayMerge($part,$columns['employeeExt4Tab']);
		$removeKeysFrom($part,'

				valid_from
				valid_to
				employee_id

			');
		$part['valid_from'	] = $dpick(200, 'valid_from');
		$part['valid_to'	] = $dpick(200, 'valid_to');


		//------------------------------------------------------------------------------------------------------------  employeeExt5Tab
		$part = &$columns['employeeExt5Tab'];
        $part['valid_from'				] = $dpick(200, 'valid_from', ['onchange' =>['employee_id']]);
		$part['valid_to'				] = $dpick(200, 'valid_to');
		$part['employee_id'				] = $combo(200,$center + $break + [
                'add' =>false,
                'options' =>$optionsFromSQL($eSQL)]);

		$stPublished = Status::PUBLISHED;
		$sqlTemplate = "

				SELECT `option_value` as id,`option_value` as value
				FROM `option`
				WHERE `status`={$stPublished} AND `option_id`='ext5_option%s'

			";
		$emptyElement = ['id' => '', 'value' => ''];
		$idValueArray = ['array' =>[$emptyElement]];

		foreach ($optionResults['ext5'] as $fieldName => $fieldData)
		{
			if ($fieldData['type'] === null) { continue; }
			
			$settings = [];

			if (isset($fieldData['settings'])) {
				$settings = json_decode($fieldData['settings'], true);
			}

			$settingLineBreak = isset($settings['line_break']) && $settings['line_break'];

			$features = [
				'dPicker' => $fieldData['type'] === 'dPicker',
				'col_type' => ($fieldData['type'] === 'dPicker') ? 'dPicker' : 'ed',
				'col_align' => 'center',
				'line_break' => $settingLineBreak,
				'options' => $optionsFromSQL(sprintf($sqlTemplate, $fieldName)) + $idValueArray,
			];

            if (isset($settings['width'])) {
                $features['width'] = $settings['width'];
            }
		
			if ($fieldData['type'] === 'combo')
			{
				$ex5OptionArray = $optionsFromLookup("ext5_$fieldName");
				array_unshift($ex5OptionArray['array'], '');
				$part["ext5_$fieldName"] = $combo(200, $center + ['line_break' => $settingLineBreak, 'options' => $ex5OptionArray]);
			}
			elseif ($fieldData['type'] === 'dPicker')
			{
				$part["ext5_$fieldName"] = $dpick(200, $features, ['line_break' => $settingLineBreak]);
			}
			else
			{
				$part["ext5_$fieldName"] = $field(200, $features);
			}
		}


        //------------------------------------------------------------------------------------------------------------  employeeExt5TabHistory
		$part = &$columns['employeeExt5TabHistory'];
		$part = ['emp_id' =>$field(200,$center)];
		$part = Yang::arrayMerge($part,$columns['employeeExt5Tab']);
		$removeKeysFrom($part,'

				valid_from
				valid_to
				employee_id

			');
		$part['valid_from'	] = $dpick(200, 'valid_from');
		$part['valid_to'	] = $dpick(200, 'valid_to');


		//------------------------------------------------------------------------------------------------------------  employeeExt6Tab
		$part = &$columns['employeeExt6Tab'];
        $part['valid_from'				] = $dpick(200, 'valid_from', ['onchange' =>['employee_id']]);
		$part['valid_to'				] = $dpick(200, 'valid_to');
		$part['employee_id'				] = $combo(200,$center + $break + [
                'add' =>false,
                'options' =>$optionsFromSQL($eSQL)]);

		$stPublished = Status::PUBLISHED;
		$sqlTemplate = "

				SELECT `option_value` as id,`option_value` as value
				FROM `option`
				WHERE `status`={$stPublished} AND `option_id`='ext6_option%s'

			";
		$emptyElement = ['id' => '', 'value' => ''];
		$idValueArray = ['array' =>[$emptyElement]];

		foreach ($optionResults['ext6'] as $fieldName => $fieldData)
		{
			if ($fieldData['type'] === null) { continue; }
			
			$settings = [];

			if (isset($fieldData['settings'])) {
				$settings = json_decode($fieldData['settings'], true);
			}
			
			$settingLineBreak = isset($settings['line_break']) && $settings['line_break'];

			$features = [
				'dPicker' => $fieldData['type'] === 'dPicker',
				'col_type' => ($fieldData['type'] === 'dPicker') ? 'dPicker' : 'ed',
				'col_align' => 'center',
				'line_break' => $settingLineBreak,
				'options' => $optionsFromSQL(sprintf($sqlTemplate, $fieldName)) + $idValueArray,
			];
		
			if ($fieldData['type'] === 'combo')
			{
				$ex6OptionArray = $optionsFromLookup("ext6_$fieldName");
				array_unshift($ex6OptionArray['array'], '');
				$part["ext6_$fieldName"] = $combo(200, $center + ['line_break' => $settingLineBreak, 'options' => $ex6OptionArray]);
			}
			elseif ($fieldData['type'] === 'dPicker')
			{
				$part["ext6_$fieldName"] = $dpick(200, $features, ['line_break' => $settingLineBreak]);
			}
			else
			{
				$part["ext6_$fieldName"] = $field(200, $features);
			}
		}


        //------------------------------------------------------------------------------------------------------------  employeeExt6TabHistory
		$part = &$columns['employeeExt6TabHistory'];
		$part = ['emp_id' =>$field(200,$center)];
		$part = Yang::arrayMerge($part,$columns['employeeExt6Tab']);
		$removeKeysFrom($part,'

				valid_from
				valid_to
				employee_id

			');
		$part['valid_from'	] = $dpick(200, 'valid_from');
		$part['valid_to'	] = $dpick(200, 'valid_to');


        //------------------------------------------------------------------------------------------------------------  employeeExt7Tab
        $part = &$columns['employeeExt7Tab'];
        $part['valid_from'				] = $dpick(200, 'valid_from', ['onchange' =>['employee_id']]);
        $part['valid_to'				] = $dpick(200, 'valid_to');
        $part['employee_id'				] = $combo(200,$center + $break + [
                'add' =>false,
                'options' =>$optionsFromSQL($eSQL)]);

        $stPublished = Status::PUBLISHED;
        $sqlTemplate = "

				SELECT `option_value` as id,`option_value` as value
				FROM `option`
				WHERE `status`={$stPublished} AND `option_id`='ext7_option%s'

			";
        $emptyElement = ['id' => '', 'value' => ''];
        $idValueArray = ['array' =>[$emptyElement]];

        foreach ($optionResults['ext7'] as $fieldName => $fieldData)
        {
            if ($fieldData['type'] === null) { continue; }

            $settings = [];

            if (isset($fieldData['settings'])) {
                $settings = json_decode($fieldData['settings'], true);
            }

            $settingLineBreak = isset($settings['line_break']) && $settings['line_break'];

            $features = [
                'dPicker' => $fieldData['type'] === 'dPicker',
                'col_type' => ($fieldData['type'] === 'dPicker') ? 'dPicker' : 'ed',
                'col_align' => 'center',
                'line_break' => $settingLineBreak,
                'options' => $optionsFromSQL(sprintf($sqlTemplate, $fieldName)) + $idValueArray,
            ];

            if ($fieldData['type'] === 'combo')
            {
                $ex7OptionArray = $optionsFromLookup("ext7_$fieldName");
                array_unshift($ex7OptionArray['array'], '');
                $part["ext7_$fieldName"] = $combo(200, $center + ['line_break' => $settingLineBreak, 'options' => $ex7OptionArray]);
            }
            elseif ($fieldData['type'] === 'dPicker')
            {
                $part["ext7_$fieldName"] = $dpick(200, $features, ['line_break' => $settingLineBreak]);
            }
            else
            {
                $part["ext7_$fieldName"] = $field(200, $features);
            }
        }


        //------------------------------------------------------------------------------------------------------------  employeeExt7TabHistory
        $part = &$columns['employeeExt7TabHistory'];
        $part = ['emp_id' =>$field(200,$center)];
        $part = Yang::arrayMerge($part,$columns['employeeExt7Tab']);
        $removeKeysFrom($part,'

				valid_from
				valid_to
				employee_id

			');
        $part['valid_from'	] = $dpick(200, 'valid_from');
        $part['valid_to'	] = $dpick(200, 'valid_to');


		//------------------------------------------------------------------------------------------------------------  employeeDocsTab
			$part = &$columns['employeeDocsTab'];
			$part['note'		] = $field(250,$center);
			$part['employee_id'	] = $combo(200,$center + $break + ['add' =>false, 'options' =>$optionsFromSQL($eSQL)]);
            $part['valid_from'	] = $dpick(200, 'valid_from', ['onchange' =>['employee_id']] + $break);
			$part['valid_to'	] = $dpick(200, 'valid_to');
			$part['fs_file_id'	] = $field(200, $this->getFileFieldOptions('employeeDocsTab'));
			if (App::getSetting('employeeControl_show_certificate_id')) {
				$part['certificate_id' ] = $field(250,$break);
				$part['institution_name'] = $field(250,$break);
			}


        //------------------------------------------------------------------------------------------------------------  employeeDocsTabHistory
			$part = &$columns['employeeDocsTabHistory'];
			$part = $columns['employeeDocsTab'];
			$removeKeysFrom($part,'

				valid_from
				valid_to
				employee_id
				fs_file_id

			');

        //------------------------------------------------------------------------------------------------------------  employeeLabourDocsTab
			$part = &$columns['employeeLabourDocsTab'];
			$part['note'		] = $field(250, $center);
			$part['employee_id'	] = $combo(200, $center + $break + ['add' => false, 'options' => $optionsFromSQL($eSQL)]);
            $part['valid_from'	] = $dpick(200, 'valid_from', ['onchange' =>['employee_id']] + $break);
			$part['valid_to'	] = $dpick(200, 'valid_to');
			$part['fs_file_id'	] = $field(200, $this->getFileFieldOptions('employeeLabourDocsTab'));


        //------------------------------------------------------------------------------------------------------------  employeeLabourDocsTabHistory
			$part = &$columns['employeeLabourDocsTabHistory'];
			$part = $columns['employeeLabourDocsTab'];
			$removeKeysFrom($part,'

				valid_from
				valid_to
				employee_id
				fs_file_id

			');


        //------------------------------------------------------------------------------------------------------------  employeeGroupTab

			$groupSQL = self::group_SQL();
			$groupValueSQL = self::groupValue_SQL();

			$columns['employeeGroupTab'] = [

				'valid_from'			=> $dpick(200,'',['onchange' => ['group_id']]),
				'valid_to'				=> $dpick(200),
				'employee_contract_id'	=> $combo(200,$break + $noadd + ['options' =>$optionsFromSQL($ecSQL)]),
				'group_id'				=> $combo(200,$break          + [
                        'options' =>$optionsFromSQL($groupSQL),
                        'onchange' =>['group_value']]),
				'group_value'			=> $combo(200,                  ['options' =>$optionsFromSQL($groupValueSQL)]),

			];

        //------------------------------------------------------------------------------------------------------------  employeeGroupTabHistory

			$part = &$columns['employeeGroupTabHistory'];
			$part['employee_contract_number'] = $field(200,$center);
			$part = Yang::arrayMerge($part,$columns['employeeGroupTab']);
			$removeKeysFrom($part,'

				valid_from
				valid_to
				employee_contract_id

			');
			$part['valid_from'	] = $dpick(200); // #see 20190702-225129
			$part['valid_to'	] = $dpick(200);

        //------------------------------------------------------------------------------------------------------------  employeeCardTab

			$part = &$columns['employeeCardTab'];
			$part['employee_contract_id' ] = $combo(200,$noadd + ['options' =>$optionsFromSQL($ecSQL)]);
			$part['card'                 ] = $field(200,$center + $break);

			if ((int)App::getSetting('employee_use_access_level'))
			{
				$optNull = ['array' =>[$nullElement]];
				$part['acl'] = $combo(200,['options' => $optionsFromSQL($aclSQL,$optNull)]);
			}

			if ((int)App::getSetting('employee_use_rgnet_check_freq')) {
				$part['rgnet_check_freq'] = $field(200,$center);
			}

			if (App::getSetting('employee_tab_show_card_type')) {
				$cardTypeArray = $optionsFromLookup('card_type', false, 1, [1,2]);
				$part['card_type'] = $combo(200,$noadd + ['options' => $cardTypeArray]);
			}

			$part['note'		] = $field(200, $center + $break);
			$part['valid_from'	] = $dpick(200, 'valid_from',$break);
			$part['valid_to'	] = $dpick(200, 'valid_to');

        //------------------------------------------------------------------------------------------------------------  employeeCardTabHistory

			$part = &$columns['employeeCardTabHistory'];
			$part['employee_contract_number'] = $field(200,$center);
			$part = Yang::arrayMerge($part,$columns['employeeCardTab']);
			$removeKeysFrom($part,'

				employee_contract_id

			');
			$part['card_type'] = $combo(200,$center + ['options' =>$optionsFromLookup('card_type')]);


        //------------------------------------------------------------------------------------------------------------  employeeCostTab

			$part = &$columns['employeeCostTab'];
			if($need('cost_id') || $need('cost_center_id'))
			{

				$part[ 'valid_from'				] = $dpick(200,
                    'valid_from',['onchange'=> ['cost_id','cost_center_id']]);
				$part[ 'valid_to'				] = $dpick(200, 'valid_to');
				$part[ 'employee_contract_id'	] = $combo(200,$break + $noadd + ['options' =>$optionsFromSQL($ecSQL)]);

				if ($need('cost_id'))
				{
					$valueField = 'cost_name';
					$dialogWidth = 1;
					if(!is_null(App::getSetting('show_cost_id_in_employee_edit'))) {
						if(App::getSetting('show_cost_id_in_employee_edit') == 1) {
							$valueField = "CONCAT(`cost_id`, ' - ',`cost_name`)";
						}
						$dialogWidth = 2; // #see 20190702-224443
					}

					$part['cost_id'] = $combo(200,$break + [
						'dialog_width' => $dialogWidth,
						'options' => $optionsFromQuery('cost', 'cost_id',$valueField,false, '', '', 'cost_name'),
					]);

				}

				if ($need('cost_center_id'))
				{
					$part['cost_center_id'] = $combo(200,$break + ['options' =>$optionsFromQuery('cost_center')]);
				}
			}

        //------------------------------------------------------------------------------------------------------------  employeeCostTabHistory

			if($need('cost_id') || $need('cost_center_id'))
			{
				$part = &$columns['employeeCostTabHistory'];
				$part['employee_contract_number'] = ['width' => 200, 'export'=> true, 'col_type'=>'ed', 'col_align'=>'center',];
				$part = Yang::arrayMerge($part,$columns['employeeCostTab']);
				$removeKeysFrom($part,'

					valid_from
					valid_to
					employee_contract_id

				');
				$part['valid_from'	] = $dpick(200, 'valid_from');
				$part['valid_to'	] = $dpick(200, 'valid_to');

			}

        //------------------------------------------------------------------------------------------------------------  employeeBaseAbsenceTab

			$baseAbsenceSQL = Dict::getDropdown('BaseAbsenceType', 'base_absence_type_id', 'dict_id', 'ttwa-base', "AND t.`status` = '" . Status::PUBLISHED . "'", true);
			$columns['employeeBaseAbsenceTab'] = [

				'employee_contract_id' => $combo(200,$noadd + ['options' =>$optionsFromSQL($ecSQL)]),
				'base_absence_type_id' => $combo(200,$break + ['options' =>$optionsFromSQL($baseAbsenceSQL)]),
			];

            if (App::getSetting('showAbsenceDayAndHour') === '1')
            {
                $columns['employeeBaseAbsenceTab']['quantity_hour']	= $field(200,$center);
                $columns['employeeBaseAbsenceTab']['quantity']	= $field(200,$center);
            }
            elseif (App::getSetting('showAbsencesInHours') === '1') {
                    $columns['employeeBaseAbsenceTab']['quantity_hour']	= $field(200,$center);
            } else {
                $columns['employeeBaseAbsenceTab']['quantity']	= $field(200,$center);
            }





			$columns['employeeBaseAbsenceTab']['note']			= $field(200,$center);
			$columns['employeeBaseAbsenceTab']['valid_from']	= $dpick(200, 'valid_from',$break);
			$columns['employeeBaseAbsenceTab']['valid_to']		= $dpick(200, 'valid_to');

        //------------------------------------------------------------------------------------------------------------  employeeBaseAbsenceTabHistory

			$part = &$columns['employeeBaseAbsenceTabHistory'];
			$part['employee_contract_number'] = $field(200,$center);
			$part['base_absence_type_name'] = $field(200,$center);
			$part = Yang::arrayMerge($part,$columns['employeeBaseAbsenceTab']);
			$removeKeysFrom($part,'

				employee_contract_id
				base_absence_type_id

			');

        //------------------------------------------------------------------------------------------------------------  employeePersonalCompetencyTab

			$resultSQL = Yang::getParam('customerDbPatchName') == 'bos' ?
				"SELECT '' as id, '' as value UNION " : '';
			$resultSQL .= "
				SELECT al.`lookup_value` AS id, dict.`dict_value` AS value
				FROM `app_lookup` al
				LEFT JOIN `dictionary` dict ON dict.`dict_id` = al.`dict_id`
				WHERE al.`lookup_id`='employee_competency_result'
					AND al.`valid` = 1
					AND dict.`lang` = '$lang'
				ORDER BY value

			";
            $columns['employeePersonalCompetencyTab'] = [
                'employee_contract_id' => $combo(200,$noadd + ['options' =>$optionsFromSQL($ecSQL)])
            ];
            $showCompetencyGroup = (int)App::getSetting('showCompetencyGroup');         
            if ($showCompetencyGroup === 1) {
                $columns['employeePersonalCompetencyTab'] += [
                    'competency_group_id'  => $break + $combo(200,[
                            'options' =>$optionsFromQuery(
                            'competency_group',0,0,true,
                            '',
                            '1',
                            'value'
                        ), 'onchange' => ['competency_id']]),
                    'competency_id'        => $combo(200,[
                        'options' =>$optionsFromQuery(
                        'competency',0,0,false,
                        '',"AND (`competency_group_id` = '{competency_group_id}')",
                        'value'
                    )])
                ];
            } else {
                $columns['employeePersonalCompetencyTab'] += [
                    'competency_id'        => $combo(200,$break + [
                            'options' =>$optionsFromQuery(
                            'competency',0,0,false,
                            '',
                            '1',
                            'value'
                        )])
                ];
            }
            $competencyOrderParams = (array)json_decode(App::getSetting('showEmployeeCompetencyOrder'));
            if ((int)$competencyOrderParams['show_order'] === 1)
            {
                $orderOptions = ['id' => 0, 'value' => ''];
                for ($i=1; $i<=(int)$competencyOrderParams['max_order_number']; $i++)
                {
                    $orderOptions[] = [
                                       'id' => $i,
                                       'value' => $i
                    ];
                }
                $columns['employeePersonalCompetencyTab']['order'] = $combo(100,['options' =>$optionsFromArray($orderOptions)]);
            }
            $columns['employeePersonalCompetencyTab'] += [
				'valid_from'           => $dpick(130, 'valid_from',$break),
				'valid_to'             => $dpick(130, 'valid_to'),
				'level_id'             => $combo(200,$break + [
                        'options' =>$optionsFromQuery(
                        'competency_levels',
                        'level_id',
                        'level_name',false,
                        '',
                        '1',
                        'level_order'
                    )]),
				'result'               => $combo(200,[
                    'grid' =>true,
                    'window' =>true,
                    'options' =>$optionsFromSQL($resultSQL)]),
				'note'                 => $field(200,$center), // wtf, #see 20190702-234520

			];
			if (Yang::getParam('customerDbPatchName') == 'schrack')
			{
				$columns['employeePersonalCompetencyTab']['fs_file_id'] =
					$field(200,['report_width'=>20,'col_type'=>'docs','image_preview'=>true,'dialog_width'=>'2']);
			}

        //------------------------------------------------------------------------------------------------------------  employeePersonalCompetencyTabHistory

			$part = &$columns['employeePersonalCompetencyTabHistory'];
			$part['competency_name'] = $field(400,$center);
            if ($showCompetencyGroup === 1) {
                    $part['competency_group_name'] = $field(200,$center);
            }
			$part = Yang::arrayMerge($part,$columns['employeePersonalCompetencyTab']); unset($part['note']); // reorder
			$part['level_name'] = $field(100,$center);
			$part['note'] = $field(200,$center);
			$part['employee_contract_number'] = $field(200,$center);
			$removeKeysFrom($part,'

				employee_contract_id
				competency_id
				level_id

			');

			if (Yang::getParam('customerDbPatchName') == 'schrack')
			{
				$removeKeysFrom($part,'fs_file_id');
			}
            if ($showCompetencyGroup === 1) 
            {
                $removeKeysFrom($part,'
                            competency_group_id
                ');
            }
            if ((int)$competencyOrderParams['show_order'] === 1)
            {
                $part['order'] = $field(100,$center);
            }

        //------------------------------------------------------------------------------------------------------------  employeeBaseArticleTab

			$baseArticleSQL = "

				SELECT		a.`article_id` AS id,a.`article_name` AS value
				FROM		`article` a
				WHERE		(CURDATE() BETWEEN a.`valid_from` and IFNULL(a.`valid_to`, '{$defaultEnd}')) AND a.`status` = 2
				ORDER BY	a.`article_name`

			";


			$wearingTimeSQL = "

				SELECT		al.`lookup_value` AS id,dict.`dict_value` AS value
				FROM		app_lookup al
				LEFT JOIN	dictionary AS dict ON dict.`dict_id` = al.`dict_id` AND dict.`valid` = '1' AND dict.`lang` = '{$lang}'
				WHERE		al.`lookup_id` = 'wearing_time_type' AND al.`valid` = '1'
				ORDER BY	dict.`dict_value`

			";

			$columns['employeeBaseArticleTab'] = [
				'employee_contract_id'	=> $combo(200,$noadd + ['options' =>$optionsFromSQL($ecSQL)]),
				'base_article_id'		=> $combo(200,$break + ['options' =>$optionsFromSQL($baseArticleSQL)]),
				'wearing_time'			=> $field(200,$center),
				'wearing_time_type'		=> $combo(200,$break + ['options' =>$optionsFromSQL($wearingTimeSQL)]),
				'quantity'              => $field(200,$center),
				'note'                  => $field(200,$center),
				'valid_from'            => $dpick(200, 'valid_from',$break),
				'valid_to'              => $dpick(200),
			];

        //------------------------------------------------------------------------------------------------------------  employeeBaseArticleTabHistory

			$part = &$columns['employeeBaseArticleTabHistory'];
			$part['employee_contract_number'] = $field(200,$center);
			$part['base_article_name'] = $field(200,$center);
			$part = Yang::arrayMerge($part, $columns['employeeBaseArticleTab']);
			$removeKeysFrom($part,'

				employee_contract_id
				base_article_id

			');

        //------------------------------------------------------------------------------------------------------------  employeeTravelCostTab

			$travelCostTypeSql = "
				SELECT		al.`lookup_value` AS id,dict.`dict_value` AS value
				FROM		`app_lookup` al
				LEFT JOIN	`dictionary` AS dict ON dict.`dict_id` = al.`dict_id` AND dict.`valid` = '1' AND dict.`lang` = '{$lang}'
				WHERE		al.`lookup_id` = 'travel_cost_type' AND al.`valid` = '1'
				ORDER BY	dict.`dict_value`
			";

			$reimbursementRateSql = "
				SELECT		al.`lookup_value` AS id,dict.`dict_value` AS value
				FROM		`app_lookup` al
				LEFT JOIN	`dictionary` AS dict ON dict.`dict_id` = al.`dict_id` AND dict.`valid` = '1' AND dict.`lang` = '{$lang}'
				WHERE		al.`lookup_id` = 'reimbursement_rate' AND al.`valid` = '1'
				ORDER BY	dict.`dict_value`
			";

			$reimbursementRates = dbFetchAll($reimbursementRateSql);

			usort($reimbursementRates, function($firstCompare, $secondCompare) {
				$valueA = ($firstCompare['value'] == '-') ? -1 : intval(preg_replace('/[^0-9]/', '', $firstCompare['value']));
				$valueB = ($secondCompare['value'] == '-') ? -1 : intval(preg_replace('/[^0-9]/', '', $secondCompare['value']));

				if ($valueA == $valueB) {
					return 0;
				}
				return ($valueA < $valueB) ? -1 : 1;
			});

            array_unshift($reimbursementRates, '');
				

			$columns['employeeTravelCostTab'] = [
				'employee_contract_id'	=> $combo(200,$noadd + ['options' =>$optionsFromSQL($ecSQL)]),
				'type'					=> $combo(200,$break + ['options' =>$optionsFromSQL($travelCostTypeSql)]),
				'length'				=> $field(200,$break + $center),
				'reimbursement_rate'	=> $combo(200,$center + ['options' =>$optionsFromArray($reimbursementRates)]),
				'reimbursement_percent'	=> $field(200,$break + $center),
				'valid_from'			=> $dpick(200, 'valid_from',$break),
				'valid_to'				=> $dpick(200),
			];

        //------------------------------------------------------------------------------------------------------------  employeeTravelCostTabHistory

			$part = &$columns['employeeTravelCostTabHistory'];
			$part['employee_contract_number'] = $field(200,$center);
			$part = Yang::arrayMerge($part, $columns['employeeTravelCostTab']);
			$removeKeysFrom($part,'
				employee_contract_id
			');

        //------------------------------------------------------------------------------------------------------------  dialogs?

			$columns['importDialog']['fullname']            = $field(200);
			$columns[EmployeeControllerEnum::EMPLOYEE_LOCK_DIALOG][EmployeeControllerEnum::EMPLOYEE_LOCK_DIALOG_DATE_COLUMN_NAME] =
                $dpick(200, '',['default_value'=>date('Y-m-d')]);
            $columns[EmployeeControllerEnum::EMPLOYEE_UNLOCK_DIALOG][EmployeeControllerEnum::EMPLOYEE_UNLOCK_DIALOG_DATE_COLUMN_NAME] =
                $dpick(200, '',['default_value'=>"{$defaultEnd}"]);

        //------------------------------------------------------------------------------------------------------------  employeeChangePositionDialog

			$optSQL = "
				SELECT		employee_position_id AS id, employee_position_name AS value
				FROM		employee_position
				WHERE		status = {$stPublished} AND (CURDATE() BETWEEN valid_from and IFNULL(valid_to,'{$defaultEnd}'))
				ORDER BY	value
			";
			$columns['employeeChangePositionDialog']['position'] = $combo(200,$center + [
				'options' => $optionsFromSQL($optSQL),
				'label_text' => Dict::getValue('employee_position_name'),
				'dialog_width' => '1',
				'report_width' => 20,
			]);

			///
		//------------------------------------------------------------------------------------------------------------  employeeWorkActivityTab

			$waSQL="

				SELECT work_activity_id as id, work_activity_name as value
				FROM work_activity
				WHERE status = {$stPublished}

			";
			$activityCompetencyLevelOn = App::getSetting('activityCompetencyLevelOn');

			$part = &$columns['employeeWorkActivityTab'];
			$part['valid_from'				] = $dpick(200, 'valid_from',['onchange'=>['cafeteria_id']]);
			$part['valid_to'				] = $dpick(200, 'valid_to');
			$part['employee_contract_id'	] = $combo(200,$noadd + $break + ['options' =>$optionsFromSQL($ecSQL)]);
			$part['work_activity_id'		] = $combo(200,$center + $break + [
                    'add' =>true,
                    'options' =>$optionsFromSQL($waSQL)]);
			if ($activityCompetencyLevelOn)
			{
				$part['level_id'			] = $combo(200, [
                    'options' =>$optionsFromQuery(
                    'competency_levels',
                    'level_id',
                    'level_name',false,
                    '',
                    '1',
                    'level_order'
                )]);
			}

        //------------------------------------------------------------------------------------------------------------  employeeWorkActivityTabHistory

			$part = &$columns['employeeWorkActivityTabHistory'];
			$part['employee_contract_number'] = $field(200,$center);
			$part['work_activity_name'] = $field(200,$center);
			if ($activityCompetencyLevelOn)
			{
				$part['level_name'] = $field(100,$center);
			}

			$part = Yang::arrayMerge($part, $columns['employeeWorkActivityTab']);
			$removeKeysFrom($part,'

				employee_contract_id
				work_activity_id
				level_id
			');


        //------------------------------------------------------------------------------------------------------------  employeeDeliveredArticleTab

		if (weHaveModule('ttwa-wwm') && App::hasRight('employee/employeeDeliveredArticleTab', 'view'))
		{
			$warehousesFromModel = WarehouseAuth::getAccessToWarehouse();
			$warehouses = implode("' , '", $warehousesFromModel);

			$warehousSql = '
				SELECT warehouse_name AS value, warehouse_id AS id
				FROM warehouse
				WHERE status = ' . Status::PUBLISHED . " AND warehouse_id IN ('" . $warehouses . "')
				ORDER BY warehouse_name
			";

			$parentCategoryIdSql = '
				SELECT article_category_name as value, article_category_id as id
				FROM article_category
				WHERE status = ' . Status::PUBLISHED . " AND warehouse_id = '{warehouse_id}' AND (parent_category_id = '1' OR parent_category_id IS NULL) AND (CURDATE() BETWEEN valid_from AND valid_to)
				ORDER BY value
			";

			$articleCategoryId = '
				SELECT article_category_name as value, article_category_id as id
				FROM article_category
				WHERE status = ' . Status::PUBLISHED . " AND warehouse_id = '{warehouse_id}' AND parent_category_id = '{parent_category_id}' AND (CURDATE() BETWEEN valid_from AND valid_to) AND parent_category_id IS NOT NULL
				ORDER BY value
			";

			$articleIdSql = '
				SELECT article_name as value, article_id as id
				FROM article
				WHERE status = ' . Status::PUBLISHED . " AND warehouse_id = '{warehouse_id}' AND article_category_id = '{article_category_id}' AND (CURDATE() BETWEEN valid_from AND valid_to)
				ORDER BY value
			";

			$onStatusChange = "
				if ($('#dialogInput_employeeDeliveredArticleTab_toLoad_status').val() == 4) {
					$('.employeeDeliveredArticleTab .printIcon').show();
				} else {
					$('.employeeDeliveredArticleTab .printIcon').hide();
				}
			";

			$part = &$columns['employeeDeliveredArticleTab'];
			$part['warehouse_id'		] = $combo(200, $center + ['add' => false, 'options' => $optionsFromSQL($warehousSql), 'onchange'=> ['parent_category_id', 'article_category_id', 'article_id']]);
			$part['parent_category_id'	] = $combo(200, $center + ['add' => false, 'options' => $optionsFromSQL($parentCategoryIdSql), 'onchange'=> ['article_category_id']]);	//típus
			$part['article_category_id'	] = $combo(200, $center + ['add' => false, 'options' => $optionsFromSQL($articleCategoryId), 'onchange'=> ['article_id']]);	//fajta
			$part['article_id'			] = $combo(200, $center + $break + ['add' => false, 'options' => $optionsFromSQL($articleIdSql)]);	//cikk azonosító / article name
			$part['item_number'			] = $field(200, $center + ['grid' => true, 'window' => true]);	//cikk neve / cikkszám
			$part['quantity'			] = $field(100, $center + $break);
			$part['note'				] = $field(250, $center);
			$part['due_date'			] = $dpick(200);	//esedékesség
			$part['entitled_or_not'		] = $field(200, $center + [
                    'grid' => true, 'window' => true, 'label_text' => Dict::getValue(
                    'entitled'
                )]);
			$part['expired'				] = $field(250, $center + $break);
			$part['expiration_date'		] = $dpick(200, ['grid' => true, 'window' => true]);
			$part['status'				] = $combo(200, $center + [
                    'grid' => true,
                    'window' => true, 'add' => false, 'options' => $optionsFromLookup('article_request_status'), 'customJs' => $onStatusChange]);
			$part['row_id'] = ['width' =>200, 'col_type' => 'hidden'];
			$part['employee_id'] = ['width' =>200, 'col_type' => 'hidden'];

			if (Yang::getParam('customerDbPatchName') == 'schrack')
			{
				$part['signature'		] = $printButton($break + ['style' => 'display:none;']);
			}

            //------------------------------------------------------------------------------------------------------------  employeeDeliveredArticleTabHistory
			$columns['employeeDeliveredArticleTabHistory'] = $columns['employeeDeliveredArticleTab'];
			$part = &$columns['employeeDeliveredArticleTabHistory'];
			$part = Yang::arrayMerge($part, $columns['employeeDeliveredArticleTab']);
			$removeKeysFrom($part,'
				signature
				employee_id
				row_id
			');
        }

		//------------------------------------------------------------------------------------------------------------  dynamic tabs

			$dinamicTabs=Tab::getTabs();

			foreach($dinamicTabs as $tab)
			{
				if(!App::hasRight('employee/dinamicTab_' .$tab['tab_id'], 'view')) continue;

				$tabColumns= TabColumn::getColumns($tab['tab_id']);
				$dinamic_tab_id= 'dinamicTab_' .$tab['tab_id'];
				$part = &$columns[ $dinamic_tab_id];
				$hist = &$columns[ $dinamic_tab_id. 'History'];

				$part = [];
				$hist = [];

				$part['tab_id'] = ['width' =>200, 'col_type' => 'hidden', 'col_align' => 'center', 'default_value' =>$tab['tab_id']];

				if($tab['connect']===Tab::CONNECT_EMPLOYEE)
				{
					$hist['emp_id'] = $field(200,$center + ['label_text'=>Dict::getValue('emp_id')]);
					$part['employee_id'] = $combo(200,$center + $noadd + [
						'label_text' => Dict::getValue('emp_id'),
						'options' => $optionsFromSQL($eSQL),
					]);
				}
				elseif($tab['connect']===Tab::CONNECT_EMPLOYEE_CONTRACT)
				{
					$hist['employee_contract_number'] = $field(200,$center + ['label_text'=>Dict::getValue(
                            'employee_contract_number'
                        )]);
					$part['employee_contract_id'] = $combo(200,$center + $noadd + [
						'label_text' => Dict::getValue('employee_contract_number'),
						'options' => $optionsFromSQL($ecSQL),
					]);
				}

				foreach($tabColumns as $tabColumn)
				{
					if($tabColumn['type']===TabColumn::TYPE_COMBO)
					{
						$tabColumnSQL="

							SELECT IFNULL(d.dict_value,t.value) as id, IFNULL(d.dict_value,t.value) as value
							FROM tab_column_combo_item t
							LEFT JOIN dictionary d ON d.dict_id = t.value
							WHERE 1
								AND t.status     = '{$stPublished}'
								AND t.tab_id     = '{$tab['tab_id']}'
								AND t.column_id  = '{$tabColumn['column_id']}'
								AND IF(d.lang IS NOT NULL, d.lang='{$lang}', 1)
							ORDER BY value;

						";

						$part[$tabColumn['column_id']] = $combo(200,$center + [

							'label_text' => Dict::getValue($tabColumn['dict_id']),
							'default_value' => TabColumnComboItem::getDefaultValue($tab['tab_id'], $tabColumn['column_id']),
							'options' => $optionsFromSQL($tabColumnSQL,$nullElement),

						]);

					}
					else
					{
						$part[$tabColumn['column_id']] = $field(200,$center + [

							'dPicker'      => ($tabColumn['type']===TabColumn::TYPE_DATE),
							'report_width' => 20,
							'dialog_width' => '1',
							'label_text'   => Dict::getValue($tabColumn['dict_id'])

						]);
					}
				}

				if((int)$tab['valid'])
				{
					$part['valid_from' ] = $dpick(200, 'valid_from',['label_text'=>Dict::getValue('valid_from')] + ['onchange'=>['employee_id']] + $break);
					$part['valid_to'   ] = $dpick(200, 'valid_to',['label_text'=>Dict::getValue('valid_to')]);
				}

				$hist=Yang::arrayMerge($hist, $part);
				$removeKeysFrom($hist,'

					tab_id
					employee_id
					employee_contract_id

				');
			}

        //------------------------------------------------------------------------------------------------------------  end

		AnyCache::set(
            'baseColumns',$columns,linesOf(
            '

			ac_access_level
			app_lookup
			article
			article_category
			dictionary
			employee
			employee_contract
			employee_position
			option
			tab_column_combo_item
			warehouse
			work_activity

		'
        ));

		return $columns;
	}

	/**
	 * Visszaadja az grid, az ablakok, a tabok, és a feltöltő form oszlop-konfigurációit.
	 */
	public function columns() {
		$columns = $this->baseColumns();
		$columns['uploadDialog'] = $this->getUploadDialogColumns($columns);

		return $columns;
	}

	/**
	 * A dhtmlxGrid wizard alapján összeállít egy összevont feltöltő formot.
	 * Az SQL-eket és a onchange event-eket a megváltozott felépítés alapján módosítja.
	 * $allValids - ha szükség van az összes tabon megjelenő valid_from, valid_to-ra
	 */
	private function getUploadDialogColumns($baseColumns = [], $allValids = false): array {
		$uplDialogColumns = [];

		$wizards = $this->wizards();
		$currentWizards = [];
		foreach ($wizards['dhtmlxGrid'] as $wizard) {
			$currentWizards[] = $wizard['contentId'];
		}

		$validFromChanges = [];

		$baseColumns = $this->rearrangeArrayData($baseColumns,'employeeContractTab');

		foreach ($baseColumns as $tabId => $baseColumn) {
			if (in_array($tabId, $currentWizards)) {
				foreach ($baseColumn as $column => $props) {
				    if($tabId == 'employeeDocsTab') {
				        continue;
                    }

					/**
					 * Összeszedi a valid_from onchange eseményeit, valamint
					 * valid_from, valid_to oszlopok esetén továbblép a ciklus.
					 */
					if (in_array($column,['valid_from', 'valid_to'])) {
						if ($column === 'valid_from' && isset($props['onchange'])) {
							foreach ($props['onchange'] as $onchange) {
								$validFromChanges[] = $tabId. '__' .$onchange;
							}
						}

						if(!$allValids) { continue; }
					}

					/**
					 * Megváltoztatja az oszlopoknak a neveit az onchange tömbben az új elnevezés alapján.
					 */
					if (isset($props['onchange'])) {
						$change = [];
						foreach ($props['onchange'] as $onchange) {
							$change[] = $tabId. '__' .$onchange;
						}

						$props['onchange'] = $change;
					}

					/**
					 * Minden korábbi valid_from onchange eventjét hozzáadja az
					 * ec_valid_from oszlophoz.
					 */
					if ($tabId === 'employeeContractTab' && $column === 'ec_valid_from') {
						if (!isset($props['onchange'])) $props['onchange'] = [];

						foreach ($validFromChanges as $validFromChange) {
							$props['onchange'][] = $validFromChange;
						}
					}

					/**
					 * SQL módosítások a megváltozott oszlopelnevezések miatt
					 */
					if (isset($props['options']['sql'])) {
						// replace all valid_from / valid_to to ec_valid_from / ec_valid_to
						$props['options']['sql'] = str_replace(['{valid_from}','{valid_to}',], ['{ec_valid_from}','{ec_valid_to}',], $props['options']['sql']);

						// replace everything between {...} to {$tabId__...}
						$props['options']['sql'] = preg_replace('/(\{)(.*?)(})/', '{'.$tabId.'__$2}', $props['options']['sql']);

						// all ec_valid_from inputs coming from employeeContractTab
						$props['options']['sql'] = preg_replace('/(\{)(.*?)(__ec_valid_from})/', '{employeeContractTab__ec_valid_from}', $props['options']['sql']);
						$props['options']['sql'] = preg_replace('/(\{)([^{]+?)(__ec_valid_to})/', '{employeeContractTab__ec_valid_to}', $props['options']['sql']);
					}

					$uplDialogColumns[$tabId. '__' .$column] = $props;
				}
			}
		}

		return $uplDialogColumns;
	}

	private function group_SQL(): string
    {
		$SQL = "
			SELECT
				employee_group_config.`group_id` AS id,
				gr_dict.`dict_value` AS value
			FROM
				`employee_group_config`
			LEFT JOIN
				`dictionary` gr_dict ON
					gr_dict.`dict_id` = employee_group_config.`dict_id`
						AND gr_dict.`lang` = '".Dict::getLang()."'
			WHERE
				employee_group_config.`status` = ".Status::PUBLISHED. '
		';

		if(!App::hasRight('employee/employeeGroupTab', 'all') && !App::hasRight(
                'employee/employeeGroupTab',
                'company_org_group1'
            ))
		{
			$SQL.="AND employee_group_config.`group_id`!='company_org_group1_id'
			";
		}
		if(!App::hasRight('employee/employeeGroupTab', 'all') && !App::hasRight(
                'employee/employeeGroupTab',
                'company_org_group2'
            ))
		{
			$SQL.="AND employee_group_config.`group_id`!='company_org_group2_id'
			";
		}
		if(!App::hasRight('employee/employeeGroupTab', 'all') && !App::hasRight(
                'employee/employeeGroupTab',
                'company_org_group3'
            ))
		{
			$SQL.="AND employee_group_config.`group_id`!='company_org_group3_id'
			";
		}
		if(!App::hasRight('employee/employeeGroupTab', 'all') && !App::hasRight(
                'employee/employeeGroupTab',
                'workgroup'
            ))
		{
			$SQL.="AND employee_group_config.`group_id`!='workgroup_id'
			";
		}
		if(!App::hasRight('employee/employeeGroupTab', 'all') && !App::hasRight('employee/employeeGroupTab', 'unit'))
		{
			$SQL.="AND employee_group_config.`group_id`!='unit_id'
			";
		}
		if(!App::hasRight('employee/employeeGroupTab', 'all') && !App::hasRight('employee/employeeGroupTab', 'cost'))
		{
			$SQL.="AND employee_group_config.`group_id`!='cost_id'
			";
		}
		if(!App::hasRight('employee/employeeGroupTab', 'all') && !App::hasRight(
                'employee/employeeGroupTab',
                'cost_center'
            ))
		{
			$SQL.="AND employee_group_config.`group_id`!='cost_center_id'
			";
		}

		if (!App::hasRight('employee/employeeGroupTab', 'all') && !App::hasRight(
                'employee/employeeGroupTab',
                'employee_position'
            ))
		{
			$SQL .= "AND `employee_group_config`.`group_id` != 'employee_position_id'";
		}
		if (!App::hasRight('employee/employeeGroupTab', 'all') && !App::hasRight(
                'employee/employeeGroupTab',
                'job_tasks'
            ))
		{
			$SQL .= "AND `employee_group_config`.`group_id` != 'job_task_id'";
		}

		return $SQL;
	}

        private function groupValue_SQL(): string
        {
			$filter = Yang::session('employeeControllerFilter');

			$validDateCache = AnyCache::key('employeeControllerValidDate',[userID()]);
			if(AnyCache::has($validDateCache) && AnyCache::get($validDateCache) != $filter['valid_date']) {
				$validDate = AnyCache::get($validDateCache);
			} else {
				$validDate = $filter['valid_date'] ?? date('Y-m-d');
				AnyCache::set($validDateCache,$validDate);
			}
            $workgroupLimit = '';
			if(App::hasRight('employee', 'workgroupLimitation'))
			{
				$workgroupLimit = 'work_order in (SELECT work_order FROM workgroup WHERE workgroup_id IN (';
				$workgroupLimitMain = '';
				$workgroupLimitConditions = "employee_id = '{row_id_p1}' AND (CURDATE() BETWEEN employee_contract.valid_from AND employee_contract.valid_to) AND employee_contract.status = " . Status::PUBLISHED . ')';
				if(EmployeeGroupConfig::isActiveGroup('workgroup_id'))
				{
					$workgroupLimitMain .= "SELECT DISTINCT group_value
										FROM employee_group
										LEFT JOIN employee_contract ON employee_group.employee_contract_id = employee_contract.employee_contract_id
										WHERE
												group_id = 'workgroup_id'
											AND employee_group.status = " . Status::PUBLISHED . '
											AND CURDATE() BETWEEN employee_group.valid_from AND employee_group.valid_to
											AND ';
				} else {
					$workgroupLimitMain .= 'SELECT workgroup_id FROM employee_contract WHERE ';
				}

				// tudom, hogy ronda, majd akinek nem tetszik csinál jobbat
				$workgroupLimit .= $workgroupLimitMain . $workgroupLimitConditions . '
									OR (' . $workgroupLimitMain . $workgroupLimitConditions . ' IS NULL
									)
									AND status = ' . Status::PUBLISHED . '
									AND ';
			}
			$eCond = " AND e.status = 2 AND
			'{valid_from}' BETWEEN e.`valid_from` and IFNULL(e.`valid_to`,'".App::getSetting('defaultEnd')."')
			";

			$wgWhere = " AND (company_id = (SELECT e.company_id FROM employee e WHERE e.employee_id = '{row_id_p1}'" . $eCond . ") OR company_id = 'ALL' OR '{row_id_p1}' = '')
						 AND (payroll_id = (SELECT e.payroll_id FROM employee e WHERE e.employee_id = '{row_id_p1}'" . $eCond . ") OR payroll_id = 'ALL' OR '{row_id_p1}' = '')";

            if(App::hasRight('employeecontrol', 'workgroupbyuserright')) {
                $workgroupLimit = '';
                $companyIds = array_column(Approver::getRelatedValuesByApprover(userID(), 'employeeManagement', 'company_id'),'related_value');
                $wgWhere  = ' 
                        1 = 2 /*Company is empty!*/
                    ';
                
                if ($companyIds > 0) {
                    $wgWhere = " AND (company_id IN ('" . join("','", $companyIds) . "'))";
                }
                
                if (($companyIds > 0) && in_array('ALL', $companyIds)) {
                    $wgWhere = ' AND company_id IS NOT NULL';
                }
            }

            if(App::hasRight('employeecontrol', 'noworkgrouplimitation')) {
                $workgroupLimit = '';
                $wgWhere  = '';
            }
            
            $groupValueSQL = "
			(
				SELECT
					workgroup.`workgroup_id` AS id,
					workgroup.`workgroup_name` AS value
				FROM
					`workgroup`
				WHERE
					$workgroupLimit
						workgroup.`status` = ".Status::PUBLISHED."
					$wgWhere
					AND '{group_id}' = 'workgroup_id'
					AND ('$validDate' BETWEEN workgroup.`valid_from` and IFNULL(workgroup.`valid_to`,'".App::getSetting(
                    'defaultEnd'
                )."'))
			)
			UNION
			(
				SELECT
					`unit_id` AS id,
					`unit_name` AS value
				FROM
					`unit`
				WHERE
						unit.`status` = ".Status::PUBLISHED."
					AND '{group_id}' = 'unit_id'
					AND ('$validDate' BETWEEN `valid_from` and IFNULL(`valid_to`,'".App::getSetting('defaultEnd')."'))
			)
			UNION
			(
				SELECT
					`company_org_group_id` AS id,
					`company_org_group_name` AS value
				FROM
					`company_org_group1`
				WHERE
						company_org_group1.`status` = ".Status::PUBLISHED."
					AND '{group_id}' = 'company_org_group1_id'
					AND ('$validDate' BETWEEN `valid_from` and IFNULL(`valid_to`,'".App::getSetting('defaultEnd')."'))
			)
			UNION
			(
				SELECT
					`company_org_group_id` AS id,
					`company_org_group_name` AS value
				FROM
					`company_org_group2`
				WHERE
						company_org_group2.`status` = ".Status::PUBLISHED."
					AND '{group_id}' = 'company_org_group2_id'
					AND ('$validDate' BETWEEN `valid_from` and IFNULL(`valid_to`,'".App::getSetting('defaultEnd')."'))
			)
			UNION
			(
				SELECT
					`company_org_group_id` AS id,
					`company_org_group_name` AS value
				FROM
					`company_org_group3`
				WHERE
						company_org_group3.`status` = ".Status::PUBLISHED."
					AND '{group_id}' = 'company_org_group3_id'
					AND ('$validDate' BETWEEN `valid_from` and IFNULL(`valid_to`,'".App::getSetting('defaultEnd')."'))
			)
			UNION
			(
				SELECT
					`cost_id` AS id,
					`cost_name` AS value
				FROM
					`cost`
				WHERE
						cost.`status` = ".Status::PUBLISHED."
					AND '{group_id}' = 'cost_id'
					AND ('$validDate' BETWEEN `valid_from` and IFNULL(`valid_to`,'".App::getSetting('defaultEnd')."'))
			)
			UNION
			(
				SELECT
					`cost_center_id` AS id,
					`cost_center_name` AS value
				FROM
					`cost_center`
				WHERE
						cost_center.`status` = ".Status::PUBLISHED."
					AND '{group_id}' = 'cost_center_id'
					AND ('$validDate' BETWEEN `valid_from` and IFNULL(`valid_to`,'".App::getSetting('defaultEnd')."'))
			)
			UNION
			(
				SELECT
					`employee_position_id` AS id,
					`employee_position_name` AS value
				FROM
					`employee_position`
				WHERE
						`employee_position`.`status` = ".Status::PUBLISHED."
					AND '{group_id}' = 'employee_position_id'
					AND ('$validDate' BETWEEN `valid_from` AND IFNULL(`valid_to`,'".App::getSetting('defaultEnd')."'))
			)";
			if (weHaveModule('ttwa-csm'))
			{
				$groupValueSQL .= '
					UNION
					(
						SELECT
							`job_task_id` AS id,
							`job_task_name` AS value
						FROM
							`job_tasks`
						WHERE
								`job_tasks`.`status` = ' .Status::PUBLISHED."
							AND '{group_id}' = 'job_task_id'
							AND ('$validDate' BETWEEN `valid_from` AND IFNULL(`valid_to`,'".App::getSetting(
                        'defaultEnd'
                    )."'))
					)
				";
			}
            return $groupValueSQL.' ORDER BY value ASC';
        }

	/*
	 * A segédfüggvény egy asszociatív tömb tetszőleges elemét a tömb elejére helyezi.
	 * @param array $array
	 * @param string $key_to_rearrange
	 * @return array $modified_array
	 */

	private function rearrangeArrayData($array,$key_to_rearrange): array
	{
		$temp_array = [];

		$temp_array[$key_to_rearrange] = $array[$key_to_rearrange];
		unset($array[$key_to_rearrange]);
        return array_merge($temp_array,$array);
	}

	private function getFileFieldOptions(string $tabName): array 
	{
		$baseOptions = [
			'report_width' => 20,
			'col_type' => 'docs',
			'image_preview' => true,
			'dialog_width' => '2'
		];

		if (App::getSetting("showDownloadButtonOnEmployeeDocsTab")) { 
			$downloadButtonOptions = [
				'showDownloadButton' => true,
				'loadDefaultDownloadButton' => false,
				'customJs' => "
					function checkImageAndEnableButton() {
						var prefix = 'dialogInput_{$tabName}_fs_file_id';
						var imagePreview = $('#' + prefix + '_imagePreview embed');
						var downloadButton = $('#' + prefix + '_downloadFileButton');
						
						if (imagePreview.length > 0) {
							downloadButton.removeClass('disabled');
							
							downloadButton.off('click').on('click', function() {
								var src = imagePreview.attr('src');
								var fileId = src.split('file_id=')[1];
								
								window.open('/filestorage/showDocsFile?file_id=' + fileId);
							});
						} else {
							downloadButton.addClass('disabled');
							downloadButton.off('click');
						}
					}
					
					$(document).ready(function() {
						checkImageAndEnableButton();
					});
					
					const observer = new MutationObserver(function(mutations) {
						checkImageAndEnableButton();
					});
					
					observer.observe(
						document.getElementById('dialogInput_{$tabName}_fs_file_id_imagePreview'),
						{ 
							childList: true,
							subtree: true 
						}
					);
				"
			];
			
			return array_merge($baseOptions, $downloadButtonOptions);
		}

		return $baseOptions;
	}
}
