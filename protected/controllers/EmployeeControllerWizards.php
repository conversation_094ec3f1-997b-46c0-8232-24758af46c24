<?php
trait EmployeeControllerWizards
{
	protected function getBigFormModeWizard() {
		return [
			'uploadDialog' => 'dhtmlxGrid',
		];
	}

	protected function getBigFormModeColors() {
		$ret = [
			'uploadDialog' => [
				"#81C784",
				"#FFB74D",
				"#FF8A65",
				"#9575CD",
				"#4DD0E1",
			],
		];

		return $ret;
	}

	public function wizards()
	{
		$acEnable = useExperimental("enable caching of EmployeeControllerWizards");
		$acVerify = useExperimental("verify cache of EmployeeControllerWizards");

		if($acEnable) {
			$acKey = AnyCache::key("EmployeeControllerWizards::wizard",[userID()]);
			$acHit = AnyCache::has($acKey);
			if($acHit) if(!$acVerify) return AnyCache::get($acKey);
		}

		$wizards = [];
		$stPublished = Status::PUBLISHED;
		$defaultEnd = App::getSetting('defaultEnd');
		$lang = Dict::getLang();

		if (App::hasRight("employee/employeeTab", "view") || App::isRootSessionEnabled()) {

			$wizards["dhtmlxGrid"][] =
					[
						"contentId" => "employeeTab",
						"contentTitle" => Dict::getValue('tab_employeetabs_employee'),
						"tabSQL" => "
							SELECT
								employee.`row_id`,
								employee.`title`,
								employee.`first_name`,
								employee.`last_name`,
								employee.`nameofbirth`,
								employee.`tax_number`,
								d.dict_value as gender,
								employee.`emp_id`,
								company.`company_name` AS company_id,
								payroll.`payroll_name` AS payroll_id,
								unit.`unit_name` AS unit_id,
								employee.`valid_from`,
								employee.`valid_to`,
								company_org_group1.`company_org_group_name` AS company_org_group1_id,
								company_org_group2.`company_org_group_name` AS company_org_group2_id,
								company_org_group3.`company_org_group_name` AS company_org_group3_id,
								employee.`employee_image`
							FROM
								`employee`
							LEFT JOIN
								`company` ON
									company.`company_id` = employee.`company_id`
										AND (employee.`valid_from` BETWEEN company.`valid_from` AND IFNULL(company.`valid_to`, '$defaultEnd'))
											AND company.`status` = $stPublished
							LEFT JOIN
								`payroll` ON
									payroll.`payroll_id` = employee.`payroll_id`
										AND (employee.`valid_from`BETWEEN payroll.`valid_from` AND IFNULL(payroll.`valid_to`, '$defaultEnd'))
											AND payroll.`status` = $stPublished
							LEFT JOIN
								`unit` ON
									unit.`unit_id` = employee.`unit_id`
										AND (employee.`valid_from` BETWEEN unit.`valid_from` AND IFNULL(unit.`valid_to`, '$defaultEnd'))
											AND unit.`status` = $stPublished
							LEFT JOIN
								`company_org_group1` ON
									company_org_group1.`company_org_group_id` = employee.`company_org_group1_id`
										AND (employee.`valid_from` BETWEEN company_org_group1.`valid_from` AND IFNULL(company_org_group1.`valid_to`, '$defaultEnd'))
											AND company_org_group1.`status` = $stPublished
							LEFT JOIN
								`company_org_group2` ON
									company_org_group2.`company_org_group_id` = employee.`company_org_group2_id`
										AND (employee.`valid_from` BETWEEN company_org_group2.`valid_from` AND IFNULL(company_org_group2.`valid_to`, '$defaultEnd'))
											AND company_org_group2.`status` = $stPublished
							LEFT JOIN
								`company_org_group3` ON
									company_org_group3.`company_org_group_id` = employee.`company_org_group3_id`
										AND (employee.`valid_from` BETWEEN company_org_group3.`valid_from` AND IFNULL(company_org_group3.`valid_to`, '$defaultEnd'))
											AND company_org_group3.`status` = $stPublished
							LEFT JOIN dictionary d ON
											d.dict_id=employee.`gender`
											and d.valid = 1
											and d.lang = '{$lang}'
							WHERE
								employee.`employee_id` = '{row_id_p1}'
									AND employee.`status` = $stPublished
							ORDER BY
								employee.`valid_from` DESC, employee.`valid_to` DESC
						",
						"modelName" => "Employee",
						"primaryKey" => "row_id",
					];
		}

		if (App::hasRight("employee/employeeContractTab", "view") || App::isRootSessionEnabled()) {


			if (App::getSetting("ec_end_on_employeecontracttab") == 1)
			{
				$ecFieldAddition =  "employee_contract.ec_end_reason,dictionary.dict_value as ec_end_type,";

				$extraJoins = /** @lang MySQL */
					<<<JOINS
							LEFT JOIN
								`app_lookup` ON
									app_lookup.`lookup_value` = employee_contract.`ec_end_type`
										AND app_lookup.`valid` = 1
											AND (
												app_lookup.lookup_id='ec_end_type'
											)
							LEFT JOIN
								`dictionary` ON
									dictionary.`dict_id` = app_lookup.dict_id
										AND dictionary.`valid` = 1
											AND (
												dictionary.lang ='{$lang}'
											)
JOINS;

			}
			else
			{
				$extraJoins = "";
				$ecFieldAddition = "";
			}

			$wizards["dhtmlxGrid"][] =
					[
						"contentId" => "employeeContractTab",
						"contentTitle" => Dict::getValue('tab_employeetabs_employeecontract'),

						"tabSQL" => /** @lang MySQL */
						"
							SELECT
								employee_contract.`row_id`,
								employee_contract.`employee_contract_number`,
								`dict_contract_type`.`dict_value` AS `employee_contract_type`,
    							`dict_wage_type`.`dict_value` AS `wage_type`,
								workgroup.`workgroup_name` AS workgroup_id,
								employee_contract.`daily_worktime`,
								employee_contract.`note`,
								{$ecFieldAddition}
								employee_contract.`ec_valid_from`,
								employee_contract.`ec_valid_to`,
								employee_contract.`valid_from`,
								employee_contract.`valid_to`,
								employee_position.`employee_position_name` AS employee_position_id
							FROM
								`employee`
							LEFT JOIN
								`employee_contract` ON
									employee_contract.`employee_id` = employee.`employee_id`
										AND employee_contract.`status` = {$stPublished}
											AND (
												employee.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'{$defaultEnd}')
													OR
												IFNULL(employee.`valid_to`,'{$defaultEnd}') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'{$defaultEnd}')
													OR
												(
													employee_contract.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'{$defaultEnd}')
														AND
													IFNULL(employee_contract.`valid_to`,'{$defaultEnd}') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'{$defaultEnd}')
												)
											)
							{$extraJoins}
							LEFT JOIN `app_lookup` AS `al_contract_type` ON
								`al_contract_type`.`lookup_value` = `employee_contract`.`employee_contract_type`
									AND `al_contract_type`.`valid` = 1
									AND `al_contract_type`.`lookup_id` = 'employee_contract_type'
                            LEFT JOIN `app_lookup` AS `al_wage_type` ON
								`al_wage_type`.`lookup_value` = `employee_contract`.`wage_type`
									AND `al_wage_type`.`valid` = 1
									AND `al_wage_type`.`lookup_id` = 'wage_type'
							LEFT JOIN `dictionary` AS `dict_contract_type` ON
								`dict_contract_type`.`dict_id` = `al_contract_type`.`dict_id`
									AND `dict_contract_type`.`valid` = 1
									AND `dict_contract_type`.`lang` = '{$lang}'
                            LEFT JOIN `dictionary` AS `dict_wage_type` ON
								`dict_wage_type`.`dict_id` = `al_wage_type`.`dict_id`
									AND `dict_wage_type`.`valid` = 1
									AND `dict_wage_type`.`lang` = '{$lang}'
							LEFT JOIN
								`workgroup` ON
									workgroup.`workgroup_id` = employee_contract.`workgroup_id`
										AND (employee_contract.`valid_from` BETWEEN workgroup.`valid_from` AND IFNULL(workgroup.`valid_to`, '{$defaultEnd}'))
											AND workgroup.`status` = {$stPublished}
							LEFT JOIN
								`employee_position` ON
									employee_position.`employee_position_id` = employee_contract.`employee_position_id`
										AND (employee_contract.`valid_from` BETWEEN employee_position.`valid_from` AND IFNULL(employee_position.`valid_to`, '{$defaultEnd}'))
											AND employee_position.`status` = {$stPublished}
							WHERE
								employee.`employee_id` = '{row_id_p1}'
									AND employee.`status` = {$stPublished}
							GROUP BY
								employee_contract.`row_id`
							ORDER BY
								employee_contract.`valid_from` DESC, employee_contract.`valid_to` DESC
						",
						"modelName" => "EmployeeContract",
						"primaryKey" => "row_id",
					];
		}

		if (App::hasRight("employee/employeeSalaryTab", "view") || App::isRootSessionEnabled()) {
			$option1_2_text = "employee_salary.es_option1,
								employee_salary.es_option2,";
			if (Yang::getParam('customerDbPatchName') == 'schrack') {
				$option1_2_text = "FORMAT(employee_salary.es_option1, 0, 'hu_HU') AS es_option1,
									FORMAT(employee_salary.es_option2, 0, 'hu_HU') AS es_option2,";
			}

			$wizards["dhtmlxGrid"][] =
					[
						"contentId" => "employeeSalaryTab",
						"contentTitle" => Dict::getValue('tab_employeetabs_employeesalary'),
						"tabSQL" => "
							SELECT
								employee_salary.row_id,
								employee_salary.employee_contract_id,
								FORMAT(employee_salary.personal_month_salary, 0, 'hu_HU') AS personal_month_salary,
								FORMAT(employee_salary.personal_hour_salary, 0, 'hu_HU') AS personal_hour_salary,
								IF(employee_salary.shift=0,'".Dict::getValue("no")."','".Dict::getValue("yes")."') as shift,
								employee_salary.shift_bonus_in_percent,
								{$option1_2_text}
								employee_salary.es_option3,
								employee_salary.es_option4,
								employee_salary.es_option5,
								employee_salary.es_option6,
								employee_salary.es_option7,
								employee_salary.es_option8,
								employee_salary.es_option9,
								employee_salary.es_option10,
								employee_salary.es_option11,
								employee_salary.es_option12,
								employee_salary.es_option13,
								employee_salary.es_option14,
								employee_salary.es_option15,
								employee_salary.es_option16,
								employee_salary.es_option17,
								employee_salary.es_option18,
								employee_salary.es_option19,
								employee_salary.es_option20,
								employee_salary.es_option21,
								employee_salary.es_option22,
								employee_salary.es_option23,
								employee_salary.es_option24,
								employee_salary.es_option25,
								employee_salary.es_option26,
								employee_salary.es_option27,
								employee_salary.es_option28,
								employee_salary.es_option29,
								employee_salary.es_option30,
								employee_salary.es_option31,
								employee_salary.es_option32,
								employee_salary.es_option33,
								employee_salary.es_option34,
								employee_salary.es_option35,
								employee_salary.es_option36,
								employee_salary.es_option37,
								employee_salary.es_option38,
								employee_salary.es_option39,
								employee_salary.es_option40,
								employee_salary.es_option41,
								employee_salary.es_option42,
								employee_salary.es_option43,
								employee_salary.es_option44,
								employee_salary.es_option45,
								employee_salary.es_option46,
								employee_salary.es_option47,
								employee_salary.es_option48,
								employee_salary.es_option49,
								employee_salary.es_option50,
								employee_salary.es_option51,
								employee_salary.es_option52,
								employee_salary.es_option53,
								employee_salary.es_option54,
								employee_salary.es_option55,
								employee_salary.es_option56,
								employee_salary.es_option57,
								employee_salary.note,
								employee_salary.valid_from,
								employee_salary.valid_to,
								employee_contract.`employee_contract_number`
							FROM `employee`
							LEFT JOIN `employee_contract` ON
									employee_contract.`employee_id` = employee.`employee_id`
								AND employee_contract.`status` = $stPublished
								AND employee.`valid_from`<=IFNULL(employee_contract.`valid_to`,'$defaultEnd')
								AND IFNULL(employee.`valid_to`,'$defaultEnd')>=employee_contract.`valid_from`
							LEFT JOIN `employee_salary` ON
									employee_salary.`employee_contract_id` = employee_contract.`employee_contract_id`
								AND employee_salary.`status` = $stPublished
								AND employee_contract.`valid_from`<=IFNULL(employee_salary.`valid_to`,'$defaultEnd')
								AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')>=employee_salary.`valid_from`
							WHERE
								employee.`employee_id` = '{row_id_p1}'
									AND employee.`status` = $stPublished
										AND employee_salary.`row_id` IS NOT NULL
							GROUP BY
								employee_salary.`row_id`
							ORDER BY
								employee_salary.`valid_from` DESC, employee_salary.`valid_to` DESC
						",
						"modelName" => "EmployeeSalary",
						"primaryKey" => "row_id",
					];
		}

		if (App::hasRight("employee/employeeCafeteriaTab", "view") || App::isRootSessionEnabled()) {
			$SQL = "";
			$SQLJoin = "";
			if(Yang::getParam('customerDbPatchName') == 'schenker') {
				$SQL = ",
					ecaf.`cafeteria`,
					ecaf.`benefit_groups`,
					ecaf.`account_number_accommodation`,
					ecaf.`account_number_hospitality`,
					ecaf.`account_number_leisure`,
					dict.dict_value as benefit_groups_name
				";
				$SQLJoin = "
				LEFT JOIN `app_lookup` al ON
						al.lookup_id = 'benefit_groups'
					AND al.lookup_value = ecaf.`benefit_groups`
					AND al.valid = 1
				LEFT JOIN `dictionary` dict ON
						dict.dict_id = al.dict_id
					AND dict.valid = 1
					AND dict.lang = '{$lang}'
				";
			}
			$wizards["dhtmlxGrid"][] =
					[
						"contentId" => "employeeCafeteriaTab",
						"contentTitle" => Dict::getValue('tab_employeetabs_employeecafeteria'),
						"tabSQL" => "
							SELECT
								ecaf.row_id,
								ecaf.employee_contract_id,
								caf.`cafeteria_name` as cafeteria_id,
								ecaf.note,
								ecaf.valid_from,
								ecaf.valid_to,
								employee_contract.`employee_contract_number`
								{$SQL}
							FROM `employee`
							LEFT JOIN `employee_contract` ON
									employee_contract.`employee_id` = employee.`employee_id`
								AND employee_contract.`status` = $stPublished
								AND employee.`valid_from`<=IFNULL(employee_contract.`valid_to`,'$defaultEnd')
								AND IFNULL(employee.`valid_to`,'$defaultEnd')>=employee_contract.`valid_from`
							LEFT JOIN `employee_cafeteria` ecaf ON
									ecaf.`employee_contract_id` = employee_contract.`employee_contract_id`
								AND ecaf.`status` = $stPublished
								AND employee_contract.`valid_from`<=IFNULL(ecaf.`valid_to`,'$defaultEnd')
								AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')>=ecaf.`valid_from`
							LEFT JOIN `cafeteria` caf ON
									caf.`cafeteria_id` = ecaf.`cafeteria_id`
								AND caf.`status` = $stPublished
								AND ecaf.`valid_from`<=IFNULL(caf.`valid_to`,'$defaultEnd')
								AND IFNULL(ecaf.`valid_to`,'$defaultEnd')>=caf.`valid_from`
							{$SQLJoin}
							WHERE
								employee.`employee_id` = '{row_id_p1}'
									AND employee.`status` = $stPublished
										AND ecaf.`row_id` IS NOT NULL
							GROUP BY
								ecaf.`row_id`
							ORDER BY
								ecaf.`valid_from` DESC, ecaf.`valid_to` DESC
						",
						"modelName" => "EmployeeCafeteria",
						"primaryKey" => "row_id",
					];
		}

		if (App::hasRight("employee/employeeAddressTab", "view") || App::isRootSessionEnabled()) {
			$wizards["dhtmlxGrid"][] =
					[
						"contentId" => "employeeAddressTab",
						"contentTitle" => Dict::getValue('tab_employeetabs_employeeaddress'),
						"tabSQL" => "
							SELECT
								employee_address.`row_id`,
								employee_address.`employee_id`,
								employee.`emp_id`,
								employee_address.`address_card_number`,
								employee_address.`full_address`,
								employee_address.`country`,
								employee_address.`zip_code`,
								employee_address.`city`,
								employee_address.`district`,
								employee_address.`public_place_name`,
								employee_address.`public_place_type`,
								employee_address.`house_number`,
								employee_address.`building`,
								employee_address.`staircase`,
								employee_address.`floor`,
								employee_address.`door`,
								employee_address.`res_full_address`,
								employee_address.`res_zip_code`,
								employee_address.`res_city`,
								employee_address.`res_district`,
								employee_address.`res_public_place_name`,
								employee_address.`res_public_place_type`,
								employee_address.`res_house_number`,
								employee_address.`res_building`,
								employee_address.`res_staircase`,
								employee_address.`res_floor`,
								employee_address.`res_door`,
								employee_address.`note`,
								employee_address.`valid_from`,
								employee_address.`valid_to`
							FROM `employee`
							LEFT JOIN `employee_contract` ON
									employee_contract.`employee_id` = employee.`employee_id`
								AND employee_contract.`status` = $stPublished
								AND (employee.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
									OR IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
									OR (employee_contract.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										AND IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')))
							LEFT JOIN `employee_address` ON
									employee_address.`employee_id`=employee.`employee_id`
								AND employee_address.`status` = $stPublished
								AND (employee_address.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										OR IFNULL(employee_address.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										OR (employee.`valid_from` BETWEEN employee_address.`valid_from` AND IFNULL(employee_address.`valid_to`,'$defaultEnd')
											AND IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_address.`valid_from` AND IFNULL(employee_address.`valid_to`,'$defaultEnd')))
							WHERE
									employee.`employee_id` = '{row_id_p1}'
								AND employee.`status` = $stPublished
							GROUP BY
								employee_address.`row_id`
							ORDER BY
								employee_address.`valid_from` DESC, employee_address.`valid_to` DESC
						",
						"modelName" => "EmployeeAddress",
						"primaryKey" => "row_id",
					];
		}

		if (App::hasRight("employee/employeeExtTab", "view") || App::isRootSessionEnabled()) {
			$passportNumberSQL		= "employee_ext.`passport_number`,";
			if (Yang::getParam('customerDbPatchName') == 'eisberg') {
				$passportNumberSQL	= "IF(employee_ext.`passport_number` = '0', '" . Dict::getValue("no") . "', IF(employee_ext.`passport_number` = '1', '" . Dict::getValue("yes") . "', employee_ext.`passport_number`)) AS passport_number,";
			}
			$wizards["dhtmlxGrid"][] =
					[
						"contentId" => "employeeExtTab",
						"contentTitle" => Dict::getValue('tab_employeetabs_employeeext'),
						"tabSQL" => "
							SELECT
								employee_ext.`row_id`,
								employee_ext.`employee_id`,
								employee.`emp_id`,
								employee_ext.`place_of_birth`,
								employee_ext.`date_of_birth`,
								employee_ext.`mothers_name`,
								employee_ext.`ssn`,
								employee_ext.`personal_id_card_number`,
								{$passportNumberSQL}
								employee_ext.`option1`,
								employee_ext.`option2`,
								employee_ext.`option3`,
								employee_ext.`option4`,
								employee_ext.`option5`,
								employee_ext.`option6`,
								employee_ext.`option7`,
								employee_ext.`option8`,
								employee_ext.`option9`,
								employee_ext.`option10`,
								employee_ext.`option11`,
								employee_ext.`option12`,
								employee_ext.`option13`,
								employee_ext.`option14`,
								employee_ext.`option15`,
								employee_ext.`option16`,
								employee_ext.`option17`,
								employee_ext.`option18`,
								employee_ext.`option19`,
								employee_ext.`option20`,
								employee_ext.`option21`,
								employee_ext.`option22`,
								employee_ext.`option23`,
								employee_ext.`option24`,
								employee_ext.`option25`,
								employee_ext.`option26`,
								employee_ext.`option27`,
								employee_ext.`option28`,
								employee_ext.`option29`,
								employee_ext.`option30`,
								employee_ext.`option31`,
								employee_ext.`option32`,
								employee_ext.`option33`,
								employee_ext.`option34`,
								employee_ext.`option35`,
								employee_ext.`option36`,
								employee_ext.`option37`,
								employee_ext.`option38`,
								employee_ext.`option39`,
								employee_ext.`option40`,
								employee_ext.`note`,
								employee_ext.`valid_from`,
								employee_ext.`valid_to`
							FROM `employee`
							LEFT JOIN `employee_contract` ON
									employee_contract.`employee_id` = employee.`employee_id`
								AND employee_contract.`status` = $stPublished
								AND (employee.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
									OR IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
									OR (employee_contract.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										AND IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')))
							LEFT JOIN `employee_ext` ON
									employee_ext.`employee_id`=employee.`employee_id`
								AND employee_ext.`status` = $stPublished
								AND (employee_ext.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										OR IFNULL(employee_ext.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										OR (employee.`valid_from` BETWEEN employee_ext.`valid_from` AND IFNULL(employee_ext.`valid_to`,'$defaultEnd')
											AND IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_ext.`valid_from` AND IFNULL(employee_ext.`valid_to`,'$defaultEnd')))
							WHERE
									employee.`employee_id` = '{row_id_p1}'
								AND employee.`status` = $stPublished
							GROUP BY
								employee_ext.`row_id`
							ORDER BY
								employee_ext.`valid_from` DESC, employee_ext.`valid_to` DESC
						",
						"modelName" => "EmployeeExt",
						"primaryKey" => "row_id",
					];
		}

		if (App::hasRight("employee/employeeExt2Tab", "view") || App::isRootSessionEnabled()) {
			$wizards["dhtmlxGrid"][] =
					[
						"contentId" => "employeeExt2Tab",
						"contentTitle" => Dict::getValue('tab_employeetabs_employeeext2'),
						"tabSQL" => "
							SELECT
								employee_ext2.`row_id`,
								employee_ext2.`employee_id`,
								employee.`emp_id`,
								employee_ext2.`ext2_option1`,
								employee_ext2.`ext2_option2`,
								employee_ext2.`ext2_option3`,
								employee_ext2.`ext2_option4`,
								employee_ext2.`ext2_option5`,
								employee_ext2.`ext2_option6`,
								employee_ext2.`ext2_option7`,
								employee_ext2.`ext2_option8`,
								employee_ext2.`ext2_option9`,
								employee_ext2.`ext2_option10`,
								employee_ext2.`ext2_option11`,
								employee_ext2.`ext2_option12`,
								employee_ext2.`ext2_option13`,
								employee_ext2.`ext2_option14`,
								employee_ext2.`ext2_option15`,
								employee_ext2.`ext2_option16`,
								employee_ext2.`ext2_option17`,
								employee_ext2.`ext2_option18`,
								employee_ext2.`ext2_option19`,
								employee_ext2.`ext2_option20`,
								employee_ext2.`ext2_option21`,
								employee_ext2.`ext2_option22`,
								employee_ext2.`ext2_option23`,
								employee_ext2.`ext2_option24`,
								employee_ext2.`ext2_option25`,
								employee_ext2.`ext2_option26`,
								employee_ext2.`ext2_option27`,
								employee_ext2.`ext2_option28`,
								employee_ext2.`ext2_option29`,
								employee_ext2.`ext2_option30`,
								employee_ext2.`ext2_option31`,
								employee_ext2.`ext2_option32`,
								employee_ext2.`ext2_option33`,
								employee_ext2.`ext2_option34`,
								employee_ext2.`ext2_option35`,
								employee_ext2.`ext2_option36`,
								employee_ext2.`ext2_option37`,
								employee_ext2.`ext2_option38`,
								employee_ext2.`ext2_option39`,
								employee_ext2.`ext2_option40`,
                                employee_ext2.`ext2_option41`,
								employee_ext2.`ext2_option42`,
								employee_ext2.`ext2_option43`,
								employee_ext2.`ext2_option44`,
								employee_ext2.`ext2_option45`,
								employee_ext2.`ext2_option46`,
								employee_ext2.`ext2_option47`,
								employee_ext2.`ext2_option48`,
								employee_ext2.`ext2_option49`,
								employee_ext2.`ext2_option50`,
								employee_ext2.`ext2_option51`,
								employee_ext2.`ext2_option52`,
								employee_ext2.`ext2_option53`,
								employee_ext2.`ext2_option54`,
								employee_ext2.`ext2_option55`,
								employee_ext2.`ext2_option56`,
								employee_ext2.`ext2_option57`,
								employee_ext2.`ext2_option58`,
								employee_ext2.`ext2_option59`,
								employee_ext2.`ext2_option60`,
								employee_ext2.`valid_from`,
								employee_ext2.`valid_to`
							FROM `employee`
							LEFT JOIN `employee_contract` ON
									employee_contract.`employee_id` = employee.`employee_id`
								AND employee_contract.`status` = $stPublished
								AND (employee.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
									OR IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
									OR (employee_contract.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										AND IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')))
							LEFT JOIN `employee_ext2` ON
									employee_ext2.`employee_id`=employee.`employee_id`
								AND employee_ext2.`status` = $stPublished
								AND (employee_ext2.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										OR IFNULL(employee_ext2.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										OR (employee.`valid_from` BETWEEN employee_ext2.`valid_from` AND IFNULL(employee_ext2.`valid_to`,'$defaultEnd')
											AND IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_ext2.`valid_from` AND IFNULL(employee_ext2.`valid_to`,'$defaultEnd')))
							WHERE
									employee.`employee_id` = '{row_id_p1}'
								AND employee.`status` = $stPublished
							GROUP BY
								employee_ext2.`row_id`
							ORDER BY
								employee_ext2.`valid_from` DESC, employee_ext2.`valid_to` DESC
						",
						"modelName" => "EmployeeExt2",
						"primaryKey" => "row_id",
					];
		}

		if (App::hasRight("employee/employeeExt3Tab", "view") || App::isRootSessionEnabled()) {
			$wizards["dhtmlxGrid"][] =
				[
					"contentId" => "employeeExt3Tab",
					"contentTitle" => Dict::getValue('tab_employeetabs_employeeext3'),
					"tabSQL" => "
							SELECT
								employee_ext3.`row_id`,
								employee_ext3.`employee_id`,
								employee.`emp_id`,
								employee_ext3.`ext3_option1`,
								employee_ext3.`ext3_option2`,
								employee_ext3.`ext3_option3`,
								employee_ext3.`ext3_option4`,
								employee_ext3.`ext3_option5`,
								employee_ext3.`ext3_option6`,
								employee_ext3.`ext3_option7`,
								employee_ext3.`ext3_option8`,
								employee_ext3.`ext3_option9`,
								employee_ext3.`ext3_option10`,
								employee_ext3.`ext3_option11`,
								employee_ext3.`ext3_option12`,
								employee_ext3.`ext3_option13`,
								employee_ext3.`ext3_option14`,
								employee_ext3.`ext3_option15`,
								employee_ext3.`ext3_option16`,
								employee_ext3.`ext3_option17`,
								employee_ext3.`ext3_option18`,
								employee_ext3.`ext3_option19`,
								employee_ext3.`ext3_option20`,
								employee_ext3.`ext3_option21`,
								employee_ext3.`ext3_option22`,
								employee_ext3.`ext3_option23`,
								employee_ext3.`ext3_option24`,
								employee_ext3.`ext3_option25`,
								employee_ext3.`ext3_option26`,
								employee_ext3.`ext3_option27`,
								employee_ext3.`ext3_option28`,
								employee_ext3.`ext3_option29`,
								employee_ext3.`ext3_option30`,
								employee_ext3.`ext3_option31`,
								employee_ext3.`ext3_option32`,
								employee_ext3.`ext3_option33`,
								employee_ext3.`ext3_option34`,
								employee_ext3.`ext3_option35`,
								employee_ext3.`ext3_option36`,
								employee_ext3.`ext3_option37`,
								employee_ext3.`ext3_option38`,
								employee_ext3.`ext3_option39`,
								employee_ext3.`ext3_option40`,
							    employee_ext3.`ext3_option41`,
								employee_ext3.`ext3_option42`,
								employee_ext3.`ext3_option43`,
								employee_ext3.`ext3_option44`,
								employee_ext3.`ext3_option45`,
								employee_ext3.`ext3_option46`,
								employee_ext3.`ext3_option47`,
								employee_ext3.`ext3_option48`,
								employee_ext3.`ext3_option49`,
								employee_ext3.`ext3_option50`,
								employee_ext3.`ext3_option51`,
								employee_ext3.`ext3_option52`,
								employee_ext3.`ext3_option53`,
								employee_ext3.`ext3_option54`,
								employee_ext3.`ext3_option55`,
								employee_ext3.`ext3_option56`,
								employee_ext3.`ext3_option57`,
								employee_ext3.`ext3_option58`,
								employee_ext3.`ext3_option59`,
								employee_ext3.`ext3_option60`,
								employee_ext3.`ext3_option61`,
								employee_ext3.`ext3_option62`,
								employee_ext3.`ext3_option63`,
								employee_ext3.`ext3_option64`,
								employee_ext3.`ext3_option65`,
								employee_ext3.`ext3_option66`,
								employee_ext3.`ext3_option67`,
								employee_ext3.`ext3_option68`,
								employee_ext3.`ext3_option69`,
								employee_ext3.`ext3_option70`,
								employee_ext3.`ext3_option71`,
								employee_ext3.`ext3_option72`,
								employee_ext3.`ext3_option73`,
								employee_ext3.`ext3_option74`,
								employee_ext3.`ext3_option75`,
								employee_ext3.`ext3_option76`,
								employee_ext3.`ext3_option77`,
								employee_ext3.`ext3_option78`,
								employee_ext3.`ext3_option79`,
								employee_ext3.`ext3_option80`,
								employee_ext3.`valid_from`,
								employee_ext3.`valid_to`
							FROM `employee`
							LEFT JOIN `employee_contract` ON
									employee_contract.`employee_id` = employee.`employee_id`
								AND employee_contract.`status` = $stPublished
								AND (employee.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
									OR IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
									OR (employee_contract.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										AND IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')))
							LEFT JOIN `employee_ext3` ON
									employee_ext3.`employee_id`=employee.`employee_id`
								AND employee_ext3.`status` = $stPublished
								AND (employee_ext3.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										OR IFNULL(employee_ext3.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										OR (employee.`valid_from` BETWEEN employee_ext3.`valid_from` AND IFNULL(employee_ext3.`valid_to`,'$defaultEnd')
											AND IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_ext3.`valid_from` AND IFNULL(employee_ext3.`valid_to`,'$defaultEnd')))
							WHERE
									employee.`employee_id` = '{row_id_p1}'
								AND employee.`status` = $stPublished
							GROUP BY
								employee_ext3.`row_id`
							ORDER BY
								employee_ext3.`valid_from` DESC, employee_ext3.`valid_to` DESC
						",
					"modelName" => "EmployeeExt3",
					"primaryKey" => "row_id",
				];
		}

		if (App::hasRight("employee/employeeExt4Tab", "view") || App::isRootSessionEnabled()) {
			$wizards["dhtmlxGrid"][] =
				[
					"contentId" => "employeeExt4Tab",
					"contentTitle" => Dict::getValue('tab_employeetabs_employeeext4'),
					"tabSQL" => "
							SELECT
								employee_ext4.`row_id`,
								employee_ext4.`employee_id`,
								employee.`emp_id`,
								employee_ext4.`ext4_option1`,
								employee_ext4.`ext4_option2`,
								employee_ext4.`ext4_option3`,
								employee_ext4.`ext4_option4`,
								employee_ext4.`ext4_option5`,
								employee_ext4.`ext4_option6`,
								employee_ext4.`ext4_option7`,
								employee_ext4.`ext4_option8`,
								employee_ext4.`ext4_option9`,
								employee_ext4.`ext4_option10`,
								employee_ext4.`ext4_option11`,
								employee_ext4.`ext4_option12`,
								employee_ext4.`ext4_option13`,
								employee_ext4.`ext4_option14`,
								employee_ext4.`ext4_option15`,
								employee_ext4.`ext4_option16`,
								employee_ext4.`ext4_option17`,
								employee_ext4.`ext4_option18`,
								employee_ext4.`ext4_option19`,
								employee_ext4.`ext4_option20`,
								employee_ext4.`ext4_option21`,
								employee_ext4.`ext4_option22`,
								employee_ext4.`ext4_option23`,
								employee_ext4.`ext4_option24`,
								employee_ext4.`ext4_option25`,
								employee_ext4.`ext4_option26`,
								employee_ext4.`ext4_option27`,
								employee_ext4.`ext4_option28`,
								employee_ext4.`ext4_option29`,
								employee_ext4.`ext4_option30`,
								employee_ext4.`ext4_option31`,
								employee_ext4.`ext4_option32`,
								employee_ext4.`ext4_option33`,
								employee_ext4.`ext4_option34`,
								employee_ext4.`ext4_option35`,
								employee_ext4.`ext4_option36`,
								employee_ext4.`ext4_option37`,
								employee_ext4.`ext4_option38`,
								employee_ext4.`ext4_option39`,
								employee_ext4.`ext4_option40`,
							    employee_ext4.`ext4_option41`,
								employee_ext4.`ext4_option42`,
								employee_ext4.`ext4_option43`,
								employee_ext4.`ext4_option44`,
								employee_ext4.`ext4_option45`,
								employee_ext4.`ext4_option46`,
								employee_ext4.`ext4_option47`,
								employee_ext4.`ext4_option48`,
								employee_ext4.`ext4_option49`,
								employee_ext4.`ext4_option50`,
								employee_ext4.`ext4_option51`,
								employee_ext4.`ext4_option52`,
								employee_ext4.`ext4_option53`,
								employee_ext4.`ext4_option54`,
								employee_ext4.`ext4_option55`,
								employee_ext4.`ext4_option56`,
								employee_ext4.`ext4_option57`,
								employee_ext4.`ext4_option58`,
								employee_ext4.`ext4_option59`,
								employee_ext4.`ext4_option60`,
								employee_ext4.`valid_from`,
								employee_ext4.`valid_to`
							FROM `employee`
							LEFT JOIN `employee_contract` ON
									employee_contract.`employee_id` = employee.`employee_id`
								AND employee_contract.`status` = $stPublished
								AND (employee.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
									OR IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
									OR (employee_contract.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										AND IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')))
							LEFT JOIN `employee_ext4` ON
									employee_ext4.`employee_id`=employee.`employee_id`
								AND employee_ext4.`status` = $stPublished
								AND (employee_ext4.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										OR IFNULL(employee_ext4.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										OR (employee.`valid_from` BETWEEN employee_ext4.`valid_from` AND IFNULL(employee_ext4.`valid_to`,'$defaultEnd')
											AND IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_ext4.`valid_from` AND IFNULL(employee_ext4.`valid_to`,'$defaultEnd')))
							WHERE
									employee.`employee_id` = '{row_id_p1}'
								AND employee.`status` = $stPublished
							GROUP BY
								employee_ext4.`row_id`
							ORDER BY
								employee_ext4.`valid_from` DESC, employee_ext4.`valid_to` DESC
						",
					"modelName" => "EmployeeExt4",
					"primaryKey" => "row_id",
				];
		}

		if (App::hasRight("employee/employeeExt5Tab", "view") || App::isRootSessionEnabled()) {
			$wizards["dhtmlxGrid"][] =
				[
					"contentId" => "employeeExt5Tab",
					"contentTitle" => Dict::getValue('tab_employeetabs_employeeext5'),
					"tabSQL" => "
							SELECT
								employee_ext5.`row_id`,
								employee_ext5.`employee_id`,
								employee.`emp_id`,
								employee_ext5.`ext5_option1`,
								employee_ext5.`ext5_option2`,
								employee_ext5.`ext5_option3`,
								employee_ext5.`ext5_option4`,
								employee_ext5.`ext5_option5`,
								employee_ext5.`ext5_option6`,
								employee_ext5.`ext5_option7`,
								employee_ext5.`ext5_option8`,
								employee_ext5.`ext5_option9`,
								employee_ext5.`ext5_option10`,
								employee_ext5.`ext5_option11`,
								employee_ext5.`ext5_option12`,
								employee_ext5.`ext5_option13`,
								employee_ext5.`ext5_option14`,
								employee_ext5.`ext5_option15`,
								employee_ext5.`ext5_option16`,
								employee_ext5.`ext5_option17`,
								employee_ext5.`ext5_option18`,
								employee_ext5.`ext5_option19`,
								employee_ext5.`ext5_option20`,
								employee_ext5.`ext5_option21`,
								employee_ext5.`ext5_option22`,
								employee_ext5.`ext5_option23`,
								employee_ext5.`ext5_option24`,
								employee_ext5.`ext5_option25`,
								employee_ext5.`ext5_option26`,
								employee_ext5.`ext5_option27`,
								employee_ext5.`ext5_option28`,
								employee_ext5.`ext5_option29`,
								employee_ext5.`ext5_option30`,
								employee_ext5.`ext5_option31`,
								employee_ext5.`ext5_option32`,
								employee_ext5.`ext5_option33`,
								employee_ext5.`ext5_option34`,
								employee_ext5.`ext5_option35`,
								employee_ext5.`ext5_option36`,
								employee_ext5.`ext5_option37`,
								employee_ext5.`ext5_option38`,
								employee_ext5.`ext5_option39`,
								employee_ext5.`ext5_option40`,
							    employee_ext5.`ext5_option41`,
								employee_ext5.`ext5_option42`,
								employee_ext5.`ext5_option43`,
								employee_ext5.`ext5_option44`,
								employee_ext5.`ext5_option45`,
								employee_ext5.`ext5_option46`,
								employee_ext5.`ext5_option47`,
								employee_ext5.`ext5_option48`,
								employee_ext5.`ext5_option49`,
								employee_ext5.`ext5_option50`,
								employee_ext5.`ext5_option51`,
								employee_ext5.`ext5_option52`,
								employee_ext5.`ext5_option53`,
								employee_ext5.`ext5_option54`,
								employee_ext5.`ext5_option55`,
								employee_ext5.`ext5_option56`,
								employee_ext5.`ext5_option57`,
								employee_ext5.`ext5_option58`,
								employee_ext5.`ext5_option59`,
								employee_ext5.`ext5_option60`,
								employee_ext5.`valid_from`,
								employee_ext5.`valid_to`
							FROM `employee`
							LEFT JOIN `employee_contract` ON
									employee_contract.`employee_id` = employee.`employee_id`
								AND employee_contract.`status` = $stPublished
								AND (employee.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
									OR IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
									OR (employee_contract.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										AND IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')))
							LEFT JOIN `employee_ext5` ON
									employee_ext5.`employee_id`=employee.`employee_id`
								AND employee_ext5.`status` = $stPublished
								AND (employee_ext5.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										OR IFNULL(employee_ext5.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										OR (employee.`valid_from` BETWEEN employee_ext5.`valid_from` AND IFNULL(employee_ext5.`valid_to`,'$defaultEnd')
											AND IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_ext5.`valid_from` AND IFNULL(employee_ext5.`valid_to`,'$defaultEnd')))
							WHERE
									employee.`employee_id` = '{row_id_p1}'
								AND employee.`status` = $stPublished
							GROUP BY
								employee_ext5.`row_id`
							ORDER BY
								employee_ext5.`valid_from` DESC, employee_ext5.`valid_to` DESC
						",
					"modelName" => "EmployeeExt5",
					"primaryKey" => "row_id",
				];
		}

		if (App::hasRight("employee/employeeExt6Tab", "view") || App::isRootSessionEnabled()) {
			$wizards["dhtmlxGrid"][] =
				[
					"contentId" => "employeeExt6Tab",
					"contentTitle" => Dict::getValue('tab_employeetabs_employeeext6'),
					"tabSQL" => "
							SELECT
								employee_ext6.`row_id`,
								employee_ext6.`employee_id`,
								employee.`emp_id`,
								employee_ext6.`ext6_option1`,
								employee_ext6.`ext6_option2`,
								employee_ext6.`ext6_option3`,
								employee_ext6.`ext6_option4`,
								employee_ext6.`ext6_option5`,
								employee_ext6.`ext6_option6`,
								employee_ext6.`ext6_option7`,
								employee_ext6.`ext6_option8`,
								employee_ext6.`ext6_option9`,
								employee_ext6.`ext6_option10`,
								employee_ext6.`ext6_option11`,
								employee_ext6.`ext6_option12`,
								employee_ext6.`ext6_option13`,
								employee_ext6.`ext6_option14`,
								employee_ext6.`ext6_option15`,
								employee_ext6.`ext6_option16`,
								employee_ext6.`ext6_option17`,
								employee_ext6.`ext6_option18`,
								employee_ext6.`ext6_option19`,
								employee_ext6.`ext6_option20`,
								employee_ext6.`ext6_option21`,
								employee_ext6.`ext6_option22`,
								employee_ext6.`ext6_option23`,
								employee_ext6.`ext6_option24`,
								employee_ext6.`ext6_option25`,
								employee_ext6.`ext6_option26`,
								employee_ext6.`ext6_option27`,
								employee_ext6.`ext6_option28`,
								employee_ext6.`ext6_option29`,
								employee_ext6.`ext6_option30`,
								employee_ext6.`ext6_option31`,
								employee_ext6.`ext6_option32`,
								employee_ext6.`ext6_option33`,
								employee_ext6.`ext6_option34`,
								employee_ext6.`ext6_option35`,
								employee_ext6.`ext6_option36`,
								employee_ext6.`ext6_option37`,
								employee_ext6.`ext6_option38`,
								employee_ext6.`ext6_option39`,
								employee_ext6.`ext6_option40`,
							    employee_ext6.`ext6_option41`,
								employee_ext6.`ext6_option42`,
								employee_ext6.`ext6_option43`,
								employee_ext6.`ext6_option44`,
								employee_ext6.`ext6_option45`,
								employee_ext6.`ext6_option46`,
								employee_ext6.`ext6_option47`,
								employee_ext6.`ext6_option48`,
								employee_ext6.`ext6_option49`,
								employee_ext6.`ext6_option50`,
								employee_ext6.`ext6_option51`,
								employee_ext6.`ext6_option52`,
								employee_ext6.`ext6_option53`,
								employee_ext6.`ext6_option54`,
								employee_ext6.`ext6_option55`,
								employee_ext6.`ext6_option56`,
								employee_ext6.`ext6_option57`,
								employee_ext6.`ext6_option58`,
								employee_ext6.`ext6_option59`,
								employee_ext6.`ext6_option60`,
								employee_ext6.`valid_from`,
								employee_ext6.`valid_to`
							FROM `employee`
							LEFT JOIN `employee_contract` ON
									employee_contract.`employee_id` = employee.`employee_id`
								AND employee_contract.`status` = $stPublished
								AND (employee.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
									OR IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
									OR (employee_contract.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										AND IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')))
							LEFT JOIN `employee_ext6` ON
									employee_ext6.`employee_id`=employee.`employee_id`
								AND employee_ext6.`status` = $stPublished
								AND (employee_ext6.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										OR IFNULL(employee_ext6.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										OR (employee.`valid_from` BETWEEN employee_ext6.`valid_from` AND IFNULL(employee_ext6.`valid_to`,'$defaultEnd')
											AND IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_ext6.`valid_from` AND IFNULL(employee_ext6.`valid_to`,'$defaultEnd')))
							WHERE
									employee.`employee_id` = '{row_id_p1}'
								AND employee.`status` = $stPublished
							GROUP BY
								employee_ext6.`row_id`
							ORDER BY
								employee_ext6.`valid_from` DESC, employee_ext6.`valid_to` DESC
						",
					"modelName" => "EmployeeExt6",
					"primaryKey" => "row_id",
				];
		}

        if (App::hasRight('employee/employeeExt7Tab', 'view') || App::isRootSessionEnabled()) {
            $wizards['dhtmlxGrid'][] =
                [
                    'contentId' => 'employeeExt7Tab',
                    'contentTitle' => Dict::getValue('tab_employeetabs_employeeext7'),
                    'tabSQL' => "
							SELECT
								employee_ext7.`row_id`,
								employee_ext7.`employee_id`,
								employee.`emp_id`,
								employee_ext7.`ext7_option1`,
								employee_ext7.`ext7_option2`,
								employee_ext7.`ext7_option3`,
								employee_ext7.`ext7_option4`,
								employee_ext7.`ext7_option5`,
								employee_ext7.`ext7_option6`,
								employee_ext7.`ext7_option7`,
								employee_ext7.`ext7_option8`,
								employee_ext7.`ext7_option9`,
								employee_ext7.`ext7_option10`,
								employee_ext7.`ext7_option11`,
								employee_ext7.`ext7_option12`,
								employee_ext7.`ext7_option13`,
								employee_ext7.`ext7_option14`,
								employee_ext7.`ext7_option15`,
								employee_ext7.`ext7_option16`,
								employee_ext7.`ext7_option17`,
								employee_ext7.`ext7_option18`,
								employee_ext7.`ext7_option19`,
								employee_ext7.`ext7_option20`,
								employee_ext7.`ext7_option21`,
								employee_ext7.`ext7_option22`,
								employee_ext7.`ext7_option23`,
								employee_ext7.`ext7_option24`,
								employee_ext7.`ext7_option25`,
								employee_ext7.`ext7_option26`,
								employee_ext7.`ext7_option27`,
								employee_ext7.`ext7_option28`,
								employee_ext7.`ext7_option29`,
								employee_ext7.`ext7_option30`,
								employee_ext7.`ext7_option31`,
								employee_ext7.`ext7_option32`,
								employee_ext7.`ext7_option33`,
								employee_ext7.`ext7_option34`,
								employee_ext7.`ext7_option35`,
								employee_ext7.`ext7_option36`,
								employee_ext7.`ext7_option37`,
								employee_ext7.`ext7_option38`,
								employee_ext7.`ext7_option39`,
								employee_ext7.`ext7_option40`,
							    employee_ext7.`ext7_option41`,
								employee_ext7.`ext7_option42`,
								employee_ext7.`ext7_option43`,
								employee_ext7.`ext7_option44`,
								employee_ext7.`ext7_option45`,
								employee_ext7.`ext7_option46`,
								employee_ext7.`ext7_option47`,
								employee_ext7.`ext7_option48`,
								employee_ext7.`ext7_option49`,
								employee_ext7.`ext7_option50`,
								employee_ext7.`ext7_option51`,
								employee_ext7.`ext7_option52`,
								employee_ext7.`ext7_option53`,
								employee_ext7.`ext7_option54`,
								employee_ext7.`ext7_option55`,
								employee_ext7.`ext7_option56`,
								employee_ext7.`ext7_option57`,
								employee_ext7.`ext7_option58`,
								employee_ext7.`ext7_option59`,
								employee_ext7.`ext7_option60`,
								employee_ext7.`valid_from`,
								employee_ext7.`valid_to`
							FROM `employee`
							LEFT JOIN `employee_contract` ON
									employee_contract.`employee_id` = employee.`employee_id`
								AND employee_contract.`status` = $stPublished
								AND (employee.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
									OR IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
									OR (employee_contract.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										AND IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')))
							LEFT JOIN `employee_ext7` ON
									employee_ext7.`employee_id`=employee.`employee_id`
								AND employee_ext7.`status` = $stPublished
								AND (employee_ext7.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										OR IFNULL(employee_ext7.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										OR (employee.`valid_from` BETWEEN employee_ext7.`valid_from` AND IFNULL(employee_ext7.`valid_to`,'$defaultEnd')
											AND IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_ext7.`valid_from` AND IFNULL(employee_ext7.`valid_to`,'$defaultEnd')))
							WHERE
									employee.`employee_id` = '{row_id_p1}'
								AND employee.`status` = $stPublished
							GROUP BY
								employee_ext7.`row_id`
							ORDER BY
								employee_ext7.`valid_from` DESC, employee_ext7.`valid_to` DESC
						",
                    'modelName' => 'EmployeeExt7',
                    'primaryKey' => 'row_id',
                ];
        }

		if (App::hasRight("employee/employeeDocsTab", "view") || App::isRootSessionEnabled()) {
			$wizards["dhtmlxGrid"][] =
					[
						"contentId" => "employeeDocsTab",
						"contentTitle" => Dict::getValue('tab_employeetabs_employeedocs'),
						"tabSQL" => "
							SELECT
								employee_docs.`row_id`,
								employee_docs.`employee_id`,
								employee.`emp_id`,
								employee_docs.`note`,
								employee_docs.`valid_from`,
								employee_docs.`valid_to`,
								employee_docs.`certificate_id`,
								employee_docs.`institution_name`
							FROM `employee`
							LEFT JOIN `employee_contract` ON
									employee_contract.`employee_id` = employee.`employee_id`
								AND employee_contract.`status` = $stPublished
								AND (employee.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
									OR IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
									OR (employee_contract.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										AND IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')))
							LEFT JOIN `employee_docs` ON
								employee_docs.`employee_id`=employee.`employee_id`
								AND employee_docs.`status` = $stPublished
								AND (employee_docs.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										OR IFNULL(employee_docs.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
										OR (employee.`valid_from` BETWEEN employee_docs.`valid_from` AND IFNULL(employee_docs.`valid_to`,'$defaultEnd')
											AND IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_docs.`valid_from` AND IFNULL(employee_docs.`valid_to`,'$defaultEnd')))
							WHERE
									employee.`employee_id` = '{row_id_p1}'
								AND employee.`status` = $stPublished
								AND `employee_docs`.`row_id` IS NOT NULL
							GROUP BY
								employee_docs.`row_id`
							ORDER BY
								employee_docs.`valid_from` DESC, employee_docs.`valid_to` DESC
						",
						"modelName" => "EmployeeDocs",
						"primaryKey" => "row_id",
					];
		}

		if (App::hasRight("employee/employeeLabourDocsTab", "view")) {
			$wizards["dhtmlxGrid"][] =
					[
						"contentId" => "employeeLabourDocsTab",
						"contentTitle" => Dict::getValue('tab_employeetabs_employeeLabourDocs'),
						"tabSQL" => "
							SELECT
								eldocs.`row_id`,
								eldocs.`employee_id`,
								e.`emp_id`,
								eldocs.`note`,
								eldocs.`valid_from`,
								eldocs.`valid_to`
							FROM `employee` e
							LEFT JOIN `employee_contract` ec ON
									ec.`employee_id` = e.`employee_id`
								AND ec.`status` = $stPublished
								AND (e.`valid_from` BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`,'$defaultEnd')
									OR IFNULL(e.`valid_to`,'$defaultEnd') BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`,'$defaultEnd')
									OR (ec.`valid_from` BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`,'$defaultEnd')
										AND IFNULL(ec.`valid_to`,'$defaultEnd') BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`,'$defaultEnd')))
							LEFT JOIN `employee_labour_docs` eldocs ON
								eldocs.`employee_id` = e.`employee_id`
								AND eldocs.`status` = $stPublished
								AND (eldocs.`valid_from` BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`,'$defaultEnd')
										OR IFNULL(eldocs.`valid_to`,'$defaultEnd') BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`,'$defaultEnd')
										OR (e.`valid_from` BETWEEN eldocs.`valid_from` AND IFNULL(eldocs.`valid_to`,'$defaultEnd')
											AND IFNULL(e.`valid_to`,'$defaultEnd') BETWEEN eldocs.`valid_from` AND IFNULL(eldocs.`valid_to`,'$defaultEnd')))
							WHERE
									e.`employee_id` = '{row_id_p1}'
								AND e.`status` = $stPublished
								AND eldocs.`row_id` IS NOT NULL
							GROUP BY
								eldocs.`row_id`
							ORDER BY
								eldocs.`valid_from` DESC, eldocs.`valid_to` DESC
						",
						"modelName" => "EmployeeLabourDocs",
						"primaryKey" => "row_id",
					];
		}

		if (App::hasRight("employee/employeeGroupTab", "view") || App::isRootSessionEnabled()) {
			$employeeGroupSQL="
							SELECT
								employee_group.`row_id`,
								employee_contract.`employee_contract_id`,
								employee_contract.`employee_contract_number`,

								gr_dict.`dict_value` AS group_id,
								`groups`.`value` AS `group_value`,
								employee_group.`valid_from`,
								employee_group.`valid_to`
							FROM
								`employee`
							LEFT JOIN
								`employee_contract` ON
									employee_contract.`employee_id` = employee.`employee_id`
										AND employee_contract.`status` = $stPublished
											AND (
												employee.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												(
													employee_contract.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
														AND
													IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
												)
											)
							LEFT JOIN
								`employee_group` ON
									employee_group.`employee_contract_id` = employee_contract.`employee_contract_id`
										AND employee_group.`status` = $stPublished
											AND (
												employee_group.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												IFNULL(employee_group.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												(
													employee_contract.`valid_from` BETWEEN employee_group.`valid_from` AND IFNULL(employee_group.`valid_to`,'$defaultEnd')
														AND
													IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee_group.`valid_from` AND IFNULL(employee_group.`valid_to`,'$defaultEnd')
												)
											)
							LEFT JOIN
								`employee_group_config` ON
									employee_group_config.`group_id` = employee_group.`group_id`
							LEFT JOIN
								`dictionary` gr_dict ON
									gr_dict.`row_id` = (SELECT dictionary.`row_id` FROM `dictionary` WHERE dictionary.`dict_id` = employee_group_config.`dict_id` AND dictionary.`lang` = '{$lang}' LIMIT 1)
							LEFT JOIN (
										(
											SELECT
												workgroup.`workgroup_id` AS id,
												workgroup.`workgroup_name` AS value,
												'workgroup_id' AS group_id
											FROM
												`workgroup`
											INNER JOIN
											(
												SELECT workgroup_id, MAX(valid_from) AS valid_from FROM `workgroup`
												WHERE workgroup.`status` = $stPublished
												GROUP BY workgroup.`workgroup_id`
											) wg
											ON workgroup.`workgroup_id` = wg.`workgroup_id` AND workgroup.`valid_from` = wg.`valid_from`
											WHERE
												workgroup.`status` = $stPublished
										)
										UNION
										(
											SELECT
												unit.`unit_id` AS id,
												unit.`unit_name` AS value,
												'unit_id' AS group_id
											FROM
												`unit`
											INNER JOIN
											(
												SELECT unit_id, MAX(valid_from) AS valid_from FROM `unit`
												WHERE unit.`status` = $stPublished
												GROUP BY unit.`unit_id`
											) u
											ON unit.`unit_id` = u.`unit_id` AND unit.`valid_from` = u.`valid_from`
											WHERE
												unit.`status` = $stPublished
										)
										UNION
										(
											SELECT
												company_org_group1.`company_org_group_id` AS id,
												company_org_group1.`company_org_group_name` AS value,
												'company_org_group1_id' AS group_id
											FROM
												`company_org_group1`
											INNER JOIN
											(
												SELECT company_org_group_id, MAX(valid_from) AS valid_from FROM `company_org_group1`
												WHERE company_org_group1.`status` = $stPublished
												GROUP BY company_org_group1.`company_org_group_id`
											) cog1
											ON company_org_group1.`company_org_group_id` = cog1.`company_org_group_id`
												AND company_org_group1.`valid_from` = cog1.`valid_from`
											WHERE
												company_org_group1.`status` = $stPublished
										)
										UNION
										(
											SELECT
												company_org_group2.`company_org_group_id` AS id,
												company_org_group2.`company_org_group_name` AS value,
												'company_org_group2_id' AS group_id
											FROM
												`company_org_group2`
											INNER JOIN
											(
												SELECT company_org_group_id, MAX(valid_from) AS valid_from FROM `company_org_group2`
												WHERE company_org_group2.`status` = $stPublished
												GROUP BY company_org_group2.`company_org_group_id`
											) cog2
											ON company_org_group2.`company_org_group_id` = cog2.`company_org_group_id`
												AND company_org_group2.`valid_from` = cog2.`valid_from`
											WHERE
												company_org_group2.`status` = $stPublished
										)
										UNION
										(
											SELECT
												company_org_group3.`company_org_group_id` AS id,
												company_org_group3.`company_org_group_name` AS value,
												'company_org_group3_id' AS group_id
											FROM
												`company_org_group3`
											INNER JOIN
											(
												SELECT company_org_group_id, MAX(valid_from) AS valid_from FROM `company_org_group3`
												WHERE company_org_group3.`status` = $stPublished
												GROUP BY company_org_group3.`company_org_group_id`
											) cog3
											ON company_org_group3.`company_org_group_id` = cog3.`company_org_group_id`
												AND company_org_group3.`valid_from` = cog3.`valid_from`
											WHERE
												company_org_group3.`status` = $stPublished
										)
										UNION
										(
											SELECT
												cost.`cost_id` AS id,
												cost.`cost_name` AS value,
												'cost_id' AS group_id
											FROM
												`cost`
											INNER JOIN
											(
												SELECT cost_id, MAX(valid_from) AS valid_from FROM `cost`
												WHERE cost.`status` = $stPublished
												GROUP BY cost.`cost_id`
											) cost1
											ON cost.`cost_id` = cost1.`cost_id` AND cost.`valid_from` = cost1.`valid_from`
											WHERE
												cost.`status` = $stPublished
										)
										UNION
										(
											SELECT
												cost_center.`cost_center_id` AS id,
												cost_center.`cost_center_name` AS value,
												'cost_center_id' AS group_id
											FROM
												`cost_center`
											INNER JOIN
											(
												SELECT cost_center_id, MAX(valid_from) AS valid_from FROM `cost_center`
												WHERE cost_center.`status` = $stPublished
												GROUP BY cost_center.`cost_center_id`
											) costcenter1
											ON cost_center.`cost_center_id` = costcenter1.`cost_center_id`
												AND cost_center.`valid_from` = costcenter1.`valid_from`
											WHERE
												cost_center.`status` = $stPublished
										)
										UNION
										(
											SELECT
												employee_position.`employee_position_id` AS id,
												employee_position.`employee_position_name` AS value,
												'employee_position_id' as group_id
											FROM
												`employee_position`
											INNER JOIN
											(
												SELECT employee_position_id, MAX(valid_from) AS valid_from FROM `employee_position`
												WHERE employee_position.`status` = $stPublished
												GROUP BY employee_position.`employee_position_id`
											) employeeposition1
											ON employee_position.`employee_position_id` = employeeposition1.`employee_position_id`
												AND employee_position.`valid_from` = employeeposition1.`valid_from`
											WHERE
												`employee_position`.`status` = $stPublished
										)";
										if (weHaveModule('ttwa-csm'))
										{
											$employeeGroupSQL .= "
											UNION
											(
												SELECT
													job_tasks.`job_task_id` AS id,
													job_tasks.`job_task_name` AS value,
													'job_task_id' as group_id
												FROM
													`job_tasks`
												INNER JOIN
												(
													SELECT job_task_id, MAX(valid_from) AS valid_from FROM `job_tasks`
													WHERE job_tasks.`status` = $stPublished
													GROUP BY job_tasks.`job_task_id`
												) job_tasks1
												ON job_tasks.`job_task_id` = job_tasks1.`job_task_id`
													AND job_tasks.`valid_from` = job_tasks1.`valid_from`
												WHERE
													`job_tasks`.`status` = $stPublished
											)";
										}
							$employeeGroupSQL .= ") `groups` ON
									`groups`.`group_id` = employee_group.`group_id`
										AND employee_group.`group_value` = `groups`.`id`
							WHERE
									employee.`employee_id` = '{row_id_p1}'
								AND employee.`status` = $stPublished
								AND employee_group.`row_id` IS NOT NULL
								AND employee_group_config.`status`= $stPublished
							";
							if(!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight("employee/employeeGroupTab", "company_org_group1"))
							{
								$employeeGroupSQL.="AND employee_group_config.`group_id`!='company_org_group1_id'
								";
							}
							if(!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight("employee/employeeGroupTab", "company_org_group2"))
							{
								$employeeGroupSQL.="AND employee_group_config.`group_id`!='company_org_group2_id'
								";
							}
							if(!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight("employee/employeeGroupTab", "company_org_group3"))
							{
								$employeeGroupSQL.="AND employee_group_config.`group_id`!='company_org_group3_id'
								";
							}
							if(!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight("employee/employeeGroupTab", "workgroup"))
							{
								$employeeGroupSQL.="AND employee_group_config.`group_id`!='workgroup_id'
								";
							}
							if(!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight("employee/employeeGroupTab", "unit"))
							{
								$employeeGroupSQL.="AND employee_group_config.`group_id`!='unit_id'
								";
							}
							if(!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight("employee/employeeGroupTab", "cost"))
							{
								$employeeGroupSQL.="AND employee_group_config.`group_id`!='cost_id'
								";
							}
							if(!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight("employee/employeeGroupTab", "cost_center"))
							{
								$employeeGroupSQL.="AND employee_group_config.`group_id`!='cost_center_id'
								";
							}
							if(!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight("employee/employeeGroupTab", "employee_position"))
							{
								$employeeGroupSQL.="AND employee_group_config.`group_id` != 'employee_position_id'
								";
							}
							if(weHaveModule('ttwa-csm') && !App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight("employee/employeeGroupTab", "job_tasks"))
							{
								$employeeGroupSQL.="AND employee_group_config.`group_id` != 'job_task_id'
								";
							}
			$employeeGroupSQL.="GROUP BY
								employee_group.`row_id`
							ORDER BY
								employee_group.`group_id`, employee_group.`valid_from` DESC, employee_group.`valid_to` DESC
						";

			$wizards["dhtmlxGrid"][] =
					[
						"contentId" => "employeeGroupTab",
						"contentTitle" => Dict::getValue('tab_employeetabs_employee_group'),
						"tabSQL" => $employeeGroupSQL,
						"modelName" => "EmployeeGroup",
						"primaryKey" => "row_id",
					];
		}

		if (App::hasRight("employee/employeeCardTab", "view") || App::isRootSessionEnabled()) {
			$wizards["dhtmlxGrid"][] =
					[
						"contentId" => "employeeCardTab",
						"contentTitle" => Dict::getValue('tab_employeetabs_employeecard'),
						"tabSQL" => "
							SELECT
								employee_card.`row_id`,
								employee_card.`employee_contract_id`,
								employee_card.`card`,
								employee_card.`note`,
								employee_card.`valid_from`,
								employee_card.`valid_to`,
								employee_card.`card_type`,
								IFNULL(aal.`ac_access_level_name`,employee_card.`acl`) as acl,
								employee_card.`rgnet_check_freq`,
								IFNULL(d.`dict_value`,employee_card.`card_type`) as card_type,
								employee_contract.`employee_contract_number`
							FROM
								`employee`
							LEFT JOIN
								`employee_contract` ON
									employee_contract.`employee_id` = employee.`employee_id`
										AND employee_contract.`status` = $stPublished
											AND (
												employee.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												(
													employee_contract.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
														AND
													IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
												)
											)
							LEFT JOIN
								`employee_card` ON
									employee_card.`employee_contract_id` = employee_contract.`employee_contract_id`
										AND employee_card.`status` = $stPublished
											AND (
												employee_card.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												IFNULL(employee_card.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												(
													employee_contract.`valid_from` BETWEEN employee_card.`valid_from` AND IFNULL(employee_card.`valid_to`,'$defaultEnd')
														AND
													IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee_card.`valid_from` AND IFNULL(employee_card.`valid_to`,'$defaultEnd')
												)
											)
							LEFT JOIN `ac_access_level` aal ON
									aal.`ac_access_level_id`=employee_card.`acl`
								AND aal.`status` = $stPublished
							LEFT JOIN `app_lookup` al ON
									al.`lookup_value`=employee_card.`card_type`
								AND al.`lookup_id`='card_type'
								AND al.`valid`=1
							LEFT JOIN `dictionary` d ON
									d.`dict_id`=al.dict_id
								AND d.`valid`=1
								AND d.`lang`= '{$lang}'
							WHERE
								employee.`employee_id` = '{row_id_p1}'
									AND employee.`status` = $stPublished
										AND employee_card.`row_id` IS NOT NULL
							GROUP BY
								employee_card.`row_id`
							ORDER BY
								employee_card.`valid_from` DESC, employee_card.`valid_to` DESC
						",
						"modelName" => "EmployeeCard",
						"primaryKey" => "row_id",
					];
		}

		$tab_employeetabs_employeecost = App::getSetting("akh_tab_profitcentrum") > 0 ? Dict::getValue('akh_tab_employeetabs_employeecost') : Dict::getValue('tab_employeetabs_employeecost');

		if (App::hasRight("employee/employeeCostTab", "view")&& (!EmployeeGroupConfig::isActiveGroup('cost_id') || !EmployeeGroupConfig::isActiveGroup('cost_center_id')) || App::isRootSessionEnabled()) {
			$wizards["dhtmlxGrid"][] =
					[
						"contentId" => "employeeCostTab",
						"contentTitle" => $tab_employeetabs_employeecost,
						"tabSQL" => "
							SELECT
								employee_cost.`row_id`,
								employee_contract.`employee_contract_id`,
								employee_contract.`employee_contract_number`,

								cost.`cost_name` AS cost_id,
								cost_center.`cost_center_name` AS cost_center_id,

								employee_cost.`valid_from`,
								employee_cost.`valid_to`
							FROM
								`employee`
							LEFT JOIN
								`employee_contract` ON
									employee_contract.`employee_id` = employee.`employee_id`
										AND employee_contract.`status` = $stPublished
											AND (
												employee.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												(
													employee_contract.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
														AND
													IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
												)
											)
							LEFT JOIN
								`employee_cost` ON
									employee_cost.`employee_contract_id` = employee_contract.`employee_contract_id`
										AND employee_cost.`status` = $stPublished
											AND (
												employee_cost.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												IFNULL(employee_cost.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												(
													employee_contract.`valid_from` BETWEEN employee_cost.`valid_from` AND IFNULL(employee_cost.`valid_to`,'$defaultEnd')
														AND
													IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee_cost.`valid_from` AND IFNULL(employee_cost.`valid_to`,'$defaultEnd')
												)
											)
							LEFT JOIN
								`cost` ON
									cost.`cost_id` = employee_cost.`cost_id`
										AND (employee_cost.`valid_from` BETWEEN cost.`valid_from` AND IFNULL(cost.`valid_to`, '$defaultEnd'))
											AND cost.`status` = $stPublished
							LEFT JOIN
								`cost_center` ON
									cost_center.`cost_center_id` = employee_cost.`cost_center_id`
										AND (employee_cost.`valid_from` BETWEEN cost_center.`valid_from` AND IFNULL(cost_center.`valid_to`, '$defaultEnd'))
											AND cost_center.`status` = $stPublished
							WHERE
								employee.`employee_id` = '{row_id_p1}'
									AND employee.`status` = $stPublished
										AND employee_cost.`row_id` IS NOT NULL
							GROUP BY
								employee_cost.`row_id`
							ORDER BY
								employee_cost.`valid_from` DESC, employee_cost.`valid_to` DESC
						",
						"modelName" => "EmployeeCost",
						"primaryKey" => "row_id",
					];
		}

		if (weHaveModule("ttwa-ahp-core") && (App::hasRight("employee/employeeBaseAbsenceTab", "view") || App::isRootSessionEnabled())) {
			$wizards["dhtmlxGrid"][] =
					[
						"contentId" => "employeeBaseAbsenceTab",
						"contentTitle" => Dict::getValue('tab_employeetabs_employeebaseabsence'),
						"tabSQL" => "
							SELECT
								employee_base_absence.*,

								employee_contract.`employee_contract_number`,

								bat_dict.`dict_value` AS base_absence_type_name
							FROM
								`employee`
							LEFT JOIN
								`employee_contract` ON
									employee_contract.`employee_id` = employee.`employee_id`
										AND employee_contract.`status` = $stPublished
											AND (
												employee.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												(
													employee_contract.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
														AND
													IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
												)
											)
							LEFT JOIN
								`employee_base_absence` ON
									employee_base_absence.`employee_contract_id` = employee_contract.`employee_contract_id`
										AND employee_base_absence.`status` = $stPublished
											AND (
												employee_base_absence.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												IFNULL(employee_base_absence.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												(
													employee_contract.`valid_from` BETWEEN employee_base_absence.`valid_from` AND IFNULL(employee_base_absence.`valid_to`,'$defaultEnd')
														AND
													IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee_base_absence.`valid_from` AND IFNULL(employee_base_absence.`valid_to`,'$defaultEnd')
												)
											)
							LEFT JOIN
								`base_absence_type` ON
									base_absence_type.`base_absence_type_id` = employee_base_absence.`base_absence_type_id`
										AND base_absence_type.`status` = $stPublished
							LEFT JOIN
								`dictionary` bat_dict ON
									bat_dict.`dict_id` = base_absence_type.`dict_id`
										AND bat_dict.`lang` = '{$lang}'
							WHERE
								employee.`employee_id` = '{row_id_p1}'
									AND employee.`status` = $stPublished
										AND employee_base_absence.`row_id` IS NOT NULL
							GROUP BY
								employee_base_absence.`row_id`
							ORDER BY
								employee_base_absence.`valid_from` DESC, employee_base_absence.`valid_to` DESC
						",
						"modelName" => "EmployeeBaseAbsence",
						"primaryKey" => "row_id",
					];
		}

		if (weHaveModule("ttwa-csm-core") && (App::hasRight("employee/employeePersonalCompetencyTab", "view") || App::isRootSessionEnabled())) {
			$wizards["dhtmlxGrid"][] =
					[
						"contentId" => "employeePersonalCompetencyTab",
						"contentTitle" => Dict::getValue('tab_employeetabs_employeecompetency'),
						"tabSQL" => "
							SELECT
								employee_competency.*,
								dictionary.`dict_value` as result,
								employee_contract.`employee_contract_number`,

								competency.`competency_name`,
								competency_levels.`level_name`,
								competency_group.`competency_group_name`
							FROM
								`employee`
							LEFT JOIN
								`employee_contract` ON
									employee_contract.`employee_id` = employee.`employee_id`
										AND employee_contract.`status` = $stPublished
											AND (
												employee.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												(
													employee_contract.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
														AND
													IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
												)
											)
							LEFT JOIN
								`employee_competency` ON
									employee_competency.`employee_contract_id` = employee_contract.`employee_contract_id`
										AND employee_competency.`status` = $stPublished
											AND (
												employee_competency.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												IFNULL(employee_competency.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												(
													employee_contract.`valid_from` BETWEEN employee_competency.`valid_from` AND IFNULL(employee_competency.`valid_to`,'$defaultEnd')
														AND
													IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee_competency.`valid_from` AND IFNULL(employee_competency.`valid_to`,'$defaultEnd')
												)
											)
							LEFT JOIN
								`competency` ON
									competency.`competency_id` = employee_competency.`competency_id`
										AND competency.`status` = $stPublished
							LEFT JOIN
								`competency_levels` ON
									competency_levels.`level_id` = employee_competency.`level_id`
										AND competency_levels.`status` = $stPublished
							LEFT JOIN
								`competency_group` ON
									competency_group.`competency_group_id` = employee_competency.`competency_group_id`
										AND competency_group.`status` = $stPublished
							LEFT JOIN
								`app_lookup` ON
									app_lookup.`lookup_value` = employee_competency.`result`
									AND app_lookup.`valid` = '1'
									AND app_lookup.`lookup_id` = 'employee_competency_result'
							LEFT JOIN
								`dictionary` ON
									dictionary.`dict_id` = app_lookup.`dict_id`
									AND dictionary.`valid` = '1'
									AND dictionary.`lang` = '{$lang}'

							WHERE
								(competency.`competency_name` LIKE '%{competency_name}%' OR '{competency_name}' = 'ALL' OR '{competency_name}' = '')
								AND (employee_competency.`valid_from` LIKE '%{valid_from}%' OR '{valid_from}' = 'ALL' OR '{valid_from}' = '')
								AND (employee_competency.`valid_to` LIKE '%{valid_to}%' OR '{valid_to}' = 'ALL' OR '{valid_to}' = '')
								AND (competency_levels.`level_name` LIKE '%{level_name}%' OR '{level_name}' = 'ALL' OR '{level_name}' = '')
								AND (competency_group.`competency_group_name` LIKE '%{competency_group_name}%' OR '{competency_group_name}' = 'ALL' OR '{competency_group_name}' = '')
								AND (competency.`note` LIKE '%{note}%' OR '{note}' = 'ALL' OR '{note}' = '')
								AND employee.`employee_id` = '{row_id_p1}'
									AND employee.`status` = $stPublished
										AND employee_competency.`row_id` IS NOT NULL
										AND  (competency.`validity` IS NULL OR competency.`validity` <> 'inactive')
										AND competency.`status` = $stPublished
							GROUP BY
								employee_competency.`row_id`
							ORDER BY
								employee_competency.`valid_from` DESC, employee_competency.`valid_to` DESC
						",
						"modelName" => "EmployeeCompetency",
						"primaryKey" => "row_id",
					];
		}

		if (weHaveModule("ttwa-wwm") && (App::hasRight("employee/employeeBaseArticleTab", "view") || App::isRootSessionEnabled())) {
			$wizards["dhtmlxGrid"][] =
					[
						"contentId" => "employeeBaseArticleTab",
						"contentTitle" => Dict::getValue('tab_employeetabs_employeebasearticle'),
						"tabSQL" => "
							SELECT
								employee_base_article.*,

								employee_contract.`employee_contract_number`,

								article.`article_name` AS base_article_name,

								dictionary.`dict_value` AS wearing_time_type
							FROM
								`employee`
							LEFT JOIN
								`employee_contract` ON
									employee_contract.`employee_id` = employee.`employee_id`
										AND employee_contract.`status` = $stPublished
											AND (
												employee.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												IFNULL(employee.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												(
													employee_contract.`valid_from` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
														AND
													IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`,'$defaultEnd')
												)
											)
							LEFT JOIN
								`employee_base_article` ON
									employee_base_article.`employee_contract_id` = employee_contract.`employee_contract_id`
										AND employee_base_article.`status` = $stPublished
											AND (
												employee_base_article.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												IFNULL(employee_base_article.`valid_to`,'$defaultEnd') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')
													OR
												(
													employee_contract.`valid_from` BETWEEN employee_base_article.`valid_from` AND IFNULL(employee_base_article.`valid_to`,'$defaultEnd')
														AND
													IFNULL(employee_contract.`valid_to`,'$defaultEnd') BETWEEN employee_base_article.`valid_from` AND IFNULL(employee_base_article.`valid_to`,'$defaultEnd')
												)
											)
							LEFT JOIN
								`article` ON
									article.`article_id` = employee_base_article.`base_article_id`
										AND article.`status` = $stPublished
											AND (CURDATE() BETWEEN article.`valid_from` AND IFNULL(article.`valid_to`, '$defaultEnd'))
							LEFT JOIN
								`app_lookup` ON
									app_lookup.`lookup_value` = employee_base_article.`wearing_time_type`
									AND app_lookup.`valid` = '1'
									AND app_lookup.`lookup_id` = 'wearing_time_type'
							LEFT JOIN
								`dictionary` ON
									dictionary.`dict_id` = app_lookup.`dict_id`
									AND dictionary.`valid` = '1'
									AND dictionary.`lang` = '{$lang}'
							WHERE
								employee.`employee_id` = '{row_id_p1}'
									AND employee.`status` = $stPublished
										AND employee_base_article.`row_id` IS NOT NULL
							GROUP BY
								employee_base_article.`row_id`
							ORDER BY
								employee_base_article.`valid_from` DESC, employee_base_article.`valid_to` DESC
						",
						"modelName" => "EmployeeBaseArticle",
						"primaryKey" => "row_id",
					];
		}

		if (weHaveModule("ttwa-wfm") && (App::hasRight("employee/employeeWorkActivityTab", "view") || App::isRootSessionEnabled())) {
			$activityCompetencyLevelOn = App::getSetting("activityCompetencyLevelOn");
			$competentyTableOn = "";
			$competentyColumnsOn = "";
			if ($activityCompetencyLevelOn)
			{
				$competentyTableOn = "LEFT JOIN
				`competency_levels` ON
					competency_levels.`level_id` = employee_work_activity.`level_id`
						AND competency_levels.`status` = $stPublished
						";
				$competentyColumnsOn = "employee_work_activity.level_id, competency_levels.level_name,";
			}

			$wizards["dhtmlxGrid"][] =
					[
						"contentId" => "employeeWorkActivityTab",
						"contentTitle" => Dict::getValue('work_activity_name'),
						"tabSQL" => "
							SELECT
								employee_work_activity.row_id,
								employee_work_activity.employee_contract_id,
								work_activity.`work_activity_name`,
								" . $competentyColumnsOn . "
								employee_work_activity.note,
								employee_work_activity.valid_from,
								employee_work_activity.valid_to,
								employee_contract.`employee_contract_number`
							FROM `employee`
							LEFT JOIN `employee_contract` ON
									employee_contract.`employee_id` = employee.`employee_id`
								AND employee_contract.`status` = $stPublished
								AND employee.`valid_from`<=IFNULL(employee_contract.`valid_to`,'$defaultEnd')
								AND IFNULL(employee.`valid_to`,'$defaultEnd')>=employee_contract.`valid_from`
							LEFT JOIN `employee_work_activity` ON
								employee_work_activity.`employee_contract_id` = employee_contract.`employee_contract_id`
								AND employee_work_activity.`status` = $stPublished
								AND employee_contract.`valid_from`<=IFNULL(employee_work_activity.`valid_to`,'$defaultEnd')
								AND IFNULL(employee_contract.`valid_to`,'$defaultEnd')>=employee_work_activity.`valid_from`
							LEFT JOIN
								`work_activity` ON
									work_activity.`work_activity_id` = employee_work_activity.`work_activity_id`
										AND work_activity.`status` = $stPublished
							" . $competentyTableOn . "
							WHERE
								employee.`employee_id` = '{row_id_p1}'
									AND employee.`status` = $stPublished
										AND employee_work_activity.`row_id` IS NOT NULL
							GROUP BY
								employee_work_activity.`row_id`
							ORDER BY
								employee_work_activity.`valid_from` DESC, employee_work_activity.`valid_to` DESC
						",
						"modelName" => "EmployeeWorkActivity",
						"primaryKey" => "row_id",
					];
		}

		if (App::hasRight("employee/employeeTravelCostTab", "view") || App::isRootSessionEnabled())
		{
			$wizards["dhtmlxGrid"][] =
					[
						"contentId" => "employeeTravelCostTab",
						"contentTitle" => Dict::getValue('tab_employeetabs_employee_travel_cost'),
						"tabSQL" => "
							SELECT
								`employee_travel_cost`.*,
								`employee_contract`.`employee_contract_number`,
								`dictionary`.`dict_value` AS type,
								drr.`dict_value` AS reimbursement_rate
							FROM
								`employee`
							LEFT JOIN `employee_contract` ON
									`employee_contract`.`employee_id` = `employee`.`employee_id`
								AND `employee_contract`.`status` = $stPublished
								AND `employee_contract`.`valid_from` <= IFNULL(`employee`.`valid_to`,'$defaultEnd')
								AND `employee_contract`.`valid_to` >= `employee`.`valid_from`
								AND `employee_contract`.`ec_valid_from` <= IFNULL(`employee`.`valid_to`,'$defaultEnd')
								AND `employee_contract`.`ec_valid_to` >= `employee`.`valid_from`
							LEFT JOIN `employee_travel_cost` ON
									`employee_travel_cost`.`employee_contract_id` = `employee_contract`.`employee_contract_id`
								AND `employee_travel_cost`.`status` = $stPublished
								AND `employee_travel_cost`.`valid_from` <= IFNULL(`employee_contract`.`valid_to`,'$defaultEnd')
								AND `employee_travel_cost`.`valid_to` >= `employee_contract`.`valid_from`
							LEFT JOIN `app_lookup` ON
									`app_lookup`.`lookup_value` = `employee_travel_cost`.`type`
								AND app_lookup.`valid` = '1'
								AND app_lookup.`lookup_id` = 'travel_cost_type'
							LEFT JOIN `dictionary` ON
									`dictionary`.`dict_id` = app_lookup.`dict_id`
								AND `dictionary`.`valid` = '1'
								AND `dictionary`.`lang` = '{$lang}'
							LEFT JOIN `app_lookup` alrr ON
									alrr.`lookup_value` = `employee_travel_cost`.`reimbursement_rate`
								AND alrr.`valid` = '1'
								AND alrr.`lookup_id` = 'reimbursement_rate'
							LEFT JOIN `dictionary` drr ON
									drr.`dict_id` = alrr.`dict_id`
								AND drr.`valid` = '1'
								AND drr.`lang` = '{$lang}'
							WHERE
									`employee`.`employee_id` = '{row_id_p1}'
								AND `employee`.`status` = $stPublished
								AND `employee_travel_cost`.`row_id` IS NOT NULL
							GROUP BY employee_contract.employee_contract_id ,`employee_travel_cost`.`valid_from`
							ORDER BY
								`employee_travel_cost`.`valid_from` DESC, `employee_travel_cost`.`valid_to` DESC
						",
						"modelName" => "EmployeeTravelCost",
						"primaryKey" => "row_id",
					];
		}

		if (weHaveModule("ttwa-wwm") && (App::hasRight("employee/employeeDeliveredArticleTab", "view") || App::isRootSessionEnabled()))
		{
			$wizards["dhtmlxGrid"][] =
					[
						"contentId" => "employeeDeliveredArticleTab",
						"contentTitle" => Dict::getValue('tab_employeetabs_employee_delivered_article'),
						"tabSQL" => self::getTransactionsSQL(),
						"modelName" => "ArticleTransactions",
						"primaryKey" => "row_id",
					];
		}

		$dinamicTabs=Tab::getTabs();

		foreach($dinamicTabs as $tab)
		{
			if(App::hasRight("employee/dinamicTab_".$tab["tab_id"], "view"))
			{
				$tabColumns= TabColumn::getColumns($tab["tab_id"]);

				$tabSQL="SELECT
						";
				if($tab["connect"]===Tab::CONNECT_EMPLOYEE)
				{
					$tabSQL.="e.`employee_id`,
						e.`emp_id`,
						";
				}
				elseif($tab["connect"]===Tab::CONNECT_EMPLOYEE_CONTRACT)
				{
					$tabSQL.="ec.`employee_contract_id`,
						ec.`employee_contract_number`,
					";
				}

				foreach($tabColumns as $tabColumn)
				{
					$tabSQL.="eti_".$tabColumn['column_id'].".`value` as `".$tabColumn['column_id']."`,
					";
				}

				if((int)$tab["valid"])
				{
					$tabSQL.="et.`valid_from`,
						et.`valid_to`,
					";
				}
				$tabSQL.="et.`row_id`
					FROM `employee` e
					LEFT JOIN `employee_contract` ec ON
							ec.`employee_id` = e.`employee_id`
						AND ec.`status` = $stPublished
						AND ec.`valid_from`<=IFNULL(e.`valid_to`,'$defaultEnd')
						AND IFNULL(ec.`valid_to`,'$defaultEnd')>=e.`valid_from`
						AND ec.`ec_valid_from`<=IFNULL(e.`valid_to`,'$defaultEnd')
						AND IFNULL(ec.`ec_valid_to`,'$defaultEnd')>=e.`valid_from`
					LEFT JOIN employee_tab et ON
							et.`tab_id`='".$tab["tab_id"]."'
						AND et.`status` = $stPublished
					";
				if($tab["connect"]===Tab::CONNECT_EMPLOYEE)
				{
					$tabSQL.="AND et.`connect_id`= e.`employee_id`
					";
					if((int)$tab["valid"])
					{
						$tabSQL.="AND et.`valid_from`<=IFNULL(e.`valid_to`,'$defaultEnd')
								AND IFNULL(et.`valid_to`,'$defaultEnd')>=e.`valid_from`
						";
					}
				}
				elseif($tab["connect"]===Tab::CONNECT_EMPLOYEE_CONTRACT)
				{
					$tabSQL.="AND et.`connect_id`= ec.`employee_contract_id`
					";
					if((int)$tab["valid"])
					{
						$tabSQL.="AND et.`valid_from`<=IFNULL(ec.`valid_to`,'$defaultEnd')
								AND IFNULL(et.`valid_to`,'$defaultEnd')>=ec.`valid_from`
								AND et.`valid_from`<=IFNULL(ec.`ec_valid_to`,'$defaultEnd')
								AND IFNULL(et.`valid_to`,'$defaultEnd')>=ec.`ec_valid_from`
						";
					}
				}
				foreach($tabColumns as $tabColumn)
				{
					$tabSQL.="LEFT JOIN `employee_tab_item` eti_".$tabColumn['column_id']." ON
							eti_".$tabColumn['column_id'].".`employee_tab_id`=et.`employee_tab_id`
						AND eti_".$tabColumn['column_id'].".`column_id`='".$tabColumn['column_id']."'
						AND eti_".$tabColumn['column_id'].".`status` = $stPublished
					";
				}
				$tabSQL.="WHERE
							e.`employee_id` = '{row_id_p1}'
						AND e.`status` = $stPublished
						AND et.`row_id` IS NOT NULL
					GROUP BY et.`row_id`
					";
				if((int)$tab["valid"])
				{
					$tabSQL.="ORDER BY et.`valid_from` DESC, et.`valid_to` DESC
					";
				}
				$wizards["dhtmlxGrid"][] =
					[
						"contentId" => "dinamicTab_".$tab["tab_id"],
						"contentTitle" => Dict::getValue($tab["dict_id"]),
						"tabSQL" => $tabSQL,
						"modelName" => "EmployeeTab",
						"primaryKey" => "row_id",
						'loadDataFromSQL' => 1
					];
			}
		}

		$out = self::rearrangeEmployeeTabs($wizards);

		$correctOutput = $out;	//	az eredeti verzió szerint ennyi lenne a return value
		if($acVerify) AnyCache::check("wizards",$acKey,$correctOutput);
		if($acEnable) AnyCache::set($acKey,$correctOutput,"employee,employee_contract,employee_tab,employee_tab_id");

		return $out;

	}

	/**
	 * Ha app_settingben meg van a adva a dolgozó kezelés tabok sorrendje, átrendezi a wizards tömböt.
	 */
	private static function rearrangeEmployeeTabs(&$wizards)
	{
		$employeeTabsOrder = App::getSetting('employeeTabsOrder');
		$employeeTabs = [];

		if ($employeeTabsOrder !== '0' )
		{
			$employeeTabs = explode(';', $employeeTabsOrder);
			$tempWizards = [];

			foreach ($employeeTabs as $contentId)
			{
				foreach ($wizards['dhtmlxGrid'] as $wizardTab)
				{
					if ($contentId === $wizardTab['contentId'])
					{
						$tempWizards['dhtmlxGrid'][] = $wizardTab;
					}
				}
			}

			$wizards = $tempWizards;
		}
		return $wizards;
	}

	public function getTransactionsSQL()
	{
		// Hozzáfért raktárak
		$warehousesFromModel = WarehouseAuth::getAccessToWarehouse();
		$warehouses = implode("' , '", $warehousesFromModel);

		$warehouseCondition = (empty($warehouses)) ? '1=1 ' : "ats.`warehouse_id` IN ('" . $warehouses . "') ";
		$articleToPositionSetting = App::getSetting('used_article_column_in_article_to_position');
		$lang = Dict::getLang();

		// App Setting: szülő kategória / kategória / cikk alapján JOIN
		if ($articleToPositionSetting === 'article_category_id') {
			$articleToPositionJoinCondition = " a2p.`article_category_id` = ats.`article_category_id` ";
		} else {
			if ($articleToPositionSetting === 'article_category_parent_id') {
				$articleToPositionJoinCondition = " a2p.`article_category_parent_id` = ats.`parent_category_id` ";
			} else {
				$articleToPositionJoinCondition = " a2p.`article_id` = ats.`article_id` ";
			}
		}

		// Jogosultságok
		$arg = new ApproverRelatedGroup;
		$gargSQL = $arg->getApproverReleatedGroupSQL("Unit", "workWear", false, "CURDATE()");

		$SQL = "
			(SELECT
				ats.`row_id`,
				ats.`employee_id`,
				warehouse.warehouse_name AS `warehouse_id`,
				a2p.`employee_position_id`,
				ac2.article_category_name AS `parent_category_id`,
				ac.`article_category_name` AS article_category_id,
				a.`article_name` AS article_id,
				ats.`quantity`,
				ats.`note`,
				dict.dict_value AS status,
				ats.`signature`,
				CASE
					WHEN
							ats.`status` = " . Status::STOCK_DELIVERED . "
						AND ats.`wearing_date` IS NOT NULL
						AND CURDATE() > ats.`wearing_date`
						THEN '" . Dict::getValue("article_request_expired_wearing_time") . "'
					WHEN
							ats.`status` = " . Status::STOCK_DELIVERED . "
						AND ats.`expiration_date` IS NOT NULL
						AND CURDATE() > ats.`expiration_date`
						THEN '" . Dict::getValue("article_request_expired_expiration_time") . "'
					ELSE ''
				END AS expired,
				a.`item_number`,
				IF(ats.`status` = " . Status::STOCK_REQUESTED . ", ats.`due_date`, '') as due_date,
				IF(
					ats.`status` NOT IN ( ". Status::STOCK_WASTED . ", " . Status::STOCK_REJECTED . "),
					IF(
						IFNULL(
							(SELECT
								IFNULL(a2p.`quantity`, 0) as quantity_for_employee_position
							FROM `article_to_position` a2p
							WHERE
								(CASE
									WHEN
											ats.`expiration_date` IS NULL
										AND CURDATE() >
											CASE
												WHEN a2p.`wearing_time_type` = 'year' THEN DATE(ats.`delivered_on` + INTERVAL CAST(a2p.`wearing_time` AS UNSIGNED) year)
												WHEN a2p.`wearing_time_type` = 'month' THEN DATE(ats.`delivered_on` + INTERVAL CAST(a2p.`wearing_time` AS UNSIGNED) month)
												WHEN a2p.`wearing_time_type` = 'day' THEN DATE(ats.`delivered_on` + INTERVAL CAST(a2p.`wearing_time` AS UNSIGNED) day)
											END
										THEN 'expired'
									WHEN
											ats.`status` = " . Status::STOCK_DELIVERED . "
										AND ats.`expiration_date` IS NOT NULL
										AND CURDATE() > ats.`expiration_date`
										THEN 'expired'
									ELSE ''
								END) <> 'expired'
							AND a2p.`status` = " . Status::PUBLISHED . "
							AND $articleToPositionJoinCondition
							AND a2p.`employee_position_id` = ec.`employee_position_id`
						), 0) <= 0,
						'" . Dict::getValue('not_entitled') . "',
						'" . Dict::getValue('entitled') . "'
					),
					''
				) AS entitled_or_not,
				CASE
					WHEN
							ats.`status` = " . Status::STOCK_DELIVERED . "
						AND ats.`wearing_date` IS NOT NULL
						AND CURDATE() > ats.`wearing_date`
						THEN ats.`wearing_date`
					WHEN
							ats.`status` = " . Status::STOCK_DELIVERED . "
						AND ats.`expiration_date` IS NOT NULL
						AND CURDATE() > ats.`expiration_date`
						THEN ats.`expiration_date`
					ELSE ''
				END AS expiration_date,
				ats.`expiration_date` AS expire_date
			FROM `article_transactions` ats
			LEFT JOIN `article` a ON
					a.`article_id` = ats.`article_id`
				AND a.`status` = " . Status::PUBLISHED . "
				AND DATE(ats.`created_on`) BETWEEN a.`valid_from` AND IFNULL(a.`valid_to`,'" . App::getSetting("defaultEnd") . "')
			LEFT JOIN `employee_contract` ec ON
					ats.`employee_id` = ec.`employee_id`
				AND ec.`status` = " . Status::PUBLISHED . "
				AND DATE(ats.`created_on`) BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`,'" . App::getSetting("defaultEnd") . "')
				AND DATE(ats.`created_on`) BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`,'" . App::getSetting("defaultEnd") . "')
			LEFT JOIN `article_category` ac ON
					ats.`article_category_id` = ac.`article_category_id`
				AND ac.`status` = " . Status::PUBLISHED . "
				AND DATE(ats.`created_on`) BETWEEN ac.`valid_from` AND IFNULL(ac.`valid_to`,'" . App::getSetting("defaultEnd") . "')
			LEFT JOIN `article_category` ac2 ON
					ats.`parent_category_id` = ac2.`article_category_id`
				AND ac2.`status` = " . Status::PUBLISHED . "
				AND DATE(ats.`created_on`) BETWEEN ac2.`valid_from` AND IFNULL(ac2.`valid_to`,'" . App::getSetting("defaultEnd") . "')
			LEFT JOIN `article_to_position` a2p ON
					a2p.`employee_position_id` = ec.`employee_position_id`
				AND $articleToPositionJoinCondition
			LEFT JOIN warehouse ON
					warehouse.warehouse_id = ats.warehouse_id
				AND warehouse.status = " . Status::PUBLISHED . "
				AND DATE(ats.`created_on`) BETWEEN warehouse.`valid_from` AND IFNULL(warehouse.`valid_to`,'" . App::getSetting("defaultEnd") . "')
			LEFT JOIN `employee` e ON
					ec.`employee_id` = e.`employee_id`
				AND e.`status` = " . Status::PUBLISHED . "
				AND DATE(ats.created_on) BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '" . App::getSetting("defaultEnd") . "')
			";
			$SQL .= EmployeeGroup::getAllActiveLeftJoinSQL("ec", "ats.created_on");
			$SQL .= "
			LEFT JOIN `unit` ON
					unit.`unit_id`=" . EmployeeGroup::getActiveGroupSQL("unit_id", "e") . "
				AND unit.`status` = " . Status::PUBLISHED . "
				AND DATE(ats.`created_on`) BETWEEN unit.`valid_from` AND IFNULL(unit.`valid_to`, '" . App::getSetting("defaultEnd"). "')
			LEFT JOIN `workgroup` ON
					workgroup.`workgroup_id`=" . EmployeeGroup::getActiveGroupSQL("workgroup_id", "ec") . "
				AND workgroup.`status` = " . Status::PUBLISHED . "
				AND DATE(ats.`created_on`) BETWEEN workgroup.`valid_from` AND IFNULL(workgroup.`valid_to`, '" . App::getSetting("defaultEnd") . "')
			LEFT JOIN `company_org_group1` ON
					company_org_group1.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL('company_org_group1_id', 'e') . "
				AND company_org_group1.status = " . Status::PUBLISHED . "
				AND DATE(ats.`created_on`) BETWEEN company_org_group1.`valid_from` AND IFNULL(company_org_group1.`valid_to`, '" . App::getSetting("defaultEnd") . "')
			LEFT JOIN `company_org_group2` ON
					company_org_group2.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL('company_org_group2_id', 'e') . "
				AND company_org_group2.`status` = " . Status::PUBLISHED . "
				AND DATE(ats.`created_on`) BETWEEN company_org_group2.`valid_from` AND IFNULL(company_org_group2.`valid_to`, '" . App::getSetting("defaultEnd") . "')
			LEFT JOIN `company_org_group3` ON
					company_org_group3.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL('company_org_group3_id', 'e') . "
				AND company_org_group3.`status` = " . Status::PUBLISHED . "
				AND DATE(ats.`created_on`) BETWEEN company_org_group3.`valid_from` AND IFNULL(company_org_group3.`valid_to`, '" . App::getSetting("defaultEnd") . "')
			LEFT JOIN app_lookup ON
					app_lookup.lookup_value = ats.status
				AND app_lookup.lookup_id = 'article_request_status'
				AND app_lookup.valid = 1
			LEFT JOIN dictionary dict ON
					dict.dict_id = app_lookup.dict_id
				AND dict.lang = '{$lang}'
				AND dict.valid = 1
		";

		if (isset($gargSQL["join"])) {
			$SQL .= $gargSQL["join"];
		}

		$SQL .= "
				WHERE
						" . $warehouseCondition . "
					AND	ats.employee_id = '{row_id_p1}'
					AND ats.`status` NOT IN (" . Status::STOCK_RECEIPT . ", " . Status::STOCK_DELETED . ") ";

		if (isset($gargSQL["where"])) {
			$SQL .= $gargSQL["where"];
		}

		$SQL .= "GROUP BY ats.`article_transaction_id`)";

		return $SQL;
	}
}