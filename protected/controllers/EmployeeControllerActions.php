<?php

Yang::loadComponentNamespaces('EmployeeManagement');
Yang::loadComponentNamespaces('Employee');
\Yang::loadComponentNamespaces('Notification');

use Components\Employee\Enum\EmployeeControllerEnum;
use Components\EmployeeManagement\Builder\ActionConfigBuilder;
use Components\EmployeeManagement\Descriptor\EmployeeGroupConfigDescriptor;
use Components\Notification\Enum\NotificationEventsEnum;
use Components\Notification\DTO\NotificationConfigRequirementsDTO;
use Components\Notification\DTO\NotificationDataChangeDTO;

trait EmployeeControllerActions
{
    public function actionIndex($layout = '//employee/layouts/indexLayout', $view = '/Grid2/index', $params = [])
    {
        $this->getForbiddenEmployees();

        parent::actionIndex($layout, $view, $params);
    }

    public function actionDialog(
        $layout = '//employee/layouts/wizardDialogLayout',
        $view = '/Grid2/wizardDialog',
        $additionalParams = []
    ) {
        $dialogMode = (int)requestParam('dialogMode');
        $editPK = requestParam('editPK');

        $editPKArr = !is_null($editPK) ? explode('_', $editPK) : [];
        $employee_id = $editPKArr[0] ?? null;
        $employee_contract_id = isset($editPKArr[1]) ? EmployeeContract::getEmployeeContractIdByEmployeeId(
            $employee_id,
            $editPKArr[1]
        ) : null;
        $hasRequests = BulkGroupChangeByCogRequest::haveBulkGroupChangeByCogRequest($employee_contract_id);

        $forbiddenTabs = App::getSetting('employeeForbiddenTabs');
        $forbiddenEmployee = !empty($forbiddenTabs) && $this->isForbiddenEmployee($employee_id);

        $configByModel = (new ActionConfigBuilder())->build();

        foreach ($configByModel->getModelConfigs() as $modelConfig) {
            $additionalParams['readOnly'][$modelConfig->getTabName()] =
                App::getRight($this->getControllerID(), $modelConfig->getOperation());
        }

        //Rosenberger csoportváltás iggénylés  miatt tiltani kell a módosítást
        if ($dialogMode === 1 && $hasRequests) {
            foreach ($configByModel->getModelConfigs() as $modelConfig) {
                $additionalParams['readOnly'][$modelConfig->getTabName()] = $modelConfig->tabRightOverrideRequest();
            }
        }

        $additionalParams['readOnly']['employeeDeliveredArticleTab'] = true;
        $additionalParams['forbiddenEmployee'] = $forbiddenEmployee;
        $additionalParams['forbiddenTabs'] = $forbiddenTabs;

        parent::actionDialog($layout, $view, $additionalParams);

        if ($dialogMode === 0) {
            echo '<script type="text/javascript">
				$(document).ready(function() {
					$("#wizardContentemployeeTab .valid_from, #wizardContentemployeeTab .valid_to").change(function() {
						//console.log("AAA1");
						if ($(this).hasClass("valid_from")) {
							$(".valid_from").val($(this).val());
						} else if ($(this).hasClass("valid_to")) {
							$(".valid_to").val($(this).val());
						}
					});
				});
			</script>';
        }
    }

    public function actionSave(
        $data = [],
        $modelName = null,
        $pk = null,
        $vOnly = false,
        $ret = false,
        $contentId = null
    ) {
        if (Yang::getUserID() === null) {
            echo json_encode([
                'status' => 0,
                'error' => Dict::getValue('pleaseSignIn'),
            ]);
            return;
        }
        $generate_from = requestParam('generate_from');
        $editPK = requestParam('editPK');

        if ($generate_from === EmployeeControllerEnum::EMPLOYEE_LOCK_DIALOG || $generate_from === EmployeeControllerEnum::EMPLOYEE_UNLOCK_DIALOG) {
            $form = requestParam("dialogInput_$generate_from");

            $editPKArr = explode('_', $editPK);

            $employeeId = $editPKArr[0];
            $filterDate = DateTimeImmutable::createFromFormat('Y-m-d', $editPKArr[1] ?? null);
            $NewValidTo = DateTimeImmutable::createFromFormat(
                'Y-m-d',
                $form[EmployeeControllerEnum::EMPLOYEE_LOCK_DIALOG_DATE_COLUMN_NAME] ??
                ($form[EmployeeControllerEnum::EMPLOYEE_UNLOCK_DIALOG_DATE_COLUMN_NAME] ?? null)
            );

            switch ($generate_from) {
                case EmployeeControllerEnum::EMPLOYEE_LOCK_DIALOG:
                    $this->lockDataAllEmployeeTables($employeeId, $filterDate, $NewValidTo);
                    break;
                case EmployeeControllerEnum::EMPLOYEE_UNLOCK_DIALOG:
                default:
                    $this->unLockDataAllEmployeeTables($employeeId, $filterDate, $NewValidTo);
                    break;
            }

            $this->lockUserByEmployeeId($employeeId, $NewValidTo);

            $status = [
                'status' => 1,
                'error' => null,
            ];

            echo json_encode($status);
            return;
        }
        parent::actionSave($data, $modelName, $pk, $vOnly, $ret);

        $dialogInput_employeeTab = requestParam('dialogInput_employeeTab');

        if (Yang::getParam('customerDbPatchName') === 'bos' && isset($dialogInput_employeeTab['emp_id'])) {
            Yang::log('sync finished', 'log', 'sys.employeeLogaSync');
        }
    }

    public function actionDelete($modelName = null, $hasRight = false)
    {
        $ids = requestParam('ids');

        $idsArray = explode(';', $ids);

        $hasRequests = false;

        foreach ($idsArray as $editPK) {
            $editPKArr = explode('_', $editPK);
            $employee_contract_id = $editPKArr[0] ?? null;

            $hasRequests = $hasRequests || BulkGroupChangeByCogRequest::haveBulkGroupChangeByCogRequest(
                    $employee_contract_id
                );
        }

        if ($hasRequests) {
            $status = [
                'status' => 0,
                'message' => Dict::getValue('employee_under_modification_cant_delete'),
            ];

            echo json_encode($status);
        }

        parent::actionDelete($modelName, $hasRight);
    }

    /**
     * @throws Exception
     */
    public function lockEmployee($employeeId, $today, $exitDate): void
    {
        $this->lockDataAllEmployeeTables($employeeId, new DateTimeImmutable($today), new DateTimeImmutable($exitDate));
        Yang::log(date('Y-m-d') . ' - Lock employee tables by employee_id: ' . $employeeId, 'log', 'system.EmployeeImport');
    }

    private function lockDataAllEmployeeTables(
        string $employeeId,
        DateTimeImmutable $filterDate,
        DateTimeImmutable $lockDate
    ): void {
        $dynamicTabs = Tab::getTabs();
        $configByModel = (new ActionConfigBuilder())->build();
        $employeeContractId = EmployeeContract::getEmployeeContractIdByEmployeeId(
            $employeeId,
            $filterDate->format('Y-m-d')
        );

        if (is_null($employeeContractId)) {
            return;
        }

        foreach ($configByModel->getModelConfigs() as $config) {
            $identifier = $this->modelContainsEmployeeContractId($config->getModelName()) ?
                $employeeContractId :
                $employeeId;
            $this->lockEmployeeDataByModel($config->getModelName(), $identifier, $lockDate);
        }

        foreach ($dynamicTabs as $tab) {
            $this->lockEmployeeTabData(
                $tab['tab_id'],
                $tab['connect'] === Tab::CONNECT_EMPLOYEE ?
                    $employeeId :
                    $employeeContractId,
                $lockDate
            );
        }
    }

    private function lockEmployeeDataByModel(string $model, string $identifier, DateTimeImmutable $lockDate): void
    {
        if (empty($model) || !class_exists($model) || empty($identifier)) {
            throw new Exception('lockEmployeeByModelSave: invalid parameters');
        }
        $defaultEnd = App::getSetting('defaultEnd');

        $set = [
            'valid_to' => $lockDate->format('Y-m-d'),
            'modified_by' => Yang::getUserID() ?? 'import',
            'modified_on' => date('Y-m-d H:i:s'),
        ];
        $criteria = new CDbCriteria();
        $criteria->addCondition("DATE(:validityLockedDate) < DATE(IFNULL(`valid_to`,'{$defaultEnd}')) ");
        $criteria->params = [
            ':validityLockedDate' => $lockDate->format('Y-m-d'),
            ':identifier' => $identifier,
        ];
        $criteria->addCondition(
            $this->modelContainsEmployeeContractId($model) ?
                '`employee_contract_id` = :identifier' :
                '`employee_id` = :identifier'
        );
        if ($model === EmployeeContract::class) {
            $set['ec_valid_to'] = $lockDate->format('Y-m-d');
            $criteria->addCondition(
                "DATE(:contractLockedDate) < DATE(IFNULL(`ec_valid_to`,'{$defaultEnd}'))"
            );
            $criteria->params[':contractLockedDate'] = $lockDate->format('Y-m-d');
        }
        $criteria->addInCondition('`status`', [Status::PUBLISHED, Status::DRAFT, Status::REJECTED]);
        if ($model === Employee::class) {
            NotificationOnChangeConfig::handleNotificationOnChange(
                new NotificationConfigRequirementsDTO($model, NotificationEventsEnum::LOCK_EMPLOYEE),
                new NotificationDataChangeDTO(['employee_id' => $identifier], $set)
            );
        }
        if ($model::model()->updateAll($set, $criteria) === 0) {
            Yang::log("Lock - Save unsuccessful. Identifier: {$identifier}, model: {$model}", 'log');
            return;
        }
        Yang::log(
            "Lock - Save successful. Identifier: {$identifier}, " .
            "model: {$model}, date: {$lockDate->format('Y-m-d')}",
            'log'
        );
    }

    private function lockEmployeeTabData(string $tabId, string $identifier, DateTimeImmutable $lockDate): void
    {
        $defaultEnd = App::getSetting('defaultEnd');

        $set = [
            'valid_to' => $lockDate->format('Y-m-d'),
            'modified_by' => Yang::getUserID(),
            'modified_on' => date('Y-m-d H:i:s'),
        ];
        $criteria = new CDbCriteria();
        $criteria->addCondition("DATE(:validityLockedDate) < DATE(IFNULL(`valid_to`,'{$defaultEnd}')) ");
        $criteria->addCondition('`connect_id` = :identifier');
        $criteria->addCondition('`tab_id` = :tabId');
        $criteria->params = [
            ':validityLockedDate' => $lockDate->format('Y-m-d'),
            ':identifier' => $identifier,
            ':tabId' => $tabId,
        ];
        $criteria->addInCondition('`status`', [Status::PUBLISHED, Status::DRAFT, Status::REJECTED]);

        if (EmployeeTab::model()->updateAll($set, $criteria) === 0) {
            Yang::log(
                "lock - Save unsuccessful. Identifier: {$identifier}, " .
                "TabId: {$tabId}",
                'log'
            );
            return;
        }
        Yang::log(
            "Lock - Save successful. Identifier: {$identifier}, " .
            "TabId: {$tabId}, date: {$lockDate->format('Y-m-d')}",
            'log'
        );
    }

    private function unLockDataAllEmployeeTables(
        string $employeeId,
        DateTimeImmutable $filterDate,
        DateTimeImmutable $lockDate
    ): void {
        $dynamicTabs = Tab::getTabs();
        $configByModel = (new ActionConfigBuilder())->build();
        $employeeContractId = EmployeeContract::getEmployeeContractIdByEmployeeId(
            $employeeId,
            $filterDate->format('Y-m-d')
        );

        foreach ($configByModel->getModelConfigs() as $config) {
            $identifier = $this->modelContainsEmployeeContractId($config->getModelName()) ?
                $employeeContractId :
                $employeeId;
            if ($config->getModelName() === EmployeeGroup::class) {
                $this->unLockEmployeeGroupData($config->getGroupConfig(), $identifier, $lockDate);
                continue;
            }
            $this->unLockEmployeeDataByModel($config->getModelName(), $identifier, $lockDate);
        }

        foreach ($dynamicTabs as $tab) {
            $this->unLockEmployeeTabData(
                $tab['tab_id'],
                $tab['connect'] === Tab::CONNECT_EMPLOYEE ?
                    $employeeId :
                    $employeeContractId,
                $lockDate
            );
        }
    }

    private function unLockEmployeeGroupData(EmployeeGroupConfigDescriptor $groupConfig, string $identifier, DateTimeImmutable $newValidTo): void
    {
        if (empty($identifier)) {
            throw new Exception('unLockEmployeeGroupData: identifier parameters is invalid');
        }

        if (!$groupConfig->hasGroupConfigByFieldName()) {
            return;
        }

        foreach ($groupConfig->getGroupConfigByFieldName() as $configLineDTO) {
            $defaultEnd = App::getSetting('defaultEnd');

            $criteria = new CDbCriteria();
            $criteria->addCondition('`employee_contract_id` = :identifier');
            $criteria->addCondition('`group_id` = :fieldName');
            $criteria->params = [
                ':identifier' => $identifier,
                ':fieldName' => $configLineDTO->getFieldName(),
            ];
            $criteria->addInCondition('`status`', [Status::PUBLISHED, Status::DRAFT, Status::REJECTED]);
            $criteria->order = "DATE(IFNULL(`valid_to`, '{$defaultEnd}')) DESC";
            $criteria->limit = 1;

            $obj = \EmployeeGroup::model()->find($criteria);
            if (!isset($obj)) {
                Yang::log(
                    "Unlock - There is no data for the employee with ID {$identifier} " .
                    'in the ' . EmployeeGroup::class . ' model, and groupId: ' . $configLineDTO->getFieldName(),
                    'log'
                );
                continue;
            }
            $obj->valid_to = $newValidTo->format('Y-m-d');
            $obj->modified_by = Yang::getUserID();
            $obj->modified_on = date('Y-m-d H:i:s');
            if ($obj->save() === false) {
                Yang::log("Unlock - Save unsuccessful. Identifier: {$identifier}, " .
                    'model: ' . EmployeeGroup::class .
                    ', groupId: ' . $configLineDTO->getFieldName() .
                    ', date: ' . $newValidTo->format('Y-m-d'),
                    'log');
                continue;
            }
            Yang::log(
                "Unlock - Save successful. Identifier: {$identifier}, " .
                'model: ' . EmployeeGroup::class .
                ', groupId: ' . $configLineDTO->getFieldName() .
                ', date: ' . $newValidTo->format('Y-m-d'),
                'log'
            );
        }
    }

    private function unLockEmployeeDataByModel(string $model, string $identifier, DateTimeImmutable $newValidTo): void
    {
        if (empty($model) || !class_exists($model) || empty($identifier)) {
            throw new Exception('unLockEmployeeByModelSave: invalid parameters');
        }
        $defaultEnd = App::getSetting('defaultEnd');

        $criteria = new CDbCriteria();
        $criteria->addCondition(
            $this->modelContainsEmployeeContractId($model) ?
                '`employee_contract_id` = :identifier' :
                '`employee_id` = :identifier'
        );
        $criteria->params = [
            ':identifier' => $identifier,
        ];
        $criteria->addInCondition('`status`', [Status::PUBLISHED, Status::DRAFT, Status::REJECTED]);
        $criteria->order = "DATE(IFNULL(`valid_to`, '{$defaultEnd}')) DESC";
        $criteria->limit = 1;

        $obj = $model::model()->find($criteria);
        if (!isset($obj)) {
            Yang::log(
                "Unlock - There is no data for the employee with ID {$identifier} " .
                "in the {$model} model.",
                'log'
            );
            return;
        }
        $obj->valid_to = $newValidTo->format('Y-m-d');
        $obj->modified_by = Yang::getUserID();
        $obj->modified_on = date('Y-m-d H:i:s');
        if ($model === EmployeeContract::class) {
            $obj->ec_valid_to = $newValidTo->format('Y-m-d');
        }
        if ($obj->save() === false) {
            Yang::log("Unlock - Save unsuccessful. Identifier: {$identifier}, model: {$model}", 'log');
            return;
        }
        Yang::log(
            "Unlock - Save successful. Identifier: {$identifier}, " .
            "model: {$model}, date: {$newValidTo->format('Y-m-d')}",
            'log'
        );
    }

    private function unLockEmployeeTabData(string $tabId, string $identifier, DateTimeImmutable $newValidTo): void
    {
        $defaultEnd = App::getSetting('defaultEnd');
        $criteria = new CDbCriteria();
        $criteria->addCondition('`connect_id` = :identifier');
        $criteria->addCondition('`tab_id` = :tabId');
        $criteria->params = [
            ':identifier' => $identifier,
            ':tabId' => $tabId,
        ];
        $criteria->addInCondition('`status`', [Status::PUBLISHED, Status::DRAFT, Status::REJECTED]);
        $criteria->order = "DATE(IFNULL(`valid_to`, '{$defaultEnd}')) DESC";
        $criteria->limit = 1;

        $employeeTab = EmployeeTab::model()->find($criteria);
        if ($employeeTab === false) {
            Yang::log("Save unsuccessful. Identifier: {$identifier}, TabId: {$tabId}", 'log');
            return;
        }
        $employeeTab->valid_to = $newValidTo->format('Y-m-d');
        $employeeTab->modified_by = Yang::getUserID();
        $employeeTab->modified_on = date('Y-m-d H:i:s');
        if ($employeeTab->save()) {
            Yang::log(
                "Unlock - Save unsuccessful. Identifier: {$identifier}, " .
                "TabId: {$tabId}",
                'log'
            );
            return;
        }
        Yang::log(
            "Unlock - Save successful. Identifier: {$identifier}, " .
            "TabId: {$tabId}, date: {$newValidTo->format('Y-m-d')}",
            'log'
        );
    }

    private function modelContainsEmployeeContractId(string $model): bool
    {
        if (!class_exists($model)) {
            throw new Exception('modelContainsEmployeeContractId: invalid model');
        }

        $acKey = AnyCache::key(__FUNCTION__, func_get_args());
        if (AnyCache::has($acKey)) {
            return AnyCache::get($acKey);
        }
        $obj = new $model();
        $columnNames = array_keys($obj->attributes);
        $isContains = in_array('employee_contract_id', $columnNames);
        AnyCache::set($acKey, $isContains);
        return $isContains;
    }


    /**
     * Dolgozó lezárása esetén a felhasználót is lezárjuk a megadot nappal
     */
    public function lockUserByEmployeeId(string $employee_id, DateTimeImmutable $newValidTo)
    {
        $date = $newValidTo->format('Y-m-d');
        $SQL = "SELECT * FROM `user` u
				WHERE u.employee_id = '{$employee_id}'
				AND u.`status` = " . Status::PUBLISHED . "
				AND '{$date}' BETWEEN u.valid_from AND 
				    IFNULL(u.`valid_to`, '" . App::getSetting('defaultEnd') . "'); ";

        $result = [];
        try {
            $result = dbFetchAll($SQL);
        } catch (Exception $e) {
        }

        $update = '';

        $userId = userID();
        if ((is_array($result)) and count($result) > 0) {
            foreach ($result as $row) {
                $update .= "UPDATE `user` 
                    SET valid_to = '{$date}', modified_by = '{$userId}', modified_on = NOW()
					WHERE row_id = {$row['row_id']} ; ";
            }
        }

        if ($update != '') {
            try {
                dbExecute($update);
            } catch (Exception $e) {
            }
        }
    }

    public function actionGoToMissingCompetencyByPosition()
    {
        $employee_id = substr(requestParam('editPK'), 0, -11);
        $SQL = 'SELECT ' . Employee::getParam('fullname_with_emp_id') . ' AS fullname
				FROM `employee`
				WHERE `status` = ' . Status::PUBLISHED . "
						AND CURDATE() BETWEEN `valid_from` AND default_end(`valid_to`)
						AND employee_id = '{$employee_id}'";
        $res = dbFetchValue($SQL);
        $_SESSION['tiptime'][userID()]['csm/missingCompetencyByPosition']['searchInput_position'] =
            requestParam('dialogInput_employeeChangePositionDialog')['position'];
        $_SESSION['tiptime'][userID()]['csm/missingCompetencyByPosition']['searchInput_employee'] = $employee_id;
        $_SESSION['tiptime'][userID()]['csm/missingCompetencyByPosition']['searchInput_auto_employee'] = $res;

        echo json_encode(['status' => 1]);
    }

    /**
     * Elmenti az összevont feltöltőform tartalmát az uploadDialogApproved jogtól függően:
     *        -    Ha nincs meg a jogosultság, akkor csak a jóváhagyásra váró dolgozók adatait
     *            tartalmazó employee_upload_data táblába mentjük az adatokat.
     *        -    Ha rendelkezik a jogosultsággal, akkor az employee_upload_data táblán felül
     *            a megfelelő modellekbe és felvisszük a dolgozó adatait. Az employee_upload_data
     *            táblába ilyenkor jóváhagyottként kerül be a dolgozó.
     *
     * Az összes táblában a valid_from, valid_to mezők a szerződés ec_valid_from, ec_valid_to
     * értékeit veszik fel.
     *
     * Az employee_id megegyezik az emp_id értékével, az employee_contract_id értéke pedig az
     * emp_id-hez hozzáfűzött "1".
     *
     * Validálási hiba esetén sehova nem kerülnek mentésre az adatok!
     */
    public function actionUploadDialogSave()
    {
        $params = requestParam('dialogInput_uploadDialog');
        $generateForm = requestParam('generateFrom');

        $data = [];
        foreach ($params as $paramId => $paramVal) {
            $paramIdArray = explode('__', $paramId);
            if (count($paramIdArray) !== 2) {
                continue;
            }

            if (!isset($data[$paramIdArray[0]])) {
                $data[$paramIdArray[0]] = [];
            }
            $data[$paramIdArray[0]][$paramIdArray[1]] = $paramVal;
        }

        if (!isset($data['employeeContractTab']['ec_valid_from']) || !isset($data['employeeContractTab']['ec_valid_to'])) {
            return;
        }

        $emp_id = $data['employeeTab']['emp_id'];
        $employee_id = $emp_id;
        $employee_contract_id = $emp_id . '1';
        $valid_from = $data['employeeContractTab']['ec_valid_from'];
        $valid_to = $data['employeeContractTab']['ec_valid_to'];

        $valid_to = !empty($valid_to) ? $valid_to : App::getSetting('defaultEnd');

        $wizards = $this->wizards();
        $wizModels = [];
        $wizTitles = [];
        foreach ($wizards['dhtmlxGrid'] as $wiz) {
            if (array_key_exists($wiz['contentId'], $data)) {
                $wizModels[$wiz['contentId']] = $wiz['modelName'];
                $wizTitles[$wiz['contentId']] = $wiz['contentTitle'];
            }
        }

        $globalParams = [];
        $respArr = [];

        $dataToSave = [];
        foreach ($wizModels as $contentId => $modelName) {
            $model = null;

            if ($modelName == 'EmployeeDocs' && $generateForm == 'uploadDialog') {
                continue;
            }

            if ($modelName && class_exists($modelName)) {
                $model = new $modelName();
            } else {
                continue;
            }

            if (!$model) {
                continue;
            }

            //$modelIdentifyColumn = $modelName::model()->getIdentifyColumn();

            /*if (!isset($globalParams[$modelIdentifyColumn])) {
                $globalParams[$modelIdentifyColumn] = md5(__CLASS__.date('YmdHis').rand(0,1000).$modelName.$modelIdentifyColumn);
            }*/

            $dataToSave[$contentId] = [];
            if (isset($data[$contentId])) {
                $dataToSave[$contentId] = $data[$contentId];
            }

            if ($modelName === 'EmployeeTab') {
                $dataToSave[$contentId]['employee_id'] = $employee_id;
                $dataToSave[$contentId]['employee_contract_id'] = $employee_contract_id;
            }

            if ($model->hasAttribute('emp_id')) {
                $dataToSave[$contentId]['emp_id'] = $emp_id;
            }
            if ($model->hasAttribute('employee_id')) {
                $dataToSave[$contentId]['employee_id'] = $employee_id;
            }
            if ($model->hasAttribute('employee_contract_id')) {
                $dataToSave[$contentId]['employee_contract_id'] = $employee_contract_id;
            }
            if ($model->hasAttribute('valid_from')) {
                $dataToSave[$contentId]['valid_from'] = $valid_from;
            }
            if ($model->hasAttribute('valid_to')) {
                $dataToSave[$contentId]['valid_to'] = ($modelName === 'EmployeeBaseAbsence') ? date(
                    'Y-12-31',
                    strtotime($valid_from)
                ) : $valid_to;
            }

            /*foreach ($globalParams as $gpColumn => $gpValue) {
                $dataToSave[$contentId][$gpColumn] = $gpValue;
            }*/

            $respArr[$contentId] = Grid2Controller::actionSave(
                $dataToSave[$contentId],
                $modelName,
                $model->tableSchema->primaryKey,
                true,
                true,
                $contentId
            );
        }

        $status = [];
        $status['status'] = 1;
        $status['error'] = '';

        foreach ($respArr as $contentId => $errors) {
            $status['status'] = $status['status'] && $errors['status'];

            if (!empty($errors['error'])) {
                $status['error'] .= '<span style="font-size:18px;font-weight:bold;">' . $wizTitles[$contentId] . '</span>' . '<br/>' . $errors['error'];
            }
        }

        if ($status['status']) {
            $status['status'] = 1;
            $status['error'] = null;

            $saveDataToJSON = [];

            foreach ($wizModels as $contentId => $modelName) {
                $model = null;

                if ($modelName && class_exists($modelName)) {
                    $model = new $modelName();
                } else {
                    continue;
                }

                if (!$model) {
                    continue;
                }

                $saveDataToJSON[$modelName] = $dataToSave[$contentId];

                if ((int)App::getRight($this->getControllerID(), 'uploadDialogApproved')) {
                    Grid2Controller::actionSave(
                        $dataToSave[$contentId],
                        $modelName,
                        $model->tableSchema->primaryKey,
                        false,
                        true,
                        $contentId
                    );
                }
            }
            //TODO: Why write, if $globalParams is empty array
            $eud = new EmployeeUploadData();
            $eud->employee_contract_id = $globalParams['employee_contract_id'] ?? '';
            $eud->employee_id = $globalParams['employee_id'] ?? '';
            $eud->data = json_encode($saveDataToJSON);
            $eud->created_by = userID();
            $eud->created_on = date('Y-m-d H:i:s');

            if ((int)App::getRight($this->getControllerID(), 'uploadDialogApproved')) {
                $eud->is_approved = 1;
                $eud->approved_by = $eud->created_by;
                $eud->approved_on = $eud->created_on;
            }

            $eud->save();
        }

        echo json_encode($status);
    }

    public function actionAbsenceCalc()
    {
        $absenceCalc = new EmployeeAbsenceCalculation($_SESSION['tiptime'][userID()]['employee']);

        if ($absenceCalc->calculate()) {
            $status = [
                'status' => 1,
                'title' => Dict::getValue('message'),
                'message' => Dict::getValue('successful_save')
            ];
        } else {
            $status = [
                'status' => 0,
                'title' => Dict::getValue('message'),
                'message' => Dict::getValue('an_error_occured')
            ];
        }

        echo json_encode($status);
    }

    public function actionPrintDeliveredArticle()
    {
        $employeeId = $_GET['employee_id'];
        $rowId = $_GET['pk'];

        $SQL = "
			SELECT 
				CONCAT(employee.last_name, ' ', employee.first_name) AS fullname,
				IF(user.user_id IS NOT NULL, 
				    (IF(emp2.last_name IS NOT NULL, CONCAT(emp2.last_name, ' ', emp2.first_name), user.username)), 
				    ats.created_by
				) AS issuer,
				DATE(ats.expiration_date) AS expiration_date,
				DATE(ats.created_on) AS issue_date,
				ac.article_category_name AS article_category_id,
				ats.signature,
				ep.employee_position_name
			FROM article_transactions ats
			LEFT JOIN article_category ac ON
					ats.article_category_id = ac.article_category_id
				AND ac.status = " . Status::PUBLISHED . "
				AND DATE(ats.created_on) BETWEEN ac.valid_from 
				    AND IFNULL(ac.valid_to,'" . App::getSetting('defaultEnd') . "')
			LEFT JOIN employee ON
					employee.employee_id = ats.employee_id
				AND employee.employee_id = '" . $employeeId . "'
				AND employee.status = " . Status::PUBLISHED . "
				AND DATE(ats.created_on) BETWEEN employee.valid_from 
				    AND IFNULL(employee.valid_to,'" . App::getSetting('defaultEnd') . "')
			LEFT JOIN user ON
					user.user_id = ats.created_by
				AND user.status = " . Status::PUBLISHED . "
				AND DATE(ats.created_on) BETWEEN user.valid_from 
				    AND IFNULL(user.valid_to,'" . App::getSetting('defaultEnd') . "')
			LEFT JOIN employee emp2 ON
					emp2.employee_id = user.employee_id
				AND emp2.status = " . Status::PUBLISHED . "
				AND DATE(ats.created_on) BETWEEN emp2.valid_from 
				    AND IFNULL(emp2.valid_to,'" . App::getSetting('defaultEnd') . "')
			LEFT JOIN employee_contract ec ON
					ec.employee_id = ats.employee_id
				AND ec.status = " . Status::PUBLISHED . "
				AND DATE(ats.created_on) BETWEEN ec.valid_from 
				    AND IFNULL(ec.valid_to,'" . App::getSetting('defaultEnd') . "') 
				AND DATE(ats.created_on) BETWEEN ec.ec_valid_from 
				    AND IFNULL(ec.ec_valid_to,'" . App::getSetting('defaultEnd') . "')
			LEFT JOIN employee_position ep ON
					ep.employee_position_id = ec.employee_position_id
				AND ep.status = " . Status::PUBLISHED . "
				AND DATE(ats.created_on) BETWEEN ep.valid_from 
				    AND IFNULL(ep.valid_to,'" . App::getSetting('defaultEnd') . "')
			WHERE ats.row_id = '" . $rowId . "'
		";

        $data = dbFetchAll($SQL);

        $this->layout = 'ajax';
        $this->render('/customers/schrack/signedCopyOfDeliveredArticleSchrack', [
            'data' => $data
        ]);
    }

    public function actionEmployeeAllData($empId, $date = null): array
    {
        if (empty($empId)) {
            return [];
        }
        return Employee::getEmployeeAllData($empId, $date);
    }

    public function getForbiddenEmployees()
    {
        if (is_null(Yang::session('forbiddenEmployees'))) {
            $employeeForbiddenEdit = dbFetchColumn(
                'SELECT `employee_id` FROM employee_edit_forbidden WHERE `status` = ' . Status::PUBLISHED
            );
            Yang::setSessionValue('forbiddenEmployees', $employeeForbiddenEdit);
        }
    }

    public function isForbiddenEmployee($employee_id): bool
    {
        $this->getForbiddenEmployees();
        return in_array($employee_id, Yang::session('forbiddenEmployees'));
    }
}