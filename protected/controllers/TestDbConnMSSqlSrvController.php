<?php

'yii2-only`;

	namespace app\controllers;
	use Yang;

`/yii2-only';


#yii2: done

/*
	http://tissms019:8008/testDbConn/
	http://tissms019:8008/test_phpinfo.php
	php -m
	php -i | FINDSTR Configuration
  Download:
	Microsoft Drivers for PHP for SQL Server: http://www.microsoft.com/en-us/download/details.aspx?id=20098
	Microsoft® ODBC Driver 11 for SQL Server:  https://www.microsoft.com/en-us/download/details.aspx?id=36434
  PHP.INI:
	; PDO MS SQL
	extension="C:/php/ext/php_sqlsrv_56_ts.dll"
	extension="C:/php/ext/php_pdo_sqlsrv_56_ts.dll"
  Connection String:
	array(
	    'class'=>'CDbConnection',
	    'connectionString' => 'sqlsrv:Server=192.168.0.1;Database=mysecretdatabase',
	    'username' => 'sa',
	    'password' => 'password',
	    'charset' => 'GB2312',
	    'tablePrefix' => '',
	    'enableProfiling'=>true,
	    'enableParamLogging'=>true,
	);
*/
class TestDbConnMSSqlSrvController extends Controller
{
	public function actionIndex()
	{
		header('Content-Type: text/html; charset=utf-8');
		echo "<p>Loading...</p>\n";

		$sql = "SELECT * FROM `employee` LIMIT 0,2";
		$dbConn = Yii::app()->db;
		$command = $dbConn->createCommand($sql);
		$rows = $command->queryAll();

		if(is_array($rows) && count($rows) > 0) {
			foreach ($rows as $row) {
				echo "<p>emp_id: ".$row['emp_id']."</p>\n";
			}
		} else {
			die('ERROR!');
		}
		echo "<hr>\n";

	    // print_r(get_loaded_extensions());

	    $serverName = "10.197.2.22";
		$connectionInfo = array( "Database"=>"sea_dat_fau", "UID"=>"login", "PWD"=>"F@urecia2017+");
	    $conn = sqlsrv_connect( $serverName, $connectionInfo);
		
	    if( $conn === false )
	    {
	         echo "Could not connect.\n";
	         die( print_r( sqlsrv_errors(), true));
	    }

	    if( $client_info = sqlsrv_client_info( $conn))
	    {
	           foreach( $client_info as $key => $value)
	          {
	                  echo $key.": ".$value."\n";
	          }
	    }
	    else
	    {
	           echo "Client info error.\n";
	    }
	    sqlsrv_close( $conn);
	    
	    if (!extension_loaded('sqlsrv')) {
			die('Missing dl: php_pdo_sqlsrv_56_ts.dll');
		}

		// SEAWING ************************************
		//$sql = "SELECT * FROM DOLG_MOZG";
		//$sql = "SELECT TOP 10 dbo.MOZGASOK.MZG_ID FROM dbo.MOZGASOK;
	    /*$sql = "SELECT TOP 5 dbo.MOZGASOK.MZG_ID, dbo.MOZGASOK.MZG_TIPUSKOD, dbo.MOZGASTIPUSOK.MZG_TIPUS, dbo.MOZGASOK.MZG_IDO, dbo.DOLGOZOK.DLG_KOD,
                      dbo.TULAJDONOSOK.TLJ_NEV
			FROM    dbo.DOLGOZOK INNER JOIN
                      dbo.MOZGASOK ON dbo.DOLGOZOK.TLJ_ID = dbo.MOZGASOK.TLJ_ID INNER JOIN
                      dbo.MOZGASTIPUSOK ON dbo.MOZGASOK.MZG_TIPUSKOD = dbo.MOZGASTIPUSOK.MZG_TIPUSKOD INNER JOIN
                      dbo.TULAJDONOSOK ON dbo.DOLGOZOK.TLJ_ID = dbo.TULAJDONOSOK.TLJ_ID
			WHERE   (dbo.DOLGOZOK.DLG_KOD <> N'') AND (NOT (dbo.MOZGASOK.MZG_TIPUSKOD IN (N'2''', N'32', N'3E', N'2G', N'1%'))) AND
                      (dbo.MOZGASOK.MZG_IDO >= CONVERT(DATETIME, '2015-01-01 00:00:00', 102)) AND (dbo.MOZGASOK.TRM_ID IN (123, 124, 130, 104, 105))";
		$dbConn = Yii::app()->SeawingDB;
		$command = $dbConn->createCommand($sql);
		$rows = $command->queryAll();

		if(is_array($rows) && count($rows) > 0) {
			foreach ($rows as $row) {
				echo "<p>MZG_ID: ".$row['MZG_ID']."</p>\n";
			}
		} else {
			die('ERROR!');
		}

		echo "<hr>\n";

		// OPUS *****************************************
		$sql = "SELECT  TOP 5 dbo.JOGVISZONY.BER_AZON, dbo.TORZS.TORZSSZ, dbo.TORZS_ADO.V_NEV, dbo.TORZS_ADO.K_NEV, dbo.TORZS.NEV,
                      dbo.JOGVISZONY.JOGV_TOL, dbo.JOGVISZONY.JOGV_IG, dbo.JOGVTORT.SZELLEMI, EGYSEG_1.EGYSEG_KOD, EGYSEG_1.NEV AS Expr1, dbo.JOGVTORT.AKOD2,
                      dbo.ADAT_KOD.NEV AS Expr2, dbo.JOGVTORT.KIF_HELY, dbo.EGYSEG.NEV AS Expr3, dbo.JOGVTORT.KEZD, dbo.JOGVTORT.VEGE
			FROM  dbo.EGYSEG INNER JOIN
                      dbo.JOGVTORT INNER JOIN
                      dbo.EGYSEG AS EGYSEG_1 ON dbo.JOGVTORT.EGYSEG_KOD = EGYSEG_1.EGYSEG_KOD INNER JOIN
                      dbo.ADAT_KOD ON dbo.JOGVTORT.AKOD2 = dbo.ADAT_KOD.ADAT_KOD ON dbo.EGYSEG.EGYSEG_KOD = dbo.JOGVTORT.KIF_HELY RIGHT OUTER JOIN
                      dbo.JOGVISZONY ON dbo.JOGVTORT.BER_AZON = dbo.JOGVISZONY.BER_AZON RIGHT OUTER JOIN
                      dbo.TORZS_ADO INNER JOIN
                      dbo.TORZS ON dbo.TORZS_ADO.AZON_K = dbo.TORZS.AZON_K ON dbo.JOGVISZONY.AZON_K = dbo.TORZS.AZON_K
			WHERE  (dbo.JOGVTORT.TOROLT = '0') AND (dbo.TORZS.TORZSSZ <> '') AND (dbo.ADAT_KOD.ADAT_CSOPORT = '103') AND (dbo.JOGVISZONY.JOGV_IG >= '20150101') OR
                      (dbo.JOGVTORT.TOROLT = '0') AND (dbo.TORZS.TORZSSZ <> '') AND (dbo.ADAT_KOD.ADAT_CSOPORT = '103') AND (dbo.JOGVISZONY.JOGV_IG = '')
			ORDER BY dbo.TORZS.TORZSSZ";

		$dbConn = Yii::app()->OpusDB;
		$command = $dbConn->createCommand($sql);
		$rows = $command->queryAll();

		if(is_array($rows) && count($rows) > 0) {
			foreach ($rows as $row) {
				echo "<p>BER_AZON: ".$row['BER_AZON']." - ".$row['NEV']."</p>\n";
			}
		} else {
			die('ERROR!');
		}
		*/
	}
}
?>