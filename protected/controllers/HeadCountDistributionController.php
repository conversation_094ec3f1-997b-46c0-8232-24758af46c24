<?php

/**
 * A kontroller meg<PERSON><PERSON><PERSON><PERSON> a megadott munkavá<PERSON><PERSON>ó csoportosítások alapján, hogy 1-1 lehetséges csomport kombinációba naponként hány munkavállaló tartozik.
 *
 * Innote link: head-count-distribution
 */

class HeadCountDistributionController extends Grid2Controller
{
	// Osztályváltozók inicializálás
	private $defaultEnd;
	private $headCountUnsetGroups;
	private $statusPublished = Status::PUBLISHED;
	private $unsortedGroups =
	[
		"company"				=> ["groupBy" => "`company`.`company_id`",						"useGarg" => 1, "multiple" => 1, "dict" => "company"],
		"payroll"				=> ["groupBy" => "`payroll`.`payroll_id`",						"useGarg" => 1, "multiple" => 1, "dict" => "Payroll"],
		"company_org_group1"	=> ["groupBy" => "`company_org_group1`.`company_org_group_id`", "useGarg" => 1, "multiple" => 1, "dict" => "company_org_group1"],
		"company_org_group2"	=> ["groupBy" => "`company_org_group2`.`company_org_group_id`", "useGarg" => 1, "multiple" => 1, "dict" => "company_org_group2"],
		"company_org_group3"	=> ["groupBy" => "`company_org_group3`.`company_org_group_id`", "useGarg" => 1, "multiple" => 1, "dict" => "company_org_group3"],
		"workgroup"				=> ["groupBy" => "`workgroup`.`workgroup_id`",					"useGarg" => 1, "multiple" => 1, "dict" => "workgroup"],
		"unit"					=> ["groupBy" => "`unit`.`unit_id`",							"useGarg" => 1, "multiple" => 1, "dict" => "unit"],
		"cost"					=> ["groupBy" => "`cost`.`cost_id`",							"useGarg" => 0, "multiple" => 1, "dict" => "cost"],
		"cost_center"			=> ["groupBy" => "`cost_center`.`cost_center_id`",				"useGarg" => 0, "multiple" => 1, "dict" => "cost_center"],
		"employee_position"		=> ["groupBy" => "`employee_position`.`employee_position_id`",	"useGarg" => 0, "multiple" => 1, "dict" => "employee_position_name"]
	];
	private $sortGroups = ["company", "payroll", "company_org_group1", "company_org_group2", "company_org_group3", "workgroup", "unit", "cost", "cost_center", "employee_position"];
	private $defaultGroups = [];

	/**
	 * Grid2s szülőosztály konstruktor meghívása
	 */
	public function __construct()
	{
		$this->defaultEnd			= App::getSetting("defaultEnd");
		$this->headCountUnsetGroups	= explode(";", App::getSetting("headCountUnsetGroups"));
		foreach ($this->headCountUnsetGroups as $group) {
			if (array_key_exists($group, $this->unsortedGroups)) {
				unset($this->unsortedGroups[$group]);
			}
		}
		if (Yang::customerDbPatchName() == "bn") {
			$this->sortGroups = ["company", "payroll", "cost", "company_org_group1", "company_org_group2", "company_org_group3", "workgroup", "unit", "cost_center", "employee_position"];
		}
		foreach ($this->sortGroups as $groupKey) {
			if (isset($this->unsortedGroups[$groupKey])) {
				$this->defaultGroups[$groupKey] = $this->unsortedGroups[$groupKey];
			}
		}
		parent::__construct("headCountDistribution");
		$this->maxDays = 999;
	}

	/**
	 * Grid2 inícializálása, használt mode: Array
	 */
	protected function G2BInit()
	{
		parent::setControllerPageTitleId("page_title_head_count_distribution");

		$this->LAGridRights->overrideInitRights("add", false);
		$this->LAGridRights->overrideInitRights("edit", false);
		$this->LAGridRights->overrideInitRights("delete", false);
		$this->LAGridRights->overrideInitRights("paging", true);
		$this->LAGridRights->overrideInitRights("search", true);
		$this->LAGridRights->overrideInitRights("select", false);
		$this->LAGridRights->overrideInitRights("multi_select", false);
		$this->LAGridRights->overrideInitRights("search_header", true);
		$this->LAGridRights->overrideInitRights("export_xlsx", true);
		$this->LAGridRights->overrideInitRights("modify", false);
		$this->LAGridRights->overrideInitRights("reload", true);
		$this->LAGridRights->overrideInitRights("init_open_search", true);

		$this->LAGridDB->enableArrMode();
		$this->LAGridDB->setPrimaryKey("row_id");

		parent::setExportFileName(Dict::getValue("page_title_head_count_distribution"));
		parent::G2BInit();
	}

	/**
	 * Kirakja session-be a kereső paramétereket.
	 * @return void
	 */
	public function actionSetInitProperties()
	{
		$this->layout = "//layouts/ajax";

		$searchBar = requestParam('searchInput');

		if (isset($searchBar)) {
			$controllerID = $this->getControllerID();
			$filterName = str_replace('/', '_', $controllerID) . '_filters';
			Yang::setSessionValue($filterName, $searchBar);
		}

		parent::actionSetInitProperties();
	}

	/**
	 * Biztonsági függvény #1
	 * @return array
	 */
	public function filters() {
        return [
            'accessControl', // perform access control for CRUD operations
        ];
    }

	/**
	 * Biztonsági függvény #2
	 * @return array
	 */
	public function accessRules()
	{
        return
		[
            [
				'allow', // allow authenticated users to access all actions
                'users' => ['@'],
            ],
            [
				'deny',  // deny all users
                'users' => ['*'],
            ],
        ];
	}

	/**
	 * Kereső definiálása - processId: employeeManagement
	 * @return array
	 */
	public function search()
	{
		// garg $$ használva a változó névre, ne változtasd!
		$art				= new ApproverRelatedGroup;
		$company			= $art->getApproverReleatedGroupSQL("Company", "employeeManagement", userID(), "'{valid_from}'", "AND", "allDate", $this->getControllerID());
		$payroll			= $art->getApproverReleatedGroupSQL("Payroll", "employeeManagement", userID(), "'{valid_from}'", "AND", "allDate", $this->getControllerID());
		$company_org_group1	= $art->getApproverReleatedGroupSQL("CompanyOrgGroup1", "employeeManagement", userID(), "'{valid_from}'", "AND", "allDate", $this->getControllerID());
		$company_org_group2	= $art->getApproverReleatedGroupSQL("CompanyOrgGroup2", "employeeManagement", userID(), "'{valid_from}'", "AND", "allDate", $this->getControllerID());
		$company_org_group3	= $art->getApproverReleatedGroupSQL("CompanyOrgGroup3", "employeeManagement", userID(), "'{valid_from}'","AND", "allDate", $this->getControllerID());
		$workgroup			= $art->getApproverReleatedGroupSQL("Workgroup", "employeeManagement", userID(), "'{valid_from}'", "AND", "allDate", $this->getControllerID());
		$unit				= $art->getApproverReleatedGroupSQL("Unit", "employeeManagement", userID(), "'{valid_from}'", "AND", "allDate", $this->getControllerID());

		// kereső dátumok onchangel
		$search =
		[
			'valid_from'	=>
			[
				'col_type'		=> 'ed',
				'dPicker'		=> true,
				'width'			=> '*',
				'label_text'	=> Dict::getValue("valid_from"),
				'default_value'	=> date('Y-m-01'),
				'columnId'		=> 'valid_from',
				'onchange'		=> array_keys($this->defaultGroups)
			],
			'valid_to'		=>
			[
				'col_type'		=> 'ed',
				'dPicker'		=> true,
				'width'			=> '*',
				'label_text'	=> Dict::getValue("valid_to"),
				'default_value'	=> date('Y-m-t'),
				'columnId'		=> 'valid_to',
				'onchange'		=> array_keys($this->defaultGroups)
			]
		];

		foreach ($this->defaultGroups as $group => $info)
		{
			if (Yang::customerDbPatchName() == "bn" && $group == "cost") {
				if (($k = array_search("cost", $search["valid_from"]["onchange"])) !== false) {
					unset($search["valid_from"]["onchange"][$k]);
				}
				if (($k = array_search("cost", $search["valid_to"]["onchange"])) !== false) {
					unset($search["valid_to"]["onchange"][$k]);
				}
			} else {
				// SQL és onchange
				$sql = "
					SELECT
						{$info["groupBy"]} as id,
						" . mb_substr($info["groupBy"], 0, -3) . "name` AS value
					FROM
						`{$group}`
					WHERE
							`{$group}`.`status` = {$this->statusPublished}
						AND `{$group}`.`valid_from` <=  '{valid_to}'
						AND IFNULL(`{$group}`.`valid_to`, '{$this->defaultEnd}') >= '{valid_from}'
				";
				if ($group != "company" && $group != "payroll" && $group != "employee_position")
				{
					if (array_key_exists("company", $this->defaultGroups)) {
						$sql .= "
						AND (`{$group}`.`company_id` IN ('{company}') OR `{$group}`.`company_id` = 'ALL' OR 'ALL' IN ('{company}'))
						";
						$search["company"]["onchange"][] = $group;
					}
					if (array_key_exists("payroll", $this->defaultGroups)) {
						$sql .= "
						AND (`{$group}`.`payroll_id` IN ('{payroll}') OR `{$group}`.`payroll_id` = 'ALL' OR 'ALL' IN ('{payroll}'))
						";
						$search["payroll"]["onchange"][] = $group;
					}
				} else if ($group == "payroll" && array_key_exists("company", $this->defaultGroups)) {
					$sql .= "
						AND (`{$group}`.`company_id` IN ('{company}') OR `{$group}`.`company_id` = 'ALL' OR 'ALL' IN ('{company}'))
					";
					$search["company"]["onchange"][] = $group;
				}
				if ($info["useGarg"]) {
					$gargSQL = $$group;
					$sql .= "
						{$gargSQL["where"]}
					";
				}
				$sql .= "
					GROUP BY
						{$info["groupBy"]}
					HAVING
						id IS NOT NULL
					ORDER BY
						" . mb_substr($info["groupBy"], 0, -3) . "name` ASC
				";

				// Kereső
				$search[$group] =
				[
					'label_text'=> Dict::getValue($info["dict"]),
					'col_type'	=> 'combo',
					'options'	=>
					[
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> $sql,
						'array'	=> [["id" => "ALL", "value" => Dict::getValue("all")]]
					],
					'default_value' => 'ALL'
				];

				// multi szűrő
				if ($info["multiple"]) {
					$search[$group]['multiple']	= 1;
					$search[$group]['col_type']	= 'combo';
					$search[$group]['class']	= 'customSelect2Class';
				}

				// Extra B+N ext.option6 szűrő cég után
				if (Yang::customerDbPatchName() == "bn" && $group == "company")
				{
					$search["valid_from"]["onchange"][]	= "option6";
					$search["valid_to"]["onchange"][]	= "option6";

					$search["option6"] =
					[
						'label_text'=> Dict::getValue("option6"),
						'col_type'	=> 'combo',
						'options'	=>
						[
							'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
							'sql'	=> "
								SELECT
									`employee_ext`.`option6` AS id,
									IF(d.`dict_value` IS NOT NULL AND `option_config`.`type` = 'combo', d.`dict_value`, `employee_ext`.`option6`) AS value
								FROM `employee_ext`
								LEFT JOIN `option_config` ON `option_config`.`option_id` = 'option6' AND `option_config`.`status` = {$this->statusPublished}
								LEFT JOIN `app_lookup` ON `app_lookup`.`lookup_id` = 'option6' AND `app_lookup`.`valid` = 1 AND `app_lookup`.`lookup_value` = `employee_ext`.`option6`
								LEFT JOIN `dictionary` d ON d.`dict_id` = `app_lookup`.`dict_id` AND d.`lang` = '" . Dict::getLang() . "' AND d.`valid` = 1
								WHERE
										`employee_ext`.`status` = {$this->statusPublished}
									AND `employee_ext`.`valid_from` <= '{valid_to}'
									AND IFNULL(`employee_ext`.`valid_to`, '{$this->defaultEnd}') >= '{valid_from}'
								GROUP BY
									`employee_ext`.`option6`
								HAVING
									id IS NOT NULL
								ORDER BY
									IFNULL(d.`dict_value`, `employee_ext`.`option6`) ASC
							",
							'array'	=> [["id" => "ALL", "value" => Dict::getValue("all")]]
						],
						'default_value' => 'ALL'
					];
					$search["option6"]['multiple']	= 1;
					$search["option6"]['col_type']	= 'combo';
					$search["option6"]['class']		= 'customSelect2Class';
				}
			}
		}

		// Egyben adat újragenerálás
		$submit = [
			'submit'	=> [
				'col_type'=>'searchBarReinitGrid',
				'width'=>'*',
				'label_text'=>''
			]
		];

		return array_merge($search, $submit);
	}

	/**
	 * A Grid2 tartalmát feltöltő SQL lekérdezés létrehozása
	 * @return string
	 */
	private function getGridData($search)
	{
		$retArr = [];

		// Init
		$art	= new ApproverRelatedGroup;
		$gargSQL= $art->getApproverReleatedGroupSQL("Employee", "employeeManagement", userID(), $search["valid_from"], "AND", "allDate", $this->getControllerID());
		$SQL = "
			SELECT
				cal.`date`,
				SUM(rowcounter.one) AS employee_count,
		";
		$SQLWHERE = "
			WHERE
					1=1
				AND cal.`date` BETWEEN '{$search["valid_from"]}' AND '{$search["valid_to"]}'
				{$gargSQL["where"]}
		";
		$SQLGROUPBY = "
			GROUP BY
		";
		$SQLORDERBY = "
			ORDER BY
		";

		// Csoportosítás selectek, feltételek és group by
		foreach ($this->defaultGroups AS $group => $info)
		{
			if (isset($search[$group])) {
				if (is_array($search[$group])) { $SQLIN = "('" . implode("', '", $search[$group]) . "')"; } else { $SQLIN = "('" . $search[$group] . "')"; }
				$SQLWHERE .= "
					AND ({$info["groupBy"]} IN {$SQLIN} OR 'ALL' IN {$SQLIN})
				";
			}

			$SQL .= " {$info["groupBy"]} AS {$group},";
			$SQLGROUPBY .= "{$info["groupBy"]}, ";
			$SQLORDERBY .= mb_substr($info["groupBy"], 0, -3) . "name`, ";
			if (Yang::customerDbPatchName() == "bn" && $group == "company")
			{
				if (is_array($search["option6"])) { $SQLIN = "('" . implode("', '", $search["option6"]) . "')"; } else { $SQLIN = "('" . $search["option6"] . "')"; }
				$SQL .= " `employee_ext`.`option6`,";
				$SQLWHERE .= "
					AND (`employee_ext`.`option6` IN {$SQLIN} OR 'ALL' IN {$SQLIN})
				";
				$SQLGROUPBY .= "`employee_ext`.`option6`, ";
				$SQLORDERBY .= "IFNULL(d.`dict_value`, `employee_ext`.`option6`), ";
			}
		}

		// Utsó vessző levág a selectből
		$SQL = mb_substr($SQL, 0, -1);
		$SQL .= "
			FROM `calendar` cal
			LEFT JOIN `employee` ON
					`employee`.`status` = {$this->statusPublished}
				AND cal.`date` BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `employee_contract` ON
					`employee_contract`.`employee_id` = `employee`.`employee_id`
				AND `employee_contract`.`status` = {$this->statusPublished}
				AND cal.`date` BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->defaultEnd}')
				AND cal.`date` BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `employee_ext` ON
					`employee_ext`.`employee_id` = `employee`.`employee_id`
				AND `employee_ext`.`status` = {$this->statusPublished}
				AND cal.`date` BETWEEN `employee_ext`.`valid_from` AND IFNULL(`employee_ext`.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `option_config` ON `option_config`.`option_id` = 'option6' AND `option_config`.`status` = {$this->statusPublished}
			LEFT JOIN `app_lookup` ON `app_lookup`.`lookup_id` = 'option6' AND `app_lookup`.`valid` = 1 AND `app_lookup`.`lookup_value` = `employee_ext`.`option6`
			LEFT JOIN `dictionary` d ON d.`dict_id` = `app_lookup`.`dict_id` AND d.`lang` = '" . Dict::getLang() . "' AND d.`valid` = 1
			LEFT JOIN ( SELECT 1 AS `one` ) AS rowcounter ON 1=1
		";
		$SQL .= EmployeeGroup::getAllActiveLeftJoinSQL();
		$SQL .= EmployeeGroup::getAllBaseTablesWithGroup();
		$SQL .= $SQLWHERE;
		$SQL .= $SQLGROUPBY . " cal.`date`";
		$SQL .= mb_substr($SQLORDERBY, 0, -2) . " ASC";

		$results = dbFetchAll($SQL);

		$dateCols = [];
		$period = new DatePeriod(
			new DateTime($search["valid_from"]),
			new DateInterval('P1D'),
			new DateTime(date("Y-m-d", strtotime($search["valid_to"] . " +1 day")))
		);
		foreach ($period as $key => $value) {
			$dateCols[$value->format("Y-m-d")] = $key;
		}

		foreach ($results as $res)
		{
			$resKey = "";
			foreach (array_keys($this->defaultGroups) AS $key) {
				$resKey .= $res[$key] . "-";
				if (Yang::customerDbPatchName() == "bn" && $key == "company") { $resKey .= $res["option6"] . "-"; }
			}
			$resKey = md5(mb_substr($resKey, 0, -1));

			if (!isset($retArr[$resKey])) {
				$retArr[$resKey] = $res;
				unset($retArr[$resKey]["date"]);
				unset($retArr[$resKey]["employee_count"]);
				$retArr[$resKey]["col_" . $dateCols[$res["date"]]] = $res["employee_count"];
			} else {
				$retArr[$resKey]["col_" . $dateCols[$res["date"]]] = $res["employee_count"];
			}
		}

		return $retArr;
	}

	/**
	 * Grid2 oszlopok inicializálása
	 * @return array
	 */
	public function columns()
	{
		$columns = [];
		$controllerID = $this->getControllerID();
		$filterName = str_replace('/', '_', $controllerID) . '_filters';
		$filters = Yang::session($filterName, []);
        if (!empty($filters)) {
            // Csoportosítások
            foreach ($this->defaultGroups as $group => $info) {
                $columns[$group] = $this->getColDetails(true, "", 150, "left", $group, $info, $filters["valid_from"], $filters["valid_to"]);
                // B+N extra option6
                if (Yang::customerDbPatchName() == "bn" && $group == "company") {
                    $columns["option6"] = $this->getColDetails(true, "", 150, "left", "option6", [], $filters["valid_from"], $filters["valid_to"]);
                }
            }
            $columns = $this->columnRights($columns);

            // Napok
            $period = new DatePeriod(
                new DateTime($filters["valid_from"]),
                new DateInterval('P1D'),
                new DateTime(date("Y-m-d", strtotime($filters["valid_to"] . " +1 day")))
            );
            foreach ($period as $key => $value) {
                $columns["col_" . $key] = $this->getColDetails(true, "numeric");
            }

        }

		return $columns;
	}

	/**
	 * Columns segítő függvény
	 * @param boolean $export
	 * @param string $exportas
	 * @param int $width
	 * @param string $align
	 * @param string $group
	 * @param array $info
	 * @param string $validFrom
	 * @param string $validTo
	 * @return array
	 */
	private function getColDetails($export = true, $exportas = "", $width = 200, $align = "center", $group = "", $info = [], $validFrom = "", $validTo = "")
	{
		$colDetails =
		[
			'grid'		=> true,
			'export'	=> $export,
			'width'		=> $width,
			'window'	=> false,
			'align'		=> $align
		];

		if ($exportas != "") { $colDetails['export_as'] = $exportas; }

		// Sima mező
		if ($group == "") {
			$colDetails['col_type'] = "ed";
			return $colDetails;
		}

		// Combo nem option6
		if ($group != "option6")
		{
			$colDetails['col_type'] = "combo";
			$colDetails['options'] =
			[
				'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
				'sql'	=> "
					SELECT
						{$info["groupBy"]} as id,
						" . mb_substr($info["groupBy"], 0, -3) . "name` AS value
					FROM
						`{$group}`
					WHERE
							`{$group}`.`status` = {$this->statusPublished}
						AND `{$group}`.`valid_from` <=  '{$validTo}'
						AND IFNULL(`{$group}`.`valid_to`, '{$this->defaultEnd}') >= '{$validFrom}'
					GROUP BY
						{$info["groupBy"]}
					HAVING
						id IS NOT NULL
				"
			];
			return $colDetails;
		} else {
			$colDetails['col_type'] = "combo";
			$colDetails['options'] =
			[
				'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
				'sql'	=> "
					SELECT
						`employee_ext`.`option6` AS id,
						IF(d.`dict_value` IS NOT NULL AND `option_config`.`type` = 'combo', d.`dict_value`, `employee_ext`.`option6`) AS value
					FROM `employee_ext`
					LEFT JOIN `option_config` ON `option_config`.`option_id` = 'option6' AND `option_config`.`status` = {$this->statusPublished}
					LEFT JOIN `app_lookup` ON `app_lookup`.`lookup_id` = 'option6' AND `app_lookup`.`valid` = 1 AND `app_lookup`.`lookup_value` = `employee_ext`.`option6`
					LEFT JOIN `dictionary` d ON d.`dict_id` = `app_lookup`.`dict_id` AND d.`lang` = '" . Dict::getLang() . "' AND d.`valid` = 1
					WHERE
							`employee_ext`.`status` = {$this->statusPublished}
						AND `employee_ext`.`valid_from` <= '{$validTo}'
						AND IFNULL(`employee_ext`.`valid_to`, '{$this->defaultEnd}') >= '{$validFrom}'
					GROUP BY
						`employee_ext`.`option6`
					HAVING
						id IS NOT NULL
				"
			];
			return $colDetails;
		}
	}

	/**
	 * Oszlop feliratok a Grid2-ben
	 * @return array
	 */
	public function attributeLabels()
	{
		$labels = [];

		// Csoportosítások
		foreach ($this->defaultGroups as $group => $info) {
			$labels[$group] = Dict::getValue($info["dict"]);
			// B+N extra option6
			if (Yang::customerDbPatchName() == "bn" && $group == "company") { $labels["option6"] = Dict::getValue("option6"); }
		}

		// Napok
		$controllerID = $this->getControllerID();
		$filterName = str_replace('/', '_', $controllerID) . '_filters';
		$filters = Yang::session($filterName, []);
        if (!empty($filters)) {
            $period = new DatePeriod(
                new DateTime($filters["valid_from"]),
                new DateInterval('P1D'),
                new DateTime(date("Y-m-d", strtotime($filters["valid_to"] . " +1 day")))
            );
            foreach ($period as $key => $value) {
                $labels["col_" . $key] = $value->format("Y-m-d");
            }
        }


		return $labels;
	}

	/**
	 * Headers összesítés miatt
	 * @return array
	 */
	public function headers()
	{
		$widthRowId	= 0;
		$withColId	= 0;
		$i			= 1;
		$colid		= 0;
        $validCol = 0;
		// Csoportosítások
		foreach ($this->defaultGroups as $group => $info)
		{
			$header[$i][$colid] =
			[
				'label_text'	=> Dict::getValue($info["dict"]),
				'col_span'		=> 1,
				'row_span'		=> 1,
				'align'			=> "left",
				'v_align'		=> "center",
				'col_type'		=> "combo",
				'left_frozen'	=> true,
                'row_span_col_index' => $validCol,
			];
            $validCol++;
			$header[$widthRowId][$withColId] =
			[
				'col_id'		=> $group,
				'left_frozen'	=> true,
				'width'			=> 150,
				'col_type'		=> "combo",
				'data_align'	=> "left"
			];

			if ($withColId == 0) {
				$footerText =
				[
					'footer_label_text'	=> Dict::getValue("total"),
					'footer_align'		=> "left",
					'footer_col_span'	=> (Yang::customerDbPatchName() == "bn") ? count(array_keys($this->defaultGroups)) + 1 : count(array_keys($this->defaultGroups)),
					'footer_col_type'	=> "text"
				];
				$header[$widthRowId][$withColId]=Yang::arrayMerge($header[$widthRowId][$withColId],$footerText);
			}
			++$withColId;
			++$colid;
			if (Yang::customerDbPatchName() == "bn" && $group == "company")
			{
				$header[$i][$colid] =
				[
					'label_text'	=> Dict::getValue("option6"),
					'col_span'		=> 1,
					'row_span'		=> 1,
					'align'			=> "left",
					'v_align'		=> "center",
					'col_type'		=> "combo",
					'left_frozen'	=> true,
                    'row_span_col_index' => $validCol
				];
				$header[$widthRowId][$withColId] =
				[
					'col_id'		=> "option6",
					'left_frozen'	=> true,
					'width'			=> 150,
					'col_type'		=> "combo",
					'data_align'	=> "left"
				];
				++$withColId;
				++$colid;
			}
		}

		// Napok
		$controllerID = $this->getControllerID();
		$filterName = str_replace('/', '_', $controllerID) . '_filters';
		$filters = Yang::session($filterName, []);
		$period = new DatePeriod(
			new DateTime($filters["valid_from"]),
			new DateInterval('P1D'),
			new DateTime(date("Y-m-d", strtotime($filters["valid_to"] . " +1 day")))
		);
		foreach ($period as $key => $value)
		{
			$header[$i][$colid] =
			[
				'label_text'	=> $value->format("Y-m-d"),
				'col_span'		=> 1,
				'row_span'		=> 1,
				'align'			=> "center",
				'v_align'		=> "center",
				'col_type'		=> "text",
				'left_frozen'	=> false,
                'row_span_col_index' => $validCol,
                'export_as' => "numeric"
			];
            $validCol++;
			$header[$widthRowId][$withColId] =
			[
				'col_id'		=> "col_" . $key,
				'left_frozen'	=> false,
				'width'			=> 200,
				'col_type'		=> "text",
				'data_align'	=> "center",
                'export_as' => "numeric"
			];

			$header[$widthRowId][$withColId] = $this->addFooterSum($header[$widthRowId][$withColId]);
			++$withColId;
			++$colid;
		}
		ksort($header);
		return $header;
	}


	/**
	 * Összesítés
	 * @param int $column
	 * @return array
	 */
	private function addFooterSum($column)
	{
		$footerSUM =
		[
			'footer_label_text'			=> "=SUM",
			'footer_align'				=> "center",
			'footer_col_span'			=> 1,
			'footer_col_type'			=> "number",
			'footer_col_type_format'	=>"%01.2f"
		];
		$column = Yang::arrayMerge($column, $footerSUM);
		return $column;
	}

	/**
	 * Adathalmaz összeállítása a Grid2 feltöltéséhez
	 * @param string $gridID
	 * @param array $filter
	 * @param bool $isExport
	 * @return array
	 */
	protected function dataArray($gridID, $filter, $isExport = false)
	{
        $regenerate = false;
		foreach ($this->fetchParams as $var => $val) {
			${$var} = $val;
		}

		$results = [];

		if ($regenerate)
		{
			$controllerID	= $this->getControllerID();
			$filterName		= str_replace('/', '_', $controllerID) . '_filters';
			$searchBar		= Yang::session($filterName);

			$results = $this->getGridData($searchBar);
		}

		return $results;
	}
}
