<?php
Yang::loadComponentNamespaces('Employee');

use Components\Employee\Enum\EmployeeControllerEnum;

trait EmployeeControllerStatusButtons
{
	protected function getStatusButtons($gridID = null) {
		if (!isset($gridID)) {
			$gridID = 'dhtmlxGrid';
		} elseif (empty($gridID)) {
			$gridID = 'dhtmlxGrid';
		}

		$buttons = [];

        if (App::hasRight($this->getControllerID(), 'bulkImageUpload')) {
            $buttons['openBulkImportDialog'] = [
                'type' => 'button',
                'id' => 'openBulkImportDialog',
                'class' => 'openBulkImportDialog',
                'name' => 'openBulkImportDialog',
                'img' => '/images/status_icons/st_bulk_upload.png',
                'label' => Dict::getValue('bulkImageUpload'),
                'onclick' => "G2BDialogMultiGrid('dhtmlxGrid','bulkImportDialog',null,0,'./dialog','./save','./gridData',null,'" . $this->getControllerPageTitle() . ' - ' . Dict::getValue('bulkImageUpload') . "','" . Dict::getValue('please_select_line') . "');",
            ];

            $buttons[] = [
                'type' => 'selector',
            ];
        }

        if (App::hasRight($this->getControllerID(), 'import')) {
			$buttons['openImportDialog'] = [
								'type' => 'button',
								'id' => 'openImportDialog',
								'class' => 'openImportDialog',
								'name' => 'openImportDialog',
								'img' => '/images/status_icons/st_upload.png',
								'label' => Dict::getValue('import')/*."(Ctrl+Shift+a)"*/,
								// G2BDialogMultiGrid(grid_id, generate_from, selected_ids, dialogMode, dialogContentUrl, actionSaveUrl, gridDataUrl, params, windowtitle, errortext)
								'onclick' => "G2BDialogMultiGrid('dhtmlxGrid','importDialog',null,0,'./dialog','./save','./gridData',null,'".$this->getControllerPageTitle(). ' - ' .Dict::getValue('import')."','".Dict::getValue('please_select_line')."');",
            ];

			$buttons[] = [
								'type' => 'selector',
            ];
		}

		if (App::hasRight($this->getControllerID(), 'employeeLock')) {
			$buttons['openLockDialog'] = [
								'type' => 'button',
								'id' => 'openImportDialog',
								'class' => 'openLockDialog',
								'name' => 'openLockDialog',
								'img' => '/images/status_icons/st_lock.png',
								'label' => Dict::getValue('lock_employee')/*."(Ctrl+Shift+a)"*/,
								// G2BDialogMultiGrid(grid_id, generate_from, selected_ids, dialogMode, dialogContentUrl, actionSaveUrl, gridDataUrl, params, windowtitle, errortext)
								'onclick' => "G2BDialogMultiGrid('dhtmlxGrid','". EmployeeControllerEnum::EMPLOYEE_LOCK_DIALOG . "',null,1,'./dialog','./save','./gridData',null,'".$this->getControllerPageTitle(). ' - ' .Dict::getValue('lock_employee')."','".Dict::getValue('please_select_line')."');",
            ];

			$buttons[] = [
								'type' => 'selector',
            ];
		}

        if (App::hasRight($this->getControllerID(), 'employeeLock')) {
            $buttons['openUnLockDialog'] = [
                'type' => 'button',
                'id' => 'openImportDialog',
                'class' => 'openUnLockDialog',
                'name' => 'openUnLockDialog',
                'img' => '/images/status_icons/st_unlock.png',
                'label' => Dict::getValue('unlock_employee')/*."(Ctrl+Shift+a)"*/,
                // G2BDialogMultiGrid(grid_id, generate_from, selected_ids, dialogMode, dialogContentUrl, actionSaveUrl, gridDataUrl, params, windowtitle, errortext)
                'onclick' => "G2BDialogMultiGrid('dhtmlxGrid','". EmployeeControllerEnum::EMPLOYEE_UNLOCK_DIALOG . "',null,1,'./dialog','./save','./gridData',null,'".$this->getControllerPageTitle(). ' - ' .Dict::getValue('unlock')."','".Dict::getValue('please_select_line')."');",
            ];

            $buttons[] = [
                'type' => 'selector',
            ];
        }

		if (App::hasRight($this->getControllerID(), 'employeeChangePosition')) {
			$buttons['openChangePositionDialog'] = [
								'type' => 'button',
								'id' => 'openChangePositionDialog',
								'class' => 'openChangePositionDialog',
								'name' => 'openChangePositionDialog',
								'img' => '/images/status_icons/st_workflow_mod.png',
								'label' => Dict::getValue('employee_change_position'),
								// G2BDialogMultiGrid(grid_id, generate_from, selected_ids, dialogMode, dialogContentUrl, actionSaveUrl, gridDataUrl, params, windowtitle, errortext)
								'onclick' => "G2BDialogMultiGrid('dhtmlxGrid','employeeChangePositionDialog',null,1,'./dialog','./goToMissingCompetencyByPosition','./gridData',null,'".$this->getControllerPageTitle(). ' - ' .Dict::getValue('employee_change_position')."','".Dict::getValue('please_select_line')."');",
            ];

			$buttons[] = [
								'type' => 'selector',
            ];
		}

		if (App::hasRight($this->getControllerID(), 'syncToLoga')) {
			$buttons['syncToLoga'] = [
								'type' => 'button',
								'id' => 'syncToLoga',
								'class' => 'syncToLoga',
								'name' => 'syncToLoga',
								'img' => '/images/status_icons/st_sync_to.png',
								'label' => Dict::getValue('sync_to_loga'),
								// G2BDialogMultiGrid(grid_id, generate_from, selected_ids, dialogMode, dialogContentUrl, actionSaveUrl, gridDataUrl, params, windowtitle, errortext)
								'onclick' => "logaSync('".Dict::getValue('sync_to_loga')."');",
            ];

			$buttons[] = [
								'type' => 'selector',
            ];
		}

		if (App::hasRight($this->getControllerID(), 'syncToBaber'))
		{
			$buttons['syncToBaber'] =
			[
				'type' => 'button',
				'id' => 'syncToBaber',
				'class' => 'syncToBaber',
				'name' => 'syncToBaber',
				'img' => '/images/status_icons/st_sync_to.png',
				'label' => Dict::getValue('sync_to_baber'),
				// G2BDialogMultiGrid(grid_id, generate_from, selected_ids, dialogMode, dialogContentUrl, actionSaveUrl, gridDataUrl, params, windowtitle, errortext)
				'onclick' => "baberSync('" . Dict::getValue('sync_to_baber') . "');",
			];

			$buttons[] = [
				'type' => 'selector',
			];
		}
		
		if (App::hasRight($this->getControllerID(), 'syncToLaurel')) {
			$buttons['syncToLaurel'] = [
								'type' => 'button',
								'id' => 'syncToLaurel',
								'class' => 'syncToLaurel',
								'name' => 'syncToLaurel',
								'img' => '/images/status_icons/st_income.png',
								'label' => Dict::getValue('sync_to_laurel'),
								// G2BDialogMultiGrid(grid_id, generate_from, selected_ids, dialogMode, dialogContentUrl, actionSaveUrl, gridDataUrl, params, windowtitle, errortext)
								'onclick' => 'laurelSyncQuestion();',
            ];

			$buttons[] = [
								'type' => 'selector',
            ];
		}

		$originalButtons = parent::getStatusButtons($gridID);

		$searchInGrid = $originalButtons['searchInGrid'];
		unset($originalButtons['searchInGrid']);
		unset($originalButtons['searchInGridSelector']);
		$buttons['searchInGrid'] = $searchInGrid;

		if (App::hasRight($this->getControllerID(), 'uploadDialog')) {
			$buttons['openUploadDialog'] = [
								'type' => 'button',
								'id' => 'openUploadDialog',
								'class' => 'openUploadDialog',
								'name' => 'openUploadDialog',
								'img' => '/images/status_icons/st_plus2.png',
								'label' => Dict::getValue('add')/*."(Ctrl+Shift+a)"*/,
								// G2BDialogMultiGrid(grid_id, generate_from, selected_ids, dialogMode, dialogContentUrl, actionSaveUrl, gridDataUrl, params, windowtitle, errortext)
								'onclick' => "G2BDialogMultiGrid('dhtmlxGrid','uploadDialog',null,0,'./dialog','./uploadDialogSave','./gridData',null,'".$this->getControllerPageTitle(). ' - ' .Dict::getValue('')."','".Dict::getValue('please_select_line')."', '50%', 'dialogContainer');",
            ];
		}

		if ((int)App::getRight($this->getControllerId(), 'uploadDialogEdit')) {
			$buttons['openUploadDialogEdit'] = [
								'type' => 'button',
								'id' => 'openUploadDialogEdit',
								'class' => 'openUploadDialogEdit',
								'name' => 'openUploadDialogEdit',
								'img' => '/images/status_icons/st_cog.png',
								'label' => Dict::getValue('add')/*."(Ctrl+Shift+a)"*/,
								// G2BDialogMultiGrid(grid_id, generate_from, selected_ids, dialogMode, dialogContentUrl, actionSaveUrl, gridDataUrl, params, windowtitle, errortext)
								'onclick' => "G2BDialogMultiGrid('dhtmlxGrid','uploadDialog',null,0,'./dialog?edit=1','./uploadDialogSave','./gridData',null,'".$this->getControllerPageTitle(). ' - ' .Dict::getValue('')."','".Dict::getValue('please_select_line')."', '50%', 'dialogContainer');",
            ];
		}

		if ((int)App::getRight($this->getControllerId(), 'absenceCalc')) {
			$buttons['absenceCalc'] = [
								'type' => 'button',
								'id' => 'absenceCalc',
								'class' => 'absenceCalc',
								'name' => 'absenceCalc',
								'img' => '/images/status_icons/st_calendar.png',
								'label' => Dict::getValue('absence_calculation'),
								'onclick' => "absenceCalc('" . Dict::getValue('absenceCalc_confirm') . "');",
            ];
		}

		return Yang::arrayMerge($buttons, $originalButtons);
	}
}