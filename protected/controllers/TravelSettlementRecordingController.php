<?php #yii2: done

'yii2-only`;

	namespace app\controllers;
	use app\components\API\GetPreDefinedFilter;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2\Grid2Controller;
	use app\models\ApproverRelatedGroup;
	use app\models\Employee;
	use app\models\EmployeeGroup;
	use app\models\Status;
	use Yang;

`/yii2-only';


#yii2: done

/*
 * 
 */

class TravelSettlementRecordingController extends Grid2Controller
{
	private $publishedStatus;
	private $defaultEnd;
	private $lang;

	public function __construct()
	{
		parent::__construct("travelSettlementRecording");

		$this->publishedStatus = Status::PUBLISHED;
		$this->defaultEnd = App::getSetting('defaultEnd');
		$this->lang = Dict::getLang();
		$this->maxDays = 366;
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("TravelSettlement");

		parent::setControllerPageTitleId("page_title_travel_settlement_recording");

		$this->LAGridRights->overrideInitRights("reload",			true);
		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);

		$this->LAGridDB->enableSQLMode();
		$art = new ApproverRelatedGroup;
		$gargSQL = $art->getApproverReleatedGroupSQLWithoutCalc("Employee", "employeeManagement");

		$gpf = new GetPreDefinedFilter(
			$this->getControllerID(), \FALSE,
			['company' => "employee", 'payroll' => "employee", "unit" => "employee", "workgroup" => "employee_contract"]
		);
		$SQLfilter = $gpf->getFilter();
		$filter = requestParam('searchInput');
		$SQL="
			SELECT
				ts.`row_id`,
				ts.`employee_contract_id`,
				ts.`type`,
				ts.`price`,
				ts.`number`,
				ts.`is_paid`,
				ts.`valid_from`,
				ts.`valid_to`,
				ts.`created_on`
			FROM `travel_settlement` ts
			LEFT JOIN `employee_contract` ON
					employee_contract.`employee_contract_id` = ts.`employee_contract_id`
				AND	employee_contract.`status` = " . $this->publishedStatus . "
				AND employee_contract.`valid_from` <= IFNULL(ts.`valid_to`, '" . $this->defaultEnd . "')
				AND employee_contract.`valid_to` >= ts.`valid_from`
				AND employee_contract.`ec_valid_from` <= IFNULL(ts.`valid_to`, '" . $this->defaultEnd . "')
				AND employee_contract.`ec_valid_to` >= ts.`valid_from`
			LEFT JOIN `employee` ON
					employee.`employee_id` = employee_contract.`employee_id`
				AND employee.`valid_from` <= IFNULL(employee_contract.`valid_to`, '" . $this->defaultEnd . "')
				AND employee.`valid_to` >= employee_contract.`valid_from`
				AND employee.`valid_from` <= IFNULL(employee_contract.`ec_valid_to`, '" . $this->defaultEnd . "')
				AND employee.`valid_to` >= employee_contract.`ec_valid_from`
			" . EmployeeGroup::getAllActiveLeftJoinSQL("employee_contract", "", "", "employee") . "
			LEFT JOIN `company_org_group3` ON
					`company_org_group3`.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL("company_org_group3_id","employee") . "
				AND `company_org_group3`.`status` = " . $this->publishedStatus . "
				AND employee.valid_from <= IFNULL(`company_org_group3`.`valid_to`, '" . $this->defaultEnd . "')
				AND `company_org_group3`.`valid_from` <= employee.valid_to
			" . $gargSQL["join"] . "
			WHERE
					ts.`status`=" . $this->publishedStatus . "
					AND ts.valid_to > '". $filter['valid_from'] . "'
				AND {$SQLfilter}
				" . $gargSQL["where"] . "
			GROUP BY ts.row_id, employee_contract.employee_contract_id
		";
		$this->LAGridDB->setSQLSelection($SQL,"row_id");
		parent::G2BInit();
	}

	protected function search()
	{
		$searchFields = $this->getPreDefinedSearchFromDb();

		$searchFields['valid_from']['default_value'] = date('Y-01-01');
		$searchFields['valid_to']['default_value'] = date('Y-12-31');

		return $searchFields;
	}

	public function columns()
	{
		$art = new ApproverRelatedGroup;
		$gargSQL = $art->getApproverReleatedGroupSQLWithoutCalc("Employee", "employeeManagement");
		return
		[
			'employee_contract_id'	=> 	[
											'grid'		=>	true,
											'window'	=>	true,
											'col_type'	=>	'combo',
											'align'		=>	'center',
											'options'	=>	[
																'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																'sql'	=> "
																SELECT	
																employee_contract.`employee_contract_id` as id,
																" . Employee::getParam('fullname_with_emp_id', 'employee') . " value
																FROM `employee_contract`
																LEFT JOIN `employee`ON
																		employee.`employee_id` = employee_contract.`employee_id`
																	AND employee.`valid_from` <= IFNULL(employee_contract.`valid_to`, '" . $this->defaultEnd . "')
																	AND employee.`valid_to` >= employee_contract.`valid_from`
																	AND employee.`valid_from` <= IFNULL(employee_contract.`ec_valid_to`, '" . $this->defaultEnd . "')
																	AND employee.`valid_to` >= employee_contract.`ec_valid_from`
																LEFT JOIN `employee_travel_cost` etc ON
																		etc.`employee_contract_id` = employee_contract.`employee_contract_id`
																	AND etc.`status` = " . $this->publishedStatus . "
																	AND CURDATE() BETWEEN etc.`valid_from` AND IFNULL(etc.`valid_to`, '" . $this->defaultEnd . "')
																" . EmployeeGroup::getAllActiveLeftJoinSQL("employee_contract", "", "", "employee") . "
																LEFT JOIN `company_org_group3` ON
																		`company_org_group3`.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL("company_org_group3_id","employee") . "
																	AND `company_org_group3`.`status` = " . $this->publishedStatus . "
																	AND employee.valid_from <= IFNULL(`company_org_group3`.`valid_to`, '" . $this->defaultEnd . "')
																	AND `company_org_group3`.`valid_from` <= employee.valid_to
																" . $gargSQL["join"] . "
																WHERE
																		employee_contract.`status` = " . $this->publishedStatus . "
																	AND CURDATE() BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`, '" . $this->defaultEnd . "')
																	AND CURDATE() BETWEEN employee_contract.`ec_valid_from` AND IFNULL(employee_contract.`ec_valid_to`, '" . $this->defaultEnd . "')
																	AND employee.`row_id` IS NOT NULL
																	AND etc.`row_id` IS NOT NULL
																	" . $gargSQL["where"] . "
																ORDER BY
																value ASC",		
																'array'	=> [["id"=>"","value"=>Dict::getValue("choose_option")]],
																]
										],
			'type' 					=> 	[
											'grid'		=>	true,
											'window'	=>	true,
											'col_type'	=>	'combo',
											'align'		=>	'center',
											'options'	=>	[
																'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																'sql'	=> "
																			SELECT	
																				al.`lookup_value` as id,
																				d.`dict_value` as value
																			FROM `app_lookup` al
																			LEFT JOIN `dictionary` d ON
																					d.`dict_id` = al.`dict_id`
																				AND d.`lang` = '" . $this->lang . "'
																				AND d.`valid` = 1
																			WHERE
																					al.`lookup_id` = 'travel_settlement_type'
																				AND al.`valid` = 1",	
																'array'	=> [["id"=>"","value"=>Dict::getValue("choose_option")]],
															]
										],
			'price'					=>	['grid'=> true, 'col_type'=>'ed', 'align' => 'center', 'window' => true],
			'number'				=>	['grid'=> true, 'col_type'=>'ed', 'align' => 'center', 'window' => true],
			'is_paid'				=>	[
											'grid'		=>	true,
											'window'	=>	false,
											'col_type'	=>	'combo',
											'align'		=>	'center',
											'default_value' => '0',
											'options' 	=> 	[
																'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																'sql'	=> "
																			SELECT	
																				al.`lookup_value` as id,
																				d.`dict_value` as value
																			FROM `app_lookup` al
																			LEFT JOIN `dictionary` d ON
																					d.`dict_id` = al.`dict_id`
																				AND d.`lang` = '" . $this->lang . "'
																				AND d.`valid` = 1
																			WHERE
																					al.`lookup_id` = 'yes_no'
																				AND al.`valid` = 1",	
																'array'	=> [["id"=>"","value"=>Dict::getValue("choose_option")]],
															]
										],
			'valid_from'			=>	['grid'=> true, 'col_type'=>'ed', 'align' => 'center', 'window' => true, 'dPicker' => true],
			'valid_to'				=>	['grid'=> true, 'col_type'=>'ed', 'align' => 'center', 'window' => true, 'dPicker' => true],
			'created_on'			=>	['grid'=> true, 'col_type'=>'ed', 'align' => 'center', 'window' => false, 'dPicker' => true]
		];
	}

	public function attributeLabels()
	{
		return array(
			'employee_contract_id'	=> Dict::getValue("fullname"),
			'type'					=> Dict::getValue("type"),
			'price'					=> Dict::getValue("price"),
			'number'				=> Dict::getValue("ticket_number"),
			'is_paid'				=> Dict::getValue("is_paid"),
			'valid_from'			=> Dict::getValue("valid_from"),
			'valid_to'				=> Dict::getValue("valid_to"),
			'created_on'			=> Dict::getValue("created_on")
		);
	}
}
