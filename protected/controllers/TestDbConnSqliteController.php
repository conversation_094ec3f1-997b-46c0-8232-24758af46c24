<?php

'yii2-only`;

	namespace app\controllers;
	use Yang;

`/yii2-only';


#yii2: done

class TestDbConnSqliteController extends Controller
{
	public function actionIndex()
	{
		$dbConn = Yii::app()->sqlite;

		$sql = "SELECT name FROM sqlite_master WHERE type='table';";
		$sql = "
			SELECT
				HardwareEvents.*
			FROM
				HardwareEvents
			LEFT JOIN EventTypes ON
				HardwareEvents.`EventId` = EventTypes.`Code`
			WHERE
				HardwareEvents.`EventId` IN (1,2,3,44)
		"; // SELECT name FROM 

		$command = $dbConn->createCommand($sql);
		$rows = $command->queryAll();

		if(is_array($rows) && count($rows) > 0) {
			echo "<table border=1>\n";
			echo "<tr>";

			foreach ($rows as $row) {
				foreach ($row as $key => $value) {
					echo "<td>$key</td>";
				}
				break;
			}
			echo "</tr>\n";

			foreach ($rows as $row2) {
				echo "<tr>";
				foreach ($row2 as $key2 => $value2) {
					echo "<td>$value2</td>";
				}
				echo "</tr>\n";
			}

			echo "</table>\n";
		}
	}
}