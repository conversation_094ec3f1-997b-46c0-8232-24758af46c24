<?php

class EmailSendingInPoolController extends Controller
{
	const MAXSENDEMAILNUMBER = 300;

	public function actionIndex()
	{
		$mailPool = $this->getSendMailPool();
		if (is_null($mailPool)) { return; }
		$this->sendMailInPool($mailPool);
		$this->clearDbTable();
	}

	private function getSendMailPool()
	{
		$publishedStatus = Status::PUBLISHED;
		$emailSendingPool = new EmailSendingPool('select');
		$criteria = new CDbCriteria();
		$criteria->select = "`row_id`, `mail_content`";
		$criteria->condition = "`status`= $publishedStatus";
		$criteria->limit = EmailSendingInPoolController::MAXSENDEMAILNUMBER;

		$result = $emailSendingPool->findAll($criteria);

		$resultMail = null;
		foreach($result AS $key => $value)
		{
			$resultMail[$key]['rowId'] = $value->row_id;
			$resultMail[$key]['mailContent'] = json_decode($value->mail_content, true);
		}

		return $resultMail;
	}

	private function sendMailInPool($mailPool)
	{
		$emailSedingPoolUpdateRowIds = [];
		foreach($mailPool AS $key => $value)
		{
			$es = new EmailSender($value['mailContent']['vars']['attachmentPath'], $value['mailContent']['vars']['attachmentFileName']);
			$es->sendMail(
				/* $addresses */
				$value['mailContent']['addresses'],
				/* $subject */
				$value['mailContent']['subject'],
				/* $message */
				$value['mailContent']['message'],
				/* $view */
				$value['mailContent']['view'],
				/* $vars */
				$value['mailContent']['vars'],
				/* $images */
				$value['mailContent']['images'],
				/* $iCal */
				$value['mailContent']['iCal'],
				/* $iCalString */
				$value['mailContent']['iCalString'],
				/* $notSkipAppSettings */
				$value['mailContent']['notSkipAppSettings'],
				/* sendMailToPool */
				false
			);

			array_push($emailSedingPoolUpdateRowIds, $value['rowId']);
		}

		$updateRowIds = implode(", ", $emailSedingPoolUpdateRowIds);

		$publishedStatus = Status::PUBLISHED;
		$deleteStatus = Status::DELETED;
		$emailSedingPool = new EmailSendingPool('update');
		EmailSendingPool::model()->updateAll(['status'=> $deleteStatus], "`status`= $publishedStatus AND row_id IN (".$updateRowIds.")");
	}

	/**
	 * Remove records older than a week
	 * @return void
	 */
	private function clearDbTable() :void {
		$delSQL = "DELETE FROM `email_sending_pool` WHERE `created_on` < DATE_SUB(CURDATE(), INTERVAL 7 DAY)";
		dbExecute($delSQL);
	}
}

