<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use app\models\EmployeeGroup;
	use app\models\Status;
	use Yang;

`/yii2-only';


#yii2: done

trait EmployeeControllerLoga
{
	public function actionLogaSync() {
		$emp_id = requestParam('emp_id');
		$emp_id = empty($emp_id) ? 'ALL' : $emp_id;

		$status = $this->logaSync($emp_id);

		echo json_encode($status);
		exit();
	}

	public function logaSync($emp_id = 'ALL') {
		$srcConn = Yang::app()->db; // #see https://innote.login.hu/n-7wpd5adl

		////////////////////////////
		$srcConn->createCommand('LOCK TABLE `process_lock` WRITE;')->execute();

		$process_name = 'BOSLogaSync';

		$SQL = "
			SELECT
				`process_locked`
			FROM
				`process_lock`
			WHERE
				`process_name` = '$process_name'
		";

		$res = $srcConn->createCommand($SQL)->queryAll();

		$alreadyLocked = false;
		if (isset($res[0]['process_locked']) && (int)$res[0]['process_locked']) {
			$alreadyLocked = true;
		} else {
			$srcConn->createCommand('INSERT INTO `process_lock` (`process_name`, `process_locked`, `process_time`) VALUES ("'.$process_name.'", 1, "'.date('Y-m-d H:i:s').'") ON DUPLICATE KEY UPDATE `process_locked` = VALUES(`process_locked`), `process_time` = VALUES(`process_time`);')->execute();
		}

		$srcConn->createCommand('UNLOCK TABLES;')->execute();

		if ($alreadyLocked) return ['status' => 'locked','msg' => Dict::getValue('sync_already_running'),];
		////////////////////////////

		$trgtName = "LOGA";
		$trgtConn = null;
		if (!empty($trgtName)) {
			try {
				$trgtConn = Yang::app()->$trgtName;
			} catch (\Exception $e) { }
		}

		$tablePref = '[LOGABOSLOGIN].[L2001].';

		// Dolg. alapadatok
		$PGRDAT = $this->getPGRDAT($emp_id, $srcConn);
		$insertedData = $failedData = [];
		$this->doInserts($PGRDAT, 'INSERT '.$tablePref.'[PGRDAT]', $trgtConn, $insertedData, $failedData);

		// PNR kapcsoló idegenkulcsokhoz
		foreach ($insertedData as $data) {
			$insSQL = "
				INSERT INTO `temp_data_pgrdat` (`emp_id`) VALUES ('".$data['PNR']."')  ON DUPLICATE KEY UPDATE `emp_id` = VALUES(`emp_id`);
			";

			$srcConn->createCommand($insSQL)->execute();
		}

		// Belép idő
		$MITARBDAT = $this->getMITARBDAT($emp_id, $srcConn);
		$insertedData = $failedData = [];
		$this->doInserts($MITARBDAT, 'INSERT '.$tablePref.'[MITARBDAT]', $trgtConn, $insertedData, $failedData);

		// szerz.
		$VERTRAG = $this->getVERTRAG($emp_id, $srcConn);
		$insertedData = $failedData = [];
		$this->doInserts($VERTRAG, 'INSERT '.$tablePref.'[VERTRAG]', $trgtConn, $insertedData, $failedData);


		// munk.szer. kieg
		$tableNameVertragerw = '[VERTRAGERW]';
		$VERTRAGERW = $this->getVERTRAGERW($emp_id, $srcConn);
		$insertedData = $failedData = [];
		$this->doInserts($VERTRAGERW, 'INSERT '.$tablePref. $tableNameVertragerw, $trgtConn, $insertedData, $failedData);

		/*
		// gyer.
		// Várjuk LOGA táblanév
		$tableNameGyerekDat = '[GYEREKDAT]';
		$GYEREKDAT = array();
		for ($i=1; $i <= 5; $i++) {
			array_push($GYEREKDAT, $this->getGYEREKDAT($emp_id, $srcConn, $i));
		}
		$insertedData = $failedData = [];
		$this->doInserts($GYEREKDAT, 'INSERT '.$tablePref. $tableNameGyerekDat, $trgtConn, $insertedData, $failedData);
		*/

		// banksz.szám
		// Várjuk LOGA táblanév
		$tableNameBankinfo = '[BANKINFO]';
		$BANKINFO = $this->getBANKINFO($emp_id, $srcConn);
		$insertedData = $failedData = [];
		$this->doInserts($BANKINFO, 'INSERT '.$tablePref. $tableNameBankinfo, $trgtConn, $insertedData, $failedData);

		$srcConn->createCommand('INSERT INTO `process_lock` (`process_name`, `process_locked`, `process_time`) VALUES ("'.$process_name.'", 0, "'.date('Y-m-d H:i:s').'") ON DUPLICATE KEY UPDATE `process_locked` = VALUES(`process_locked`), `process_time` = VALUES(`process_time`);')->execute();

		return ['status' => 'success', 'msg' => Dict::getValue('syncronization_done_successfully'),];
	}

	private function doInserts($data = [], $insPref = '', $trgtConn = null, &$insertedData = [], &$failedData = []) {
		foreach ($data as $res) {
			$ins = $insPref.' ';
			$insCols = '';
			$insVals = '';

			$cnt = 0;
			foreach ($res as $col => $val) {
				if ($cnt === 0) {
					$insCols .= '(';
					$insVals .= '(';
				}

				if ($cnt > 0) {
					$insCols .= ', ';
					$insVals .= ', ';
				}

				$insCols .= '['.$col.']';

				if ($col == "LOGA_PROCESSED_DATETIME" || $col == "HAUSBKNR") {
					$insVals .= 'NULL';
				} else {
					$insVals .= '\''.$val.'\'';
				}

				if ($cnt === count($res)-1) {
					$insCols .= ')';
					$insVals .= ')';
				}

				$cnt++;
			}

			$ins .= $insCols . ' VALUES ' . $insVals . ';';

			//echo $ins.'<br/><br/>';
			//$insertedData[] = $res;

			if (!empty($trgtConn)) {
				try {
					$trgtConn->createCommand($ins)->execute();

					$insertedData[] = $res;
				} catch (\Exception $e) {
					$failedData[] = $res;
				}
			}
		}
	}

	private function getPGRDAT($emp_id, $conn) {
		$SQL = "
			SELECT
				lsc.LIT_KZL AS MAN,
				lsc2.LIT_KZL AS AK,
				lsc3.LIT_KZL AS BETRST,
				emp.emp_id AS PNR,
				GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01'), IFNULL(u.valid_from, '1915-01-01'), IFNULL(user.valid_from, '1915-01-01'), IFNULL(etab.valid_from, '1915-01-01'), IFNULL(etab3.valid_from, '1915-01-01')) AS PST_AB,
				LEAST(IFNULL(emp.valid_to, '2038-01-01'), IFNULL(ec.valid_to, '2038-01-01'), IFNULL(u.valid_to, '2038-01-01'), IFNULL(user.valid_to, '2038-01-01'), IFNULL(etab.valid_to, '2038-01-01'), IFNULL(etab3.valid_to, '2038-01-01')) AS PST_BIS,
				emp.last_name AS NANAME,
				emp.first_name AS VORNAME,
				GROUP_CONCAT(DISTINCT tab5.`value`) AS AV_VNR,
				emp.tax_number AS PTAX_NR,
				CASE
					WHEN (emp.nameofbirth IS NULL OR emp.nameofbirth = '') AND emp.gender = 'male' THEN CONCAT_WS(' ', emp.last_name, emp.first_name)
					ELSE emp.nameofbirth
				END AS GEBNAME,
				GROUP_CONCAT(DISTINCT tab2.`value`) AS GEBMUTTER,
				GROUP_CONCAT(DISTINCT tab3.`value`) AS GEBORT,
				GROUP_CONCAT(DISTINCT tab4.`value`) AS GEBDAT,
				CASE emp.gender
				WHEN 'female' THEN 'W'
				WHEN 'male' THEN 'M'
				ELSE NULL
				END AS GESCHL,
				GROUP_CONCAT(DISTINCT tab7.`value`) AS PLZ,
				GROUP_CONCAT(DISTINCT tab8.`value`) AS ORT,
				GROUP_CONCAT(DISTINCT tab9.`value`) AS STRASSE,
				lsc4.LIT_KZL AS STAAT,
				CASE
					WHEN user.lang IS NULL OR user.lang = '' THEN 'U'
					ELSE lsc5.LIT_KZL
				END AS SPR,
				lsc6.LIT_KZL AS LND,

				'' AS LOGA_PROCESSED_DATETIME,
				NOW() AS LOGIN_CREATED_DATETIME
			FROM
				employee AS emp
			LEFT JOIN
				loga_sync_connect AS lsc ON lsc.LOGIN_KEY = 'company_id' AND lsc.LOGIN_ID = emp.company_id
			LEFT JOIN
				loga_sync_connect AS lsc2 ON lsc2.LOGIN_KEY = 'payroll_id' AND lsc2.LOGIN_ID = emp.payroll_id
			JOIN
				employee_contract AS ec ON ec.employee_id = emp.employee_id AND ec.status = 2
				AND ec.`valid_from` <= IFNULL(emp.`valid_to`,'2038-01-01') AND emp.`valid_from` <= IFNULL(ec.`valid_to`,'2038-01-01')
			LEFT JOIN
				unit AS u ON u.unit_id = emp.unit_id AND u.status = 2
				AND u.`valid_from` <= LEAST(IFNULL(emp.valid_to, '2038-01-01'), IFNULL(ec.valid_to, '2038-01-01')) AND GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01')) <= IFNULL(u.`valid_to`,'2038-01-01')
			LEFT JOIN
				loga_sync_connect AS lsc3 ON lsc3.LOGIN_KEY = 'unit_id' AND lsc3.LOGIN_TXT = u.unit_name
			LEFT JOIN
				user ON user.employee_id = emp.employee_id
				AND user.status = 2
				AND user.`valid_from` <= LEAST(IFNULL(emp.valid_to, '2038-01-01'), IFNULL(ec.valid_to, '2038-01-01'), IFNULL(u.valid_to, '2038-01-01')) AND GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01'), IFNULL(u.valid_from, '1915-01-01')) <= IFNULL(user.`valid_to`,'2038-01-01')
			LEFT JOIN loga_sync_connect AS lsc5 ON lsc5.LOGIN_KEY = 'lang' AND lsc5.LIT_ART = 'SPRACHE' AND lsc5.LOGIN_ID = user.lang
			LEFT JOIN `employee_tab` AS `etab` ON
				etab.connect_id = emp.employee_id
                AND etab.tab_id = 1
				AND etab.status = 2
				AND etab.`valid_from` <= LEAST(IFNULL(emp.valid_to, '2038-01-01'), IFNULL(ec.valid_to, '2038-01-01'), IFNULL(u.valid_to, '2038-01-01'), IFNULL(user.valid_to, '2038-01-01')) AND GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01'), IFNULL(u.valid_from, '1915-01-01'), IFNULL(user.valid_from, '1915-01-01')) <= IFNULL(etab.`valid_to`,'2038-01-01')
			LEFT JOIN `employee_tab` AS `etab3` ON
				etab3.connect_id = emp.employee_id
                AND etab3.tab_id = 3
				AND etab3.status = 2
				AND etab3.`valid_from` <= LEAST(IFNULL(emp.valid_to, '2038-01-01'), IFNULL(ec.valid_to, '2038-01-01'), IFNULL(u.valid_to, '2038-01-01'), IFNULL(user.valid_to, '2038-01-01')) AND GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01'), IFNULL(u.valid_from, '1915-01-01'), IFNULL(user.valid_from, '1915-01-01')) <= IFNULL(etab3.`valid_to`,'2038-01-01')

 			LEFT JOIN `employee_tab_item` tab ON tab.`column_id` LIKE 'nationality'
				AND etab.employee_tab_id = tab.employee_tab_id
				AND tab.`status` = 2
			LEFT JOIN loga_sync_connect AS lsc4 ON lsc4.LOGIN_TXT = tab.value AND lsc4.LOGIN_KEY = 'nationality'
 			LEFT JOIN `employee_tab_item` tab2 ON tab2.`column_id` LIKE 'mothers_name'
				AND etab.employee_tab_id = tab2.employee_tab_id
				AND tab2.`status` = 2
			LEFT JOIN `employee_tab_item` tab3 ON tab3.`column_id` LIKE 'place_of_birth'
				AND etab.employee_tab_id = tab3.employee_tab_id
				AND tab3.`status` = 2
 			LEFT JOIN `employee_tab_item` tab4 ON tab4.`column_id` LIKE 'date_of_birth'
				AND etab.employee_tab_id = tab4.employee_tab_id
				AND tab4.`status` = 2
 			LEFT JOIN `employee_tab_item` tab5 ON tab5.`column_id` LIKE 'social_security_number'
				AND etab.employee_tab_id = tab5.employee_tab_id
				AND tab5.`status` = 2
 			LEFT JOIN `employee_tab_item` tab6 ON tab6.`column_id` LIKE 'country'
				AND etab3.employee_tab_id = tab6.employee_tab_id
				AND tab6.`status` = 2
			LEFT JOIN loga_sync_connect AS lsc6 ON lsc6.LIT_TXT = tab6.value AND lsc6.LIT_ART = 'LAND'
 			LEFT JOIN `employee_tab_item` tab7 ON tab7.`column_id` LIKE 'address_zip'
				AND etab3.employee_tab_id = tab7.employee_tab_id
				AND tab7.`status` = 2
			LEFT JOIN `employee_tab_item` tab8 ON tab8.`column_id` LIKE 'address_city'
				AND etab3.employee_tab_id = tab8.employee_tab_id
				AND tab8.`status` = 2
			LEFT JOIN `employee_tab_item` tab9 ON tab9.`column_id` LIKE 'address_adr'
				AND etab3.employee_tab_id = tab9.employee_tab_id
				AND tab9.`status` = 2
 			WHERE
				emp.status = 2
				AND ('$emp_id' = 'ALL' OR emp.emp_id = '$emp_id')
 			GROUP BY emp.employee_id,
 				GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01'), IFNULL(u.valid_from, '1915-01-01'), IFNULL(user.valid_from, '1915-01-01'), IFNULL(etab.valid_from, '1915-01-01'), IFNULL(etab3.valid_from, '1915-01-01'))
			ORDER BY emp.employee_id
		";

		$res = $conn->createCommand($SQL)->queryAll();

		return $res;
	}

	private function getMITARBDAT($emp_id, $conn) {
		$SQL = "
			SELECT
				lsc.LIT_KZL AS MAN,
				lsc2.LIT_KZL AS AK,
				emp.emp_id AS PNR,
				GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01'), IFNULL(etab3.valid_from, '1915-01-01')) AS MA_AB,
				LEAST(IFNULL(emp.valid_to, '2038-01-01'), IFNULL(ec.valid_to, '2038-01-01'), IFNULL(etab3.valid_to, '2038-01-01')) AS MA_BIS,
				ec.ec_valid_from AS KONEINTRT,
				GROUP_CONCAT(DISTINCT tab2.`value`) AS PLZ2,
				GROUP_CONCAT(DISTINCT tab3.`value`) AS ORT2,
				GROUP_CONCAT(DISTINCT tab4.`value`) AS STRASSE2,
				lsc3.LIT_KZL AS LAND2,
				GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01'), IFNULL(etab3.valid_from, '1915-01-01')) AS PST_AB,
				'' AS LOGA_PROCESSED_DATETIME,
				NOW() AS LOGIN_CREATED_DATETIME
			FROM
				employee AS emp
			LEFT JOIN
				loga_sync_connect AS lsc ON lsc.LOGIN_KEY = 'company_id' AND lsc.LOGIN_ID = emp.company_id
			LEFT JOIN
				loga_sync_connect AS lsc2 ON lsc2.LOGIN_KEY = 'payroll_id' AND lsc2.LOGIN_ID = emp.payroll_id
			JOIN
				employee_contract AS ec ON ec.employee_id = emp.employee_id
				AND ec.status = 2
				AND `ec`.`valid_from` <= IFNULL(`emp`.`valid_to`,'2038-01-01') AND `emp`.`valid_from` <= IFNULL(`ec`.`valid_to`,'2038-01-01')
			LEFT JOIN `employee_tab` AS `etab3` ON
				etab3.connect_id = emp.employee_id
                AND etab3.tab_id = 3
				AND etab3.status = 2
				AND etab3.`valid_from` <= LEAST(IFNULL(emp.valid_to, '2038-01-01'), IFNULL(ec.valid_to, '2038-01-01')) AND GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01')) <= IFNULL(etab3.`valid_to`,'2038-01-01')

			LEFT JOIN `employee_tab_item` tab ON tab.`column_id` LIKE 'res_country'
				AND etab3.employee_tab_id = tab.employee_tab_id
				AND tab.`status` = 2
			LEFT JOIN loga_sync_connect AS lsc3 ON lsc3.LIT_TXT = tab.value AND lsc3.LIT_ART = 'LAND'
 			LEFT JOIN `employee_tab_item` tab2 ON tab2.`column_id` LIKE 'res_address_zip'
				AND etab3.employee_tab_id = tab2.employee_tab_id
				AND tab2.`status` = 2
			LEFT JOIN `employee_tab_item` tab3 ON tab3.`column_id` LIKE 'res_address_city'
				AND etab3.employee_tab_id = tab3.employee_tab_id
				AND tab3.`status` = 2
			LEFT JOIN `employee_tab_item` tab4 ON tab4.`column_id` LIKE 'res_address_adr'
				AND etab3.employee_tab_id = tab4.employee_tab_id
				AND tab4.`status` = 2
			JOIN
				temp_data_pgrdat AS tdp ON tdp.emp_id = emp.emp_id
 			WHERE
				tdp.emp_id IS NOT NULL
				AND emp.status = 2
				AND ('$emp_id' = 'ALL' OR emp.emp_id = '$emp_id')
 			GROUP BY emp.employee_id,
 				GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01'), IFNULL(etab3.valid_from, '1915-01-01'))
			ORDER BY emp.employee_id
		";

		$res = $conn->createCommand($SQL)->queryAll();

		return $res;
	}

	private function getVERTRAG($emp_id, $conn) {
		$SQL = "
			SELECT
				lsc.LIT_KZL AS MAN,
				lsc2.LIT_KZL AS AK,
				emp.emp_id AS PNR,
				ec.employee_contract_number AS VERTNR,
				GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01'), IFNULL(etab.valid_from, '1915-01-01'), IFNULL(etab2.valid_from, '1915-01-01')) AS VER_AB,
				LEAST(IFNULL(emp.valid_to, '2038-01-01'), IFNULL(ec.valid_to, '2038-01-01'), IFNULL(etab.valid_to, '2038-01-01'), IFNULL(etab2.valid_to, '2038-01-01')) AS VER_BIS,
				ec.wage_type AS VERART,
				lsc4.LIT_KZL AS HAUPTV,
				ec.valid_from AS VERBEGIN,
				IFNULL(ec.valid_to, '2038-01-01') AS VERENDE,
				lsc6.LIT_KZL AS VENDEGRUND,
				lsc3.LIT_KZL AS BESCHSCHL,
				ec.daily_worktime AS STDTAG,
				lsc5.LIT_KZL AS SPTAGE,
				GROUP_CONCAT(DISTINCT tab4.`value`) AS OPTURL,
				GROUP_CONCAT(DISTINCT tab5.`value`) AS ARBENDE,
				'1' AS UT_GRUPPE,
				'SZ' AS TRF,
				'S' AS UANSTD,
				IF(`workgroup`.`work_order` < 2, NULL, 'E1') as ENLOGRU,
				GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01'), IFNULL(etab.valid_from, '1915-01-01'), IFNULL(etab2.valid_from, '1915-01-01')) AS MA_AB,
				'' AS LOGA_PROCESSED_DATETIME,
				NOW() AS LOGIN_CREATED_DATETIME
			FROM
				employee AS emp
			LEFT JOIN
				loga_sync_connect AS lsc ON lsc.LOGIN_KEY = 'company_id' AND lsc.LOGIN_ID = emp.company_id
			LEFT JOIN
				loga_sync_connect AS lsc2 ON lsc2.LOGIN_KEY = 'payroll_id' AND lsc2.LOGIN_ID = emp.payroll_id
			JOIN
				employee_contract AS ec ON ec.employee_id = emp.employee_id
				AND ec.status = 2
				AND `ec`.`valid_from` <= IFNULL(`emp`.`valid_to`,'2038-01-01') AND `emp`.`valid_from` <= IFNULL(`ec`.`valid_to`,'2038-01-01')
				AND `ec`.`ec_valid_from` <= IFNULL(`emp`.`valid_to`,'2038-01-01') AND `emp`.`valid_from` <= IFNULL(`ec`.`ec_valid_to`,'2038-01-01')
			LEFT JOIN `employee_tab` AS `etab2` ON
				etab2.connect_id = emp.employee_id
                AND etab2.tab_id = 2
				AND etab2.status = 2
				AND etab2.`valid_from` <= LEAST(IFNULL(emp.valid_to, '2038-01-01'), IFNULL(ec.valid_to, '2038-01-01')) AND GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01')) <= IFNULL(etab2.`valid_to`,'2038-01-01')
			LEFT JOIN `employee_tab` AS `etab` ON
				etab.connect_id = emp.employee_id
                AND etab.tab_id = 1
				AND etab.status = 2
				AND etab.`valid_from` <= LEAST(IFNULL(emp.valid_to, '2038-01-01'), IFNULL(ec.valid_to, '2038-01-01'), IFNULL(etab2.valid_to, '2038-01-01')) AND GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01'), IFNULL(etab2.valid_from, '1915-01-01')) <= IFNULL(etab.`valid_to`,'2038-01-01')

			LEFT JOIN `employee_tab_item` tab ON tab.`column_id` LIKE 'types_of_employment'
				AND etab2.employee_tab_id = tab.employee_tab_id
				AND tab.`status` = 2
			LEFT JOIN
				loga_sync_connect AS lsc3 ON lsc3.LOGIN_KEY = 'types_of_employment' AND lsc3.LOGIN_TXT = tab.value
			LEFT JOIN `employee_tab_item` tab2 ON tab2.`column_id` LIKE 'munkaviszonytipus'
				AND etab.employee_tab_id = tab2.employee_tab_id
				AND tab2.`status` = 2
			LEFT JOIN
				loga_sync_connect AS lsc4 ON lsc4.LIT_ART = 'VTYP' AND lsc4.LOGIN_TXT = tab2.value
			LEFT JOIN `employee_tab_item` tab3 ON tab3.`column_id` LIKE 'athidalonapok'
				AND etab.employee_tab_id = tab3.employee_tab_id
				AND tab3.`status` = 2
			LEFT JOIN `dictionary` AS dc ON dc.dict_value = tab3.value
				AND dc.valid = 1
			LEFT JOIN
				loga_sync_connect AS lsc5 ON lsc5.LIT_ART = 'FBTAGGEN' AND lsc5.LOGIN_ID = dc.dict_id
			LEFT JOIN `employee_tab_item` tab4 ON tab4.`column_id` LIKE 'szabadsagigenykiert'
				AND etab.employee_tab_id = tab4.employee_tab_id
				AND tab4.`status` = 2
			LEFT JOIN `employee_tab_item` tab5 ON tab5.`column_id` LIKE 'folybizkezd'
				AND etab.employee_tab_id = tab5.employee_tab_id
				AND tab5.`status` = 2
			LEFT JOIN `employee_tab_item` tab6 ON tab6.`column_id` LIKE 'types_of_termination_term'
				AND etab2.employee_tab_id = tab6.employee_tab_id
				AND tab6.`status` = 2
			LEFT JOIN
				loga_sync_connect AS lsc6 ON lsc6.LIT_ART = 'VEG' AND lsc6.LOGIN_TXT = tab6.value
			JOIN
				temp_data_pgrdat AS tdp ON tdp.emp_id = emp.emp_id
			";
			$SQL .= EmployeeGroup::getAllActiveLeftJoinSQL("ec");
			$SQL .= "
			LEFT JOIN `workgroup` ON
					`workgroup`.`workgroup_id` = ".EmployeeGroup::getActiveGroupSQL("workgroup_id","employee_contract")."
				AND `workgroup`.`status` = ".Status::PUBLISHED."
				AND `workgroup`.`valid_from` <= LEAST(IFNULL(emp.valid_to, '".App::getSetting("defaultEnd")."'),
														IFNULL(ec.valid_to, '".App::getSetting("defaultEnd")."'),
														IFNULL(ec.ec_valid_to, '".App::getSetting("defaultEnd")."'),
														IFNULL(etab.valid_to, '".App::getSetting("defaultEnd")."'),
														IFNULL(etab2.valid_to, '".App::getSetting("defaultEnd")."')
												)
				AND GREATEST(	IFNULL(emp.valid_from, '1915-01-01'),
								IFNULL(ec.valid_from, '1915-01-01'),
								IFNULL(ec.ec_valid_from, '1915-01-01'),
								IFNULL(etab.valid_from, '1915-01-01'),
								IFNULL(etab2.valid_from, '1915-01-01')
					) <= IFNULL(`workgroup`.`valid_to`,'".App::getSetting("defaultEnd")."')
			WHERE
				tdp.emp_id IS NOT NULL
				AND emp.status = 2
				AND ('$emp_id' = 'ALL' OR emp.emp_id = '$emp_id')
			GROUP BY emp.employee_id,
 				GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01'), IFNULL(etab.valid_from, '1915-01-01'), IFNULL(etab2.valid_from, '1915-01-01'))
			ORDER BY emp.employee_id
		";

		Yang::log("log sync". '__' . json_encode($SQL), 'log', 'system.PT');

		$res = $conn->createCommand($SQL)->queryAll();

		return $res;
	}

	private function getVERTRAGERW($emp_id, $conn) {
		$SQL = "
			SELECT
				lsc.LIT_KZL AS MAN,
				lsc2.LIT_KZL AS AK,
				emp.emp_id AS PNR,
				ec.employee_contract_number AS VERTNR,
				GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01'), IFNULL(etab2.valid_from, '1915-01-01'), IFNULL(etab4.valid_from, '1915-01-01')) AS VERERW_AB,
				LEAST(IFNULL(emp.valid_to, '2038-01-01'), IFNULL(ec.valid_to, '2038-01-01'), IFNULL(etab2.valid_to, '2038-01-01'), IFNULL(etab4.valid_to, '2038-01-01')) AS VERERW_BIS,
				lsc4.LIT_KZL AS ARBKREIS,
				GROUP_CONCAT(DISTINCT tab3.`value`) AS PROBEENDE,
				GROUP_CONCAT(DISTINCT tab2.`value`) AS FEOR,
				'' AS LOGA_PROCESSED_DATETIME,
				NOW() AS LOGIN_CREATED_DATETIME
			FROM
				employee AS emp
			LEFT JOIN
				loga_sync_connect AS lsc ON lsc.LOGIN_KEY = 'company_id' AND lsc.LOGIN_ID = emp.company_id
			LEFT JOIN
				loga_sync_connect AS lsc2 ON lsc2.LOGIN_KEY = 'payroll_id' AND lsc2.LOGIN_ID = emp.payroll_id
			JOIN
				employee_contract AS ec ON ec.employee_id = emp.employee_id
				AND ec.status = 2
				AND `ec`.`valid_from` <= IFNULL(`emp`.`valid_to`,'2038-01-01') AND `emp`.`valid_from` <= IFNULL(`ec`.`valid_to`,'2038-01-01')
			LEFT JOIN
				loga_sync_connect AS lsc4 ON lsc4.LOGIN_KEY = 'employee_position_id' AND lsc4.LOGIN_ID = ec.employee_position_id
			LEFT JOIN `employee_tab` AS `etab2` ON
				etab2.connect_id = emp.employee_id
                AND etab2.tab_id = 2
				AND etab2.status = 2
				AND etab2.`valid_from` <= LEAST(IFNULL(emp.valid_to, '2038-01-01'), IFNULL(ec.valid_to, '2038-01-01')) AND GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01')) <= IFNULL(etab2.`valid_to`,'2038-01-01')
			LEFT JOIN `employee_tab` AS `etab4` ON
				etab4.connect_id = emp.employee_id
                AND etab4.tab_id = 4
				AND etab4.status = 2
				AND etab4.`valid_from` <= LEAST(IFNULL(emp.valid_to, '2038-01-01'), IFNULL(ec.valid_to, '2038-01-01'), IFNULL(etab2.valid_to, '2038-01-01')) AND GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01'), IFNULL(etab2.valid_from, '1915-01-01')) <= IFNULL(etab4.`valid_to`,'2038-01-01')

			LEFT JOIN `employee_tab_item` tab ON tab.`column_id` LIKE 'types_of_employment'
				AND etab2.employee_tab_id = tab.employee_tab_id
				AND tab.`status` = 2
			LEFT JOIN
				loga_sync_connect AS lsc3 ON lsc3.LOGIN_KEY = 'types_of_employment' AND lsc3.LOGIN_TXT = tab.value
			LEFT JOIN `employee_tab_item` tab2 ON tab2.`column_id` LIKE 'feor_number'
				AND etab4.employee_tab_id = tab2.employee_tab_id
				AND tab2.`status` = 2
			LEFT JOIN `employee_tab_item` tab3 ON tab3.`column_id` LIKE 'end_of_probation'
				AND etab2.employee_tab_id = tab3.employee_tab_id
				AND tab3.`status` = 2
			JOIN
				temp_data_pgrdat AS tdp ON tdp.emp_id = emp.emp_id
			WHERE
				tdp.emp_id IS NOT NULL
				AND emp.status = 2
				AND ('$emp_id' = 'ALL' OR emp.emp_id = '$emp_id')
			GROUP BY emp.employee_id,
 				GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01'), IFNULL(etab2.valid_from, '1915-01-01'), IFNULL(etab4.valid_from, '1915-01-01'))
			ORDER BY emp.employee_id
		";

		$res = $conn->createCommand($SQL)->queryAll();

		return $res;
	}

	private function getBANKINFO($emp_id, $conn) {
		$SQL = "
			SELECT
				lsc.LIT_KZL AS MAN,
				lsc2.LIT_KZL AS AK,
				emp.emp_id AS PNR,
				ec.employee_contract_number AS VERTNR,
				tab.`value` AS BK_BLZ,
				tab2.`value` AS UEKONTO,
				'' AS HAUSBKNR,
				'' AS LOGA_PROCESSED_DATETIME,
				NOW() AS LOGIN_CREATED_DATETIME
			FROM
				employee AS emp
			LEFT JOIN
				loga_sync_connect AS lsc ON lsc.LOGIN_KEY = 'company_id' AND lsc.LOGIN_ID = emp.company_id
			LEFT JOIN
				loga_sync_connect AS lsc2 ON lsc2.LOGIN_KEY = 'payroll_id' AND lsc2.LOGIN_ID = emp.payroll_id
			JOIN
				employee_contract AS ec ON ec.employee_id = emp.employee_id
				AND ec.status = 2
				AND (NOW() BETWEEN ec.valid_from AND ec.valid_to)
			LEFT JOIN `employee_tab` AS `etab` ON
				etab.connect_id = emp.employee_id
                AND etab.tab_id = 1
				AND etab.status = 2
				AND (NOW() BETWEEN etab.valid_from AND etab.valid_to)
			LEFT JOIN `employee_tab_item` tab ON tab.`column_id` LIKE 'referral_account_account_code'
				AND etab.employee_tab_id = tab.employee_tab_id
				AND tab.`status` = 2
			LEFT JOIN `employee_tab_item` tab2 ON tab2.`column_id` LIKE 'referral_account_number'
				AND etab.employee_tab_id = tab2.employee_tab_id
				AND tab2.`status` = 2
			JOIN
				temp_data_pgrdat AS tdp ON tdp.emp_id = emp.emp_id
			WHERE
				tdp.emp_id IS NOT NULL
				AND emp.status = 2
				AND ('$emp_id' = 'ALL' OR emp.emp_id = '$emp_id')
				AND (NOW() BETWEEN emp.valid_from AND emp.valid_to)
			ORDER BY emp.employee_id
		";

		$res = $conn->createCommand($SQL)->queryAll();

		return $res;
	}

	// AS VALAMI az oszlopok változó nevei ami a LOGA táblába van
	private function getGYEREKDAT($emp_id, $conn, $child_number) {
		switch($child_number) {
			case '1':
				$bdayString = 'first';
				break;
			case '2':
				$bdayString = 'second';
				break;
			case '3':
				$bdayString = 'third';
				break;
			case '4':
				$bdayString = 'fourth';
				break;
			case '5':
				$bdayString = 'fifth';
				break;
		}
		$SQL = "
			SELECT
				lsc.LIT_KZL AS MAN,
				lsc2.LIT_KZL AS AK,
				emp.emp_id AS PNR,
				GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01'), IFNULL(etab6.valid_from, '1915-01-01')) AS Történetiségkezdete,
				LEAST(IFNULL(emp.valid_to, '2038-01-01'), IFNULL(ec.valid_to, '2038-01-01'), IFNULL(etab6.valid_to, '2038-01-01')) AS Történetiségvége,
				'".$child_number."' AS Belsőazonosító,
				LEFT(tab.`value`, LOCATE(' ',tab.`value`) - 1) AS Vezetéknév,
				SUBSTRING_INDEX(tab.`value`, SUBSTRING_INDEX(SUBSTRING_INDEX(tab.`value`, ' ', 1), ' ', -1), -1) AS Utónév,
				tab2.`value` AS Születésiidő,
				CASE tab3.`value`
				WHEN 'Gyerek' THEN '1'
				WHEN 'Magzat' THEN 'HUMAG'
				ELSE NULL
				END AS Rokonságkapcsolat,
				'' AS LOGA_PROCESSED_DATETIME,
				NOW() AS LOGIN_CREATED_DATETIME
			FROM
				employee AS emp
			LEFT JOIN
				loga_sync_connect AS lsc ON lsc.LOGIN_KEY = 'company_id' AND lsc.LOGIN_ID = emp.company_id
			LEFT JOIN
				loga_sync_connect AS lsc2 ON lsc2.LOGIN_KEY = 'payroll_id' AND lsc2.LOGIN_ID = emp.payroll_id
			JOIN
				employee_contract AS ec ON ec.employee_id = emp.employee_id
				AND ec.status = 2
				AND `ec`.`valid_from` <= IFNULL(`emp`.`valid_to`,'2038-01-01') AND `emp`.`valid_from` <= IFNULL(`ec`.`valid_to`,'2038-01-01')
			LEFT JOIN `employee_tab` AS `etab6` ON
				etab6.connect_id = emp.employee_id
                AND etab6.tab_id = 6
				AND etab6.status = 2
				AND etab6.`valid_from` <= LEAST(IFNULL(emp.valid_to, '2038-01-01'), IFNULL(ec.valid_to, '2038-01-01')) AND GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01')) <= IFNULL(etab6.`valid_to`,'2038-01-01')

			LEFT JOIN `employee_tab_item` tab ON tab.`column_id` LIKE '".$child_number."_child_name.'
				AND etab6.employee_tab_id = tab.employee_tab_id
				AND tab.`status` = 2
			LEFT JOIN `employee_tab_item` tab2 ON tab2.`column_id` LIKE 'birth_date_".$bdayString."_child'
				AND etab6.employee_tab_id = tab2.employee_tab_id
				AND tab2.`status` = 2
			LEFT JOIN `employee_tab_item` tab3 ON tab3.`column_id` LIKE '".$child_number."_child_relation'
				AND etab6.employee_tab_id = tab3.employee_tab_id
				AND tab3.`status` = 2
			JOIN
				temp_data_pgrdat AS tdp ON tdp.emp_id = emp.emp_id
 			WHERE
				tdp.emp_id IS NOT NULL
				AND tab.`value` IS NOT NULL
				AND tab.`value` <> ''
				AND emp.status = 2
				AND ('$emp_id' = 'ALL' OR emp.emp_id = '$emp_id')
			GROUP BY emp.employee_id,
 				GREATEST(IFNULL(emp.valid_from, '1915-01-01'), IFNULL(ec.valid_from, '1915-01-01'), IFNULL(etab6.valid_from, '1915-01-01'))
			ORDER BY emp.employee_id
		";

		$res = $conn->createCommand($SQL)->queryAll();

		return $res;
	}
}