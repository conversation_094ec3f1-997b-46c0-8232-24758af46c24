<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2\Grid2Controller;
	use app\models\AuthRolegroup;
	use app\models\Status;
	use Yang;

`/yii2-only';


#yii2: done

class RolegroupController extends Grid2Controller
{
	private $defaultEnd;
	private $statusPublished = Status::PUBLISHED;
	private $useCompanyAndPayrollRights;

	public function __construct() {
		parent::__construct("rolegroup");
		$this->defaultEnd = App::getSetting("defaultEnd");
		$this->useCompanyAndPayrollRights = (int)App::getSetting("useCompanyAndPayrollRights");
	}

	protected function G2BInit() {
		$this->LAGridDB->setModelName("AuthRolegroup");

		parent::setControllerPageTitleId("page_title_rolegroup");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		false);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);

//		parent::exportSettings(Dict::getValue("export_file_rolegroup"));

		if ($this->useCompanyAndPayrollRights) {
			$art = new ApproverRelatedGroup;
			$gargSQL = $art->getApproverReleatedGroupSQL("AuthRolegroup", ["companyMainData"], userID(), "CURDATE()", "AND", "CurrentDate");
		} else {
			$gargSQL = ["where" => ""];
		}

		$SQL ="
			SELECT
				`auth_rolegroup`.`row_id`,
				`auth_rolegroup`.`company_id`,
				`auth_rolegroup`.`company_id` AS grid_company_id,
				`auth_rolegroup`.`payroll_id`,
				`auth_rolegroup`.`payroll_id` AS grid_payroll_id,
				`auth_rolegroup`.`rolegroup_id`,
				`auth_rolegroup`.`rolegroup_name`
			FROM `auth_rolegroup`
			WHERE
					`auth_rolegroup`.`visibility` = 1
				AND (`auth_rolegroup`.`company_id` = '{company}' OR '{company}' = 'ALL' OR `auth_rolegroup`.`company_id` = 'ALL')
				AND (`auth_rolegroup`.`payroll_id` = '{payroll}' OR '{payroll}' = 'ALL' OR `auth_rolegroup`.`payroll_id` = 'ALL')
				{$gargSQL["where"]}
		";
		$this->LAGridDB->enableSQLMode();
		$this->LAGridDB->setSQLSelection($SQL, "row_id");

		parent::G2BInit();
	}

	/**
	 * Kereső mezők
	 * @return array
	 */
	public function search()
	{
		if ($this->useCompanyAndPayrollRights) {
			$art = new ApproverRelatedGroup;
			$gargSQL = $art->getApproverReleatedGroupSQL("Company", ["companyMainData"], userID(), "CURDATE()", "AND", "CurrentDate");
			$gargPayrollSQL = $art->getApproverReleatedGroupSQL("Payroll", ["companyMainData"], userID(), "CURDATE()", "AND", "CurrentDate");
		} else {
			$gargSQL = ["where" => ""];
			$gargPayrollSQL = ["where" => ""];
		}

		$ret =
		[
			'company'					=> [
				'col_type'		=> 'combo',
				'options'		=>	[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							`company_id` AS id,
							`company_name` AS value
						FROM `company`
						WHERE
								`status` = {$this->statusPublished}
							AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->defaultEnd}')
							{$gargSQL["where"]}
						ORDER BY value
					",
					'array'	=> [["id" => "ALL", "value" => Dict::getValue("all")]]
				],
				'label_text'	=> Dict::getValue("company"),
				'onchange'		=> ["payroll"]
			],
			'payroll'					=> [
				'col_type'	=> 'combo',
				'options'	=>	[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							`payroll_id` AS id,
							`payroll_name` AS value
						FROM `payroll`
						WHERE
								`status` = {$this->statusPublished}
							AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->defaultEnd}')
							AND (`company_id` = '{company}' OR 'ALL' = '{company}' OR `company_id` = 'ALL')
							{$gargPayrollSQL["where"]}
						ORDER BY value
					",
					'array'	=> [["id" => "ALL", "value" => Dict::getValue("all")]]
				],
				'label_text'=> Dict::getValue("Payroll")
			],
			'submit'					=> ['col_type' => 'searchBarReinitGrid', 'gridID' => 'dhtmlxGrid', 'width' => '*', 'label_text' => '']
		];
		if ($this->useCompanyAndPayrollRights) {
			unset($ret["company"]["options"]["array"]);
		}
		return $ret;
	}

	public function columns()
	{
		if ($this->useCompanyAndPayrollRights) {
			$art = new ApproverRelatedGroup;
			$gargSQL = $art->getApproverReleatedGroupSQL("Company", ["companyMainData"], userID(), "CURDATE()", "AND", "CurrentDate");
			$gargPayrollSQL = $art->getApproverReleatedGroupSQL("Payroll", ["companyMainData"], userID(), "CURDATE()", "AND", "CurrentDate");
		} else {
			$gargSQL = ["where" => ""];
			$gargPayrollSQL = ["where" => ""];
		}

		$ret =
		[
//			'rolegroup_id'				=> array('export'=> false, 'col_type'=>'ro'),
			'grid_company_id' => [
				'export'		=> true,
				'grid'			=> true,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width' 		=> 250,
				'edit'			=> false,
				'window'        => false,
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							`company_id` AS id,
							`company_name` AS value
						FROM `company`
						WHERE
								`status` = {$this->statusPublished}
							AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->defaultEnd}')
							{$gargSQL["where"]}
						ORDER BY value
					",
					'array'	=> [["id" => "ALL", "value" => Dict::getValue("all")]]
				]
			],
			'company_id' => [
				'export'		=> false,
				'grid'			=> false,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width' 		=> 250,
				'edit'			=> true,
				'window'		=> true,
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							`company_id` AS id,
							`company_name` AS value
						FROM `company`
						WHERE
								`status` = {$this->statusPublished}
							AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->defaultEnd}')
							{$gargSQL["where"]}
						ORDER BY value
					",
					'array'	=> [["id" => "ALL", "value" => Dict::getValue("all")]]
				],
				'onchange'		=> ["payroll_id"]
			],
			'grid_payroll_id' => [
				'export'		=> true,
				'grid'			=> true,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width' 		=> 250,
				'edit'			=> false,
				'window'        => false,
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							`payroll_id` AS id,
							`payroll_name` AS value
						FROM `payroll`
						WHERE 
								`status` = {$this->statusPublished}
							AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->defaultEnd}')
							{$gargPayrollSQL["where"]}
						ORDER BY value
					",
					'array'	=> [["id" => "ALL", "value" => Dict::getValue("all")]]
				]
			],
			'payroll_id' => [
				'export'		=> false,
				'grid'			=> false,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width' 		=> 250,
				'edit'			=> true,
				'window'		=> true,
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							`payroll_id` AS id,
							`payroll_name` AS value
						FROM `payroll`
						WHERE 
								`status` = {$this->statusPublished}
							AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->defaultEnd}')
							AND (`company_id` = '{company_id}' OR 'ALL' = '{company_id}' OR `company_id` = 'ALL')
							{$gargPayrollSQL["where"]}
						ORDER BY value
					",
					'array'	=> [["id" => "ALL", "value" => Dict::getValue("all")]]
				]
			],
			'rolegroup_name'			=> ['export'=> true , 'col_type'=>'ed', 'width'=>'*']
		];

		if ($this->useCompanyAndPayrollRights) {
			unset($ret["company_id"]["options"]["array"]);
		}
		return $ret;
	}

	/**
	 * Grid oszlopcímkék
	 * @return array
	 */
	public function attributeLabels()
	{
		return [
			'company_id'				=> Dict::getValue("company"),
			'grid_company_id'			=> Dict::getValue("company"),
			'payroll_id'				=> Dict::getValue("Payroll"),
			'grid_payroll_id'			=> Dict::getValue("Payroll"),
			'rolegroup_name'			=> Dict::getValue("rolegroup_name")
		];
	}

	/**
	 * Mentés / szerkesztés funkció
	 * @param array $data
	 * @param string $modelName
	 * @param string $pk
	 * @param boolean $vOnly
	 * @param boolean $ret
	 * @param string $contentId
	 * @return void
	 */
	public function actionSave($data = [], $modelName = null, $pk = null, $vOnly = false, $ret = false, $contentId = null)
	{
		$this->layout = '//layouts/ajax';

		$generateFrom	= requestParam('generateFrom');
		$data			= requestParam('dialogInput_'.$generateFrom);
		$editPK			= requestParam('editPK');
		$valid			= true;

		$status = [
			'status'	=> 1,
			'pkSaved'	=> null,
			'error'		=> "",
		];

		if ($editPK != '')
		{
			$SQL = "SELECT * FROM `auth_rolegroup` WHERE `rolegroup_name` = '{$data["rolegroup_name"]}' AND `row_id` <> {$editPK}";
			$res = dbFetchRow($SQL);
			if (!empty($res)) {
				$valid = false;
				$status = [
					'status'	=> 0,
					'pkSaved'	=> null,
					'error'		=> Dict::getModuleValue("ttwa-base", "error_duplicated_name", ["nameLabel" => $res["rolegroup_name"]]),
				];
			}
		} else {
			$SQL = "SELECT * FROM `auth_rolegroup` WHERE `rolegroup_name` = '{$data["rolegroup_name"]}'";
			$res = dbFetchRow($SQL);
			if (!empty($res)) {
				$valid = false;
				$status = [
					'status'	=> 0,
					'pkSaved'	=> null,
					'error'		=> Dict::getModuleValue("ttwa-base", "error_duplicated_name", ["nameLabel" => $res["rolegroup_name"]]),
				];
			}
		}

		if ($valid) {
			parent::actionSave();
		} else {
			echo json_encode($status);
		}
	}
}
?>
