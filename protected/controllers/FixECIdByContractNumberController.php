<?php

class FixECIdByContractNumberController extends Controller
{
	private $publishedStatus = Status::PUBLISHED;
	private $invalidStatus = Status::INVALID;

	public function actionIndex()
	{
		ini_set('memory_limit', '5096M');
		ini_set('max_execution_time', 0);
		echo "<pre>";
		
		$idLength			= requestParam('length');
		$validFrom			= requestParam('validFrom');
		$validTo			= requestParam('validTo');
		$usePayrollTables	= requestParam('payroll');

		if ($idLength)
		{
			$fixableContracts = $this->getFixableContracts($idLength, $validFrom, $validTo);
			$tableData = $this->getTableData($usePayrollTables);
			$skippedTables = ['`bulk_group_change_by_cog_request`'];

			foreach ($fixableContracts as $contract)
			{
				$newContractId = substr($contract['employee_contract_id'], 0, -1) . $contract['employee_contract_number'];
				$sql = '';

				foreach ($tableData as $table => $tableFields)
				{
					$dateField = $tableFields['dateField'];
					if (!empty($dateField) && !in_array($table, $skippedTables)) 
					{
						$selectSql = "
							SELECT * 
							FROM {$table}
						";
						$where = "
								WHERE
										employee_contract_id = '" . $contract['employee_contract_id'] ."'
							";
						
						if ($tableFields['hasStatus']) {
							$where .= " AND status <> " . $this->invalidStatus;
						}

						if ($dateField == 'date' || $dateField == 'day') {
							$where .= " AND `" . $dateField . "` BETWEEN '" . $contract['valid_from'] . "' AND '" . $contract['valid_to'] . "';";
						} else if ($dateField == 'valid_from') {
							$where .= " AND valid_from <= '" . $contract['valid_to'] . "'
									AND valid_to >= '" . $contract['valid_from'] . "';";
						}

						$row = dbFetchRow($selectSql . $where);

						if (isset($row['status']) && !empty($row['status']))
						{
							unset($row['row_id']);
							unset($row['status']);
							$columns = implode(", ", array_keys($row));
							$insertSql = "INSERT INTO {$table} ({$columns}, status) 
											SELECT {$columns}, " . $this->invalidStatus . " AS status FROM {$table} {$where};";
							$insertSql = strtr($insertSql, ["''" => "NULL"]);
							echo $insertSql . "\r\n";
							dbExecute($insertSql);
						}

						$updateSql = "
							UPDATE {$table} 
							SET 
								employee_contract_id = '{$newContractId}'
							{$where}
						";

						echo $updateSql . "\r\n";
						dbExecute($updateSql);
					}
					else
					{
						$skippedTables[] = $table;
					}
				}
			}
			

			echo "A következő táblák nem frissültek:\r\n";
			echo implode(",\r\n", $skippedTables);
		}
		else
		{
			echo "Something missing?";
		}
	}

	private function getFixableContracts($idLength, $validFrom = null, $validTo = null)
	{
		$getFixableContractsSql = "
				SELECT
					employee_contract_id,
					employee_contract_number,
					valid_from,
					valid_to
				FROM
					employee_contract
				WHERE
						status = " . $this->publishedStatus . "
					AND RIGHT(employee_contract_id, 1) <> employee_contract_number
					AND LENGTH(employee_contract_id) = '{$idLength}'
			";

			if (!empty($validFrom) && !empty($validTo)) {
				$getFixableContractsSql .= "
					AND valid_from <= '{$validTo}'
					AND valid_to >= '{$validFrom}'
				";
			} else if (!empty($validFrom)) {
				$getFixableContractsSql .= "
					'{$validFrom}' BETWEEN valid_from AND valid_to
				";
			}
			return dbFetchAll($getFixableContractsSql);
	}

	private function getTableData($usePayrollTables = 0)
	{
		$connection = Yii::app()->db;//get connection
		$dbSchema = $connection->schema;
		$tables = $dbSchema->getTables();//returns array of tbl schema's
		$tableRes = [];

		foreach($tables as $tbl)
		{
			if ($tbl->rawName != "`v_employee_calc_per_day`" 
				&& in_array("employee_contract_id", $tbl->columnNames)
				&& preg_match('/^`(TEMP|temp|tmp|_|old)/', $tbl->rawName) !== 1
				&& preg_match('/(bck|_old|tmp)`$/', $tbl->rawName) !== 1
				&& (!$usePayrollTables && preg_match('/^`(payroll)/', $tbl->rawName) !== 1)
			)
			{
				$tableRes[$tbl->rawName] = $this->getDateField($tbl->columnNames);
			}
		}

		return $tableRes;
	}

	private function getDateField($columns)
	{
		$fields = ['date', 'day', 'valid_from'];
		$i = 0;
		$res = [];
		while ($i <= count($fields))
		{
			if (in_array($fields[$i], $columns)) {
				$res['dateField'] = $fields[$i];
			}
			$i++;
		}
		if (in_array('status', $columns)) {
			$res['hasStatus'] = true;
		} else {
			$res['hasStatus'] = false;
		}
		return $res;
	}
}