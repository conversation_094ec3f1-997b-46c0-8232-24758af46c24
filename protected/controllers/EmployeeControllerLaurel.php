<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\ArrayToCsv;
	use app\components\Dict;
	use app\components\FS;
	use app\models\FileStorage;
	use app\models\Status;
	use Yang;

`/yii2-only';


/**
 * Sync Euronics employees to Laurel (Prohuman) interface
 * Ask the user for modifications approval to send to <PERSON>, only if approved generate new CSV files for Laurel
 * Generate predefined CSV files for laurel, with full employee base
 * 
 * Innote link: laurel-vorosko
 */

trait EmployeeControllerLaurel
{
	/**
	 * Basic Laurel sync run action
	 */
	public function actionLaurelSync() {
		$status = $this->laurelSync();
		echo json_encode($status);
		exit();
	}
	
	/**
	 * First action to ask for file generating approval of new modifications, if approved: run sync
	 */
	public function actionLaurelSyncQuestion()
	{
		$ret = [];
		$ret["modifications"] = Dict::getValue("laurel_csv_appprove") . "<br />";
		
		$firstRun = false;
		
		if (!$firstRun) {
			$lastParams = [];
			$lastData = $this->getLastSync();
			for ($i = 0; $i < count($lastData); $i++)
			{
				$log_params = json_decode($lastData[$i]["log_params"], TRUE);	
				$lastParams[$lastData[$i]["tax_number"]] = $log_params;
			}

			$currentData = $this->getCurrentEmployees();
			$currData = [];
			foreach ($currentData as $cD) {
				$newKey = $cD["TSZEM_ADOAZON"];
				$currData[$newKey] = $cD;
			}

			$results = $this->arrayDiffAssocMultidimensional($currData, $lastParams);

			$counter = 0;
			foreach ($results as $key => $res)
			{
				$changes = false;
				$changedData = "";
				foreach ($res as $colKey => $r) {
					$end = substr($colKey, -8);
					if ($end != "TTWADESC" && $end != "TTWANAME" && $end != "TTWADICT") {
						$counter++;
						$changes = true;
						$changedData .=
						Dict::getValue("changed_column") . ": " . $currData[$key][$colKey . "_TTWADESC"] . "<br />" .
						Dict::getValue("changed_from") . ": " .$lastParams[$key][$colKey . "_TTWANAME"] . "<br />" .
						Dict::getValue("changed_to") . ": " . $currData[$key][$colKey . "_TTWANAME"] . "<br />";
					}
				}
				if ($changes) {
					$ret["modifications"] .= "<br />" . Dict::getValue("modified_employee") . ": " . $key . "<br />";
					$ret["modifications"] .= $changedData;
				}
			}

			if ($counter == 0) {
				$ret["modifications"] .= Dict::getValue("data_upload_no_modification");
			}
		}
		
		
		echo json_encode($ret);
		exit();
	}
	
	/**
	 * Get differences in two arrays
	 * @param array $array1
	 * @param array $array2
	 * @return array difference
	 */
	public function arrayDiffAssocMultidimensional(array $array1, array $array2)
	{
		$difference = [];
		foreach ($array1 as $key => $value) {
			if (is_array($value)) {
				if (!array_key_exists($key, $array2)) {
					$difference[$key] = $value;
				} elseif (!is_array($array2[$key])) {
					$difference[$key] = $value;
				} else {
					$multidimensionalDiff = $this->arrayDiffAssocMultidimensional($value, $array2[$key]);
					if (count($multidimensionalDiff) > 0) {
    					$difference[$key] = $multidimensionalDiff;
					}
				}
			} else {
				if (!array_key_exists($key, $array2) || $array2[$key] !== $value) {
					$difference[$key] = $value;
				}
			}
		}
		return $difference;
	}
	
	/**
	 * Check if value in multidim array
	 * @param type $needle
	 * @param type $haystack
	 * @param type $strict
	 * @return boolean found or not
	 */
	function inArrayR($needle, $haystack, $strict = false)
	{
		foreach ($haystack as $item) {
			if (($strict ? $item === $needle : $item == $needle) || (is_array($item) && $this->inArrayR($needle, $item, $strict))) {
				return true;
			}
		}
		return false;
	}
	
	/**
	 * Get all data from last laurel sync
	 * @return array last sync data
	 */
	public function getLastSync() {
		$SQL = "SELECT * FROM laurel_sync_log WHERE `last` = '1'";
		$res = dbFetchAll($SQL);
		return $res;
	}
	
	/**
	 * Based on Laurel sync config and sync connect table generate all current employee data
	 * @return array current employee data
	 */
	public function getCurrentEmployees()
	{
		// Fields for select and convert table joining
		$FIELDSSQL = "SELECT
						field_name,
						field_table,
						field_alias,
						field_function,
						file_name,
						field_dict_id,
						hidden,
						forced_value,
						convert_id,
						convert_id_login_field
					FROM laurel_sync_config
					WHERE `status` = ".Status::PUBLISHED."";
		$fields = dbFetchAll($FIELDSSQL);
		// TTWA table joins
		$TABLESSQL = "SELECT
						field_table,
						field_name,
						table_connector
					FROM laurel_sync_config WHERE `status` = ".Status::PUBLISHED." GROUP BY field_table ORDER BY field_table DESC";
		$tables = dbFetchAll($TABLESSQL);
		
		$SELECT = "";
		$JOINS = "";
		$CONVERT = "";
		
		// SELECT & Convert table
		$fieldCounter = 0;
		foreach ($fields as $f)
		{
			$fieldCounter++;
			// hidden forced field
			if ($f["hidden"] == "1") {
				$SELECT .= " '" . $f["forced_value"] . "' AS " . $f["file_name"] . "_" . $f["field_alias"] . "";
			} else {
				// dict to show for user at modifications window
				// Original --> too many joins mysql error
				/*if ($f["field_dict_id"] != "unknown") {
						$SELECT .= "`dict" . $fieldCounter . "`.`dict_value` AS " . $f["file_name"] . "_" . $f["field_alias"] . "_TTWADESC,";
						$JOINS .= " LEFT JOIN dictionary AS dict" . $fieldCounter . " ON
									`dict" . $fieldCounter . "`.`dict_id` = '" . $f["field_dict_id"] . "'
								AND `dict" . $fieldCounter . "`.`valid` = '1'
								AND `dict" . $fieldCounter . "`.`lang` = '" . Dict::getLang() . "'
								AND `dict" . $fieldCounter . "`.`module` = 'ttwa-base' 
							";
				}*/
				if ($f["field_dict_id"] != "unknown") {
					$SELECT .= "'" . $f["field_dict_id"] . "' AS " . $f["file_name"] . "_" . $f["field_alias"] . "_TTWADICT,";
				}

				// Check for convert table
				if ($f["convert_id"] == "1")
				{
					// Check for forced value
					if ($f["forced_value"] == "" || $f["forced_value"] == "NULL") {
						$SELECT .= " `lsc" . $fieldCounter . "`.`laurel_field_id` AS " . $f["file_name"] . "_" . $f["field_alias"] . ",";
					} else {
						$SELECT .= " '" . $f["forced_value"] . "' AS " . $f["file_name"] . "_" . $f["field_alias"] . ",";
					}

					$SELECT .= " `lsc" . $fieldCounter . "`.`login_field_name` AS " . $f["file_name"] . "_" . $f["field_alias"] . "_TTWANAME
					";

					if ($f["field_function"] == "" || $f["field_function"] == "NULL") {
						$CONVERT .= " LEFT JOIN laurel_sync_connect AS lsc" . $fieldCounter . " ON
										`lsc" . $fieldCounter . "`.`laurel_field` = '" . $f["field_alias"] . "'
									AND `lsc" . $fieldCounter . "`.`login_field_id` = `" . $f["field_table"] . "`.`" . $f["field_name"] ."` 
									AND `lsc" . $fieldCounter . "`.`status` = " . Status::PUBLISHED . "
									AND `lsc" . $fieldCounter . "`.`laurel_validated` = '1'
									AND `lsc" . $fieldCounter . "`.`login_validated` = '1'
						";
					} else {
						$CONVERT .= " LEFT JOIN laurel_sync_connect AS lsc" . $fieldCounter . " ON
										`lsc" . $fieldCounter . "`.`laurel_field` = '" . $f["field_alias"] . "'
									AND `lsc" . $fieldCounter . "`.`login_field_id` = " . $f["field_function"] . " 
									AND `lsc" . $fieldCounter . "`.`status` = " . Status::PUBLISHED . "
									AND `lsc" . $fieldCounter . "`.`laurel_validated` = '1'
									AND `lsc" . $fieldCounter . "`.`login_validated` = '1'
						";
					}

				} else {
					// Check for forced value
					if ($f["forced_value"] == "" || $f["forced_value"] == "NULL") {
						if ($f["field_function"] == "" || $f["field_function"] == "NULL") {
							$SELECT .= " `" . $f["field_table"] . "`.`" . $f["field_name"] . "` AS " . $f["file_name"] . "_" . $f["field_alias"] . ",";
						} else {
							$SELECT .= $f["field_function"] . " AS " . $f["file_name"] . "_" . $f["field_alias"] . ",";
						}
					} else {
						$SELECT .= " '" . $f["forced_value"] . "' AS " . $f["file_name"] . "_" . $f["field_alias"] . ",";
					}

					// Check for field_name if exists, else original value
					$nameColumn = explode("_id", $f["field_name"]);
					if (count($nameColumn) > 1) {
						$nameColumn = $nameColumn[0] . "_name";
						$CHECKCOL = "SELECT * FROM information_schema.COLUMNS WHERE TABLE_NAME = '" . $f["field_table"] . "' AND COLUMN_NAME = '{$nameColumn}'";
						$res = dbFetchAll($CHECKCOL);
						if (count($res) > 0) {
							$fieldName = "`" . $f["field_table"] . "`.`{$nameColumn}`";
						} else {
							$fieldName = " `" . $f["field_table"] . "`.`" . $f["field_name"] . "`";
						}
					} else {
						if ($f["field_function"] == "" || $f["field_function"] == "NULL") {
							$fieldName = " `" . $f["field_table"] . "`.`" . $f["field_name"] . "`";
						} else {
							$fieldName = $f["field_function"];
						}
					}

					$SELECT .= " {$fieldName} AS " . $f["file_name"] . "_" . $f["field_alias"] . "_TTWANAME
							";
				}
			}
			
			// Check for last field
			if ($fieldCounter != count($fields)) {
				$SELECT .= ",";
			}
		}
		
		
		// TTWA TABLE JOINS
		foreach ($tables as $t)
		{
			if ($t["field_table"] != "employee" && $t["field_table"] != "employee_contract" && 
				$t["field_table"] != "unknown" && $t["table_connector"] != "unknown") {
				if ($t["table_connector"] == "employee_id") {
					$JOINS .= " LEFT JOIN " . $t["field_table"] . " ON
							`" . $t["field_table"] . "`.`employee_id` = `employee`.`employee_id`
						AND `" . $t["field_table"] . "`.`status` = " . Status::PUBLISHED . "
						AND (CURDATE() BETWEEN `" . $t["field_table"] . "`.`valid_from` AND `" . $t["field_table"] . "`.`valid_to`)
					";
				} elseif ($t["table_connector"] == "employee_contract_id") {
					$JOINS .= " LEFT JOIN " . $t["field_table"] . " ON
							`" . $t["field_table"] . "`.`employee_contract_id` = `employee_contract`.`employee_contract_id`
						AND `" . $t["field_table"] . "`.`status` = " . Status::PUBLISHED . "
						AND (CURDATE() BETWEEN `" . $t["field_table"] . "`.`valid_from` AND `" . $t["field_table"] . "`.`valid_to`)
					";
				} else {
					$JOINS .= " LEFT JOIN " . $t["field_table"] . " ON
							`" . $t["field_table"] . "`.`" . $t["field_name"] . "` = `" . $t["table_connector"] . "`.`" . $t["field_name"] . "`
						AND `" . $t["field_table"] . "`.`status` = " . Status::PUBLISHED . "
						AND (CURDATE() BETWEEN `" . $t["field_table"] . "`.`valid_from` AND `" . $t["field_table"] . "`.`valid_to`)
					";
				}
				
			}
		}
		
		$EMPLOYEESSQL = "
			SELECT
				{$SELECT}
			FROM employee
			JOIN employee_contract ON
					`employee_contract`.`employee_id` = `employee`.`employee_id`
				AND `employee_contract`.`status` = " . Status::PUBLISHED . "
				AND (CURDATE() BETWEEN `employee_contract`.`valid_from` AND `employee_contract`.`valid_to`)
			{$JOINS}
			{$CONVERT}
			WHERE
					`employee`.`status` = " . Status::PUBLISHED . "
				AND CHAR_LENGTH(`employee`.`tax_number`) = 10
				AND CHAR_LENGTH(`employee_ext`.`ssn`) = 11
				AND `employee_ext`.`ssn` LIKE '% % %'
				AND `employee_ext`.`place_of_birth` <> ''
				AND `employee_ext`.`place_of_birth` IS NOT NULL
				AND `employee_contract`.`employee_position_id` IS NOT NULL
				AND `employee_contract`.`employee_position_id` <> ''
				AND `employee_ext`.`date_of_birth` IS NOT NULL
				AND `employee_ext`.`date_of_birth` <> ''
				AND `employee_ext`.`mothers_name` IS NOT NULL
				AND `employee_ext`.`mothers_name` IS NOT NULL
				AND `employee_ext2`.`ext2_option12` IS NOT NULL
				AND `employee_ext2`.`ext2_option12` <> ''
				AND `employee_address`.`full_address` IS NOT NULL
				AND `employee_address`.`full_address` <> ''
				AND `employee_address`.`full_address` LIKE '% %, %'
				AND `employee_ext`.`option5` IS NOT NULL
				AND `employee_ext`.`option5` <> ''
				AND (CURDATE() BETWEEN `employee`.`valid_from` AND `employee`.`valid_to`)
		";
			
		$employees = dbFetchAll($EMPLOYEESSQL);

		// Get Desc for modified fields
		foreach ($employees as $empKey => $employee)
		{
			foreach ($employee as $key => $value)
			{
				$end = substr($key, -8);
				$field = substr($key, 0, -8);
				if ($end == "TTWADICT")
				{
					$employees[$empKey][$field. "TTWADESC"] = Dict::getValue($value);
				}
			}
		}
		
		return $employees;
	}

	/**
	 * Generate files to FTP folder and donwloadable, return result window
	 * @return array results to show
	 */
	public function laurelSync()
	{
		// Get employee data
		$currentData = $this->getCurrentEmployees();
		
		// Get files
		$keys = array_keys($currentData[0]);
		$files = [];
		foreach ($keys as $k) {
			$fileName = explode("_", $k);
			$fileName = $fileName[0];
			if (!in_array($fileName, $files)) {
				$files[] = $fileName;
			}
		}
		
		// Generate files
		$file_group_id = $this->saveLaurelFiles($files, $currentData);
		
		// Show results and file downloads
		$has_error = false;
		if ($has_error) {
			$color = "#F98C25";
			$msg = Dict::getValue("failed_upload");
		} else {
			$color = "#039BE5";
			$msg = Dict::getValue("data_upload_success");
		}
		
		$created_on = date("Y-m-d H:i:s");
		
		$html = '<div class="'.($has_error?"orange":"").'" style="width:100%;height:100%;box-sizing:border-box;">';
		$html .= '<span style="color:'.$color.';font-size:16px;">'.Dict::getValue("created_on").": <b>$created_on</b>".'</span><br/>';
		if (!empty($msg)) {
			$html .= "<b>$msg</b><br/>";
		}
		
		$fs = new FileStorage;
		$crt = new CDbCriteria();
		$crt->condition = "`file_group_id` = '{$file_group_id}'";
		$crt->order = "`row_id` ASC";
		$res = $fs->findAll($crt);

		foreach ($res as $file) {
			$html .= '<i><a href="/filestorage/downloadFile?file_id='.$file->file_id.'" onclick="window.open(this.href);return false;">'.$file->file_name.'</a></i></br>';
		}

		$html .= '</div>';
		
		$ret = [];
		$ret["title"] = Dict::getValue("laurel_csv_generation");
		$ret["html"] = $html;
		$ret["has_error"] = $has_error;

		return $ret;
	}
	
	/**
	 * Save sync to log and files to given location
	 * @param array $filelist
	 * @param array $data
	 * @return string uniqueid
	 */
	public function saveLaurelFiles($files, $data)
	{
		// Save sync to log
		$file_group_id = $this->saveSyncLog($data);
		
		// Generate files with correct data
		foreach ($files as $f)
		{
			$fileName = $f . date("YmdHis").".csv";
			$dataArray = [];
			$colArray = [];
			foreach ($data as $empKey => $dataRows)
			{
				$counter = 0;
				$taxNumber = "";
				foreach ($dataRows as $key => $dR)
				{
					$explode = explode("_", $key, 2);
					$end = substr($key, -8);
					// Check if current file and not modification info fields
					if ($f == $explode[0] && $end != "TTWADESC" && $end != "TTWANAME" && $end != "TTWADICT") {
						if ($f == "TIG")
						{
							if (substr($explode[1], 0, 6) == "KIGTIP") {
								if (!in_array("KIGTIP_NEV", $colArray[0])) {
									$colArray[0][] = "KIGTIP_NEV";
								}
								if ($counter > 0) {
									$dataArray[$empKey.$counter]["TSZEM_ADOAZON"] = $taxNumber;
								}
								$dataArray[$empKey.$counter]["KIGTIP_NEV"] = $dR;
							} else if (substr($explode[1], 0, 6) == "IGSZAM") {
								if (!in_array("IGSZAM", $colArray[0])) {
									$colArray[0][] = "IGSZAM";
								}
								$dataArray[$empKey.$counter]["IGSZAM"] = $dR;
								$counter++;
							} else {
								if (!in_array($explode[1], $colArray[0])) {
									$colArray[0][] = $explode[1];
								}
								$dataArray[$empKey.$counter][$key] = $dR;
								$taxNumber = $dR;
							}
						} else {
							if (!in_array($explode[1], $colArray[0])) {
								$colArray[0][] = $explode[1];
							}
							if (($f == "KEGYSEG" || $f == "KMKOR" || $f == "KKIFIZ") && $explode[1] == "KOD" && $dR != "") {
								if(!$this->inArrayR($dR, $dataArray)) {
									$dataArray[$empKey][$key] = $dR;
								}
							} elseif (($f == "KEGYSEG" || $f == "KMKOR" || $f == "KKIFIZ") && $explode[1] == "NEV" && $dR != "") {
								if(!$this->inArrayR($dR, $dataArray)) {
									$dataArray[$empKey][$key] = $dR;
								}
							} elseif ($f == "KMKOR" && $explode[1] == "ERVKEZEVHO" && $dR == "") {
								
							} else {
								$dataArray[$empKey][$key] = $dR;
							}
						}
					}
				}
			}
			
			$dataArray = array_merge($colArray, $dataArray);
			
			$csv = ArrayToCsv::getCsvText($dataArray);
			$csv = iconv('UTF-8', 'Windows-1250//TRANSLIT', $csv);

			$path = Yang::getBasePath() . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'ftp' . DIRECTORY_SEPARATOR . 'vorosko_ftp';
			if (!file_exists($path)) {
				mkdir($path, 0775, true);
			}

			$file = fopen($path . DIRECTORY_SEPARATOR . $fileName, "w") or die("Unable to open file!");
			fwrite($file, $csv);
			fclose($file);

			$fs = new FS;
			$fs->disableMySQLStore();
			$fs->setFileGroupID($file_group_id);
			$fs->uploadTextFileFromContent($fileName, $csv, "text/csv");
		}
		
		return $file_group_id;
		
	}
	
	/**
	 * Save sync to log db table and mark previous ones as not last
	 * @param array $data
	 * @return string uniqueid
	 */
	public function saveSyncLog($data)
	{
		// Update previous sync log -> set not last anymore
		$UPDATELASTLOGSQL = "UPDATE laurel_sync_log SET last = '0' WHERE last = '1'";
		dbExecute($UPDATELASTLOGSQL);
		
		$counter = 0;
		$SQL = "";
		
		foreach ($data as $d)
		{
			$counter++;
			$logParams = json_encode($d, JSON_UNESCAPED_UNICODE);
			$SQL .= "('" . $d["TSZEM_ADOAZON"] . "', '" . $logParams . "', '1', 'system', NOW())";
			
			if ($counter != count($data)) {
				$SQL .= ",";
			} else {
				$SQL .= ";";
			}
		}
		
		$INSERTNEWSQL = "INSERT INTO laurel_sync_log (tax_number, log_params, last, created_by, created_on) VALUES {$SQL}";
		dbExecute($INSERTNEWSQL);
		
		return uniqid();
	}
}