<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use app\components\Helpers\AnyCache;
	use app\models\ColumnTabs;
	use app\models\Employee;
	use app\models\Status;
	use Yang;

`/yii2-only';


#yii2: done

trait EmployeecontrolControllerWizards
{
	protected function getBigFormModeWizard()
	{
		return [
			'uploadDialog' => 'dhtmlxGrid',
		];
	}

	protected function getBigFormModeColors()
	{
		$ret = [
			'uploadDialog' => [
				"#81C784",
				"#FFB74D",
				"#FF8A65",
				"#9575CD",
				"#4DD0E1",
			],
		];

		return $ret;
	}

	public function wizards()
	{
		$published = Status::PUBLISHED;
		$defaultEnd = App::getSetting('defaultEnd');
		$editPK = explode('_', requestParam('editPK'));
		isset($editPK[1]) ? $searchDate = $editPK[1] : $searchDate = date("Y-m-d");
		
		$groupSQL = dbFetchRow($this->group_SQL());
		$groupID = $groupSQL['id'];

		$employeeBaseAbsenceQuantityMod = Yang::getParam('customerDbPatchName') == 'bos' ? 'quantity_hour' : 'quantity';

		$exprFullName = AnyCache::get("G2BInit.exprFullName2");
		if (!$exprFullName) {
			$exprFullName = Employee::getParam('fullname', 'employee');
			AnyCache::set("G2BInit.exprFullName2", $exprFullName, "-");
		}

		$defaultTabSql = "
						SELECT
							employee.`row_id`,
							employee.`first_name`,
							employee.`last_name`,
							employee.`emp_id`,
							employee.`valid_from`,
							employee.`valid_to`,
							employee.emp_id as employee_id,
							employee.title,
							company.company_id,
							company.row_id AS company_row_id,
							payroll.`payroll_id`,
							payroll.row_id AS payroll_row_id,
							unit.`unit_id`,
							".$exprFullName." as fullname,
							unit.unit_name,
							unit.row_id AS unit_row_id,
							company_org_group1.company_org_group_id AS company_org_group1_id,
							company_org_group2.`company_org_group_id` AS company_org_group2_id,
							company_org_group3.`company_org_group_id` AS company_org_group3_id,
							company_org_group1.row_id AS company_org_group1_row_id,
							company_org_group2.row_id AS company_org_group2_row_id,
							company_org_group3.row_id AS company_org_group3_row_id,
							employee_contract.`employee_position_id`,
							employee_contract.`ec_valid_from`,
							employee_contract.`ec_valid_to`,
							employee_contract.employee_contract_number,
							employee_contract.row_id AS employee_contract_row_id,
							employee_group.`group_value`,
							employee_group.`group_id`,
							employee_group.row_id AS employee_group_row_id,
							employee_cost.`cost_id`,
							employee_cost.`cost_center_id`,
							employee_cost.row_id AS employee_cost_row_id,
							employee_base_absence.`$employeeBaseAbsenceQuantityMod` as quantity,
							employee_base_absence.row_id AS employee_base_absence_row_id,
							employee_group.valid_from as history_valid_from,
							employee_card.card,
							employee_contract.employee_contract_type,
							employee.nameofbirth,
							employee_ext2.ext2_option1,
							employee_ext2.ext2_option10,
							employee_ext2.ext2_option11,
							employee_ext2.ext2_option12,
							employee_ext.option10,
							employee_ext2.ext2_option13,
							employee_ext2.ext2_option8,
							employee_ext2.ext2_option9,
							employee_ext2.ext2_option14,
							employee_ext2.ext2_option15,
							employee_ext2.ext2_option16,
							NULL as workgroup_id,
							employee_ext2.ext2_option17,
							employee_ext2.ext2_option18,
							employee_ext2.ext2_option19,
							employee_ext2.ext2_option20,
							employee_ext2.ext2_option21,
							employee_ext2.ext2_option22,
							employee_ext2.ext2_option23,
							employee_ext2.ext2_option24,
							employee_ext2.ext2_option25,
							employee_ext2.ext2_option26,
							employee_ext2.ext2_option27,
							employee_ext2.ext2_option28,
							employee_ext2.ext2_option29,
							employee_ext2.ext2_option30,
							employee_ext2.ext2_option31,
							employee_ext2.ext2_option32,
							employee_ext2.ext2_option33,
							employee_ext2.ext2_option34,
							employee_ext2.ext2_option35,
							employee_ext2.ext2_option36,
							employee_ext2.ext2_option37,
							employee_ext2.ext2_option38,
							employee_ext2.ext2_option39,
							employee_ext2.ext2_option40,
							employee_ext.date_of_birth,
							employee_ext.place_of_birth,
							employee_ext.mothers_name,
							employee.tax_number,
							employee_ext.ssn,
							employee_ext.personal_id_card_number,
							employee_ext.option1,
							employee.gender,
							employee_address.address_card_number,
							employee_address.full_address,
							employee_address.country,
							employee_address.zip_code,
							employee_address.city,
							employee_address.district,
							employee_address.public_place_name,
							employee_address.public_place_type,
							employee_address.house_number,
							employee_address.floor,
							employee_address.door,
							employee_address.res_full_address,
							employee_address.res_country,
							employee_address.res_zip_code,
							employee_address.res_city,
							employee_address.res_district,
							employee_address.res_public_place_name,
							employee_address.res_public_place_type,
							employee_address.res_house_number,
							employee_address.res_floor,
							employee_address.res_door,
							employee_address.accomodated,
							employee_address.acc_zip_code,
							employee_address.acc_country,
							employee_address.acc_city,
							employee_address.acc_district,
							employee_address.acc_public_place_name,
							employee_address.acc_public_place_type,
							employee_address.acc_house_number,
							employee_address.acc_floor,
							employee_address.acc_door,
							employee_ext3.ext3_option1,
							employee_ext3.ext3_option2,
							employee_ext3.ext3_option3,
							employee_ext3.ext3_option4,
							employee_ext3.ext3_option5,
							employee_ext3.ext3_option6,
							employee_ext3.ext3_option7,
							employee_ext3.ext3_option8,
							employee_ext3.ext3_option9,
							employee_ext3.ext3_option10,
							employee_ext3.ext3_option11,
							employee_ext3.ext3_option12,
							employee_ext3.ext3_option13,
							employee_ext3.ext3_option14,
							employee_ext3.ext3_option15,
							employee_ext3.ext3_option16,
							employee_ext3.ext3_option17,
							employee_ext3.ext3_option18,
							employee_ext3.ext3_option19,
							employee_ext3.ext3_option20,
							employee_ext3.ext3_option21,
							employee_ext3.ext3_option22,
							employee_ext3.ext3_option23,
							employee_ext3.ext3_option24,
							employee_ext3.ext3_option25,
							employee_ext3.ext3_option26,
							employee_ext3.ext3_option27,
							employee_ext3.ext3_option28,
							employee_ext3.ext3_option29,
							employee_ext3.ext3_option30,
							employee_ext3.valid_from as ext3_valid_from,
							employee_ext3.valid_to as ext3_valid_from,
							employee_ext3.ext3_option31,
							employee_ext3.ext3_option32,
							employee_ext3.ext3_option33,
							employee_ext3.ext3_option34,
							employee_ext3.ext3_option35,
							employee_ext3.ext3_option36,
							employee_ext3.ext3_option37,
							employee_ext3.ext3_option38,
							employee_ext3.ext3_option39,
							employee_ext3.ext3_option40,
							employee_salary.personal_month_salary,
							employee_salary.es_option4,
							employee_salary.es_option5,
							employee_salary.es_option2,
							employee_salary.es_option3,
							employee_salary.es_option6,
							employee_salary.es_option7,
							employee_salary.es_option8,
							employee_salary.es_option9,
							employee_salary.es_option10,
							employee_salary.es_option11,
							employee_salary.es_option12,
							employee_salary.es_option13,
							employee_salary.es_option14,
							employee_salary.es_option15,
							employee_salary.es_option16,
							employee_salary.es_option17,
							employee_salary.es_option18,
							employee_salary.es_option19,
							employee_salary.es_option20,
							employee_salary.es_option21,
							employee_salary.es_option22,
							employee_salary.es_option23,
							employee_salary.es_option24,
							employee_ext2.row_id as employee_ext2_row_id,
							employee_ext3.row_id as employee_ext3_row_id,
							employee_ext.row_id as employee_ext_row_id,
							employee_address.row_id as employee_address_row_id,
							employee_contract.employee_contract_id,
							employee_ext4.row_id as employee_ext4_row_id,
							employee_ext3.ext3_option41,
							employee_ext3.ext3_option42,
							employee_ext3.ext3_option43,
							employee_ext3.ext3_option44,
							employee_ext3.ext3_option45,
							employee_ext3.ext3_option46,
							employee_ext3.ext3_option47,
							employee_ext3.ext3_option48,
							employee_ext3.ext3_option49,
							employee_ext3.ext3_option50,
							employee_ext3.ext3_option51,
							employee_ext3.ext3_option52,
							employee_ext3.ext3_option53,
							employee_ext3.ext3_option54,
							employee_ext3.ext3_option55,
							employee_ext3.ext3_option56,
							employee_ext3.ext3_option57,
							employee_ext3.ext3_option58,
							employee_ext3.ext3_option59,
							employee_ext3.ext3_option60,
							employee_ext3.ext3_option61,
							employee_ext3.ext3_option62,
							employee_ext3.ext3_option63,
							employee_ext3.ext3_option64,
							employee_ext3.ext3_option65,
							employee_ext3.ext3_option66,
							employee_ext3.ext3_option67,
							employee_ext3.ext3_option68,
							employee_ext3.ext3_option69,
							employee_ext3.ext3_option70,
							employee_ext3.ext3_option71,
							employee_ext3.ext3_option72,
							employee_ext3.ext3_option73,
							employee_ext3.ext3_option74,
							employee_ext3.ext3_option75,
							employee_ext3.ext3_option76,
							employee_ext3.ext3_option77,
							employee_ext3.ext3_option78,
							employee_ext3.ext3_option79,
							employee_ext3.ext3_option80,
							employee_ext3.ext3_option81,
							employee_ext4.ext4_option1,
							employee_ext4.ext4_option2,
							employee_ext4.ext4_option3,
							employee_ext4.ext4_option4,
							employee_ext4.ext4_option5,
							employee_ext4.ext4_option6,
							employee_ext4.ext4_option7,
							employee_ext4.ext4_option8,
							employee_ext4.ext4_option9,
							employee_ext4.ext4_option10,
							employee_ext4.ext4_option11,
							employee_ext4.ext4_option12,
							employee_ext4.ext4_option13,
							employee_ext4.ext4_option14,
							employee_ext4.ext4_option15,
							employee_ext4.ext4_option16,
							employee_ext4.ext4_option17,
							employee_ext4.ext4_option18,
							employee_ext4.ext4_option19,
							employee_ext4.ext4_option20,
							employee_ext4.ext4_option21,
							employee_ext4.ext4_option22,
							employee_ext4.ext4_option23,
							employee_ext4.ext4_option24,
							employee_ext4.ext4_option25,
							employee_ext4.ext4_option26,
							employee_ext4.ext4_option27,
							employee_ext4.ext4_option28,
							employee_ext4.ext4_option29,
							employee_ext4.ext4_option30,
							employee_ext4.ext4_option31,
							employee_ext4.ext4_option32,
							employee_ext4.ext4_option33,
							employee_ext4.ext4_option34,
							employee_ext4.ext4_option35,
							employee_ext4.ext4_option36,
							employee_ext4.ext4_option37,
							employee_ext4.ext4_option38,
							employee_ext4.ext4_option39,
							employee_ext4.ext4_option40,
							employee_ext4.ext4_option41,
							employee_ext4.ext4_option42,
							employee_ext4.ext4_option43,
							employee_ext4.ext4_option44,
							employee_ext4.ext4_option45,
							employee_ext4.ext4_option46,
							employee_ext4.ext4_option47,
							employee_ext4.ext4_option48,
							employee_ext4.ext4_option49,
							employee_ext4.ext4_option50,
							employee_ext4.ext4_option51,
							employee_ext4.ext4_option52,
							employee_ext4.ext4_option53,
							employee_ext4.ext4_option54,
							employee_ext4.ext4_option55,
							employee_ext4.ext4_option56,
							employee_ext4.ext4_option57,
							employee_ext4.ext4_option58,
							employee_ext4.ext4_option59,
							employee_ext4.ext4_option60,
							employee_salary.es_option25,
							employee_salary.es_option26,
							employee_salary.es_option27,
							employee_salary.es_option28,
							employee_salary.es_option29,
							employee_salary.es_option30,
							employee_salary.es_option31,
							employee_salary.es_option32,
							employee_salary.es_option33,
							employee_salary.es_option34,
							employee_salary.es_option35,
							employee_salary.es_option36,
							employee_salary.es_option37,
							employee_salary.es_option38,
							employee_salary.es_option39,
							employee_salary.es_option40,
							employee_salary.es_option41,
							employee_salary.es_option42,
							employee_salary.es_option43,
							employee_salary.es_option44,
							employee_salary.es_option45,
							employee_salary.es_option46,
							employee_salary.es_option47,
							employee_salary.es_option48,
							employee_salary.es_option49,
							employee_salary.es_option50,
							employee_salary.es_option51,
							employee_salary.es_option52,
							employee_salary.es_option53,
							employee_salary.es_option54,
							employee_salary.es_option55,
							employee_salary.es_option56,
							employee_salary.es_option57,
							employee_docs.row_id as employee_docs_row_id,
							employee_contract.employee_contract_id,
							employee_docs.fs_file_id
						FROM
							`employee`
						LEFT JOIN
							`company` ON
								company.`company_id` = employee.`company_id`
									AND (employee.`valid_from` BETWEEN company.`valid_from` AND IFNULL(company.`valid_to`, '$defaultEnd'))
										AND company.`status` = $published
						LEFT JOIN
							`payroll` ON
								payroll.`payroll_id` = employee.`payroll_id`
									AND (employee.`valid_from` BETWEEN payroll.`valid_from` AND IFNULL(payroll.`valid_to`, '$defaultEnd'))
										AND payroll.`status` = $published
						LEFT JOIN
							`unit` ON
								unit.`unit_id` = employee.`unit_id`
									AND (employee.`valid_from` BETWEEN unit.`valid_from` AND IFNULL(unit.`valid_to`, '$defaultEnd'))
										AND unit.`status` = $published
						LEFT JOIN
							employee_contract ON
								employee_contract.`employee_id` = employee.`employee_id`
									AND ('$searchDate' BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`, '$defaultEnd'))
									AND ('$searchDate' BETWEEN employee_contract.`ec_valid_from` AND IFNULL(employee_contract.`ec_valid_to`, '$defaultEnd'))
									AND employee_contract.`status` = $published
						";
						if (EmployeeGroupConfig::isActiveGroup('workgroup_id')) {
							$defaultTabSql .= EmployeeGroup::getLeftJoinSQL("workgroup_id","employee_contract",$searchDate);
							$defaultTabSql .= " 
							LEFT JOIN
							`workgroup` ON
								workgroup.`workgroup_id` = employee_group_workgroup_id.`group_value`
									AND (employee_group_workgroup_id.`valid_from` BETWEEN workgroup.`valid_from` AND IFNULL(workgroup.`valid_to`, '$defaultEnd'))
										AND workgroup.`status` = $published
							";
						}else{
								$defaultTabSql .= "
								LEFT JOIN
									`workgroup ON`
										workgroup.workgroup_id = employee_contract.workgroup_id
											AND ('$searchDate' BETWEEN workgroup.`valid_from` AND IFNULL(workgroup.`valid_to`, '$defaultEnd'))
												AND workgroup.status = $published
								";
						}

						if (EmployeeGroupConfig::isActiveGroup('company_org_group1_id')) {
							$defaultTabSql .= EmployeeGroup::getLeftJoinSQL("company_org_group1_id","employee_contract",$searchDate);
							$defaultTabSql .= " 
							LEFT JOIN
							`company_org_group1` ON
								company_org_group1.`company_org_group_id` = employee_group_company_org_group1_id.`group_value`
									AND (employee_group_company_org_group1_id.`valid_from` BETWEEN company_org_group1.`valid_from` AND IFNULL(company_org_group1.`valid_to`, '$defaultEnd'))
										AND company_org_group1.`status` = $published
							";
						}else{
								$defaultTabSql .= "
								LEFT JOIN
									`company_org_group1` ON
										company_org_group1.`company_org_group_id` = employee.`company_org_group1_id`
											AND (employee.`valid_from` BETWEEN company_org_group1.`valid_from` AND IFNULL(company_org_group1.`valid_to`, '$defaultEnd'))
												AND company_org_group1.`status` = $published
								";
						}

						if (EmployeeGroupConfig::isActiveGroup('company_org_group2_id')) {
							$defaultTabSql .= EmployeeGroup::getLeftJoinSQL("company_org_group2_id","employee_contract",$searchDate);
							$defaultTabSql .= " 
							LEFT JOIN
							`company_org_group2` ON
								company_org_group2.`company_org_group_id` = employee_group_company_org_group2_id.`group_value`
									AND (employee_group_company_org_group2_id.`valid_from` BETWEEN company_org_group2.`valid_from` AND IFNULL(company_org_group2.`valid_to`, '$defaultEnd'))
										AND company_org_group2.`status` = $published";
						}else{
								$defaultTabSql .= "
								LEFT JOIN
									`company_org_group2` ON
										company_org_group2.`company_org_group_id` = employee.`company_org_group2_id`
											AND (employee.`valid_from` BETWEEN company_org_group2.`valid_from` AND IFNULL(company_org_group2.`valid_to`, '$defaultEnd'))
												AND company_org_group2.`status` = $published
								";
						}

						if (EmployeeGroupConfig::isActiveGroup('company_org_group3_id')) {
							$defaultTabSql .= EmployeeGroup::getLeftJoinSQL("company_org_group3_id","employee_contract",$searchDate);
							$defaultTabSql .= " 
							LEFT JOIN
							`company_org_group3` ON
								company_org_group3.`company_org_group_id` = employee_group_company_org_group3_id.`group_value`
									AND (employee_group_company_org_group3_id.`valid_from` BETWEEN company_org_group3.`valid_from` AND IFNULL(company_org_group3.`valid_to`, '$defaultEnd'))
										AND company_org_group3.`status` = $published
							";
						}else{
								$defaultTabSql .= "
								LEFT JOIN
								`company_org_group3` ON
									company_org_group3.`company_org_group_id` = employee.`company_org_group3_id`
										AND (employee.`valid_from` BETWEEN company_org_group3.`valid_from` AND IFNULL(company_org_group3.`valid_to`, '$defaultEnd'))
											AND company_org_group3.`status` = $published
								";
						}
						$defaultTabSql .= "
						
						LEFT JOIN
							employee_group ON
								employee_group.employee_contract_id = employee_contract.employee_contract_id
									AND ('$searchDate' BETWEEN employee_group.`valid_from` AND IFNULL(employee_group.`valid_to`, '$defaultEnd'))
									AND employee_group.status = $published";

									if( !is_null($groupID) && !empty($groupID) ){
										$defaultTabSql .= " AND employee_group.group_id = '" . $groupID . "'";
									}

						$defaultTabSql .= "
						LEFT JOIN
							employee_cost ON
								employee_cost.`employee_contract_id` = employee_contract.`employee_contract_id`
									AND ('$searchDate' BETWEEN employee_cost.`valid_from` AND IFNULL(employee_cost.`valid_to`, '$defaultEnd'))
									AND employee_cost.`status` = $published
						LEFT JOIN
							employee_base_absence ON
								employee_base_absence.`employee_contract_id` = employee_contract.`employee_contract_id`
									AND ('$searchDate' BETWEEN employee_base_absence.`valid_from` AND IFNULL(employee_base_absence.`valid_to`, '$defaultEnd'))
									AND employee_base_absence.`status` = $published
						LEFT JOIN
							employee_card ON
								employee_card.`employee_contract_id` = employee_contract.`employee_contract_id`
									AND ('$searchDate' BETWEEN employee_card.`valid_from` AND IFNULL(employee_card.`valid_to`, '$defaultEnd'))
									AND employee_card.`status` = $published
						LEFT JOIN
							employee_ext ON
								employee_ext.`employee_id` = employee.`employee_id`
									AND ('$searchDate' BETWEEN employee_ext.`valid_from` AND IFNULL(employee_ext.`valid_to`, '$defaultEnd'))
									AND employee_ext.`status` = $published
						LEFT JOIN
							employee_ext2 ON
								employee_ext2.`employee_id` = employee.`employee_id`
									AND ('$searchDate' BETWEEN employee_ext2.`valid_from` AND IFNULL(employee_ext2.`valid_to`, '$defaultEnd'))
									AND employee_ext2.`status` = $published
						LEFT JOIN
							employee_ext3 ON
								employee_ext3.`employee_id` = employee.`employee_id`
									AND ('$searchDate' BETWEEN employee_ext3.`valid_from` AND IFNULL(employee_ext3.`valid_to`, '$defaultEnd'))
									AND employee_ext3.`status` = $published
						LEFT JOIN
							employee_ext4 ON
								employee_ext4.`employee_id` = employee.`employee_id`
									AND ('$searchDate' BETWEEN employee_ext4.`valid_from` AND IFNULL(employee_ext4.`valid_to`, '$defaultEnd'))
									AND employee_ext4.`status` = $published
						LEFT JOIN
							employee_docs ON
								employee_docs.`employee_id` = employee.`employee_id`
									AND ('$searchDate' BETWEEN employee_docs.`valid_from` AND IFNULL(employee_docs.`valid_to`, '$defaultEnd'))
									AND employee_docs.`status` = $published
						LEFT JOIN
							employee_address ON
								employee_address.`employee_id` = employee.`employee_id`
									AND ('$searchDate' BETWEEN employee_address.`valid_from` AND IFNULL(employee_address.`valid_to`, '$defaultEnd'))
									AND employee_address.`status` = $published
						LEFT JOIN
							employee_salary ON
								employee_salary.`employee_contract_id` = employee_contract.`employee_contract_id`
									AND ('$searchDate' BETWEEN employee_salary.`valid_from` AND IFNULL(employee_salary.`valid_to`, '$defaultEnd'))
									AND employee_salary.`status` = $published
						WHERE
							employee.`employee_id` = '{row_id_p1}'
								AND employee.`status` = $published
								AND '$searchDate' BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`, '$defaultEnd')
						ORDER BY
							employee.`valid_from` DESC, employee.`valid_to` DESC
					";

		$defaultPrimaryKey = 'row_id';
		$defaultModelName = null;

		$wizards = [];
		/*$wizards["dhtmlxGrid"][] = [
			"contentId"       => "dhtmlxGrid",
			"contentTitle"    => Dict::getValue('tab_employeetabs_employee'),
			"tabSQL"          => $defaultTabSql,
			"modelName"       => $defaultModelName,
			"primaryKey"      => $defaultPrimaryKey,
			'loadDataFromSQL' => 1,
		];*/
		$wizards["dhtmlxGrid"] = [];

		$wizardTabs = ColumnTabs::getTabs('tab_id');

		$tabs = [];

		foreach ( $wizardTabs as $wKey => $wValue ){
			if( !isset($wValue['tabSQL']) || (isset($wValue['tabSQL']) && empty($wValue['tabSQL'])) ){
				$tabs[$wKey]['tabSQL'] = $defaultTabSql;
			}

			if( !isset($wValue['modelName']) || (isset($wValue['modelName']) && empty($wValue['modelName'])) ){
				$tabs[$wKey]['modelName'] = $defaultModelName;
			}

			if( !isset($wValue['primaryKey']) || (isset($wValue['primaryKey']) && empty($wValue['primaryKey'])) ){
				$tabs[$wKey]['primaryKey'] = $defaultPrimaryKey;
			}

			$tabs[$wKey]['contentId'] = $wValue['contentId'];
			$tabs[$wKey]['contentTitle'] = $wValue['contentTitle'];
			$tabs[$wKey]['loadDataFromSQL'] = 1;
		}

		$wizards['dhtmlxGrid'] = array_merge($wizards['dhtmlxGrid'], $tabs);

		return $wizards;
	}
}