<?php
use Components\Core\Enum\ApproverProcessIdEnum;

Yang::loadComponentNamespaces('Core');

ini_set('max_execution_time', 3600);
ini_set('memory_limit', '2048M');

/**
 * Employee report to show all employee data that the user has access to.
 * The report can run on the active employees on the filtered day OR the employees that already quit OR both.
 *
 */
class EmployeeReportController extends Grid2Controller
{
	//Declaration of class variables and const
	private $published = Status::PUBLISHED; //active status
	public const EMPTY_SQL = "empty_sql"; 			//empty select

	private $employeeIds; 					//string of employee_ids
	private $employeeContractIds; 			//string of employee_contract_ids
	private $validFrom;						//filterDate $validFrom
	private $validTo;						//filterDate $validTo
	private $columns; 						//Grid columns and their attributes
	private $lang; 							//language
	private $isOnlyQuitEmployees = false; 	//true = filter data only for quit Employees
	private bool $displayEcEnd;				//munkaviszony megszűnésének okát és módját taglaló mezők megjelenítése
	private $groupList;						//list of group tables
	private $customerDbPatchName; 			//see AppSetting description
	private $separateData; 					//see AppSetting description
	private $customersPtrName; 				//see AppSetting description
	private $firstContractStart; 			//see AppSetting description
	private $defaultEnd; 					//see AppSetting description
	private $isCertificateIdShown; 			//see AppSetting description
	private $orderBy; 						//see AppSetting description
	private $firstContractField; 			//see AppSetting description


	//Calling of construtor
	public function __construct()
	{
		parent::__construct("employeeReport");
		parent::enableLAGrid();

        $this->processID = $this->processID ?? [ApproverProcessIdEnum::EMPLOYEE_MANAGEMENT];

		$this->defaultEnd 			= App::getSetting("defaultEnd");
		$this->lang = Dict::getLang();
		$this->isCertificateIdShown = App::getSetting("employeeControl_show_certificate_id");
		$this->orderBy = (App::getSetting("employeeReportOrderBy")) ? App::getSetting("employeeReportOrderBy") : "fullname";
		$this->firstContractStart 	= (int)App::getSetting("employeeReportShowOriginalContractStart");
		$this->firstContractField     = App::getSetting("firstContractFieldEmployeeReport");
		$this->customerDbPatchName  = Yang::getParam("customerDbPatchName");
		$this->customersPtrName     = App::getSetting("ptr_customer");
		$this->separateData         = App::getSetting("employeereportSeparateData") == "1" ? true : false;
		$this->groupList			= [
										"unit",
										"company_org_group1",
										"company_org_group2",
										"company_org_group3",
                                        "employee_position",
										"workgroup",
										"cost_center",
										"cost"
		];
		$this->displayEcEnd = App::getSetting("ec_end_on_employeecontracttab") === '1';

		//columns configuration
		$this->configGridColumns();

		$this->maxDays = 365;
	}

	/**
	 * Grid2 initialization, used mode: Array
	 */
	protected function G2BInit()
	{
        parent::setControllerPageTitleId("page_title_employee_report");
		parent::setExportFileName(Dict::getValue("page_title_employee_report"));

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);

		$this->LAGridRights->overrideInitRights("export_xls",		false);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);
		$this->LAGridRights->overrideInitRights("export_pdf_node",	false);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);

		parent::setGridProperty("splitColumnEnabled",    true);
		if ($this->customerDbPatchName == "schenker") {
			$cols = $this->separateData ? 4 : 3;
		} elseif ($this->customersPtrName == "BOS" || $cols = $this->separateData) {
			$cols = 2;
		} else {
			$cols = 1;
		}
		parent::setGridProperty("splitColumn", $cols);

		$this->LAGridDB->enableArrMode();
		parent::G2BInit();
	}

	/**
	 * Search definition - processId: employeeManagement
	 *
	 * $quit_employee["id"] = "no" = only show active Employees,
	 * $quit_employee["id"] = "just_yes" = only show quit Employees,
	 * $quit_employee["id"] = "yes" = show both active and quit Employees
	 * @return array
	 */
	public function search()
	{
		$quit_employees = [
				["id" => "no", "value"		=> Dict::getValue("no")],
				["id" => "yes","value"		=> Dict::getValue("yes")],
				["id" => "just_yes","value" => Dict::getValue("just_quit_employees")]
		];
		$quit_employees_search = [
			"quit_employees" => [
									"col_type"		=> "combo",
									"columnId"		=> "quit_employees",
									"width"			=> "*",
									"label_text"	=> Dict::getModuleValue("ttwa-base","quited_employees_too"),
									"options"		=> ["mode"	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,"array"	=> $quit_employees],
									"onchange" 		=> ["employee_contract"],
									"default_value"	=> "no"
								]
		];
        
        $GetActiveEmployeeData = new GetActiveEmployeeData_WithoutCalendar(
            [
                'controllerId'	=> $this->getControllerID(),
                'search'		=>  requestParam('searchInput'),
                'nameFilter'	=> true
            ],
            ['processId' => $this->processID],
            ['employee','employee_contract','company_org_group1','company_org_group2'],
            "employee_contract.`employee_contract_id`",
            false,
            $this->getControllerID()
        );
        $getEmployeeSQL = $GetActiveEmployeeData->getSQL();

        $validEmployeesSQL = "";
        $validEmployeesSQLSelect = "
            SELECT
                `employee_contract`.`employee_contract_id` AS `id`,
                " . Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]) . " AS `value`,
                " . '1 as valid_to' ."
                " . substr($getEmployeeSQL,strpos($getEmployeeSQL,"FROM"));
        $groupByPosition = strpos($validEmployeesSQLSelect, "GROUP BY");
        $beforGroupBy = substr($validEmployeesSQLSelect, 0, $groupByPosition);
        $validEmployeesSQL .= $beforGroupBy . 
            "
                AND ('{quit_employees}' = 'no' 
                OR '{quit_employees}' = 'yes')
                GROUP BY employee_contract.`employee_contract_id`
            ";

        $quitEmployeesSQL = "";
        $quitEmployeesSQLSelect = "
				SELECT *
				 FROM (SELECT
					`employee_contract`.`employee_contract_id` AS `id`,
					" . Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]) . " AS `value`,
					" .  "MAX(LEAST(IFNULL(employee.valid_to,'{$this->defaultEnd}'),IFNULL(employee_contract.valid_to,'{$this->defaultEnd}'),IFNULL(employee_contract.ec_valid_to,'{$this->defaultEnd}'))) as valid_to" . "
				    " . substr($getEmployeeSQL,strpos($getEmployeeSQL,"FROM"));
        $wherePosition = strpos($quitEmployeesSQLSelect, "WHERE");
        $beforeWhere = substr($quitEmployeesSQLSelect, 0, $wherePosition);
        $quitEmployeesSQL .= $beforeWhere . 
            "
               AND employee.status = {$this->published}
               AND employee_contract.row_id IS NOT NULL
               GROUP BY employee_contract.`employee_contract_id`
               HAVING valid_to<'{valid_date}'
               ) as subquery
            WHERE 
               `value` LIKE '%%{search}%%'
               AND ('{quit_employees}' = 'yes' 
               OR'{quit_employees}' = 'just_yes')
            ";
        
        $employeeName =
            [
                'employee_contract'	=>
                    [
                        'col_type'		=> 'auto',
                        'multiple'		=> 0,
                        'options'		=> [
                            'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
                            'sql'	=> $validEmployeesSQL .
                                       "UNION" .
                                       $quitEmployeesSQL .
                                       "ORDER BY `value`"
                        ],
                        'label_text'	=> Dict::getValue("name")
                    ]
            ];
        
		return Yang::arrayMerge($quit_employees_search, $this->getPreDefinedSearchFromDb("employeeManagement"), $employeeName);
	}

	/**
	 * Configuration of Grid columns and related table query.
	 *
	 * columns[column_name]:
	 * table: the table the column is queried from, mandatory
	 * select: added to select the field value, optional - as default the column_name is used in the query
	 * dictionary: [0] = dictionary id, [1] = module name, optional - as default the column_name  is usedas dictionary id
	 * width: width of column, optional (default = 150)
	 * align: alignment of column, optional
	 * type: type of column, optional (default = "ro")
	 * optionName: in case type = combo, option name to search in AppLookup, optional
	 * class: css class of column, optional
	 * export_as: excel export type
	 * export_format: excel export format
	 *
	 * Configuration is saved in $columns class variable.
	 */
	public function configGridColumns()
	{
		$this->columns = [];

		if ($this->customersPtrName == "BOS"  || ($this->separateData && $this->customerDbPatchName != "teesztergom")) {
			$this->columns += [
				"emp_id" 			=> ["table" => "employee", "export_as" => Yang::customerDbPatchName() == "mchale" ? 'numeric' : 'string']
			];
		}
			$this->columns += [
				"fullname" 			=> ["width" => 300, "table" => "employee", "select" => Employee::getParam("fullname", "employee")." AS fullname", "dictionary" => ["name"]]
			];
		//DEV-11265
		if ($this->customerDbPatchName == "teesztergom") {
			$this->columns += [
				"emp_id" 			=> ["table" => "employee"]
			];
		}
		//DEV-13247
		if ($this->customerDbPatchName == "schenker") {
			$this->columns += [
				"last_name" 		=> ["width" => 130, "table" => "employee"],
				"first_name"		=> ["width" => 130, "table" => "employee"]
			];
		}
			$this->columns += [
				"username" 		 	=> ["table" => "user"],
				"email"			 	=> ["table" => "user"],
				"rolegroup_name" 	=> ["table" => "user", "dictionary" => ["rolegroup"]]
			];

		if (App::hasRight("employee/employeeTab", "view")) {
			$this->columns += [
				"company_name" 				=> ["table" => "company", "dictionary" => ["company"]],
				"payroll_name" 				=> ["table" => "payroll", "dictionary" => ["Payroll"]],
				"unit_name"	   				=> ["table"	=> "unit", "dictionary" => ["unit", "ttwa-base"]],
				"company_org_group1_name" 	=> ["table"	=> "company_org_group1", "dictionary" => ["company_org_group1"], "select" => "company_org_group1.company_org_group_name as company_org_group1_name"],
				"company_org_group2_name" 	=> ["table"	=> "company_org_group2", "dictionary" => ["company_org_group2"], "select" => "company_org_group2.company_org_group_name as company_org_group2_name"],
				"company_org_group3_name" 	=> ["table"	=> "company_org_group3", "dictionary" => ["company_org_group3"], "select" => "company_org_group3.company_org_group_name as company_org_group3_name"],
				"vf" 						=> ["table" => "employee_contract", "dictionary" => ["valid_from"], "select" => "GREATEST(employee.valid_from,employee_contract.valid_from) AS vf"],
				"vt" 						=> ["table" => "employee_contract", "dictionary" => ["valid_to"], "select" => "LEAST(IFNULL(MAX(employee.valid_to),'{$this->defaultEnd}'),	IFNULL(MAX(employee_contract.valid_to),'{$this->defaultEnd}')) AS vt"],
				"nameofbirth" 				=> ["table" => "employee"]

			];
			if ($this->customersPtrName != "SAGA") {
				$this->columns += [
				"tax_number"  				=> ["table" => "employee", "select" => "employee.tax_number AS tax_number" ]
				];
			}
			$this->columns += [
				"gender"	  				=> ["table" => "employee", "colType" => "combo", "optionName" => "gender", "class" => "dialogTitle"]
			];
			if ($this->displayEcEnd) {
				$this->columns += [
					"ec_end_reason"  		=> ["width" => 200, "table" => "employee_contract", "select" => "IFNULL(`employee_contract`.`ec_end_reason`, '') AS `ec_end_reason`"],
					"ec_end_type"			=> ["width" => 200, "table" => "employee_contract", "select" => "MAX(IFNULL(`d_ec`.`dict_value`, IFNULL(`employee_contract`.`ec_end_type`, ''))) AS `ec_end_type`"]
				];
			}
			if (in_array($this->customerDbPatchName, ["rosenberger", "danubius"])) {
				$this->columns += [
				"employee_contract_note"  	=> ["table" => "employee_contract", "dictionary" => ["note"], "select" => "employee_contract.note AS employee_contract_note"]
				];
			}
			$this->columns += [
				"workgroup_name"			=> ["table" => "workgroup", "dictionary" => ["workgroup", "ttwa-base"]]
			];
			if ($this->firstContractStart) {
				$this->columns += [
				"first_ec_valid_from"		=> ["table" => "employee", "select" => "firstcontract.first_ec_valid_from"]
				];
			}
			$this->columns += [
				"ec_valid_from"				=> ["table" => "lastValidEc"],
				"ec_valid_to"				=> ["table" => "lastValidEc"],
				"employee_contract_number" 	=> ["table" => "employee_contract"],
				"employee_contract_type" 	=> ["table" => "employee_contract", "colType" => "combo", "optionName" => "employee_contract_type"],
				"wage_type" 				=> ["table" => "employee_contract", "colType" => "combo", "optionName" => "wage_type"]
            ];
            if ($this->customerDbPatchName !== "hopi") {
                $this->columns += [
                    "employee_position_name"=> ["table" => "employee_position"]
                ];
            }
			$this->columns += [
                "daily_worktime"            => ["table" => "employee_contract", "export_as" => "decimal", "export_format" => "0.0"]
            ];
		}

		if (App::hasRight("employee/employeeSalaryTab", "view")) {
			$this->columns += [
				"personal_month_salary"		=> ["table" => "employee_salary"],
				"personal_hour_salary"		=> ["table" => "employee_salary"],
				"shift"						=> ["table" => "employee_salary", "select" => "IF(employee_salary.shift=0,'".Dict::getValue("no")."','".Dict::getValue("yes")."') as shift"],
				"shift_bonus_in_percent"	=> ["table" => "employee_salary"]
			];

			for ($i=1; $i<=EmployeeSalary::OPTION_PIECES; ++$i)
			{
				$type = OptionConfig::getType("es_option$i");
				if ($type === null) { continue; }
				if ($type == "combo") {
					$this->columns += [
						"es_option" . $i =>  ["table" => "employee_salary", "colType" => "combo", "optionName" => "es_option" . $i]
					];
				} else {
					$this->columns += [
						"es_option" . $i => ["table" => "employee_salary"]
					];
				}
			}
		}

		if (App::hasRight("employee/employeeCafeteriaTab", "view")) {
			$this->columns += [
				"cafeteria_id"				=> ["table" => "employee_cafeteria", "select" => "caf.cafeteria_name as cafeteria_id"]
			];
		}

		if (App::hasRight("employee/employeeAddressTab", "view")) {
			$employee_address=App::getSetting("employee_address");
			if ($employee_address===EmployeeAddress::SETTING_ALL || $employee_address===EmployeeAddress::SETTING_FULL) {
				$this->columns += [
					"full_address"			=> ["table" => "employee_address", "width" => 250, "align"=>"center"]
				];
			}
			if ($employee_address===EmployeeAddress::SETTING_ALL || $employee_address===EmployeeAddress::SETTING_PIECE) {
				$this->columns += [
					"zip_code"				=> ["table" => "employee_address", "width" => 200, "align"=>"center"],
					"city"					=> ["table" => "employee_address", "width" => 250, "align"=>"center"],
					"district"				=> ["table" => "employee_address", "width" => 200, "align"=>"center"],
					"public_place_name"		=> ["table" => "employee_address", "width" => 250, "align"=>"center"],
					"public_place_type"		=> ["table" => "employee_address", "width" => 200, "align"=>"center"],
					"house_number"			=> ["table" => "employee_address", "width" => 200, "align"=>"center"],
                    "building"			    => ["table" => "employee_address", "width" => 200, "align"=>"center"],
                    "staircase"			    => ["table" => "employee_address", "width" => 200, "align"=>"center"],
					"floor"					=> ["table" => "employee_address", "width" => 200, "align"=>"center"],
					"door"					=> ["table" => "employee_address", "width" => 200, "align"=>"center"]
				];
			}
			if ($employee_address===EmployeeAddress::SETTING_ALL || $employee_address===EmployeeAddress::SETTING_FULL) {
				$this->columns += [
					"res_full_address"		=> ["table" => "employee_address", "width" => 250, "align"=>"center"]
				];
			}
			if ($employee_address===EmployeeAddress::SETTING_ALL || $employee_address===EmployeeAddress::SETTING_PIECE) {
				$this->columns += [
					"res_zip_code"			=> ["table" => "employee_address", "width" => 200, "align"=>"center"],
					"res_city"				=> ["table" => "employee_address", "width" => 250, "align"=>"center"],
					"res_district"			=> ["table" => "employee_address", "width" => 200, "align"=>"center"],
					"res_public_place_name"	=> ["table" => "employee_address", "width" => 250, "align"=>"center"],
					"res_public_place_type"	=> ["table" => "employee_address", "width" => 200, "align"=>"center"],
					"res_house_number"		=> ["table" => "employee_address", "width" => 200, "align"=>"center"],
                    "res_building"		    => ["table" => "employee_address", "width" => 200, "align"=>"center"],
                    "res_staircase"		    => ["table" => "employee_address", "width" => 200, "align"=>"center"],
					"res_floor"				=> ["table" => "employee_address", "width" => 200, "align"=>"center"],
					"res_door"				=> ["table" => "employee_address", "width" => 200, "align"=>"center"]
				];
			}
		}

		if (App::hasRight("employee/employeeExtTab", "view")) {
			$this->columns += [
				"place_of_birth"			=> ["table" => "employee_ext"],
				"date_of_birth"				=> ["table" => "employee_ext"],
				"mothers_name"				=> ["table" => "employee_ext"],
				"ssn"						=> ["table" => "employee_ext"],
				"personal_id_card_number"	=> ["table" => "employee_ext"],
				"passport_number"			=> ["table" => "employee_ext"]
			];
			for ($i=1; $i<=EmployeeExt::OPTION_PIECES; ++$i)
			{
				$type = OptionConfig::getType("option$i");
				if ($type === null) { continue; }
				if ($type == "combo") {
					$this->columns += [
						"option" . $i =>  ["table" => "employee_ext", "colType" => "combo", "optionName" => "option" . $i]
					];
				} else {
					$this->columns += [
						"option" . $i => ["table" => "employee_ext"]
					];
				}
			}
			$this->columns += [
				"employee_ext_note"  	=> ["table" => "employee_ext", "dictionary" => ["note"], "select" => "employee_ext.note AS employee_ext_note"]
			];
		}
		for ($t=2; $t<=6; ++$t)
		{
			if (App::hasRight("employee/employeeExt".$t."Tab", "view")) {
				$className = "EmployeeExt$t";
				for ($i=1; $i<=$className::OPTION_PIECES; ++$i)
				{
					$type = OptionConfig::getType("ext".$t."_option$i");
					if ($type === null) { continue; }
					if ($type == "combo") {
						$this->columns += [
							"ext".$t."_option".$i 		=>  ["table" => "employee_ext".$t, "colType" => "combo", "optionName" => "ext".$t."_option".$i]
						];
					} else {
						$this->columns += [
							"ext".$t."_option".$i 		=>  ["table" => "employee_ext".$t]
						];
					}
				}
				if ($t === 2 && $this->customerDbPatchName === "kedrion") {
					$this->columns += [
						"employee_ext".$t."_valid_from"	=>  ["table" => "employee_ext$t", "colType" => "ed", "optionName" => "ext".$t."_option".$i, "select" => "employee_ext$t.valid_from AS employee_ext2_valid_from"],
						"employee_ext".$t."_valid_to"	=>  ["table" => "employee_ext".$t, "colType" => "ed", "optionName" => "ext".$t."_option".$i, "select" => "employee_ext$t.valid_to AS employee_ext2_valid_to"],
					];
				}
			}
		}

		if (App::hasRight("employee/employeeDocsTab", "view") && $this->isCertificateIdShown) {
			$this->columns += [
				"certificate_id"	=> ["table" => "employee_docs"],
				"institution_name" 	=> ["table" => "employee_docs"]
			];
		}

		if (App::hasRight("employee/employeeCardTab", "view")) {
			if ((int)App::getSetting("employee_use_access_level")) {
				$this->columns += [
					"card" => ["width" => 250, "table" => "employee_card", "select" => "GROUP_CONCAT(DISTINCT CONCAT(employee_card.card, IFNULL(CONCAT(' (',ac_access_level.ac_access_level_name,')'), '')) SEPARATOR ', ') as card"]
				];
			} else {
				$this->columns += [
					"card" => ["width" => 250, "table" => "employee_card", "select" => "GROUP_CONCAT(DISTINCT employee_card.card SEPARATOR ', ') as card"]
				];
			}
			$this->columns += [
				"card_note" 		=> ["table" => "employee_card", "select" => "GROUP_CONCAT(DISTINCT employee_card.note SEPARATOR ', ') as card_note"]
			];
		}

		if (App::hasRight("employee/employeeCostTab", "view")) {
			$this->columns += [
				"cost_name"			=> ["table" => "cost", "dictionary" => ["cost"]],
				"cost_center_name" 	=> ["table" => "cost_center", "dictionary" => App::getSetting("akh_tab_profitcentrum") > 0 ? ["akh_tab_employeetabs_employeecost"] : ["costcenter"],]
			];
		}

		$modules = Yang::getModules();

		if (in_array("ttwa-ahp-core", $modules) && App::hasRight("employee/employeeBaseAbsenceTab", "view")) {
			$activeBaseAbsenceTypes = $this->getBaseAbsenceTypes();
            $absenceField = ((int)App::getSetting("showAbsencesInHours") === 1) ? "quantity_hour" : "quantity";
			foreach ($activeBaseAbsenceTypes as $index=>$baseAbsence)
			{
				$this->columns += [
					$baseAbsence["dict_id"]	=> ["table" => "employee_base_absence", "align" => "center", "select" => ($index === 0) ? "employee_base_absence.$absenceField as quantity, base_absence_type.dict_id" : self::EMPTY_SQL]
				];
			}
		}

		if (in_array("ttwa-wfm", $modules) && App::hasRight("employee/employeeWorkActivityTab", "view")) {
			$this->columns += [
				"wa_employee_contract_id"	 => ["table" => "employee_work_activity", "dictionary" => ["employee_contract_id"], "select" => "employee_work_activity.employee_contract_id AS wa_employee_contract_id"],
				"work_activity_name" 		 => ["table" => "employee_work_activity"]
			];
		}

		if (App::hasRight("employee/employeeTravelCostTab", "view"))
		{
			$this->columns += [
				"travel_cost_type" 			=> ["table" => "employee_travel_cost", "select" => "travel_cost_dict.dict_value AS travel_cost_type"],
				"travel_cost_length" 	  	=> ["table" => "employee_travel_cost", "select" => "employee_travel_cost.length AS travel_cost_length"],
				"reimbursement_rate" 		=> ["table" => "employee_travel_cost", "select" => "reimbursement_rate_dict.dict_value AS reimbursement_rate"],
				"reimbursement_percent" 	=> ["table" => "employee_travel_cost", "select" => "employee_travel_cost.reimbursement_percent AS reimbursement_percent"]
			];
		}
		//DEV-9352
		if ($this->customerDbPatchName === "sertec") {

			foreach (
				[   "gender"       , "nameofbirth",

					"option3"      , "option4"      , "option5"      , "option6"      , "option7"      , "option8"      , "option9"      , "option10",
					"ext2_option3" , "ext2_option4" , "ext2_option5" , "ext2_option6" , "ext2_option7" , "ext2_option8" , "ext2_option9" , "ext2_option10",
					"ext2_option13", "ext2_option12", "ext2_option14", "ext2_option15", "ext2_option16", "ext2_option11", "ext2_option17", "ext2_option18", "ext2_option30",
					"es_option1"   , "es_option2"   , "es_option3"   , "es_option4"   , "es_option5"   , "es_option6"   , "es_option7"   , "es_option8"   , "es_option9"   , "es_option10",
				] as $colKeyToRemove
			) {
				unset ($this->columns[$colKeyToRemove]);
			}
		}

		//DEV-11265
        if ($this->customerDbPatchName == "teesztergom") {
            // Ordered column names
            $order = [
                "fullname",
                "emp_id",
                "company_name",
                "unit_name",
                "workgroup_name",
                "card",
                "quantity",
                "ec_valid_from",
                "ec_valid_to",
                "username",
                "rolegroup_name",
                "company_org_group1_name",
                "company_org_group2_name",
                "company_org_group3_name",
                "vf",
                "vt",
                "employee_contract_type",
                "wage_type",
                "employee_position_name",
                "daily_worktime",
                "group_id",
                "group_value",
                "cost_name",
                "cost_center_name",
                "base_absence_type_name",
                "email",
                "payroll_name",
			];

            // Ordered columns
            $cols_ordered = [];

            // Fill ordered columns array
            foreach ($order as &$value) {
                if(isset($this->columns[$value])) {
                    $cols_ordered[$value] = $this->columns[$value];
                }
            }

            // Copy ordered columns array to columns array
            $this->columns = $cols_ordered;

            // Truncate ordered columns array
            unset($cols_ordered);
        }

	}

	/**
	 * Get Base Absence Types and Dictionaries
	 * @return array
	 */
	private function getBaseAbsenceTypes ()
	{
		$SQL = "SELECT
					base_absence_type_id, dict_id
				FROM
					base_absence_type
				WHERE
					status = {$this->published}
					";

		$array = dbFetchAll($SQL);

		return $array;
	}

	/**
	 * Grid column definition
	 * Configured in configGridColumns and formatted with getColDetails helper
	 * @return array
	 */
	public function columns()
	{
		$cols=[];
		foreach ($this->columns as $dataName => $dataAttributes) {
			 $cols[$dataName]= $this->getColDetails(
				!empty($dataAttributes["width"]) ? $dataAttributes["width"] : 150,
				!empty($dataAttributes["colType"]) ? $dataAttributes["colType"] : "ro",
				!empty($dataAttributes["optionName"]) ? $dataAttributes["optionName"] : "",
				!empty($dataAttributes["class"]) ? $dataAttributes["class"] : "",
				!empty($dataAttributes["align"]) ? ($dataAttributes["align"]) : "",
				!empty($dataAttributes["export_as"]) ? $dataAttributes["export_as"] : "",
				!empty($dataAttributes["export_format"]) ? $dataAttributes["export_format"] : ""
			);
		}

		return $cols;
	}

	/**
	 * Grid2 column defintion helper
	 * @param int $width
     * @param string $colType
	 * @param string $optionName
     * @param string $class
	 * @param string $align
	 * @param string $export_as
	 * @param string $export_format
	 * @return array
	 */
	private function getColDetails($width, $colType, $optionName, $class, $align, $export_as, $export_format)
	{
		$col["export"]			= true;
		$col["report_width"]	= 20;
		$col["edit"]			= false;
		$col["width"]			= $width;
		$col["col_type"]		= $colType;
		if ($colType = "combo")	{
			$col["options"] = [
				"mode"=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,
				"array"=>App::getLookup($optionName)];
		}
		if(!empty($class)) {
			$col["class"]	  	= $class;
		}
		if(!empty($align)) {
			$col["align"]	= $align;
		}
		if(!empty($export_as)) {
			$col["export_as"]	= $export_as;
		}
		if(!empty($export_format)) {
			$col["export_format"]	= $export_format;
		}

		return $col;
	}

	/**
	 * Definition of column labels
	 *
	 * If dictionary_id != column_name, then configured in configGridColumns:
	 * 	  columns["dictionary"][0] =  dictionary_id, columns["dictionary"][1] = module name
	 * @return array
	 */
	public function attributeLabels()
	{
		$result=[];
		foreach ($this->columns as $dataName => $dataAttributes) {
				if (!empty($dataAttributes["dictionary"])) {
					if (!empty($dataAttributes["dictionary"][1])) {
						$result[$dataName] = Dict::getModuleValue($dataAttributes["dictionary"][1], $dataAttributes["dictionary"][0]);
					} else {
						$result[$dataName] = Dict::getValue($dataAttributes["dictionary"][0]);
					}
				} else {
					$result[$dataName] = Dict::getValue($dataName);
				}
		}

		switch ($this->customerDbPatchName) {
			//DEV-12195
            case "kedrion":
                $result["employee_ext2_valid_from"] = Dict::getValue("option1") . " - " . Dict::getValue("valid_from");
                $result["employee_ext2_valid_to"]   = Dict::getValue("option1") . " - " . Dict::getValue("valid_to");
                break;
		}

		return $result;
	}

	/**
	 * Grid data to show for Grid2
	 * @param string $gridID
	 * @param array $filter
	 * @return array
	 */
    protected function dataArray($gridID, $filter)
    {
		if (!empty($filter["valid_from"]) && !empty($filter["valid_to"])) {
			$this->validFrom = $filter["valid_from"];
			$this->validTo = $filter["valid_to"];
		} else {
			$this->validFrom = $this->validTo =  $filter["valid_date"];
		}

		//$quit_employee["id"] = "yes" => show both active and quit Employees,
		//so first get data for active Employees
		if($filter["quit_employees"]==="no" || $filter["quit_employees"]==="yes" && !$this->isOnlyQuitEmployees) {
			$this->isOnlyQuitEmployees = false;
		} else {
			$this->isOnlyQuitEmployees = true;
		}

		//get Employees base (employee and employee_contract) data
		$filteredEmployees = $this->getEmployeesAndContractData($filter);

		$employeIdArray = array_column($filteredEmployees, "employee_id");
		$employeContractIdArray = array_column($filteredEmployees, "employee_contract_id");
		$this->employeeIds = join("','", $employeIdArray);
		$this->employeeContractIds = join("','", $employeContractIdArray);

		//collect tables need to be queried from $this->columns
		$tableArray = [];
		$tableArray = array_column($this->columns,"table", "table");
		unset($tableArray["employee"],$tableArray["employee_contract"],$tableArray["lastValidEc"]);

		//collect all data to show on Grid in $result
		$result = $filteredEmployees;

		//query all table from $tableArray, and add to $result
		//number of Employees, and data order is not changing, it only adds extra data
		foreach ($tableArray as $table)
		{
			//check if $table has data at all
			$SQL = "SELECT * FROM $table LIMIT 1";
			$hasTableData = dbFetch($SQL);

			if (count($hasTableData) > 0)
			{
				//calling functions to get and order tables data
				$configurationOfTableSQL = $this->configureTableSQL($table);
				$resultOfSQLArray = dbFetchAll($this->generateTableSQL($table, ...$configurationOfTableSQL));
				$indexedResult= $this->addIndexToSQLResult($resultOfSQLArray, $table);

				//add query results to $result
				//check if index employee_id or employee_contract_id
                //if quit employees, use always contract as index to show data with history
				if ($configurationOfTableSQL[0] === "employee" && !$this->isOnlyQuitEmployees) {
					foreach ($employeIdArray as $index=>$id) {
						$result[$index] += $indexedResult[$id] ?? [];
					}
				} elseif ($configurationOfTableSQL[0] === "employee_contract" || $configurationOfTableSQL[0] === "employee_cost" || ($this->isOnlyQuitEmployees && $configurationOfTableSQL[0] === "employee")) {
					foreach ($employeContractIdArray as $index=>$id) {
						$result[$index] += $indexedResult[$id] ?? [];
					}
				}
			}
		}

		//if ["quit_employees"]==="yes", then get data for quit Employees as well
		if($filter["quit_employees"]==="yes" && !$this->isOnlyQuitEmployees) {
			$this->isOnlyQuitEmployees = true;
			$resultActiveEmployees  = $result;
			$resultQuitEmployees    = $this->dataArray($gridID, $filter);
			$result 		   		= array_merge($resultActiveEmployees,$resultQuitEmployees);
		}

		return $result;
    }

	/**
	 * Function to query Employees data from employee and employee_contract tables
	 *
	 * If $this->isOnlyQuitEmployees = true, then query quit Employees
	 * If $this->isOnlyQuitEmployees = false, then query active Employees
	 * @return array
	 */
	protected function getEmployeesAndContractData($filter)
	{
		$gpf = new GetPreDefinedFilter(
			$this->getControllerID(),
			false,
			['company'=> "employee",'payroll'=> "employee"],
			false,
			$filter
		);
		$filterWhere = $gpf->getFilter();

		$art = new ApproverRelatedGroup;
		$gargSQL = $art->getApproverReleatedGroupSQLWithoutCalc("Employee", "employeeManagement", false, "AND", "CurrentDate", $this->getControllerID());

		//SELECT
		$select = $this->getSelectAttributes("employee");
		$select .= $this->getSelectAttributes("employee_contract");
        $select .= $this->getSelectAttributes("lastValidEc");
		$select .= ($this->isOnlyQuitEmployees) ? "MAX(LEAST(IFNULL(employee.valid_to,'{$this->defaultEnd}'),IFNULL(employee_contract.valid_to,'{$this->defaultEnd}'),IFNULL(employee_contract.ec_valid_to,'{$this->defaultEnd}'))) as valid_to," : "";
		$select = substr($select, 0, -1);

		$SQL="
		    WITH validContracts AS (
            SELECT 
                ec.employee_contract_id,
                ec.ec_valid_to,
                ec.ec_valid_from,
                ec.row_id,
                ROW_NUMBER() OVER (PARTITION BY ec.employee_contract_id ORDER BY ec.row_id DESC) as rn
            FROM employee_contract ec
            WHERE ec.status = 2
            )
			SELECT
				employee.employee_id,
				employee_contract.employee_contract_id,
				$select
				FROM employee
			";

		//LEFT JOIN
		if ($this->firstContractStart) {
			$SQL .=  "
				LEFT JOIN
				(
					SELECT
						MIN(ec.ec_valid_from) AS first_ec_valid_from,
						e.{$this->firstContractField} AS unique_id
					FROM employee e
					LEFT JOIN employee_contract ec ON
							ec.employee_id = e.employee_id
						AND ec.status = {$this->published}
						AND ec.valid_from <= IFNULL(e.valid_to, '{$this->defaultEnd}') AND e.valid_from <= IFNULL(ec.valid_to, '{$this->defaultEnd}')
						AND ec.ec_valid_from <= IFNULL(e.valid_to, '{$this->defaultEnd}') AND e.valid_from <= IFNULL(ec.ec_valid_to, '{$this->defaultEnd}')
					WHERE e.status = {$this->published}
					GROUP BY e.{$this->firstContractField}
				) AS firstcontract ON firstcontract.unique_id = employee.{$this->firstContractField}
			";
		}

		if ($this->isOnlyQuitEmployees) {
			$SQL .= " LEFT JOIN employee_contract ON
						employee_contract.employee_id = employee.employee_id
					AND employee_contract.status = {$this->published}
					AND employee.valid_from <= IFNULL(employee_contract.valid_to,'{$this->defaultEnd}')
					AND employee.valid_to >= employee_contract.valid_from
					AND employee.valid_from <= IFNULL(employee_contract.ec_valid_to,'{$this->defaultEnd}')
					AND employee.valid_to >= employee_contract.ec_valid_from
				";
		} else {
			$SQL .= "	LEFT JOIN employee_contract ON
						employee_contract.employee_id = employee.employee_id
					AND employee_contract.status = {$this->published}
					AND employee_contract.valid_from <=  '{$this->validTo}'
					AND IFNULL(employee_contract.valid_to, '{$this->defaultEnd}') >= '{$this->validFrom}'
					AND employee_contract.ec_valid_from <=  '{$this->validTo}'
					AND IFNULL(employee_contract.ec_valid_to, '{$this->defaultEnd}') >= '{$this->validFrom}'
					AND employee.valid_from <= IFNULL(employee_contract.valid_to, '{$this->defaultEnd}')
					AND employee_contract.valid_from <= IFNULL(employee.valid_to, '{$this->defaultEnd}')
					AND employee.valid_from <= IFNULL(employee_contract.ec_valid_to, '{$this->defaultEnd}')
					AND employee_contract.ec_valid_from <= IFNULL(employee.valid_to, '{$this->defaultEnd}')
					";
		}
        
        $SQL .= "
                LEFT JOIN validContracts lastValidEc ON
                    lastValidEc.employee_contract_id = employee_contract.employee_contract_id
                AND lastValidEc.rn = 1	
        ";

		if ($this->displayEcEnd) {
			$SQL .= "
				LEFT JOIN `app_lookup` `al_ec`
					ON  `al_ec`.`lookup_id`     = 'ec_end_type'
					AND `al_ec`.`lookup_value`  = `employee_contract`.`ec_end_type`
					AND `al_ec`.`valid`         = 1
				LEFT JOIN `dictionary` `d_ec`
					ON  `al_ec`.`dict_id`   = `d_ec`.`dict_id`
					AND `d_ec`.`lang`       = '{$this->lang}'
					AND `d_ec`.`valid`      = 1
			";
		}

		if (isset($filter['cost']) && !EmployeeGroupConfig::isActiveGroup('cost')) {
			$SQL .= "
					LEFT JOIN employee_cost ON
							employee_cost.employee_contract_id = employee_contract.employee_contract_id
					AND employee_cost.status= '{$this->published}'";
			if (!$this->isOnlyQuitEmployees) {
					$SQL .= "
						AND employee_cost.valid_from <= '{$this->validTo}'
						AND IFNULL(employee_cost.valid_to, '{$this->defaultEnd}') >= '{$this->validFrom}'
						";
			} else {
				$SQL .= "
						AND employee_contract.valid_from <= IFNULL(employee_cost.valid_to,'{$this->defaultEnd}') AND employee_contract.valid_to >= employee_cost.valid_from
						AND employee_contract.ec_valid_from <= IFNULL(employee_cost.valid_to,'{$this->defaultEnd}') AND employee_contract.ec_valid_to >= employee_cost.valid_from
						";
			}
		}

        $filter = array_merge($filter, ['company_org_group1' => '1', 'company_org_group2' => '2', 'company_org_group3' => '3']);

		foreach (array_keys($filter) as $filterName)
		{
			if (in_array($filterName, $this->groupList))
			{
				$SQL .= $this->getGroupJoinSQL($filterName, "");
			}
		}

		//gargSQL join
		if(isset($gargSQL["join"]))
		{
			$SQL .= $gargSQL["join"];
		}
		//WHERE
		if ($this->isOnlyQuitEmployees) {
			$SQL .= "
					WHERE
							employee.status= {$this->published}
						AND employee_contract.row_id IS NOT NULL
					";


		} else {
			$SQL .= "
					WHERE
							employee.status = {$this->published}
						AND employee_contract.employee_contract_id IS NOT NULL
						AND employee.valid_from <=  '{$this->validTo}'
						AND IFNULL(employee.valid_to, '{$this->defaultEnd}') >= '{$this->validFrom}'
					";
		}

		//gargSQL where
		if (isset($gargSQL["where"])) {
			$SQL .= $gargSQL["where"];
		}

		//filter where conditions
		$SQL .= "AND ".$filterWhere;

		//GROUPBY
		$SQL .= "
			GROUP BY employee_contract.employee_contract_id
			";

		//HAVING
		if ($this->isOnlyQuitEmployees)
		{
			$SQL .= "
			HAVING valid_to<'{$this->validFrom}'
			";
		}

		//ORDERBY
		$SQL .= "
			ORDER BY {$this->orderBy}
			";

		return dbFetchAll($SQL);
	}

	/**
	 * Configure variables used in generateTableSQL
	 * @param string $table
	 * @return array
	 */
	protected function configureTableSQL($table)
	{
		$isGroupTable = in_array($table, $this->groupList) ? true : false;  //group table or not
		$joinTable = ""; 													//joined table to get filtered employee rows
		$joinTableId = ""; 													//id that is used in join
		$filteredEmployeesId = ""; 											//id that identifies employee, added as last field to select and used as index later

		switch ($table) {
			case "company":
			case "payroll":
				$joinTable 	 = "employee";
				$joinTableId = $table."_id";
				$filteredEmployeesId = $joinTable."_id";
				break;
			case "employee_address":
			case "user":
			case "employee_ext":
			case "employee_ext2":
			case "employee_ext3":
			case "employee_ext4":
			case "employee_ext5":
			case "employee_ext6":
            case 'employee_ext7':
			case "employee_docs":
				$joinTable = "employee";
				$joinTableId = $joinTable."_id";
				$filteredEmployeesId = $joinTableId;
				break;
			case "employee_position":
				$joinTable 	 = "employee_contract";
				$joinTableId = $table."_id";
				$filteredEmployeesId = $joinTable."_id";
				break;
			case "employee_salary":
			case "employee_card":
			case "employee_base_absence":
			case "employee_work_activity":
			case "employee_travel_cost":
			case "employee_cafeteria":
				$joinTable 	 = "employee_contract";
				$joinTableId = $joinTable."_id";
				$filteredEmployeesId = $joinTableId;
				break;
			case "unit":
			case "company_org_group1":
			case "company_org_group2":
			case "company_org_group3":
				$joinTable 	 = "employee";
				$filteredEmployeesId = $joinTable."_id";
				break;
			case "workgroup":
				$joinTable 	 = "employee_contract";
				$filteredEmployeesId = $joinTable."_id";
				break;
			case "cost_center":
			case "cost":
				$joinTable 	 = "employee_cost";
				$filteredEmployeesId = "employee_contract_id";
				break;

		}

		return [$joinTable, $isGroupTable, $joinTableId, $filteredEmployeesId];

	}

	/**
	 * Generate SQL to query $table data
	 * @param string $table
	 * @param string $joinTable
	 * @param boolean $isGroupTable
	 * @param string $joinTableId
	 * @param string $filteredEmployeesId
	 * @return string
	 */
	public function generateTableSQL($table, $joinTable, $isGroupTable, $joinTableId, $filteredEmployeesId)
	{
		//SELECT
		$select = $this->getSelectAttributes($table);
        
        if ($this->isOnlyQuitEmployees && $joinTable === "employee") {
            $select .= "employee_contract.employee_contract_id";
        } else {
            $select .= "$joinTable.$filteredEmployeesId";
        }

		//JOINS
		$leftJoin ="";
		$rightJoin ="";
		$leftJoinEmployeeTableSQL = "
			LEFT JOIN employee_contract ON
				employee_contract.employee_id = employee.employee_id
			AND employee_contract.status = {$this->published}
			AND employee.valid_from <= IFNULL(employee_contract.valid_to,'{$this->defaultEnd}')
			AND IFNULL(employee.valid_to, '{$this->defaultEnd}') >= employee_contract.valid_from
		";
		
		$leftJoinEmployeeCostTableSQL = "
			LEFT JOIN employee_contract ON
				employee_contract.employee_contract_id = employee_cost.employee_contract_id
			AND employee_contract.status = {$this->published}
			AND employee_contract.valid_from <= IFNULL(employee_cost.valid_to,'{$this->defaultEnd}')
			AND employee_contract.valid_to >= employee_cost.valid_from
		";

		if ($this->isOnlyQuitEmployees) {
			if ($joinTable === "employee") {
				$leftJoin .= $leftJoinEmployeeTableSQL . "
							AND employee.valid_from <= IFNULL(employee_contract.ec_valid_to,'{$this->defaultEnd}')
							AND employee.valid_to >= employee_contract.ec_valid_from
							";
			} elseif ($joinTable === "employee_cost") {
				$leftJoin .= $leftJoinEmployeeCostTableSQL . "
							AND employee_contract.ec_valid_from <= IFNULL(employee_cost.valid_to,'{$this->defaultEnd}')
							AND employee_contract.ec_valid_to >= employee_cost.valid_from
							";
			}
		} elseif ($isGroupTable) {
			if ($joinTable === "employee") {
				$leftJoin .= $leftJoinEmployeeTableSQL . "
							AND employee_contract.valid_from <= '{$this->validTo}'
							AND IFNULL(employee_contract.valid_to, '2038-01-01') >= '{$this->validFrom}'
							";
			} elseif ($joinTable === "employee_cost") {
				$leftJoin .= $leftJoinEmployeeCostTableSQL . "
							AND employee_contract.valid_from <= '{$this->validTo}'
							AND IFNULL(employee_contract.valid_to, '2038-01-01') >= '{$this->validFrom}'
							";
			}
		}

		if (!$isGroupTable) {
			//right join to ensure query has the same number of result row as filtered employees
			$rightJoin .= "RIGHT JOIN $joinTable
								ON  $table.$joinTableId = $joinTable.$joinTableId
								AND $table.status = '$this->published'";
			if (!$this->isOnlyQuitEmployees)
			{
				$rightJoin .= "
							AND $table.valid_from <= '{$this->validTo}'
							AND IFNULL($table.valid_to, '{$this->defaultEnd}') >= '{$this->validFrom}'";
			}
		} else {
			$leftJoin .= $this->getGroupJoinSQL($table, $joinTable);
		}

		//section for extra joins
		if ($table === "user")
		{
			$leftJoin .= "LEFT JOIN auth_rolegroup ON
								auth_rolegroup.rolegroup_id = user.rolegroup_id
							AND auth_rolegroup.visibility = 1
						";
		}

		if ($table === "employee_base_absence")
		{
			$leftJoin .= "LEFT JOIN base_absence_type ON
								base_absence_type.base_absence_type_id = employee_base_absence.base_absence_type_id
							AND base_absence_type.status = {$this->published}
						 LEFT JOIN dictionary bat_dict ON
								bat_dict.dict_id = base_absence_type.dict_id
							AND bat_dict.lang = '{$this->lang}'
						";
		}

		if ($table === "employee_work_activity")
		{
			$leftJoin .= "LEFT JOIN work_activity ON
								work_activity.work_activity_id = employee_work_activity.work_activity_id
							AND work_activity.status = {$this->published}
						";
		}

		if ($table === "employee_travel_cost")
		{
			$leftJoin .= "LEFT JOIN app_lookup al_travel_cost ON
								al_travel_cost.lookup_value = employee_travel_cost.type
							AND al_travel_cost.valid = '1'
							AND al_travel_cost.lookup_id = 'travel_cost_type'
						LEFT JOIN dictionary travel_cost_dict ON
								travel_cost_dict.dict_id = al_travel_cost.dict_id
							AND travel_cost_dict.valid = '1'
							AND travel_cost_dict.lang = '{$this->lang}'
						LEFT JOIN app_lookup al_reimbursement_rate ON
								al_reimbursement_rate.lookup_value = employee_travel_cost.reimbursement_rate
							AND al_reimbursement_rate.valid = '1'
							AND al_reimbursement_rate.lookup_id = 'reimbursement_rate'
						LEFT JOIN dictionary reimbursement_rate_dict ON
								reimbursement_rate_dict.dict_id = al_reimbursement_rate.dict_id
							AND reimbursement_rate_dict.valid = '1'
							AND reimbursement_rate_dict.lang = '{$this->lang}'
						";
		}

		if ($table === "employee_cafeteria") {
			$leftJoin .= "
						LEFT JOIN cafeteria caf ON
							caf.cafeteria_id = employee_cafeteria.cafeteria_id
						AND caf.status = {$this->published}
						AND employee_contract.valid_from <= IFNULL(caf.valid_to,'{$this->defaultEnd}') AND employee_contract.valid_to >= caf.valid_from
						AND employee_contract.ec_valid_from <= IFNULL(caf.valid_to,'{$this->defaultEnd}') AND employee_contract.ec_valid_to >= caf.valid_from
						";
		}

		if ($table === "employee_card" && (int)App::getSetting("employee_use_access_level")) {
			$leftJoin .= "
						LEFT JOIN ac_access_level	ON
							employee_card.acl = ac_access_level.ac_access_level_id
						AND ac_access_level.status = {$this->published}
						AND ac_access_level.valid_from <=  '{$this->validTo}'
						AND IFNULL(ac_access_level.valid_to, '{$this->defaultEnd}') >= '{$this->validFrom}'
						";
		}

		//WHERE
		$where = "";

		//get data only for filtered employees
		if ($joinTable === "employee") {
			$where .= "$joinTable.$filteredEmployeesId IN ('{$this->employeeIds}')";
		} elseif ($joinTable === "employee_contract" || $joinTable === "employee_cost") {
			$where .= "$joinTable.$filteredEmployeesId IN ('{$this->employeeContractIds}')";
		}

		if(!$this->isOnlyQuitEmployees) {
			$where .= "
					AND $joinTable.status={$this->published}
					AND $joinTable.valid_from <=  '{$this->validTo}'
					AND IFNULL($joinTable.valid_to, '{$this->defaultEnd}') >= '{$this->validFrom}'
			";
		}

		if($this->isOnlyQuitEmployees) {
			$where .= "AND employee_contract.valid_from <= IFNULL($table.valid_to,'{$this->defaultEnd}') AND employee_contract.valid_to >= $table.valid_from
					   AND employee_contract.ec_valid_from <= IFNULL($table.valid_to,'{$this->defaultEnd}') AND employee_contract.ec_valid_to >= $table.valid_from
					   AND employee_contract.valid_to < '{$this->validFrom}'
					   ";
		}

		//GROUP_BY
		$groupBy = "";
		if ($table === "employee_card") {
			$groupBy = "GROUP BY employee_contract.employee_contract_id";
		}

		//MERGE SQL parts

		//in case of group table, there is no right join and need to query from $joinTable
		$selectTableFrom = ($isGroupTable) ? $joinTable : $table;

		$SQL = "SELECT {$select}
				FROM {$selectTableFrom}
				{$rightJoin}
				{$leftJoin}
				WHERE {$where}
				{$groupBy}
				";

		return $SQL;
	}

	/**
	 * Helper to get columns for $table that need to be queried
	 *
	 * If columns[colum_name]["select"] is not empty, then use this expression, otherwise query column_name
	 * @param string $gridID
	 * @param array $filter
	 * @return string
	 */
	protected function getSelectAttributes($table)
	{
		$SQL = "";
		foreach ($this->columns as $dataName => $dataAttributes) {
				if (!empty($dataAttributes["table"]) && ($dataAttributes["table"] === $table)) {
				if (!empty($dataAttributes["select"]) && $dataAttributes["select"]!==self::EMPTY_SQL) {
					$SQL .= "{$dataAttributes["select"]},";
				} elseif(!empty($dataAttributes["select"]) && $dataAttributes["select"] === self::EMPTY_SQL) {
					$SQL .= "";
				} else {
					if ($dataAttributes["table"]==="employee" ||$dataAttributes["table"]==="employee_contract" || $dataAttributes["table"]==="lastValidEc"){
						$SQL .= "{$dataAttributes["table"]}.{$dataName},";
					} else {
						$SQL .= "{$dataName},";
					}
				}
			}
		}
	
		return $SQL;
	}

	/**
	 *
	 * Helper to get join Sql for group tables
	 *
	 * @param string $group
	 * @param string $joinTable
	 * @return string
	 */
	protected function getGroupJoinSQL($group, $joinTable)
	{

		if (empty($joinTable)) {
			switch($group) {
				case "unit":
				case "company_org_group1":
				case "company_org_group2":
				case "company_org_group3":
						$joinTable 	 = "employee";
						break;
				case "workgroup":
                case "employee_position":    
						$joinTable 	 = "employee_contract";
						break;
				case "cost_center":
				case "cost":
						$joinTable 	 = "employee_cost";
						break;
			}
		}
		$groupId 	= $group."_id";
		$joinId	= (strpos($group, "company_org") !== false) ? "company_org_group_id" : $groupId;

		$SQL = "";

		if (EmployeeGroupConfig::isActiveGroup($groupId))
		{
			if (!$this->isOnlyQuitEmployees) {
				$SQL .= EmployeeGroup::getLeftJoinSQL($groupId,"employee_contract",$this->validFrom);
			} else {
				$SQL .= EmployeeGroup::getLeftJoinSQLWithoutCal($groupId);
			}
		}

		$SQL .= "
					LEFT JOIN {$group} ON
						{$group}.{$joinId}= ".EmployeeGroup::getActiveGroupSQL("{$groupId}",$joinTable)."
					AND {$group}.status= '{$this->published}'";
		if (!$this->isOnlyQuitEmployees) {
				$SQL .= "
					AND {$group}.valid_from <= '{$this->validTo}'
					AND IFNULL({$group}.valid_to, '{$this->defaultEnd}') >= '{$this->validFrom}'
					";
		} else {
			$SQL .= "
					AND employee_contract.valid_from <= IFNULL({$group}.valid_to,'{$this->defaultEnd}') AND employee_contract.valid_to >= {$group}.valid_from
					AND employee_contract.ec_valid_from <= IFNULL({$group}.valid_to,'{$this->defaultEnd}') AND employee_contract.ec_valid_to >= {$group}.valid_from
					";
		}

		return $SQL;
	}

	/**
	 *
	 * Add employee identifier ($filteredEmployeesId) as index
	 *
	 * @param array $array
	 * @param string $table
	 * @return array
	 */
	protected function addIndexToSQLResult($array, $table)
	{
		$indexedResult = [];

		foreach ($array as $data) {
			if ($table === "employee_base_absence"){
				$indexedResult[end($data)][$data["dict_id"]] = $data["quantity"];
			} else {
				$indexedResult[end($data)] = $data;
			}
		}

		return $indexedResult;
	}

	public function filters()
	{
        return [
            'accessControl', // perform access control for CRUD operations
        ];
    }

	public function accessRules()
	{
        return
		[
            [
				'allow', // allow authenticated users to access all actions
                'users' => ['@'],
            ],
            [
				'deny',  // deny all users
                'users' => ['*'],
            ],
        ];
	}
}

?>