<?php

class ReportUserSubstitutionController extends Grid2Controller
{
	private $defaultEnd;
	private $statusPublished = Status::PUBLISHED;
	private $useCompanyAndPayrollRights;

	public function __construct() {
		parent::__construct("reportUserSubstitution");
		$this->useCompanyAndPayrollRights = (int)App::getSetting("useCompanyAndPayrollRights");
		$this->defaultEnd = App::getSetting("defaultEnd");
	}

	protected function G2BInit()
	{
		parent::setControllerPageTitleId("page_title_report_user_substitution");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);
		$this->LAGridRights->overrideInitRights("export_xls",		false);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);
		$this->LAGridRights->overrideInitRights("export_pdf_node",	false);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);
		$this->LAGridDB->enableSQLMode();

		$this->LAGridDB->setSQLSelection($this->getSQLForGridData(), "row_id");
		parent::setExportFileName(Dict::getValue("page_title_report_user_substitution"));
		parent::G2BInit();
	}

	/**
	 * Grid adattartalmat visszaadó SQL
	 * @return string
	 */
	private function getSQLForGridData()
	{
		if (!$this->useCompanyAndPayrollRights)
		{
			$SQL = "
				SELECT DISTINCT
					IF(" . Employee::getParam('fullname_with_emp_id_ec_id', ["e1", "ec1"]) . " IS NULL, u1.`username`, " . Employee::getParam('fullname_with_emp_id_ec_id', ["e1", "ec1"]) . ") AS user_substitution_id,
					IF(" . Employee::getParam('fullname_with_emp_id_ec_id', ["e2", "ec2"]) . " IS NULL, u2.`username`, " . Employee::getParam('fullname_with_emp_id_ec_id', ["e2", "ec2"]) . ") AS user_id,
					us.`note`,
					us.`valid_from`,
					us.`valid_to`
				FROM `calendar` cal
				LEFT JOIN `user_substitution` us ON
						us.`status` = {$this->statusPublished}
					AND cal.`date` BETWEEN us.`valid_from` AND IFNULL(us.`valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `user` u1 ON
						u1.`user_id` = us.`user_substitution_id`
					AND u1.`status` = {$this->statusPublished}
					AND cal.`date` BETWEEN u1.`valid_from` AND IFNULL(u1.`valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `employee` e1 ON
						e1.`employee_id` = u1.`employee_id`
					AND e1.`status` = {$this->statusPublished}
					AND cal.`date` BETWEEN e1.`valid_from` AND IFNULL(e1.`valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `employee_contract` ec1 ON
						ec1.`employee_id` = e1.`employee_id`
					AND ec1.`status` = {$this->statusPublished}
					AND cal.`date` BETWEEN ec1.`valid_from` AND IFNULL(ec1.`valid_to`, '{$this->defaultEnd}')
					AND cal.`date` BETWEEN ec1.`ec_valid_from` AND IFNULL(ec1.`ec_valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `user` u2 ON
						u2.`user_id` = us.`user_id`
					AND u2.`status` = {$this->statusPublished}
					AND cal.`date` BETWEEN u2.`valid_from` AND IFNULL(u2.`valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `employee` e2 ON
						e2.`employee_id` = u2.`employee_id`
					AND e2.`status` = {$this->statusPublished}
					AND cal.`date` BETWEEN e2.`valid_from` AND IFNULL(e2.`valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `employee_contract` ec2 ON
						ec2.`employee_id` = e2.`employee_id`
					AND ec2.`status` = {$this->statusPublished}
					AND cal.`date` BETWEEN ec2.`valid_from` AND IFNULL(ec2.`valid_to`, '{$this->defaultEnd}')
					AND cal.`date` BETWEEN ec2.`ec_valid_from` AND IFNULL(ec2.`ec_valid_to`, '{$this->defaultEnd}')
				WHERE
						us.`row_id` IS NOT NULL
					AND cal.`date` = '{date}'
					AND (ec1.`employee_contract_id` = '{user_substitution_id}' OR '{user_substitution_id}' = '')
					AND (ec2.`employee_contract_id` = '{user_id}' OR '{user_id}' = '')
				ORDER BY user_substitution_id
			";
		} else {

			$art		= new ApproverRelatedGroup;
			$gargSQL	= $art->getApproverReleatedGroupSQL("Employee", ["employeeManagement"], userID(), "'{date}'", "AND", "allDate", $this->getControllerID());
			$SQL = "
				SELECT DISTINCT
					IF(" . Employee::getParam('fullname_with_emp_id_ec_id', ["e1", "ec1"]) . " IS NULL, u1.`username`, " . Employee::getParam('fullname_with_emp_id_ec_id', ["e1", "ec1"]) . ") AS user_substitution_id,
					IF(" . Employee::getParam('fullname_with_emp_id_ec_id', ["e2", "ec2"]) . " IS NULL, u2.`username`, " . Employee::getParam('fullname_with_emp_id_ec_id', ["e2", "ec2"]) . ") AS user_id,
					us.`note`,
					us.`valid_from`,
					us.`valid_to`
				FROM `calendar` cal
				LEFT JOIN `employee_group_config` AS group_conf ON
						group_conf.`status` = {$this->statusPublished}
					AND group_conf.`group_id` = 'unit_id'
				LEFT JOIN `user_substitution` us ON
						us.`status` = {$this->statusPublished}
					AND cal.`date` BETWEEN us.`valid_from` AND IFNULL(us.`valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `user` u1 ON
						u1.`user_id` = us.`user_substitution_id`
					AND u1.`status` = {$this->statusPublished}
					AND cal.`date` BETWEEN u1.`valid_from` AND IFNULL(u1.`valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `employee` e1 ON
						e1.`employee_id` = u1.`employee_id`
					AND e1.`status` = {$this->statusPublished}
					AND cal.`date` BETWEEN e1.`valid_from` AND IFNULL(e1.`valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `employee_contract` ec1 ON
						ec1.`employee_id` = e1.`employee_id`
					AND ec1.`status` = {$this->statusPublished}
					AND cal.`date` BETWEEN ec1.`valid_from` AND IFNULL(ec1.`valid_to`, '{$this->defaultEnd}')
					AND cal.`date` BETWEEN ec1.`ec_valid_from` AND IFNULL(ec1.`ec_valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `employee_group` g1 ON
						g1.`group_id` = 'unit_id'
					AND g1.`status` = {$this->statusPublished}
					AND g1.`employee_contract_id` = ec1.`employee_contract_id`
					AND cal.`date` BETWEEN g1.`valid_from` AND IFNULL(g1.`valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `unit` unit1 ON
						unit1.`unit_id` = IF(group_conf.`row_id` IS NOT NULL, g1.`group_value`, e1.`unit_id`)
					AND unit1.`status` = {$this->statusPublished}
					AND cal.`date` BETWEEN unit1.`valid_from` AND IFNULL(unit1.`valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `user` u2 ON
						u2.`user_id` = us.`user_id`
					AND u2.`status` = {$this->statusPublished}
					AND cal.`date` BETWEEN u2.`valid_from` AND IFNULL(u2.`valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `employee` e2 ON
						e2.`employee_id` = u2.`employee_id`
					AND e2.`status` = {$this->statusPublished}
					AND cal.`date` BETWEEN e2.`valid_from` AND IFNULL(e2.`valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `employee_contract` ec2 ON
						ec2.`employee_id` = e2.`employee_id`
					AND ec2.`status` = {$this->statusPublished}
					AND cal.`date` BETWEEN ec2.`valid_from` AND IFNULL(ec2.`valid_to`, '{$this->defaultEnd}')
					AND cal.`date` BETWEEN ec2.`ec_valid_from` AND IFNULL(ec2.`ec_valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `employee_group` g2 ON
						g2.`group_id` = 'unit_id'
					AND g2.`status` = {$this->statusPublished}
					AND g2.`employee_contract_id` = ec2.`employee_contract_id`
					AND cal.`date` BETWEEN g2.`valid_from` AND IFNULL(g2.`valid_to`, '{$this->defaultEnd}')
				LEFT JOIN `unit` unit2 ON
						unit2.`unit_id` = IF(group_conf.`row_id` IS NOT NULL, g2.`group_value`, e2.`unit_id`)
					AND unit2.`status` = {$this->statusPublished}
					AND cal.`date` BETWEEN unit2.`valid_from` AND IFNULL(unit2.`valid_to`, '{$this->defaultEnd}')
				WHERE
						us.`row_id` IS NOT NULL
					AND cal.`date` = '{date}'
					AND (ec1.`employee_contract_id` = '{user_substitution_id}' OR '{user_substitution_id}' = '')
					AND (ec2.`employee_contract_id` = '{user_id}' OR '{user_id}' = '')
			";
			if (isset($gargSQL["where"])) {
				$garg2 = str_replace("unit.", "unit2.", str_replace("`employee_contract`.", "ec2.", str_replace("employee.", "e2.", $gargSQL["where"])));
				$garg1 = str_replace("unit.", "unit1.", str_replace("`employee_contract`.", "ec1.", str_replace("employee.", "e1.", $gargSQL["where"])));
				// Mindkettő üres keresőben, egyikben van aktív munkavállaló:
				// helyettesítőre legyen joga
				$SQL .= str_replace_first("AND", "AND (('{user_substitution_id}' = '' AND e2.`row_id` IS NOT NULL AND", $garg2) . ")";
				// felh-ra legyen joga
				$SQL .= str_replace_first("AND", "OR ('{user_id}' = '' AND e1.`row_id` IS NOT NULL AND", $garg1) . ")";
				// Mindkettő üres és nincs is hozzá társított munkavállaló, akkor hozza
				$SQL .= " OR ('{user_id}' = '' AND e2.`row_id` IS NULL AND '{user_substitution_id}' = '' AND e1.`row_id` IS NULL)";
				// Mindkettő töltve van keresőben, muszáj lennie jognak a kereső SQL miatt
				$SQL .= " OR ('{user_id}' <> '' AND '{user_substitution_id}' <> ''))";
			}
			$SQL .= "
				ORDER BY user_substitution_id
			";
		}

		return $SQL;
	}

	private function getUserSearchSQL()
	{
		if (!$this->useCompanyAndPayrollRights) {
			$SQL = "
				SELECT
					ec.`employee_contract_id` AS id,
					".Employee::getParam('fullname_with_emp_id_ec_id', ["e", "ec"])." AS value
				FROM `employee` e
				LEFT JOIN `employee_contract` ec ON
						ec.`employee_id` = e.`employee_id`
					AND ec.`status` = {$this->statusPublished}
					AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '{$this->defaultEnd}')
					AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '{$this->defaultEnd}')
				WHERE
						e.`status` = {$this->statusPublished}
					AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '{$this->defaultEnd}')
					AND " . Employee::getParam('fullname_with_emp_id_ec_id', ["e", "ec"]) . " LIKE '%{search}%'
			";
		} else {
			$art		= new ApproverRelatedGroup;
			$gargSQL	= $art->getApproverReleatedGroupSQL("Employee", ["employeeManagement"], userID(), "'{date}'", "AND", "allDate", $this->getControllerID());
			$SQL = "
				SELECT
					`employee_contract`.`employee_contract_id` AS id,
					" . Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]) . " AS value
				FROM `employee`
				LEFT JOIN `employee_contract` ON
						`employee_contract`.`employee_id` = `employee`.`employee_id`
					AND `employee_contract`.`status` = {$this->statusPublished}
					AND '{date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->defaultEnd}')
					AND '{date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->defaultEnd}')
					" . EmployeeGroup::getAllActiveLeftJoinSQL("employee_contract", "'{date}'", "", "employee") . "
					" . EmployeeGroup::getAllBaseTablesWithGroup("employee", "employee_contract", "'{date}'") . "
			";
			if (isset($gargSQL["join"])) {
				$SQL .= $gargSQL["join"];
			}
			$SQL .= "
				WHERE
						`employee`.`status` = {$this->statusPublished}
					AND '{date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->defaultEnd}')
					AND " . Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]) . " LIKE '%{search}%'
			";
			if (isset($gargSQL["where"])) {
				$SQL .= $gargSQL["where"];
			}
			$userId = userID();
			$controllerId = $this->getControllerID();
			$input = "searchInput_date";
			if(isset($_SESSION["tiptime"][$userId][$controllerId][$input])) {
				$SQL = str_replace("'{date}'", "'" . $_SESSION["tiptime"][$userId][$controllerId][$input] . "'", $SQL);
			}
		}
		return $SQL;
	}

	public function search()
	{
		return
		[
			'date' => [
				'export'		=> true,
				'report_width'	=> 20,
				'dPicker'		=> true,
				'col_type'		=> 'ed',
				'default_value'	=> date('Y-m-d'),
				'label_text'	=> Dict::getValue("date"),
				'onchange'		=> ['user_substitution_id', 'user_id']
			],
			'user_substitution_id' => [
				'col_type'		=> 'auto',
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> $this->getUserSearchSQL()
				],
				'label_text'	=> Dict::getValue("user"),
				'default_value'	=>''
			],
			'user_id' => [
				'col_type'		=> 'auto',
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> $this->getUserSearchSQL()
				],
				'label_text'	=> Dict::getValue("substitute"),
				'default_value'	=> ''
			],
			'submit' => ['col_type' => 'searchBarReinitGrid', 'gridID' => 'dhtmlxGrid', 'width' => '*', 'label_text' => '']
		];
	}

	public function columns()
	{
		return [
			'user_substitution_id'	=> ['export'=> true, 'report_width' => 20, 'col_type' => 'ed', 'width' => 250],
			'user_id'				=> ['export'=> true, 'report_width' => 20, 'col_type' => 'ed', 'width' => 250],
			'note'					=> ['export'=> true, 'report_width' => 20, 'col_type' => 'ed', 'width' => 250],
			'valid_from'			=> ['export'=> true, 'report_width' => 20, 'dPicker' => true, 'col_type' => 'ed', 'width' => 150],
			'valid_to'				=> ['export'=> true, 'report_width' => 20, 'dPicker' => true, 'col_type' => 'ed', 'width' => 150]
		];
	}

	public function attributeLabels()
	{
		return [
			'user_substitution_id'	=> Dict::getValue("user"),
			'user_id'				=> Dict::getValue("substitute"),
			'note'					=> Dict::getValue("note"),
			'valid_from'			=> Dict::getValue("valid_from"),
			'valid_to'				=> Dict::getValue("valid_to"),
		];
	}
}

?>