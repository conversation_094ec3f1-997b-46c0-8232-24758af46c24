<?php

/**
 *
 * Alapszabadság-típus vezérl<PERSON>
 *
 * @see: https://njt.hu/jogszabaly/2012-1-00-00 (Munka törvénykönyve), 115-121. §
 */
class BaseAbsenceTypeController extends Grid2Controller
{
    const PUBLISHED = Status::PUBLISHED;
    /**
     * @var string $layout - az alkalmazott elrendezés
     */
    public $layout = '//layouts/main';

    /**
     * @var array $usedLangs - az aktuálisan használt nyelvek listája
     * [$num_index => $string_value] formátumban
     */
	private $usedLangs;

    /**
     * @var array $relatedDictionaries - az aktuális szótár kifejezései, egy virtuális PK-val indexelve
     * $this->relatedDictionaries[$lang.'#'$module.'#'.$dict_id] = $dict_value; formátumban
     */
	private $relatedDictionaries;

    /**
     * @var array $baseAbsenceTypes - a j<PERSON><PERSON><PERSON> l<PERSON>z<PERSON>, publik<PERSON>lt státuszú alapszabadság típusok
     * $this->baseAbsenceTypes[$dict_id] = $base_absence_type_id formátumban
     */
    private $baseAbsenceTypes;
	
    public function __construct() 
    {
        parent::__construct("baseAbsenceType");
        $this->usedLangs           = explode(';', App::getSetting("used_languages"));
        $this->relatedDictionaries = BaseAbsenceType::getRelatedDictionaries();
        $this->baseAbsenceTypes    = BaseAbsenceType::getBaseAbsenceTypes();
    }

    /**
     * Grid2 inicializálása
     */
    protected function G2BInit() 
    {
        
		$this->LAGridDB->setModelName("BaseAbsenceType");

		parent::setControllerPageTitleId("page_title_baseAbsenceType");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("add",				true);
		$this->LAGridRights->overrideInitRights("delete",			true);
		$this->LAGridRights->overrideInitRights("inline_modify",	true);
		$this->LAGridRights->overrideInitRights("edit",				true);
		$this->LAGridDB->enableSQLMode();
		
		$selectLang = "";
		$join = "";
        
		foreach ($this->usedLangs as $lang) {
			$join .= "LEFT JOIN
                          `dictionary` AS `dict_{$lang}` ON `bat`.`dict_id` = `dict_{$lang}`.`dict_id`
                      AND `dict_{$lang}`.`lang` = '{$lang}' \n";
			$selectLang .= "`dict_{$lang}`.`dict_value` AS `dict_value_{$lang}`, \n";
		}
		        
		$sql = "SELECT DISTINCT
                    `bat`.`row_id`,
                    `bat`.`priority`,
					{$selectLang}
                    `bat`.`dict_id`,
                    `bat`.`note`,
                    `bat`.`status`,
                    IFNULL(".Employee::getParam('fullname_with_emp_id', 'ec').", `uc`.`username`) AS `created_by`,
                    `bat`.`created_on`,
                    IFNULL(".Employee::getParam('fullname_with_emp_id', 'em').", `um`.`username`) AS `modified_by`,
                    `bat`.`modified_on`
                FROM `base_absence_type` AS `bat`
				{$join}
                LEFT JOIN 
                    `user` AS `uc` ON `bat`.`created_by` = `uc`.`user_id`
                LEFT JOIN 
                    `user` AS `um` ON `bat`.`modified_by` = `um`.`user_id`
                LEFT JOIN 
                    `employee` AS `ec` ON `ec`.`employee_id` = `uc`.`employee_id`
                LEFT JOIN 
                    `employee` AS `em` ON `em`.`employee_id` = `um`.`employee_id`
                WHERE 
                    `bat`.`status` = ".self::PUBLISHED."
                ORDER BY 
                    `bat`.`priority`";
        		
        $this->LAGridDB->setSQLSelection($sql, 'row_id');
        parent::setGridProperty("splitColumnEnabled", false);
		parent::setGridProperty("splitColumn", 1);
        parent::G2BInit();
        
	}

	public function search(): array { return []; }

    /**
     * A megjelenített oszlopok tulajdonságainak beállítása
     * @return array $return
     */
    public function columns(): array 
    {
        	
        $return = [
			'row_id'   => ['grid' => false, 'window' => false],
			'priority' => ['grid' => true, 'width' => 100, 'window' => true, 'col_type' => 'ed']
        ];
		
		foreach ($this->usedLangs as $lang)	{
			$return["dict_value_{$lang}"] = ['col_type' => 'ed', 'window' => true,];
		}

        $return['note']		   = ['grid' => true, 'width' => 200, 'window' => true, 'col_type' => 'ed'];
		$return['created_by']  = ['grid' => true, 'width' => 200, 'window' => false, 'col_type' => 'ro'];
		$return['created_on']  = ['grid' => true, 'width' => 150, 'window' => false, 'col_type' => 'ro'];
		$return['modified_by'] = ['grid' => true, 'width' => 200, 'window' => false, 'col_type' => 'ro'];
		$return['modified_on'] = ['grid' => true, 'width' => 150, 'window' => false, 'col_type' => 'ro'];
        
		return $return;
        
    }

    /**
     * A megjelenített oszlopok fejléccímkéinek beállítása az összes használt nyelvhez
     */
    public function attributeLabels(): array 
    {
        
        $model  = new BaseAbsenceType();
        $return = $model->attributeLabels();
		
		foreach($this->usedLangs as $lang) {
			$return["dict_value_{$lang}"] = Dict::getValue("lang_{$lang}");
		}

		return $return;
        
    }

    /**
     * Új alapszabadság-típus mentése
     *
     * @param array $data - a küldött űrlap adatai
     * @return void
     */
    public function actionSave($data = [], $modelName = null, $pk = null, $vOnly = false, $ret = false, $contentId = null) 
    {
        
		$this->layout = "//layouts/ajax";
		$this->G2BInit();
		$form         = requestParam('gridID');
		$data         = requestParam("dialogInput_$form");
		$finished     = false;
		$message      = '';
        $genId        = substr(genIdFromString($data['dict_value_en']), 0, 11);
        $dictId       = 'base_absence_type_'.$genId;
          
        foreach($this->usedLangs as $lang) {

            if($data["dict_value_{$lang}"] === '')	{
                $finished = true;
                $message  = Dict::getValue('error_field_required', ['attribute' => Dict::getValue("lang_{$lang}") . ' ' . Dict::getValue("name_dict_id")]) . "<br>";
            } elseif(in_array($data["dict_value_{$lang}"], $this->relatedDictionaries)) {
                $finished = true;
                $message  = Dict::getValue('error_field_is_exists_in_table') . "<br>";               
            }
                       
        }

        if(!$finished) {

            if(isset($this->baseAbsenceTypes[$dictId])) {
                $count   = BaseAbsenceType::countTheSameTypes($dictId);
                $dictId .= ($count > 1) ? '_'.$count : '_1';
            }

            $bat = new BaseAbsenceType();
            $bat->base_absence_type_id = $dictId;
            $bat->priority             = $data['priority'];
            $bat->dict_id              = $dictId;
            $bat->note                 = $data['note'];
            $bat->status               = self::PUBLISHED;
            $bat->created_by           = UserID();

            if($bat->save()) {  
                
                $addedLangs = 0;

                foreach($this->usedLangs as $lang) {
                    if($this->addDict($lang, $dictId, $data["dict_value_{$lang}"])) { 
                        $changesArray["addedDict{$lang}"] = $data["dict_value_{$lang}"];
                        $addedLangs++;
                    } else {
                        $message  .= Dict::getValue('error_addigng_lang_failed', ['lang' => Dict::getValue("lang_{$lang}")])."<br>";               
                    }
                }

                if($addedLangs == count($this->usedLangs)) {
                    $status = ['status'	=> 1];
                    Log::create($this, "BASE_ABSENCE_TYPE_SAVE", ["data" => $changesArray]);
                } else {
                    $bat->delete();
                    Dictionary::deleteDictionaryEntry('ttwa-base', $dictId);
                }            

            } else {
                $status = [
                    'status' => 0,
                    'error'	 => $message,
                ];
            }

        } else {
            $status = [
                'status' => 0,
                'error'	 => $message,
            ];
        }

        echo json_encode($status);
								        
	}

    /**
     * Alapszabadság típus módosítása
     * 
     * @return void
     */
	public function actionGridEdit(): void 
    {
        
		$ids    = requestParam('ids');
        $model	= BaseAbsenceType::model()->findByPk($ids);
		$dictId	= $model->dict_id;

        $i = 1;
        foreach ($this->usedLangs as $lang) {
			${'newDictValue'.$lang} = requestParam($ids . '_c' . $i);
			$i++;
		}

		$editorStatus = requestParam($ids.'_'."!nativeeditor_status");
		$action		  = 'update';
		$dict         = Dictionary::model()->findAllByAttributes(['dict_id' => $dictId]);

		foreach($dict as $d) {
            
			if(method_exists($d, "getOldAttributes")) {
                
				$oldData      = $d->getOldAttributes();
				$changesArray = [];
				$ignoreCols   = ["valid", "module", "row_id", "dict_id", "lang"];

				foreach($oldData as $key => $oldDataRow) {
                    
					if(!in_array($key, $ignoreCols) && $oldDataRow != ${'newDictValue'.$oldData["lang"]}) {
						$changesArray[] = [
							"dict_id"	=> $dictId,
							"prevValue"	=> $oldData[$key],
							"newValue"	=> ${'newDictValue'.$oldData["lang"]},
							"lang"		=> $oldData["lang"]
						];
					}

					$newD = Dictionary::model()->findByAttributes(['dict_id' => $dictId, 'lang' => $oldData["lang"]]);
					$newD->dict_value = ${'newDictValue'.$oldData["lang"]};
					$newD->save();
                    
					if (count($changesArray) > 0) {
						Log::create($this, "BASE_ABSENCE_TYPE_CHANGE", ["data" => $changesArray]);
					}
                    
				}
                
			}

        }
			
		if(stripos($_SERVER["HTTP_ACCEPT"], "application/xhtml+xml") !== false) {
			header("Content-type: application/xhtml+xml");
		} else {
			header("Content-type: text/xml");
		}
		echo('<?xml version="1.0" encoding="utf-8" ?>'."\n");
	    ?>
		<data>
            <action type='//<?=$action?>'
                    sid='<?=$ids?>'
                    tid='<?=$editorStatus?>'/>
		</data>
		<?php
        exit();
        
	}

    public function actionDelete($modelName = null, $hasRight = false) 
    {

		$this->layout  = "//layouts/ajax";
		$this->G2BInit();
		$ids           = explode(";", requestParam('ids'));
		$deleteSuccess = true;			
		$message       = null;
		
        foreach($ids as $id) {

            $model   = BaseAbsenceType::model()->findByPk($id);
            $empList = EmployeeBaseAbsence::getEmloyeesWhoUseThis($model->base_absence_type_id);

            if(!empty($empList)) {

                $deleteSuccess = false;			
                $message = Dict::getValue("sync_check_deleted_rows_failed").'<br>';

                for($i = 0; $i < 3; $i++) { $message .= array_shift($empList).'<br>'; }
                $listSize = count($empList);

                if($listSize > 3) {
                    $message .= Dict::getValue("there_are_employees_who_use_this", ['number' => $listSize]);
                } else {
                    $message .= Dict::getValue("there_is_employee_use_this");
                }

            } elseif(LinkAtToBat::isTypeInUsed($model->base_absence_type_id) == false) {
                $modelUpdated = Yang::modelUpdateByPk(
                    'BaseAbsenceType', 
                    $id, 
                    [
                        "status" => Status::INVALID,
                        "modified_by" => UserID(),
                        "modified_on" => date("Y-m-d h:i:s")
                    ]
                );
                $dictDeleted  = Dictionary::deleteDictionaryEntry('ttwa-base', $model->dict_id);
                if($modelUpdated && $dictDeleted) {
                    Log::create($this, "BASE_ABSENCE_TYPE_DELETE", ["data" => ['deletedRowId' => $id]]);
                } else {
                    $deleteSuccess = false;			
                    $message = Dict::getValue("sync_check_deleted_rows_failed");
                    Log::create($this, "BASE_ABSENCE_TYPE_DELETE_FAILED", ["data" => ['deletedRowId' => $id]]);   
                }
            } else {
                $deleteSuccess = false;			
                $message = Dict::getValue("sync_check_deleted_rows_failed");
            }

        }
		
		if($deleteSuccess) {
			$status = array(
				'status'	=> 1,
				'pkDeleted' => $ids,
			);
		} else {
			$status = array(
				'status'	=> 0,
				'message'	=> $message,
			);
		}

		echo json_encode($status);

	}

    /**
     * Új szótár bejegyzés létrehozása
     * 
     * @param string $lang - kétbetűs nyelvazonosító (ISO 639-1)
     * @param string $dictId - bejegyzés-azonosító
     * @param string $dictValue - a kifejezés maga
     * @param int $valid - érvényes|aktív-e a kifejezés (alapból: nem)
     *
     * @see: https://hu.wikipedia.org/wiki/ISO_639-1_nyelvk%C3%B3dok_list%C3%A1ja
     *
     * @return bool - true siker, false kudarc esetén 
     */
	private function addDict($lang, $dictId, $dictValue, $valid = 1): bool 
    {
		$dict			  = new Dictionary();
		$dict->lang		  = $lang;
        $dict->dict_id	  = $dictId;
		$dict->dict_value = $dictValue;
		$dict->module     = 'ttwa-base';
        $dict->valid      = $valid;
		return $dict->save();
	}

}
