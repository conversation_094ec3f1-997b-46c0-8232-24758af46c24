<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\Controller;
	use Yang;

`/yii2-only';


#yii2:done

class UserSwitchMenuController extends Controller
{
	public $layout = 'ajax';
	
	public function actionIndex()
	{
		$this->layout = 'ajax';
		
		$baseUrl = baseURL();
		$view = 'index';
		
		if(isYii1()) {
			print $this->render($view, ["baseUrl" => $baseUrl]);
		}
		if(isYii2()){
			$view = '@app/views/userSwitchMenu/index';
		}
	}
}

?>