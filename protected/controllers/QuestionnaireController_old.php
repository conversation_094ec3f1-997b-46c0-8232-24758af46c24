<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\MyActiveForm;
	use app\models\Registration;
	use app\models\Status;
	use Yang;

`/yii2-only';


class QuestionnaireController extends Controller
{
	public function actionGetNextQuestion() {
		$this->layout = "//layouts/empty";

		$response = [];

		$callback          = requestParam('callback');
		$questionnarie_id  = requestParam('questionnarie_id');
		$user_id           = requestParam('user_id');

		if (empty($questionnarie_id)/* || empty($user_id)*/) {
			$response = ["status" => "paramsError",];

			echo "$callback(" . json_encode($response) . ");";

			return false;
		}

		$SQL = "
			SELECT
				questionnaire.*
			FROM
				`questionnaire`
			WHERE
				questionnaire.`questionnaire_id` = '" . $questionnarie_id . "'
		";

		$res = dbFetchAll($SQL);
                
                $order_by = null;
                
		if (count($res)) {
                        $order_by = $res[0]["questionnaire_order"];
                }
                
		if (in_array($order_by, ["rand", "asc", "desc"])) {
		} else {
			$order_by = "rand";
		}
                    
                $SQL2 = "
                        SELECT
                                questionnaire_questions.*
                        FROM
                                `questionnaire_questions`
                        LEFT JOIN
                                `questionnaire_answer` ON
                                        questionnaire_answer.`questionnaire_id` = questionnaire_questions.`questionnaire_id`
                                        AND questionnaire_answer.`question_id` = questionnaire_questions.`question_id`
                                        AND questionnaire_answer.`user_id` = '" . $user_id . "'
                        WHERE
                                questionnaire_questions.`questionnaire_id` = '" . $questionnarie_id . "'
                                        AND questionnaire_answer.`row_id` IS NULL
                ";

                if ($order_by === "asc") {
                        $SQL2 .= "
                                ORDER BY
                                        questionnaire_questions.`question_order` ASC
                        ";
                } else if ($order_by === "desc") {
                        $SQL2 .= "
                                ORDER BY
                                        questionnaire_questions.`question_order` DESC
                        ";
                } else {
                        $SQL2 .= "
                                ORDER BY
                                        rand()
                        ";
                }
                
                //$SQL2 .= "LIMIT 1";
                     
                $results = (array) dbFetchAll($SQL2);
                    
		if (count($results)) {
                    $response = ["status" => 2, "data" => $results];	
		} else {
                    $response = ["status" => 1,];
                }

		echo "$callback(" . json_encode($response) . ");";
	}
        
        public function actionGetQuestionsInRandomOrder() {
                //$this->layout = "//layouts/empty";
                
                $response = [];

		$questionnarie_id  = requestParam('questionnarie_id');
                //$callback               = requestParam('callback');
                //$user_id         = requestParam('user_id');

		if (empty($questionnarie_id)) {
			$response = ["status" => "paramsError",];

			echo json_encode($response);

			return false;
		}
                
                $SQL = "
                        SELECT
                                questionnaire_questions.*
                        FROM
                                `questionnaire_questions`
                        WHERE
                                questionnaire_questions.`questionnaire_id` = '" . $questionnarie_id . "'
                                        AND questionnaire_questions.`question_category` = 1
                ";

                $resultsQCat1 = dbFetchAll($SQL);
                shuffle($resultsQCat1);
                
                if (count($resultsQCat1)) {
                    $response = ["status" => 2, "data" => $resultsQCat1];	
		} else {
                    $response = ["status" => 1,];
                }

		echo json_encode($response);
        }

	public function actionGetQuestionAnswers() {
		$this->layout = "//layouts/empty";

		$response = [];

		$callback          = requestParam('callback');
		$questionnarie_id  = requestParam('questionnarie_id');
		$question_id       = requestParam('question_id');

		if (empty($questionnarie_id) || empty($question_id)) {
			$response = ["status" => "paramsError",];

			echo "$callback(" . json_encode($response) . ");";

			return  false;
		}

		$SQL = "
			SELECT
				questionnaire_question_answer_group.*
			FROM
				`questionnaire_questions`
			LEFT JOIN
				`questionnaire_question_answer_group` ON
					questionnaire_question_answer_group.`question_answer_group_id` = questionnaire_questions.`question_answer_group_id`
			WHERE
				questionnaire_questions.`questionnaire_id` = '" . $questionnarie_id . "'
					AND questionnaire_questions.`question_id` = '" . $question_id . "'
			GROUP BY
				questionnaire_questions.`questionnaire_id`,
				questionnaire_question_answer_group.`question_answer_id`
			ORDER BY
				questionnaire_question_answer_group.`question_answer_order`
		";

		$res = dbFetchAll($SQL);

		if (count($res)) {
			$response = ["status" => 2, "data" => $res,];
		} else {
			$response = ["status" => 1,];
		}

		echo "$callback(" . json_encode($response) . ");";
	}

	public function actionSaveQuestionAnswer() {
		$this->layout = "//layouts/empty";

		$response = [];

		$callback                  = requestParam('callback');
		$questionnarie_id          = requestParam('questionnarie_id');
		$question_id               = requestParam('question_id');
		$question_answer_response  = requestParam('question_answer_response');
		$user_id                   = requestParam('user_id');
                
		if (empty($user_id)) {
			$user_id = md5(__CLASS__.date('YmdHis').rand(1, 100000));
		}              
                
		if (isset($_POST["submitEmail"])) {
			$email = $_POST["question_answer_response"];
			$validator = new CEmailValidator;

			if (empty($email)) {
				$response = ["status" => 5, "message" => "Kérjük, adja meg az e-mail címét!"];
			} else if (!empty($email) && !$validator->validateValue($email)) {
				$response = ["status" => 4, "message" => "Kérjük, adjon meg egy valós e-mail címet!"];
			} else {
				$SQL = "
					INSERT INTO
							`questionnaire_answer`
									(`questionnaire_id`,`question_id`,`question_answer_response`,`user_id`,`created_on`)
							VALUES
									('" . $questionnarie_id . "','" . $question_id . "','" . $question_answer_response . "','" . $user_id . "',NOW())
						";
				$exec = dbExecute($SQL);
				$response = ["status" => 3, "message" => "Köszönjük! E-mail címét rögzítettük."];
			}

		} else if (isset($_POST["submitAnswer"])) {
			$answer = htmlspecialchars(trim($_POST["question_answer_response"]));
			
			if (empty($answer)) {
				$response = ["status" => 5, "message" => "Kérjük, ne hagyja üresen a mezőt!"];
			} else {
				$SQL = "
					INSERT INTO
						`questionnaire_answer`
						(`questionnaire_id`,`question_id`,`question_answer_response`,`user_id`,`created_on`)
					VALUES
						('" . $questionnarie_id . "','" . $question_id . "','" . $question_answer_response . "','" . $user_id . "',NOW())
					";
				$exec = dbExecute($SQL);
				$response = ["status" => 3, "user_id" => $user_id];
			}
		} else if (isset($_POST['submitNumber'])) {
			$answer = htmlspecialchars(trim($_POST["question_answer_response"]));
			$number_pattern = '/^\s*[-+]?[0-9]*\.?[0-9]+([eE][-+]?[0-9]+)?\s*$/';
			
			if (empty($answer) && (int)$answer !== 0) {
				$response = ["status" => 5, "message" => "Kérjük, ne hagyja üresen a mezőt!"];
			} else if (!preg_match($number_pattern,$answer)) {
				$response = ["status" => 4, "message" => "Kérjük, csak numerikus karaktereket használjon!"];
			} else {
				$SQL = "
					INSERT INTO
						`questionnaire_answer`
						(`questionnaire_id`,`question_id`,`question_answer_response`,`user_id`,`created_on`)
					VALUES
						('" . $questionnarie_id . "','" . $question_id . "','" . $question_answer_response . "','" . $user_id . "',NOW())
					";
				$exec = dbExecute($SQL);
				$response = ["status" => 3, "user_id" => $user_id];
			}
		} else {

			if (empty($questionnarie_id) || empty($question_id) || empty($question_answer_response) || empty($user_id)) {
				$response = ["status" => "paramsError"];

				echo "$callback(" . json_encode($response) . ");";

				return  false;
			}

			$SQL = "
					INSERT INTO
							`questionnaire_answer`
									(`questionnaire_id`,`question_id`,`question_answer_response`,`user_id`,`created_on`)
							VALUES
									('" . $questionnarie_id . "','" . $question_id . "','" . $question_answer_response . "','" . $user_id . "',NOW())
			";

			$exec = dbExecute($SQL);

			if ($exec) {
					$response = ["status" => 2, "user_id" => $user_id];
			} else {
					$response = ["status" => 1, "user_id" => $user_id];
			}
		}   
                

		echo "$callback(" . json_encode($response) . ");";
	}

	public function actionResetAnswers() {
		$this->layout = "//layouts/empty";

		$response = [];

		$callback                  = requestParam('callback');
		$questionnarie_id          = requestParam('questionnarie_id');
		$user_id                   = requestParam('user_id');

		if (empty($questionnarie_id) || empty($user_id)) {
			$response = ["status" => "paramsError",];

			echo "$callback(" . json_encode($response) . ");";

			return  false;
		}

		$SQL = "
			DELETE FROM
				`questionnaire_answer`
			WHERE
				`questionnaire_id` = '" . $questionnarie_id . "'
					AND `user_id` = '" . $user_id . "'
		";

		$exec = dbExecute($SQL);

		if ($exec) {
			$response = ["status" => 2,];
		} else {
			$response = ["status" => 1,];
		}

		echo "$callback(" . json_encode($response) . ");";
	}
        
	public function actionGetNumberOfQuestions() {
		
		$questionnaire_id = requestParam('questionnarie_id');

		$SQL = "SELECT *
				FROM `questionnaire_questions`
				WHERE `questionnaire_id` ='" .$questionnaire_id. "'";

		$results = dbFetchAll($SQL);

		if ($results) {
			$response = ["status" => 2, "numberOfQuestions" => count($results)];
		} else {
			$response = ["status" => 1,];
		}

		echo json_encode($response);
	}

	public function actionSaveRegistration() {
		$this->layout = "//layouts/empty";

		$retArr = [];

		$cardNumber = requestParam('cardNumber');

		if (!empty($cardNumber)) {
			$cardNumber = $this->getRealCardNumber($cardNumber);

			$time = date("Y-m-d H:i:s");

			$new_reg = new Registration;
			$new_reg->terminal_id = "rk_001";
			$new_reg->card = $cardNumber;
			$new_reg->time = $time;
			$new_reg->event_type_id = "1";
			$new_reg->cost_id = "1";
			$new_reg->status = Status::PUBLISHED;
			$new_reg->created_by = "in_kiosk";
			$new_reg->created_on = date("Y-m-d H:i:s");

			if ($new_reg->validate()) {
				$new_reg->save();
			} else {
				$error = MyActiveForm::_validate($new_reg);

				$arr = (array) json_decode($error);

				foreach ($arr as $value) {
					foreach ($value as $val) {
						$msg .= $val . "<br/>";
					}
				}
			}
		}

			echo json_encode($retArr);
	}

	private function getRealCardNumber($inCardNumber) {
		if (empty($inCardNumber)) {
			return false;
		}

		$bCardNumber = decbin($inCardNumber);

		if (strlen($bCardNumber) > 32) {
			$bCardNumber = substr($bCardNumber, -32);
		}

		$cardNumber = bindec($bCardNumber);

		return $cardNumber;
	}
}