<?php

ini_set('max_execution_time', 3600);
ini_set("memory_limit", "2048M");

define('DATE_PATTERN', '/^[0-9]{4}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/');

class AbsenceExpenditureController extends Grid2Controller
{
	private $date					= NULL; // dátum
	private $acceptance				= NULL; // szabadság elfogadás kell-e
	private $simulation				= NULL; // szimulációban legyen-e futtatva
	private $error_log				= ''; // hibák
	private $table_row				= 0; // táblázat sora
	private $result_array			= []; // eredmény tömb amit a grid-nek adunk át
	private $abs_status				= '1';
	private $employee_data			= [];
	private $public_holiday			= FALSE;
	public $action					= NULL;
	private $workgroup				= NULL;
	private $unit					= NULL;
	private $publishedStatus		= NULL;
	private $defaultEnd				= NULL;

	public function __construct($id, $module = null)
	{
		parent::__construct("absenceExpenditure");
		$this->publishedStatus = Status::PUBLISHED;
		$this->defaultEnd = App::getSetting("defaultEnd");
	}


	protected function G2BInit()
	{
		parent::setControllerPageTitleId("page_title_absence_release");
		parent::setGridProperty("splitColumnEnabled", true,	 "dhtmlxGrid");

		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);

		$this->LAGridDB->enableSQLMode();
		parent::setExportFileName(Dict::getValue("page_title_absence_release"));
		parent::G2BInit();
	}


	protected function setSQL($filter, $gridID, $forReport = false)
	{
		$this->date			= $filter['date'];
		$this->acceptance	= $filter['acceptance'];
		$this->simulation	= $filter['simulation'];
		$this->action		= $filter['abs_action'];
		$this->workgroup	= $filter['workgroup'];

		$this->abs_status = $this->acceptance == 'y' ? '2' : '1';

		$unitSearchAllowed = (int)App::getSetting('addUnitSearchToAbsenceExpenditure');
		if (isset($filter['unit']) && $unitSearchAllowed === 1) {
			$this->unit = $filter['unit'];
		}

		if (isset($this->date)) {
			$this->validationPost();
			if ($this->error_log === 'ok') {
				$employees = $this->getEmployees();
				// szabadság kiadás
				if ($this->action == 'abs_in') {
					$this->public_holiday = $this->checkPublicHoliday();
					if (is_array($employees) && count($employees) > 0) {
						$this->processEmployee($employees, $filter);
					}
				}

				//szabadság visszavétel
				if ($this->action == 'abs_revert') {
					if ((is_array($employees)) and (count($employees))) {
						$this->processEmployeeRevertAbsence($employees, $filter);
					}
				}
			} else {
				$this->result_array[] = [
					'table_row'			=> '',
					'employee_name'		=> '',
					'emp_id'			=> '',
					'company_name'		=> '',
					'msg'				=> '<span style="color:red; font-size:16px;"><b>' . $this->error_log . '</b></span>'
				];
			}

			$this->create_temp_table();
			$this->multiInsert($this->result_array);
			$SQL = "SELECT * FROM temp_absence_expenditure ";
			$this->LAGridDB->setSQLSelection($SQL, 'table_row');

			return $SQL;
		}
	}


	/**
	 * Grid oszlopok
	 * @return array
	 */
	public function columns()
	{
		$columns = [
			'employee_name'		=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '200',],
			'emp_id'			=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150',],
			'company_name'		=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '200',],
			'msg'				=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '500',],
		];
		$columns = $this->columnRights($columns);

		return $columns;
	}

	/**
	 * Nyelvi elemek
	 * @return array
	 */
	public function attributeLabels()
	{
		return [
			//	'table_row'		=> '',
			'emp_id'		=> Dict::getValue("emp_id"),
			'employee_name'	=> Dict::getValue("employee_name"),
			'company_name'	=> Dict::getValue("company"),
			'msg'			=> Dict::getValue("operation"),

		];
	}

	/**
	 * kereső mező beállítása
	 * @return array
	 */
	public function search()
	{
		$return = [
			'date'			=> [
				'col_type'		=> 'ed',
				'dPicker'		=> TRUE,
				'width'			=> '*',
				'label_text'	=> Dict::getValue("date"),
				'default_value'	=> date('Y-m-d'),
				'onchange'		=> ['workgroup']
			],
			'acceptance'	=> [
				'col_type'		=> 'combo',
				'multiple'		=> false,
				'options'		=> [
					'mode'		=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
					'array'		=> [
						['id'	=> '0', 'value' => Dict::getValue("please_select")],
						['id'	=> 'y', 'value' => Dict::getValue("yes")],
						['id'	=> 'n', 'value' => Dict::getValue("no") . ' (' . Dict::getValue("without_approval_email") . ')'],
						['id'	=> 'ne', 'value' => Dict::getValue("no") . ' (' . Dict::getValue("send_approval_email") . ')'],
					],

				],
				'label_text'	=> Dict::getValue("accepting_holidays"),
				'default_value'	=> '0',
				'onchange'		=> []
			],
			'simulation'	=> [
				'col_type'		=> 'combo',
				'multiple'		=> false,
				'options'		=> [
					'mode'		=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
					'array'		=> [
						['id'	=> '0', 'value' => Dict::getValue("please_select")],
						['id'	=> 'y', 'value' => Dict::getValue("yes")],
						['id'	=> 'n', 'value' => Dict::getValue("no")],
					]

				],
				'label_text'	=> Dict::getValue("run_simulation"),
				'default_value'	=> '0',
				'onchange'		=> []
			],
			'abs_action'	=> [
				'col_type'		=> 'combo',
				'multiple'		=> false,
				'options'		=> [
					'mode'		=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
					'array'		=> [
						['id'	=> 'abs_in', 'value' => Dict::getValue("menu_item_absence_release")],
						['id'	=> 'abs_revert', 'value' => Dict::getValue("onleave") . ' ' . Dict::getValue("delete")],
					],
				],
				'label_text'	=> Dict::getValue("absence_operation"),
				'default_value'	=> '0',
				'onchange'		=> []

			],
			'workgroup'	=> [
				'col_type'		=> 'combo',
				'multiple'		=> false,
				'options'		=> [
					'mode'			=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'			=> "
										SELECT
											`workgroup_id` AS id,
											`workgroup_name` AS value
										FROM `workgroup`
										WHERE
												`status` = '" . $this->publishedStatus . "'
											AND '{date}' BETWEEN `valid_from` AND IFNULL(`valid_to`, '" . App::getSetting("defaultEnd") . "')
										ORDER BY value
															",
					'array'			=> [["id" => "ALL", "value" => Dict::getValue("all")]]
				],

				'label_text'	=> Dict::getModuleValue("ttwa-base", "workgroup"),
				'default_value'	=> 'ALL'
			],
			'unit'		=> [
				'col_type'		=> 'combo',
				'multiple'		=> false,
				'options'		=>	[
					'mode'			=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'			=> "
										SELECT
											`unit_id` AS id,
											`unit_name` as value
										FROM `unit`
										WHERE
												`status` = '" . $this->publishedStatus . "'
											AND '{date}' BETWEEN `valid_from` AND IFNULL(`valid_to`, '" . App::getSetting("defaultEnd") . "')
										ORDER BY value
															",
					'array'			=> [["id" => "ALL", "value" => Dict::getValue("all")]]
				],
				'label_text'	=> Dict::getValue("unit_id"),
				'default_value'	=> 'ALL'
			],
			'submit'		=> [
				'col_type'		=> 'searchBarReloadGrid',
				'width'			=> '*',
				'label_text'	=> '',
			]

		];

		$unitSearchAllowed = (int)App::getSetting('addUnitSearchToAbsenceExpenditure');
		if ($unitSearchAllowed === 0) {
			unset($return['unit']);
		} else {
			$return['date']['onchange'][] = 'unit';
		}

		return $return;
	}


	/**
	 * Bejövő adatok validálása
	 * @return string
	 */
	public function validationPost()
	{
		if ((!isset($this->date)) or (strlen($this->date) != 10) or (!preg_match(DATE_PATTERN, $this->date))) {
			$this->error_log = Dict::getModuleValue("ttwa-base", "wrong_date");
			return '';
		}

		// szabadság elfogadás validáció
		if ($this->acceptance == '0') {
			$this->error_log = Dict::getModuleValue("ttwa-base", "should_absences_automatically_accepted");
			return '';
		}

		// szimuláció validáció
		if ($this->simulation == '0') {
			$this->error_log = Dict::getModuleValue("ttwa-base", "should_run_in_simulation");
			return '';
		}

		$this->error_log = 'ok';
		return '';
	}


	/**
	 * Lekért Dolgozók feldolgozása
	 * @param array $employees
     * @param array $filter
	 */
	public function processEmployee($employees, $filter)
	{
		foreach ($employees as $employee) {
			$this->employee_data = $employee;
			if (App::getSetting("absenceexpediture_only_workday") == 1) {
				// ha nincs munkanapja arra a napra
				if ($this->checkWorkday() === FALSE) {
					$this->writeResultArray(Dict::getModuleValue("ttwa-base", "no_workday_according_to_schedule"));
					continue;
				}
			} else {
				if ((is_array($this->public_holiday)) and (count($this->public_holiday) > 0)) {
					$this->writeResultArray("<b>" . Dict::getModuleValue("ttwa-base", "public_holiday") . "</b>");
					continue;
				}
			}

			// van-e már az adott napra szabadsága
			if ($this->checkABS() === TRUE) {
				continue;
			}

			$employe_abs = $this->checkFreeAbsence();
			if (empty($employe_abs)) {
				$this->writeResultArray("<b>" . Dict::getModuleValue("ttwa-base", "not_enough_absences") . "</b>");
				continue;
			}

			$contractId = $this->employee_data["employee_contract_id"];
			$year = explode("-", $this->date)[0];
			$frameBalance = $this->getFrameBalanceByContractIdAndYear($contractId, $year);

			if ($frameBalance["available"] <= 0) {
				$this->writeResultArray("<b>" . Dict::getModuleValue("ttwa-base", "not_enough_absences") . "</b>");
				continue;
			}

			// szabadság mentése
			$employee_absence_id = $this->insertAbsence();
			if ($employee_absence_id !== FALSE) {
				// jóváhagyó email küldése
				if ($filter['acceptance'] == 'ne') {
					$this->sendApprovalEmail($employee_absence_id);
				}
			}
		}
	}


	/**
	 * Szabadság visszavétel dolgozóknak
	 *
	 * @param array $employees
	 * @param array $filter
	 */
	public function processEmployeeRevertAbsence($employees, $filter)
	{
		foreach ($employees as $employee) {
			$this->employee_data = $employee;
			$abs = $this->getEmployeeAbsByDate(TRUE);
			if (is_array($abs)) {
				if ((isset($this->simulation)) and ($this->simulation === 'y')) {
					$this->writeResultArray(Dict::getModuleValue("ttwa-base", "successful_withdrawal_of_absences") . ' ' . Dict::getModuleValue("ttwa-base", "simulation_in_parenthesis"));
				} else {
					$revert = $this->revertAbsence($abs);
					if ($revert === TRUE) {
						$this->writeResultArray(Dict::getModuleValue("ttwa-base", "successful_withdrawal_of_absences"));
					}
				}
			} else {
				$this->writeResultArray(Dict::getModuleValue("ttwa-base", "no_absence_to_withdraw"));
			}
		}
	}


	/**
	 * Szabadság visszavétel update SQL
	 *
	 * @param array $abs
	 * @return boolean
	 */
	public function revertAbsence($abs)
	{
		$update = '';
		if (is_array($abs)) {

			$update .= "UPDATE employee_absence SET "
				. "	status = 5, "
				. "	modified_by = 'absenceExpenditureRevert', "
				. " modified_on = NOW()"
				. " WHERE row_id = '" . $abs['row_id'] . "'; ";
		}

		if ($update != '') {
			try {
				dbExecute($update);
				return true;
			} catch (\Exception $e) {
				Yang::log($e->getMessage(), 'log', 'system');
				$this->writeResultArray(Dict::getModuleValue("ttwa-base", "error_in_absence_withdrawal"));
				return false;
			}
		}
        return false;
	}


	/**
	 * Jóváhagyó email küldése
	 *
	 * @param string $employee_absence_id
	 */
	public function sendApprovalEmail($employee_absence_id)
	{
		if ((isset($this->simulation)) and ($this->simulation === 'y')) {
			$this->writeResultArray(Dict::getModuleValue("ttwa-base", "email_sending_ok"));
		} else {
			$employee_absence_row_ids = $this->getEmployeeAbsenceRowId($employee_absence_id);
			$messaging = new Messaging("absenceapproval", "EmployeeAbsence", $employee_absence_id, ["employee_absence_row_ids" => $employee_absence_row_ids,]);
			$senderUserID      = userID();
			$recipientUsers = [Employee::getEmployeeUserIDByEcID($this->employee_data['employee_contract_id'])];
			$absenceData[]	= AHPAbsenceFunctions::getAbsenceDataByID($employee_absence_id);
			foreach ($absenceData as $absence) {
				$generalContentParams = $absence;
			}
			$event = "req_add";
			$process_id = "absenceApprover";
			$message = '';
			$messaging->sendMessage($senderUserID, $recipientUsers, $generalContentParams, $event, $process_id, $message);
			$this->writeResultArray(Dict::getModuleValue("ttwa-base", "approving_email_sent"));
		}
	}


	/**
	 * Szabadság adatok lekérdezése $employee_absence_id alapján
	 * @param string $employee_absence_id
	 * @return mixed
	 */
	public function getEmployeeAbsenceRowId($employee_absence_id)
	{
		$ret = FALSE;
		$select_sql = "SELECT * FROM employee_absence ea
						WHERE ea.`employee_absence_id` = '" . $employee_absence_id . "'
						AND ea.`status` = 1
						AND ea.`day` = '" . $this->date . "'
						AND ea.`employee_contract_id` = '" . $this->employee_data['employee_contract_id'] . "'
						;";

		$result = $this->getSqlResult($select_sql, TRUE);

		if ((is_array($result)) and count($result) > 0) {
			$ret = $result['row_id'];
		}

		return $ret;
	}


	/**
	 * Minden olyan dolgozó lekérdezése akinek ki kell adni szabadnapot
	 */
	public function getEmployees()
	{
		$art = new ApproverRelatedGroup;
		$gargSQL = $art->getApproverReleatedGroupSQLWithoutCalc("Employee", "absenceApprover", false, "AND", "CurrentDate", $this->getControllerID());
		$and = '';
		if ((isset($this->workgroup)) and ($this->workgroup != 'ALL') and (!is_null($this->workgroup))) {
			$and .= " AND `workgroup`.`workgroup_id` = '$this->workgroup' ";
		}

		if (isset($this->unit) && ($this->unit !== 'ALL') && !is_null($this->unit)) {
			$and .= " AND `unit`.`unit_id` = '$this->unit' ";
		}
		$select_sql = "
						SELECT DISTINCT
							`employee`.`employee_id`,
							`employee`.`emp_id`,
							`employee_contract`.`employee_contract_id`,
							CONCAT(`employee`.`last_name`, ' ', `employee`.`first_name`) as empl_name,
							`employee`.`company_id`,
							`company`.`company_name`,
							`unit`.`unit_id`,
							`workgroup`.`workgroup_id`
						FROM `employee`
						LEFT JOIN `employee_contract` ON
								`employee_contract`.`employee_id` = `employee`.`employee_id`
							AND `employee_contract`.`status` = {$this->publishedStatus}
							AND '$this->date' BETWEEN
								`employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '$this->defaultEnd')
							AND '$this->date' BETWEEN
								`employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.ec_valid_to, '$this->defaultEnd')
						";

		if (EmployeeGroupConfig::isActiveGroup('company_id')) {
			$select_sql .= EmployeeGroup::getLeftJoinSQLWithoutCal("company_id", "employee_contract", "", "", "employee");
		}
		if (EmployeeGroupConfig::isActiveGroup('workgroup_id')) {
			$select_sql .= EmployeeGroup::getLeftJoinSQLWithoutCal("workgroup_id", "employee_contract", "", "", "employee");
		}
		if (EmployeeGroupConfig::isActiveGroup('unit_id')) {
			$select_sql .= EmployeeGroup::getLeftJoinSQLWithoutCal("unit_id", "employee_contract", "", "", "employee");
		}
		if (EmployeeGroupConfig::isActiveGroup('company_org_group1_id')) {
			$select_sql .= EmployeeGroup::getLeftJoinSQLWithoutCal("company_org_group1_id", "employee_contract", "", "", "employee");
		}
		if (EmployeeGroupConfig::isActiveGroup('company_org_group2_id')) {
			$select_sql .= EmployeeGroup::getLeftJoinSQLWithoutCal("company_org_group2_id", "employee_contract", "", "", "employee");
		}
		if (EmployeeGroupConfig::isActiveGroup('company_org_group3_id')) {
			$select_sql .= EmployeeGroup::getLeftJoinSQLWithoutCal("company_org_group3_id", "employee_contract", "", "", "employee");
		}

		$select_sql .= "
						LEFT JOIN `company` ON
								`company`.`company_id` = `employee`.company_id
							AND `company`.`status` = {$this->publishedStatus}
							AND '$this->date' BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`,'$this->defaultEnd')
						LEFT JOIN `workgroup` ON
								`workgroup`.`workgroup_id` = " . EmployeeGroup::getActiveGroupSQL("workgroup_id", "employee_contract") . "
							AND `workgroup`.`status` = {$this->publishedStatus}
							AND '$this->date' BETWEEN `workgroup`.`valid_from` AND IFNULL(`workgroup`.`valid_to`, '{$this->defaultEnd}')
						LEFT JOIN `unit` ON
								`unit`.`unit_id` = " . EmployeeGroup::getActiveGroupSQL("unit_id", "employee") . "
							AND `unit`.`status` = {$this->publishedStatus}
							AND '$this->date' BETWEEN `unit`.`valid_from` AND IFNULL(`unit`.`valid_to`, '{$this->defaultEnd}')
						LEFT JOIN `company_org_group1` ON
								`company_org_group1`.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL("company_org_group1_id", "employee") . "
							AND	`company_org_group1`.`status` = {$this->publishedStatus}
							AND '$this->date' BETWEEN `company_org_group1`.`valid_from` AND IFNULL(`company_org_group1`.`valid_to`, '{$this->defaultEnd}')
						LEFT JOIN `company_org_group2` ON
								`company_org_group2`.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL("company_org_group2_id", "employee") . "
							AND `company_org_group2`.`status` = {$this->publishedStatus}
							AND '$this->date' BETWEEN `company_org_group2`.`valid_from` AND IFNULL(`company_org_group2`.`valid_to`, '{$this->defaultEnd}')
						LEFT JOIN `company_org_group3` ON
								`company_org_group3`.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL("company_org_group3_id", "employee") . "
							AND `company_org_group3`.`status` = {$this->publishedStatus}
							AND '$this->date' BETWEEN `company_org_group3`.`valid_from` AND IFNULL(`company_org_group3`.`valid_to`, '{$this->defaultEnd}')
		";

		$select_sql	.= $gargSQL["join"];

		$select_sql .= "
						WHERE
								`employee`.`status` = {$this->publishedStatus}
							AND '$this->date' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`,'$this->defaultEnd')
							AND `employee_contract`.`row_id` IS NOT NULL
							" . $and . "
						";

		$select_sql .= $gargSQL["where"];
		$select_sql .= " ORDER BY empl_name";
		$result = $this->getSqlResult($select_sql);

		return $result;
	}


	/**
	 * Munkanap ellenőrzése
	 * @return bool
	 */
	public function checkWorkday()
	{
		$wsu = $this->getWorkDayByWSU();
		$wsbe = (!$wsu) ? $this->getWorkDayByWSBE() :  false;
		$wsbu = (!$wsu && !$wsbe) ? $this->getWorkDayByWSBU() : false;
		if ($wsu || $wsbe || $wsbu) {
			return true; // van munkanap
		}

		return false;
	}


	/**
	 * Munkanap lekérdezése a work_schedule_by_employee táblából
	 *
	 * @return mixed
	 */
	public function getWorkDayByWSBE()
	{
		$wsbe_sql = "SELECT wsbe.`employee_contract_id`, wsbe.`day`, wsbe.`daytype_id`, d.`name`
						FROM work_schedule_by_employee wsbe
						LEFT JOIN daytype d ON d.`daytype_id` = wsbe.`daytype_id`
						WHERE wsbe.`employee_contract_id` = '" . $this->employee_data['employee_contract_id'] . "'
						AND wsbe.`status` = '" . $this->publishedStatus . "'
						AND d.`type_of_daytype` = 'WORKDAY'
						AND wsbe.`day` = '" . $this->date . "' ";

		$result = $this->getSqlResult($wsbe_sql, TRUE);

		return (!$result) ? false : true;
	}


	/**
	 * Munkanap lekérdezése a work_schedule_by_unit táblából
	 *
	 * @return mixed
	 */
	public function getWorkDayByWSBU()
	{
		$wsbu_sql = "SELECT wsbu.`unit_id`, wsbu.`workgroup_id`, wsbu.`day`, wsbu.`daytype_id` , d.`name`
						FROM work_schedule_by_unit wsbu
						LEFT JOIN daytype d ON d.`daytype_id` = wsbu.`daytype_id`
						WHERE wsbu.`unit_id` = '" . $this->employee_data['unit_id'] . "'
						AND wsbu.`workgroup_id` = '" . $this->employee_data['workgroup_id'] . "'
						AND wsbu.`day` = '" . $this->date . "'
						AND d.`type_of_daytype` = 'WORKDAY'
						AND wsbu.`status` = '" . $this->publishedStatus . "' ";

		$result = $this->getSqlResult($wsbu_sql, TRUE);

		return (!$result) ? false : true;
	}


	/**
	 * Munkanap lekérdezése a work_schedule_used táblából
	 * @return mixed
	 */
	public function getWorkDayByWSU()
	{
		$wsu_sql = "
					SELECT
						`row_id`
					FROM `work_schedule_used`
					WHERE
							`employee_contract_id` = '" . $this->employee_data['employee_contract_id'] . "'
						AND `status` = {$this->publishedStatus}
						AND `type_of_daytype` = 'WORKDAY'
						AND `day` = '$this->date'
					";

		$result = $this->getSqlResult($wsu_sql, TRUE);

		return (!$result) ? false : true;
	}


	/**
	 * Elérhető szabadság lekérdezése
	 */
	public function checkFreeAbsence()
	{
		$full_absence_sql = "SELECT SUM(eba.`quantity`) as quantity
								FROM employee_base_absence eba
								WHERE eba.`employee_contract_id` = '" . $this->employee_data['employee_contract_id'] . "'
								AND eba.`valid_from` >= '" . date('Y-01-01', strtotime($this->date)) . "'
								AND eba.`valid_to` <= '" . date('Y-12-31', strtotime($this->date)) . "'
								AND eba.`valid_to` >= '" . $this->date . "'
								AND eba.`status` = '" . $this->publishedStatus . "'
								AND eba.`base_absence_type_id`  IN (
												SELECT base_absence_type_id
												FROM link_at_to_bat
												WHERE `state_type_id` = 'a272f564576d443e7832587126b070aa')
								";

		$result = $this->getSqlResult($full_absence_sql, TRUE);
		if ($result !== \FALSE) {
			return $result['quantity'];
		}

		return FALSE;
	}


	/**
	 * Log abs status
	 *
	 * @param string $abs_check_status
	 */
	public function logEmployeeAbsStatus($abs_check_status)
	{
		$simulation = '';
		if ((isset($this->simulation)) and ($this->simulation === 'y')) {
			$simulation = ' ' . Dict::getModuleValue("ttwa-base", "simulation_in_parenthesis");
		}

		switch ($abs_check_status) {
			case '1':
				$this->writeResultArray(Dict::getModuleValue("ttwa-base", "absence_already_requested") . $simulation);
				break;
			case '2':
				$this->writeResultArray(Dict::getModuleValue("ttwa-base", "absence_already_approved") . $simulation);
				break;
			default:
				$this->writeResultArray(Dict::getModuleValue("ttwa-base", "error_in_saving_absence") . $simulation);
				break;
		}
	}


	/**
	 * Szabadság beírása
	 */
	public function insertAbsence()
	{
		$ret = FALSE;
		if ((isset($this->simulation)) and ($this->simulation === 'y')) {
			$ret = md5(time() . rand(10, 10000000) . uniqid());
			$this->writeResultArray(Dict::getModuleValue("ttwa-base", "absence_issued") . ' ' . Dict::getModuleValue("ttwa-base", "simulation_in_parenthesis"));
		} else {
			$absType = 'a272f564576d443e7832587126b070aa';
			$employee_absence_id = md5($this->getControllerId() . $this->employee_data['employee_contract_id'] . $this->date . $this->date . $absType . date('YmdHis'));

			$insert_sql = "INSERT INTO employee_absence (`employee_absence_id`, `company_id`, `employee_contract_id`, `employee_id`, `day`,
														`state_type_id`, `note`, `status`, `created_by`, `created_on` )
							VALUES('" . $employee_absence_id . "',
									'" . $this->employee_data['company_id'] . "',
									'" . $this->employee_data['employee_contract_id'] . "',
									'" . $this->employee_data['employee_id'] . "',
									'" . $this->date . "',
									'" . $absType . "',
									'',
									'" . $this->abs_status . "',
									'absenceExpenditure',
									NOW()
									)
							;";

			try {

				dbExecute($insert_sql);
				$ret = $employee_absence_id;
				$text = $this->abs_status == '2' ? Dict::getModuleValue("ttwa-base", "absence_issued_and_approved") : Dict::getModuleValue("ttwa-base", "absence_issued");
				$this->writeResultArray($text);
			} catch (\Exception $e) {
				Yang::log($e->getMessage(), 'log', 'system');
				$this->writeResultArray(Dict::getModuleValue("ttwa-base", "error_in_issue_of_absence"));
			}
		}

		return $ret;
	}


	/**
	 * Adottnapra van-e már szabadsága a dolgozónak
	 * @return boolean
	 */
	function checkABS()
	{
		$abs_check = $this->getEmployeeAbsByDate();
		if ($abs_check === FALSE) {
			// nincs szabadság az adott napra
			return false;
		} else {
			// van szabadság az adott napra
			$this->logEmployeeAbsStatus($abs_check['status']);
			return true;
		}
	}


	/**
	 * Adott napra van-e már a dolgozónak kiadott szabadsága
	 *
	 * @param boolean $revert
	 */
	public function getEmployeeAbsByDate($revert = FALSE)
	{
		$and = "";
		if ($revert === TRUE) {
			$and = " AND ea.created_by LIKE '%absenceExpenditure%' ";
		}
		$publishedStatus = Status::PUBLISHED;
		$draftStatus = Status::DRAFT;
		$sql = "SELECT * FROM employee_absence ea
				WHERE ea.`employee_contract_id` = '" . $this->employee_data['employee_contract_id'] . "'
				AND ea.`day` = '" . $this->date . "'
				AND ea.`status` IN ($draftStatus, $publishedStatus)
				$and;";


		$result = $this->getSqlResult($sql, TRUE);

		return $result;
	}


	/**
	 * Eredmény tömb írása
	 *
	 * @param string $msg
	 */
	public function writeResultArray($msg)
	{
		$this->table_row++;
		$this->result_array[] = [
			'table_row'			=> $this->table_row,
			'employee_name'		=> $this->employee_data['empl_name'],
			'emp_id'			=> $this->employee_data['emp_id'],
			'company_name'		=> $this->employee_data['company_name'],
			'msg'				=> $msg
		];
	}


	/**
	 * Többsoros beszúrás
	 *
	 * @param array $data
	 */
	public function multiInsert($data = [])
	{
		dbMultiInsert('temp_absence_expenditure', $data);
	}


	/**
	 * SQL futtatás, és eredmény visszaadása
	 *
	 * @param string $sql
	 * @param bool $one_row
	 * @return string
	 */
	public function getSqlResult($sql, $one_row = FALSE)
	{
		$ret = FALSE;
		try {
			if ($one_row === FALSE) {
				$result = dbFetchAll($sql);
			} else {
				$result = dbFetchRow($sql);
			}
		} catch (\Exception $e) {
			Yang::log($e->getMessage(), 'log', 'system');
		}

		if ($result) {
			$ret = $result;
		}

		return $ret;
	}


	/**
	 * temp tábla létrehozása (mivel ebből olvassuk ki a grid adatokat)
	 */
	public function create_temp_table()
	{
		$sql = "DROP TABLE IF EXISTS `temp_absence_expenditure` ";
		dbExecute($sql);

		//TEMPORARY
		$sql = "CREATE TEMPORARY TABLE IF NOT EXISTS `temp_absence_expenditure` (
                        `table_row` INT(11) NOT NULL AUTO_INCREMENT,
                        `employee_name` VARCHAR(150) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',
                        `emp_id` VARCHAR(150) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',
                        `company_name` VARCHAR(150) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',
                        `msg` VARCHAR(250) NULL DEFAULT NULL COLLATE 'utf8_unicode_ci',

                        PRIMARY KEY (`table_row`)
                )
                COLLATE='utf8_unicode_ci'
                ENGINE=InnoDB;";

		dbExecute($sql);
	}


	public function checkPublicHoliday()
	{
		$sql = "SELECT ph.`holidaydate`, ph.`name`, ph.`type`, IF(ph.`type` IN ('1','2','3','4'), ph.`name`, '') AS paidholiday
				FROM public_holiday ph
				WHERE
				ph.`holidaydate` = '" . $this->date . "'
				AND ph.`type` IN ('1','2','3','4')
				AND ph.`status` = '" . $this->publishedStatus . "'
				;";

		$result = $this->getSqlResult($sql, TRUE);

		return $result;
	}

	/**
	 * Megmondja egy évre, hogy adott contract id-hez kapcsolódóan mekkora a szabadság keret, mennyi lett ebből elhasználva és hogy mennyi maradt még.
	 *
	 * @param $employeeContractId
	 * @param $yearFilter
	 * @return array
	 */
	private function getFrameBalanceByContractIdAndYear($employeeContractId, $yearFilter)
	{
		$settingAbsenceCalculationHour	= App::getSetting("absence_calculation_hour");
		$settingAbsenceFloorRounding		= App::getSetting("absence_floor_rounding");
		$stateTypeId	= App::getSetting('getBaseAbsencesByStateType');
		$baseAbsences	= AHPAbsenceFunctions::getBaseAbsencesByStateType($stateTypeId);
		$frame			= AHPEmployeeFunctions::getEmployeeAbsenceFrameByEcID($employeeContractId, $yearFilter, $baseAbsences);
		$used			= AHPEmployeeFunctions::getEmployeeAbsenceUsedByEcID($employeeContractId, $yearFilter, false, $baseAbsences);
		$dailyWorktime	= AHPEmployeeFunctions::getEmployeeDailyWorktime($employeeContractId, true, $yearFilter);

		if ($settingAbsenceCalculationHour == '1' && $settingAbsenceCalculationHour === '0') {
			$frame = round(($frame / $dailyWorktime), 1);
			//ha órában számolja a szabadságot, rosszul számolja a visszakapott értéket, fv-ben dw daily_worktime helyett
			//$used = round( ($used / $dailyWorktime), 1);
		}

		$available = round(($frame - $used), 2);

		//szabadság lefelé kerekítés
		if ($settingAbsenceFloorRounding == '1') {
			$frame		= floor($frame);
			$used		= floor($used);
			$available	= floor($available);
		}

		return compact("frame", "used", "available");
	}
}