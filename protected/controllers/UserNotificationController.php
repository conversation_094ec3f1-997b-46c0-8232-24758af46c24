<?php

/**
 * User notification controller
 */
class UserNotificationController extends Grid2Controller
{
    const LVL_INFO = 1;
    const LVL_WARNING = 2;
    const LVL_DANGER = 3;
    const LVL_SUCCESS = 4;

    public function __construct()
    {

    }

    /**
     * Get unread user notification by user id
     *
     * @param string $userId
     * @return void
     */
    public function actionGetUnreadNotifications(string $userId = '')
    {
        $userNotification    = new UserNotification();
        $criteria            = new CDbCriteria();
        $criteria->order     = "created_on DESC";
        $criteria->condition = "`valid` = 1 AND `is_read` = 0 AND `user_id` = '$userId'";
        $notifications       = $userNotification->findAll($criteria);

        foreach ($notifications as $notification)
        {
            $text = json_decode($notification->notification);

            $results[] = [
             "id"			=> $notification->row_id,
             "level" 		=> $notification->level,
             "date"      	=> $notification->created_on,
             "text"	        => $text->notification,
             "button"	    => Dict::getValue("alteration"),
             "actionUrl"	=> $notification->action_url,
             "actionAltUrl"	=> $notification->action_alt_url
            ];
        }

        echo json_encode($results);
    }

    /**
     * Set notification to read
     *
     * @param string $id
     * @return string
     * @throws \yii\db\Exception
     */
    public function actionSetNotificationRead()
    {
        $id                    = (int)requestParam('id', '0');
        $notification          = UserNotification::model()->findByPk($id);
        $transaction           = Yang::app()->db->beginTransaction();
        $commitedItemCount     = 0;
        $userId                = userId();

        if ($userId === $notification->user_id) {

            try {
                $notification->is_read = 1;
                $notification->save();
                $transaction->commit();
                $commitedItemCount++;
            } catch (Exception $e) {
                $transaction->rollback();
                Yang::log('Update user notification error: ' . $e->getMessage());
            }
        }
        $result = ['text' => 'Update user notification items: ' . $commitedItemCount];

        echo json_encode($result);

    }

    /**
     * Check users where expired password
     *
     * @return string
     * @throws \yii\db\Exception
     */
    public function actionCheckUserExpiredPassword()
    {
        $daynum            = (int)App::getSetting("warningUserPasswordExpireDays");
        $commitedItemCount = 0;

        $SQL = "
            SELECT
                u.user_id AS user_id,
                DATEDIFF((u.`password_date` + INTERVAL 6 MONTH),CURDATE()) AS days,
                u.lang AS lang
            FROM
                `user` u 
            WHERE
                    u.`status` = " . Status::PUBLISHED . "
                AND (CURDATE() BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
                AND u.`receive_email` = '1'
                AND u.`disable_ldap_auth` = '1'
                AND DATEDIFF((u.`password_date` + INTERVAL 6 MONTH),CURDATE()) <= " . $daynum . "
        ";

        $results = dbFetchAll($SQL);

        if (count($results) > 0) {

            $transaction       = Yang::app()->db->beginTransaction();

            try {
                for ($i = 0; $i < count($results); $i++) {

                    $result                       = $results[$i];
                    $lang                         = $result["lang"] ?? "en";
                    $notification                 = [];
                    $notification['notification'] = $result["days"] > 0 ? Dict::getValueWithLang('notificationWarningPasswordAlteration',$lang,['expirationDay' => $result["days"]]) : Dict::getValueWithLang('notificationWarningPasswordAlterationToday', $lang);
                    $notification['email']        = '<br/><br/>';
                    $notification['email']        .= $result["days"] > 0 ? Dict::getValueWithLang('notificationWarningExpiringPasswordEmailBody',$lang,['expirationDay' => $result["days"]]) : Dict::getValueWithLang('notificationWarningExpiringPasswordTodayEmailBody', $lang);
                    $notification['email']        .= '<br/><br/>';

                    if ($daynum >= (int)$result["days"]) {
                        $item = [
                            'userId'                 => $result["user_id"],
                            'userNotificationTypeId' => "passwordExpire",
                            'notification'           => json_encode($notification, JSON_UNESCAPED_UNICODE),
                            'actionUrl'              => App::getSetting("actionChangeUserPassword"),
                            'actionAltUrl'           => App::getSetting("actionAltChangeUserPassword"),
                            'level'                  => self::LVL_DANGER,
                            'send_email'             => 1
                        ];

                        $this->createItem($item);
                        $commitedItemCount++;
                   }
                }
                $transaction->commit();
            } catch (Exception $e) {
                $transaction->rollback();
                $commitedItemCount = 0;
                Yang::log('Create user notification error: ' . $e->getMessage());
            }
        }
        $result = ['text' => 'Created user notification items: ' . $commitedItemCount ];

        echo json_encode($result);
    }

    /**
     * Create user notification item
     *
     * @param array $item
     * @return void
     */
    private function createItem(array $item)
    {
        $userNotification = new UserNotification();
        $userNotification->user_id                   = $item["userId"];
        $userNotification->user_notification_type_id = $item["userNotificationTypeId"];
        $userNotification->notification              = $item["notification"];
        $userNotification->action_url                = $item["actionUrl"];
        $userNotification->action_alt_url            = $item["actionAltUrl"];
        $userNotification->level                     = $item["level"];
        $userNotification->send_email                = $item["send_email"];
        $userNotification->save();
    }
}
