<?php

class QuestionnaireManagementController extends Grid2Controller
{
    const MAX_POSSIBLE_ANSWERS = 5;

	private string $defaultEnd;
	private int $publishedStatus;

	public function __construct()
	{
		parent::__construct("questionnaireManagement");

		$assetsPath = Yang::addAsset(Yang::getAlias('application.assets.base.questionnaireManagement'));
		Yang::registerCssFile($assetsPath.'/css/questionnaireManagement.css');

		$this->defaultEnd				= App::getSetting("defaultEnd");
		$this->publishedStatus 			= Status::PUBLISHED;
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("QuestionnaireManagement");

		parent::setControllerPageTitleId("page_title_questionnaire_management");

		$this->LAGridRights->overrideInitRights("paging", true);
		$this->LAGridRights->overrideInitRights("search_header", true);
		$this->LAGridRights->overrideInitRights("select", true);
		$this->LAGridRights->overrideInitRights("col_sorting", true);
		$this->LAGridRights->overrideInitRights("reload_sortings", true);
        $this->LAGridRights->overrideInitRights("add", true);
        $this->LAGridRights->overrideInitRights("modify", true);
        $this->LAGridRights->overrideInitRights("delete", true);
        $this->LAGridRights->overrideInitRights("export_xlsx",		true);
		
		$model = new QuestionnaireManagement;
		$crit = new CDbCriteria();
		$crit->condition = "`status` = {$this->publishedStatus} AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->defaultEnd}')";
		$crit->order = "`questionnaire_name`, `question_order`";

		$this->LAGridDB->setModelSelection($model, $crit);

		parent::setExportFileName(Dict::getValue("export_file_questionnaire_management"));
		parent::G2BInit();
	}

	public function columns()
	{
        $columns = [
            'questionnaire_id'	                    => ['grid' => true, 'width' => 200, 'window' => true, 'export' => true, 'col_type'=>'ed'],
		    'questionnaire_name'	                => ['grid' => true, 'width' => 300, 'window' => true, 'export' => true, 'col_type'=>'ed'],
		    'question_order'		                => ['grid' => true, 'width' => 200, 'window' => true, 'export' => true, 'col_type'=>'ed', 'line_break' => true],
		    'question_text'			                => ['grid' => true, 'width' => 600, 'window' => true, 'export' => true, 'col_type'=> 'txt', 'cssClass' => 'wrap', 'dialog_width'=> '2']
        ];

		for ($i = 1; $i <= (self::MAX_POSSIBLE_ANSWERS); $i++) 
		{
			$columns["question_answer_text_$i"]     = ['grid' => true, 'width' => 300, 'window' => true, 'export' => true, 'col_type'=> 'txt', 'cssClass' => 'wrap', 'dialog_width'=> '2', 'line_break' => true];
			$columns["question_answer_is_right_$i"] = ['grid' => true, 'width' => 200, 'window' => true, 'export' => true, 'col_type'=>'ed'];
		}

        $columns['valid_from']		                = ['grid' => true, 'width' => 200, 'window' => true, 'export' => true, 'col_type'=>'ed', 'line_break' => true];
        $columns['valid_to']		                = ['grid' => true, 'width' => 200, 'window' => true, 'export' => true, 'col_type'=>'ed'];

		return $columns;
    }
}
