<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\Controller;
	use app\components\Dict;
	use app\components\EmailSender;
	use yii\filters\AccessControl;
	use Yang;

`/yii2-only';

class ErrorController extends Controller
{
	private $customer;

	/**
	 * This is the default 'index' action that is invoked
	 * when an action is not explicitly requested by users.
	 */
	public function actionIndex($id = null)
	{
		// Set the base layout to the full layout
		$this->layout = '//layouts/full';

		$error = Yii::app()->errorHandler->error;

        if (preg_match("/^\/rest\//", $_ENV["REQUEST_URI"]??"")){
            return;
        }

		// Get the image PATH
		$image = Yii::app()->baseUrl . "/images/errors/error{$error['code']}.png";

		// Set the error message for the log
		$error_message = "Error code: {$error['code']} {$error['message']} in {$error['file']}:{$error['line']}";
		$mailMessage = $error_message;
		switch ($error['code']) {
				case 404:
					Yang::log($error_message, "error", $error['type']);
					$this->render('error404', [
						'image' => $image,
						'error_message' => Dict::getValue('error_404'),
						'error_code'	=> $error['code'],
						'home_page'		=> Dict::getValue('home_page'),
					]);
					break;

				case 500:
					if ($error['message'] == 'CDbConnection failed to open the DB connection.') {
						$errorMessage = 'Database connection error / Adatbázis kapcsolat hiba';
						$homePage = 'Home Page / Kezdő lap';
						$sendEmail = false;
					} else {
						$errorMessage = Dict::getValue('error_500');
						$homePage = Dict::getValue('home_page');
						$sendEmail = true;
					}

					Yang::log($error_message, "error", $error['type']);
					foreach ($error["traces"] as $err) {
						$mailMessage .= "\n" . $err["file"] . " line: " . $err["line"];
					}
					if ($sendEmail) {
						$this->sendMail($mailMessage);
					}
					$this->render('error500', [
						'image' => $image,
						'error_message' => $errorMessage,
						'error_code'	=> $error['code'],
						'home_page'		=> $homePage,
					]);
				break;

				default:
					// Get the image PATH for the errors are not 404 or 500
					$image = Yii::app()->baseUrl . "/images/errors/default_error.png";
					Yang::log($error_message, "error", $error['type']);
					if ($error['code'] != 403) {
						$this->sendMail($mailMessage);
					}
					$this->render('index', [
						'image' => $image,
						'error_message' => Dict::getValue('default_error'),
						'error_code'	=> $error['code'],
						'home_page'		=> Dict::getValue('home_page'),
					]);
					break;
			}
	}

	private function sendMail(&$mailMessage)
	{
		$this->customer = Yang::getParam('customerDbPatchName');

		$emailAttrs = [
			'emailTitle' => $this->customer . ' - error',
			'emailLayout' => [
				'folder'	=> 'application.views.email',
				'view'		=> 'messageemail',
			],
			'emailParams' => [
				'recipient_fullname'	=> "Login",
				'sender_fullname'		=> $this->customer,
				'theme'					=> Dict::getValue("error"),
				'generalContent'		=> "",
				'message'				=> &$mailMessage,
				'sw_link'              => Yang::getParam('serverUrl'),
				'message_created_on'	=> date("Y-m-d H:i:s"),
			],
		];
		$mail = new EmailSender();
		$mail->sendMail(
			/* $addresses */
			[
				'addr' => [
					[
						'email'	=> "<EMAIL>",
						'name'	=> "dr. Talján András"
					],
				],
			],
			/* $subject */
			$emailAttrs['emailTitle'],
			/* $message */
			null,
			/* $view */
			[$emailAttrs['emailLayout']['folder'], $emailAttrs['emailLayout']['view']],
			/* $vars */
			$emailAttrs['emailParams']
		);
	}
}
