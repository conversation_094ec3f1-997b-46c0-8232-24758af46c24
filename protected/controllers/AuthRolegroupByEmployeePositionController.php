<?php

class AuthRolegroupByEmployeePositionController extends Grid2HistoryController
{
	private $user;
	private $defaultEnd;
	private $statusPublished;

	public function __construct()
	{
		parent::__construct("authRolegroupByEmployeePosition");
		$this->user    = userID();
		$this->statusPublished = Status::PUBLISHED;
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("AuthRolegroupByEmployeePosition");

		parent::setControllerPageTitleId("page_title_auth_rolegroup_by_employee_position");

		$this->LAGridRights->overrideInitRights("paging", true);
		$this->LAGridRights->overrideInitRights("search", true);
		$this->LAGridRights->overrideInitRights("search_header", true);
		$this->LAGridRights->overrideInitRights("select", true);
		$this->LAGridRights->overrideInitRights("column_move", true);
		$this->LAGridRights->overrideInitRights("reload_sortings", true);
		$this->LAGridRights->overrideInitRights("details", false);
		$this->LAGridRights->overrideInitRights("column_move", true);
		$this->LAGridRights->overrideInitRights("col_sorting", true);
		$this->LAGridRights->overrideInitRights("add", true);
		$this->LAGridRights->overrideInitRights("modify", true);
		$this->LAGridRights->overrideInitRights("delete", true);
		$this->LAGridDB->enableSQLMode();

		$SQL = "
			SELECT *
			FROM auth_rolegroup_by_employee_position
			WHERE `status` = " . $this->statusPublished . "
			ORDER BY `rolegroup_id`
		";
		$this->LAGridDB->setSQLSelection($SQL, "row_id");

		parent::G2BInit();
	}

	public function search()
	{
		return [];
	}

	/**
	 * @return array customized grid columns (name=>params)
	 */
	public function columns()
	{
		$roleGroupSQL = "SELECT rolegroup_id AS id, rolegroup_name AS value FROM auth_rolegroup WHERE visibility = 1 ORDER BY value";
		$employeePositionSQL = "
				SELECT
					employee_position_id AS id,
					employee_position_name AS value
				FROM
					employee_position
				WHERE
						status = " . $this->statusPublished . "
					AND CURDATE() BETWEEN valid_from AND valid_to
				ORDER BY value";

		return [
			'rolegroup_id'		 => ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'combo',
										'options'	=>	[
											'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
											'sql'	=> $roleGroupSQL,
											'array'	=> []
										],
			],
			'employee_position_id'	=> ['grid'=>true, 'width'=>200, 'window'=>true, 'export'=> true, 'col_type'=>'combo',
										'options'	=>	[
											'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
											'sql'	=> $employeePositionSQL,
										],
			],
		];
	}

	/**
	 * Kereső értékek átadása sessionnek az oszlopok felépítéséhez
	 * @return void
	 */
	public function actionSetInitProperties()
	{
		Yang::setSessionValue("AuthRolegroupByEmployeePosition_filters", requestParam('searchInput'));

		parent::actionSetInitProperties();
	}

	
}
