<?php

/**
 * <PERSON><PERSON><PERSON><PERSON>, mely megmutatja milyen es<PERSON>közökről léptek be a softwarebe kategória szerint.
 *
 * Innote: report-user-device-type
 */
class ReportUserDeviceTypeController extends Grid2Controller
{
    /**
     * Világ vége app setting
     * @var string
     */
    private string $defaultEnd;
    /**
     * Aktív státusz
     * @var integer
     */
    private int $publishedStatus = Status::PUBLISHED;

    /**
     * Grid2 konstruktor, app setting beállítás
     */
	public function __construct() {
		parent::__construct("reportUserDeviceType");
		$this->defaultEnd = App::getSetting("defaultEnd");
        $this->maxDays    = 3650;
	}

    /**
     * Grid2 beállítások, használt mód: SQL
     * @return void
     */
	protected function G2BInit() :void
	{
		parent::setControllerPageTitleId("page_title_report_user_device_type");

		$this->LAGridRights->overrideInitRights("paging",			    true);
		$this->LAGridRights->overrideInitRights("search",			    true);
		$this->LAGridRights->overrideInitRights("search_header",	    true);
		$this->LAGridRights->overrideInitRights("select",			    false);
		$this->LAGridRights->overrideInitRights("multi_select",		    false);
		$this->LAGridRights->overrideInitRights("column_move",		    true);
		$this->LAGridRights->overrideInitRights("col_sorting",		    true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	    true);
		$this->LAGridRights->overrideInitRights("details",			    false);
		$this->LAGridRights->overrideInitRights("export_xls",		    false);
		$this->LAGridRights->overrideInitRights("export_xlsx",		    true);
		$this->LAGridRights->overrideInitRights("export_pdf_node",    	false);
		$this->LAGridRights->overrideInitRights("init_open_search",	    true);
		$this->LAGridRights->overrideInitRights("modify",	            false);
		$this->LAGridRights->overrideInitRights("add",	                false);
		$this->LAGridRights->overrideInitRights("delete",	            false);
		$this->LAGridDB->enableSQLMode();

		$this->LAGridDB->setSQLSelection($this->getSQLForGridData(), "row_id");
		parent::setExportFileName(Dict::getValue("page_title_report_user_device_type"));
		parent::G2BInit();
	}

	/**
	 * Grid adattartalmat visszaadó SQL
	 * @return string
	 */
	private function getSQLForGridData() :string
	{
        $name   = Employee::getParam("fullname");
        $SQL    = "
            SELECT
                IF(`employee`.`row_id` IS NOT NULL, {$name}, '') AS fullname,
                IF(`employee`.`row_id` IS NOT NULL, `employee`.`emp_id`, '') AS emp_id,
                `user`.`username`,
                `user_device_log`.`device_type`,
                `user_device_log`.`login_time` AS event_time,
                '" . Dict::getValue("no") . "' AS is_gtc_approve_time
            FROM `user_device_log`
            LEFT JOIN `user` ON
                    `user`.`user_id` = `user_device_log`.`user_id`
                AND `user`.`status` = {$this->publishedStatus}
                AND DATE(`user_device_log`.`login_time`) BETWEEN `user`.`valid_from` AND IFNULL(`user`.`valid_to`, '{$this->defaultEnd}')
            LEFT JOIN `employee` ON
                    `employee`.`employee_id` = `user`.`employee_id`
                AND `employee`.`status` = {$this->publishedStatus}
                AND DATE(`user_device_log`.`login_time`) BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->defaultEnd}')
            WHERE 
					`user_device_log`.`status` = {$this->publishedStatus} 
				AND DATE(`user_device_log`.`login_time`) BETWEEN '{valid_from}' AND '{valid_to}'
            UNION
            SELECT
                IF(`employee`.`row_id` IS NOT NULL, {$name}, '') AS fullname,
                IF(`employee`.`row_id` IS NOT NULL, `employee`.`emp_id`, '') AS emp_id,
                `user`.`username`,
                IFNULL(`user_gtc_log`.`device_type`, 'Ismeretlen / Unknown') AS device_type,
                `user_gtc_log`.`approved_on` AS event_time,
                '" . Dict::getValue("yes") . "' AS is_gtc_approve_time
            FROM `user_gtc_log`
            LEFT JOIN `user` ON
                    `user`.`user_id` = `user_gtc_log`.`user_id`
                AND `user`.`status` = {$this->publishedStatus}
                AND DATE(`user_gtc_log`.`approved_on`) BETWEEN `user`.`valid_from` AND IFNULL(`user`.`valid_to`, '{$this->defaultEnd}')
            LEFT JOIN `employee` ON
                    `employee`.`employee_id` = `user`.`employee_id`
                AND `employee`.`status` = {$this->publishedStatus}
                AND DATE(`user_gtc_log`.`approved_on`) BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->defaultEnd}')
            WHERE DATE(`user_gtc_log`.`approved_on`) BETWEEN '{valid_from}' AND '{valid_to}'
        ";
		return $SQL;
	}

    /**
     * Érvényesség eleje vége kereső
     * @return array
     */
	public function search() :array
	{
		return
		[
			'valid_from'    =>
            [
				'dPicker'		=> true,
				'col_type'		=> 'ed',
				'default_value'	=> date('Y-m') . '-01',
				'label_text'	=> Dict::getValue("valid_from")
			],
			'valid_to'      =>
            [
				'dPicker'		=> true,
				'col_type'		=> 'ed',
				'default_value'	=> date('Y-m-t'),
				'label_text'	=> Dict::getValue("valid_to")
			],
			'submit' => ['col_type' => 'searchBarReinitGrid', 'gridID' => 'dhtmlxGrid', 'width' => '*', 'label_text' => '']
		];
	}

    /**
     * Grid2 oszlop definiálás
     * @return array
     */
	public function columns() :array
	{
		return [
			'fullname'              => ['export'=> true, 'report_width' => 20, 'col_type' => 'ro', 'width' => 250],
			'emp_id'                => ['export'=> true, 'report_width' => 20, 'col_type' => 'ro', 'width' => 250],
			'username'				=> ['export'=> true, 'report_width' => 20, 'col_type' => 'ro', 'width' => 250],
			'device_type'			=> ['export'=> true, 'report_width' => 20, 'col_type' => 'ro', 'width' => 250],
			'event_time'		    => ['export'=> true, 'report_width' => 20, 'col_type' => 'ro', 'width' => 250],
			'is_gtc_approve_time'	=> ['export'=> true, 'report_width' => 20, 'col_type' => 'ro', 'width' => 250]
		];
	}


    /**
     * Grid2 oszlop feliratok definiálása
     * @return array
     */
	public function attributeLabels() :array
	{
		return
        [
			'fullname'              => Dict::getValue("fullname"),
			'emp_id'				=> Dict::getValue("emp_id"),
			'username'				=> Dict::getValue("username"),
			'device_type'			=> Dict::getValue("device_type"),
			'event_time'		    => Dict::getValue("login_time"),
			'is_gtc_approve_time'	=> Dict::getValue("is_event_gtc_approve")
		];
	}
}

?>