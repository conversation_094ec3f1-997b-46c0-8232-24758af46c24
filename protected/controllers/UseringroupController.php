<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\Dict;
	use Yang;

`/yii2-only';


#yii2: done

class UseringroupController extends GridController
{
	public $layout = '//layouts/main';

	public function __construct()
	{
		parent::__construct("useringroup");
		$this->setModelName("Useringroup");
		parent::setTitle(Dict::getValue("page_title_useringroup"));
		parent::enableSubgrid(false);
		parent::exportSettings(Dict::getValue("export_file_useringroup"));
	}

	public function columns()
	{
		return array(
			'usergroup_id'			=> array('export'=> true , 'col_type'=>'combo',
											 'options'=>array('comboModel'=>'Usergroup','comboId'=>'usergroup_id','comboValue'=>'usergroup_name'),
											 'width'=>'300'),
			'user_id'				=> array('export'=> true , 'col_type'=>'combo',
											 'options'=>array('comboModel'=>'User','comboId'=>'user_id','comboValue'=>'username'),
											 'width'=>'300'),
		);
	}
}
?>
