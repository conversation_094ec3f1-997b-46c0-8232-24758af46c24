<?php

ini_set('memory_limit', '2048M');
ini_set('max_execution_time', 3600);
class EmployeeGroupConfigController extends Grid2Controller
{	
	private $publishedStatus = Status::PUBLISHED;
	private $deletedStatus = Status::DELETED;
	

	public function __construct()
	{
            parent::__construct("employeeGroupConfig");
	}

	protected function G2BInit() {	
		$this->LAGridDB->setModelName("EmployeeGroupConfig");
		parent::setControllerPageTitleId("page_title_employee_group_config");
 
		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("details",			false);
		$this->LAGridRights->overrideInitRights("export_xls",		false);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);
		
		$this->LAGridDB	->enableSQLMode();

		$SQL ="
			SELECT
				egc.`row_id`,
				egc.`dict_id`,
				egc.`status`
			FROM `employee_group_config` egc
			WHERE
					egc.`status`= {$this->publishedStatus}
				OR egc.`status`={$this->deletedStatus}
			ORDER BY dict_id
			";

		$this->LAGridDB->setSQLSelection($SQL, "row_id");
		parent::G2BInit();
	}

	public function columns()
	{				
		$status=[
				['id' => $this->publishedStatus, 'value' => Dict::getValue("status_active")],
				['id' => $this->deletedStatus,  'value' => Dict::getValue("inactive")]
		];

 		$columns = ['dict_id' 	=> [
									'export'	=> 	true , 
									'align' 	=> 	'center',
									'width'		=>	'400',
									'window'    => false,
									'editable'  =>  false,
									'col_type'	=>	'combo',
									'options'	=> [
										'mode'			=> Grid2Controller::G2BC_QUERY_MODE_SQL,
										'sql'			=> "
															SELECT
																egc.`dict_id` as id,															
																d.`dict_value` as value															
															FROM `employee_group_config` egc
															LEFT JOIN `dictionary` d ON
																	egc.`dict_id`=d.`dict_id`
																AND lang='".Dict::getLang()."'
															WHERE
																d.module ='ttwa-base'
																AND	(egc.`status`={$this->publishedStatus} 
																	OR egc.`status`={$this->deletedStatus})
															ORDER BY egc.dict_id;
															",
										'array'			=>["id" => "", "value" => ""],			
									], 																		
								],
					'status' 	=> [								
									'export'	=> 	true , 
									'align' 	=> 'center',
									'width'		=>	'400',					 
									'col_type'	=> 'combo',											 
									'options'	=> [
										'mode'		=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
										'array'		=> $status,			
									], 
								]
					];

		return $columns;
	}

	public function attributeLabels()
	{
		$attributeLabels = [
			'dict_id' => Dict::getValue("type"),
			'status' => Dict::getValue("status"),
		];

		return $attributeLabels;
	}

	public function actionSave($data = array(), $modelName = null, $pk = null, $vOnly = false, $ret = false, $contentId = null)
	{		
		$id            = requestParam('editPK');		
		$status 	    = requestParam('dialogInput_dhtmlxGrid')['status'];
		$isConfirmed	= requestParam('confirmed');
		$userid			= userID();
		
		if ($status == $this->publishedStatus) {
			$this->createBackup();
			$this->copyToEmployeeGroup($this->getGroupId($id));
		} elseif ($status == $this->deletedStatus && $isConfirmed != 1){
			echo json_encode([	'status'	=> 'confirmAction', 
								'msg' 		=> ['body' => Dict::getValue('group_config_groupname',['groupname' => Dict::getValue($this->getGroupId($id))])]
							]);
			return;
		}

		$SQL = "
			UPDATE 
					`employee_group_config`
			SET 
					`status` = {$status},
					`modified_by` = '{$userid}',
					`modified_on` = NOW()
			WHERE  
					`row_id` ={$id};" ;

		dbExecute($SQL);		
		echo json_encode(['status'	=> 1]);		
	}	

	private function createBackup()
	{
		$createTempTableSql = "
				CREATE TABLE `employee_group_backup_".date("Ynt_H:i:s")."`
				SELECT *
				FROM `employee_group`;
			";
		dbExecute($createTempTableSql);
	}

	public function copyToEmployeeGroup ($groupid) 
	{
		$tableName = '';
		$groups =[
					'company_org_group1_id' 	=> 'employee',
					'company_org_group2_id'  	=> 'employee',
					'company_org_group3_id' 	=> 'employee',
					'unit_id' 					=> 'employee',
					'workgroup_id' 				=> 'employee_contract',
					'employee_position_id'		=> 'employee_contract',
					'cost_id' 					=> 'employee_cost',
					'cost_center_id'			=> 'employee_cost'
		];
		$tableName = $groups[$groupid] ?? "";

		$SQL = "
		INSERT INTO 
		employee_group (
			`employee_contract_id`, 
			`group_id`,
			`group_value`,
			`valid_from`,
			`valid_to`,
			`status`,
			`created_by`,
			`created_on`)
		SELECT
		";
		if ($tableName == 'employee')
		{
		$SQL .= "employee_contract.employee_contract_id,";
		} else {
		$SQL .= "main.employee_contract_id,";
		}
		$SQL .= "	
			'{$groupid}' as group_id, 
			main.{$groupid} as group_value, 
			main.`valid_from`, 
			main.`valid_to`, 
			main.`status`, 
			main.`created_by`, 
			main.`created_on` 
		FROM
		{$tableName} as main";
		if ($tableName == 'employee')
		{
		$SQL .= "
		JOIN 
			`employee_contract`
			ON 
					`employee_contract`.`employee_id` = main.`employee_id`
				AND main.`valid_from` <= `employee_contract`.`valid_to`
				AND `employee_contract`.valid_from <= main.`valid_to`
				AND `employee_contract`.`status` = {$this->publishedStatus}
			";
		}
		$SQL .= " 	
		WHERE 
			{$groupid} IS NOT NULL
			AND {$groupid} <> ''
			AND main.`status` = {$this->publishedStatus}
		";
		
		dbExecute($SQL);		
	}

	public function getGroupId($id) 
	{
		$SQL= "
			SELECT 
				`group_id`
			FROM
				`employee_group_config`
			WHERE
				`row_id` ={$id};" ;
		
		$result = dbFetchValue($SQL);	
		return $result;
	}

	public function accessRules()
	{
        return
		[
            [
				'allow', // allow authenticated users to access all actions
				'users' => ['root'],
            ],
            [
				'deny',  // deny all users
                'users' => ['*'],
            ],
        ];
	} 
}
?>