<?php

'yii2-only`;

	namespace app\controllers;
	use DateTime;
	use app\components\App;
	use app\components\Dict;
	use app\controllers\wfm\ExportTabController;
	use app\models\ColumnTabRights;
	use app\models\Status;
	use Yang;

`/yii2-only';


#yii2: done

/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy be<PERSON>, hogy a dolgozó kezelés felületen egy dolgozó kiválasztásánál a
 * felugró ablakon milyen fülek jelenjenek meg egy adott szerepkörcsoportnál.
 */
class EmployeeControlTabSettingController extends ExportTabController
{
	/**
	 * Connects page url with this controller.
	 * Provides column header names.
	 * 
	 * @return void 
	 */
    public function __construct()
    {
        $this->baseControllerUrl = sprintf('%s/employeeControlTabSetting', baseURL());
        $this->columnTitle1 = Dict::getModuleValue("ttwa-base", "associated_tabs_to_rolegroup");
        $this->columnTitle2 = Dict::getModuleValue("ttwa-base", "available_tabs_to_rolegroup");
    }

	/**
	 * Provides the index file which is rendered on the given url.
	 * Provides the name of the page.
	 *
	 * @return void
	 */
    public function actionIndex()
    {
        if (!App::hasRight('EmployeeControlTabSetting', 'view')) {
            $this->redirect(array(Yang::getParam('permdeniedUrl')));
        }

        $this->layout = '//Grid2/layouts/indexLayout';
        $this->pageTitle = Dict::getModuleValue("ttwa-base", Yang::appName()) . " - " . Dict::getModuleValue("ttwa-base", "page_title_employee_control_tab_setting");

        $this->render('/employeeControlTabSetting/index', array(
            'title' => Dict::getModuleValue("ttwa-base", "page_title_employee_control_tab_setting"),
            'modify' => 0,
            'modifyFields' => 1,
        ));
    }

	/**
	 * Creates the xml for the rolegroups and their associated tabs.
	 * It is rendered in the left column of the page.
	 *
	 * @return void
	 */
    public function actionGenerateTreeViewXML()
	{
		$companyName = $this->getCompanyName();

		if (stristr($_SERVER["HTTP_ACCEPT"], "application/xhtml+xml")) {
			header("Content-type: application/xhtml+xml");
		} else {
			header("Content-type: text/xml");
		}
		echo('<?xml version="1.0" encoding="utf-8" ?>' . "\n");
		echo('<tree id="0">' . "\n");

		$_SESSION['parentIds'] = array();

		$this->getParentItems();

		echo("</tree>\n");
    }
	
	/**
	 * Gets the rolegroups (parents) and renders them on the page in the left column.
	 *
	 * @return void
	 */
    protected function getParentItems (){
		$sql = "SELECT rolegroup_id as item_id, rolegroup_name as item_text FROM auth_rolegroup WHERE visibility = 1 ";
		$rows = dbFetchAll($sql);

		if(is_array($rows) && count($rows) > 0) {
			foreach($rows as $row) {
				$itemId = $row['item_id'];
				$tags = ' im0="folderClosed.gif" im1="folderOpen.gif" im2="folderClosed.gif"';

				$children = $this->getChildrenItems($itemId);

				if (empty($children)) {
					print("<item id='".$itemId."' text=\"". str_replace(['"', '&'],["&quot;", "&amp;"],$row['item_text'])."\"$tags>\n");
				} else {
					print("<item id='".$itemId."_".$row['parent_id']."' text=\"". str_replace(['"', '&'],["&quot;", "&amp;"],$row['item_text'])."\"$tags>\n");
				}

				print $children;

				print("</item>\n");
			}
		}
    }
	
	/**
	 * Gets the tabs (children) which are associated to the selected rolegroup.
	 * Renders them under the folder of the selected rolegroup.
	 *
	 * @param string $parentId	the selected rolegroup
	 * @return string $itemXML	the xml for the tabs to be rendered under the selected rolegroup folder
	 */
    protected function getChildrenItems($parentId)
	{
		$itemXML = "";
		$sql = "
                SELECT
                    column_tabs.tab_id as item_id,
                    dictionary.dict_value as item_text
                FROM column_tab_rights rights
                LEFT JOIN column_tabs
                    ON column_tabs.tab_id = rights.tab_id AND column_tabs.status = ".$this->published."
                LEFT JOIN dictionary
					ON column_tabs.dict_id = dictionary.dict_id
					AND dictionary.lang = '" .Dict::getLang(). "'
                WHERE rights.status = ".$this->published." 
                    AND rights.rolegroup_id = '".$parentId."'
                ";
		$rows = dbFetchAll($sql);

		if(is_array($rows) && count($rows) > 0) {
			foreach($rows as $row) {
				$itemId = $row['item_id'];
				$tags = ' im0="folderClosed.gif" im1="folderOpen.gif" im2="folderClosed.gif"';

				$itemXML .= "<item id='".$itemId."_".$parentId."' text=\"". str_replace(['"', '&'],["&quot;", "&amp;"],$row['item_text'])."\"$tags>\n";

				$itemXML .= "</item>\n";
			}
		}

		return $itemXML;
    }
    
    /**
	 * Gets the available tabs and the associated tabs to rolegroups.
	 * Filters the tabs according to the search parameter provided by the user.
	 * 
     * @return void
    */
    public function actionLoadSortableContents()
    {
        $selectedTreeItem = requestParam('selectedTreeItem');
        $sortable1_filter = requestParam('sortable1_filter');
        $sortable2_filter = requestParam('sortable2_filter');

        $sortable1_filter = str_replace(" ", "%%", $sortable1_filter);
        $sortable2_filter = str_replace(" ", "%%", $sortable2_filter);

		$idArr = explode("_", $selectedTreeItem);
		$treeId = $idArr[0];

		$usedFieldsSql = "
			SELECT 
                column_tabs.tab_id as field_id,
				dictionary.dict_value as field_name
			FROM column_tab_rights rights 
			LEFT JOIN column_tabs
                ON column_tabs.tab_id = rights.tab_id AND column_tabs.status = ".$this->published."
            LEFT JOIN dictionary
				ON column_tabs.dict_id = dictionary.dict_id
				AND dictionary.lang = '" .Dict::getLang(). "'
            WHERE rights.status = ".$this->published." 
                AND rights.rolegroup_id = '".$treeId."'
				AND LOWER(dictionary.dict_value) LIKE '".mb_strtolower($sortable1_filter)."%'
			ORDER BY rights.tab_order
		";

		$availableFieldsSql = "
			SELECT
				column_tabs.tab_id as field_id,
				dictionary.dict_value as field_name
			FROM column_tabs
			LEFT JOIN dictionary
				ON column_tabs.dict_id = dictionary.dict_id
				AND dictionary.lang = '" .Dict::getLang(). "'
			WHERE LOWER(dictionary.dict_value) LIKE '".mb_strtolower($sortable1_filter)."%'
				AND column_tabs.status = ".$this->published."
				AND column_tabs.tab_id NOT IN (".$this->getUsedTabIds($usedFieldsSql).")
		";

        $this->loadSortableContents($usedFieldsSql, $availableFieldsSql);
    }

    /**
	 * When user drags one item from the available column (the right column) and drops it in the associated column
	 * (the middle column), the item is saved and connected to the rolegroup which is selected in the tree (the left column).
	 * Vica versa the item is deleted from the connected rolegroup, when the item is removed from the associated column.
	 * 
     * @return void
     */
    public function actionSaveReportFieldAssign()
    {
		$model = new ColumnTabRights();
		$from = requestParam('from');
		$to = requestParam('to');
		$id = requestParam('id');
		$tabsInOrder = requestParam('tabsInOrder');
		$user_id = userID();

		$response = [
			'hasLoggedUser' => (int)!empty($user_id),
			'status' => 0,
			'id' => $id,
			'class' => get_class($model),
		];

		$this->checkIsEmptyParameter($response);
		$this->checkIncorrectParameterKeys($response);
		$this->checkIDParameterFormat($response);

		$response["status"] = 1;

		$exp = explode("_", $id);
		$exp = array_filter($exp);
		$exp = array_values($exp);

		$treeId = $exp[0];
		$tabId = $exp[1];
		$date = new DateTime();
		$dateOfYesterday = $date->modify("-1 day")->format("Y-m-d");
		

		if (!empty($user_id)) {
			// Add item to list
			if ($from === "sortable2" && $to === "sortable1") {
				$model->tab_id = $tabId;
				$model->rolegroup_id = $treeId;
				$model->status = Status::PUBLISHED;
				$model->valid_from = $dateOfYesterday;
				$model->valid_to = App::getSetting("defaultEnd");
				$model->tab_order = array_search($id, $tabsInOrder);

				try {
					$response['result'] = (int) $model->save();
					$response['message'] = $model->getErrors();
					$this->saveTabsOrderInDatabase($tabsInOrder, $model);
				} catch (Exception $ex) {
					$response['message'] = $ex->getMessage();
				}
			}
			// Change items order
			elseif ($from === "sortable1" && $to === "sortable1") {
				$this->saveTabsOrderInDatabase($tabsInOrder, $model);
			}
			// Remove item from list
			else {
				$model = $model::model()->findByAttributes(array(
					'tab_id' => $tabId,
					'rolegroup_id' => $treeId,
					'status' => Status::PUBLISHED,
				));

				if ($model) {
					$model->delete();
					$this->saveTabsOrderInDatabase($tabsInOrder, $model);
				}
			}
		}

		$this->resultJson($response);
    }

	public function checkIDParameterFormat($response)
	{
		$id = requestParam('id');
		$idArr = explode("_", $id);

		if (count($idArr) <= 1) {
			$response["error"] = 'Incorrect id parameter';
			$this->resultJson($response);
		}
	}

	private function saveTabsOrderInDatabase($tabsInOrder, $model)
	{
		if (count($tabsInOrder) > 1) {
			foreach ($tabsInOrder as $order => $tab) {
				$model = $model::model()->findByAttributes(array(
					'tab_id' => explode("__", $tab)[1],
					'rolegroup_id' => explode("__", $tab)[0],
					'status' => Status::PUBLISHED,
				));
				$model->tab_order = $order;
				$model->update();
			}
		}
	}

	/**
	 * Takes the tab ids and tab names in an array and returns the tab ids as strings
	 *
	 * @param string $usedFieldsSql	sql query to get the tabs used by the selected rolegroup
	 * @return string tab ids as strings
	 */
	private function getUsedTabIds($usedFieldsSql)
	{
		$usedTabs = dbFetchAll($usedFieldsSql);
		foreach ($usedTabs as $tab) {
			$tabIds[] = $tab['field_id'];
		}
		return "'".implode("','",$tabIds)."'";
	}
}
