<?php

class EmployeeGroupController extends Grid2Controller
{
	public function __construct()
	{
            parent::__construct("employeeGroup");
	}

	protected function G2BInit() {
		$this->LAGridDB->setModelName("EmployeeGroup");

		parent::setControllerPageTitleId("page_title_employee_group");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);

		$this->LAGridRights->overrideInitRights("export_xls",		false);
		$this->LAGridRights->overrideInitRights("export_xlsx",		false);
		$this->LAGridRights->overrideInitRights("export_pdf_node",	false);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);

		$this->LAGridDB->enableSQLMode();

		$SQL ="
			SELECT
				eg.row_id,
				".Employee::getParam('fullname_with_emp_id_ec_id',["employee","employee_contract"])." as fullname,
				eg.employee_contract_id,
				eg.group_id,
				eg.group_value,
				iv.value,
				eg.note,
				eg.valid_from,
				eg.valid_to
			FROM `employee_group` eg
			LEFT JOIN `employee_group_config` egc ON
					egc.group_id=eg.group_id
				AND egc.`status`=".Status::PUBLISHED."
			LEFT JOIN `employee_contract` ON
					employee_contract.`employee_contract_id`=eg.`employee_contract_id`
				AND employee_contract.`status`=".Status::PUBLISHED."
				AND '{valid_date}' BETWEEN employee_contract.`valid_from` and IFNULL(employee_contract.`valid_to`,'".App::getSetting("defaultEnd")."')
				AND '{valid_date}' BETWEEN employee_contract.`ec_valid_from` and IFNULL(employee_contract.`ec_valid_to`,'".App::getSetting("defaultEnd")."')
			LEFT JOIN `employee` ON
					employee.`employee_id`=employee_contract.`employee_id`
				AND employee.`status`=".Status::PUBLISHED."
				AND '{valid_date}' BETWEEN employee.`valid_from` and IFNULL(employee.`valid_to`,'".App::getSetting("defaultEnd")."')
			LEFT JOIN `company` ON
					company.`company_id`=employee.`company_id`
				AND company.`status`=".Status::PUBLISHED."
				AND '{valid_date}' BETWEEN company.`valid_from` and IFNULL(company.`valid_to`,'".App::getSetting("defaultEnd")."')
			LEFT JOIN `payroll` ON
					payroll.`payroll_id`=employee.`payroll_id`
				AND payroll.`status`=".Status::PUBLISHED."
				AND '{valid_date}' BETWEEN payroll.`valid_from` and IFNULL(payroll.`valid_to`,'".App::getSetting("defaultEnd")."')
			";
			if(EmployeeGroupConfig::isActiveGroup('unit_id'))
			{
				$SQL.=EmployeeGroup::getLeftJoinSQL('unit_id',"employee_contract","'{valid_date}'");
			}
			if(EmployeeGroupConfig::isActiveGroup('workgroup_id'))
			{
				$SQL.=EmployeeGroup::getLeftJoinSQL('workgroup_id',"employee_contract","'{valid_date}'");
			}
		$SQL.="LEFT JOIN `unit` ON
					unit.`unit_id`=".EmployeeGroup::getActiveGroupSQL('unit_id','employee')."
				AND unit.`status`=".Status::PUBLISHED."
				AND '{valid_date}' BETWEEN unit.`valid_from` and IFNULL(unit.`valid_to`,'".App::getSetting("defaultEnd")."')
			LEFT JOIN `workgroup` ON
					workgroup.`workgroup_id`=".EmployeeGroup::getActiveGroupSQL('workgroup_id','employee_contract')."
				AND workgroup.`status`=".Status::PUBLISHED."
				AND '{valid_date}' BETWEEN workgroup.`valid_from` and IFNULL(workgroup.`valid_to`,'".App::getSetting("defaultEnd")."')
			LEFT JOIN (SELECT CONCAT('workgroup_id','-',workgroup_id) as id, workgroup_name as value
						FROM workgroup
						WHERE
								`status`=".Status::PUBLISHED."
							AND CURDATE() BETWEEN `valid_from` and IFNULL(`valid_to`,'".App::getSetting("defaultEnd")."')
						UNION
						SELECT CONCAT('unit_id','-',unit_id) as id, unit_name as value
						FROM unit
						WHERE
								`status`=".Status::PUBLISHED."
							AND CURDATE() BETWEEN `valid_from` and IFNULL(`valid_to`,'".App::getSetting("defaultEnd")."')
						UNION
						SELECT CONCAT('company_org_group1_id','-',company_org_group_id) as id, company_org_group_name as value
						FROM company_org_group1
						WHERE
								`status`=".Status::PUBLISHED."
							AND CURDATE() BETWEEN `valid_from` and IFNULL(`valid_to`,'".App::getSetting("defaultEnd")."')
						UNION
						SELECT CONCAT('company_org_group2_id','-',company_org_group_id) as id, company_org_group_name as value
						FROM company_org_group2
						WHERE
								`status`=".Status::PUBLISHED."
							AND CURDATE() BETWEEN `valid_from` and IFNULL(`valid_to`,'".App::getSetting("defaultEnd")."')
						UNION
						SELECT CONCAT('company_org_group3_id','-',company_org_group_id) as id, company_org_group_name as value
						FROM company_org_group3
						WHERE
								`status`=".Status::PUBLISHED."
							AND CURDATE() BETWEEN `valid_from` and IFNULL(`valid_to`,'".App::getSetting("defaultEnd")."')) iv ON
				CONCAT(eg.group_id,'-',eg.group_value)=iv.id
			";
			$art = new ApproverRelatedGroup;
			$gargSQL= $art->getApproverReleatedGroupSQL("Employee", "employeeManagement");

			$SQL.=$gargSQL["join"];
		$SQL.="WHERE
					eg.`status`=".Status::PUBLISHED."
				AND egc.`status`=".Status::PUBLISHED."
				AND (employee.`company_id` like '{company}' OR '{company}' = 'ALL' OR '{company}' = '' OR employee.`company_id` like 'ALL')
				AND (employee.`payroll_id` like '{payroll}' OR '{payroll}' = 'ALL' OR '{payroll}' = '' OR employee.`payroll_id` like 'ALL')
				AND (employee_contract.`employee_contract_id` like '{employee_contract}' OR '{employee_contract}' like 'ALL' OR '{employee_contract}' = '')
				AND (unit.`unit_id` like '{unit}' OR '{unit}' = 'ALL'  OR '{unit}' = '' OR unit.`unit_id` like 'ALL')
				AND (workgroup.`workgroup_id` like '{workgroup}' OR '{workgroup}' = 'ALL'  OR '{workgroup}' = '' OR workgroup.`workgroup_id` like 'ALL')
				AND '{valid_date}' BETWEEN eg.`valid_from` and IFNULL(eg.`valid_to`,'".App::getSetting("defaultEnd")."')
		";
		$SQL.=$gargSQL["where"];
		$SQL.="ORDER BY fullname";

		$this->LAGridDB->setSQLSelection($SQL, "row_id");
		parent::G2BInit();
	}

	public function search()
	{
		$submit = ['submit'		=> ['col_type' => 'searchBarReloadGrid', 'gridID' => 'dhtmlxGrid', 'width' => '*', 'label_text' => ''], ];
		return Yang::arrayMerge($this->getPreDefinedSearch(Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_DATE, Grid2Controller::G2BC_SEARCH_WITH_DEFAULT_GROUP_FILTER, "employeeManagement"), $submit);
	}

	public function columns()
	{
		return array(
			'fullname'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 300,'window' => false,),
			'employee_contract_id'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'combo','edit'=>false,'grid' => false,
					'options'			=>	array(
																		'mode'			=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																		'sql'			=> "SELECT employee_contract_id as id, ".Employee::getParam('fullname_with_emp_id_ec_id',["e","ec"])." as value
																							FROM employee_contract ec
																							LEFT JOIN `employee` e ON
																									e.`employee_id`=ec.employee_id
																								AND e.`status`=".Status::PUBLISHED."
																								AND CURDATE() BETWEEN e.`valid_from` and IFNULL(e.`valid_to`,'".App::getSetting("defaultEnd")."')
																							WHERE
																									ec.`status`=".Status::PUBLISHED."
																								AND CURDATE() BETWEEN ec.`valid_from` and IFNULL(ec.`valid_to`,'".App::getSetting("defaultEnd")."')
																								AND CURDATE() BETWEEN ec.`ec_valid_from` and IFNULL(ec.`ec_valid_to`,'".App::getSetting("defaultEnd")."')
																							",),
				),
			'group_id'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'combo','width' => 300,'onchange' => ['group_value'],
					'options'			=>	array(
																		'mode'			=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																		'sql'			=> "SELECT group_id as id, dict_value as value
																							FROM employee_group_config egc
																							LEFT JOIN `dictionary` d ON
																									egc.`dict_id`=d.`dict_id`
																								AND lang='".Dict::getLang()."'
																							WHERE
																									`status`=".Status::PUBLISHED."
																							",),
				),
			'group_value'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'combo','width' => 300, 'grid' => false,
					'options'			=>	array(
																		'mode'			=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																		'sql'			=> "SELECT workgroup_id as id, workgroup_name as value
																							FROM workgroup
																							WHERE
																									`status`=".Status::PUBLISHED."
																								AND 'workgroup_id'='{group_id}'
																								AND CURDATE() BETWEEN `valid_from` and IFNULL(`valid_to`,'".App::getSetting("defaultEnd")."')
																							UNION
																							SELECT unit_id as id, unit_name as value
																							FROM unit
																							WHERE
																									`status`=".Status::PUBLISHED."
																								AND 'unit_id'='{group_id}'
																								AND CURDATE() BETWEEN `valid_from` and IFNULL(`valid_to`,'".App::getSetting("defaultEnd")."')
																							UNION
																							SELECT company_org_group_id as id, company_org_group_name as value
																							FROM company_org_group1
																							WHERE
																									`status`=".Status::PUBLISHED."
																								AND 'company_org_group1_id'='{group_id}'
																								AND CURDATE() BETWEEN `valid_from` and IFNULL(`valid_to`,'".App::getSetting("defaultEnd")."')
																							UNION
																							SELECT company_org_group_id as id, company_org_group_name as value
																							FROM company_org_group2
																							WHERE
																									`status`=".Status::PUBLISHED."
																								AND 'company_org_group2_id'='{group_id}'
																								AND CURDATE() BETWEEN `valid_from` and IFNULL(`valid_to`,'".App::getSetting("defaultEnd")."')
																							UNION
																							SELECT company_org_group_id as id, company_org_group_name as value
																							FROM company_org_group3
																							WHERE
																									`status`=".Status::PUBLISHED."
																								AND 'company_org_group3_id'='{group_id}'
																								AND CURDATE() BETWEEN `valid_from` and IFNULL(`valid_to`,'".App::getSetting("defaultEnd")."')
																							",),
				),
			'value'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 300, 'window' => false),
			'valid_from'	=> array('export'=> true, 'dPicker'=>true, 'report_width' => 20, 'col_type'=>'ed','width' => 150),
			'valid_to'	=> array('export'=> true, 'dPicker'=>true, 'report_width' => 20, 'col_type'=>'ed','width' => 150),
			'note'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => '*'),
		);
	}

	public function attributeLabels()
	{
		return array(
			'fullname' => Dict::getValue("name"),
			'group_id' => Dict::getValue("type"),
			'value' => Dict::getValue("value"),
			'note' => Dict::getValue("note"),
			'valid_from' => Dict::getValue("valid_from"),
			'valid_to' => Dict::getValue("valid_to"),
		);
	}
}
?>