<?php

class CostDictionaryController extends Grid2Controller
{
	private $publishedStatus = Status::PUBLISHED;

	public function __construct()
	{
		parent::__construct("costDictionary");
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("CostDictionary");

		parent::setControllerPageTitleId("page_title_cost_dictionary");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			false);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		true);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("add",				true);
		$this->LAGridRights->overrideInitRights("modify",			true);
		$this->LAGridRights->overrideInitRights("delete",			true);
		$this->LAGridDB->enableSQLMode();

		$SQL = "
			SELECT 
				cd.row_id,
				cd.id,
				cd.name,
				cd.note,
				IFNULL(" . Employee::getParam('fullname', 'e') . ", IFNULL(u.username, cd.created_by)) AS created_by,
				cd.created_on
			FROM `cost_dictionary` cd
			LEFT JOIN user u ON
					u.user_id = cd.created_by
				AND u.status = " . $this->publishedStatus . "
				AND CURDATE() BETWEEN u.valid_from AND u.valid_to
			LEFT JOIN employee e ON
					e.employee_id = u.employee_id
				AND e.status = " . $this->publishedStatus . "
				AND CURDATE() BETWEEN e.valid_from AND e.valid_to
			WHERE 
				cd.`status` = " . $this->publishedStatus . "
			ORDER BY `id`
		";
		$this->LAGridDB->setSQLSelection($SQL, "row_id");

		parent::G2BInit();
	}

	public function columns()
	{
		return [
			'id'			=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'width' => '250'],
			'name'			=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'width' => '400'],
			'note'			=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'width' => '*', 'line_break' => true],
			'created_by'	=> ['grid' => true, 'window' => false, 'export' => true, 'col_type' => 'ro', 'width' => '300'],
			'created_on'	=> ['grid' => true, 'window' => false, 'export' => true, 'col_type' => 'ro', 'width' => '300'],
		];
	}
}
?>