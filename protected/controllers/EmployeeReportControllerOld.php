<?php

ini_set('memory_limit', '512M');
ini_set('max_execution_time', 3600);

/**
 * A dolgozókezelésben a dolgozóhoz tartozó alap adatot megjelenítő kimutatás
 * Ha két ugyanolyan nevű column van különböző tá<PERSON>l<PERSON>, ak<PERSON> elhhhhh, mivel egy temp táblába rakja össze az adatokat így név ütközés lesz.
 */
class EmployeeReportController extends Grid2Controller
{
	private $publishedStatus;
	private $defaultEnd;
	private $lang;
	private $isCertificateIdShown;
	private $orderBy;
	private $firstContractStart;
    private $customerDbPatchName;
    private $customersPtrName;
    private $separateData;

	public function __construct()
	{
		parent::__construct("employeeReport");

		parent::enableLAGrid();
		//parent::enableLAGridLight();

		$this->publishedStatus = Status::PUBLISHED;
		$this->defaultEnd = App::getSetting('defaultEnd');
		$this->lang = Dict::getLang();
		$this->isCertificateIdShown = App::getSetting("employeeControl_show_certificate_id");
		$this->orderBy = App::getSetting("employeeReportOrderBy");
		$this->firstContractStart = (int)App::getSetting("employeeReportShowOriginalContractStart");
        $this->customerDbPatchName  = Yang::getParam('customerDbPatchName');
        $this->customersPtrName     = App::getSetting('ptr_customer');
        $this->separateData         = App::getSetting('employeereportSeparateData') == '1' ? true : false;
	}

	protected function G2BInit()
	{
		parent::setControllerPageTitleId("page_title_employee_report");
		parent::setExportFileName(Dict::getValue("page_title_employee_report"));

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);

		$this->LAGridRights->overrideInitRights("export_xls",		false);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);
		$this->LAGridRights->overrideInitRights("export_pdf_node",	false);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);

		$this->LAGridDB->enableSQLMode();

		parent::setGridProperty("splitColumnEnabled",    true);

		switch (true) {
            case ($this->customerDbPatchName == "schenker"):
                $cols = $this->separateData ? 4 : 3;
                break;
            case ($this->customersPtrName == "BOS"):
            default:
                $cols = $this->separateData ? 2 : 1;
                break;
        }

		//$cols =  App::getSetting("employeereportSeparateData") > 0 ? 2 : 1;
		parent::setGridProperty("splitColumn",            $cols);
		parent::G2BInit();
	}

	/**
	 * Dolgozó neve, emp_idje, alap tabok adatainak kiolvasása
	 * @param array $filter
	 * @param string $gridID
	 * @param bool $forReport
	 * @return string
	 */
	protected function setSQL($filter, $gridID, $forReport = false) {

		$edFilter=array('controllerId'	=> $this->getControllerID(),'search' => $filter);
		$approverParams=array('processId' => "employeeManagement");

  		$first_col = ($this->customerDbPatchName == 'schenker')
			? "all_data.`last_name` AS `last_name`, all_data.`first_name` AS `first_name`,"
			: '';

		$first_col .= $this->separateData
            ? Employee::getParam("fullname", "all_data") . ' as fullname, all_data.emp_id, '
            : Employee::getParam('fullname_with_emp_id_ec_id',["all_data","all_data"])." AS fullname,\n";

		if ($this->firstContractStart) {
			$contractJoinSQL = "
				LEFT JOIN
				(
					SELECT
						MIN(ec.`ec_valid_from`) AS first_ec_valid_from,
						e.`tax_number` AS t_number
					FROM `employee` e
					LEFT JOIN `employee_contract` ec ON
							ec.`employee_id` = e.`employee_id`
						AND ec.`status` = {$this->publishedStatus}
						AND ec.`valid_from` <= IFNULL(e.`valid_to`, '{$this->defaultEnd}') AND e.`valid_from` <= IFNULL(ec.`valid_to`, '{$this->defaultEnd}')
						AND ec.`ec_valid_from` <= IFNULL(e.`valid_to`, '{$this->defaultEnd}') AND e.`valid_from` <= IFNULL(ec.`ec_valid_to`, '{$this->defaultEnd}')
					WHERE e.`status` = {$this->publishedStatus}
					GROUP BY e.`tax_number`
				) AS firstcontract ON firstcontract.t_number = all_data.`tax_number`
			";
		} else {
			$contractJoinSQL = "";
		}

		$selectByRights = $this->getDataAndColumnsByRights();

		$data = array();
		foreach($selectByRights["temp_employee_data"] as $temp) {
			$data[] = $temp;
		}

		$GetActiveEmployeeData =new GetActiveEmployeeData($edFilter,$approverParams, $data, "cal.`date`, employee.`employee_id`, employee_contract.`employee_contract_id`", $this->getControllerID());

		$actieTable=$GetActiveEmployeeData->createTempTable();

		$SQL = "
			SELECT
				" . $first_col . "
				user.username,
				user.email,
				ar.rolegroup_name
				" . $selectByRights["base"] . ",
				'' AS valid_to
			FROM $actieTable all_data
			{$contractJoinSQL}
			LEFT JOIN `user` ON
					user.`employee_id` = all_data.`employee_id`
				AND user.`status`=" . $this->publishedStatus . "
				AND all_data.`date` BETWEEN user.`valid_from` AND IFNULL(user.`valid_to`, '" . $this->defaultEnd . "')
			LEFT JOIN `auth_rolegroup` ar ON
					ar.`rolegroup_id`=user.`rolegroup_id`
				AND ar.`visibility`=1
			LEFT JOIN app_lookup al_ec ON
				al_ec.lookup_id = 'ec_end_type'
				AND al_ec.lookup_value = all_data.ec_end_type
				AND al_ec.valid = 1
			LEFT JOIN dictionary d_ec ON
				al_ec.dict_id = d_ec.dict_id
				AND d_ec.lang = '" . $this->lang . "'
				AND d_ec.valid = 1
				";
			$SQL .= "
						GROUP BY all_data.`employee_id`,all_data.`employee_contract_id`";
		if($filter['quit_employees']==='yes')
		{
			$SQL.="
				UNION
			(".$this->getQuitEmployeesAllDataSQL($filter,"employeeManagement") . ")";
		}
		else if ($filter['quit_employees']==='just_yes')
		{
			$quitSQL =
				$this->getQuitEmployeesAllDataSQL($filter,"employeeManagement");
			$quitSQL .= "
				ORDER BY {$this->orderBy}
			";
			return $quitSQL;
		}
		$SQL.="
			ORDER BY {$this->orderBy}
		";

		return $SQL;
	}

	/**
	 * Kereső beállítása
	 */
	public function search() {
		$quit_employees = [
				['id' => 'no', 'value'		=> Dict::getValue("no")],
				['id' => 'yes','value'		=> Dict::getValue("yes")],
				['id' => 'just_yes','value' => Dict::getValue("just_quit_employees")]
			];
		$quit_employees_search=array('quit_employees'				=> array(
										'col_type'	=>'combo',
										'columnId'	=> 'quit_employees',
										'width'		=>'*',
										'label_text'=>Dict::getModuleValue("ttwa-base",'quited_employees_too'),
										'options'	=>	array('mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,'array'	=> $quit_employees,),
										'onchange' => ["employee_contract"],
										'default_value'=>'no',
									),);

		$preDefinedSearch = $this->getPreDefinedSearchFromDb("employeeManagement");
		return Yang::arrayMerge($quit_employees_search, $preDefinedSearch);
	}

	/**
	 * Oszlopok beállítása
	 */
	public function columns()
	{
		$retArr = [];
		/**
		 * Ezt a tömb bővítést az getDataAndColumnsByRights() metódus megcsinálja (1086-88. sor)
		 * (az első oszlop után beszúrja az emp_id oszlopot és utána csapja a maradékot).
		 * Szerintem a jobb áttekinthetőség érdekében jelen metódusnak
		 * az alábbi két sorból kellene állnia:
		 * $columnsByRights = $this->getDataAndColumnsByRights();
         * return $this->columnRights($columnsByRights["columns"]);
		 */
		if ($this->customersPtrName == "BOS" || ($this->separateData && $this->customerDbPatchName != "teesztergom")) {
			$retArr = array_slice($retArr, 0, 1) + ["emp_id" => array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 150) ] + array_slice($retArr, 1, count($retArr)-1);
		}
		$columnsByRights = $this->getDataAndColumnsByRights();

		$retArr = Yang::arrayMerge($retArr,$columnsByRights["columns"]);

		$retArr = $this->columnRights($retArr);

		return $retArr;

	}

	/**
	 * Címkék beállítása
	 */
	public function attributeLabels()
	{
		$result = array(
			'fullname'					=> Dict::getValue("name"),
			'emp_id'					=> Dict::getValue("emp_id"),
			'company_name'				=> Dict::getValue("company"),
			'payroll_name'				=> Dict::getValue("Payroll"),
			'unit_name'					=> Dict::getModuleValue("ttwa-base","unit"),
			'workgroup_name'			=> Dict::getModuleValue("ttwa-base","workgroup"),
			'cost_name'					=> Dict::getValue("cost"),
			'cost_center_name'			=> App::getSetting("akh_tab_profitcentrum") > 0 ? Dict::getValue("akh_tab_employeetabs_employeecost") : Dict::getValue("costcenter"),
			'company_org_group1_name'	=> Dict::getValue("company_org_group1"),
			'company_org_group2_name'	=> Dict::getValue("company_org_group2"),
			'company_org_group3_name'	=> Dict::getValue("company_org_group3"),
			'first_ec_valid_from'		=> Dict::getValue("first_ec_valid_from"),
			'ec_valid_from'				=> Dict::getValue("ec_valid_from"),
			'ec_valid_to'				=> Dict::getValue("ec_valid_to"),
			'vf'						=> Dict::getValue("valid_from"),
			'vt'						=> Dict::getValue("valid_to"),
			'nameofbirth'				=> Dict::getValue("nameofbirth"),
			'tax_number'				=> Dict::getValue("tax_number"),
			'gender'					=> Dict::getValue("gender"),
			'employee_contract_type'	=> Dict::getValue("employee_contract_type"),
			'wage_type'					=> Dict::getValue("wage_type"),
			'employee_position_name'	=> Dict::getValue("employee_position_name"),
			'daily_worktime'			=> Dict::getValue("daily_worktime"),
			'card'						=> Dict::getValue("card"),
			'card_note'					=> Dict::getValue("card_note"),
			'username'					=> Dict::getValue("username"),
			'email'						=> Dict::getValue("email"),
			'rolegroup_name'			=> Dict::getValue("rolegroup"),
			'address_card_number'		=> Dict::getValue("address_card_number"),
			'full_address'				=> Dict::getValue("full_address"),
			'zip_code'					=> Dict::getValue("zip_code"),
			'city'						=> Dict::getValue("city"),
			'district'					=> Dict::getValue("district"),
			'public_place_name'			=> Dict::getValue("public_place_name"),
			'public_place_type'			=> Dict::getValue("public_place_type"),
			'house_number'				=> Dict::getValue("house_number"),
			'floor'						=> Dict::getValue("floor"),
			'door'						=> Dict::getValue("door"),
			'res_full_address'			=> Dict::getValue("res_full_address"),
			'res_zip_code'				=> Dict::getValue("res_zip_code"),
			'res_city'					=> Dict::getValue("res_city"),
			'res_district'				=> Dict::getValue("res_district"),
			'res_public_place_name'		=> Dict::getValue("res_public_place_name"),
			'res_public_place_type'		=> Dict::getValue("res_public_place_type"),
			'res_house_number'			=> Dict::getValue("res_house_number"),
			'res_floor'					=> Dict::getValue("res_floor"),
			'res_door'					=> Dict::getValue("res_door"),
			'place_of_birth'			=> Dict::getValue("place_of_birth"),
			'date_of_birth'				=> Dict::getValue("date_of_birth"),
			'mothers_name'				=> Dict::getValue("mothers_name"),
			'ssn'						=> Dict::getValue("ssn"),
			'personal_id_card_number'	=> Dict::getValue("personal_id_card_number"),
			'passport_number'			=> Dict::getValue("passport_number"),
			'option1'					=> Dict::getValue("option1"),
			'option2'					=> Dict::getValue("option2"),
			'option3'					=> Dict::getValue("option3"),
			'option4'					=> Dict::getValue("option4"),
			'option5'					=> Dict::getValue("option5"),
			'option6'					=> Dict::getValue("option6"),
			'option7'					=> Dict::getValue("option7"),
			'option8'					=> Dict::getValue("option8"),
			'option9'					=> Dict::getValue("option9"),
			'option10'					=> Dict::getValue("option10"),
			'ext2_option1'				=> Dict::getValue("ext2_option1"),
			'ext2_option2'				=> Dict::getValue("ext2_option2"),
			'ext2_option3'				=> Dict::getValue("ext2_option3"),
			'ext2_option4'				=> Dict::getValue("ext2_option4"),
			'ext2_option5'				=> Dict::getValue("ext2_option5"),
			'ext2_option6'				=> Dict::getValue("ext2_option6"),
			'ext2_option7'				=> Dict::getValue("ext2_option7"),
			'ext2_option8'				=> Dict::getValue("ext2_option8"),
			'ext2_option9'				=> Dict::getValue("ext2_option9"),
			'ext2_option10'				=> Dict::getValue("ext2_option10"),
			'ext2_option11'				=> Dict::getValue("ext2_option11"),
			'ext2_option12'				=> Dict::getValue("ext2_option12"),
			'ext2_option13'				=> Dict::getValue("ext2_option13"),
			'ext2_option14'				=> Dict::getValue("ext2_option14"),
			'ext2_option15'				=> Dict::getValue("ext2_option15"),
			'ext2_option16'				=> Dict::getValue("ext2_option16"),
			'ext2_option17'				=> Dict::getValue("ext2_option17"),
			'ext2_option18'				=> Dict::getValue("ext2_option18"),
			'ext2_option19'				=> Dict::getValue("ext2_option19"),
			'ext2_option20'				=> Dict::getValue("ext2_option20"),
			'ext2_option21'				=> Dict::getValue("ext2_option21"),
			'ext2_option22'				=> Dict::getValue("ext2_option22"),
			'ext2_option23'				=> Dict::getValue("ext2_option23"),
			'ext2_option24'				=> Dict::getValue("ext2_option24"),
			'ext2_option25'				=> Dict::getValue("ext2_option25"),
			'ext2_option26'				=> Dict::getValue("ext2_option26"),
			'ext2_option27'				=> Dict::getValue("ext2_option27"),
			'ext2_option28'				=> Dict::getValue("ext2_option28"),
			'ext2_option29'				=> Dict::getValue("ext2_option29"),
			'ext2_option30'				=> Dict::getValue("ext2_option30"),
			'ext2_option31'				=> Dict::getValue("ext2_option31"),
			'ext2_option32'				=> Dict::getValue("ext2_option32"),
			'ext2_option33'				=> Dict::getValue("ext2_option33"),
			'ext2_option34'				=> Dict::getValue("ext2_option34"),
			'ext2_option35'				=> Dict::getValue("ext2_option35"),
			'ext2_option36'				=> Dict::getValue("ext2_option36"),
			'ext2_option37'				=> Dict::getValue("ext2_option37"),
			'ext2_option38'				=> Dict::getValue("ext2_option38"),
			'ext2_option39'				=> Dict::getValue("ext2_option39"),
			'ext2_option40'				=> Dict::getValue("ext2_option40"),
			'ext3_option1'				=> Dict::getValue("ext3_option1"),
			'ext3_option2'				=> Dict::getValue("ext3_option2"),
			'ext3_option3'				=> Dict::getValue("ext3_option3"),
			'ext3_option4'				=> Dict::getValue("ext3_option4"),
			'ext3_option5'				=> Dict::getValue("ext3_option5"),
			'ext3_option6'				=> Dict::getValue("ext3_option6"),
			'ext3_option7'				=> Dict::getValue("ext3_option7"),
			'ext3_option8'				=> Dict::getValue("ext3_option8"),
			'ext3_option9'				=> Dict::getValue("ext3_option9"),
			'ext3_option10'				=> Dict::getValue("ext3_option10"),
			'ext3_option11'				=> Dict::getValue("ext3_option11"),
			'ext3_option12'				=> Dict::getValue("ext3_option12"),
			'ext3_option13'				=> Dict::getValue("ext3_option13"),
			'ext3_option14'				=> Dict::getValue("ext3_option14"),
			'ext3_option15'				=> Dict::getValue("ext3_option15"),
			'ext3_option16'				=> Dict::getValue("ext3_option16"),
			'ext3_option17'				=> Dict::getValue("ext3_option17"),
			'ext3_option18'				=> Dict::getValue("ext3_option18"),
			'ext3_option19'				=> Dict::getValue("ext3_option19"),
			'ext3_option20'				=> Dict::getValue("ext3_option20"),
			'ext3_option21'				=> Dict::getValue("ext3_option21"),
			'ext3_option22'				=> Dict::getValue("ext3_option22"),
			'ext3_option23'				=> Dict::getValue("ext3_option23"),
			'ext3_option24'				=> Dict::getValue("ext3_option24"),
			'ext3_option25'				=> Dict::getValue("ext3_option25"),
			'ext3_option26'				=> Dict::getValue("ext3_option26"),
			'ext3_option27'				=> Dict::getValue("ext3_option27"),
			'ext3_option28'				=> Dict::getValue("ext3_option28"),
			'ext3_option29'				=> Dict::getValue("ext3_option29"),
			'ext3_option30'				=> Dict::getValue("ext3_option30"),
			'ext3_option31'				=> Dict::getValue("ext3_option31"),
			'ext3_option32'				=> Dict::getValue("ext3_option32"),
			'ext3_option33'				=> Dict::getValue("ext3_option33"),
			'ext3_option34'				=> Dict::getValue("ext3_option34"),
			'ext3_option35'				=> Dict::getValue("ext3_option35"),
			'ext3_option36'				=> Dict::getValue("ext3_option36"),
			'ext3_option37'				=> Dict::getValue("ext3_option37"),
			'ext3_option38'				=> Dict::getValue("ext3_option38"),
			'ext3_option39'				=> Dict::getValue("ext3_option39"),
			'ext3_option40'				=> Dict::getValue("ext3_option40"),
			'ext4_option1'				=> Dict::getValue("ext4_option1"),
			'ext4_option2'				=> Dict::getValue("ext4_option2"),
			'ext4_option3'				=> Dict::getValue("ext4_option3"),
			'ext4_option4'				=> Dict::getValue("ext4_option4"),
			'ext4_option5'				=> Dict::getValue("ext4_option5"),
			'ext4_option6'				=> Dict::getValue("ext4_option6"),
			'ext4_option7'				=> Dict::getValue("ext4_option7"),
			'ext4_option8'				=> Dict::getValue("ext4_option8"),
			'ext4_option9'				=> Dict::getValue("ext4_option9"),
			'ext4_option10'				=> Dict::getValue("ext4_option10"),
			'ext4_option11'				=> Dict::getValue("ext4_option11"),
			'ext4_option12'				=> Dict::getValue("ext4_option12"),
			'ext4_option13'				=> Dict::getValue("ext4_option13"),
			'ext4_option14'				=> Dict::getValue("ext4_option14"),
			'ext4_option15'				=> Dict::getValue("ext4_option15"),
			'ext4_option16'				=> Dict::getValue("ext4_option16"),
			'ext4_option17'				=> Dict::getValue("ext4_option17"),
			'ext4_option18'				=> Dict::getValue("ext4_option18"),
			'ext4_option19'				=> Dict::getValue("ext4_option19"),
			'ext4_option20'				=> Dict::getValue("ext4_option20"),
			'ext4_option21'				=> Dict::getValue("ext4_option21"),
			'ext4_option22'				=> Dict::getValue("ext4_option22"),
			'ext4_option23'				=> Dict::getValue("ext4_option23"),
			'ext4_option24'				=> Dict::getValue("ext4_option24"),
			'ext4_option25'				=> Dict::getValue("ext4_option25"),
			'ext4_option26'				=> Dict::getValue("ext4_option26"),
			'ext4_option27'				=> Dict::getValue("ext4_option27"),
			'ext4_option28'				=> Dict::getValue("ext4_option28"),
			'ext4_option29'				=> Dict::getValue("ext4_option29"),
			'ext4_option30'				=> Dict::getValue("ext4_option30"),
			'ext4_option31'				=> Dict::getValue("ext4_option31"),
			'ext4_option32'				=> Dict::getValue("ext4_option32"),
			'ext4_option33'				=> Dict::getValue("ext4_option33"),
			'ext4_option34'				=> Dict::getValue("ext4_option34"),
			'ext4_option35'				=> Dict::getValue("ext4_option35"),
			'ext4_option36'				=> Dict::getValue("ext4_option36"),
			'ext4_option37'				=> Dict::getValue("ext4_option37"),
			'ext4_option38'				=> Dict::getValue("ext4_option38"),
			'ext4_option39'				=> Dict::getValue("ext4_option39"),
			'ext4_option40'				=> Dict::getValue("ext4_option40"),
			'ext4_option41'				=> Dict::getValue("ext4_option41"),
			'ext4_option42'				=> Dict::getValue("ext4_option42"),
			'ext4_option43'				=> Dict::getValue("ext4_option43"),
			'ext4_option44'				=> Dict::getValue("ext4_option44"),
			'ext4_option45'				=> Dict::getValue("ext4_option45"),
			'ext4_option46'				=> Dict::getValue("ext4_option46"),
			'ext4_option47'				=> Dict::getValue("ext4_option47"),
			'ext4_option48'				=> Dict::getValue("ext4_option48"),
			'ext4_option49'				=> Dict::getValue("ext4_option49"),
			'ext4_option50'				=> Dict::getValue("ext4_option50"),
			'ext4_option51'				=> Dict::getValue("ext4_option51"),
			'ext4_option52'				=> Dict::getValue("ext4_option52"),
			'ext4_option53'				=> Dict::getValue("ext4_option53"),
			'ext4_option54'				=> Dict::getValue("ext4_option54"),
			'ext4_option55'				=> Dict::getValue("ext4_option55"),
			'ext4_option56'				=> Dict::getValue("ext4_option56"),
			'ext4_option57'				=> Dict::getValue("ext4_option57"),
			'ext4_option58'				=> Dict::getValue("ext4_option58"),
			'ext4_option59'				=> Dict::getValue("ext4_option59"),
			'ext4_option60'				=> Dict::getValue("ext4_option60"),
			'ext5_option1'				=> Dict::getValue("ext5_option1"),
			'ext5_option2'				=> Dict::getValue("ext5_option2"),
			'ext5_option3'				=> Dict::getValue("ext5_option3"),
			'ext5_option4'				=> Dict::getValue("ext5_option4"),
			'ext5_option5'				=> Dict::getValue("ext5_option5"),
			'ext5_option6'				=> Dict::getValue("ext5_option6"),
			'ext5_option7'				=> Dict::getValue("ext5_option7"),
			'ext5_option8'				=> Dict::getValue("ext5_option8"),
			'ext5_option9'				=> Dict::getValue("ext5_option9"),
			'ext5_option10'				=> Dict::getValue("ext5_option10"),
			'ext5_option11'				=> Dict::getValue("ext5_option11"),
			'ext5_option12'				=> Dict::getValue("ext5_option12"),
			'ext5_option13'				=> Dict::getValue("ext5_option13"),
			'ext5_option14'				=> Dict::getValue("ext5_option14"),
			'ext5_option15'				=> Dict::getValue("ext5_option15"),
			'ext5_option16'				=> Dict::getValue("ext5_option16"),
			'ext5_option17'				=> Dict::getValue("ext5_option17"),
			'ext5_option18'				=> Dict::getValue("ext5_option18"),
			'ext5_option19'				=> Dict::getValue("ext5_option19"),
			'ext5_option20'				=> Dict::getValue("ext5_option20"),
			'ext5_option21'				=> Dict::getValue("ext5_option21"),
			'ext5_option22'				=> Dict::getValue("ext5_option22"),
			'ext5_option23'				=> Dict::getValue("ext5_option23"),
			'ext5_option24'				=> Dict::getValue("ext5_option24"),
			'ext5_option25'				=> Dict::getValue("ext5_option25"),
			'ext5_option26'				=> Dict::getValue("ext5_option26"),
			'ext5_option27'				=> Dict::getValue("ext5_option27"),
			'ext5_option28'				=> Dict::getValue("ext5_option28"),
			'ext5_option29'				=> Dict::getValue("ext5_option29"),
			'ext5_option30'				=> Dict::getValue("ext5_option30"),
			'ext5_option31'				=> Dict::getValue("ext5_option31"),
			'ext5_option32'				=> Dict::getValue("ext5_option32"),
			'ext5_option33'				=> Dict::getValue("ext5_option33"),
			'ext5_option34'				=> Dict::getValue("ext5_option34"),
			'ext5_option35'				=> Dict::getValue("ext5_option35"),
			'ext5_option36'				=> Dict::getValue("ext5_option36"),
			'ext5_option37'				=> Dict::getValue("ext5_option37"),
			'ext5_option38'				=> Dict::getValue("ext5_option38"),
			'ext5_option39'				=> Dict::getValue("ext5_option39"),
			'ext5_option40'				=> Dict::getValue("ext5_option40"),
			'ext5_option41'				=> Dict::getValue("ext5_option41"),
			'ext5_option42'				=> Dict::getValue("ext5_option42"),
			'ext5_option43'				=> Dict::getValue("ext5_option43"),
			'ext5_option44'				=> Dict::getValue("ext5_option44"),
			'ext5_option45'				=> Dict::getValue("ext5_option45"),
			'ext5_option46'				=> Dict::getValue("ext5_option46"),
			'ext5_option47'				=> Dict::getValue("ext5_option47"),
			'ext5_option48'				=> Dict::getValue("ext5_option48"),
			'ext5_option49'				=> Dict::getValue("ext5_option49"),
			'ext5_option50'				=> Dict::getValue("ext5_option50"),
			'ext5_option51'				=> Dict::getValue("ext5_option51"),
			'ext5_option52'				=> Dict::getValue("ext5_option52"),
			'ext5_option53'				=> Dict::getValue("ext5_option53"),
			'ext5_option54'				=> Dict::getValue("ext5_option54"),
			'ext5_option55'				=> Dict::getValue("ext5_option55"),
			'ext5_option56'				=> Dict::getValue("ext5_option56"),
			'ext5_option57'				=> Dict::getValue("ext5_option57"),
			'ext5_option58'				=> Dict::getValue("ext5_option58"),
			'ext5_option59'				=> Dict::getValue("ext5_option59"),
			'ext5_option60'				=> Dict::getValue("ext5_option60"),
			'es_option1'				=> Dict::getValue("es_option1"),
			'es_option2'				=> Dict::getValue("es_option2"),
			'es_option3'				=> Dict::getValue("es_option3"),
			'es_option4'				=> Dict::getValue("es_option4"),
			'es_option5'				=> Dict::getValue("es_option5"),
			'es_option6'				=> Dict::getValue("es_option6"),
			'es_option7'				=> Dict::getValue("es_option7"),
			'es_option8'				=> Dict::getValue("es_option8"),
			'es_option9'				=> Dict::getValue("es_option9"),
			'es_option10'				=> Dict::getValue("es_option10"),
			'personal_month_salary'		=> Dict::getValue("personal_month_salary"),
			'personal_hour_salary'		=> Dict::getValue("personal_hour_salary"),
			'shift'						=> Dict::getValue("shift"),
			'shift_bonus_in_percent'	=> Dict::getValue("shift_bonus_in_percent"),
			'cafeteria_id'				=> Dict::getValue("cafeteria_id"),
			'group_id'					=> Dict::getValue("group_id"),
			'group_value'				=> Dict::getValue("group_value"),
			'base_absence_type_name'	=> Dict::getValue("base_absence_type_id"),
			'quantity'					=> Dict::getValue("base_holiday_quantity"),
			'competency_name'			=> Dict::getValue("competency_name"),
			'level_name'				=> Dict::getValue("level_name"),
			'wa_employee_contract_id'	=> Dict::getValue("employee_contract_id"),
			'work_activity_name'		=> Dict::getValue("work_activity_name"),
			'travel_cost_type'			=> Dict::getValue("travel_cost_type"),
			'travel_cost_length'		=> Dict::getValue("travel_cost_length"),
			'reimbursement_rate'		=> Dict::getValue("reimbursement_rate"),
			'reimbursement_percent'		=> Dict::getValue("reimbursement_percent"),
			'ec_end_reason'				=> Dict::getValue("ec_end_reason"),
			'ec_end_type'				=> Dict::getValue("ec_end_type"),
			'employee_contract_note'	=> Dict::getValue("note"),
		);
		if ($this->isCertificateIdShown) {
			$certificateAttributes = [
				'certificate_id'=> Dict::getValue("certificate_id"),
				'institution_name'=> Dict::getValue("institution_name")
			];

			$result = array_merge($result, $certificateAttributes);
		}

        switch ($this->customerDbPatchName) {
			//DEV-13247
            case 'schenker':
                $result['last_name']  = Dict::getValue("last_name");
                $result['first_name'] = Dict::getValue("first_name");
                break;
			//DEV-12195
            case 'kedrion':
                $result['employee_ext2_valid_from'] = Dict::getValue("option1") . " - " . Dict::getValue("valid_from");
                $result['employee_ext2_valid_to']   = Dict::getValue("option1") . " - " . Dict::getValue("valid_to");
                break;
			//DEV-13398
			case 'flex':
                foreach ($result as $k => $v) {
                    if (($v == "" || $v == "&nbsp;") && strpos($k, 'option') !== false) {
                        $result[$k] = $k;
                    }
                }
                break;
        }

		return $result;
	}

	/**
	 * A kilépett dologozó SQL-jét visszaadó függvény
	 * @param array $filter
	 * @param string $process_id
	 * @return string
	 */
	protected function getQuitEmployeesAllDataSQL($filter,$process_id)
	{
		$quit_employees=$this->getQuitEmployeesTable($filter);

		$gpf=new GetPreDefinedFilter($this->getControllerID(),FALSE,array('company' => 'employee', 'payroll' => 'employee'),FALSE,$filter);
		$where=$gpf->getFilter();

		$art = new ApproverRelatedGroup;
		$gargSQL = $art->getApproverReleatedGroupSQLWithoutCalc("Employee", $process_id, false, "AND", "CurrentDate", $this->getControllerID());

        $first_col = ($this->customerDbPatchName == 'schenker')
			? "`employee`.`last_name` AS `last_name`, `employee`.`first_name` AS `first_name`,"
			: '';

        $first_col .= ($this->customersPtrName == "BOS" || $this->separateData)
            ? 'CONCAT(`employee`.`last_name`, \' \', `employee`.`first_name`) as `fullname`, `employee`.`emp_id`,'
            : Employee::getParam('fullname_with_emp_id_ec_id',["employee", "employee_contract"])." AS `fullname`,";

		if ($this->firstContractStart) {
			$contractJoinSQL = "
				LEFT JOIN
				(
					SELECT
						MIN(ec.`ec_valid_from`) AS first_ec_valid_from,
						e.`tax_number` AS t_number
					FROM `employee` e
					LEFT JOIN `employee_contract` ec ON
							ec.`employee_id` = e.`employee_id`
						AND ec.`status` = {$this->publishedStatus}
						AND ec.`valid_from` <= IFNULL(e.`valid_to`, '{$this->defaultEnd}') AND e.`valid_from` <= IFNULL(ec.`valid_to`, '{$this->defaultEnd}')
						AND ec.`ec_valid_from` <= IFNULL(e.`valid_to`, '{$this->defaultEnd}') AND e.`valid_from` <= IFNULL(ec.`ec_valid_to`, '{$this->defaultEnd}')
					WHERE e.`status` = {$this->publishedStatus}
					GROUP BY e.`tax_number`
				) AS firstcontract ON firstcontract.t_number = `employee`.`tax_number`
			";
		} else {
			$contractJoinSQL = "";
		}

		$selectByRights = $this->getDataAndColumnsByRights();

		$SQL="
			SELECT
				".$first_col."
				user.username,
				user.email,
				ar.rolegroup_name
				" . $selectByRights["baseQuit"] . ",
				qe.valid_to
			FROM $quit_employees qe
			LEFT JOIN `employee` ON
					employee.`employee_id`=qe.`employee_id`
				AND employee.`status`=" . $this->publishedStatus . "
			{$contractJoinSQL}
			LEFT JOIN `employee_contract` ON
					employee_contract.`employee_id`=employee.`employee_id`
				AND employee_contract.`status`=" . $this->publishedStatus . "
				AND employee.`valid_from` <= IFNULL(employee_contract.`valid_to`,'" . $this->defaultEnd . "')
				AND employee.`valid_to` >= employee_contract.`valid_from`
				AND employee.`valid_from` <= IFNULL(employee_contract.`ec_valid_to`,'" . $this->defaultEnd . "')
				AND employee.`valid_to` >= employee_contract.`ec_valid_from`
			LEFT JOIN `company` ON
					company.`company_id`=employee.`company_id`
				AND	company.`status`=" . $this->publishedStatus . "
				AND employee_contract.`valid_from` <= IFNULL(company.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= company.`valid_from`
				AND employee_contract.`ec_valid_from` <= IFNULL(company.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= company.`valid_from`
			LEFT JOIN `payroll` ON
					payroll.`payroll_id`=employee.`payroll_id`
				AND	payroll.`status`=" . $this->publishedStatus . "
				AND employee.`valid_from` <= IFNULL(payroll.`valid_to`,'" . $this->defaultEnd . "')
				AND employee.`valid_to` >= payroll.`valid_from`
				AND employee_contract.`valid_from` <= IFNULL(payroll.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= payroll.`valid_from`
				AND employee_contract.`ec_valid_from` <= IFNULL(payroll.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= payroll.`valid_from`
			LEFT JOIN `employee_position` ON
					employee_position.`employee_position_id`=employee_contract.`employee_position_id`
				AND	employee_position.`status`=" . $this->publishedStatus . "
				AND employee_contract.`valid_from` <= IFNULL(employee_position.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= employee_position.`valid_from`
				AND employee_contract.`ec_valid_from` <= IFNULL(employee_position.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= employee_position.`valid_from`
			LEFT JOIN `employee_address` ON
					employee_address.employee_id=`employee`.employee_id
				AND	employee_address.`status`=" . $this->publishedStatus . "
				AND employee_contract.`valid_from` <= IFNULL(employee_address.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= employee_address.`valid_from`
				AND employee_contract.`ec_valid_from` <= IFNULL(employee_address.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= employee_address.`valid_from`
			LEFT JOIN `employee_salary` ON
					employee_salary.`employee_contract_id` = employee_contract.`employee_contract_id`
				AND	employee_salary.`status`=" . $this->publishedStatus . "
				AND employee_contract.`valid_from` <= IFNULL(employee_salary.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= employee_salary.`valid_from`
				AND employee_contract.`ec_valid_from` <= IFNULL(employee_salary.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= employee_salary.`valid_from`
			LEFT JOIN `employee_cafeteria` ON
					employee_cafeteria.`employee_contract_id` = employee_contract.`employee_contract_id`
				AND	employee_cafeteria.`status`=" . $this->publishedStatus . "
				AND employee_contract.`valid_from` <= IFNULL(employee_cafeteria.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= employee_cafeteria.`valid_from`
				AND employee_contract.`ec_valid_from` <= IFNULL(employee_cafeteria.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= employee_cafeteria.`valid_from`
			LEFT JOIN `cafeteria` caf ON
					caf.`cafeteria_id` = employee_cafeteria.`cafeteria_id`
				AND caf.`status` = " . $this->publishedStatus . "
				AND employee_contract.`valid_from` <= IFNULL(caf.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= caf.`valid_from`
				AND employee_contract.`ec_valid_from` <= IFNULL(caf.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= caf.`valid_from`";
			if (App::hasRight("employee/employeeDocsTab", "view") && $this->isCertificateIdShown)
			{
				$SQL .= "
				LEFT JOIN `employee_docs` ON
						`employee_docs`.`employee_id` = `employee`.`employee_id`
					AND `employee_docs`.`status` = 	" . $this->publishedStatus . "
					AND employee.`valid_from` <= IFNULL(employee_docs.`valid_to`,'" . $this->defaultEnd . "')
					AND employee.`valid_to` >= employee_docs.`valid_from`";
			}
			if($this->customerDbPatchName == 'stx')
			{
				$SQL .= "
				LEFT JOIN app_lookup al_ec ON
					al_ec.lookup_id = 'ec_end_type'
					AND al_ec.lookup_value = employee_contract.ec_end_type
					AND al_ec.valid = 1
				LEFT JOIN dictionary d_ec ON
					al_ec.dict_id = d_ec.dict_id
					AND d_ec.lang = '" . $this->lang . "'
					AND d_ec.valid = 1
				";
			}
			$SQL .= "
			LEFT JOIN `employee_base_absence` ON
					employee_base_absence.`employee_contract_id` = employee_contract.`employee_contract_id`
				AND	employee_base_absence.`status`=" . $this->publishedStatus . "
				AND employee_contract.`valid_from` <= IFNULL(employee_base_absence.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= employee_base_absence.`valid_from`
				AND employee_contract.`ec_valid_from` <= IFNULL(employee_base_absence.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= employee_base_absence.`valid_from`
			LEFT JOIN `base_absence_type` ON
					base_absence_type.`base_absence_type_id` = employee_base_absence.`base_absence_type_id`
				AND base_absence_type.`status` = " . $this->publishedStatus . "
			LEFT JOIN `dictionary` bat_dict ON
					bat_dict.`dict_id` = base_absence_type.`dict_id`
				AND bat_dict.`lang` = '" . $this->lang . "'
					"
			/*LEFT JOIN `employee_competency` ON
					employee_competency.`employee_contract_id` = employee_contract.`employee_contract_id`
				AND	employee_contract.`status`=" . $this->publishedStatus . "
				AND employee_contract.`valid_from` <= IFNULL(employee_competency.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= employee_competency.`valid_from`
				AND employee_contract.`ec_valid_from` <= IFNULL(employee_competency.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= employee_competency.`valid_from`
			LEFT JOIN
					`competency` ON
						competency.`competency_id` = employee_competency.`competency_id`
				AND competency.`status` = " . $this->publishedStatus . "
			LEFT JOIN
					`competency_levels` ON
					competency_levels.`level_id` = employee_competency.`level_id`
				AND competency_levels.`status` = " . $this->publishedStatus . "*/."
			LEFT JOIN `employee_work_activity` ON
					employee_work_activity.`employee_contract_id` = employee_contract.`employee_contract_id`
				AND	employee_work_activity.`status`=" . $this->publishedStatus . "
				AND employee_contract.`valid_from` <= IFNULL(employee_work_activity.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= employee_work_activity.`valid_from`
				AND employee_contract.`ec_valid_from` <= IFNULL(employee_work_activity.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= employee_work_activity.`valid_from`
			LEFT JOIN
					`work_activity` ON
					work_activity.`work_activity_id` = employee_work_activity.`work_activity_id`
				AND work_activity.`status` = " . $this->publishedStatus . "
			LEFT JOIN `employee_travel_cost` ON
					`employee_travel_cost`.`employee_contract_id` = `employee_contract`.`employee_contract_id`
				AND	`employee_travel_cost`.`status` = " . $this->publishedStatus . "
				AND `employee_contract`.`valid_from` <= IFNULL(`employee_travel_cost`.`valid_to`,'" . $this->defaultEnd . "')
				AND `employee_contract`.`valid_to` >= `employee_travel_cost`.`valid_from`
				AND `employee_contract`.`ec_valid_from` <= IFNULL(`employee_travel_cost`.`valid_to`,'" . $this->defaultEnd . "')
				AND `employee_contract`.`ec_valid_to` >= `employee_travel_cost`.`valid_from`
			LEFT JOIN `app_lookup` al_travel_cost ON
					al_travel_cost.`lookup_value` = `employee_travel_cost`.`type`
				AND al_travel_cost.`valid` = '1'
				AND al_travel_cost.`lookup_id` = 'travel_cost_type'
			LEFT JOIN `dictionary` travel_cost_dict ON
					`travel_cost_dict`.`dict_id` = al_travel_cost.`dict_id`
				AND `travel_cost_dict`.`valid` = '1'
				AND `travel_cost_dict`.`lang` = '" . $this->lang . "'
			LEFT JOIN `app_lookup` al_reimbursement_rate ON
					al_reimbursement_rate.`lookup_value` = `employee_travel_cost`.`reimbursement_rate`
				AND al_reimbursement_rate.`valid` = '1'
				AND al_reimbursement_rate.`lookup_id` = 'reimbursement_rate'
			LEFT JOIN `dictionary` reimbursement_rate_dict ON
					reimbursement_rate_dict.`dict_id` = al_reimbursement_rate.`dict_id`
				AND reimbursement_rate_dict.`valid` = '1'
				AND reimbursement_rate_dict.`lang` = '" . $this->lang . "'
			LEFT JOIN `employee_group` ON
					employee_group.`employee_contract_id` = employee_contract.`employee_contract_id`
						AND employee_group.`status` = " . $this->publishedStatus . "
							AND (
								employee_group.`valid_from` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'" . $this->defaultEnd . "')
							OR
							IFNULL(employee_group.`valid_to`,'" . $this->defaultEnd . "') BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`,'" . $this->defaultEnd . "')
								OR
								(
									employee_contract.`valid_from` BETWEEN employee_group.`valid_from` AND IFNULL(employee_group.`valid_to`,'" . $this->defaultEnd . "')
									AND
									IFNULL(employee_contract.`valid_to`,'" . $this->defaultEnd . "') BETWEEN employee_group.`valid_from` AND IFNULL(employee_group.`valid_to`,'" . $this->defaultEnd . "')
									)
								)
			LEFT JOIN `employee_group_config` ON
					`employee_group_config`.`group_id` = employee_group.`group_id`
				AND	`employee_group_config`.`status` = " . $this->publishedStatus . "
			LEFT JOIN
					`dictionary` gr_dict ON
						gr_dict.`row_id` = (SELECT dictionary.`row_id` FROM `dictionary` WHERE dictionary.`dict_id` = employee_group_config.`dict_id` AND dictionary.`lang` = '" . $this->lang . "' LIMIT 1)
			LEFT JOIN (
										(
											SELECT
												`workgroup_id` AS id,
												`workgroup_name` AS value,
												'workgroup_id' AS group_id
											FROM
												`workgroup`
											WHERE
												workgroup.`status` = " . $this->publishedStatus . "
										)
										UNION
										(
											SELECT
												`unit_id` AS id,
												`unit_name` AS value,
												'unit_id' AS group_id
											FROM
												`unit`
											WHERE
												unit.`status` = " . $this->publishedStatus . "
										)
										UNION
										(
											SELECT
												`company_org_group_id` AS id,
												`company_org_group_name` AS value,
												'company_org_group1_id' AS group_id
											FROM
												`company_org_group1`
											WHERE
												company_org_group1.`status` = " . $this->publishedStatus . "
										)
										UNION
										(
											SELECT
												`company_org_group_id` AS id,
												`company_org_group_name` AS value,
												'company_org_group2_id' AS group_id
											FROM
												`company_org_group2`
											WHERE
												company_org_group2.`status` = " . $this->publishedStatus . "
										)
										UNION
										(
											SELECT
												`company_org_group_id` AS id,
												`company_org_group_name` AS value,
												'company_org_group3_id' AS group_id
											FROM
												`company_org_group3`
											WHERE
												company_org_group3.`status` = " . $this->publishedStatus . "
										)
										UNION
										(
											SELECT
												`cost_id` AS id,
												`cost_name` AS value,
												'cost_id' AS group_id
											FROM
												`cost`
											WHERE
												cost.`status` = " . $this->publishedStatus . "
										)
										UNION
										(
											SELECT
												`cost_center_id` AS id,
												`cost_center_name` AS value,
												'cost_center_id' AS group_id
											FROM
												`cost_center`
											WHERE
												cost_center.`status` = " . $this->publishedStatus . "
										)
								) `groups` ON
									`groups`.`group_id` = employee_group.`group_id`
				AND employee_group.`group_value` = `groups`.`id`
			LEFT JOIN `employee_ext` ON
					employee_ext.employee_id=`employee`.employee_id
				AND	employee_ext.`status`=" . $this->publishedStatus . "
				AND employee_contract.`valid_from` <= IFNULL(employee_ext.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= employee_ext.`valid_from`
				AND employee_contract.`ec_valid_from` <= IFNULL(employee_ext.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= employee_ext.`valid_from`
			LEFT JOIN `employee_ext2` ON
					employee_ext2.employee_id=`employee`.employee_id
				AND	employee_ext2.`status`=" . $this->publishedStatus . "
				AND employee_contract.`valid_from` <= IFNULL(employee_ext2.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= employee_ext2.`valid_from`
				AND employee_contract.`ec_valid_from` <= IFNULL(employee_ext2.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= employee_ext2.`valid_from`
			LEFT JOIN `employee_ext3` ON
				employee_ext3.employee_id=`employee`.employee_id
			AND	employee_ext3.`status`=" . $this->publishedStatus . "
			AND employee_contract.`valid_from` <= IFNULL(employee_ext3.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= employee_ext3.`valid_from`
			AND employee_contract.`ec_valid_from` <= IFNULL(employee_ext3.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= employee_ext3.`valid_from`
			LEFT JOIN `employee_ext4` ON
				employee_ext4.employee_id=`employee`.employee_id
			AND	employee_ext4.`status`=" . $this->publishedStatus . "
			AND employee_contract.`valid_from` <= IFNULL(employee_ext4.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= employee_ext4.`valid_from`
			AND employee_contract.`ec_valid_from` <= IFNULL(employee_ext4.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= employee_ext4.`valid_from`
		LEFT JOIN `employee_ext5` ON
				employee_ext5.employee_id=`employee`.employee_id
			AND	employee_ext5.`status`=" . $this->publishedStatus . "
			AND employee_contract.`valid_from` <= IFNULL(employee_ext5.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= employee_ext5.`valid_from`
			AND employee_contract.`ec_valid_from` <= IFNULL(employee_ext5.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= employee_ext5.`valid_from`

				";
		if(!EmployeeGroupConfig::isActiveGroup('cost_id') || !EmployeeGroupConfig::isActiveGroup('cost_center_id'))
		{
			$SQL.="LEFT JOIN `employee_cost` ecost ON
					ecost.`employee_contract_id` = employee_contract.`employee_contract_id`
				AND ecost.`status` = " . $this->publishedStatus . "
				AND employee_contract.`valid_from` <= IFNULL(ecost.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= ecost.`valid_from`
				AND employee_contract.`ec_valid_from` <= IFNULL(ecost.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= ecost.`valid_from`
			";
		}
		if(EmployeeGroupConfig::isActiveGroup('unit_id'))
		{
			$SQL.=EmployeeGroup::getLeftJoinSQLWithoutCal("unit_id");
		}
		$SQL.="LEFT JOIN `unit` ON
				`unit`.`unit_id`= ".EmployeeGroup::getActiveGroupSQL("unit_id","employee")."
			AND `unit`.`status`=" . $this->publishedStatus . "
			AND employee_contract.`valid_from` <= IFNULL(unit.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= unit.`valid_from`
			AND employee_contract.`ec_valid_from` <= IFNULL(unit.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= unit.`valid_from`
		";
		if(EmployeeGroupConfig::isActiveGroup('workgroup_id'))
		{
			$SQL.=EmployeeGroup::getLeftJoinSQLWithoutCal("workgroup_id");
		}
		$SQL.="LEFT JOIN `workgroup` ON
				workgroup.`workgroup_id`= ".EmployeeGroup::getActiveGroupSQL("workgroup_id","employee_contract")."
			AND workgroup.`status`=" . $this->publishedStatus . "
			AND employee_contract.`valid_from` <= IFNULL(workgroup.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= workgroup.`valid_from`
			AND employee_contract.`ec_valid_from` <= IFNULL(workgroup.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= workgroup.`valid_from`
		";
		if(EmployeeGroupConfig::isActiveGroup('company_org_group1_id'))
		{
			$SQL.=EmployeeGroup::getLeftJoinSQLWithoutCal("company_org_group1_id");
		}
		$SQL.="LEFT JOIN company_org_group1 ON
				company_org_group1.`company_org_group_id` = ".EmployeeGroup::getActiveGroupSQL("company_org_group1_id","employee")."
			AND company_org_group1.`status` = " . $this->publishedStatus . "
			AND employee_contract.`valid_from` <= IFNULL(company_org_group1.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= company_org_group1.`valid_from`
			AND employee_contract.`ec_valid_from` <= IFNULL(company_org_group1.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= company_org_group1.`valid_from`
		";
		if(EmployeeGroupConfig::isActiveGroup('company_org_group2_id'))
		{
			$SQL.=EmployeeGroup::getLeftJoinSQLWithoutCal("company_org_group2_id");
		}
		$SQL.="LEFT JOIN company_org_group2 ON
				company_org_group2.`company_org_group_id` = ".EmployeeGroup::getActiveGroupSQL("company_org_group2_id","employee")."
			AND company_org_group2.`status` = " . $this->publishedStatus . "
			AND employee_contract.`valid_from` <= IFNULL(company_org_group2.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= company_org_group2.`valid_from`
			AND employee_contract.`ec_valid_from` <= IFNULL(company_org_group2.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= company_org_group2.`valid_from`
		";
		if(EmployeeGroupConfig::isActiveGroup('company_org_group3_id'))
		{
			$SQL.=EmployeeGroup::getLeftJoinSQLWithoutCal("company_org_group3_id");
		}
		$SQL.="LEFT JOIN company_org_group3 ON
				company_org_group3.`company_org_group_id` = ".EmployeeGroup::getActiveGroupSQL("company_org_group3_id","employee")."
			AND company_org_group3.`status` = " . $this->publishedStatus . "
			AND employee_contract.`valid_from` <= IFNULL(company_org_group3.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= company_org_group3.`valid_from`
			AND employee_contract.`ec_valid_from` <= IFNULL(company_org_group3.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= company_org_group3.`valid_from`
		";
		if(EmployeeGroupConfig::isActiveGroup('cost_id'))
		{
			$SQL.= EmployeeGroup::getLeftJoinSQLWithoutCal("cost_id");
		}

		$SQL.= "LEFT JOIN `cost` ON
					cost.`cost_id`=".EmployeeGroup::getActiveGroupSQL("cost_id","ecost")."
				AND cost.`status` = " . $this->publishedStatus . "
				AND employee_contract.`valid_from` <= IFNULL(cost.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= cost.`valid_from`
				AND employee_contract.`ec_valid_from` <= IFNULL(cost.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= cost.`valid_from`
			";
		if(EmployeeGroupConfig::isActiveGroup('cost_center_id'))
		{
			$SQL.= EmployeeGroup::getLeftJoinSQLWithoutCal("cost_center_id");
		}

		$SQL .= "LEFT JOIN `cost_center` ON
					cost_center.`cost_center_id`=".EmployeeGroup::getActiveGroupSQL("cost_center_id","ecost")."
				AND cost_center.`status` = " . $this->publishedStatus . "
				AND employee_contract.`valid_from` <= IFNULL(cost_center.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= cost_center.`valid_from`
				AND employee_contract.`ec_valid_from` <= IFNULL(cost_center.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= cost_center.`valid_from`
			";
		if(isset($gargSQL["join"]))
		{
			$SQL .= $gargSQL["join"];
		}
		$SQL.="LEFT JOIN `employee_card` ON
					employee_card.`employee_contract_id` = employee_contract.`employee_contract_id`
				AND employee_card.`status`=" . $this->publishedStatus . "
				AND employee_contract.`valid_from` <= IFNULL(employee_card.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= employee_card.`valid_from`
				AND employee_contract.`ec_valid_from` <= IFNULL(employee_card.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= employee_card.`valid_from`
			LEFT JOIN `user` ON
					user.`employee_id` = employee.`employee_id`
				AND user.`status`=" . $this->publishedStatus . "
				AND employee_contract.`valid_from` <= IFNULL(user.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`valid_to` >= user.`valid_from`
				AND employee_contract.`ec_valid_from` <= IFNULL(user.`valid_to`,'" . $this->defaultEnd . "') AND employee_contract.`ec_valid_to` >= user.`valid_from`
			LEFT JOIN `auth_rolegroup` ar ON
					ar.`rolegroup_id`=user.`rolegroup_id`
				AND ar.`visibility`=1";
			$SQL .= "
			WHERE
					`employee_contract`.`row_id` IS NOT NULL
				AND	$where ";

		if (!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight("employee/employeeGroupTab", "company_org_group1")) {
			$SQL .= "
				AND (`employee_group_config`.`group_id` != 'company_org_group1_id'
				OR	`employee_group_config`.`group_id` IS NULL)
			";
		}

		if (!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight("employee/employeeGroupTab", "company_org_group2")) {
			$SQL .= "
				AND (`employee_group_config`.`group_id` != 'company_org_group2_id'
				OR	`employee_group_config`.`group_id` IS NULL)
			";
		}

		if (!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight("employee/employeeGroupTab", "company_org_group3")) {
			$SQL .= "
				AND (`employee_group_config`.`group_id` != 'company_org_group3_id'
				OR	`employee_group_config`.`group_id` IS NULL)
			";
		}

		if (!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight("employee/employeeGroupTab", "workgroup")) {
			$SQL .= "
				AND (`employee_group_config`.`group_id` != 'workgroup_id'
				OR	`employee_group_config`.`group_id` IS NULL)
			";
		}

		if (!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight("employee/employeeGroupTab", "unit")) {
			$SQL .= "
				AND (`employee_group_config`.`group_id` != 'unit_id'
				OR	`employee_group_config`.`group_id` IS NULL)
			";
		}

		if (!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight("employee/employeeGroupTab", "cost")) {
			$SQL .= "
				AND (`employee_group_config`.`group_id` != 'cost_id'
				OR	`employee_group_config`.`group_id` IS NULL)
			";
		}

		if (!App::hasRight("employee/employeeGroupTab", "all") && !App::hasRight("employee/employeeGroupTab", "cost_center")) {
			$SQL .= "
				AND (`employee_group_config`.`group_id` != 'cost_center_id'
				OR	`employee_group_config`.`group_id` IS NULL)
			";
		}

		if(isset($gargSQL["where"]))
		{
			$SQL .= $gargSQL["where"];
		}

		$SQL.="GROUP BY employee_contract.employee_contract_id
				HAVING vt = qe.valid_to
		";

		return $SQL;
	}

	/**
	 * Kilépett dolgozók alap temp tábla
	 * @return string
	 */
	protected function getQuitEmployeesTable($filter)
	{
		$table="`quit_employee`";
		$validDate = $filter['valid_date'];
		$SQL="
			CREATE TEMPORARY TABLE IF NOT EXISTS $table
			ENGINE=MyISAM
			SELECT
				e.`employee_id`,
                MAX(LEAST(IFNULL(e.`valid_to`,'2038-01-01'),
						IFNULL(ec.`valid_to`,'2038-01-01'),
						IFNULL(ec.`ec_valid_to`,'2038-01-01'))) as valid_to
			FROM `employee` e
			LEFT JOIN `employee_contract` ec ON
					ec.`employee_id`=e.`employee_id`
				AND ec.`status`=" . $this->publishedStatus . "
				AND e.`valid_from` <= IFNULL(ec.`valid_to`,'" . $this->defaultEnd . "')
				AND e.`valid_to` >= ec.`valid_from`
				AND e.`valid_from` <= IFNULL(ec.`ec_valid_to`,'" . $this->defaultEnd . "')
				AND e.`valid_to` >= ec.`ec_valid_from`
			WHERE
					e.`status`=" . $this->publishedStatus . "
				AND ec.`row_id` IS NOT NULL
			GROUP BY e.`employee_id`
			HAVING valid_to<'$validDate'
		";

		dbExecute($SQL);

		return $table;
	}

	/**
	 * Jogosultság alapján oszlopok, alap adatok oszlopai, kilépett dolgozók alapadatainak oszlopai, tab azonosítók tömbjét rakja össze
	 * @return array
	 */
	protected function getDataAndColumnsByRights() {

		$tempTables = array('employee', 'employee_contract');
		$SQL = "";
		$SQLQuit = "";

		$cols['fullname']   	= ['export' => true, 'report_width' => 20, 'col_type' => 'ed', 'edit' => false, 'width' => 300];

		if ($this->customersPtrName == "BOS" || ($this->separateData && $this->customerDbPatchName != "teesztergom")) {
            $cols['emp_id'] 	= ['export' => true, 'report_width' => 20, 'col_type'=>'ed', 'width' => 150];
        }

        if ($this->customerDbPatchName == 'schenker') {
            $cols['last_name']  = ['export' => true, 'report_width' => 20, 'col_type' => 'ed', 'edit' => false, 'width' => 130];
            $cols['first_name'] = ['export' => true, 'report_width' => 20, 'col_type' => 'ed', 'edit' => false, 'width' => 130];
        }

        $cols['username']       = ['export' => true, 'report_width' => 20, 'col_type' => 'ed', 'edit' => false, 'width' => 150];
        $cols['email']          = ['export' => true, 'report_width' => 20, 'col_type' => 'ed', 'edit' => false, 'width' => 150];
        $cols['rolegroup_name'] = ['export' => true, 'report_width' => 20, 'col_type' => 'ed', 'edit' => false, 'width' => 150];

        // TE - DEV-11265
        if ($this->customerDbPatchName == "teesztergom") {
            $cols = Yang::arrayMerge($cols, array("emp_id" => array('export'=> true, 'report_width' => 20, 'col_type'=>'ed','width' => 150)));
        }

		if (App::hasRight("employee/employeeTab", "view")) {
			$tempTablesTab = array('company', 'payroll', 'unit', 'company_org_group1', 'company_org_group2', 'company_org_group3');
			$tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
			$SQL .= ",
					all_data.`nameofbirth`,
					all_data.`tax_number`,
					all_data.`gender`,
					all_data.`company_name`,
					all_data.`payroll_name`,
					all_data.`unit_name`,
					all_data.company_org_group1_name,
					all_data.company_org_group2_name,
					all_data.company_org_group3_name,
					GREATEST(all_data.`employee_valid_from`,all_data.`employee_contract_valid_from`) AS vf,
					LEAST(IFNULL(all_data.`employee_valid_to`,'" . $this->defaultEnd . "'),
						  IFNULL(all_data.`employee_contract_valid_to`,'" . $this->defaultEnd . "')) AS vt
			";
			$SQLQuit .= ",
					employee.`nameofbirth`,
					employee.`tax_number`,
					employee.`gender`,
					company.`company_name`,
					payroll.`payroll_name`,
					unit.`unit_name`,
					company_org_group1.`company_org_group_name` as company_org_group1_name,
					company_org_group2.`company_org_group_name` as company_org_group2_name,
					company_org_group3.`company_org_group_name` as company_org_group3_name,
					GREATEST(employee.`valid_from`,employee_contract.`valid_from`) AS vf,
					LEAST(IFNULL(employee.`valid_to`,'" . $this->defaultEnd . "'),
						  IFNULL(employee_contract.`valid_to`,'" . $this->defaultEnd . "')) AS vt
			";
			$cols = Yang::arrayMerge($cols, array(
				'company_name'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'payroll_name'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150,),
				'unit_name'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'company_org_group1_name'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'company_org_group2_name'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'company_org_group3_name'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'vf'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'vt'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'nameofbirth'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'tax_number'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'gender'			=> array('width' => 250, 'export'=> true , 'col_type'=>'combo', 'class'=>'dialogTitle',
									'options'		=>	array('mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,'array'=>App::getLookup('gender')),),

			));

			if (App::getSetting("ptr_customer") == "SAGA") {
				unset($cols['tax_number']);
			}
		}
		if (App::hasRight("employee/employeeContractTab", "view")) {
			$tempTablesTab = array('workgroup', 'employee_contract', 'employee_position');
			$tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
			if (!$this->firstContractStart)
			{
				$SQL .= ",
					all_data.`workgroup_name`,
					all_data.ec_valid_from,
					all_data.ec_valid_to,
					all_data.employee_contract_type,
					all_data.`wage_type`,
					all_data.employee_position_name,
					all_data.daily_worktime
				";
				$SQLQuit .= ",
					workgroup.`workgroup_name`,
					employee_contract.`ec_valid_from`,
					employee_contract.`ec_valid_to`,
					employee_contract.`employee_contract_type`,
					employee_contract.`wage_type`,
					employee_position.employee_position_name,
					employee_contract.daily_worktime
				";

				if($this->customerDbPatchName == 'stx')
				{
					$SQL .=",
					all_data.`ec_end_reason`,
					d_ec.dict_value as ec_end_type";
					$cols = Yang::arrayMerge($cols, array(
						'ec_end_reason'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
						'ec_end_type'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
					));
					$SQLQuit .= ",
					employee_contract.ec_end_reason,
					d_ec.dict_value as ec_end_type";
				}

				if ($this->customerDbPatchName == 'rosenberger')
				{
					$SQL .= ",
						all_data.`employee_contract_note`
					";
					$cols = Yang::arrayMerge($cols, [
						'employee_contract_note'	=> ['export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150]
					]);
					$SQLQuit .= ",
						employee_contract.`note` AS employee_contract_note
					";
				}

				$cols = Yang::arrayMerge($cols, array(
					'workgroup_name'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
					'ec_valid_from'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
					'ec_valid_to'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
					'employee_contract_type'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'combo', 'edit' => false,'width' => 150,
									'options'		=>	array('mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,'array'=>App::getLookup('employee_contract_type')),),
					'wage_type'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'combo', 'edit' => false,'width' => 150,
									'options'		=>	array('mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,'array'=>App::getLookup('wage_type')),),
					'employee_position_name'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
					'daily_worktime'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150, 'export_as' => 'decimal', 'export_format' => '0.0')

				));
			} else {
				$SQL .= ",
					all_data.`workgroup_name`,
					firstcontract.first_ec_valid_from,
					all_data.ec_valid_from,
					all_data.ec_valid_to,
					all_data.employee_contract_type,
					all_data.`wage_type`,
					all_data.employee_position_name,
					all_data.daily_worktime
				";
				$SQLQuit .= ",
					workgroup.`workgroup_name`,
					firstcontract.first_ec_valid_from,
					employee_contract.`ec_valid_from`,
					employee_contract.`ec_valid_to`,
					employee_contract.`employee_contract_type`,
					employee_contract.`wage_type`,
					employee_position.employee_position_name,
					employee_contract.daily_worktime
				";

				$cols = Yang::arrayMerge($cols, array(
					'workgroup_name'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
					'first_ec_valid_from'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
					'ec_valid_from'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
					'ec_valid_to'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
					'employee_contract_type'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'combo', 'edit' => false,'width' => 150,
									'options'		=>	array('mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,'array'=>App::getLookup('employee_contract_type')),),
					'wage_type'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'combo', 'edit' => false,'width' => 150,
									'options'		=>	array('mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,'array'=>App::getLookup('wage_type')),),
					'employee_position_name'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
					'daily_worktime'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150, 'export_as' => 'decimal', 'export_format' => '0.0')

				));
			}

		}
		if (App::hasRight("employee/employeeSalaryTab", "view")) {
			$tempTablesTab = array('employee_salary');
			$tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
			$SQL .= ",
					all_data.`personal_month_salary`,
					all_data.`personal_hour_salary`,
					all_data.shift,
					all_data.shift_bonus_in_percent,
					all_data.`es_option1`,
					all_data.`es_option2`,
					all_data.`es_option3`,
					all_data.`es_option4`,
					all_data.`es_option5`,
					all_data.`es_option6`,
					all_data.`es_option7`,
					all_data.`es_option8`,
					all_data.`es_option9`,
					all_data.`es_option10`
			";
			$SQLQuit .= ",
					`employee_salary`.`personal_month_salary`,
					`employee_salary`.`personal_hour_salary`,
					IF(`employee_salary`.`shift`=0,'".Dict::getValue("no")."','".Dict::getValue("yes")."') as shift,
					`employee_salary`.`shift_bonus_in_percent`,
					`employee_salary`.`es_option1`,
					`employee_salary`.`es_option2`,
					`employee_salary`.`es_option3`,
					`employee_salary`.`es_option4`,
					`employee_salary`.`es_option5`,
					`employee_salary`.`es_option6`,
					`employee_salary`.`es_option7`,
					`employee_salary`.`es_option8`,
					`employee_salary`.`es_option9`,
					`employee_salary`.`es_option10`
			";
			$cols = Yang::arrayMerge($cols, array(
				'personal_month_salary'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'personal_hour_salary'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'shift'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'shift_bonus_in_percent'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'es_option1'					=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'es_option2'					=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'es_option3'					=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'es_option4'					=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'es_option5'					=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'es_option6'					=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'es_option7'					=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'es_option8'					=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'es_option9'					=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'es_option10'					=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
			));
		}
		if (App::hasRight("employee/employeeCafeteriaTab", "view")) {
			$tempTablesTab = array('employee_cafeteria');
			$tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
			$SQL .= ",
					all_data.cafeteria_id
			";
			$SQLQuit .= ",
					`caf`.`cafeteria_name` as cafeteria_id
			";
			$cols = Yang::arrayMerge($cols, array(
				'cafeteria_id'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
			));
		}
		if (App::hasRight("employee/employeeAddressTab", "view")) {
			$tempTablesTab = array('employee_address');
			$tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
			$SQL .= ",
					all_data.`address_card_number`,
					all_data.`full_address`,
					all_data.`zip_code`,
					all_data.`city`,
					all_data.`district`,
					all_data.`public_place_name`,
					all_data.`public_place_type`,
					all_data.`house_number`,
					all_data.`floor`,
					all_data.`door`,
					all_data.`res_full_address`,
					all_data.`res_zip_code`,
					all_data.`res_city`,
					all_data.`res_district`,
					all_data.`res_public_place_name`,
					all_data.`res_public_place_type`,
					all_data.`res_house_number`,
					all_data.`res_floor`,
					all_data.`res_door`
			";
			$SQLQuit .= ",
					employee_address.`address_card_number`,
					employee_address.`full_address`,
					employee_address.`zip_code`,
					employee_address.`city`,
					employee_address.`district`,
					employee_address.`public_place_name`,
					employee_address.`public_place_type`,
					employee_address.`house_number`,
					employee_address.`floor`,
					employee_address.`door`,
					employee_address.`res_full_address`,
					employee_address.`res_zip_code`,
					employee_address.`res_city`,
					employee_address.`res_district`,
					employee_address.`res_public_place_name`,
					employee_address.`res_public_place_type`,
					employee_address.`res_house_number`,
					employee_address.`res_floor`,
					employee_address.`res_door`
			";
			$address=array();

			$employee_address=App::getSetting("employee_address");
			if($employee_address===EmployeeAddress::SETTING_ALL || $employee_address===EmployeeAddress::SETTING_FULL)
			{
				$address['full_address']			= array('width' => 250, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center');
			}
			if($employee_address===EmployeeAddress::SETTING_ALL || $employee_address===EmployeeAddress::SETTING_PIECE)
			{
				$address['zip_code']				= array('width' => 200, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center');
				$address['city']					= array('width' => 250, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center');
				$address['district']				= array('width' => 200, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center');
				$address['public_place_name']		= array('width' => 250, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center');
				$address['public_place_type']		= array('width' => 200, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center');
				$address['house_number']			= array('width' => 200, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center');
				$address['floor']					= array('width' => 200, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center');
				$address['door']					= array('width' => 200, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center');
			}

			if($employee_address===EmployeeAddress::SETTING_ALL || $employee_address===EmployeeAddress::SETTING_FULL)
			{
				$address['res_full_address']		= array('width' => 250, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center');
			}
			if($employee_address===EmployeeAddress::SETTING_ALL || $employee_address===EmployeeAddress::SETTING_PIECE)
			{
				$address['res_zip_code']			= array('width' => 200, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center');
				$address['res_city']				= array('width' => 250, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center');
				$address['res_district']			= array('width' => 200, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center');
				$address['res_public_place_name']	= array('width' => 250, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center');
				$address['res_public_place_type']	= array('width' => 200, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center');
				$address['res_house_number']		= array('width' => 200, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center');
				$address['res_floor']				= array('width' => 200, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center');
				$address['res_door']				= array('width' => 200, 'export'=> true , 'col_type'=>'ed', 'col_align'=>'center');
			}

			$cols = Yang::arrayMerge($cols,$address);
		}
		if (App::hasRight("employee/employeeExtTab", "view")) {
			$tempTablesTab = array('employee_ext');
			$tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
			$SQL .= ",
				all_data.`place_of_birth`,
				all_data.`date_of_birth`,
				all_data.`mothers_name`,
				all_data.`ssn`,
				all_data.`personal_id_card_number`,
				all_data.`passport_number`
			";

			$SQLQuit .= ",
					employee_ext.`place_of_birth`,
					employee_ext.`date_of_birth`,
					employee_ext.`mothers_name`,
					employee_ext.`ssn`,
					employee_ext.`personal_id_card_number`,
					employee_ext.`passport_number`
			";

			$cols = Yang::arrayMerge($cols, array(
				'place_of_birth'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'date_of_birth'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'mothers_name'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'ssn'						=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'personal_id_card_number'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'passport_number'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
			));

			for($i=1;$i<= EmployeeExt::OPTION_PIECES;++$i)
			{
				$type = OptionConfig::getType("option$i");
				if ($type === null) { continue; }
				$SQL .= ",all_data.`option$i`";
				$SQLQuit .= ",employee_ext.`option$i`";
				$cols = Yang::arrayMerge($cols, array(
					'option'.$i => array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				));
			}
		}

		if (App::hasRight("employee/employeeExt2Tab", "view")) {
			$tempTablesTab = array('employee_ext2');
			$tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
			for($i=1;$i<= EmployeeExt2::OPTION_PIECES;++$i)
			{
				$type = OptionConfig::getType("ext2_option$i");
				if ($type === null) { continue; }
				$SQL .= ",all_data.`ext2_option$i`";
				$SQLQuit .= ",employee_ext2.`ext2_option$i`";
				if ($type == 'combo') {
					$cols = Yang::arrayMerge($cols, array(
						'ext2_option'.$i => array('export'=> true, 'report_width' => 20, 'col_type'=>'combo', 'edit' => false,'width' => 150,
							'options'	=>	array('mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,'array'=>App::getLookup('ext2_option'.$i)),
					),
					));
				}
				else {
					$cols = Yang::arrayMerge($cols, array(
						'ext2_option'.$i => array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
					));
				}

			}
			if ($this->customerDbPatchName === "kedrion")
			{
				$SQL .= ",all_data.`employee_ext2_valid_from` ";
				$SQL .= ",all_data.`employee_ext2_valid_to` ";
				$SQLQuit .= ",employee_ext2.`valid_from` AS employee_ext2_valid_from ";
				$SQLQuit .= ",employee_ext2.`valid_to` AS employee_ext2_valid_to ";
				$cols = Yang::arrayMerge($cols, array(
					'employee_ext2_valid_from' => array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				));
				$cols = Yang::arrayMerge($cols, array(
					'employee_ext2_valid_to' => array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				));
			}
		}

		if (App::hasRight("employee/employeeExt3Tab", "view")) {
			$tempTablesTab = array('employee_ext3');
			$tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
			for($i=1;$i<= EmployeeExt3::OPTION_PIECES;++$i)
			{
				$type = OptionConfig::getType("ext3_option$i");
				if ($type === null) { continue; }
				$SQL .= ",all_data.`ext3_option$i`";
				$SQLQuit .= ",employee_ext3.`ext3_option$i`";
				if ($type == 'combo') {
					$cols = Yang::arrayMerge($cols, array(
						'ext3_option'.$i => array('export'=> true, 'report_width' => 20, 'col_type'=>'combo', 'edit' => false,'width' => 150,
							'options'	=>	array('mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,'array'=>App::getLookup('ext3_option'.$i)),
					),
					));
				}
				else {
					$cols = Yang::arrayMerge($cols, array(
						'ext3_option'.$i => array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
					));
				}
			}
		}

		if (App::hasRight("employee/employeeExt4Tab", "view")) {
			$tempTablesTab = array('employee_ext4');
			$tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
			for($i=1;$i<= EmployeeExt4::OPTION_PIECES;++$i)
			{
				$type = OptionConfig::getType("ext4_option$i");
				if ($type === null) { continue; }
				$SQL .= ",all_data.`ext4_option$i`";
				$SQLQuit .= ",employee_ext4.`ext4_option$i`";
				if ($type == 'combo') {
					$cols = Yang::arrayMerge($cols, array(
						'ext4_option'.$i => array('export'=> true, 'report_width' => 20, 'col_type'=>'combo', 'edit' => false,'width' => 150,
							'options'	=>	array('mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,'array'=>App::getLookup('ext4_option'.$i)),
					),
					));
				}
				else {
					$cols = Yang::arrayMerge($cols, array(
						'ext4_option'.$i => array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
					));
				}
			}
		}

		if (App::hasRight("employee/employeeExt5Tab", "view")) {
			$tempTablesTab = array('employee_ext5');
			$tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
			for($i=1;$i<= EmployeeExt5::OPTION_PIECES;++$i)
			{
				$type = OptionConfig::getType("ext5_option$i");
				if ($type === null) { continue; }
				$SQL .= ",all_data.`ext5_option$i`";
				$SQLQuit .= ",employee_ext5.`ext5_option$i`";
				if ($type == 'combo') {
					$cols = Yang::arrayMerge($cols, array(
						'ext5_option'.$i => array('export'=> true, 'report_width' => 20, 'col_type'=>'combo', 'edit' => false,'width' => 150,
							'options'	=>	array('mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,'array'=>App::getLookup('ext5_option'.$i)),
					),
					));
				}
				else {
					$cols = Yang::arrayMerge($cols, array(
						'ext5_option'.$i => array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
					));
				}
			}
		}

		if (App::hasRight("employee/employeeExt6Tab", "view")) {
			$tempTablesTab = array('employee_ext6');
			$tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
			for($i=1;$i<= EmployeeExt6::OPTION_PIECES;++$i)
			{
				$type = OptionConfig::getType("ext6_option$i");
				if ($type === null) { continue; }
				$SQL .= ",all_data.`ext6_option$i`";
				$SQLQuit .= ",employee_ext6.`ext6_option$i`";
				if ($type == 'combo') {
					$cols = Yang::arrayMerge($cols, array(
						'ext6_option'.$i => array('export'=> true, 'report_width' => 20, 'col_type'=>'combo', 'edit' => false,'width' => 150,
							'options'	=>	array('mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,'array'=>App::getLookup('ext6_option'.$i)),
					),
					));
				}
				else {
					$cols = Yang::arrayMerge($cols, array(
						'ext6_option'.$i => array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
					));
				}
			}
		}

        if (App::hasRight('employee/employeeExt7Tab', 'view')) {
            $tempTablesTab = ['employee_ext7'];
            $tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
            for($i=1;$i<= EmployeeExt7::OPTION_PIECES;++$i)
            {
                $type = OptionConfig::getType("ext7_option$i");
                if ($type === null) { continue; }
                $SQL .= ",all_data.`ext7_option$i`";
                $SQLQuit .= ",employee_ext7.`ext7_option$i`";
                if ($type == 'combo') {
                    $cols = Yang::arrayMerge($cols, [
                        'ext7_option'.$i => ['export'=> true, 'report_width' => 20, 'col_type'=>'combo', 'edit' => false,'width' => 150,
                            'options'	=>	['mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,'array'=>App::getLookup('ext7_option'.$i)],
                        ],
                    ]);
                }
                else {
                    $cols = Yang::arrayMerge($cols, [
                        'ext7_option'.$i => ['export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150],
                    ]);
                }
            }
        }

		if (App::hasRight("employee/employeeDocsTab", "view") && $this->isCertificateIdShown) {
			$tempTablesTab = ['employee_docs'];
			$tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
			$SQL .= ",
					all_data.certificate_id,
					all_data.institution_name
			";
			$SQLQuit .= ",
					employee_docs.certificate_id,
					employee_docs.institution_name
			";
			$cols = Yang::arrayMerge($cols, [
				'certificate_id'	=> ['export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150],
				'institution_name'	=> ['export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150],
			]);
		}

		if (App::hasRight("employee/employeeGroupTab", "view")) {
			$tempTablesTab = array('employee_group');
			$tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
			$SQL .= ",
					all_data.group_id,
					all_data.group_value
			";
			$SQLQuit .= ",
					`gr_dict`.`dict_value` AS group_id,
					`groups`.`value` AS `group_value`
			";
			$cols = Yang::arrayMerge($cols, array(
				'group_id'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'group_value'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
			));
		}
		if (App::hasRight("employee/employeeCardTab", "view")) {
			$tempTablesTab = array('employee_card');
			$tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
			$SQL .= ",
					GROUP_CONCAT(DISTINCT all_data.card SEPARATOR ', ') as card,
					GROUP_CONCAT(DISTINCT all_data.employee_card_note SEPARATOR ', ') as card_note
			";
			$SQLQuit .= ",
					GROUP_CONCAT(DISTINCT employee_card.card SEPARATOR ', ') as card,
					GROUP_CONCAT(DISTINCT employee_card.note SEPARATOR ', ') as card_note
			";
			$cols = Yang::arrayMerge($cols, array(
				'card'				=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'card_note'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
			));
		}
		if (App::hasRight("employee/employeeCostTab", "view")) {
			$tempTablesTab = array('cost', 'cost_center');
			$tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
			$SQL .= ",
					all_data.`cost_name`,
					all_data.`cost_center_name`
			";
			$SQLQuit .= ",
					cost.`cost_name`,
					cost_center.`cost_center_name`
			";
			$cols = Yang::arrayMerge($cols, array(
				'cost_name'			=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'cost_center_name'	=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
			));
		}
		
		if (weHaveModule("ttwa-ahp-core") && App::hasRight("employee/employeeBaseAbsenceTab", "view")) 
		{
			$tempTablesTab = array('employee_base_absence');
			$tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
			$SQL .= ",
					all_data.base_absence_type_name,
					all_data.quantity
			";
			$SQLQuit .= ",
					`bat_dict`.`dict_value` AS base_absence_type_name,
					`employee_base_absence`.`quantity`
			";
			$cols = Yang::arrayMerge($cols, array(
				'base_absence_type_name'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'quantity'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
			));
		}
		
		/*if (weHaveModule("ttwa-csm-core") && App::hasRight("employee/employeePersonalCompetencyTab", "view")) {
			$tempTablesTab = array('employee_competency');
			$tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
			$SQL .= ",
					all_data.competency_name,
					all_data.level_name
			";
			$SQLQuit .= ",
					`competency`.`competency_name`,
					`competency_levels`.`level_name`
			";
			$cols = Yang::arrayMerge($cols, array(
				'competency_name'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'level_name'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
			));
		}*/

		if (weHaveModule("ttwa-wwm") && App::hasRight("employee/employeeBaseArticleTab", "view")) {

		}

		if (weHaveModule("ttwa-wfm") && App::hasRight("employee/employeeWorkActivityTab", "view")) {
			$tempTablesTab = array('employee_work_activity');
			$tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
			$SQL .= ",
					all_data.wa_employee_contract_id,
					all_data.work_activity_name
			";
			$SQLQuit .= ",
					`employee_work_activity`.`employee_contract_id` AS wa_employee_contract_id,
					`work_activity`.`work_activity_name`
			";
			$cols = Yang::arrayMerge($cols, array(
				'wa_employee_contract_id'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
				'work_activity_name'		=> array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150),
			));
		}

		if (App::hasRight("employee/employeeTravelCostTab", "view"))
		{
			$tempTablesTab = array('employee_travel_cost');
			$tempTables = Yang::arrayMerge($tempTables, $tempTablesTab);
			$SQL .= ",
					`all_data`.`travel_cost_type`,
					`all_data`.`travel_cost_length`,
					`all_data`.`reimbursement_rate`,
					`all_data`.`reimbursement_percent`
			";
			$SQLQuit .= ",
					`travel_cost_dict`.`dict_value` AS travel_cost_type,
					`employee_travel_cost`.`length` AS travel_cost_length,
					`reimbursement_rate_dict`.`dict_value` AS reimbursement_rate,
					`employee_travel_cost`.`reimbursement_percent` AS reimbursement_percent
			";
			$cols = Yang::arrayMerge($cols, [
				'travel_cost_type'		=> ['export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150],
				'travel_cost_length'	=> ['export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150],
				'reimbursement_rate'	=> ['export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150],
				'reimbursement_percent'	=> ['export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'edit' => false,'width' => 150],
			]);
		}

		//https://jira.login.hu/browse/DEV-9352
		if ($this->customerDbPatchName === "sertec") {      //@formatter:off

			foreach (
				[   "gender"       , "nameofbirth",

					"option3"      , "option4"      , "option5"      , "option6"      , "option7"      , "option8"      , "option9"      , "option10",
					"ext2_option3" , "ext2_option4" , "ext2_option5" , "ext2_option6" , "ext2_option7" , "ext2_option8" , "ext2_option9" , "ext2_option10",
					"ext2_option13", "ext2_option12", "ext2_option14", "ext2_option15", "ext2_option16", "ext2_option11", "ext2_option17", "ext2_option18", "ext2_option30",
					"es_option1"   , "es_option2"   , "es_option3"   , "es_option4"   , "es_option5"   , "es_option6"   , "es_option7"   , "es_option8"   , "es_option9"   , "es_option10",
				] as $colKeyToRemove                                    //@formatter:on
			) {
				unset ($cols[$colKeyToRemove]);
			}
		}

		$tempTables = array_unique($tempTables);

        // TE - DEV-11265
        if ($this->customerDbPatchName == "teesztergom") {

            // Ordered column names
            $order = array(
                "fullname",
                "emp_id",
                "company_name",
                "unit_name",
                "workgroup_name",
                "card",
                "quantity",
                "ec_valid_from",
                "ec_valid_to",
                "username",
                "rolegroup_name",
                "company_org_group1_name",
                "company_org_group2_name",
                "company_org_group3_name",
                "vf",
                "vt",
                "employee_contract_type",
                "wage_type",
                "employee_position_name",
                "daily_worktime",
                "group_id",
                "group_value",
                "cost_name",
                "cost_center_name",
                "base_absence_type_name",
                "email",
                "payroll_name",
            );

            // Ordered columns
            $cols_ordered = [];

            // Fill ordered columns array
            foreach ($order as &$value) {
                if(isset($cols[$value])) {
                    $cols_ordered[$value] = $cols[$value];
                }
            }

            // Truncate columns array
            unset($cols);

            // Copy ordered columns array to columns array
            $cols = $cols_ordered;

            // Truncate ordered columns array
            unset($cols_ordered);
        }

		$returnArr = array("base" => $SQL, "baseQuit" => $SQLQuit, "columns" => $cols, "temp_employee_data" => $tempTables);

		return $returnArr;
	}
}
?>