<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2\Grid2Controller;
	use app\components\XlsxToArray;
	use app\models\AuthRole;
	use app\models\AuthRolegroup;
	use app\models\AuthRoleingroup;
	use Yang;

`/yii2-only';


class RoleingroupController extends Grid2Controller
{
	private $isRoot;
	
	public function __construct()
	{
		parent::__construct("roleingroup");
		parent::enableLAGrid();
		$this->isRoot = App::isRootSessionEnabled();
	}

	protected function G2BInit()
	{
		parent::enableMultiGridMode();

		$this->LAGridDB->setModelName("AuthRoleingroup", "dhtmlxGrid");

		parent::setControllerPageTitleId("page_title_roleingroup");

		parent::setExportFileName(Dict::getValue('rolegroup'));

		$this->LAGridRights->overrideInitRights("paging", true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search_header", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("select", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("multi_select", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("column_move", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload_sortings", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("details", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("add", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("modify", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("delete", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload", true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("init_open_search", false, 'dhtmlxGrid');
		$this->LAGridRights->overrideInitRights("export_xls", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_xlsx", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("save_state", true,	"dhtmlxGrid");

		parent::setGridProperty("splitColumnEnabled", true,	 "dhtmlxGrid");
		parent::setGridProperty("splitColumn", 1,	 "dhtmlxGrid");

		$this->LAGridDB->enableSQLMode();

		$SQL = $this->getSQL();

		$this->LAGridDB->setSQLSelectionForReport($SQL);

		parent::G2BInit();
	}

	private function getSQL()
	{
		$SQL = "
			SELECT * 
			FROM (
					SELECT
						CONCAT_WS('-', arg.row_id, ar.row_id) AS row_id,
						arg.`rolegroup_name` AS `rolegroup_name`,
						ar.`role_name` AS `role_name`,
						IF(1=1, '1', '0') AS `status_active`,
						ar.`description`,
						ar.customer_visibility
					FROM
						`auth_role_in_group` arig
					JOIN
						(
							SELECT
								row_id,
								rolegroup_name,
								rolegroup_id,
								visibility
							FROM
								auth_rolegroup
						) AS arg ON (arg.rolegroup_id = arig.rolegroup_id)
					JOIN
						(
							SELECT
								row_id,
								role_name,
								role_id,
								`description`,
								customer_visibility
							FROM
								auth_role
						) AS ar ON (ar.role_id = arig.role_id)
					WHERE
						(arg.`visibility` = 1
		";

		if ($this->isRoot) {
			$SQL .= " OR arg.rolegroup_id='a5b6bd79e008725744118c7c46e10cda'";
		}

		$SQL .= ")";

		if (!App::getRight(null,"su") && (int)App::getSetting("auth_role_customer_visibility")) {
			$SQL .= " AND ar.`customer_visibility` = 1";
		}

		$SQL .= "
			UNION DISTINCT
			SELECT
				CONCAT_WS('-', auth_rolegroup.row_id, auth_role.row_id) AS row_id,
				auth_rolegroup.rolegroup_name,
				auth_role.role_name,
				IF(1=1, '2', '0') AS status_active,
				`description`,
				 auth_role.customer_visibility
			FROM auth_rolegroup
			CROSS JOIN auth_role
			WHERE
				auth_rolegroup.rolegroup_name <> '' AND
				auth_role.role_name <> ''
			AND
				(auth_rolegroup.`visibility` = 1 ";
			if ($this->isRoot) {
			$SQL .= " OR auth_rolegroup.rolegroup_id='a5b6bd79e008725744118c7c46e10cda'";
		}

		$SQL .= ")";

		if (!App::getRight(null,"su") && (int)App::getSetting("auth_role_customer_visibility")) {
			$SQL .= " AND auth_role.`customer_visibility` = 1";
		}

		$SQL.="
		) AS uni
		GROUP BY row_id
		ORDER BY rolegroup_name, role_name";

		$this->LAGridDB->setSQLSelection($SQL, "row_id",	"dhtmlxGrid");

		$SQL = "
			SELECT
				arg.`rolegroup_name` AS `rolegroup_id`,
				ar.`role_name` AS `role_id`,
				ar.`description`
			FROM
				`auth_role_in_group` arig
			LEFT JOIN
				`auth_rolegroup` arg ON arg.`rolegroup_id` = arig.`rolegroup_id`
			LEFT JOIN
				`auth_role` ar ON ar.`role_id` = arig.`role_id`
			WHERE
				(arg.`visibility` = 1
		";

		if ($this->isRoot) {
			$SQL .= " OR arg.rolegroup_id='a5b6bd79e008725744118c7c46e10cda'";
		}

		$SQL .= ")";

		if (!App::getRight(null,"su") && (int)App::getSetting("auth_role_customer_visibility")) {
			$SQL .= " AND ar.`customer_visibility` = 1";
		}

		$SQL .= "
			ORDER BY
				arg.`rolegroup_name`
		";

		return $SQL;
	}

	public function columns()
	{
		$rolegroup = new AuthRolegroup;
		$rolegroupCriteria = new CDbCriteria();
		$rolegroupCriteria->order = "rolegroup_name ASC";
		$rolegroupCriteria->condition = "(`visibility` = 1";

		$cv = false;
		if ($this->isRoot) {
			$rolegroupCriteria->condition .= "
				 OR rolegroup_id='a5b6bd79e008725744118c7c46e10cda'";
			$cv = true;
		}

		$rolegroupCriteria->condition .= ")";

		$toRolegroupCriteria=$rolegroupCriteria;
		$toRolegroupCriteria->condition .="
				AND rolegroup_id!='{from_rolegroup}'";

		$columns=array();

		$role_SQL = "
			SELECT `role_id` as id, `role_name` AS value
			FROM `auth_role`
			WHERE 1
		";

		if (!App::getRight(null,"su") && (int)App::getSetting("auth_role_customer_visibility")) {
			$role_SQL .= " AND `customer_visibility` = 1";			
		}

		$role_SQL .= "
			ORDER BY `role_name`
		";
		
		$columns["dhtmlxGrid"]= [
			'rolegroup_name'	=> array('grid' => true, 'window' => false, 'export' => true, 'col_type' => 'ed', 'width' => 250),
			'status_active'		=> array('grid' => true, 'window' => false, 'export' => true, 'col_type' => 'cb', 'width' => 50, 'align' => 'center'),
			'customer_visibility'	=> array('grid' => $cv, 'window' => false, 'export' => true, 'col_type' => 'cb', 'width' => 88, 'align' => 'center'),
			'role_name'			=> array('grid' => true, 'window' => false, 'export' => true, 'col_type' => 'ed', 'width' => 650),
			'description'		=> array('grid' => true, 'window' => false, 'export' => true, 'col_type' => 'ed', 'width' => 650),
			'rolegroup_id'		=> array(
											'grid'			=> false,
											'export'		=> true,
											'col_type'		=> 'combo',
											'options'		=>	array(
																	'mode'					=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
																	'modelSelectionModel'	=> $rolegroup,
																	'modelSelectionCriteria'=> $rolegroupCriteria,
																	'comboId'				=> 'rolegroup_id',
																	'comboValue'			=> 'rolegroup_name'
																),
										),
			'role_id'			=> array(
											'grid'			=> false,
											'export'		=> true,
											'col_type'		=> 'combo',
											'options'		=>	array(
																	'mode'			=> Grid2Controller::G2BC_QUERY_MODE_SQL,
																	'sql'			=> $role_SQL,
																),
										),
		];

		$columns['copyDialog']=array(
			'from_rolegroup'	=> array(
							'grid'			=> false,
							'export'		=> true,
							'col_type'		=> 'combo',
							'label_text'=>Dict::getValue("from_rolegroup"),
							'onchange' => ['to_rolegroup'],
							'options'		=>	array(
													'mode'					=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
													'modelSelectionModel'	=> $rolegroup,
													'modelSelectionCriteria'=> $rolegroupCriteria,
													'comboId'				=> 'rolegroup_id',
													'comboValue'			=> 'rolegroup_name',
													'array'	=> array(array("id"=>"","value"=>"")),
												),
						),
			'to_rolegroup'		=> array(
							'grid'			=> false,
							'export'		=> true,
							'col_type'		=> 'combo',
							'label_text'=>Dict::getValue("to_rolegroup"),
							'options'		=>	array(
													'mode'					=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
													'modelSelectionModel'	=> $rolegroup,
													'modelSelectionCriteria'=> $toRolegroupCriteria,
													'comboId'				=> 'rolegroup_id',
													'comboValue'			=> 'rolegroup_name',
													'array'	=> array(array("id"=>"","value"=>"")),
												),
						),
			'overwrite'		=> array('id'=> "overwrite",'default_value'=> "no",'col_type'=>'combo', 'label_text'=>Dict::getValue("overwrite"),
					'options' => array('mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,'array'=>App::getLookup('yes_no'))),
		);

		$columns['uploadDialog']['xlsx_file'] = ['export' => false, 'report_width' => 20, 'col_type' => 'fileUpload', 'dialog_width' => '2' ];

		return $columns;
	}

	public function attributeLabels()
	{
		$labels=array();

		$labels["dhtmlxGrid"]=array(
			'rolegroup_name' => Dict::getValue("rolegroup"),
			'role_name' => Dict::getValue("role"),
			'description' => Dict::getValue("description"),
			'status_active' => Dict::getValue("status_active"),
			'customer_visibility' => Dict::getValue("customer_visibility"),
		);

		return $labels;
	}

	public function actionSaveAll()
	{
		$this->layout = "//layouts/ajax";

		$this->G2BInit();

		// GET CHECKED KEYS
		$ids = requestParam('ids');
		$idsArray = explode(";", $ids);
		$idsArray = explodeKeyVal('[]', $idsArray);

		// GET UNCHECKED KEYS
		$uncheckedIds = requestParam('unids');
		$unIdsArray = explode(";", $uncheckedIds);
		$unIdsArray = explodeKeyVal('[]', $unIdsArray);

		// GET PREVIOUS FROM DB
		$arig = new AuthRoleingroup;
		$results = $arig->findAll();

		$oldArigKeys = array();
		foreach ($results as $r) {
			$SQL = "SELECT row_id FROM auth_rolegroup WHERE rolegroup_id = '".$r->rolegroup_id."'";
				$rolegroupIdRow = dbFetchAll($SQL);
				$SQL2 = "SELECT row_id FROM auth_role WHERE role_id = '".$r->role_id."'";
				$roleIdRow = dbFetchAll($SQL2);
				if ((count($roleIdRow) > 0) && (count($rolegroupIdRow) > 0)) {
					$key = $rolegroupIdRow[0]['row_id'] . "-" . $roleIdRow[0]['row_id'];
					// STORE PRE-SAVE STATE
					$oldArigKeys[] = $key;
					//DELETE UNCHECKED ONLY IF IN UNCHECKED ARRAY PARAM
					if (isset($unIdsArray['status_active']) && in_array($key, $unIdsArray['status_active'])) {
						$delSQL="DELETE FROM auth_role_in_group WHERE role_id = '".$r['role_id']."' AND rolegroup_id = '".$r['rolegroup_id']."'";
						dbExecute($delSQL);
					}
				}
			}

		foreach ($idsArray['status_active'] as $ida) {
			if (!in_array($ida, $oldArigKeys) && $ida !== "") {
				// SAVE CHECKED, IF NOT IN PRE-SAVE STATE
				$explodeKey = explode("-", $ida);
				$roleGroupRowId = $explodeKey[0];
				$roleRowId = $explodeKey[1];

				$arg = new AuthRolegroup;
				$crit = new CDbCriteria();
				$crit->condition = "row_id = ".$roleGroupRowId;
				$roleGroup = $arg->findAll($crit);

				$ar = new AuthRole;
				$criteria = new CDbCriteria();
				$criteria->condition = "row_id = ".$roleRowId;
				$role = $ar->findAll($criteria);

				$roleInGroup = new AuthRoleingroup;
				$roleInGroup->rolegroup_id = $roleGroup[0]->rolegroup_id;
				$roleInGroup->role_id = $role[0]->role_id;
				$roleInGroup->save();
			}
		}
		if ($this->isRoot)
		{
			$roleRowId = $roleRowId ?? null;
			$this->updateVisibility($idsArray, 1, $roleRowId);
			$this->updateVisibility($unIdsArray, 0, $roleRowId);
		}

		$status = [
			'status'	=> 1,
		];

		echo json_encode($status);
	}

	protected function getStatusButtons($gridID = null)
	{
		$buttons = [];

		if ( $gridID === "dhtmlxGrid" ) {
			$buttons['openImportDialog'] = [
				'type' => 'button',
				'id' => 'openImportDialog',
				'class' => 'openImportDialog',
				'name' => 'openImportDialog',
				'img' => '/images/status_icons/st_upload.png',
				'label' => Dict::getValue('import'),
				'onclick' => " G2BDialogMultiGrid(
					'dhtmlxGrid',
					'uploadDialog',
					null,
					0,
					'./dialog',
					'./processFile',
					'./gridData',
					'null',
					'" . Dict::getValue("import") . "',
					null
				);"
			];

			$buttons["openCopyDialog"] = [
				"type" => "button",
				"id" => "openCopyDialog",
				"class" => "openCopyDialog",
				"name" => "openCopyDialog",
				"img" => "/images/status_icons/st_copy.png",
				"label" => Dict::getValue("copy_rights"),
				"onclick" => " G2BDialogMultiGrid('dhtmlxGrid','copyDialog', null, 0,'./dialog','".baseURL()."/".$this->getControllerID()."/copyRights','./gridData',null,'".Dict::getValue("copy_rights")."',null);"
			];

			$buttons[] = [ "type" => "selector" ];
		}

		return Yang::arrayMerge($buttons,parent::getStatusButtons($gridID));
	}

	public function actionProcessFile()
	{
		$file = $_SESSION['file']['path'] . $_SESSION['file']['name'];
		$data = XlsxToArray::getExcelFileToArray($file);

		if (empty($data)) {
			echo json_encode([ 'status' => 0, 'error' => Dict::getValue('no_data') ]);
			return;
		}

		$values = '';
		$updateRoleVisibility = '';
		$user = userID();
		$authRolegroups = $this->getAuthRolegroups();
		$authRoles = $this->getAuthRoles();
		$customerVisibilities = $this->getAuthRoleVisibilities();
		$arig = $this->getRole();
		foreach ($data as $row) {
			$active = $row[1];

			if ($active != "2") {
				$modifiedBy = userID();
				$authRoleGroupName = $row[0];
				$authRoleGroupId = array_search($authRoleGroupName, $authRolegroups);
				$customerVisibility = $row[2];
				$authRoleName = $row[3];
				$authRoleId = array_search($authRoleName, $authRoles);
				$roleExists = isset($arig[$authRoleGroupId]) && in_array($authRoleId, $arig[$authRoleGroupId]);
				$prevCustomerVisibility = $customerVisibilities[$authRoleId];
				$modifiedOn = date('Y-m-d H:i:s');

				if (!empty($authRoleGroupId) && !empty($authRoleId))
				{
					if(!$roleExists)
					{
						$values .= '(' . '"' . $authRoleGroupId . '",' . '"' . $authRoleId . '",' . '"' . $modifiedBy . '",' . '"' . $modifiedOn . '"' . '),';
					}

					if ($prevCustomerVisibility != $customerVisibility)
					{
						$updateRoleVisibility .= "
						UPDATE auth_role
						SET customer_visibility = {$customerVisibility}, modified_by = '{$user}', modified_on = NOW()
						WHERE role_id = '{$authRoleId}';";
					}
					
				}
			}
		}
		
		if($updateRoleVisibility !== '')
		{
			dbExecute($updateRoleVisibility);
			$success = 1;
		}

		if ($values !== '')
		{
			$values = substr($values, 0, -1);
			$sql = "INSERT INTO `auth_role_in_group` (`rolegroup_id`, `role_id`, `created_by`, `created_on`) VALUES
					" . $values;
			dbExecute($sql);
			$success = 1;
		}
	
        if ($success || (empty($updateRoleVisibility) && empty($values))) {
            echo json_encode(['success_status' => 1, 'title' => Dict::getValue('message'), 'message' => Dict::getValue('data_upload_finished')]);
		}
		else
		{
			echo json_encode(['status' => 0, 'error' => Dict::getValue('uploader_fail_500')]);
			return;
		}
	}

	private function getAuthRoles()
	{
		$sql = "SELECT `role_id`, `role_name`FROM `auth_role`";
		return dbFetchAll($sql, 'role_id', 'role_name');
	}

	private function getAuthRoleVisibilities()
	{
		$sql = "SELECT `role_id`, `customer_visibility`FROM `auth_role`";
		return dbFetchAll($sql, 'role_id', 'customer_visibility');
	}

	private function getAuthRolegroups()
	{
		$sql = "SELECT `rolegroup_id`, `rolegroup_name` FROM `auth_rolegroup`";
		return dbFetchAll($sql, 'rolegroup_id', 'rolegroup_name');
	}

	private function getRole()
	{
		$sql = "SELECT `rolegroup_id`, `role_id` FROM `auth_role_in_group`";
		$res = dbFetchAll($sql);
		$ret = [];
		foreach($res as $r)
		{
			$ret[$r['rolegroup_id']][] = $r['role_id'];
		}
		return $ret;
	}

	public function actionCopyRights()
	{
		$input = requestParam('dialogInput_copyDialog');
		$result=array('status'=>1,'error'=>'');

		if(empty($input['from_rolegroup']) || empty($input['to_rolegroup']) )
		{
			$result['status']=0;
			$result['error']=Dict::getValue("empty_fields");
		}
		else
		{
			if((int)$input['overwrite'])
			{
				$delSQL="DELETE FROM `auth_role_in_group` WHERE `rolegroup_id`='".$input['to_rolegroup']."' ";
				dbExecute($delSQL);
			}

			$SQL="
				SELECT
					from_rg.`role_id`
				FROM `auth_role_in_group` from_rg
				";
				if(!(int)$input['overwrite'])
				{
					$SQL.="LEFT JOIN `auth_role_in_group` to_rg ON
							to_rg.`role_id`=from_rg.`role_id`
						AND	to_rg.`rolegroup_id`='".$input['to_rolegroup']."'
					";
				}
			$SQL.="WHERE
						from_rg.`rolegroup_id`='".$input['from_rolegroup']."'
					";
			if(!(int)$input['overwrite'])
			{
				$SQL.="AND to_rg.`row_id` IS NULL
					";
			}

			$roles=dbFetchAll($SQL);

			for($i=0;$i<count($roles);++$i)
			{
				$roleingroup= new AuthRoleingroup;
				$roleingroup->rolegroup_id=$input['to_rolegroup'];
				$roleingroup->role_id=$roles[$i]['role_id'];
				$roleingroup->save();
			}
		}
		echo json_encode($result);
	}
	
	private function updateVisibility($arr, $field, $roleRowId)
	{
		if (!isset($arr['customer_visibility'])) { return; }
		$user = userID();
		foreach ($arr['customer_visibility'] as $ids)
		{
			$explodeKey = explode("-", $ids);
			$roleRowId = $explodeKey[1];
			$updateRoleVisibility = "
					UPDATE auth_role
					SET customer_visibility = {$field}, modified_by = '{$user}', modified_on = NOW()
					WHERE row_id = '{$roleRowId}' AND customer_visibility <> {$field}";
			dbExecute($updateRoleVisibility);
		}
	}
}
?>
