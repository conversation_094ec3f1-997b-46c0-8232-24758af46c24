<?php

class UserRightController extends Grid2Controller
{
	private $isRoot;
	private $statusPublished = Status::PUBLISHED;
	private $defaultEnd;
	private $useCompanyAndPayrollRights;
	private $all;

	public function __construct($controllerID = "userRight")
	{
		parent::__construct($controllerID);
		$this->isRoot						= App::isRootSessionEnabled();
		$this->defaultEnd					= App::getSetting("defaultEnd");
		$this->useCompanyAndPayrollRights	= (int)App::getSetting("useCompanyAndPayrollRights");
		$this->all							= Dict::getValue("all");
	}

	protected function G2BInit()
	{
		parent::enableMultiGridMode();

		$this->LAGridDB->setModelName("Approver", "dhtmlxGrid");

		parent::setControllerPageTitleId("page_title_user_rights");
		parent::setExportFileName(Dict::getValue("page_title_user_rights"));

		$this->LAGridRights->overrideInitRights("paging",				true, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search",				true, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search_header",		true, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("select",				true, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("multi_select",			true, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("column_move",			true, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("col_sorting",			true, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload_sortings",		true,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("details",				false,	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload",				true, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_xls",			false, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_xlsx",			true, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_pdf_node",		false, 	"dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("init_open_search",		true, 	"dhtmlxGrid");

		$this->LAGridDB->enableSQLMode();
		$this->LAGridDB->setSQLSelection($this->getGridDataSQL(), "row_id", "dhtmlxGrid");
		parent::G2BInit();
	}

	/**
	 * Grid adattartalmat visszaadó SQL
	 * @return string
	 */
	private function getGridDataSQL()
	{
		$rootEnableSQL = "1";
		if (!$this->isRoot) {
			$rootEnableSQL = "u.`user_id` <> '6acd9683761b153750db382c1c3694f6'";
		}
		$art		= new ApproverRelatedGroup;
		$gargSQL	= $art->getApproverReleatedGroupSQL("Employee", ["employeeManagement"], userID(), "'{date}'", "AND", "allDate");
		if ($this->useCompanyAndPayrollRights) {
			$joinsSQL = "
				" . EmployeeGroup::getAllActiveLeftJoinSQL("employee_contract", "'{date}'", "", "employee") . "
				" . EmployeeGroup::getAllBaseTablesWithGroup("employee", "employee_contract", "'{date}'") . "
			";
			if (isset($gargSQL["join"])) {
				$joinsSQL .= $gargSQL["join"];
			}
			$whereSQL = "";
			if (isset($gargSQL["where"])) {
				$whereSQL .= str_replace_first("AND", "AND (('{employee_contract}' = '' AND `employee`.`row_id` IS NULL) OR (", $gargSQL["where"]) . "))";
				$whereSQL .= " AND (`employee_contract`.`employee_contract_id` = '{employee_contract}' OR '{employee_contract}' = '' OR '{employee_contract}' = 'ALL')";
				$whereSQL .= " AND (`employee`.`company_id` = '{company}' OR '{company}' = 'ALL' OR `employee`.`row_id` IS NULL)";
			}
		} else {
			$joinsSQL = "";
			$whereSQL = "AND (`employee_contract`.`employee_contract_id` = '{employee_contract}' OR '{employee_contract}' = '' OR '{employee_contract}' = 'ALL')";
		}

		$SQL = "
			SELECT
				a.process_id,
				a.related_model,
				IF(a.`related_model` = 'Unit',				IF(a.related_value = 'ALL', '" . Dict::getValue("unit_id")				. " {$this->all}', un.`unit_name`),
				IF(a.`related_model` = 'Workgroup',			IF(a.related_value = 'ALL', '" . Dict::getValue("workgroup_id")			. " {$this->all}', w.`workgroup_name`),
				IF(a.`related_model` = 'Company',			IF(a.related_value = 'ALL', '" . Dict::getValue("company_id") 			. " {$this->all}', c.`company_name`),
				IF(a.`related_model` = 'Payroll',			IF(a.related_value = 'ALL', '" . Dict::getValue("payroll_id") 			. " {$this->all}', p.`payroll_name`),
				IF(a.`related_model` = 'EmployeeContract',	IF(a.related_value = 'ALL', '" . Dict::getValue("employee_contract_id") . " {$this->all}', " . Employee::getParam('fullname_with_emp_id_ec_id', ["e", "ec"]) . "),
				IF(a.`related_model` = 'CompanyOrgGroup1',	IF(a.related_value = 'ALL', '" . Dict::getValue("company_org_group1") 	. " {$this->all}', cog1.`company_org_group_name`),
				IF(a.`related_model` = 'CompanyOrgGroup2',	IF(a.related_value = 'ALL', '" . Dict::getValue("company_org_group2") 	. " {$this->all}', cog2.`company_org_group_name`),
				IF(a.`related_model` = 'CompanyOrgGroup3',	IF(a.related_value = 'ALL', '" . Dict::getValue("company_org_group3") 	. " {$this->all}', cog3.`company_org_group_name`),
				IF(a.`related_model` = 'Cost',				IF(a.related_value = 'ALL', '" . Dict::getValue("cost") 				. " {$this->all}', cost2.`cost_name`),";
		if (weHaveModule('ttwa-csm')) {
			$SQL .= "
				IF(a.`related_model`= 'Competency',			IF(a.related_value = 'ALL', '" . Dict::getValue("comp") 				. " {$this->all}', comp.`competency_name`),
				IF(a.`related_value` = 'ALL', '" . Dict::getValue("employee_id") . " {$this->all}', CONCAT(" . Employee::getParam('fullname', 'em') . ", ' - ', em.`emp_id`)
				)))))))))))	as related,
			";
		} else {
			$SQL .=	"
				IF(a.`related_value` = 'ALL', '" . Dict::getValue("employee_id") . " {$this->all}', CONCAT(" . Employee::getParam('fullname', 'em') . ", ' - ', em.`emp_id`)
				)))))))))) as related,
			";
		}
		$SQL .= "
			" . Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]) . " AS fullname,
				a.approver_user_id,
				u.username,
				a.`level`,
				a.note,
				a.valid_from,
				a.valid_to,
				a.row_id,
				a.valid_from AS grid_valid_from,
				a.valid_to AS grid_valid_to
			FROM `approver` a
			LEFT JOIN (
				SELECT
					`related_model`,
					`related_id`
				FROM `approver_related_group`
				WHERE `status`= {$this->statusPublished}
				GROUP BY `related_model`
			) arg ON arg.`related_model` = a.`related_model` AND arg.`related_id` = a.`related_id`
			LEFT JOIN `approver_related_type` art ON
					art.`related_model` = arg.`related_model`
				AND art.`related_id` = arg.`related_id`
				AND art.`status` = {$this->statusPublished}
			LEFT JOIN `user` u ON
					a.`approver_user_id` = u.`user_id`
				AND u.`status` = {$this->statusPublished}
				AND '{date}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `unit` un ON
					a.`related_value`= un.`unit_id`
				AND un.`status` = {$this->statusPublished}
				AND a.`related_model` = 'Unit'
				AND '{date}' BETWEEN un.`valid_from` AND IFNULL(un.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `workgroup` w ON
					a.`related_value` = w.`workgroup_id`
				AND w.`status` = {$this->statusPublished}
				AND a.`related_model` = 'Workgroup'
				AND '{date}' BETWEEN w.`valid_from` AND IFNULL(w.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `company` c ON
					a.`related_value` = c.`company_id`
				AND c.`status` = {$this->statusPublished}
				AND a.`related_model` = 'Company'
				AND '{date}' BETWEEN c.`valid_from` AND IFNULL(c.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `payroll` p ON
					p.`payroll_id` = a.`related_value`
				AND p.`status` = {$this->statusPublished}
				AND a.`related_model` = 'Payroll'
				AND '{date}' BETWEEN p.`valid_from` AND IFNULL(p.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `employee_contract` ec ON
					ec.`employee_contract_id` = a.`related_value`
				AND ec.`status` = {$this->statusPublished}
				AND a.`related_model` = 'EmployeeContract'
				AND '{date}' BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '{$this->defaultEnd}')
				AND '{date}' BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `employee` e ON
					ec.`employee_id` = e.`employee_id`
				AND e.`status` = {$this->statusPublished}
				AND '{date}' BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `company_org_group1` cog1 ON
					cog1.`company_org_group_id` = a.`related_value`
				AND cog1.`status` = {$this->statusPublished}
				AND a.`related_model` = 'CompanyOrgGroup1'
				AND '{date}' BETWEEN cog1.`valid_from` AND IFNULL(cog1.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `company_org_group2` cog2 ON
					cog2.`company_org_group_id` = a.`related_value`
				AND cog2.`status` = {$this->statusPublished}
				AND a.`related_model` = 'CompanyOrgGroup2'
				AND '{date}' BETWEEN cog2.`valid_from` AND IFNULL(cog2.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `company_org_group3` cog3 ON
					cog3.`company_org_group_id` = a.`related_value`
				AND cog3.`status` = {$this->statusPublished}
				AND a.`related_model` = 'CompanyOrgGroup3'
				AND '{date}' BETWEEN cog3.`valid_from` AND IFNULL(cog3.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `cost` cost2 ON
					cost2.`cost_id` = a.`related_value`
				AND cost2.`status` = {$this->statusPublished}
				AND a.`related_model` = 'Cost'
				AND '{date}' BETWEEN cost2.`valid_from` AND IFNULL(cost2.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `employee` em ON
					em.`employee_id` = ec.`employee_id`
				AND em.`status` = {$this->statusPublished}
				AND '{date}' BETWEEN em.`valid_from` AND IFNULL(em.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `employee` ON
					u.`employee_id` = `employee`.`employee_id`
				AND `employee`.`status` = {$this->statusPublished}
				AND '{date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->defaultEnd}')
			LEFT JOIN `employee_contract` ON
					u.`employee_id` = `employee_contract`.`employee_id`
				AND `employee_contract`.`status` = {$this->statusPublished}
				AND '{date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->defaultEnd}')
				AND '{date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->defaultEnd}')
			{$joinsSQL}
		";
		if (weHaveModule('ttwa-csm')) {
			$SQL .= "
			LEFT JOIN `competency` comp ON
					comp.`competency_id` = a.`related_value`
				AND a.`related_model` = 'Competency'
				AND comp.`status` = {$this->statusPublished}
			";
		}
		$SQL .= "
			WHERE
					a.`status` = {$this->statusPublished}
				AND {$rootEnableSQL}
				AND art.`row_id` IS NOT NULL
				AND '{date}' BETWEEN a.`valid_from` AND IFNULL(a.`valid_to`, '{$this->defaultEnd}')
				AND (u.`user_id` = '{user_id}' OR '{user_id}' = '' OR '{user_id}' = 'ALL')
				{$whereSQL}
				AND (a.related_value = '{related}' OR '{related}' = '' OR '{related}' = 'ALL')
				AND (a.related_model = '{related_model}' OR '{related_model}' = '' OR '{related_model}' = 'ALL')
				AND ('{process_id}' = '' OR '{process_id}' = 'ALL' OR a.`process_id` LIKE '{process_id}')
			ORDER BY `process_id`, `related_model`
		";

		return $SQL;
	}

	public function search()
	{
		$rootEnableSQL		= "1";
		if (!$this->isRoot) {
			$rootEnableSQL	= "u.`user_id` <> '6acd9683761b153750db382c1c3694f6'";
		}
		$rightsOptions		= App::getLookup('approver_process_ids', false, null, [], true);
		$art				= new ApproverRelatedGroup;
		$gargSQL			= $art->getApproverReleatedGroupSQL("Employee", ["employeeManagement"], userID(), "'{date}'", "AND", "allDate");
		if ($this->useCompanyAndPayrollRights) {
			$joinsSQL = "
				" . EmployeeGroup::getAllActiveLeftJoinSQL("employee_contract", "'{date}'", "", "employee") . "
				" . EmployeeGroup::getAllBaseTablesWithGroup("employee", "employee_contract", "'{date}'") . "
			";
			if (isset($gargSQL["join"])) {
				$joinsSQL .= $gargSQL["join"];
			}
			$whereSQL = "";
			$userWhereSQL = "";
			if (isset($gargSQL["where"])) {
				$whereSQL .= $gargSQL["where"];
				$userWhereSQL = str_replace_first("AND", "AND (`employee`.`row_id` IS NULL OR (", $gargSQL["where"]) . "))";
			}
		} else {
			$joinsSQL = "";
			$whereSQL = "";
			$userWhereSQL = "";
		}

		$searchFields['date'] =
		[
			'col_type'		=> 'ed',
			'dPicker'		=> true,
			'width'			=> '*',
			'label_text'	=> Dict::getValue("date"),
			'default_value'	=> date('Y-m-d'),
			'onchange'		=> ['employee_contract', 'user_id', 'related']
		];
		if ($this->useCompanyAndPayrollRights)
		{
			$art	= new ApproverRelatedGroup;
			$gargSQL= $art->getApproverReleatedGroupSQL("Company", ["employeeManagement"], userID(), "'{date}'", "AND", "allDate");
			$searchFields['date']['onchange'][] = 'company';
			$searchFields['company'] =
			[
				'col_type'		=> 'combo',
				'width'			=> '*',
				'label_text'	=> Dict::getModuleValue("ttwa-base", "company"),
				'options'		=>
				[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							`company`.`company_id` AS id,
							`company`.`company_name` AS value
						FROM `company`
						WHERE
								`company`.`status` = {$this->statusPublished}
							AND '{date}' BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$this->defaultEnd}')
					",
				],
				'onchange'		=> ['employee_contract', 'user_id']
			];
			$searchFields['company']['options']['sql'] .= " {$gargSQL["where"]} ORDER BY value";
		}
		$searchFields['employee_contract'] =
		[
			'col_type'		=> 'auto',
			'options'		=>	[
				'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
				'sql'	=> "
					SELECT
						`employee_contract`.`employee_contract_id` AS id,
						" . Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]) . " AS value
					FROM `employee`
					LEFT JOIN `employee_contract` ON
							`employee_contract`.`employee_id` = `employee`.`employee_id`
						AND `employee_contract`.`status` = {$this->statusPublished}
						AND '{date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->defaultEnd}')
						AND '{date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->defaultEnd}')
					{$joinsSQL}
					WHERE
							`employee`.`status` = {$this->statusPublished}
						AND '{date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->defaultEnd}')
						{$whereSQL}
						AND " . Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]) . " LIKE '%%{search}%%'
				",
				'array'	=> [["id"=>"", "value"=>""]]
			],
			'label_text'	=> Dict::getValue("name"),
			'default_value'	=> '',
		];
		if ($this->useCompanyAndPayrollRights) {
			$searchFields['employee_contract']['options']['sql'] .= " AND (`employee`.`company_id` = '{company}' OR '{company}' = 'ALL') ORDER BY value";
		} else {
			$searchFields['employee_contract']['options']['sql'] .= " ORDER BY value";
		}

		$searchFields['user_id'] =
		[
			'col_type'			=> 'auto',
			'options'			=>	[
				'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
				'sql'	=> "
					SELECT
						u.`user_id` AS id,
						u.`username` AS value
					FROM `user` u
					LEFT JOIN `employee` ON
							`employee`.`employee_id` = u.`employee_id`
						AND `employee`.`status` = {$this->statusPublished}
						AND '{date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->defaultEnd}')
					LEFT JOIN `employee_contract` ON
							`employee_contract`.`employee_id` = `employee`.`employee_id`
						AND `employee_contract`.`status` = {$this->statusPublished}
						AND '{date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->defaultEnd}')
						AND '{date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->defaultEnd}')
					{$joinsSQL}
					WHERE
							u.`status` = {$this->statusPublished}
						AND '{date}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$this->defaultEnd}')
						{$userWhereSQL}
						AND u.`username` LIKE '%%{search}%%'
						AND {$rootEnableSQL}
				",
				'array'	=> [["id"=>"", "value"=>""]]
			],
			'label_text'	=> Dict::getValue("user"),
			'default_value'	=> '',
		];

		if ($this->useCompanyAndPayrollRights) {
			$searchFields['user_id']['options']['sql'] .= " AND (`employee`.`company_id` = '{company}' OR '{company}' = 'ALL' OR `employee`.`row_id` IS NULL) ORDER BY value";
		} else {
			$searchFields['user_id']['options']['sql'] .= " ORDER BY value";
		}

		$searchFields['process_id'] =
		[
			'col_type'		=> 'combo',
			'options'      	=> [
				'mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY,
				'array'=> Yang::arrayMerge([["id" => "ALL", "value" => $this->all]], $rightsOptions)],
			'label_text'	=> Dict::getValue("process_id"),
			'default_value'	=> 'ALL',
		];

		$searchFields['related_model'] =
		[
			'col_type'		=> 'combo',
			'options'		=> [
				'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
				'sql'	=> "
					SELECT
						art.`related_model` AS id,
						d.`dict_value` AS value
					FROM `approver_related_type` art
					LEFT JOIN `dictionary` d ON
							d.`dict_id` = art.`name_dict_id`
						AND d.`lang` = '" . Dict::getLang() . "'
					WHERE
							art.`status` = {$this->statusPublished}
						AND (d.`dict_value` LIKE '%%{search}%%')
					ORDER BY d.`dict_value`
				",
				'array'	=> [["id" => "ALL", "value" => $this->all]]
			],
			'label_text'	=> Dict::getValue("classification"),
			'default_value'	=> '',
		];

		$searchFields['related'] =
		[
			'col_type'		=> 'auto',
			'options'		=> [
				'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
				'sql'	=> $this->getRelatedSQL($joinsSQL, $whereSQL),
				'array'	=> [["id" => "", "value" => Dict::getValue("")]]
			],
			'label_text'	=> Dict::getValue("selected_group_or_employee"),
			'default_value'	=> '',
		];

		$searchFields['submit'] = ['col_type' => 'searchBarReinitGrid', 'width' => '*', 'label_text' => ''];

		$userId = userID();
		$controllerId = $this->getControllerID();
		$input = "searchInput_date";
		if(isset($_SESSION["tiptime"][$userId][$controllerId][$input])) {
			$searchFields["employee_contract"]["options"]["sql"] = str_replace("'{date}'", "'" . $_SESSION["tiptime"][$userId][$controllerId][$input] . "'", $searchFields["employee_contract"]["options"]["sql"]);
			$searchFields["user_id"]["options"]["sql"] = str_replace("'{date}'", "'" . $_SESSION["tiptime"][$userId][$controllerId][$input] . "'", $searchFields["user_id"]["options"]["sql"]);
			$searchFields["related"]["options"]["sql"] = str_replace("'{date}'", "'" . $_SESSION["tiptime"][$userId][$controllerId][$input] . "'", $searchFields["related"]["options"]["sql"]);
			if ($this->useCompanyAndPayrollRights) {
				$searchFields["company"]["options"]["sql"] = str_replace("'{date}'", "'" . $_SESSION["tiptime"][$userId][$controllerId][$input] . "'", $searchFields["company"]["options"]["sql"]);
			}
		}
		return $searchFields;
	}

	/**
	 * Visszaadja a related SQL-t.
	 * @return string
	 */
	private function getRelatedSQL($employeeJoinsSQL, $employeeWhereSQL)
	{
		$art				= new ApproverRelatedGroup;
		if ($this->useCompanyAndPayrollRights) {
			$unitGargSQL	= $art->getApproverReleatedGroupSQL("Unit", ["companyMainData"], userID(), "'{date}'", "AND", "allDate");
			$wgGargSQL		= $art->getApproverReleatedGroupSQL("Workgroup", ["companyMainData"], userID(), "'{date}'", "AND", "allDate");
			$companyGargSQL	= $art->getApproverReleatedGroupSQL("Company", ["companyMainData"], userID(), "'{date}'", "AND", "allDate");
			$payrollGargSQL	= $art->getApproverReleatedGroupSQL("Payroll", ["companyMainData"], userID(), "'{date}'", "AND", "allDate");
			$cog1GargSQL	= $art->getApproverReleatedGroupSQL("CompanyOrgGroup1", ["companyMainData"], userID(), "'{date}'", "AND", "allDate");
			$cog2GargSQL	= $art->getApproverReleatedGroupSQL("CompanyOrgGroup2", ["companyMainData"], userID(), "'{date}'", "AND", "allDate");
			$cog3GargSQL	= $art->getApproverReleatedGroupSQL("CompanyOrgGroup3", ["companyMainData"], userID(), "'{date}'", "AND", "allDate");
		} else {
			$unitGargSQL	= ["where" => ""];
			$wgGargSQL 		= ["where" => ""];
			$companyGargSQL	= ["where" => ""];
			$payrollGargSQL = ["where" => ""];
			$cog1GargSQL	= ["where" => ""];
			$cog2GargSQL	= ["where" => ""];
			$cog3GargSQL	= ["where" => ""];
		}
		$SQL = "
			SELECT * FROM
			(
				SELECT 'ALL' AS `id`, '{$this->all}' AS `value`
				UNION
				SELECT
					`unit`.`unit_id` AS id,
					`unit`.`unit_name` AS value
				FROM `unit`
				WHERE
						`unit`.`status` = {$this->statusPublished}
					AND '{date}' BETWEEN `unit`.`valid_from` AND IFNULL(`unit`.`valid_to`, '{$this->defaultEnd}')
					AND 'Unit' = '{related_model}'
					{$unitGargSQL["where"]}
				UNION
				SELECT
					`workgroup`.`workgroup_id` AS id,
					`workgroup`.`workgroup_name` AS value
			 	FROM `workgroup`
			 	WHERE
				 		`workgroup`.`status` = {$this->statusPublished}
					AND '{date}' BETWEEN `workgroup`.`valid_from` AND IFNULL(`workgroup`.`valid_to`, '{$this->defaultEnd}')
					AND 'Workgroup' = '{related_model}'
					{$wgGargSQL["where"]}
				UNION
			 	SELECT
					`company_id` AS id,
					`company_name` AS value
			 	FROM `company`
			 	WHERE
						`status` = {$this->statusPublished}
					AND '{date}' BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->defaultEnd}')
					AND 'Company' = '{related_model}'
					{$companyGargSQL["where"]}
				UNION
				SELECT
					`payroll`.`payroll_id` AS id,
					`payroll`.`payroll_name` AS value
			 	FROM `payroll`
				LEFT JOIN `company` ON
						`company`.`company_id` = `payroll`.`company_id`
					AND `company`.`status` = {$this->statusPublished}
					AND '{date}' BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$this->defaultEnd}')
				WHERE
						`payroll`.`status` = {$this->statusPublished}
					AND '{date}' BETWEEN `payroll`.`valid_from` AND IFNULL(`payroll`.`valid_to`, '{$this->defaultEnd}')
					AND 'Payroll' = '{related_model}'
					{$payrollGargSQL["where"]}
				UNION
			 	SELECT
				 	`employee_contract`.`employee_contract_id` AS id,
					" . Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]) . "AS value
			 	FROM `employee_contract`
			 	LEFT JOIN `employee` ON
						`employee`.`employee_id` = `employee_contract`.`employee_id`
					AND `employee`.`status` = {$this->statusPublished}
					AND '{date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->defaultEnd}')
				{$employeeJoinsSQL}
			 	WHERE
						`employee_contract`.`status` = {$this->statusPublished}
					AND '{date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->defaultEnd}')
					AND '{date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->defaultEnd}')
					AND 'EmployeeContract' = '{related_model}'
					{$employeeWhereSQL}
				UNION
			 	SELECT
					`company_org_group1`.`company_org_group_id` AS id,
					`company_org_group1`.`company_org_group_name` AS value
			 	FROM `company_org_group1`
			 	WHERE
				 		`company_org_group1`.`status` = {$this->statusPublished}
					AND ('{date}' BETWEEN `company_org_group1`.`valid_from` AND IFNULL(`company_org_group1`.`valid_to`, '{$this->defaultEnd}'))
					AND 'CompanyOrgGroup1' = '{related_model}'
					{$cog1GargSQL["where"]}
				UNION
				SELECT
					`company_org_group2`.`company_org_group_id` AS id,
					`company_org_group2`.`company_org_group_name` AS value
				FROM `company_org_group2`
				WHERE
						`company_org_group2`.`status` = {$this->statusPublished}
					AND ('{date}' BETWEEN `company_org_group2`.`valid_from` AND IFNULL(`company_org_group2`.`valid_to`, '{$this->defaultEnd}'))
					AND 'CompanyOrgGroup2' = '{related_model}'
					{$cog2GargSQL["where"]}
				UNION
				SELECT
					`company_org_group3`.`company_org_group_id` AS id,
					`company_org_group3`.`company_org_group_name` AS value
				FROM `company_org_group3`
				WHERE
						`company_org_group3`.`status` = {$this->statusPublished}
					AND ('{date}' BETWEEN `company_org_group3`.`valid_from` AND IFNULL(`company_org_group3`.`valid_to`, '{$this->defaultEnd}'))
					AND 'CompanyOrgGroup3' = '{related_model}'
					{$cog3GargSQL["where"]}
				UNION
				SELECT
					`cost_id` AS id,
					`cost_name` AS value
			 	FROM `cost`
				WHERE
						`status` = {$this->statusPublished}
					AND ('{date}' BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->defaultEnd}'))
					AND 'Cost' = '{related_model}'
			) a
			WHERE `value` like '%%{search}%%'
		";

		return $SQL;
	}

	public function columns()
	{
		$statusPublished	= $this->statusPublished;
		$defaultEnd			= $this->defaultEnd;
		$rootEnableSQL		= "1";
		if (!$this->isRoot) {
			$rootEnableSQL	= "u.`user_id` <> '6acd9683761b153750db382c1c3694f6'";
		}
		$rightsOptions         = App::getLookup('approver_process_ids', false, null, [], true);
		$filter                = requestParam('searchInput');
		$row_id                = requestParam('editPK') !== null ? requestParam('editPK') : 0;
		$date                  = $filter['date'] ?? null;
		$columns               = [];
        $approver_user_id_type = App::getSetting("userRightDialogApproverUserIdComponentType");
        $related_value_type    = App::getSetting("userRightDialogRelatedValueComponentType");

        // If approver_user_id_type is autocomplete
        if ($approver_user_id_type === 'auto') {
            $approver_user_id_search = "AND u.`username` LIKE '%%{search}%%'";
        } else {
            $approver_user_id_search = '';
        }

        // If related_value_type is autocomplete
        if ($related_value_type === 'auto') {
            $related_value_search = "AND value LIKE '%%{search}%%'";
        } else {
            $related_value_search = '';
        }

		if ($this->useCompanyAndPayrollRights) {
			$art				= new ApproverRelatedGroup;
			$gargSQL			= $art->getApproverReleatedGroupSQL("Employee", ["employeeManagement"], userID(), "'" . $date . "'", "AND", "allDate");
			$joinsSQL = "
				" . EmployeeGroup::getAllActiveLeftJoinSQL("employee_contract", "'" . $date . "'", "", "employee") . "
				" . EmployeeGroup::getAllBaseTablesWithGroup("employee", "employee_contract", "'" . $date . "'") . "
			";
			if (isset($gargSQL["join"])) {
				$joinsSQL .= $gargSQL["join"];
			}
			$whereSQL = "";
			$userWhereSQL = "";
			if (isset($gargSQL["where"])) {
				$whereSQL .= $gargSQL["where"];
				$userWhereSQL = str_replace_first("AND", "AND (`employee`.`row_id` IS NULL OR (", $gargSQL["where"]) . "))";
			}
		} else {
			$joinsSQL = "";
			$whereSQL = "";
			$userWhereSQL = "";
		}

		$columns['dhtmlxGrid'] =
		[
			'valid_from'=> ['export' => false, 'grid' => false, 'report_width' => 20, 'onchange' => ['approver_user_id', 'related_value'], 'col_type' => 'ed', 'dPicker' => true, 'width' => "*"],
			'valid_to'	=> ['export' => false, 'grid' => false, 'report_width' => 20, 'onchange' => ['approver_user_id', 'related_value'], 'col_type' => 'ed', 'dPicker' => true, 'width' => "*"],
			'fullname'	=> ['export' => true, 'report_width' => 20, 'col_type' => 'ed', 'window' => false, 'width' => 220],
			'username'	=> ['export' => true, 'report_width' => 20, 'col_type' => 'ed', 'window' => false, 'width' => 220],
			'approver_user_id' => [
				'export'		=> false,
				'report_width'	=> 20,
				'col_type'		=> $approver_user_id_type,
				'window'		=> true,
				'grid'			=> false,
				'width'			=> 200,
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							u.`user_id` AS id,
							u.`username` AS value
						FROM `user` u
						LEFT JOIN `employee` ON
								`employee`.`employee_id` = u.`employee_id`
							AND `employee`.`status` = {$statusPublished}
							AND '{$date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$defaultEnd}')
						LEFT JOIN `employee_contract` ON
								`employee_contract`.`employee_id` = u.`employee_id`
							AND `employee_contract`.`status` = {$statusPublished}
							AND '{$date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$defaultEnd}')
							AND '{$date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$defaultEnd}')
						{$joinsSQL}
						WHERE
								u.`status` = {$statusPublished}
							AND '{$date}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$defaultEnd}')
							AND {$rootEnableSQL}
							{$userWhereSQL}
							AND ('{valid_from}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$defaultEnd}') OR '{valid_to}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$defaultEnd}'))
						    {$approver_user_id_search}
						ORDER BY value
					",
					'array'	=> [["id" => "", "value" => ""]]
				],
				'onchange'      => ['related_value']
			],
			'process_id' => [
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width' 		=> "*",
				'options'		=>	[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
					'array'	=> $rightsOptions
				],
				'onchange' => ['related_value']
			],
			'related_model'	=> [
				'export'	=> true,
				'report_width'	=> 20,
				'col_type'	=> 'combo',
				'width' 	=>  "*",
				'onchange' 	=> ['related_id', 'related_value'],
				'options'	=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							art.`related_model` AS id,
							d.`dict_value` AS value
						FROM `approver_related_type` art
						LEFT JOIN `dictionary` d ON
								d.`dict_id` = art.`name_dict_id`
							AND d.`lang` = '" . Dict::getLang() . "'
						WHERE art.`status` = {$statusPublished}
						ORDER BY d.`dict_value`
					",
					'array'	=> [["id" => "", "value" => ""]]
				]
			],
			'related_value'	=> [
				'export' 		=> false,
				'report_width'	=> 20,
				'col_type' 		=> $related_value_type,
				'width' 		=> 300,
				'grid' 			=> false,
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> $this->createRelatedValueSQL($joinsSQL, $whereSQL, $date, $this->useCompanyAndPayrollRights, $related_value_search),
					'array'	=> [["id" => "", "value" => ""]]
				]
			],
			'related'	=> ['export' => true, 'report_width' => 20, 'col_type' => 'ed', 'width' => 220, 'window' => false]
		];

		if (App::getSetting("useApprovalWithEndorsement")) {
			$columns['dhtmlxGrid']['level'] = ['export' => true, 'report_width' => 20, 'align' => 'center', 'col_type' => 'ed', 'width' => "*"];
		}

		// If type is auto then set default value
		if ($approver_user_id_type == 'auto') {
            $columns['dhtmlxGrid']['approver_user_id']['default_value'] = ($row_id !== 0 ? $this->getApproverUserIdComputedValue($row_id) : '');
        }

        if ($related_value_type == 'auto') {
            $columns['dhtmlxGrid']['related_value']['default_value'] = ($row_id !== 0 ? $this->getRelatedValueComputedValue($row_id) : '');
        }

		$columns['dhtmlxGrid']['note']            = ['export' => true, 'report_width' => 20, 'col_type' => 'ed', 'width' => "*"];
		$columns['dhtmlxGrid']['grid_valid_from'] = ['export' => true, 'grid' => true, 'window' => false, 'report_width' => 20, 'col_type' => 'ed', 'dPicker' => true, 'width' => "*"];
		$columns['dhtmlxGrid']['grid_valid_to']	  = ['export' => true, 'grid' => true, 'window' => false, 'report_width' => 20, 'col_type' => 'ed', 'dPicker' => true, 'width' => "*"];

		$type= [
			['id' => 'N', 'value' => Dict::getValue("no")],
			['id' => 'Y', 'value' => Dict::getValue("yes")]
		];

		foreach ($rightsOptions as $i => $ro) {
			if (isset($ro["default"])) {
				unset($rightsOptions[$i]["default"]);
			}
		}

		$columns['copyDialog']=
		[
			'valid_from'	=> ['export' => false, 'report_width' => 20, 'onchange' => ['from_fullname', 'from_username', 'to_fullname', 'to_username'], 'col_type' => 'ed', 'width' => 220, 'dPicker' => true, 'line_break' => true],
			'valid_to'		=> ['export' => false, 'report_width' => 20, 'onchange' => ['from_fullname', 'from_username', 'to_fullname', 'to_username'], 'col_type' => 'ed', 'width' => 220, 'dPicker' => true],
			'from_fullname'	=> [
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width' 		=> 220,
				'label_text'	=> Dict::getValue("from_employee"),
				'onchange' 		=> ['from_username'],
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							`employee_contract`.`employee_contract_id` AS id,
							" . Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]) . " AS value
						FROM `employee`
						LEFT JOIN `employee_contract` ON
								`employee_contract`.`employee_id` = `employee`.`employee_id`
							AND `employee_contract`.`status` = {$statusPublished}
							AND '{$date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$defaultEnd}')
							AND '{$date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$defaultEnd}')
						{$joinsSQL}
						RIGHT JOIN `user` u ON
								u.`employee_id` = `employee`.`employee_id`
							AND '{$date}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$defaultEnd}')
							AND u.`status` = {$statusPublished}
						WHERE
								`employee`.`status` = {$statusPublished}
							{$whereSQL}
							AND '{$date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$defaultEnd}')
							AND ('{valid_from}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$defaultEnd}') OR '{valid_to}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$defaultEnd}'))
						ORDER BY value",
					'array'	=> [["id" => "", "value" => ""]]
				]
			],
			'from_username'	=> [
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width' 		=> 220,
				'label_text'	=> Dict::getValue("from_username"),
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							u.`user_id` AS id,
							u.`username` AS value
						FROM `user` u
						LEFT JOIN `employee` ON
								`employee`.`employee_id` = u.`employee_id`
							AND `employee`.`status` = {$statusPublished}
							AND '{$date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$defaultEnd}')
						LEFT JOIN `employee_contract` ON
								`employee_contract`.`employee_id` = u.`employee_id`
							AND `employee_contract`.`status` = {$statusPublished}
							AND '{$date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$defaultEnd}')
							AND '{$date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$defaultEnd}')
						{$joinsSQL}
						WHERE
								u.`status` = {$statusPublished}
							AND '{$date}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$defaultEnd}')
							{$userWhereSQL}
							AND (`employee_contract`.`employee_contract_id` = '{from_fullname}' OR ('{from_fullname}' = '' AND (u.`employee_id` IS NULL OR u.`employee_id` = '')))
							AND ('{valid_from}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$defaultEnd}') OR '{valid_to}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$defaultEnd}'))
							AND {$rootEnableSQL}
						ORDER BY value
					",
					'array'	=> [["id" => "", "value" => ""]]
				]
			],
			'from_process_id' => [
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width' 		=> 220,
				'label_text'	=> Dict::getValue("from_process_id"),
				'options'      	=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
					'array'	=> Yang::arrayMerge([["id" => "ALL", "value" => Dict::getValue("all"), "default" => 1]], $rightsOptions)
				]
			],
			'to_fullname' => [
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width' 		=> 220,
				'label_text'	=> Dict::getValue("to_employee"),
				'onchange' 		=> ['to_username'],
				'line_break'	=> true,
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							`employee_contract`.`employee_contract_id` AS id,
							" . Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]) . " AS value
						FROM `employee`
						LEFT JOIN `employee_contract` ON
								`employee_contract`.`employee_id` = `employee`.`employee_id`
							AND `employee_contract`.`status` = {$statusPublished}
							AND '{$date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$defaultEnd}')
							AND '{$date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$defaultEnd}')
						{$joinsSQL}
						RIGHT JOIN `user` u ON
								u.`employee_id` = `employee`.`employee_id`
							AND '{$date}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$defaultEnd}')
							AND u.`status` = {$statusPublished}
						WHERE
								`employee`.`status` = {$statusPublished}
							{$whereSQL}
							AND '{$date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$defaultEnd}')
							AND ('{valid_from}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$defaultEnd}') OR '{valid_to}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$defaultEnd}'))
						ORDER BY value
					",
					'array'	=> [["id" => "", "value" => ""]]
				]
			],
			'to_username' => [
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width' 		=> 220,
				'label_text'	=> Dict::getValue("to_username"),
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							u.`user_id` AS id,
							u.`username` AS value
						FROM `user` u
						LEFT JOIN `employee` ON
								`employee`.`employee_id` = u.`employee_id`
							AND `employee`.`status` = {$statusPublished}
							AND '{$date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$defaultEnd}')
						LEFT JOIN `employee_contract` ON
								`employee_contract`.`employee_id` = u.`employee_id`
							AND `employee_contract`.`status`= {$statusPublished}
							AND '{$date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$defaultEnd}')
							AND '{$date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$defaultEnd}')
						{$joinsSQL}
						WHERE
								u.`status` = {$statusPublished}
							AND '{$date}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$defaultEnd}')
							AND (`employee_contract`.`employee_contract_id` = '{to_fullname}' OR ('{to_fullname}' = '' AND (u.`employee_id` IS NULL OR u.`employee_id` = '')))
							{$userWhereSQL}
							AND ('{valid_from}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$defaultEnd}') OR '{valid_to}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$defaultEnd}'))
							AND {$rootEnableSQL}
						ORDER BY value
					",
					'array'	=> [["id" => "", "value" => ""]]
				]
			],
			'to_process_id'	=> [
				'export'		=> true,
				'report_width' 	=> 20,
				'col_type'		=> 'combo',
				'width'			=> 220,
				'label_text'	=> Dict::getValue("to_process_id"),
				'default_value'	=> "ALL",
				'options'		=> [
					'mode' 	=>	Grid2Controller::G2BC_QUERY_MODE_ARRAY,
					'array'	=>	Yang::arrayMerge([["id" => "ALL", "value" => Dict::getValue("all"), "default"=>1]], $rightsOptions)
				]
			],
			'overwrite'		=> [
				'id' 			=> "overwrite",
				'default_value'	=> "no",
				'col_type' 		=> 'combo',
				'label_text' 	=> Dict::getValue("overwrite"),
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
					'array'	=> $type
				]
			]
		];

		$options= [
			['id'=>'Y', 'value'=>Dict::getValue("yes")],
			['id'=>'N', 'value'=>Dict::getValue("no")]
		];

		$columns['dhtmlxGrid'] = $this->columnRights($columns['dhtmlxGrid']);

		return $columns;
	}

	public function attributeLabels()
	{
		$labels=[];
		$labels['dhtmlxGrid'] =
		[
			'process_id'		=> Dict::getValue("process_id"),
			'related_model'		=> Dict::getValue("classification"),
			'fullname'			=> Dict::getValue("name"),
			'username'			=> Dict::getValue("user"),
			'approver_user_id'	=> Dict::getValue("user"),
			'related'			=> Dict::getValue("selected_group_or_employee"),
			'related_value'		=> Dict::getValue("selected_group_or_employee"),
			'level'				=> Dict::getValue("approver_process_level"),
			'note'				=> Dict::getValue("note"),
			'valid_from'		=> Dict::getValue("valid_from"),
			'valid_to'			=> Dict::getValue("valid_to"),
			'grid_valid_from'	=> Dict::getValue("valid_from"),
			'grid_valid_to'		=> Dict::getValue("valid_to")
		];

		$labels['copyDialog'] =
		[
			'from_fullname'	=> Dict::getValue("from_employee"),
			'from_username'	=> Dict::getValue("from_username"),
			'to_fullname'	=> Dict::getValue("to_employee"),
			'to_username'	=> Dict::getValue("to_username"),
			'valid_from'	=> Dict::getValue("valid_from"),
			'valid_to'		=> Dict::getValue("valid_to")
		];

		return $labels;
	}

	protected function getStatusButtons($gridID = null)
	{
		$buttons=[];
		if ($gridID==="dhtmlxGrid" && App::hasRight($this->getControllerID(), "copy"))
		{
			$buttons["openCopyDialog"] = [
				"type"		=> "button",
				"id"		=> "openCopyDialog",
				"class"		=> "openCopyDialog",
				"name"		=> "openCopyDialog",
				"img"		=> "/images/status_icons/st_copy.png",
				"label"		=> Dict::getValue("copy_user_rights"),
				"onclick"	=> "G2BDialogMultiGrid('dhtmlxGrid','copyDialog', null, 0,'./dialog','" . baseURL() . "/" . $this->getControllerID() . "/copyUserRights','./gridData',null,'" . Dict::getValue("copy_user_rights") . "',null);",
			];
			$buttons[] = [
				"type"	=> "selector",
			];
		}

		$parent = parent::getStatusButtons($gridID);
		if ($this->useCompanyAndPayrollRights) {
			$parent["openDelDialog"]["onclick"] = "G2BDelDialog('dhtmlxGrid','./gridData',null,$('.offCanvasSearchSection .to-serialize').serialize()+'&mainGridID={$gridID}',controllerRights['{$gridID}'],'./delete','".Dict::getValue("deleteRelatedRights")."','".Dict::getValue("please_select_line")."');";
		}

		return Yang::arrayMerge($buttons, $parent);
	}

	public function copyRightsAllToAllOverwrite($input)
	{
		$toRightsSQL = "
			SELECT
				`row_id`,
				`process_id`,
				`related_model`,
				`related_id`,
				`related_value`,
				`valid_from`,
				`valid_to`
			FROM `approver`
			WHERE
					`approver_user_id` = '" . $input["to_username"] . "'
				AND `status` = {$this->statusPublished}
				AND `valid_from` <= '" . $input["valid_to"] . "'
				AND `valid_to` >= '" . $input["valid_from"] . "'
		";
		$resultToRights = dbFetchAll($toRightsSQL);

		$fromRightsSQL = "
			SELECT
				`process_id`,
				`related_model`,
				`related_id`,
				`related_value`,
				`valid_to`
			FROM `approver`
			WHERE
					`approver_user_id` = '" . $input["from_username"] . "'
				AND `status` = {$this->statusPublished}
				AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->defaultEnd}')
		";
		$resultFromRights = dbFetchAll($fromRightsSQL);

		foreach ($resultToRights as $to) {
			$toApprover = Approver::model()->findByPk($to["row_id"]);
			$toApprover->status=Status::DELETED;
			$toApprover->save();
		}

		foreach ($resultFromRights as $from)
		{
			$toApprover = new Approver();
			$toApprover->approver_user_id	= $input["to_username"];
			$toApprover->process_id 		= $from["process_id"];
			$toApprover->related_model 		= $from["related_model"];
			$toApprover->related_id 		= $from["related_id"];
			$toApprover->related_value 		= $from["related_value"];
			$toApprover->valid_from 		= $input["valid_from"];
			$toApprover->valid_to 			= $input["valid_to"];
			$toApprover->status 			= $this->statusPublished;
			$toApprover->save();
		}
	}

	public function copyRightsAllToAllAdd($input)
	{
		$diffSQL = "
			SELECT
                a_from.`process_id`,
                a_from.`related_model`,
                a_from.`related_id`,
                a_from.`related_value`
			FROM `approver` a_from
			LEFT JOIN `approver` a_to ON
                    a_to.`process_id` = a_from.`process_id`
                AND	a_to.`related_model` = a_from.`related_model`
                AND	a_to.`related_id` = a_from.`related_id`
                AND	(a_to.`related_value` = a_from.`related_value` OR (a_to.`related_value` = 'ALL'))
                AND a_to.`status` = {$this->statusPublished}
                AND a_to.`approver_user_id` = '" . $input["to_username"] . "'
                AND CURDATE() BETWEEN a_to.`valid_from` AND IFNULL(a_to.`valid_to`, '{$this->defaultEnd}')
			WHERE
					a_from.`approver_user_id` = '" . $input["from_username"] . "'
				AND a_from.`status` = {$this->statusPublished}
				AND CURDATE() BETWEEN a_from.`valid_from` AND IFNULL(a_from.`valid_to`, '{$this->defaultEnd}')
				AND a_to.`row_id` IS NULL
		";
		$diffResult = dbFetchAll($diffSQL);

		foreach ($diffResult as $dr) {
			if ($dr["related_value"] == 'ALL') {
				$SQL = "
					SELECT
						`row_id`
					FROM `approver`
					WHERE
							`approver_user_id` = '" . $input["to_username"] . "'
						AND `process_id` = '" . $dr["process_id"] . "'
						AND `related_model` = '" . $dr["related_model"] . "'
						AND `related_id` = '" . $dr["related_id"] . "'
				";
				$result = dbFetchAll($SQL);

				foreach ($result as $r) {
					$delApprover = Approver::model()->findByPk($r["row_id"]);
					$delApprover->status = Status::DELETED;
					$delApprover->save();
				}
			}

			$toApprover = new Approver();
			$toApprover->approver_user_id	= $input["to_username"];
			$toApprover->process_id 		= $dr["process_id"];
			$toApprover->related_model 		= $dr["related_model"];
			$toApprover->related_id 		= $dr["related_id"];
			$toApprover->related_value 		= $dr["related_value"];
			$toApprover->valid_from 		= $input["valid_from"];
			$toApprover->valid_to 			= $input["valid_to"];
			$toApprover->status 			= $this->statusPublished;
			$toApprover->save();
		}
	}

	public function copyRightsOneToOneOverwrite($input)
	{
		$fromRightsSQL = "
			SELECT
				`related_model`,
				`related_id`,
				`related_value`
			FROM `approver`
			WHERE
					`approver_user_id` = '" . $input["from_username"] . "'
				AND `process_id` = '" . $input["from_process_id"] . "'
				AND `status` = {$this->statusPublished}
				AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->defaultEnd}')
		";
		$resultFromRights = dbFetchAll($fromRightsSQL);

		$delSQL = "
			SELECT
				`row_id`
			FROM `approver`
			WHERE
					`approver_user_id` = '" . $input["to_username"] . "'
				AND `process_id` = '" . $input["to_process_id"] . "'
				AND `status` = {$this->statusPublished}
		";
		$delResult = dbFetchAll($delSQL);

		foreach ($delResult as $dr) {
			$delApprover = Approver::model()->findByPk($dr["row_id"]);
			if ($this->useCompanyAndPayrollRights && $input["to_process_id"] == "companyMainData")
			{
				$newValue = "";
				foreach ($resultFromRights as $r) {
					if ($r["related_model"] == $delApprover->related_model && $r["related_id"] == $delApprover->related_id) {
						$newValue = $r["related_value"];
					}
				}
				$this->deleteRelatedRights($delApprover->process_id, $delApprover->related_model, $delApprover->approver_user_id, $delApprover->related_value, $newValue, $input["valid_from"], $input["valid_to"]);
			}
			$delApprover->status = Status::DELETED;
			$delApprover->save();
		}

		foreach ($resultFromRights as $rfr)
		{
			$toApprover = new Approver();
			$toApprover->approver_user_id	= $input["to_username"];
			$toApprover->process_id			= $input["to_process_id"];
			$toApprover->related_model		= $rfr["related_model"];
			$toApprover->related_id			= $rfr["related_id"];
			$toApprover->related_value		= $rfr["related_value"];
			$toApprover->valid_from			= $input["valid_from"];
			$toApprover->valid_to			= $input["valid_to"];
			$toApprover->status				= $this->statusPublished;
			$toApprover->save();
		}
	}

	public function copyRightsOneToOneAdd($input)
	{
		$diffSQL = "
			SELECT
				a_from.`process_id`,
				a_from.`related_model`,
				a_from.`related_id`,
				a_from.`related_value`
			FROM `approver` a_from
			LEFT JOIN `approver` a_to ON
					a_to.`related_model` = a_from.`related_model`
				AND	a_to.`related_id` = a_from.`related_id`
				AND	a_to.`related_value` = a_from.`related_value`
				AND a_to.`status` = {$this->statusPublished}
				AND a_to.`approver_user_id` = '" . $input["to_username"] . "'
				AND a_to.`process_id` = '" . $input["to_process_id"] . "'
				AND CURDATE() BETWEEN a_to.`valid_from` AND IFNULL(a_to.`valid_to`, '{$this->defaultEnd}')
			WHERE
					a_from.`approver_user_id` = '" . $input["from_username"] . "'
				AND a_from.`process_id` = '" . $input["from_process_id"] . "'
				AND a_from.`status` = {$this->statusPublished}
				AND CURDATE() BETWEEN a_from.`valid_from` AND IFNULL(a_from.`valid_to`, '{$this->defaultEnd}')
				AND a_to.`row_id` IS NULL
		";
		$diffResult = dbFetchAll($diffSQL);

		foreach ($diffResult as $dr)
		{
			$toApprover = new Approver();
			$toApprover->approver_user_id	= $input["to_username"];
			$toApprover->process_id			= $input["to_process_id"];
			$toApprover->related_model		= $dr["related_model"];
			$toApprover->related_id			= $dr["related_id"];
			$toApprover->related_value 		= $dr["related_value"];
			$toApprover->valid_from 		= $input["valid_from"];
			$toApprover->valid_to 			= $input["valid_to"];
			$toApprover->status 			= $this->statusPublished;
			$toApprover->save();
		}
	}

	public function copyRightsOneToAllOverwrite($input)
	{
		$fromRightsSQL = "
			SELECT
				`process_id`,
				`related_model`,
				`related_id`,
				`related_value`,
				`valid_to`
			FROM `approver`
			WHERE
					`approver_user_id` = '" . $input["from_username"] . "'
				AND `process_id` = '" . $input["from_process_id"] . "'
				AND `status` = {$this->statusPublished}
				AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$this->defaultEnd}')
		";
		$resultFromRights = dbFetchAll($fromRightsSQL);

		$delSQL = "
			SELECT
				`row_id`
			FROM `approver`
			WHERE
					`approver_user_id` = '" . $input["to_username"] . "'
				AND `status` = {$this->statusPublished}
		";
		$delResult = dbFetchAll($delSQL);

		foreach ($delResult as $dr) {
			$delApprover = Approver::model()->findByPk($dr["row_id"]);
			$delApprover->status = Status::DELETED;
			$delApprover->save();
		}

		$allProcessesSQL = "
			SELECT DISTINCT
				`lookup_value`
			FROM `app_lookup`
			WHERE
					`lookup_id` = 'approver_process_ids'
				AND `valid` = '1'
		";
		$resultAllProcesses = dbFetchAll($allProcessesSQL);

		foreach ($resultAllProcesses as $rap)
		{
			foreach ($resultFromRights as $rfr)
			{
				$toApprover = new Approver();
				$toApprover->approver_user_id	= $input["to_username"];
				$toApprover->process_id 		= $rap["lookup_value"];
				$toApprover->related_model 		= $rfr["related_model"];
				$toApprover->related_id 		= $rfr["related_id"];
				$toApprover->related_value 		= $rfr["related_value"];
				$toApprover->valid_from 		= $input["valid_from"];
				$toApprover->valid_to 			= $input["valid_to"];
				$toApprover->status 			= $this->statusPublished;
				$toApprover->save();
			}
		}
	}

	public function copyRightsOneToAllAdd($input)
	{
		$diffSQL = "
			SELECT
				a_from.`process_id`,
				a_from.`related_model`,
				a_from.`related_id`,
				a_from.`related_value`
			FROM (
				SELECT
                    al.`lookup_value` as process_id,
                    a_from.`related_model`,
                    a_from.`related_id`,
                    a_from.`related_value`
                FROM `approver` a_from
                LEFT JOIN `app_lookup` al ON
                        `lookup_id` = 'approver_process_ids'
                    AND `valid` = '1'
                WHERE a_from.`approver_user_id` = '" . $input["from_username"] . "'
                    AND a_from.`process_id` = '" . $input["from_process_id"] . "'
                    AND a_from.`status` = {$this->statusPublished}
                    AND CURDATE() BETWEEN a_from.`valid_from` AND IFNULL(a_from.`valid_to`, '{$this->defaultEnd}')
            ) a_from
			LEFT JOIN `approver` a_to ON
					a_to.`process_id` = a_from.`process_id`
				AND	a_to.`related_model` = a_from.`related_model`
				AND	a_to.`related_id` = a_from.`related_id`
				AND	(a_to.`related_value` = a_from.`related_value` OR (a_to.`related_value` = 'ALL'))
				AND a_to.`status` = {$this->statusPublished}
				AND a_to.`approver_user_id` = '" . $input["to_username"] . "'
			WHERE a_to.`row_id` IS NULL
		";
		$diffResult = dbFetchAll($diffSQL);

		foreach ($diffResult as $dr)
		{
			if ($dr["related_value"] == 'ALL')
			{
				$SQL = "
					SELECT
						`row_id`
					FROM `approver`
					WHERE
							`approver_user_id` = '" . $input["to_username"] . "'
						AND `process_id` = '" . $dr["process_id"] . "'
						AND `related_model` = '" . $dr["related_model"] . "'
						AND `related_id` = '" . $dr["related_id"] . "'
				";
				$result = dbFetchAll($SQL);

				foreach ($result as $r) {
					$delApprover = Approver::model()->findByPk($r["row_id"]);
					$delApprover->status = Status::DELETED;
					$delApprover->save();
				}
			}

			$toApprover = new Approver();
			$toApprover->approver_user_id	= $input["to_username"];
			$toApprover->process_id 		= $dr["process_id"];
			$toApprover->related_model 		= $dr["related_model"];
			$toApprover->related_id 		= $dr["related_id"];
			$toApprover->related_value 		= $dr["related_value"];
			$toApprover->valid_from 		= $input["valid_from"];
			$toApprover->valid_to 			= $input["valid_to"];
			$toApprover->status 			= $this->statusPublished;
			$toApprover->save();
		}
	}

	public function actionCopyUserRights()
	{
		$input  = requestParam('dialogInput_copyDialog');
		$result = ['status' => 1, 'error' => ''];

		$missingValuesError		= true;
		$allToOneError			= false;
		$sameFromToUserError	= false;

		if ($input["from_username"] != null && $input["to_username"] != null && $input["valid_from"] != null && $input["valid_to"] != null)
		{
			$missingValuesError = false;
			if ($input["from_process_id"] == 'ALL' && $input["to_process_id"] !== 'ALL') {
				$allToOneError = true;
			} elseif ($input["from_process_id"] == 'ALL' && $input["to_process_id"] == 'ALL') {
				if ($input["from_username"] === $input["to_username"]) {
					$sameFromToUserError = true;
				} else {
					if ($input["overwrite"] == "Y") {
						$this->copyRightsAllToAllOverwrite($input);
					} elseif ($input["overwrite"] == "N") {
						$this->copyRightsAllToAllAdd($input);
					}
				}
			} elseif ($input["from_process_id"] !== 'ALL') {
				if ($input["overwrite"] == "Y") {
					if ($input["to_process_id"] == 'ALL') {
						$this->copyRightsOneToAllOverwrite($input);
					} elseif ($input["to_process_id"] !== 'ALL') {
						$this->copyRightsOneToOneOverwrite($input);
					}
				} elseif ($input["overwrite"] == "N") {
					if ($input["to_process_id"] == 'ALL') {
						$this->copyRightsOneToAllAdd($input);
					} elseif ($input["to_process_id"] !== 'ALL') {
						$this->copyRightsOneToOneAdd($input);
					}
				}
			}
		}

		if ($missingValuesError) {
			$result["error"]	= Dict::getValue("empty_fields");
			$result['status'] 	= 0;
		} elseif ($allToOneError) {
			$result["error"] 	= Dict::getValue("error_all_to_one_copy");
			$result['status'] 	= 0;
		} elseif ($sameFromToUserError) {
			$result["error"] 	= Dict::getValue("same_from_to_user");
			$result['status'] 	= 0;
		}

		$this->delCache();
		echo json_encode($result);
	}

	/**
	 * Visszaadja a related value sql-t
	 * @return string
	 */
	protected function createRelatedValueSQL($employeeJoinsSQL, $employeeWhereSQL, $date, $useCompanyAndPayrollRights, $related_value_search)
	{
		$pub = Status::PUBLISHED;
		$end = App::getSetting("defaultEnd");
		$art				= new ApproverRelatedGroup;
		if ($useCompanyAndPayrollRights) {
			// Editing user
			$unitGargSQL	= $art->getApproverReleatedGroupSQL("Unit", ["companyMainData"], userID(), "'" . $date . "'", "AND", "allDate");
			$wgGargSQL		= $art->getApproverReleatedGroupSQL("Workgroup", ["companyMainData"], userID(), "'" . $date . "'", "AND", "allDate");
			$companyGargSQL	= $art->getApproverReleatedGroupSQL("Company", ["companyMainData"], userID(), "'" . $date . "'", "AND", "allDate");
			$payrollGargSQL	= $art->getApproverReleatedGroupSQL("Payroll", ["companyMainData"], userID(), "'" . $date . "'", "AND", "allDate");
			$cog1GargSQL	= $art->getApproverReleatedGroupSQL("CompanyOrgGroup1", ["companyMainData"], userID(), "'" . $date . "'", "AND", "allDate");
			$cog2GargSQL	= $art->getApproverReleatedGroupSQL("CompanyOrgGroup2", ["companyMainData"], userID(), "'" . $date . "'", "AND", "allDate");
			$cog3GargSQL	= $art->getApproverReleatedGroupSQL("CompanyOrgGroup3", ["companyMainData"], userID(), "'" . $date . "'", "AND", "allDate");
		} else {
			$unitGargSQL	= ["where" => ""];
			$wgGargSQL 		= ["where" => ""];
			$companyGargSQL	= ["where" => ""];
			$payrollGargSQL = ["where" => ""];
			$cog1GargSQL	= ["where" => ""];
			$cog2GargSQL	= ["where" => ""];
			$cog3GargSQL	= ["where" => ""];
		}

		$unionAll = "
			UNION
			SELECT
				'ALL' AS id,
				IF('Unit'				= '{related_model}', '" . Dict::getValue("unit_id")					. " " . Dict::getValue("all") . "',
				IF('Workgroup'			= '{related_model}', '" . Dict::getValue("workgroup_id")			. " " . Dict::getValue("all") . "',
				IF('Company'			= '{related_model}', '" . Dict::getValue("company_id")				. " " . Dict::getValue("all") . "',
				IF('Payroll'			= '{related_model}', '" . Dict::getValue("payroll_id")				. " " . Dict::getValue("all") . "',
				IF('EmployeeContract'	= '{related_model}', '" . Dict::getValue("employee_contract_id")	. " " . Dict::getValue("all") . "',
				IF('CompanyOrgGroup1'	= '{related_model}', '" . Dict::getValue("company_org_group1")		. " " . Dict::getValue("all") . "',
				IF('CompanyOrgGroup2'	= '{related_model}', '" . Dict::getValue("company_org_group2")		. " " . Dict::getValue("all") . "',
				IF('CompanyOrgGroup3'	= '{related_model}', '" . Dict::getValue("company_org_group3")		. " " . Dict::getValue("all") . "',
				IF('Competency'			= '{related_model}', '" . Dict::getValue("competency") 				. " " . Dict::getValue("all") . "',
				IF('Cost'				= '{related_model}', '" . Dict::getValue("cost") 					. " " . Dict::getValue("all") . "',
				IF('Employee'			= '{related_model}', '" . Dict::getValue("employee") 				. " " . Dict::getValue("all") . "',
				'')))))))))))
		";

		$SQL = "
			SELECT
				id,
				value
			FROM (
				SELECT
					`unit`.`unit_id` AS id,
					`unit`.`unit_name` AS value
				FROM `unit`
				LEFT JOIN `company` ON
						`company`.`company_id` = `unit`.`company_id`
					AND `company`.`status` = {$pub}
					AND '{$date}' BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$end}')
				LEFT JOIN `payroll` ON
						`payroll`.`payroll_id` = `unit`.`payroll_id`
					AND `payroll`.`status` = {$pub}
					AND '{$date}' BETWEEN `payroll`.`valid_from` AND IFNULL(`payroll`.`valid_to`, '{$end}')
				WHERE
						`unit`.`status` = {$pub}
					AND 'Unit' = '{related_model}'
					AND (CURDATE() BETWEEN `unit`.`valid_from` AND IFNULL(`unit`.`valid_to`, '{$end}'))
					{$unitGargSQL["where"]}
					AND IF('{process_id}' = 'companyMainData', 'companyMainDataUnit' = 'companyMainDataUnit', `unit`.`payroll_id` = 'ALL' OR (1=1 AND 'unit' = 'unit'))
					AND ('{valid_from}' BETWEEN `unit`.`valid_from` AND IFNULL(`unit`.`valid_to`, '{$end}') OR '{valid_to}' BETWEEN `unit`.`valid_from` AND IFNULL(`unit`.`valid_to`, '{$end}'))
				UNION
				SELECT
					`workgroup`.`workgroup_id` AS id,
					`workgroup`.`workgroup_name` AS value
				FROM `workgroup`
				LEFT JOIN `company` ON
						`company`.`company_id` = `workgroup`.`company_id`
					AND `company`.`status` = {$pub}
					AND '{$date}' BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$end}')
				LEFT JOIN `payroll` ON
						`payroll`.`payroll_id` = `workgroup`.`payroll_id`
					AND `payroll`.`status` = {$pub}
					AND '{$date}' BETWEEN `payroll`.`valid_from` AND IFNULL(`payroll`.`valid_to`, '{$end}')
				WHERE
						`workgroup`.`status` = {$pub}
					AND 'Workgroup' = '{related_model}'
					AND (CURDATE() BETWEEN `workgroup`.`valid_from` AND IFNULL(`workgroup`.`valid_to`, '{$end}'))
					{$wgGargSQL["where"]}
					AND IF('{process_id}' = 'companyMainData', 'companyMainDataWorkgoup' = 'companyMainDataWorkgroup', `workgroup`.`payroll_id` = 'ALL' OR (1=1 AND 'workgroup' = 'workgroup'))
					AND ('{valid_from}' BETWEEN `workgroup`.`valid_from` AND IFNULL(`workgroup`.`valid_to`, '{$end}') OR '{valid_to}' BETWEEN `workgroup`.`valid_from` AND IFNULL(`workgroup`.`valid_to`, '{$end}'))
				UNION
				SELECT
					`company`.`company_id` AS id,
					`company`.`company_name` AS value
				FROM `company`
				WHERE
						`company`.`status` = {$pub}
					AND 'Company' = '{related_model}'
					AND (CURDATE() BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$end}'))
					{$companyGargSQL["where"]}
					AND IF('{process_id}' = 'companyMainData', 'companyMainDataCompany' = 'companyMainDataCompany', 1=1 AND 'company' = 'company')
					AND ('{valid_from}' BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$end}') OR '{valid_to}' BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$end}'))
				UNION
				SELECT
					`payroll`.`payroll_id` AS id,
					`payroll`.`payroll_name` AS value
				FROM `payroll`
				LEFT JOIN `company` ON
						`company`.`company_id` = `payroll`.`company_id`
					AND `company`.`status` = {$pub}
					AND '{$date}' BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$end}')
				WHERE
						`payroll`.`status` = {$pub}
					AND 'Payroll' = '{related_model}'
					AND (CURDATE() BETWEEN `payroll`.`valid_from` AND IFNULL(`payroll`.`valid_to`, '{$end}'))
					{$payrollGargSQL["where"]}
					AND IF('{process_id}' = 'companyMainData', 'companyMainDataPayroll' = 'companyMainDataPayroll', `payroll`.`company_id` = 'ALL' OR (1=1 AND 'payroll' = 'payroll'))
					AND ('{valid_from}' BETWEEN `payroll`.`valid_from` AND IFNULL(`payroll`.`valid_to`, '{$end}') OR '{valid_to}' BETWEEN `payroll`.`valid_from` AND IFNULL(`payroll`.`valid_to`, '{$end}'))
				/*UNION
				SELECT DISTINCT
					'ALL' AS id,
					'" . Dict::getValue("payroll_id") . " " . Dict::getValue("all") . "' AS value
				FROM `payroll` p
				WHERE
						p.`status` = {$pub}
					AND 'Payroll' = '{related_model}'*/
				UNION
				SELECT
					`employee_contract`.`employee_contract_id` AS id,
					" . Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]) . " AS value
				FROM `employee_contract`
				LEFT JOIN `employee` ON
						`employee_contract`.`employee_id` = `employee`.`employee_id`
					AND `employee`.`status` = {$pub}
					AND (CURDATE() BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$end}'))
					AND ('{valid_from}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$end}') OR '{valid_to}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$end}'))
				{$employeeJoinsSQL}
				WHERE
						`employee_contract`.`status`= {$pub}
					AND 'EmployeeContract' = '{related_model}'
					{$employeeWhereSQL}
					AND IF('{process_id}' = 'companyMainData', 'companyMainDataEmployeeContract' = 'companyMainDataEmployeeContract', 1=1 AND 'employee_contract' = 'employee_contract')
					AND (CURDATE() BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$end}'))
					AND (CURDATE() BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$end}'))
					AND ('{valid_from}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$end}') OR '{valid_to}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$end}'))
					AND `employee`.`row_id` IS NOT NULL
				UNION
				SELECT
					`company_org_group_id` AS id,
					`company_org_group_name` AS value
				FROM `company_org_group1`
				LEFT JOIN `company` ON
						`company`.`company_id` = `company_org_group1`.`company_id`
					AND `company`.`status` = {$pub}
					AND '{$date}' BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$end}')
				LEFT JOIN `payroll` ON
						`payroll`.`payroll_id` = `company_org_group1`.`payroll_id`
					AND `payroll`.`status` = {$pub}
					AND '{$date}' BETWEEN `payroll`.`valid_from` AND IFNULL(`payroll`.`valid_to`, '{$end}')
				WHERE
						`company_org_group1`.`status` = {$pub}
					AND 'CompanyOrgGroup1' = '{related_model}'
					AND (CURDATE() BETWEEN `company_org_group1`.`valid_from` AND IFNULL(`company_org_group1`.`valid_to`, '{$end}'))
					{$cog1GargSQL["where"]}
					AND IF('{process_id}' = 'companyMainData', 'companyMainDataCompanyOrgGroup1' = 'companyMainDataCompanyOrgGroup1', `company_org_group1`.`payroll_id` = 'ALL' OR (1=1 AND 'company_org_group1' = 'company_org_group1'))
					AND ('{valid_from}' BETWEEN `company_org_group1`.`valid_from` AND IFNULL(`company_org_group1`.`valid_to`, '{$end}') OR '{valid_to}' BETWEEN `company_org_group1`.`valid_from` AND IFNULL(`company_org_group1`.`valid_to`, '{$end}'))
				UNION
				SELECT
					`company_org_group2`.`company_org_group_id` AS id,
					`company_org_group2`.`company_org_group_name` AS value
				FROM `company_org_group2`
				LEFT JOIN `company` ON
						`company`.`company_id` = `company_org_group2`.`company_id`
					AND `company`.`status` = {$pub}
					AND '{$date}' BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$end}')
				LEFT JOIN `payroll` ON
						`payroll`.`payroll_id` = `company_org_group2`.`payroll_id`
					AND `payroll`.`status` = {$pub}
					AND '{$date}' BETWEEN `payroll`.`valid_from` AND IFNULL(`payroll`.`valid_to`, '{$end}')
				WHERE
						`company_org_group2`.`status` = {$pub}
					AND 'CompanyOrgGroup2' = '{related_model}'
					AND (CURDATE() BETWEEN `company_org_group2`.`valid_from` AND IFNULL(`company_org_group2`.`valid_to`, '{$end}'))
					{$cog2GargSQL["where"]}
					AND IF('{process_id}' = 'companyMainData', 'companyMainDataCompanyOrgGroup2' = 'companyMainDataCompanyOrgGroup2', `company_org_group2`.`payroll_id` = 'ALL' OR (1=1 AND 'company_org_group2' = 'company_org_group2'))
					AND ('{valid_from}' BETWEEN `company_org_group2`.`valid_from` AND IFNULL(`company_org_group2`.`valid_to`, '{$end}') OR '{valid_to}' BETWEEN `company_org_group2`.`valid_from` AND IFNULL(`company_org_group2`.`valid_to`, '{$end}'))
				UNION
				SELECT
					`company_org_group3`.`company_org_group_id` AS id,
					`company_org_group3`.`company_org_group_name` AS value
				FROM `company_org_group3`
				LEFT JOIN `company` ON
						`company`.`company_id` = `company_org_group3`.`company_id`
					AND `company`.`status` = {$pub}
					AND '{$date}' BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$end}')
				LEFT JOIN `payroll` ON
						`payroll`.`payroll_id` = `company_org_group3`.`payroll_id`
					AND `payroll`.`status` = {$pub}
					AND '{$date}' BETWEEN `payroll`.`valid_from` AND IFNULL(`payroll`.`valid_to`, '{$end}')
				WHERE
						`company_org_group3`.`status` = {$pub}
					AND 'CompanyOrgGroup3' = '{related_model}'
					AND (CURDATE() BETWEEN `company_org_group3`.`valid_from` AND IFNULL(`company_org_group3`.`valid_to`, '{$end}'))
					{$cog3GargSQL["where"]}
					AND IF('{process_id}' = 'companyMainData', 'companyMainDataCompanyOrgGroup3' = 'companyMainDataCompanyOrgGroup3', `company_org_group3`.`payroll_id` = 'ALL' OR (1=1 AND 'company_org_group3' = 'company_org_group3'))
					AND ('{valid_from}' BETWEEN `company_org_group3`.`valid_from` AND IFNULL(`company_org_group3`.`valid_to`, '{$end}') OR '{valid_to}' BETWEEN `company_org_group3`.`valid_from` AND IFNULL(`company_org_group3`.`valid_to`, '{$end}'))
				UNION
				SELECT
					cost.`cost_id` AS id,
					cost.`cost_name` AS value
				FROM `cost` cost
				WHERE
						cost.`status` = {$pub}
					AND 'Cost' = '{related_model}'
					AND (CURDATE() BETWEEN cost.`valid_from` AND IFNULL(cost.`valid_to`, '{$end}'))
					AND ('{valid_from}' BETWEEN cost.`valid_from` AND IFNULL(cost.`valid_to`, '{$end}') OR '{valid_to}' BETWEEN cost.`valid_from` AND IFNULL(cost.`valid_to`, '{$end}'))
		";

		if (weHaveModule('ttwa-csm')) {
			$SQL .= "
				UNION
				SELECT
					comp.`competency_id` AS id,
					comp.`competency_name` AS value
				FROM `competency` comp
				WHERE
						comp.`status` = {$pub}
					AND 'Competency' = '{related_model}'
			";
			// valid from & to vizsgálat?
		}

		$SQL .= "
				UNION
				SELECT
					`employee`.`employee_id` AS id,
					" . Employee::getParam('fullname', 'employee') . " AS value
				FROM `employee`
				LEFT JOIN `employee_contract` ON
						`employee_contract`.`employee_id` = `employee`.`employee_id`
					AND `employee_contract`.`status` = {$pub}
					AND CURDATE() BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$end}')
					AND CURDATE() BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$end}')
				{$employeeJoinsSQL}
				WHERE
						`employee`.`status` = {$pub}
					AND 'Employee' = '{related_model}'
					AND (CURDATE() BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$end}'))
					{$employeeWhereSQL}
					AND IF('{process_id}' = 'companyMainData', 'companyMainDataEmployee' = 'companyMainDataEmployee', 1=1 AND 'employee' = 'employee')
					AND ('{valid_from}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$end}') OR '{valid_to}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$end}'))
				{$unionAll}
				) as value
				WHERE
				    1 = 1
				    {$related_value_search}
				ORDER
				    BY value
		";

		return $SQL;
	}

	protected function createRelatedValueSQLNotApprover()
	{
		$SQL = "
				SELECT
					id,
					value
				FROM (
					SELECT
						u.`unit_id` as id,
						u.`unit_name` AS value
					FROM `unit` u
					WHERE
							u.`status`=" . Status::PUBLISHED . " AND 'Unit' = '{related_model}'
						AND (CURDATE() BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '" . App::getSetting("defaultEnd") . "')
					)
				UNION
					SELECT w.`workgroup_id` as id,
					w.`workgroup_name` AS value
					FROM `workgroup` w
					WHERE w.`status`=" . Status::PUBLISHED . " AND 'Workgroup' = '{related_model}'
					AND (CURDATE() BETWEEN w.`valid_from` AND IFNULL(w.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
				UNION
					SELECT c.`company_id` as id,
					c.`company_name` AS value
					FROM `company` c
					WHERE c.`status`=" . Status::PUBLISHED . " AND 'Company' = '{related_model}'
					AND (CURDATE() BETWEEN c.`valid_from` AND IFNULL(c.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
				UNION
					SELECT p.`payroll_id` as id,
					p.`payroll_name` AS value
					FROM `payroll` p
					WHERE p.`status`=" . Status::PUBLISHED . " AND 'Payroll' = '{related_model}'
					AND (CURDATE() BETWEEN p.`valid_from` AND IFNULL(p.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
				UNION
					SELECT DISTINCT 'ALL' as id,
					'" . Dict::getValue("payroll_id") . " " . Dict::getValue("all") . "' AS value
					FROM `payroll` p
					WHERE p.`status`='2' AND 'Payroll' = '{related_model}'
				UNION
					SELECT ec.`employee_contract_id` as id,
					" . Employee::getParam('fullname_with_emp_id_ec_id', ["e", "ec"]) . " AS value
					FROM `employee_contract` ec
					LEFT JOIN `employee` e ON ec.`employee_id`=e.`employee_id` and e.`status`='2'
						AND (CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
					WHERE ec.`status`='2' AND 'EmployeeContract' = '{related_model}'
						AND (CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '" . App::getSetting("defaultEnd") . "'))
						AND (CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
						AND e.`row_id` IS NOT NULL
				UNION
					SELECT cog1.`company_org_group_id` as id,
					cog1.`company_org_group_name` AS value
					FROM `company_org_group1` cog1
					WHERE cog1.`status`=" . Status::PUBLISHED . " AND 'CompanyOrgGroup1' = '{related_model}'
						AND (CURDATE() BETWEEN cog1.`valid_from` AND IFNULL(cog1.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
				UNION
					SELECT cog2.`company_org_group_id` as id,
					cog2.`company_org_group_name` AS value
					FROM `company_org_group2` cog2
					WHERE cog2.`status`=" . Status::PUBLISHED . " AND 'CompanyOrgGroup2' = '{related_model}'
						AND (CURDATE() BETWEEN cog2.`valid_from` AND IFNULL(cog2.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
				UNION
					SELECT cog3.`company_org_group_id` as id,
					cog3.`company_org_group_name` AS value
					FROM `company_org_group3` cog3
					WHERE cog3.`status`=" . Status::PUBLISHED . " AND 'CompanyOrgGroup3' = '{related_model}'
						AND (CURDATE() BETWEEN cog3.`valid_from` AND IFNULL(cog3.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
				UNION
					SELECT
						cost.`cost_id` as id,
						cost.`cost_name` AS value
					FROM `cost` cost
					WHERE
							cost.`status`=" . Status::PUBLISHED . "
						AND 'Cost' = '{related_model}'
						AND (CURDATE() BETWEEN cost.`valid_from` AND IFNULL(cost.`valid_to`, '" . App::getSetting("defaultEnd") . "'))";

		if (weHaveModule('ttwa-csm')) {
			$SQL .= "
				UNION
					SELECT comp.`competency_id` as id,
					comp.`competency_name` AS value
					FROM `competency` comp
					WHERE comp.`status`=" . Status::PUBLISHED . " AND 'Competency' = '{related_model}'";
		}

		$SQL .=	"
				UNION
					SELECT e.`employee_id` as id,
						" . Employee::getParam('fullname', 'e') . " AS value
						FROM `employee` e
						WHERE e.`status`='2' AND 'Employee' = '{related_model}'
						AND (CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
				UNION
					SELECT 'ALL' as id,
						IF('Unit' = '{related_model}','" . Dict::getValue("unit_id") . " " . Dict::getValue("all") . "',
						IF('Workgroup' = '{related_model}', '" . Dict::getValue("workgroup_id") . " " . Dict::getValue("all") . "',
						IF('Company' = '{related_model}','" . Dict::getValue("company_id") . " " . Dict::getValue("all") . "',
						IF('Payroll' = '{related_model}','" . Dict::getValue("payroll_id") . " " . Dict::getValue("all") . "',
						IF('EmployeeContract' = '{related_model}','" . Dict::getValue("employee_contract_id") . " " . Dict::getValue("all") . "',
						IF('CompanyOrgGroup1' = '{related_model}','" . Dict::getValue("company_org_group1") . " " . Dict::getValue("all") . "',
						IF('CompanyOrgGroup2' = '{related_model}','" . Dict::getValue("company_org_group2") . " " . Dict::getValue("all") . "',
						IF('CompanyOrgGroup3' = '{related_model}','" . Dict::getValue("company_org_group3") . " " . Dict::getValue("all") . "',
						IF('Competency' = '{related_model}','" . Dict::getValue("competency") . " " . Dict::getValue("all") . "',
						IF('Cost' = '{related_model}','" . Dict::getValue("cost") . " " . Dict::getValue("all") . "',
						IF('Employee' = '{related_model}','" . Dict::getValue("employee") . " " . Dict::getValue("all") . "','')))))))))))) as value
				ORDER BY value";

		return $SQL;
	}

	/**
	 * Törlés
	 * @param string $modelName
	 * @param boolean $hasRight
	 * @return void
	 */
	public function actionDelete($modelName = null, $hasRight = false)
	{
		$this->layout = "//layouts/ajax";
		$this->G2BInit();
		$ids = explode(";", requestParam('ids'));

		foreach ($ids as $rowid)
		{
			$a = new Approver();
			$criteria = new CDbCriteria();
			$criteria->condition = "`row_id` = '" . $rowid . "'";
			$aI = $a->find($criteria);
			if ($aI) {
				$aI->status = Status::DELETED;
				$aI->modified_by = userID();
				$aI->modified_on = date("Y-m-d H:i:s");
				$aI->save();
				if ($aI->valid_to == null) { $aI->valid_to = $this->defaultEnd; }
				if ($this->useCompanyAndPayrollRights) {
					$this->deleteRelatedRights($aI->process_id, $aI->related_model, $aI->approver_user_id, $aI->related_value, "", $aI->valid_from, $aI->valid_to);
				}
				$this->delCache();
			}
		}

		$status = [
			'status'	=> 1,
			'pkSaved'	=> null,
			'error'		=> "",
		];

		return $status;
	}

	/**
	 * Mentés
	 * @param array $data
	 * @param string $modelName
	 * @param string $pk
	 * @param boolean $vOnly
	 * @param boolean $ret
	 * @param string $contentId
	 * @return void
	 */
	public function actionSave($data = [], $modelName = null, $pk = null, $vOnly = false, $ret = false, $contentId = null)
	{
		$this->layout = "//layouts/ajax";
		$this->G2BInit();

		$generateFrom	= requestParam('generateFrom');
		$data			= requestParam('dialogInput_' . $generateFrom);
		$rowId			= CHtml::value($data, 'row_id');
		$confirmed		= requestParam('confirmed');

		// Ha módosul a korábbiról akkor kövesse le az alárendelt viszonyt
		if ($rowId) {
        	$a = Approver::model()->find('row_id = :id', [':id' => $rowId]);
			$new = new Approver();
			$new->attributes = $data;
			if ($new->validate() && $this->useCompanyAndPayrollRights)
			{
				if ($a->valid_to == null) { $a->valid_to = $this->defaultEnd; }
				if ($confirmed) {
					parent::actionSave();
					$this->deleteRelatedRights($a->process_id, $a->related_model, $a->approver_user_id, $a->related_value, $data["related_value"], $a->valid_from, $a->valid_to);
					$this->delCache();
					exit();
				} else {
					$status = [
						'status'	=> "confirmAction",
						'msg'		=> ["title" => Dict::getValue("confirm"), "body" => Dict::getValue("deleteRelatedRights")]
					];
					echo json_encode($status);
					exit();
				}
			} else {
				$error = MyActiveForm::_validate($new);
				$arr = (array) json_decode($error);
				if(count($arr) == 1 && strpos($arr[0][0], 'error_approver_use') !== false && $this->useCompanyAndPayrollRights)
				{
					if ($a->valid_to == null) { $a->valid_to = $this->defaultEnd; }
					if ($confirmed) {
						parent::actionSave();
						$this->deleteRelatedRights($a->process_id, $a->related_model, $a->approver_user_id, $a->related_value, $data["related_value"], $a->valid_from, $a->valid_to);
						$this->delCache();
						exit();
					} else {
						$status = [
							'status'	=> "confirmAction",
							'msg'		=> ["title" => Dict::getValue("confirm"), "body" => Dict::getValue("deleteRelatedRights")]
						];
						echo json_encode($status);
						exit();
					}
				}
			}
        }

		parent::actionSave();
		$this->delCache();
	}

	/**
	 * Bácsi féle cache törlés
	 * @return void
	 */
	private function delCache() {
		AnyCache::destroyByName('getApproverReleatedGroupSQL');
		AnyCache::destroyByName('getApproverArray');
		unset($_SESSION["tiptime"]["visibility"]);
	}

	/**
	 * Kitörli az alárendelt jogokat módosításnál és törlésnél
	 * @param string $processId
	 * @param string $relatedModel
	 * @param string $approverUserId
	 * @param string $oldValue
	 * @param string $newValue
	 * @param string $validFrom
	 * @param string $validTo
	 * @return void
	 */
	private function deleteRelatedRights($processId, $relatedModel, $approverUserId, $oldValue, $newValue, $validFrom, $validTo)
	{
		if ($relatedModel == "Company" && $newValue != "ALL" && $oldValue != $newValue)
		{
			$tables =
			[
				"Payroll"			=> "payroll",
				"Workgroup"			=> "workgroup",
				"Unit"				=> "unit",
				"CompanyOrgGroup1"	=> "company_org_group1",
				"CompanyOrgGroup2"	=> "company_org_group2",
				"CompanyOrgGroup3"	=> "company_org_group3"
			];
			if ($processId == 'companyMainData') { $tables["Company"] = "company"; }
			if ($oldValue == "ALL") {
				$whereSQL = "AND t.`company_id` <> '{$newValue}'";
				$whereEmpSQL = "AND ((a.`related_model` = 'EmployeeContract' AND e.`company_id` <> '{$newValue}') OR (a.`related_model` = 'Employee' AND `employee`.`company_id` <> '{$newValue}'))";
			} else {
				$whereSQL = "AND t.`company_id` = '{$oldValue}'";
				$whereEmpSQL = "AND ((a.`related_model` = 'EmployeeContract' AND e.`company_id` = '{$oldValue}') OR (a.`related_model` = 'Employee' AND `employee`.`company_id` = '{$oldValue}'))";
			}
			foreach ($tables as $k => $t)
			{
				if ($k == "CompanyOrgGroup1" || $k == "CompanyOrgGroup2" || $k == "CompanyOrgGroup3") { $id = "company_org_group_id"; } else { $id = $t . "_id"; }
				$updateGroupsSQL = "
					UPDATE approver a
					JOIN {$t} t ON
							t.{$id} = a.`related_value`
						AND t.`status` = {$this->statusPublished}
						AND '{$validFrom}' <= IFNULL(t.`valid_to`, '{$this->defaultEnd}') AND t.`valid_from` <= '{$validTo}'
					SET a.`status` = " . Status::DELETED . ", a.`modified_by` = '" . userID() . "', a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
					WHERE
							a.`approver_user_id` = '{$approverUserId}'
						AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
						AND a.`related_model` = '{$k}'
						AND a.`status` = {$this->statusPublished}
						AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->defaultEnd}') AND a.`valid_from` <= '{$validTo}'
						{$whereSQL}
				";
				dbExecute($updateGroupsSQL);
			}

			$updateEmployeeSQL = "
				UPDATE approver a
				LEFT JOIN `employee_contract` ON
						`employee_contract`.`employee_contract_id` = a.`related_value`
					AND a.`related_model` = 'EmployeeContract'
					AND `employee_contract`.`status` = {$this->statusPublished}
					AND '{$validFrom}' <= IFNULL(`employee_contract`.`valid_to`, '{$this->defaultEnd}') AND `employee_contract`.`valid_from` <= '{$validTo}'
					AND '{$validFrom}' <= IFNULL(`employee_contract`.`ec_valid_to`, '{$this->defaultEnd}') AND `employee_contract`.`ec_valid_from` <= '{$validTo}'
				LEFT JOIN `employee` e ON
						e.`employee_id` = `employee_contract`.`employee_id`
					AND a.`related_model` = 'EmployeeContract'
					AND e.`status` = {$this->statusPublished}
					AND '{$validFrom}' <= IFNULL(e.`valid_to`, '{$this->defaultEnd}') AND e.`valid_from` <= '{$validTo}'
				LEFT JOIN `employee` ON
						`employee`.`employee_id` = a.`related_value`
					AND a.`related_model` = 'Employee'
					AND `employee`.`status` = {$this->statusPublished}
					AND '{$validFrom}' <= IFNULL(`employee`.`valid_to`, '{$this->defaultEnd}') AND `employee`.`valid_from` <= '{$validTo}'
				SET a.`status` = " . Status::DELETED . ", a.`modified_by` = '" . userID() . "', a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
				WHERE
						a.`approver_user_id` = '{$approverUserId}'
					AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
					AND a.`status` = {$this->statusPublished}
					AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->defaultEnd}') AND a.`valid_from` <= '{$validTo}'
					{$whereEmpSQL}
			";
			dbExecute($updateEmployeeSQL);

		} else if ($relatedModel == "Payroll" && $newValue != "ALL" && $oldValue != $newValue) {
			$tables =
			[
				"Workgroup"			=> "workgroup",
				"Unit"				=> "unit",
				"CompanyOrgGroup1"	=> "company_org_group1",
				"CompanyOrgGroup2"	=> "company_org_group2",
				"CompanyOrgGroup3"	=> "company_org_group3"
			];
			if ($processId == 'companyMainData') { $tables["Payroll"] = "payroll"; }
			if ($oldValue == "ALL") {
				$whereSQL = "AND t.`payroll_id` <> '{$newValue}'";
				$whereEmpSQL = "AND ((a.`related_model` = 'EmployeeContract' AND e.`payroll_id` <> '{$newValue}') OR (a.`related_model` = 'Employee' AND `employee`.`payroll_id` <> '{$newValue}'))";
			} else {
				$whereSQL = "AND t.`payroll_id` = '{$oldValue}'";
				$whereEmpSQL = "AND ((a.`related_model` = 'EmployeeContract' AND e.`payroll_id` = '{$oldValue}') OR (a.`related_model` = 'Employee' AND `employee`.`payroll_id` = '{$oldValue}'))";
			}
			foreach ($tables as $k => $t)
			{
				if ($k == "CompanyOrgGroup1" || $k == "CompanyOrgGroup2" || $k == "CompanyOrgGroup3") { $id = "company_org_group_id"; } else { $id = $t . "_id"; }
				$updateGroupsSQL = "
					UPDATE approver a
					JOIN {$t} t ON
							t.{$id} = a.`related_value`
						AND t.`status` = {$this->statusPublished}
						AND '{$validFrom}' <= IFNULL(t.`valid_to`, '{$this->defaultEnd}') AND t.`valid_from` <= '{$validTo}'
					SET a.`status` = " . Status::DELETED . ", a.`modified_by` = '" . userID() . "', a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
					WHERE
							a.`approver_user_id` = '{$approverUserId}'
						AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
						AND a.`related_model` = '{$k}'
						AND a.`status` = {$this->statusPublished}
						AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->defaultEnd}') AND a.`valid_from` <= '{$validTo}'
						{$whereSQL}
				";
				dbExecute($updateGroupsSQL);
			}

			$updateEmployeeSQL = "
				UPDATE approver a
				LEFT JOIN `employee_contract` ON
						`employee_contract`.`employee_contract_id` = a.`related_value`
					AND a.`related_model` = 'EmployeeContract'
					AND `employee_contract`.`status` = {$this->statusPublished}
					AND '{$validFrom}' <= IFNULL(`employee_contract`.`valid_to`, '{$this->defaultEnd}') AND `employee_contract`.`valid_from` <= '{$validTo}'
					AND '{$validFrom}' <= IFNULL(`employee_contract`.`ec_valid_to`, '{$this->defaultEnd}') AND `employee_contract`.`ec_valid_from` <= '{$validTo}'
				LEFT JOIN `employee` e ON
						e.`employee_id` = `employee_contract`.`employee_id`
					AND a.`related_model` = 'EmployeeContract'
					AND e.`status` = {$this->statusPublished}
					AND '{$validFrom}' <= IFNULL(e.`valid_to`, '{$this->defaultEnd}') AND e.`valid_from` <= '{$validTo}'
				LEFT JOIN `employee` ON
						`employee`.`employee_id` = a.`related_value`
					AND a.`related_model` = 'Employee'
					AND `employee`.`status` = {$this->statusPublished}
					AND '{$validFrom}' <= IFNULL(`employee`.`valid_to`, '{$this->defaultEnd}') AND `employee`.`valid_from` <= '{$validTo}'
				SET a.`status` = " . Status::DELETED . ", a.`modified_by` = '" . userID() . "', a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
				WHERE
						a.`approver_user_id` = '{$approverUserId}'
					AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
					AND a.`status` = {$this->statusPublished}
					AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->defaultEnd}') AND a.`valid_from` <= '{$validTo}'
					{$whereEmpSQL}
			";
			dbExecute($updateEmployeeSQL);

		} else if ($newValue != "ALL" && $oldValue != $newValue && ($relatedModel == "Workgroup" || $relatedModel == "Unit" || $relatedModel == "CompanyOrgGroup1" || $relatedModel == "CompanyOrgGroup2" || $relatedModel == "CompanyOrgGroup3"))
		{
			$tables =
			[
				"Workgroup"			=> "workgroup",
				"Unit"				=> "unit",
				"CompanyOrgGroup1"	=> "company_org_group1",
				"CompanyOrgGroup2"	=> "company_org_group2",
				"CompanyOrgGroup3"	=> "company_org_group3"
			];
			$contractOrEmployee =
			[
				"workgroup" 			=> "employee_contract",
				"unit"					=> "employee",
				"company_org_group1"	=> "employee",
				"company_org_group2"	=> "employee",
				"company_org_group3"	=> "employee"
			];

			if ($processId == 'companyMainData')
			{
				if ($tables[$relatedModel] == "CompanyOrgGroup1" || $tables[$relatedModel] == "CompanyOrgGroup2" || $tables[$relatedModel] == "CompanyOrgGroup3") { $id = "company_org_group_id"; } else { $id = $tables[$relatedModel] . "_id"; }
				if ($oldValue == "ALL") {
					$whereSQL = "AND t.{$id} <> '{$newValue}'";
				} else {
					$whereSQL = "AND t.{$id} = '{$oldValue}'";
				}
				$updateGroupsSQL = "
					UPDATE approver a
					JOIN {$tables[$relatedModel]} t ON
							t.{$id} = a.`related_value`
						AND t.`status` = {$this->statusPublished}
						AND '{$validFrom}' <= IFNULL(t.`valid_to`, '{$this->defaultEnd}') AND t.`valid_from` <= '{$validTo}'
					SET a.`status` = " . Status::DELETED . ", a.`modified_by` = '" . userID() . "', a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
					WHERE
							a.`approver_user_id` = '{$approverUserId}'
						AND a.`related_model` = '{$tables[$relatedModel]}'
						AND a.`status` = {$this->statusPublished}
						AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->defaultEnd}') AND a.`valid_from` <= '{$validTo}'
						{$whereSQL}
				";
				dbExecute($updateGroupsSQL);
			}

			if ($oldValue == "ALL") {
				$whereEmpContractSQL = "AND (a.`related_model` = 'EmployeeContract' AND " . EmployeeGroup::getActiveGroupSQL($tables[$relatedModel] . "_id", $contractOrEmployee[$tables[$relatedModel]]) . " <> '{$newValue}')";
				$whereEmpSQL = "AND (a.`related_model` = 'Employee' AND " . EmployeeGroup::getActiveGroupSQL($tables[$relatedModel] . "_id", $contractOrEmployee[$tables[$relatedModel]]) . " <> '{$newValue}')";
			} else {
				$whereEmpContractSQL = "AND (a.`related_model` = 'EmployeeContract' AND " . EmployeeGroup::getActiveGroupSQL($tables[$relatedModel] . "_id", $contractOrEmployee[$tables[$relatedModel]]) . " = '{$oldValue}')";
				$whereEmpSQL = "AND (a.`related_model` = 'Employee' AND " . EmployeeGroup::getActiveGroupSQL($tables[$relatedModel] . "_id", $contractOrEmployee[$tables[$relatedModel]]) . " = '{$oldValue}')";
			}

			$updateEmployeeContractSQL = "
				UPDATE approver a
				LEFT JOIN `employee_contract` ON
						`employee_contract`.`employee_contract_id` = a.`related_value`
					AND a.`related_model` = 'EmployeeContract'
					AND `employee_contract`.`status` = {$this->statusPublished}
					AND '{$validFrom}' <= IFNULL(`employee_contract`.`valid_to`, '{$this->defaultEnd}') AND `employee_contract`.`valid_from` <= '{$validTo}'
					AND '{$validFrom}' <= IFNULL(`employee_contract`.`ec_valid_to`, '{$this->defaultEnd}') AND `employee_contract`.`ec_valid_from` <= '{$validTo}'
				LEFT JOIN `employee` ON
						`employee`.`employee_id` = `employee_contract`.`employee_id`
					AND a.`related_model` = 'EmployeeContract'
					AND `employee`.`status` = {$this->statusPublished}
					AND '{$validFrom}' <= IFNULL(`employee`.`valid_to`, '{$this->defaultEnd}') AND `employee`.`valid_from` <= '{$validTo}'
				" . EmployeeGroup::getLeftJoinSQLWithoutCal($tables[$relatedModel], "employee_contract", "", "", "employee", "'{$validFrom}'", "'{$validTo}'") . "
				SET a.`status` = " . Status::DELETED . ", a.`modified_by` = '" . userID() . "', a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
				WHERE
						a.`approver_user_id` = '{$approverUserId}'
					AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
					AND a.`status` = {$this->statusPublished}
					AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->defaultEnd}') AND a.`valid_from` <= '{$validTo}'
					{$whereEmpContractSQL}
			";
			dbExecute($updateEmployeeContractSQL);

			$updateEmployeeSQL = "
				UPDATE approver a
				LEFT JOIN `employee` ON
						`employee`.`employee_id` = a.`related_value`
					AND a.`related_model` = 'Employee'
					AND `employee`.`status` = {$this->statusPublished}
					AND '{$validFrom}' <= IFNULL(`employee`.`valid_to`, '{$this->defaultEnd}') AND `employee`.`valid_from` <= '{$validTo}'
				LEFT JOIN `employee_contract` ON
						`employee_contract`.`employee_id` = `employee`.`employee_id`
					AND a.`related_model` = 'Employee'
					AND `employee_contract`.`status` = {$this->statusPublished}
					AND '{$validFrom}' <= IFNULL(`employee_contract`.`valid_to`, '{$this->defaultEnd}') AND `employee_contract`.`valid_from` <= '{$validTo}'
					AND '{$validFrom}' <= IFNULL(`employee_contract`.`ec_valid_to`, '{$this->defaultEnd}') AND `employee_contract`.`ec_valid_from` <= '{$validTo}'

				" . EmployeeGroup::getLeftJoinSQLWithoutCal($tables[$relatedModel], "employee_contract", "", "", "employee", "'{$validFrom}'", "'{$validTo}'") . "
				SET a.`status` = " . Status::DELETED . ", a.`modified_by` = '" . userID() . "', a.`modified_on` = '" . date("Y-m-d H:i:s") . "'
				WHERE
						a.`approver_user_id` = '{$approverUserId}'
					AND IF('{$processId}' = 'companyMainData', 1=1, a.`process_id` = '{$processId}')
					AND a.`status` = {$this->statusPublished}
					AND '{$validFrom}' <= IFNULL(a.`valid_to`, '{$this->defaultEnd}') AND a.`valid_from` <= '{$validTo}'
					{$whereEmpSQL}
			";
			dbExecute($updateEmployeeSQL);
		}
	}

    /**
     * Get computed value for approver user autocomplete
     *
     * @param int $id
     * @return mixed
     */
	private function getApproverUserIdComputedValue($id = 0)
    {
		$approverId = AnyCache::get("UserRight.getApproverUserIdComputedValueId");
		$result = AnyCache::get("UserRight.getApproverUserIdComputedValueResult");
		if ($id != $approverId)
		{
			$sql = "
				SELECT
					u.username AS value
				FROM
					approver a
				INNER JOIN
					user u ON a.approver_user_id = u.user_id
					AND u.status = {$this->statusPublished}
				WHERE
					a.row_id = '{$id}'
					AND a.status = {$this->statusPublished}

			";
			$result = dbFetchValue($sql);
			AnyCache::set("UserRight.getApproverUserIdComputedValueId", $id);
			AnyCache::set("UserRight.getApproverUserIdComputedValueResult", $result);
		}

        return $result;
    }

    /**
     * Get computed value for related value autocomplete
     *
     * @param int $id
     * @return mixed
     */
    private function getRelatedValueComputedValue($id = 0)
    {
		$approverId = AnyCache::get("UserRight.getRelatedValueComputedValueId");
		$result = AnyCache::get("UserRight.getRelatedValueComputedValueResult");
		if ($id != $approverId)
		{
			// Get approver data
			$sql = "
				SELECT
					a.related_model AS related_model,
					a.related_id AS related_id,
					a.related_value AS related_value
				FROM
					approver a
				WHERE
					a.row_id = '{$id}'
					AND a.status = {$this->statusPublished}
			";

			$approver = dbFetchRow($sql);
			if (!is_array($approver)) { return ''; }

			// Get related data
			$model        = $approver["related_model"];
			$column_id    = $approver["related_id"];
			$column_value = $approver["related_value"];
			$table        = $model::model()->tableSchema->name;

			$column_names = array(
				'Unit'             => 'a.unit_name',
				'Workgroup'        => 'a.workgroup_name',
				'Company'          => 'a.company_name',
				'Payroll'          => 'a.payroll_name',
				'EmployeeContract' => Employee::getParam('fullname_with_emp_id_ec_id', ["e", "ec"]),
				'CompanyOrgGroup1' => 'a.company_org_group_name',
				'CompanyOrgGroup2' => 'a.company_org_group_name',
				'CompanyOrgGroup3' => 'a.company_org_group_name',
				'Cost'             => 'a.cost_name',
				'Competency'       => 'a.competency_name'
			);

			if ($model == 'EmployeeContract') {

				$sql = "
					SELECT
						{$column_names[$model]} AS name
					FROM `employee_contract` ec
					INNER JOIN `employee` e ON
							ec.`employee_id` = e.`employee_id`
							AND e.`status` = {$this->statusPublished}
					WHERE
						ec.{$column_id} = '{$column_value}'
						AND ec.`status` = {$this->statusPublished}
						AND ec.`valid_from` <= IFNULL(e.`valid_to`, '{$this->defaultEnd}') AND e.`valid_from` <= IFNULL(ec.`valid_to`, '{$this->defaultEnd}')
						AND ec.`ec_valid_from` <= IFNULL(e.`valid_to`, '{$this->defaultEnd}') AND e.`valid_from` <= IFNULL(ec.`ec_valid_to`, '{$this->defaultEnd}')
					GROUP BY e.`employee_id`
				";
			} else {
				$sql = "
					SELECT
						{$column_names[$model]} AS name
					FROM
						{$table} a
					WHERE
						a.{$column_id} = '{$column_value}'
						AND a.status = {$this->statusPublished}
						AND CURDATE() BETWEEN a.valid_from and IFNULL(a.`valid_to`, '{$this->defaultEnd}')
				";
			}
			$result = dbFetchValue($sql);
			AnyCache::set("UserRight.getRelatedValueComputedValueId", $id);
			AnyCache::set("UserRight.getRelatedValueComputedValueResult", $result);
		}
        return $result;
    }
}
