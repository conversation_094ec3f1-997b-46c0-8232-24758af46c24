<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2History\Grid2HistoryController;
	use app\components\Grid2\Grid2Controller;
	use app\models\CostCenter;
	use app\models\Payroll;
	use app\models\Status;
	use Yang;

`/yii2-only';


class CostCenterController extends Grid2HistoryController
{
	private $payroll;
	private $user;
	private $adminId = '6acd9683761b153750db382c1c3694f6';
	private $defaultEnd;
	private $akhTabProfitcentrum;
	private $costcenterControllerShowId;

	public function __construct()
	{
		parent::__construct("costCenter");
		$this->user    = userID();
		$this->defaultEnd 	= App::getSetting("defaultEnd");
		$this->akhTabProfitcentrum = App::getSetting("akh_tab_profitcentrum");
		$this->costcenterControllerShowId = (int)App::getSetting('costcenterController_show_id');
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("CostCenter");

		$title = $this->akhTabProfitcentrum > 0 ? 'akh_tab_employeetabs_employeecost' : 'page_title_cost_center';

		parent::setControllerPageTitleId($title);
		parent::enableMultiGridMode();

		$this->LAGridRights->overrideInitRights("paging",			true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search",			true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search_header",	true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("select",			true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("multi_select",		true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("column_move",		true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload_sortings",	true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("col_sorting",		true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("details",			false, "dhtmlxGrid");

		$u = new CostCenter;
		$c = new CDbCriteria();
		$c->condition = "(`company_id` = '{company}' OR '{company}' = 'ALL' OR '{company}' = '') AND "
			. "(`payroll_id` = '{payroll}' OR '{payroll}' = 'ALL' OR '{payroll}' = '') AND "
			. "(`cost_center_id` = '{cost_center}' OR '{cost_center}' = 'ALL' OR '{cost_center}' = '') AND "
			. "('{valid_from}' = '' OR ('{valid_from}' BETWEEN `valid_from` AND default_end(`valid_to`))) AND "
			. " `status`=" . Status::PUBLISHED;
		$c->order = "`cost_center_name`";
		$this->LAGridDB->setModelSelection($u, $c, "dhtmlxGrid");

		$this->payroll = new Payroll;

		parent::G2BInit();
	}

	public function search()
	{
		return array(
			'valid_from'	=> array('col_type' => 'ed', 'dPicker' => true, 'width' => '*', 'label_text' => Dict::getValue("valid_from"), 'default_value' => date('Y-m-d')),

			'company'		=> array(
				'label_text' => Dict::getValue("company_id"),
				'col_type'	=> 'combo',
				'options'	=>	array(
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "SELECT c.`company_id` as id, c.`company_name` AS value
																		FROM `company` c
																		WHERE
																				c.`status`=" . Status::PUBLISHED . "
																			AND ('{valid_from}' BETWEEN c.`valid_from` and IFNULL(c.`valid_to`, '" . $this->defaultEnd . "'))
																		ORDER BY value",
					'array'	=> array(array("id" => "ALL", "value" => Dict::getValue("all"))),
				),
			),
			'payroll'		=> array(
				'label_text' => Dict::getValue("payroll_id"),
				'col_type'	=> 'combo',
				'options'	=>	array(
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "SELECT p.`payroll_id` as id, p.`payroll_name` AS value
																						FROM `payroll` p
																						WHERE
																								p.`status`=" . Status::PUBLISHED . "
																							AND ('{valid_from}' BETWEEN p.`valid_from` and IFNULL(p.`valid_to`, '" . $this->defaultEnd . "'))
																						ORDER BY value",
					'array'					=> array(array("id" => "ALL", "value" => Dict::getValue("all"))),
				),
			),
			'cost_center'	=> array(
				'col_type'	=> 'auto',
				'width'		=> '*',
				'label_text' => $this->akhTabProfitcentrum > 0 ? Dict::getValue("akh_tab_employeetabs_employeecost") : Dict::getValue("cost_center_name"),
				'options'	=>	array(
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "SELECT `cost_center_id` as id, `cost_center_name` AS value"
						. " FROM `cost_center`"
						. " WHERE `status` = " . Status::PUBLISHED . " AND (`cost_center_name` LIKE '%%{search}%%')"
						. " AND ('{valid_from}' BETWEEN `valid_from` AND default_end(`valid_to`))"
						. " ORDER BY `cost_center_name` ASC",
					'array'	=> array(array("id" => "ALL", "value" => Dict::getValue("all"))),
				),
			),
			'submit'		=> array('col_type' => 'searchBarReloadGrid', 'width' => '*', 'label_text' => ''),
		);
	}

	public function columns()
	{
		$id['dhtmlxGrid'] = [];
		$showCostCenterId = $this->costcenterControllerShowId === 1 || $this->user === $this->adminId;

		if ($showCostCenterId) {
			$id['dhtmlxGrid'] = ['cost_center_id' => ['grid' => true, 'width' => 200, 'window' => true, 'export' => true, 'col_type' => 'ed', 'edit' => false]];
		}

		$column["dhtmlxGrid"] = array(
			'cost_center_name'	=> array('grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'width' => '300'),
			'company_id'		=> array(
				'grid'		=> true,
				'window'	=> true,
				'export'	=> true,
				'col_type'	=> 'combo',
				'options'	=>	array(
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "SELECT c.`company_id` as id, c.`company_name` AS value
																			FROM `company` c
																			WHERE
																					c.`status`=" . Status::PUBLISHED . "
																				AND (CURDATE() BETWEEN c.`valid_from` and IFNULL(c.`valid_to`, '" . $this->defaultEnd . "'))
																			ORDER BY value",
					'array'	=> array(array("id" => "ALL", "value" => Dict::getValue("all"))),
				),
				'align'		=> 'left',
			),
			'payroll_id'		=> array(
				'grid'		=> true,
				'window'	=> true,
				'export'	=> true,
				'col_type'	=> 'combo',
				'options'	=>	array(
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "SELECT p.`payroll_id` as id, p.`payroll_name` AS value
																			FROM `payroll` p
																			WHERE
																					p.`status`=" . Status::PUBLISHED . "
																				AND (CURDATE() BETWEEN p.`valid_from` and IFNULL(p.`valid_to`, '" . $this->defaultEnd . "'))
																			ORDER BY value",
					'array'	=> array(array("id" => "ALL", "value" => Dict::getValue("all"))),
				),
				'align'		=> 'left',
			),
			'improductive'		=> array('grid' => false, 'window' => false, 'export' => false, 'col_type' => 'ed', 'line_break' => true),
			'valid_from'		=> array('grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'dPicker' => true),
			'valid_to'			=> array('grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'dPicker' => true)
		);
		if (Yang::customerDbPatchName() == 'eisberg') {
			$column["dhtmlxGrid"]["note"] = ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed'];
		}

		$column["multiEditDialog"] = ['multi_valid_to' => array('grid' => false, 'window' => true, 'export' => false, 'col_type' => 'ed', 'dPicker' => true)];

		return Yang::arrayMerge($id, $column);
	}

	/**
	 * Oszlop feliratok a Grid2-ben
	 * @return array
	 */
	public function attributeLabels()
	{
		$labels = [];

		// Main Grid
		$labels['dhtmlxGrid'] = [
			'cost_center_id'		=> Dict::getValue('cost_center_id'),
			'cost_center_name'		=> Dict::getValue('cost_center_name'),
			'company_id'			=> Dict::getValue('company_id'),
			'payroll_id'			=> Dict::getValue('payroll_id'),
			'improductive'			=> Dict::getValue('improductive'),
			'valid_from'			=> Dict::getValue('valid_from'),
			'valid_to'				=> Dict::getValue('valid_to'),
			'note'					=> Dict::getValue('note'),
		];

		$showCostCenterId = $this->costcenterControllerShowId === 1 || $this->user === $this->adminId;

		if (!$showCostCenterId && isset($labels['dhtmlxGrid']['cost_center_id'])) {
			unset($labels['dhtmlxGrid']['cost_center_id']);
		}

		// Csoportos érvényesség vége Grid
		$labels['multiEditDialog'] = [
			'multi_valid_to'		=> Dict::getValue('valid_to'),
		];

		return $labels;
	}

	/**
	 * Grid2 státusz gombok inícializálása
	 * @param string $gridID
	 * @return array
	 */
	protected function getStatusButtons($gridID = null)
	{
		// Main Grid gombok
		$actButtons = parent::getStatusButtons();

		$modButtons = [];

		// Csoportos érvényesség vége szerkesztés
		if (App::getRight($this->getControllerId(), "multiEdit")) {
			$modButtons["multiEditDialog"] =
				[
					"type"		=> "button",
					"id"		=> "multiEditDialog",
					"class"		=> "multiEditDialog",
					"name"		=> "multiEditDialog",
					"img"		=> "/images/status_icons/st_multimod.png",
					"label"		=> Dict::getValue("multi_edit"),
					"onclick"  => "G2BMultiEditValidToDialog('dhtmlxGrid','multiEditDialog','./dialog','" . baseURL() . "/" . $this->getControllerID() . "/saveMulti','./gridData',null,'" . Dict::getValue("multi_edit") . "','" . Dict::getValue("please_select_line") . "');"
				];
		}

		$buttons = Yang::arrayMerge($modButtons, $actButtons);

		return $buttons;
	}

	/**
	 * Csoportos érvényesség vége mentés
	 * @return void
	 */
	public function actionSaveMulti()
	{
		$requests = requestParam('dialogInput_multiEditDialog');
		$ids = explode(";", $requests["row_id"]);

		if ($requests["multi_valid_to"] == "" || !isset($requests["multi_valid_to"])) {
			$status = [
				'status'	=> 0,
				'pkSaved'	=> null,
				'error'		=> Dict::getValue("missing_parameter"),
			];
		} else {
			foreach ($ids as $rowId) {
				$cc = new CostCenter();
				$criteria = new CDbCriteria();
				$criteria->condition = "`row_id` = '" . $rowId . "'";
				$ccI = $cc->find($criteria);

				if ($ccI) {
					$ccI->valid_to = $requests["multi_valid_to"];
					$ccI->modified_by = userID();
					$ccI->modified_on = date("Y-m-d H:i:s");
					$ccI->save();
				}
			}

			$status = [
				'status'	=> 1,
				'pkSaved'	=> null,
				'error'		=> "",
			];
		}

		echo CJSON::encode($status);
	}
}
