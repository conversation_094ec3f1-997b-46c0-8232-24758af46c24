<?php

class TerminationOfEmploymentController extends Grid2Controller
{
	use EmployeeControllerWizards;
	use Grid2HistoryActions;
	use Grid2HistoryData;

	private $published = Status::PUBLISHED;
	
	public function __construct(){
		parent::__construct("TerminationOfEmployment");
		parent::enableLAGrid();
		$assetsPath = Yang::addAsset(Yang::getAlias('application.assets.base.terminationOfEmployement'), false, -1, true);
		Yang::registerScriptFile($assetsPath.'/js/terminationOfEmployement.js');
	}

	protected function G2BInit()
	{
		$this->LAGridDB->enableSplitRowID("_");
        $this->LAGridDB->setSplitRowIDStructure(["employee_id", "valid_to",]);
		$this->LAGridDB->setModelName("TerminationOfEmployment");
		$this->LAGridDB->setModelName('employeeTabHistory');

		parent::setControllerPageTitleId("page_title_termination_of_employment");
		
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);
		$this->LAGridRights->overrideInitRights("modify", false, "employeeTabHistory");

		$this->LAGridDB->enableArrMode();
		$this->LAGridDB->setPrimaryKey("employee_contract_id", "dthmxGrid");
		parent::setExportFileName(Dict::getValue("page_title_termination_of_employment"));
		parent::G2BInit();		
	}

	protected function dataArray($gridID, $filter)
	{
		$data = $this->getEmployeeData($filter);

		if(App::hasRight($this->getControllerID(), "openlocalmenu"))
		{
			for($i=0;$i<count($data);$i++)
			{
				$data[$i]['employee_name'] = '<a href="#" style="text-decoration:none;color:inherit;cursor:pointer;"
					onclick="openLocalMenu(event, \'params[employeeID]='.$data[$i]['employee_id'].'&params[date]='.$data[$i]['valid_to'].'\',
					\''.$data[$i]['employee_name'].'\')">' 
				 .$data[$i]['employee_name'] . '</a>';
			}
		}

		return $data;
	}


	protected function getEmployeeData($filter) {
	
		$employee_contract	= $filter['employee_contract']		!= '' ? " AND ec.employee_contract_id = '{$filter['employee_contract']}' " : '';
		$payroll			= $filter['payroll']				!= 'ALL' ? " AND p.payroll_id = '{$filter['payroll']}' " : '';
		$leader				= $filter['leader']					!= 'ALL' ? " AND ext.option7 = '{$filter['leader']}' " : '';
		$munkakor			= $filter['employee_position_id']	!= 'ALL' ? " AND ep.employee_position_id = '{$filter['employee_position_id']}' " : '';
		$allomanycsop		= isset($filter['company_org_group3']) ? ($filter['company_org_group3']	!= 'ALL' ? " AND company_org_group3.company_org_group_id = '{$filter['company_org_group3']}' " : '') : '';
		$publishedStatus 	= Status::PUBLISHED;

		$cost				= '';
		if (isset($filter['cost']))
		{
			if (is_array($filter['cost'])) {
				if (!in_array('ALL', $filter['cost'])) {
					$cost = " AND ecost.cost_id IN ('" . implode("', '", $filter['cost']) . "') ";
				}
			} else {
				$cost = $filter['cost'] != 'ALL' ? " AND cost_name LIKE '{$filter['cost']}' " : '';
			}
		}
		$unit				= '';
		if (isset($filter['unit'])) {
			if (is_array($filter['unit'])) {
				if (!in_array('ALL', $filter['unit'])) {
					$unit 	= " AND unit.unit_id IN ('" . implode("', '", $filter['unit']) . "') ";
				}
			} else {
				$unit 		= $filter['unit'] != 'ALL' ? " AND unit_name LIKE '{$filter['unit']}' " : '';
			}
		}

		$customer = App::getSetting("ptr_customer");
		$defaultEnd = App::getSetting("defaultEnd");
		
		if ($customer == "CARRIER")
		{
			$SELECTEET = "`dc`.`dict_value` AS ec_end_type,";
			$EETJOIN = 
			"
				LEFT JOIN app_lookup al ON `al`.`lookup_value` = `ec`.`ec_end_type`
					AND `al`.`lookup_id` = 'ec_end_type'
					AND `al`.`valid` = '1'
					
				LEFT JOIN dictionary dc ON `dc`.`dict_id` = `al`.`dict_id`
					AND `dc`.`lang` = '".Dict::getLang()."'
					AND `dc`.`valid` = '1'
			";
		} else {
			$SELECTEET = "`ec`.`ec_end_type` AS ec_end_type,";
			$EETJOIN = "";
		}

		$validFrom = $filter['valid_from'];
		$SQL = "
		SELECT 

				ec.employee_contract_id,
				e.emp_id AS emp_id, 
				".Employee::getParam('fullname', 'e')." as employee_name,
				".Employee::getParam('fullname', 'e')." as employee_name_hidden,				
				";
				if (Yang::getParam('customerDbPatchName') != 'carrier') 
				{
					$SQL.="dict_leader.dict_value AS vezeto,";
				} else {
					$SQL.="ext.option7 AS vezeto,";
				}				
				$SQL.= "
				ext.option2 AS allomany_kod,
				ec.valid_from,
				ec.valid_to,
				ec.ec_end_reason AS ec_end_reason,
				{$SELECTEET}
				ep.employee_position_name,
				e.employee_id

				FROM employee e

				LEFT JOIN employee_contract ec ON ec.employee_id = e.employee_id
					AND ec.`status` = $publishedStatus
				
				{$EETJOIN}
				";

				if(EmployeeGroupConfig::isActiveGroup('company_org_group3_id'))
				{
					$SQL.=EmployeeGroup::getLeftJoinSQL("company_org_group3_id", "ec", $validFrom);
				}
				$SQL.= "
				LEFT JOIN company_org_group3 ON
						company_org_group3.`company_org_group_id` = ".EmployeeGroup::getActiveGroupSQL("company_org_group3_id","e")."
					AND company_org_group3.`status` = $publishedStatus
					AND company_org_group3.`valid_from` <= '{$filter['valid_to']}'
					AND '{$filter['valid_from']}' <= IFNULL(company_org_group3.`valid_to`,'$defaultEnd')				
				LEFT JOIN (
					SELECT
						`employee_id`,
						MAX(`valid_to`) as valid_to
					FROM `employee_ext`
					WHERE `status` = 2 
						AND ( `employee_ext`.`valid_from` <= '{$filter['valid_to']}' 
						AND `employee_ext`.`valid_to` >='{$filter['valid_from']}' )
					GROUP BY `employee_id`
					) AS max_ext_valid_to ON 
						max_ext_valid_to.`employee_id` = e.`employee_id`
				LEFT JOIN employee_ext ext ON 
						ext.`employee_id` = max_ext_valid_to.`employee_id` 
					AND ext.`valid_to` = max_ext_valid_to.`valid_to`

				";
				if (Yang::getParam('customerDbPatchName') != 'carrier') 
				{
					$SQL.="
				LEFT JOIN `app_lookup` al_leader ON
						al_leader.`lookup_value` = ext.`option7`
					AND al_leader.`lookup_id` = 'option7'
					AND al_leader.`valid` = '1'
				LEFT JOIN dictionary dict_leader ON
						dict_leader.`dict_id` = al_leader.`dict_id`
					AND dict_leader.`lang` = '" . Dict::getLang() . "'
					AND dict_leader.`valid` = '1'";
				}	
				$SQL.="				
				LEFT JOIN employee_position ep ON ep.employee_position_id = ec.employee_position_id 
					AND ( ep.valid_from <= '{$filter['valid_to']}' AND ep.valid_to >='{$filter['valid_from']}' ) 
					AND ep.status = $publishedStatus
						
				LEFT JOIN payroll p ON
						e.`payroll_id` = p.`payroll_id`
					AND p.`valid_from` <= '{$filter['valid_to']}' AND p.valid_to >='{$filter['valid_from']}'
					AND p.`status` = $publishedStatus
				";
				if(EmployeeGroupConfig::isActiveGroup('unit_id'))
				{
					$SQL.=EmployeeGroup::getLeftJoinSQL("unit_id");
				}
		$SQL .= "
				LEFT JOIN `unit` ON
						unit.`status`= {$publishedStatus}
					AND unit.`unit_id`=" . EmployeeGroup::getActiveGroupSQL("unit_id", "e") . "
					AND e.`valid_from` <= IFNULL(unit.`valid_to`, '" . $defaultEnd . "')
					AND unit.`valid_from`<= e.`valid_to`
				LEFT JOIN employee_cost ecost ON
					ecost.employee_contract_id = ec.employee_contract_id
					AND e.`valid_from` <= IFNULL(ecost.`valid_to`, '" . $defaultEnd . "')
					AND ecost.`valid_from`<= e.`valid_to`
					AND ecost.`status` = {$publishedStatus}
				WHERE
					e.`status` = {$publishedStatus}
				AND e.valid_to BETWEEN '{$filter['valid_from']}' AND '{$filter['valid_to']}' 
				AND ec.valid_to BETWEEN '{$filter['valid_from']}' AND '{$filter['valid_to']}'
				AND e.valid_to = (SELECT MAX(valid_to)
									FROM employee e2
									WHERE e2.employee_id = e.employee_id
									AND e2.status = $publishedStatus)
                AND ec.valid_to = (SELECT MAX(valid_to)
									FROM employee_contract ec2
									WHERE ec2.employee_contract_id = ec.employee_contract_id
									AND ec2.status = $publishedStatus)

				".$employee_contract."
				".$payroll."
				".$leader." 
				".$munkakor."
				".$allomanycsop."
				".$unit."
				".$cost."
				GROUP BY emp_id
				ORDER BY employee_name

				;";

		return dbFetchAll($SQL);
	}
	
	
	public function search(){
		
		$filter = requestParam('searchInput');
		$ret = $this->getPreDefinedSearchFromDb("workForce", false);
		$customer = Yang::getParam('customerDbPatchName');
				
		$leader = [
				'col_type'			=> 'combo',
				'multiple'			=> false,
				'options'			=>	[
											'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
											'sql'	=> "SELECT DISTINCT eext.option7 as id, eext.option7 as value
														FROM employee_ext eext
														WHERE eext.option7 IS NOT NULL
														AND eext.option7 != ''
														;",
											'array'	=> ['options' => ["id"=>"ALL","value"=>Dict::getValue("all")]],
										],
				'label_text'		=> Dict::getValue("workplace_leader"),
				'onchange'			=> array('employee_contract'),
				'default_value'		=> "ALL"
		];

		$employee_position_id = [
				'col_type'			=> 'combo',
				'multiple'			=> false,
				'options'			=>	[
											'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
											'sql'	=> "SELECT ep.employee_position_id as id, ep.employee_position_name as value 
														FROM employee_position ep
														WHERE ep.employee_position_id IS NOT NULL
														AND ep.employee_position_id != ''
														AND ep.employee_position_name IS NOT NULL
														AND ep.employee_position_name != ''
														;",
											'array'	=> ['options' => ["id"=>"ALL","value"=>Dict::getValue("all")]],
										],
				'label_text'		=> Dict::getValue("employee_position_name"),
				'onchange'			=> array(''),
				'default_value'		=> "ALL"
		];

		
		unset($ret['company']);
		unset($ret['workgroup']);
		if ($customer !== 'carrier') {
			unset($ret['unit']);
		}
		
		$return = array_slice($ret, 0, -2) 
					+ ['leader' => $leader]
					+ ['employee_position_id' => $employee_position_id] 
				+ array_slice($ret, -2);
		
		$return['valid_from']['label_text'] = Dict::getValue("valid_to");

		return $return;
		
	}

	
	public function columns(){
		
		return [
			'emp_id'					=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '120',],
			'employee_name'				=> ['grid' => true, 'window' => true, 'export' => false, 'col_type' => 'ed', 'align' => 'left', 'width' => '150',],
			'employee_name_hidden'		=> ['grid' => false, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150',],
			'vezeto'					=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150',],
			'allomany_kod'				=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150',],
			'valid_from'				=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150',],
			'valid_to'					=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150',],
			'ec_end_reason'				=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150',],
			'ec_end_type'				=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150',],
			'employee_position_name'	=> ['grid' => true, 'window' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '120',],
		];

	}
	
	public function attributeLabels(){
		
		return [
			'emp_id'					=> Dict::getValue("emp_id"),
			'employee_name'				=> Dict::getValue("employee_name"),
			'employee_name_hidden'		=> Dict::getValue("employee_name"),
			'vezeto'					=> Dict::getValue("workplace_leader"),
			'allomany_kod'				=> Dict::getValue("option2"),
			'valid_from'				=> Dict::getValue("valid_from"), 
			'valid_to'					=> Dict::getValue("valid_to"),
			'ec_end_reason'				=> Dict::getValue("ec_end_reason"),
			'ec_end_type'				=> Dict::getValue("ec_end_type"), 
			'employee_position_name'	=> Dict::getValue("employee_position_name"),
			
		];
	}

	public function actionLocalMenu()
	{
		$this->layout = "//layouts/ajax";
		$publishedStatus = Status::PUBLISHED;

		$params = requestParam('params');

		if (isset($params["employeeID"])) {
			$employeeID = $params["employeeID"];
		}

		if(isset($params["date"])){
			$date = $params["date"];
		}
		$resp = '';
		$resp .= '<div class="line"></div>';
		if( App::hasRight('employee','modify') )
		{
			$pageTitle = Dict::getValue('page_title_employee_management');
			$pageTitleModify = $pageTitle.' - '.Dict::getValue('modify');
			$resp .= '<div class="item" id="employeeManagementButton" onclick="openEmployeeManagementDialog(\''.$employeeID.'\',\''.$date.'\', \''.$pageTitle.'\', \''.$pageTitleModify.'\')">' . Dict::getValue('employee_management') . '</div>';
		}

		$title = '';

		if (isset($params["title"]) && !empty($params["title"])) {
			$title = $params["title"];
		}

		$results = array(
			'data' => $resp,
		);

		echo json_encode($results);
	}
}
