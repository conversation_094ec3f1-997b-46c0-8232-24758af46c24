<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\Controller;
	use app\components\FS;
	use Yang;

`/yii2-only';


class FilestorageController extends Controller
{
	public function actionUploadLocalFile() {
		if (requestParam('file')) {
			/**
			 * PHP rawUrlEncode() == JavaScript encodeURIComponent()
			 * PHP rawUrlDecode() == JavaScript decodeURIComponent()
			 */
			$file = rawUrlDecode(requestParam('file'));
			$fs = new FS();
			$fs->uploadFile($file);
			$fs->getMessage();
		}
	}

	/**
	 * HTML <img> elembe csomagolva visszadja a kért képet.
	 *
	 * @param string $file_id a vágyott képfájl elérési útja
	 * @param ?string $style opcionális inline css tulajdonságok az img elem 
	 * style attribútumához
	 * @return string a kép <img> html-tagben, vagy üres string, ha a kép nem van.
	 */
	public static function getImage(string $file_id, ?string $style = null): string 
	{
		$fs = new FS($file_id);
		return $fs->showImageFile($style, false);
	}

	public function actionShowImageFile() {
		if (requestParam('file_id')) {
			$file_id = requestParam('file_id');
			$style = requestParam('style')?rawUrlDecode(requestParam('style')):"";
			$fs = new FS($file_id);
			$fs->showImageFile($style);
		}
	}

	public function actionShowPDFFile() {
		if (requestParam('file_id')) {
			$file_id = requestParam('file_id');
			$style = requestParam('style')?rawUrlDecode(requestParam('style')):"";
			$fs = new FS($file_id);
			$fs->downloadFile("inline");
		}
	}

	public function actionShowDocsFile() {
		if (requestParam('file_id')) {
			$type = requestParam('type');

			$file_id = requestParam('file_id');
			$fs = new FS($file_id);

			if ((int)$type) {
				echo $fs->getFileType();
			} else {
				$fs->downloadFile("inline");
			}
		}
	}

	public function actionShowTextFile() {
		if (requestParam('file_id')) {
			$file_id = requestParam('file_id');
			$fs = new FS($file_id);
			$fs->showTextFile();
		}
	}

    public function actionShowHeaderFooterTextFile() {
        if (requestParam('file_id') && requestParam('header_footer')) {
            $file_id = requestParam('file_id');
            $headerFooter = requestParam('header_footer');
            $fs = new FS($file_id, $headerFooter);
            $fs->showHeaderFooterTextFile();
        }
    }

	public function actionDownloadFile() {
		if (requestParam('file_id')) {
			$file_id = requestParam('file_id');
			$fs = new FS($file_id);
			$fs->downloadFile();
		}
	}

	public function actionDeleteFile() {
		if (requestParam('file_id')) {
			$file_id = requestParam('file_id');
			$fs = new FS($file_id);
			$fs->deleteFile();
			$fs->getMessage();
		}
	}

	public function actionTest() {
		$file = Yang::getBasePath()."/../webroot/images/logo/login-logo.png";

		$fs = new FS();
		$fs->uploadFile($file);
		$fs->getMessage();
	}
}

?>