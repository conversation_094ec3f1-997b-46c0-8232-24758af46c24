<?php

/**
 * Description of ReportSqlFieldsController
 *
 * <AUTHOR>
 */
class ReportSqlFieldsController extends Grid2Controller
{
	protected $filters;
	protected $gridMode=1;
	protected $onlyReport=false;
	protected $activities=array();
	protected $payrollUsers=array();
	protected $onlySum=false;

	public function __construct()
	{
			WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__, ['index' => '1']);
		parent::__construct("reportSqlFields");
			WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__, ['index' => '1']);
	}

	public function actionSetInitProperties()
	{
			WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__, ['index' => '1']);
		Yang::setSessionValue('report_sql_fields_filters', requestParam('searchInput'));
			WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__, ['index' => '1']);
		parent::actionSetInitProperties();
	}

	protected function G2BInit()
	{
		WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__, ['index' => '1']);
		$title="page_title_report_sql_fields";
		$model="ReportModels";
		$title="page_title_report_models";
		if (!is_null(Yang::session('report_sql_fields_filters')))
		{
			$this->filters = Yang::session('report_sql_fields_filters');
			$filter=$this->filters;
			$activity=(isset($this->filters['activity'])) ? $this->filters['activity'] : 0;
			WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__, ['activity' => $activity]);
			//$valid_month=$filter['yearMonth'];
			{
				$this->gridMode=($activity<100) ? $activity : 1;
				if ($activity==1)
				{
					$model="ReportModels";
					$title="page_title_report_models";
				}
				elseif ($activity==2)
				{
					$model="ReportModelFields";
					$title="page_title_report_model_fields";
				}
				elseif ($activity==3)
				{
					$model="ReportModelFilters";
					$title="page_title_report_model_filters";
				}
				elseif ($activity==4)
				{
					$model="ReportModelReports";
					$title="page_title_report_model_reports";
				}
				elseif ($activity==5)
				{
					$model="ReportModelReportFields";
					$title="page_title_report_model_report_fields";
				}
			}
		}

		WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__, ['title' => $title]);
		parent::setControllerPageTitleId($title);

		WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__, ['model' => $model]);
		$this->LAGridDB->setModelName($model);

		$u = new $model;
		$c = new CDbCriteria();
		//$c->order = "`payroll_name`";
		$this->LAGridDB->setModelSelection($u, $c);

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);
		$this->LAGridRights->overrideInitRights("export_pdf_node",	true);

		parent::setReportOrientation("landscape");

		parent::G2BInit();
	}

	public function search()
	{
		WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__);
		$defaultEnd	= App::getSetting("defaultEnd");
		$userId = userID();
		$empName= Employee::getEmployeeFullnameByUserID($userId);
		if (empty($empName)) $empName="root";


		$monthOptions = array();
		$year = date('Y');
		$month = date('m');
		$actualMonth = $year.'-'.$month;

		$i = 0;
		for($y=$year-1; $y<=$year+1; $y++) {
			for($m=1; $m<=12; $m++) {
				if(strlen($m) == 1) {
					$m = '0'.$m;
				}
				$monthOptions[$i]['id'] = $y.'-'.$m;
				$monthOptions[$i]['value'] = $y.'-'.$m;
				$i++;
			}
		}

		$payrollSQL="select payroll_id as id, payroll_name as value from payroll where status=".Status::PUBLISHED."";
		$payrollSQL="select 0 as id, 'Összes' as value union ".$payrollSQL;

		$employeeSQL="select * from (select employee_id as id, concat(last_name, ' ', first_name, ' ', employee_id) as value from employee "
				. "where status=2 and left(valid_from,7)<='{yearMonth}' and left(IFNULL(valid_to, '$defaultEnd'),7)>='{yearMonth}'"
				. " order by value) e";
		$employeeSQL="select 0 as id, 'Összes' as value union ".$employeeSQL;

		$activityArray=array();
		{
			{
				$activityArray[]=array('id'=> '1', 'value'	=> 'Modellek (adathalmazok)'); //Dict::getValue("work_type_flexible")),
				$activityArray[]=array('id'=> '2', 'value'	=> 'Model (adathalmaz) mezők'); //Dict::getValue("work_type_framework")),
				$activityArray[]=array('id'=> '3', 'value'	=> 'Model (adathalmaz) szűrők'); //Dict::getValue("work_type_framework")),
				$activityArray[]=array('id'=> '4', 'value'	=> 'Model (adathalmaz) reportok'); //Dict::getValue("work_type_framework")),
				$activityArray[]=array('id'=> '5', 'value'	=> 'Report mezők'); //Dict::getValue("work_type_framework")),
				/*
				$activityArray[]=array('id'=> '6', 'value'	=> 'User event/Table/To show'); //Dict::getValue("work_type_framework")),
				$activityArray[]=array('id'=> '7', 'value'	=> 'User event/Replacement table'); //Dict::getValue("work_type_framework")),
				$activityArray[]=array('id'=> '8', 'value'	=> 'User event/Érintett táblák'); //Dict::getValue("work_type_framework")),
				$activityArray[]=array('id'=> '9', 'value'	=> 'User event'); //Dict::getValue("work_type_framework")),
				$activityArray[]=array('id'=> '10', 'value' => 'User event típusok'); //Dict::getValue("work_type_framework")),
				$activityArray[]=array('id'=> '11', 'value'	=> 'TimeSheet mezők'); //Dict::getValue("work_type_framework")),
				$activityArray[]=array('id'=> '12', 'value'	=> 'Report TimeSheet mezők'); //Dict::getValue("work_type_framework")),
				$activityArray[]=array('id'=> '101', 'value' => 'User event log-1'); //Dict::getValue("work_type_framework")),
				$activityArray[]=array('id'=> '102', 'value' => 'User event log-2'); //Dict::getValue("work_type_framework")),
				 *
				 */
			}

		}

		return array(
			'yearMonth'		=> array(
							'label_text' 	=> Dict::getModuleValue("ttwa-base",'year') . ' és '. Dict::getModuleValue("ttwa-base",'month'),
							'col_type' 		=> 'ed',
							'default_value' => date("Y-m"),
							'mPicker' 		=> true,
							//'onchange' => array("archiv", "correction"),
								),
			'activity'	=>	array(
									'label_text'=> 'Művelet (tábla)', //Dict::getValue("work_type"),
									'col_type'	=> 'combo',
									'options'	=>	array(
													'mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,
													'array'=> $activityArray,
												),
								),
			//'submit'		=> array('col_type'=>'submitButton', 'width'=>'*', 'label_text'=>''),
			//'submit'		=> array('col_type'=>'searchBarReloadGrid', 'width'=>'*', 'label_text'=>''),
			'submit'		=> array('col_type'=>'searchBarReinitGrid', 'width'=>'*', 'label_text'=>''),
		);
	}

	/**
	 * @return array customized grid columns (name=>params)
	 */
/*					if ($model && $CActiveForm) {
						if ($model->hasAttribute($column)) {
							$inputTag = $CActiveForm->dropDownList($model,$column,array(),array('id'=>$inputPrefix."_".$column,'name'=>$inputPrefix."[".$column."]",'class'=>'g2b styled-select to-serialize '.$attrs['class'],'disabled'=>false?'true':''));
						} else {
							//if (isset($attrs['multiple']))
								//$inputTag = CHtml::dropDownList($inputPrefix."[".$column."]",$attrs['default_value'],array(),array('class'=>'g2b styled-select to-serialize '.$attrs['class'], 'multiple'=>'multiple'));
							//else
								$inputTag = CHtml::dropDownList($inputPrefix."[".$column."]",$attrs['default_value'],array(),array('class'=>'g2b styled-select to-serialize '.$attrs['class']));
						}
					} else {
						//if (isset($attrs['multiple']))
							//$inputTag = CHtml::dropDownList($inputPrefix."[".$column."]",$attrs['default_value'],array(),array('class'=>'g2b styled-select to-serialize '.$attrs['class'], 'multiple'=>'multiple'));
						//else
							$inputTag = CHtml::dropDownList($inputPrefix."[".$column."]",$attrs['default_value'],array(),array('class'=>'g2b styled-select to-serialize '.$attrs['class']));
					}
*/
	public function columns()
	{
		WriteUserEvent::writeEvent(99, __CLASS__, __FUNCTION__, __LINE__, ['gridmode' => $this->gridMode]);
		$relationSQL		= "SELECT '' AS id, 'Nincs művelet' AS value ";
		$relationSQL	   .= "UNION SELECT 'AND' AS id, 'AND' AS value ";
		$relationSQL	   .= "UNION SELECT 'OR' AS id, 'OR' AS value ";
		$muveletSQL			= "SELECT '=0' AS id, '=0' AS value ";
		$muveletSQL		   .= "UNION SELECT '<0' AS id, '<0' AS value ";
		$muveletSQL		   .= "UNION SELECT '>0' AS id, '>0' AS value ";
		$muveletSQL		   .= "UNION SELECT '!=0' AS id, '!=0' AS value ";
		$muveletSQL		   .= "UNION SELECT '<=0' AS id, '<=0' AS value ";
		$muveletSQL		   .= "UNION SELECT '>=0' AS id, '>=0' AS value ";
		$yesNoSQL			= "SELECT 0 AS id, 'Nem' AS value ";
		$yesNoSQL		   .= "UNION SELECT 1 AS id, 'Igen' AS value ";
		$filterSQL			= "SELECT 0 AS id, 'Nincs szűrő' AS value ";
		$filterSQL		   .= "UNION SELECT filter_id AS id, CONCAT(m.model_name, ': ', f.filter_name) AS value"
							. " FROM report_model_filters f LEFT JOIN report_models m ON"
							. "	m.model_id=f.model_id AND m.status=2"
							. " WHERE f.status=2";
		$modelSQL			= "SELECT model_id AS id, model_name AS value FROM report_models WHERE status=2";
		$filterFieldSQL		= "SELECT 0 AS id, 'Nincs mező szűrés' AS value ";
		$filterFieldSQL	   .= "UNION SELECT field_id AS id, CONCAT(m.model_name, ': ', f.field_name) AS value "
							. " FROM report_model_fields f LEFT JOIN report_models m ON"
							. "	m.model_id=f.model_id AND m.status=2"
							. " WHERE f.status=2 AND f.summed";
		$filterContentSQL	= "SELECT 0 AS id, 'Nincs mező szűrés' AS value ";
		$filterContentSQL  .= "UNION SELECT field_id AS id, CONCAT(m.model_name, ': ', f.field_name) AS value"
							. " FROM report_model_fields f LEFT JOIN report_models m ON"
							. "	m.model_id=f.model_id AND m.status=2"
							. " WHERE f.status=2 AND f.filtered_contents";
		$reportSQL			= "SELECT report_id AS id, CONCAT(m.model_name, ': ', r.report_name) AS value"
							. " FROM report_model_reports r LEFT JOIN report_models m ON"
							. "	m.model_id=r.model_id AND m.status=2"
							. " WHERE r.status=2";
		$fieldSQL			= "SELECT f.field_id AS id, CONCAT(m.model_name, ': ', f.field_name) AS value"
							. "	FROM report_model_fields f LEFT JOIN report_models m ON"
							. "	m.model_id=f.model_id AND m.status=2"
							. " WHERE f.status=2";
		if ($this->gridMode==1)
		{
			return array
			(
				'model_id'			=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ro', 'width'=>'80'),
				'model_name'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'250'),
				'model_description'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'800'),
			);
		}
		elseif ($this->gridMode==2)
		{
			$muveletSQL  = "SELECT '' AS id, 'Nincs művelet' AS value ";
			$muveletSQL .= "UNION SELECT '+' AS id, '+' AS value ";
			$muveletSQL .= "UNION SELECT '*' AS id, '*' AS value ";
			$cols=array
			(
				'field_id'					=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ro', 'width'=>'80'),
				'model_id'					=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$modelSQL)),
				'operand'					=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$muveletSQL)),
				'negation'					=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$yesNoSQL)),
				'reciprocal'				=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$yesNoSQL)),
				'constant'					=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$yesNoSQL)),
				'filter_id'					=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'300', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$filterSQL)),
				'field_ids'					=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'200'),
				'field_name'				=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300'),
				'field_description'			=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300'),
				'data_tags'					=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300'),
				'data_summed'				=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$yesNoSQL)),
			);
			$cols['column_name']			= array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300');
			$cols['filtered_contents']		= array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$yesNoSQL));
			$cols['filter_table']			= array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300');
			$cols['filter_table_id']		= array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300');
			$cols['filter_table_content']	= array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300');
			$cols['filter_lookup_id']		= array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300');
			return $cols;
		}
		elseif ($this->gridMode==3)
		{
			return array
			(
				'filter_id'					=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ro', 'width'=>'80'),
				'model_id'					=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$modelSQL)),
				'filter_not'				=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$yesNoSQL)),
				'field_id_field'			=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$filterFieldSQL)),
				'field_filter'				=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$muveletSQL)),
				'filter_ids'				=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300'),
				'relation'					=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$relationSQL)),
				'field_id_content'			=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$filterContentSQL)),
				'field_contents'			=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300'),
				'filter_name'				=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300'),
				'filter_description'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'500'),
				'filter_tags'				=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300'),
			);
		}
		elseif ($this->gridMode==4)
		{
			return array
			(
				'report_id'					=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ro', 'width'=>'80'),
				'model_id'					=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$modelSQL)),
				'report_name'				=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'250'),
				'report_description'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'800'),
			);
		}
		elseif ($this->gridMode==5)
		{
			return array(
				'report_field_id'			=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ro', 'width'=>'80'),
				'report_id'					=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'300', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$reportSQL)),
				'data_time'					=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$yesNoSQL)),
				'full_frame'				=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$yesNoSQL)),
				'frame_to_interval_from'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$yesNoSQL)),
				'frame_to_interval_to'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$yesNoSQL)),
				'data_exists'				=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$yesNoSQL)),
				'filter_id'					=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'300', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$filterSQL)),
				'field_id'					=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'300', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$fieldSQL)),
				'data_sequence'				=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'ed'),
				'data_summary'				=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$yesNoSQL)),
				'data_view'					=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'100', 'col_type'=>'combo', 'options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$yesNoSQL)),
				'data_code'					=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300'),
				'data_header'				=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300'),
			);
		}
		elseif ($this->gridMode==5)
		{
			return array(
				'user_id'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'100'),
				'user_name'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300'),
				'detail_where'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300'),
				'sum_where'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300'),
				'detail_per_due'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'80'),
				'code_order'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'100'),
				'type'			=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'80'),
				'type_order'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'50'),
				'time_function'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300'),
				'all_sum'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'100'),
			);
		}
		elseif ($this->gridMode==6) //user_event_table_log_to_show
		{
			$filterSQL="SELECT table_name AS id, concat(table_name, ' - ', key_field) AS value FROM user_event_log_tables order by table_name, key_field";
			return array(
				'table_name'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'300', 'col_type'=>'combo','options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$filterSQL)),
				//'table_name'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'200'),
				'to_show'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'1000'),
			);
		}
		elseif ($this->gridMode==7) //user_event_table_join
		{
			$filterSQL="SELECT table_name AS id, concat(table_name, ' - ', key_field) AS value FROM user_event_log_tables order by table_name, key_field";
			return array(
				'table_name'			=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'200', 'col_type'=>'combo','options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$filterSQL)),
				//'table_name'			=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'200'),
				'reference_field'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'200'),
				'reference_field_to_link'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'200'),
				'link_table_name'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'200', 'col_type'=>'combo','options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$filterSQL)),
				//'link_table_name'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'200'),
				'link_field'			=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'200'),
				'status_expected'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'80'),
				'is_valid_from'			=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'80'),
				'is_valid'			=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'80'),
				'dict_module'			=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'80'),
				'lookup_field'			=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'200'),
			);
		}
		elseif ($this->gridMode==8) //user_event_log_tables
		{
			return array(
				'table_name'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300'),
				'key_field'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300'),
			);
		}
		elseif ($this->gridMode==9) //user_event
		{
			$filterSQL="SELECT table_name AS id, concat(table_name, ' - ', key_field) AS value FROM user_event_log_tables order by table_name, key_field";
			return array(
				'event_code'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'80'),
				'page_title'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'100'),
				'table_name'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'width'=>'300', 'col_type'=>'combo','options'=>array('mode'=> Grid2Controller::G2BC_QUERY_MODE_SQL,'sql'=>$filterSQL)),
//				'table_name'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300'),
				'new_row_id'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'80'),
				'to_show'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'500'),
				'created_by'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300'),
				'created_on'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'140'),
			);
		}
		elseif ($this->gridMode==10) //user_event_type
		{
			return array(
				'event_code'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'400'),
				'title'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'400'),
				'status'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'400'),
				'created_by'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'300'),
				'created_on'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'140'),
			);
		}
		elseif ($this->gridMode==11) //ts_fields
		{
			return array(
				'oszlop'			=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'250'),
				'megnevezes'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'250'),
				'leiras'			=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'800'),
				'cimkek'			=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'250'),
			);
		}
		elseif ($this->gridMode==1 || $this->gridMode==12) //report ts_fields
		{
			return array(
				'model_name'		=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'250'),
				'model_description'	=> array('grid'=>true, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'width'=>'800'),
			);
		}
	}
}
