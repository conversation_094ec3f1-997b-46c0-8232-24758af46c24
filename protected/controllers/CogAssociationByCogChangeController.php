<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use app\components\Grid2\Grid2Controller;
	use app\models\BulkGroupChangeByCog;
	use app\models\Status;
	use Yang;

`/yii2-only';


/**
 * Összerendelések csoport2 váltáshoz.
 * Ha egy dolgozót más csoport 2-be helyeznek át a szoftverben,
 * az adott csoport 2-vel összekapcsolt valamennyi adat automatikusan módosul az érintett munkavállaló esetében.
 * Ezen a felületen lehet hozzárendelni a különböző mezőket (munkacsoportot, egységet, költséghelyet
 * költségviselőt, telephelyet, részleget, területet és csoportot) az adott csoport2-höz.
 */
class CogAssociationByCogChangeController extends Grid2Controller
{
	/**
	 * Grid2s szül<PERSON><PERSON>zt<PERSON><PERSON> konstruktor meghív<PERSON>a
	 */
	public function __construct()
	{
        parent::__construct("cogAssociationByCogChange");
    }

	/**
	 * Grid2 inícializálása, használt mód: SQL
	 */
	protected function G2BInit()
    {
		$this->LAGridDB->setModelName("BulkGroupChangeByCog");
		parent::setControllerPageTitleId("page_title_cog_association_by_cog_change");

		$this->LAGridRights->overrideInitRights("paging",				true);
		$this->LAGridRights->overrideInitRights("search_header",		true);
		$this->LAGridRights->overrideInitRights("select",				true);
		$this->LAGridRights->overrideInitRights("col_sorting",			true);
		$this->LAGridRights->overrideInitRights("reload",				true);
		$this->LAGridRights->overrideInitRights("reload_sortings",		true);
		$this->LAGridRights->overrideInitRights("modify",				true);

		$this->LAGridDB->enableSQLMode("dhtmlxGrid");
		$this->LAGridDB->setSQLSelection($this->setSQL("", "", false), "cog_id", "dhtmlxGrid");
		parent::G2BInit();
	}

	/**
	 * sql kérés definiálása
	 *
	 * @return string sql kérés
	 */
	protected function setSQL($filter, $gridID, $forReport = false)
	{
		$SQL = "
            SELECT 
                bgcbc.cog_id AS cog_id,
                
                MAX(CASE WHEN bgcbc.to_column = 'workgroup_id' 
                         THEN bgcbc.to_value END) AS workgroup_id, 
                MAX(CASE WHEN bgcbc.to_column = 'unit_id' 
                         THEN bgcbc.to_value END) AS unit_id,
                MAX(CASE WHEN bgcbc.to_column = 'cost_id' 
                         THEN bgcbc.to_value END) AS cost_id,
                MAX(CASE WHEN bgcbc.to_column = 'cost_center_id' 
                         THEN bgcbc.to_value END) AS cost_center_id,
                MAX(CASE WHEN bgcbc.to_column = 'option7' 
                         THEN bgcbc.to_value END) AS option7,
                MAX(CASE WHEN bgcbc.to_column = 'option1' 
                         THEN bgcbc.to_value END) AS option1,
                MAX(CASE WHEN bgcbc.to_column = 'option2' 
                         THEN bgcbc.to_value END) AS option2,
                MAX(CASE WHEN bgcbc.to_column = 'option3' 
                         THEN bgcbc.to_value END) AS option3
            FROM bulk_group_change_by_cog bgcbc
            LEFT JOIN company_org_group2 cog2 ON
                cog2.company_org_group_id = bgcbc.cog_id
            WHERE 
                bgcbc.status = ".Status::PUBLISHED."
            GROUP BY cog_id
            ORDER BY company_org_group_name
		";

		return $SQL;
	}

    /**
     * Visszaadja a felületen kiválasztott sor adatait.
     */
    public function getSelectedRow()
    {
        $cogId = requestParam('editPK');
        $model = new BulkGroupChangeByCog();
        $selectedCog = $model::model()->findAllByAttributes([
            'cog_id' => $cogId,
            'status' => Status::PUBLISHED
        ]);

        $row = [];
        if (isset($selectedCog) && is_array($selectedCog)) {
            for ($i = 0; $i < count($selectedCog); $i++) {
                $key = $selectedCog[$i]["to_column"];
                $value = $selectedCog[$i]["to_value"];
                $row[$key] = $value;
            }
        }
        $row = array_merge($row, ["cog_id"=>$cogId]);

        return $row;
    }

	/**
	 * adatok megjelenítése az egyes oszlopokban
	 */
	public function columns()
	{
        $selectedRow = $this->getSelectedRow();
        $defaultEnd = App::getSetting("defaultEnd");
        
        $where = "
            WHERE
                    status = ".Status::PUBLISHED."
                AND (CURDATE() BETWEEN valid_from AND IFNULL(valid_to, ".$defaultEnd."))
        ";

        $cogSql = "
            SELECT 
                company_org_group_id AS id, 
                company_org_group_name AS value
            FROM 
                company_org_group2
            " . $where . "
            ORDER BY company_org_group_name;
        ";

        $workgroupSql = "
            SELECT 
                workgroup_id AS id, 
                workgroup_name AS value
            FROM 
                workgroup
            " . $where . "
            ORDER BY workgroup_name;
        ";

        $unitSql = "
            SELECT 
                unit_id AS id, 
                unit_name AS value
            FROM 
                unit
            " . $where . "
            ORDER BY unit_name;
        ";

        $costSql = "
            SELECT 
                cost_id AS id, 
                cost_name AS value
            FROM 
                cost
            " . $where . "
            ORDER BY cost_name;
        ";

        $costCenterSql = "
            SELECT 
                cost_center_id AS id, 
                cost_center_name AS value
            FROM 
                cost_center
            " . $where . "
            ORDER BY cost_center_name;
        ";

        $defaultValueOfCog = isset($selectedRow["cog_id"]) ? $selectedRow["cog_id"] : '';
        $defaultValueOfWorkgroup = isset($selectedRow["workgroup_id"]) ? $selectedRow["workgroup_id"] : '';
        $defaultValueOfUnit = isset($selectedRow["unit_id"]) ? $selectedRow["unit_id"] : '';
        $defaultValueOfCost = isset($selectedRow["cost_id"]) ? $selectedRow["cost_id"] : '';
        $defaultValueOfCostCenter = isset($selectedRow["cost_center_id"]) ? $selectedRow["cost_center_id"] : '';
        $defaultValueOfOption7 = isset($selectedRow["option7"]) ? $selectedRow["option7"] : '';
        $defaultValueOfOption1 = isset($selectedRow["option1"]) ? $selectedRow["option1"] : '';
        $defaultValueOfOption2 = isset($selectedRow["option2"]) ? $selectedRow["option2"] : '';
        $defaultValueOfOption3 = isset($selectedRow["option3"]) ? $selectedRow["option3"] : '';

		return [
			'cog_id'       => [
                                    'col_type'=>'combo', 
                                    'options' => [
                                        'mode'		=> Grid2Controller::G2BC_QUERY_MODE_SQL,
                                        'sql'		=> $cogSql,
                                        'array'	    => ["id"=>"","value"=>""]], 
                                    'default_value' => $defaultValueOfCog,
                                    'export'		=> false,
									'readonly'		=> true,
									'disabled'		=> false
                                ],
			'workgroup_id'   => [
                                    'col_type'=>'combo', 
                                    'options' => [
                                        'mode'		=> Grid2Controller::G2BC_QUERY_MODE_SQL,
                                        'sql'		=> $workgroupSql,
                                        'array'	    => [["id"=>"ALL","value"=>Dict::getValue("all")]]], 
                                    'default_value' => $defaultValueOfWorkgroup,
                                    'export' => false,
                                    'edit' => true,
                                ],
			'unit_id'        => [
                                    'col_type'=>'combo', 
                                    'options' => [
                                        'mode'		=> Grid2Controller::G2BC_QUERY_MODE_SQL,
                                        'sql'		=> $unitSql,
                                        'array'	    => ["id"=>"","value"=>""]],
                                    'default_value' => $defaultValueOfUnit, 
                                    'export' => false,
                                    'edit' => true,
                                ],
			'cost_id'        => [
                                    'col_type'=>'combo', 
                                    'options' => [
                                        'mode'		=> Grid2Controller::G2BC_QUERY_MODE_SQL,
                                        'sql'		=> $costSql,
                                        'array'	    => ["id"=>"","value"=>""]],
                                    'default_value' => $defaultValueOfCost, 
                                    'export' => false,
                                    'edit' => true,
                                ],
			'cost_center_id' => [
                                    'col_type'=>'combo', 
                                    'options' => [
                                        'mode'		=> Grid2Controller::G2BC_QUERY_MODE_SQL,
                                        'sql'		=> $costCenterSql,
                                        'array'	    => ["id"=>"","value"=>""]],
                                    'default_value' => $defaultValueOfCostCenter, 
                                    'export' => false,
                                    'edit' => true,
                                ],
			'option7'        => ['col_type'=>'ed', 'export' => false, 'edit' => true, 'default_value' => $defaultValueOfOption7],
			'option1'        => ['col_type'=>'ed', 'export' => false, 'edit' => true, 'default_value' => $defaultValueOfOption1],
			'option2'        => ['col_type'=>'ed', 'export' => false, 'edit' => true, 'default_value' => $defaultValueOfOption2],
			'option3'        => ['col_type'=>'ed', 'export' => false, 'edit' => true, 'default_value' => $defaultValueOfOption3],
		];
	}

	/**
	 * Oszlop elnevezések definiálása
	 */
	public function attributeLabels()
    {
        return [
            'cog_id'         => Dict::getValue("company_org_group2"),
            'workgroup_id'   => Dict::getValue("workgroup"),
            'unit_id'        => Dict::getValue("unit_id"),
            'cost_id'        => Dict::getValue("cost"),
            'cost_center_id' => Dict::getModuleValue("ttwa-base", "cost_center"),
            'option7'        => Dict::getValue("option7"),
            'option1'        => Dict::getValue("option1"),
            'option2'        => Dict::getValue("option2"),
            'option3'        => Dict::getValue("option3"),
		];
    }

    /**
     * Új sor mentése vagy már meglévő sor szerkesztésének mentése
     */
    public function actionSave($data = [], $modelName = null, $pk = null, $vOnly = false, $ret = false, $contentId = null) {
		$this->layout = "//layouts/ajax";
		$this->G2BInit();

		$gridID = requestParam('gridID');
		$generateFrom = requestParam('generateFrom');

        if (!empty($generateFrom)) {
            $modelName = $this->LAGridDB->getModelName($generateFrom);
            $pk = $this->LAGridDB->getPrimaryKey($generateFrom);
        } else {
            $modelName = $this->LAGridDB->getModelName($gridID);
            $pk = $this->LAGridDB->getPrimaryKey($gridID);
        }

		if (!count($data)) {
			$data = requestParam('dialogInput_'.$generateFrom);
			$defaults = requestParam('defaults');

			if ((!isset($data[$pk]) || empty($data[$pk])) && is_array($defaults) && count($defaults)) {
				$data = Yang::arrayMerge($defaults, $data);
			}
		}

        $cog2InDict = Dict::getValue("company_org_group2");
        $response = [
            'status'	=> [],
            'error'		=> [],
        ];
        
		
        $model = new $modelName();
        //save new company org group 2
        if (isset($data["cog_id"]) && $data["cog_id"] && empty($data["row_id"])) {
			$cogInDb = $model::model()->findByAttributes([
				'cog_id' => $data["cog_id"],
				'status' => Status::PUBLISHED,
            ]);

            //save new but company org group 2 is already in use
			if ($cogInDb && empty($data["row_id"])) {
				$response['status'][] = 0;
                $response['error'][] = Dict::getValue('error_value_already_in_use', ['attribute' => $cog2InDict]);
            } elseif (!$cogInDb && empty($data["row_id"])) {
                $areAllComboFieldsSet = true;
                $this->checkIfRequiredFieldsAreSet($data, $areAllComboFieldsSet, $response);

                if ($areAllComboFieldsSet) {
                    $cogId = $data["cog_id"];
                    $data["company_org_group2_id"] = $cogId;
                    unset($data["row_id"]);
                    unset($data["cog_id"]);
                    
                    foreach ($data as $column => $value)
                    {
                        $this->saveNewAssociationToCog($modelName, $cogId, $column, $value, $response);
                    }
                }
            }
            //save new but company org group 2 is not selected
        } elseif (isset($data["cog_id"]) && empty($data["cog_id"])) {
            $response['status'][] = 0;
            $response['error'][] = Dict::getValue('error_field_required', ['attribute' => $cog2InDict]);
        
            //modify
		} elseif (isset($data["row_id"]) && !empty($data["row_id"])) {
            $areAllComboFieldsSet = true;
            $this->checkIfRequiredFieldsAreSet($data, $areAllComboFieldsSet, $response);

            if ($areAllComboFieldsSet) {
                $modelCogInDb = $model::model()->findAllByAttributes([
                    'cog_id' => $data["row_id"],
                    'status' => Status::PUBLISHED,
                ]);
                $cog = [];
    
                for ($i = 0; $i < count($modelCogInDb); $i++) {
                    $rowId = $modelCogInDb[$i]->row_id;
                    $column = $modelCogInDb[$i]->to_column;
                    $value = $modelCogInDb[$i]->to_value;
                    $cog[$column] = [
                        "row_id" => $rowId, 
                        "to_value" => $value
                    ];
                }
                
                $cogId = $data["row_id"];
                $data["company_org_group2_id"] = $cogId;
                unset($data["cog_id"]);
                unset($data["row_id"]);
    
                foreach ($data as $column => $value)
                {
                    if (isset($cog[$column]) && ($cog[$column]["to_value"] !== $value)) {
                        //update
                        $model = $model::model()->findByPk($cog[$column]["row_id"]);
                        $model->to_value = $value;

                        try {
                            $model->update();
                            $response['status'][] = 1;
                            $response['error'][] = $model->getErrors();
                        } catch (Exception $ex) {
                            $response['status'][] = 0;
                            $response['error'][] = $ex->getMessage();
                        }
                    } elseif (!isset($cog[$column])) {
                        //save
                        $this->saveNewAssociationToCog($modelName, $cogId, $column, $value, $response);
                    } 
                }
            }
        }

        $errorMessage = "";

        foreach ($response["error"] as $error) {
                $errorMessage .= $error . "<br/>";
        }

        $status = [
            'status'	=> in_array(0, $response["status"]) ? 0 : 1,
            'error'		=> $errorMessage,
        ];

        echo json_encode($status);
    }
    
    /**
     * új sorok lementése az adatbázisba
     */
    public function saveNewAssociationToCog($modelName, $cogId, $column, $value, &$response)
    {
        $model = new $modelName();
        $model->cog_id = $cogId;
        $model->to_column = $column;
        $model->to_value = $value;
        $model->status = Status::PUBLISHED;
        $model->created_by = userID();
        $model->created_on = date("Y-m-d H:i:s");

        try {
            (int) $model->save();
            $response['status'][] = 1;
            $response['error'][] = $model->getErrors();
        } catch (Exception $ex) {
            $response['status'][] = 0;
            $response['error'][] = $ex->getMessage();
        }
    }

    /**
     * vizsgálja, hogy a kötelező mezők ki vannak-e töltve
     */
    public function checkIfRequiredFieldsAreSet($data, &$areAllComboFieldsSet, &$response)
    {
        $requiredFields = [
            "workgroup_id"   => Dict::getValue("workgroup"),
            "unit_id"        => Dict::getValue("unit_id"),
            "cost_id"        => Dict::getValue("cost"),
            "cost_center_id" => Dict::getModuleValue("ttwa-base", "cost_center")
        ];

        foreach ($requiredFields as $field => $value) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $areAllComboFieldsSet = false;
                $response['status'][] = 0;
                $response['error'][] = Dict::getValue('error_field_required', ['attribute' => $value]);
            }
        }
    }

    /**
     * törli a kiválasztott csoport2-höz tartozó összerendeléseket
     */
    public function actionDelete($modelName = null, $hasRight = false) {
		$this->layout = "//layouts/ajax";
		$this->G2BInit();

		$gridID = requestParam('gridID');
		$gridID = empty($gridID) ? "dhtmlxGrid" : $gridID;
        $hasRight = $hasRight||$this->hasRight("delete", $gridID);
        $modelName = $this->LAGridDB->getModelName($gridID);
        $cogIdOfSelectedRow = requestParam('ids');
        
        $response = [
            'status'	=> [],
            'error'		=> [],
        ];
	
		if ($hasRight && isset($cogIdOfSelectedRow) && !empty($cogIdOfSelectedRow) && isset($modelName) && !empty($modelName)) {
            $model = new $modelName();
            $modelCogInDb = $model::model()->findAllByAttributes([
                'cog_id' => $cogIdOfSelectedRow,
                'status' => Status::PUBLISHED,
            ]);

            if (count($modelCogInDb)) {
                $columnsAssociatedToCog = [];

                for ($i = 0; $i < count($modelCogInDb); $i++) {
                    $columnsAssociatedToCog[] = $modelCogInDb[$i]->to_column;
                }

                foreach ($columnsAssociatedToCog as $column) {
                    $modelForOneRow = $model::model()->findByAttributes([
                        'cog_id' => $cogIdOfSelectedRow,
                        'to_column' => $column,
                        'status' => Status::PUBLISHED,
                    ]);

                    if ($modelForOneRow) {
                        $modelForOneRow->status = Status::DELETED;
    
                        try {
                            $modelForOneRow->update();
                            $response['status'][] = 1;
                        } catch (Exception $ex) {
                            $response['status'][] = 0;
                            $response['error'][] = $ex->getMessage();
                        }
                    } else {
                        $response['status'][] = 0;
                        $response['error'][] = Dict::getValue("failed");
                    }
                }
            } else {
                $response['status'][] = 0;
                $response['error'][] = Dict::getValue("failed");
			}
		} else {
            $response['status'][] = 0;
            $response['error'][] = Dict::getValue("failed");
        }
        
        $errorMessage = "";

        foreach ($response["error"] as $error) {
                $errorMessage .= $error . "<br/>";
        }

        $status = [
            'status'	=> in_array(0, $response["status"]) ? 0 : 1,
            'message'		=> $errorMessage,
        ];

		echo json_encode($status);
	}
}