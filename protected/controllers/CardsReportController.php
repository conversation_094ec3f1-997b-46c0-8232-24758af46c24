<?php

class CardsReportController extends Grid2Controller
{
	private $columnProperties = [ 'grid' => true, 'export' => true, 'col_type' => 'ro', 'col_align' => 'left', 'width' => '200' ];
	private $statusPublished = Status::STATUS_PUBLISHED;

	public function __construct()
	{
		parent::__construct('cardsReport');
		$this->maxDays = 999;
	}

	protected function G2BInit()
	{
		parent::setControllerPageTitleId('page_title_cards_report');

		$this->LAGridRights->overrideInitRights('paging', true);
		$this->LAGridRights->overrideInitRights('search', true);
		$this->LAGridRights->overrideInitRights('search_header', true);
		$this->LAGridRights->overrideInitRights('select', true);
		$this->LAGridRights->overrideInitRights('multi_select', false);
		$this->LAGridRights->overrideInitRights('column_move', true);
		$this->LAGridRights->overrideInitRights('col_sorting', true);
		$this->LAGridRights->overrideInitRights('reload_sortings', true);
		$this->LAGridRights->overrideInitRights('details', false);
		$this->LAGridRights->overrideInitRights('init_open_search', true);

		$this->LAGridRights->overrideInitRights('export_xlsx', true);
		parent::setExportFileName(Dict::getValue("page_title_cards_report"));
		$this->LAGridDB->enableArrMode();

		parent::G2BInit();
	}

	protected function getStatusButtons($gridID = NULL)
	{
		return parent::getStatusButtons();
	}

	public function search()
	{
		return $this->getPreDefinedSearchFromDb();
	}

	public function columns()
	{
		return [
			'emp_id' => $this->columnProperties,
			'employee_name' => $this->columnProperties,
			'employee_contract_number' => $this->columnProperties,
			'card_num' => $this->columnProperties,
			'valid_from' => $this->columnProperties,
			'valid_to' => $this->columnProperties,
			'card_type' => $this->columnProperties,
			'note' => $this->columnProperties
		];
	}

	public function attributeLabels()
	{
		return [
			'emp_id' => Dict::getValue('emp_id'),
			'employee_name' => Dict::getValue('employee_name'),
			'employee_contract_number' => Dict::getValue('employee_contract_number'),
			'card_num' => Dict::getValue('cardnum'),
			'valid_from' => Dict::getValue('valid_from'),
			'valid_to' => Dict::getValue('valid_to'),
			'card_type' => Dict::getValue('card_type'),
			'note' => Dict::getValue('note')
		];
	}

	public function dataArray($gridID, $filter)
	{
		$cards = $this->getCards($filter);

		return $this->makeCards($cards);
	}

	private function getCards($filter)
	{
		$statusPublished = $this->statusPublished;
		$validFrom = $filter['valid_date'];
		$defaultEnd = App::getSetting('defaultEnd');
		$fullName = Employee::getParam('fullname', 'employee');
		$sqlFilter = $this->getSqlFilter($filter);
		$art = new ApproverRelatedGroup;
		$gargSQL = $art->getApproverReleatedGroupSQLWithoutCalc('Employee', 'workForce', false, "AND", "CurrentDate", $this->getControllerID());

		$sql = "SELECT
			`employee_card`.`card`,
			`employee`.`emp_id`,
			$fullName AS full_name,
			`employee_contract`.`employee_contract_id`,
			`employee_card`.`note`,
			`employee_card`.`valid_from`,
			`employee_card`.`valid_to`,
			`employee_card`.`card_type`

			FROM `employee_card` ";

		$sql .= "
			LEFT JOIN `employee_contract` ON `employee_contract`.`employee_contract_id` = `employee_card`.`employee_contract_id`
				AND `employee_contract`.`status` = $statusPublished
				AND
				(
					'$validFrom' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '$defaultEnd')
					AND
					'$validFrom' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '$defaultEnd')
				) ";

		$sql .= "
			LEFT JOIN `employee` ON `employee`.`employee_id` = `employee_contract`.`employee_id`
				AND `employee`.`status` = $statusPublished
				AND
				(
					'$validFrom' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '$defaultEnd')
				) ";

		if(EmployeeGroupConfig::isActiveGroup('workgroup_id')) {
			$sql.= EmployeeGroup::getLeftJoinSQLWithoutCal("workgroup_id", "employee_contract", "", "", "employee", $validFrom, $defaultEnd);
		}

		if (EmployeeGroupConfig::isActiveGroup('unit_id')) {
			$sql .= EmployeeGroup::getLeftJoinSQLWithoutCal('unit_id', "employee_contract", "", "", "employee", $validFrom, $defaultEnd);
		}

		$sql.=	"
			LEFT JOIN `workgroup` ON workgroup.`workgroup_id`= ".EmployeeGroup::getActiveGroupSQL("workgroup_id","employee_contract")."
				AND workgroup.`status` = ".$statusPublished."
				AND '$validFrom' BETWEEN `workgroup`.`valid_from` AND IFNULL(`workgroup`.`valid_to`, '$defaultEnd')
			";

		$sql .= "
			LEFT JOIN `unit` ON unit.`status`=". $statusPublished ."
				AND unit.`unit_id`=" . EmployeeGroup::getActiveGroupSQL("unit_id","employee") ."
				AND '$validFrom' BETWEEN unit.`valid_from` AND IFNULL(unit.`valid_to`, '" .$defaultEnd . "')
			";

		if (isset($gargSQL['join'])) {
			$sql .= $gargSQL['join'];
		}

		$sql .= "
			WHERE {$sqlFilter} AND `employee_card`.`status` = $statusPublished
				AND '$validFrom' BETWEEN `employee_card`.`valid_from` AND IFNULL(employee_card.`valid_to`, '" .$defaultEnd . "')
			";

		if (isset($gargSQL['where'])) {
			$sql .= $gargSQL['where'];
		}

		$sql .= " ORDER BY `employee`.`last_name`, `employee`.`first_name`, `employee_card`.`valid_from`, `employee_card`.`valid_to`";

		return dbFetchAll($sql);
	}

	protected function getSqlFilter($filter)
	{
		$gpf = new GetPreDefinedFilter($this->getControllerID(), \FALSE, ['company' => 'employee', 'payroll' => 'employee' ]);
		$SqlFilter = $gpf->getFilter();
		$SqlFilter = App::replaceSQLFilter($SqlFilter, $filter);

		return $SqlFilter;
	}

	private function makeCards($cards)
	{
		$cardsMade = [];

		if (sizeof($cards) !== 0) {
			foreach ($cards as $card) {
				if ($card['employee_contract_id'] !== null) {
					array_push($cardsMade, [
						'emp_id' => $card['emp_id'],
						'employee_name' => $card['full_name'],
						'employee_contract_number' => $card['employee_contract_id'],
						'card_num' => $card['card'],
						'note' => $card['note'],
						'valid_from' => $card['valid_from'],
						'valid_to' => $card['valid_to'],
						'card_type' => $this->getCardType($card['card_type'])
					]);
				}
			}
		} else {
			array_push($cardsMade, [
				'emp_id' => '',
				'employee_name' => '',
				'employee_contract_number' => '',
				'card_num' => '',
				'note' => '',
				'valid_from' => '',
				'valid_to' => '',
				'card_type' => ''
			]);
		}

		return $cardsMade;
	}

	private function getCardType($cardType)
	{
		$cardType = ($cardType === null) ? null : intval($cardType);

		if ($cardType === EmployeeCard::CARDTYPE_NORMAL) {
			return Dict::getValue('normal_card');
		}

		if ($cardType === EmployeeCard::CARDTYPE_TEMPORARY) {
			return Dict::getValue('temporary_card');
		}

		if ($cardType === EmployeeCard::CARDTYPE_VISITOR) {
			return Dict::getValue('visitor_card');
		}

		return null;
	}
}
