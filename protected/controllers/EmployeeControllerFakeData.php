<?php #yii2: done

'yii2-only`;

	namespace app\controllers;
	use app\components\Dict;
	use Yang;

`/yii2-only';


trait EmployeeControllerFakeData
{
	private function generateFakeData($data) {
		foreach ($data["data"] as $id => $line) {
			/*
				NULL AS risk_of_term,
				NULL AS time_of_term,
				NULL AS cause_of_term,
				NULL AS ssc_index,
				NULL AS mood_index,
				NULL AS role_in_tem_comm,
				NULL AS absenteeism,
				NULL AS time_prop_abs,
				NULL AS delays,
				NULL AS overtime,
				NULL AS cond_eval,
				NULL AS subs_eval,
				NULL AS cowrs_eval
			*/

			$line["columns"]["risk_of_term"]["data"] = "";
			$line["columns"]["risk_of_term"]["cssClass"] = ""; // yellow
			
			switch($line["columns"]["emp_id"]["data"]) {
				case "12336666":
				case "9999":
				case "458888":
					$line["columns"]["risk_of_term"]["data"] = Dict::getValue("high");
					$line["columns"]["risk_of_term"]["cssClass"] = "red";
					break;
				case "DEMO-004":
				case "123222":
					$line["columns"]["risk_of_term"]["data"] = Dict::getValue("moderate");
					$line["columns"]["risk_of_term"]["cssClass"] = "yellow";
					break;
				case "1222222":
					$line["columns"]["risk_of_term"]["data"] = Dict::getValue("negligible");
					$line["columns"]["risk_of_term"]["cssClass"] = "green";
					break;
				default:
					$line["columns"]["risk_of_term"]["data"] = Dict::getValue("low");
					$line["columns"]["risk_of_term"]["cssClass"] = "green";
					break;
			}
			
			switch($line["columns"]["emp_id"]["data"]) {
				case "12336666":
				case "9999":
					$line["columns"]["role_in_tem_comm"]["data"] = Dict::getValue("isolated");
					$line["columns"]["role_in_tem_comm"]["cssClass"] = "red";
					break;
				case "DEMO-004":
					$line["columns"]["role_in_tem_comm"]["data"] = Dict::getValue("below_average");
					$line["columns"]["role_in_tem_comm"]["cssClass"] = "red";
					break;
				case "55555":
				case "1222222":
					$line["columns"]["role_in_tem_comm"]["data"] = Dict::getValue("opinion_former");
					$line["columns"]["role_in_tem_comm"]["cssClass"] = "green";
					break;
				default:
					$line["columns"]["role_in_tem_comm"]["data"] = "";
					$line["columns"]["role_in_tem_comm"]["cssClass"] = "";
					break;
			}

			if ($line["columns"]["emp_id"]["data"] === "12336666") {
				$line["columns"]["time_of_term"]["data"] = "2 ".Dict::getValue("month");
				$line["columns"]["cause_of_term"]["data"] = Dict::getValue("isolation");

				$line["columns"]["ssc_index"]["data"] = "0";
				$line["columns"]["mood_index"]["data"] = "-2";
				$line["columns"]["absenteeism"]["data"] = "15";
				$line["columns"]["time_prop_abs"]["data"] = "-10";
				$line["columns"]["delays"]["data"] = "-2";
				$line["columns"]["overtime"]["data"] = "15";

				$line["columns"]["cond_eval"]["cssClass"] = "face1";
				$line["columns"]["subs_eval"]["cssClass"] = "face3";
				$line["columns"]["cowrs_eval"]["cssClass"] = "face3";
			} else if ($line["columns"]["emp_id"]["data"] === "658666") {
				$line["columns"]["ssc_index"]["data"] = "1";
				$line["columns"]["mood_index"]["data"] = "1";
				$line["columns"]["absenteeism"]["data"] = "4";
				$line["columns"]["time_prop_abs"]["data"] = "0";
				$line["columns"]["delays"]["data"] = "1";
				$line["columns"]["overtime"]["data"] = "22";

				$line["columns"]["cond_eval"]["cssClass"] = "face5";
				$line["columns"]["subs_eval"]["cssClass"] = "face5";
				$line["columns"]["cowrs_eval"]["cssClass"] = "face5";
			} else if ($line["columns"]["emp_id"]["data"] === "DEMO-004") {
				$line["columns"]["time_of_term"]["data"] = "6 ".Dict::getValue("month");
				$line["columns"]["cause_of_term"]["data"] = Dict::getValue("salary");

				$line["columns"]["ssc_index"]["data"] = "-2";
				$line["columns"]["mood_index"]["data"] = "-1";
				$line["columns"]["absenteeism"]["data"] = "5";
				$line["columns"]["time_prop_abs"]["data"] = "0";
				$line["columns"]["delays"]["data"] = "-1";
				$line["columns"]["overtime"]["data"] = "33";

				$line["columns"]["cond_eval"]["cssClass"] = "face3";
				$line["columns"]["subs_eval"]["cssClass"] = "face1";
				$line["columns"]["cowrs_eval"]["cssClass"] = "face3";
			} else if ($line["columns"]["emp_id"]["data"] === "1222222") {
				$line["columns"]["ssc_index"]["data"] = "2";
				$line["columns"]["mood_index"]["data"] = "2";
				$line["columns"]["absenteeism"]["data"] = "2";
				$line["columns"]["time_prop_abs"]["data"] = "0";
				$line["columns"]["delays"]["data"] = "2";
				$line["columns"]["overtime"]["data"] = "0";

				$line["columns"]["cond_eval"]["cssClass"] = "face5";
				$line["columns"]["subs_eval"]["cssClass"] = "face5";
				$line["columns"]["cowrs_eval"]["cssClass"] = "face5";
			} else if ($line["columns"]["emp_id"]["data"] === "123222") { // Nagy Aladin
				$line["columns"]["time_of_term"]["data"] = "6 ".Dict::getValue("month");
				$line["columns"]["cause_of_term"]["data"] = Dict::getValue("salary");

				$line["columns"]["ssc_index"]["data"] = "-2";
				$line["columns"]["mood_index"]["data"] = "-1";
				$line["columns"]["absenteeism"]["data"] = "0";
				$line["columns"]["time_prop_abs"]["data"] = "5";
				$line["columns"]["delays"]["data"] = "-1";
				$line["columns"]["overtime"]["data"] = "20";

				$line["columns"]["cond_eval"]["cssClass"] = "face3";
				$line["columns"]["subs_eval"]["cssClass"] = "face3";
				$line["columns"]["cowrs_eval"]["cssClass"] = "face5";
			} else if ($line["columns"]["emp_id"]["data"] === "DEMO-003") {
				$line["columns"]["ssc_index"]["data"] = "1";
				$line["columns"]["mood_index"]["data"] = "0";
				$line["columns"]["absenteeism"]["data"] = "0";
				$line["columns"]["time_prop_abs"]["data"] = "7";
				$line["columns"]["delays"]["data"] = "0";
				$line["columns"]["overtime"]["data"] = "20";

				$line["columns"]["cond_eval"]["cssClass"] = "face5";
				$line["columns"]["subs_eval"]["cssClass"] = "face5";
				$line["columns"]["cowrs_eval"]["cssClass"] = "face5";
			} else if ($line["columns"]["emp_id"]["data"] === "55555") {
				$line["columns"]["ssc_index"]["data"] = "1";
				$line["columns"]["mood_index"]["data"] = "0";
				$line["columns"]["absenteeism"]["data"] = "3";
				$line["columns"]["time_prop_abs"]["data"] = "-7";
				$line["columns"]["delays"]["data"] = "0";
				$line["columns"]["overtime"]["data"] = "10";

				$line["columns"]["cond_eval"]["cssClass"] = "face5";
				$line["columns"]["subs_eval"]["cssClass"] = "face5";
				$line["columns"]["cowrs_eval"]["cssClass"] = "face5";
			} else if ($line["columns"]["emp_id"]["data"] === "DEMO-001") {
				$line["columns"]["ssc_index"]["data"] = "0";
				$line["columns"]["mood_index"]["data"] = "1";
				$line["columns"]["absenteeism"]["data"] = "5";
				$line["columns"]["time_prop_abs"]["data"] = "-2";
				$line["columns"]["delays"]["data"] = "1";
				$line["columns"]["overtime"]["data"] = "35";

				$line["columns"]["cond_eval"]["cssClass"] = "face5";
				$line["columns"]["subs_eval"]["cssClass"] = "face5";
				$line["columns"]["cowrs_eval"]["cssClass"] = "face5";
			} else if ($line["columns"]["emp_id"]["data"] === "9999") {
				$line["columns"]["time_of_term"]["data"] = "3 ".Dict::getValue("month");
				$line["columns"]["cause_of_term"]["data"] = Dict::getValue("mood");

				$line["columns"]["ssc_index"]["data"] = "-1";
				$line["columns"]["mood_index"]["data"] = "-1";
				$line["columns"]["absenteeism"]["data"] = "12";
				$line["columns"]["time_prop_abs"]["data"] = "1";
				$line["columns"]["delays"]["data"] = "-1";
				$line["columns"]["overtime"]["data"] = "0";

				$line["columns"]["cond_eval"]["cssClass"] = "face1";
				$line["columns"]["subs_eval"]["cssClass"] = "face1";
				$line["columns"]["cowrs_eval"]["cssClass"] = "face1";
			} else if ($line["columns"]["emp_id"]["data"] === "458888") {
				$line["columns"]["time_of_term"]["data"] = "2 ".Dict::getValue("month");
				$line["columns"]["cause_of_term"]["data"] = Dict::getValue("isolation");

				$line["columns"]["ssc_index"]["data"] = "0";
				$line["columns"]["mood_index"]["data"] = "0";
				$line["columns"]["absenteeism"]["data"] = "10";
				$line["columns"]["time_prop_abs"]["data"] = "1";
				$line["columns"]["delays"]["data"] = "0";
				$line["columns"]["overtime"]["data"] = "0";

				$line["columns"]["cond_eval"]["cssClass"] = "face3";
				$line["columns"]["subs_eval"]["cssClass"] = "face1";
				$line["columns"]["cowrs_eval"]["cssClass"] = "face1";
			}

			$data["data"][$id] = $line;
		}

		return $data;
	}
}