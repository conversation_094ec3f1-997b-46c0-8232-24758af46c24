<?php

class QneReportController extends Grid2Controller {
    private $numberOfQuestions = 13;
    
    public function __construct() {
	parent::__construct("qneReport");
    }
        
    protected function G2BInit() {
        
        parent::setControllerPageTitleId("page_title_qne_report");

        $this->LAGridRights->overrideInitRights("paging",true);
        $this->LAGridRights->overrideInitRights("search",true);
        $this->LAGridRights->overrideInitRights("search_header",true);
        $this->LAGridRights->overrideInitRights("select",true); 
        $this->LAGridRights->overrideInitRights("multi_select",false);
        $this->LAGridRights->overrideInitRights("column_move",true);
        $this->LAGridRights->overrideInitRights("col_sorting",true);
        $this->LAGridRights->overrideInitRights("reload_sortings",true);
        $this->LAGridRights->overrideInitRights("details",false);

        $this->LAGridRights->overrideInitRights("export_xls",true);
        $this->LAGridRights->overrideInitRights("export_xlsx",true);
        $this->LAGridRights->overrideInitRights("export_pdf_node",false);
        
        $this->LAGridDB->enableSQLMode();
        
        $SQL = "SELECT qa.user_id as userID,
                    MAX(IF(qa.`question_id` = 1, qa.question_answer_response, '-')) as question1,
                    MAX(IF(qa.`question_id` = 2, qa.question_answer_response, '-')) as question2,
                    MAX(IF(qa.`question_id` = 3, qa.question_answer_response, '-')) as question3,
                    MAX(IF(qa.`question_id` = 4, qa.question_answer_response, '-')) as question4,
                    MAX(IF(qa.`question_id` = 5, qa.question_answer_response, '-')) as question5,
                    MAX(IF(qa.`question_id` = 6, qa.question_answer_response, '-')) as question6,
                    MAX(IF(qa.`question_id` = 7, qa.question_answer_response, '-')) as question7,
                    MAX(IF(qa.`question_id` = 8, qa.question_answer_response, '-')) as question8,
                    MAX(IF(qa.`question_id` = 9, qa.question_answer_response, '-')) as question9,
                    MAX(IF(qa.`question_id` = 10, qa.question_answer_response, '-')) as question10,
                    MAX(IF(qa.`question_id` = 11, qa.question_answer_response, '-')) as question11,
                    MAX(IF(qa.`question_id` = 12, qa.question_answer_response, '-')) as question12,
                    MAX(IF(qa.`question_id` = 13, qa.question_answer_response, '-')) as question13,
                    MAX(IF(qa.`question_id` = 14, qa.question_answer_response, '-')) as email
                FROM questionnaire_answer AS qa
                WHERE qa.questionnaire_id = '{questionnaire_id}'
                AND (qa.created_on BETWEEN '{date_from}' AND '{date_to}')
                GROUP BY qa.user_id
                ORDER BY qa.created_on";
        
        $this->LAGridDB->setSQLSelection($SQL, "user_id");
        
        $this->LAGridDB->setSQLSelectionForReport($SQL, "user_id");
        
        parent::setReportOrientation("landscape");
        parent::setReportHeader("QNE-RESULTS_", '"{dateTime}"', true);
        
        parent::setExportFileName("export_file_qne_results");
        
        parent::G2BInit();
        
    }
    
    public function search() {
        $search = array(
            'date_from'         => array('col_type' => 'ed', 'dtPicker' => true, 'width' => '*', 'label_text' => 'Dátumtól', 'default_value' =>                                         date('Y-m-d H:i:s')),
            'date_to'           => array('col_type' => 'ed', 'dtPicker' => true, 'width' => '*', 'label_text' => 'Dátumig', 'default_value' =>                                         date('Y-m-d H:i:s')),
            'questionnaire_id'  => array('col_type' => 'ed', 'width' => '*', 'label_text' => 'Kérdőív száma', 'default_value'=> '3'),
            'submit'		=> array('col_type'=>'searchBarReloadGrid', 'width'=>'*', 'label_text'=>'')                               
	);
        
        return $search;
    }
    
    public function columns() {
        
        $columns = array(
                        'userID'    => array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'width' => 250, 'align' => 'center'),
                        'email' => array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'width' => 250, 'align' => 'center')   
                    );
        
        for ($i = 1; $i <= $this->numberOfQuestions; $i++) {
            $columns['question' .$i] = array('export'=> true, 'report_width' => 20, 'col_type'=>'ed', 'width' => 125, 'align' => 'center');
        }
         
        return $columns;    
    }
    
    public function attributeLabels() {
        
        $labels = array(
			'userID'       => 'Felhasználó azonosítószáma',
                        'email'        => 'E-mail'
                   );
        
        for ($j = 1; $j <= $this->numberOfQuestions; $j++) {
            $labels['question' .$j] = $j. '. kérdés';
        }
        
        return $labels;   
    }
    
}