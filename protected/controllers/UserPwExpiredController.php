<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\Dict;
	use Yang;

`/yii2-only';


#yii2: done

class UserPwExpiredController extends Grid2Controller
{
	public function __construct() {
		parent::__construct("userPwExpired");
//		$this->LAGridDB->setModelName("User");
//		parent::setTitle(Dict::getValue("page_title_user_management"));
//		parent::enableSubgrid(true);
//		parent::setRights(/*add*/true,/*mod*/true,/*imod*/false,/*del*/true,/*exp*/true,/*$sel*/false,/*$msel*/false,/*$details*/true);
//		parent::exportSettings(Dict::getValue("export_file_user_management"));
	}
	
	protected function G2BInit() {
		$this->LAGridDB->setModelName("User");
	}
	
	public function columns()
	{
		return [
			'password' => ['grid' => false, 'export'=> false, 'col_type'=>'pw', 'prev_password'=>true,],
		];
	}
}