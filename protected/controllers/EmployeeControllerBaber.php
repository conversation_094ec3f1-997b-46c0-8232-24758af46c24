<?php

'yii2-only`;

	namespace app\controllers;
	use XLSXWriter;
	use app\components\Dict;
	use app\models\FileStorage;
	use app\models\Status;
	use Yang;

`/yii2-only';

#yii2: done

/**
 * Babér felé történő dolgozó adatok szinkront végző controller
 *
 * Innote link: baber-employee-sync
 */

trait EmployeeControllerBaber
{
	/**
	 * Javascript Ajax alap meghívás a gombra kattintva
	 * @return void
	 */
	public function actionBaberSync()
	{
		$status = $this->baberSync();
		echo json_encode($status);
	}

	/**
	 * Visszaadja az Ajaxnak a szinkron feldolgozás eredményét
	 * @return array
	 */
	public function baberSync()
	{
		// Adatbázis elérés
		$dbConn = Yang::app()->db; // #see https://innote.login.hu/n-7wpd5adl

		// Fejlécek
		$torzsAdatHeader = $this->getTorzsAdatHeader();
		$eltartottakHeader = $this->getEltartottakHeader();

		// Aktív BOS cégek listája
		$companiesSQL = "
			SELECT
				`company_id`,
				REPLACE(REPLACE(`company_name`, ' ', '-'), '.', '') AS comp_name
			FROM
				`company`
			WHERE
					`status` = " . Status::PUBLISHED . "
				AND CURDATE() BETWEEN `valid_from` AND `valid_to`
				AND `company_name` LIKE '%BOS%'
		";
		$command = $dbConn->createCommand($companiesSQL);
		$companies = $command->queryAll();

		// Fájlok mentésének elérési útvonala és Excel fülek nevei
		$path = Yang::getBasePath() . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'webroot' . DIRECTORY_SEPARATOR . 'file_storage' . DIRECTORY_SEPARATOR . 'bosBaberEmployeeSync';
		$torzsSheetName		= "t_Torzs";
		$eltartottSheetName	= "t_CsEltartottak";

		// Adatok és fájlok generálása cégenként
		$files = [];
		foreach ($companies as $company) {
			// Törzsadatok adat
			$torzsadat = $this->getTorzsadatok($dbConn, $company['company_id']);
			// 5 Gyermek adat
			$eltartott = [];
			for ($i = 1; $i <= 5; $i++) {
				$eltartott = array_merge($eltartott, $this->getEltartottak($dbConn, $i, $company['company_id']));
			}
			// Amennyiben van adat XLSX generálás
			if (!empty($torzsadat)) {
				$files[] = $this->generateExcel2($torzsSheetName, $torzsadat, $torzsAdatHeader, $eltartottSheetName, $eltartott, $eltartottakHeader, $path, $company['comp_name']);
			}
		}

		// Ajax eredmény kiírás
		$retArray = $this->getProcessReturn($files);

		return $retArray;
	}

	/**
	 * Visszaadja a törzsadatok fejlécét excelhez
	 * @return array
	 */
	public function getTorzsadatHeader()
	{
		$headerWithExcelType =
		[
			"Adoazonositojel" => "string",
			"Elonev" => "string",
			"Vezeteknev" => "string",
			"Keresztnev" => "string",
			"Leanynev" => "string",
			"Anyjaneve" => "string",
			"Neme" => "string",
			"Allampolg" => "string",
			"SzuletesiOrszag_Allampolg" => "string",
			"Szulhely" => "string",
			"Szuldatum" => "date",
			"TAJszam" => "string",
			"Szemelyisz" => "string",
			"Szemelyigsz" => "string",
			"NyugdijasStatuszKod" => "string",
			"NyugdijasStatuszDatum" => "string",
			"Nytorzsszam" => "string",
			"Adoszam" => "string",
			"LakhelyTavolsag" => "string",
			"LakhelyTavolsag2" => "string",
			"Utikoltseg1" => "string",
			"Utikoltseg2" => "string",
			"Iranyitoszam" => "string",
			"Helyseg" => "string",
			"Utca" => "string",
			"UtcaJellege" => "string",
			"Hazszam" => "string",
			"Epulet" => "string",
			"Lepcsohaz" => "string",
			"Emelet" => "string",
			"Ajto" => "string",
			"LevIrsz" => "string",
			"LevHelyseg" => "string",
			"LevUtca" => "string",
			"LevUtcaJellege" => "string",
			"LevHazszam" => "string",
			"LevEpulet" => "string",
			"LevLepcsohaz" => "string",
			"LevEmelet" => "string",
			"LevAjto" => "string",
			"Telefon" => "string",
			"EmailCim" => "string",
			"JogviszonyAzonosito" => "string",
			"Belepes" => "date",
			"Kilepes" => "string", // csak kilépés esetén kell és üres esetén nem jó 1900.
			"Kilepes_modja" => "string",
			"Probaidok" => "date",
			"Probaidov" => "date",
			"JogviszonyokKod" => "string",
			"JogviszonyKod" => "string",
			//"JogviszonyMegnevezes" => "string",
			"JogviszonyDatum" => "date",
			"MunkaszerzodesTipusa" => "string",
			"MunkaszerzodesTolDatum" => "date",
			"MunkaszerzodesIgDatum" => "string",
			"MunkakorKod" => "string",
			"MunkakorMegnevezes" => "string",
			"MunkakorDatum" => "date",
			"FEORkod" => "string",
			"FEORDatum" => "date",
			"BeosztasKod" => "string",
			"BeosztasDatum" => "string", // NULL
			"ProfitcentrumKod" => "string",
			//"ProfitcentrumMegnevezes" => "string",
			"ProfitcentrumDatum" => "date",
			"AbKod" => "string",
			"ABDatum" => "string",
			"Szervezetkod" => "string",
			"SzervezetMegnevezes" => "string",
			"SzervezetDatum" => "date",
			"FokszamokKod" => "string",
			"FokszamokMegnevezes" => "string",
			"FokszamokDatum" => "string",
			"BertipKod" => "string",
			"Alapber" => "string",
			"ArfolyamokKod" => "string",
			"AlapberValtozasDatum" => "string",
			"UjSzamlaszam" => "string",
			"BankKod" => "string",
			"Penztarkod" => "string",
			// "PenztarMegnevezes" => "string",
			"PenztarDatum" => "date",
			"MunkIranyitoszam" => "string",
			"MunkHelyseg" => "string",
			"MunkUtca" => "string",
			"MunkUtcaJellege" => "string",
			"MunkHazszam" => "string",
			"MunkEpulet" => "string",
			"MunkLepcsohaz" => "string",
			"MunkEmelet" => "string",
			"MunkAjto" => "string",
			"HetiMunkaora" => "string",
			"HetiMunkaoraValtozasDatum" => "date",
			"MuszakokKod" => "string",
			//"MuszakokMegnevezes" => "string",
			"MuszakokDatum" => "date",
			"OKKezdetEv" => "string",
			"OKKezdet" => "string",
			"OKHonap" => "string",
			"OKTOraKeret" => "string",
			"Nyelvazonosito" => "string",
			"KulsoAzonositoKod" => "string",
			"Azonosito" => "string",
			"Torzsszam" => "string",
			"UtolsoMunkanap" => "string",
			"Vegzettseg" => "string",
			"MegszerzesDatuma" => "string",
			"Tagozat" => "string"
		];

		return $headerWithExcelType;
	}

	/**
	 * Visszaadja az eltartottak fejlécét excelhez
	 * @return array
	 */
	public function getEltartottakHeader()
	{
		$headerWithExcelType =
		[
			"DolgozoAzonosito"  => "string",
			"Adoazonositojel" => "string",
			"TAJszam" => "string",
			"Neve" => "string",
			"Neme" => "string",
			"Allampolg" => "string",
			"SzulIdo" => "date",
			"SzulHelyseg" => "string",
			"Anyjaneve" => "string",
			"Apjaneve" => "string",
			"ID" => "string"
		];

		return $headerWithExcelType;
	}

	/**
	 * Lekérdezi a gyermekek dinamikus tabról az adatokat az átadáshoz Babér felé
	 * @param object $conn
	 * @param int $child_number
	 * @return array
	 */
	public function getEltartottak($conn, $childNumber, $companyId)
	{
		switch ($childNumber) {
			case '1':
				$bdayString = 'first';
				break;
			case '2':
				$bdayString = 'second';
				break;
			case '3':
				$bdayString = 'third';
				break;
			case '4':
				$bdayString = 'fourth';
				break;
			case '5':
				$bdayString = 'fifth';
				break;
		}

		$eltartottSQL = "
			SELECT
				emp.`tax_number` AS DolgozoAzonosito,
				tab5.`value` AS Adoazonositojel,
				'' AS TAJszam,
				IF(tab3.`value` = 'Magzat', 'Magzat', tab.`value`) AS Neve,
				'' AS Neme,
				'' AS Allampolg,
				tab2.`value` AS SzulIdo,
				'' AS SzulHelyseg,
				IFNULL(tab4.`value`, 'Teszt Dummy Javítandó') AS Anyjaneve,
				'' AS Apjaneve,
				CONCAT(emp.`tax_number`, '{$childNumber}') AS ID
			FROM
				`employee` AS emp
			JOIN
				`employee_contract` AS ec ON
						ec.`employee_id` = emp.`employee_id`
					AND ec.`status` = " . Status::PUBLISHED . "
					AND (CURDATE() BETWEEN ec.`valid_from` AND ec.`valid_to`)
			LEFT JOIN
				`employee_tab` AS `etab` ON
						etab.`connect_id` = emp.`employee_id`
					AND etab.`tab_id` = 6
					AND etab.`status` = " . Status::PUBLISHED . "
					AND (CURDATE() BETWEEN etab.`valid_from` AND etab.`valid_to`)
			LEFT JOIN
				`employee_tab_item` tab ON
						tab.`column_id` LIKE '" . $childNumber . "_child_name'
					AND etab.`employee_tab_id` = tab.`employee_tab_id`
					AND tab.`status` = " . Status::PUBLISHED . "
			LEFT JOIN
				`employee_tab_item` tab2 ON
						tab2.`column_id` LIKE 'birth_date_" . $bdayString . "_child'
					AND etab.`employee_tab_id` = tab2.`employee_tab_id`
					AND tab2.`status` = " . Status::PUBLISHED . "
			LEFT JOIN
				`employee_tab_item` tab3 ON
						tab3.`column_id` LIKE '" . $childNumber . "_child_relation'
					AND etab.`employee_tab_id` = tab3.`employee_tab_id`
					AND tab3.`status` = " . Status::PUBLISHED . "
			LEFT JOIN
				`employee_tab_item` tab4 ON
						tab4.`column_id` LIKE '" . $childNumber . "_mothers_name'
					AND etab.`employee_tab_id` = tab4.`employee_tab_id`
					AND tab4.`status` = " . Status::PUBLISHED . "
			LEFT JOIN
				`employee_tab_item` tab5 ON
						tab5.`column_id` LIKE '" . $childNumber . "_child_tax_number'
					AND etab.`employee_tab_id` = tab5.`employee_tab_id`
					AND tab5.`status` = " . Status::PUBLISHED . "
			WHERE
					((tab.`value` IS NOT NULL AND tab.`value` <> '') OR tab3.`value` = 'Magzat')
				AND tab5.`value` IS NOT NULL
				AND tab5.`value` <> ''
				AND emp.`status` = " . Status::PUBLISHED . "
				AND emp.`tax_number` IS NOT NULL
				AND (CURDATE() BETWEEN emp.`valid_from` AND emp.`valid_to`)
				AND emp.`company_id` = '{$companyId}'
			ORDER BY
				emp.`last_name`,
				emp.`first_name`
		";

		$command = $conn->createCommand($eltartottSQL);
		$res = $command->queryAll();

		return $res;
	}

	/**
	 * Lekérdezi a dolgozók törzsadatait, hogy átadhassuk a babérnak
	 * @param object $conn
	 * @return array
	 */
	public function getTorzsadatok($conn, $companyId)
	{
		$empDataSQL = $this->getEmployeeSQL($companyId);
		$command = $conn->createCommand($empDataSQL);
		$data = $command->queryAll();

		// Babér kapcsolótábla
		$connectSQL = $this->getBaberConnectTableSQL();
		$connectors = dbFetchAll($connectSQL);

		// Kötelező mezők
		$mustHave = ["Penztarkod", "Szervezetkod", "Szulhely", "Szuldatum", "JogviszonyKod", "Neme", "JogviszonyAzonosito", "FokszamokKod"];
		$unsetArr = [];

		$ret = [];
		foreach ($data as $employee) {
			foreach ($employee as $key => $value) {
				if (in_array($key, $mustHave) && $value == "") {
					continue;
				}

				if ($value == "") {
					$employee[$key] = 'NULL';
				}

				foreach ($connectors as $syncField) {
					if ($syncField['baber_field'] == $key && $syncField['login_id'] == $value) {
						$employee[$key] = $syncField['baber_id'];
					}
				}
			}
			$ret[] = $employee;
		}
		return $ret;
	}

	private function getEmployeeSQL($companyId)
	{
		$statusPublished = Status::PUBLISHED;
		$defaultEnd	= App::getSetting("defaultEnd");
		$SQL = "
		SELECT
			emp.`tax_number` AS Adoazonositojel,
			emp.`title` AS Elonev,
			emp.`last_name` AS Vezeteknev,
			emp.`first_name` AS Keresztnev,
			emp.`nameofbirth` AS Leanynev,
			tab2.`value` AS Anyjaneve,
			emp.`gender` AS Neme,
			tab.`value` AS Allampolg,
			tab6.`value` AS SzuletesiOrszag_Allampolg,
			tab3.`value` AS Szulhely,
			tab4.`value` AS Szuldatum,
			tab5.`value` AS TAJszam,
			'NULL' AS Szemelyisz,
			'NULL' AS Szemelyigsz,
			'0' AS NyugdijasStatuszKod,
			'NULL' AS NyugdijasStatuszDatum,
			'NULL' AS Nytorzsszam,
			'NULL' AS Adoszam,
			'NULL' AS LakhelyTavolsag,
			'NULL' AS LakhelyTavolsag2,
			'NULL' AS Utikoltseg1,
			'NULL' AS Utikoltseg2,
			tab7.`value` AS 'Iranyitoszam',
			tab8.`value` AS 'Helyseg',
			tab10.`value` AS Utca,
			'NULL' AS UtcaJellege,
			'NULL' AS Hazszam,
			'NULL' AS Epulet,
			'NULL' AS Lepcsohaz,
			'NULL' AS Emelet,
			'NULL' AS Ajto,
			tab20.`value` AS LevIrsz,
			tab21.`value` AS LevHelyseg,
			tab22.`value` AS LevUtca,
			'NULL' AS LevUtcaJellege,
			'NULL' AS LevHazszam,
			'NULL' AS LevEpulet,
			'NULL' AS LevLepcsohaz,
			'NULL' AS LevEmelet,
			'NULL' AS LevAjto,
			tab9.`value` AS Telefon,
			user.`email` AS EmailCim,
			CONCAT_WS('', emp.`tax_number`, ec.`employee_contract_number`) AS JogviszonyAzonosito,
			ec.`ec_valid_from` AS Belepes,
			IF(ec.`ec_valid_to` = '2038-01-01', 'NULL', ec.`ec_valid_to`) AS Kilepes,
			tab11.`value` AS Kilepes_modja,
			ec.`ec_valid_from` AS Probaidok,
			tab12.`value` AS Probaidov,
			'1101' AS JogviszonyokKod,
			tab13.`value` AS JogviszonyKod,
			-- 'NULL' AS JogviszonyMegnevezes,
			etab2.`valid_from` AS JogviszonyDatum,
			ec.`employee_contract_type` AS MunkaszerzodesTipusa,
			ec.`ec_valid_from` AS MunkaszerzodesTolDatum,
			IF(ec.`employee_contract_type` <> 1, ec.`ec_valid_to`, 'NULL')  AS MunkaszerzodesIgDatum,
			TRIM(ep.`employee_position_name`) AS MunkakorKod,
			TRIM(ep.`employee_position_name`) AS MunkakorMegnevezes,
			etab4.`valid_from` AS MunkakorDatum,
			tab15.`value` AS FEORkod,
			etab4.`valid_from` AS FEORDatum,
			'NULL' AS BeosztasKod,
			'NULL' AS BeosztasDatum,
			emp.`company_org_group1_id` AS ProfitcentrumKod,
			-- 'NULL' AS ProfitcentrumMegnevezes,
			emp.`valid_from` AS ProfitcentrumDatum,
			'NULL' AS AbKod,
			'NULL' AS ABDatum,
			emp.`unit_id` AS Szervezetkod,
			'NULL' AS SzervezetMegnevezes,
			emp.`valid_from` AS SzervezetDatum,
			ecost.`cost_id` AS FokszamokKod,
			`cost`.`cost_name` AS FokszamokMegnevezes,
			ecost.`valid_from` AS FokszamokDatum,
			ec.`wage_type` AS BertipKod,
			'NULL' AS Alapber,
			'NULL' AS ArfolyamokKod,
			'NULL' AS AlapberValtozasDatum,
			CONCAT(tab17.`value`, tab18.`value`) AS UjSzamlaszam,
			'NULL' AS BankKod,
			emp.`company_org_group3_id` AS Penztarkod,
			-- 'NULL' AS PenztarMegnevezes,
			emp.`valid_from` AS PenztarDatum,
			'NULL' AS MunkIranyitoszam,
			'NULL' AS MunkHelyseg,
			'NULL' AS MunkUtca,
			'NULL' AS MunkUtcaJellege,
			'NULL' AS MunkHazszam,
			'NULL' AS MunkEpulet,
			'NULL' AS MunkLepcsohaz,
			'NULL' AS MunkEmelet,
			'NULL' AS MunkAjto,
			ec.`daily_worktime` * 5 AS HetiMunkaora,
			ec.`valid_from` AS HetiMunkaoraValtozasDatum,
			eg.`group_value` AS MuszakokKod,
			-- 'NULL' AS MuszakokMegnevezes,
			eg.`valid_from` AS MuszakokDatum,
			'NULL' AS OKKezdetEv,
			'NULL' AS OKKezdet,
			'NULL' AS OKHonap,
			'NULL' AS OKTOraKeret,
			'NULL' AS Nyelvazonosito,
			'NULL' AS KulsoAzonositoKod,
			'NULL' AS Azonosito,
			emp.`emp_id` AS Torzsszam,
			'NULL' AS UtolsoMunkanap,
			'NULL' AS Vegzettseg,
			'NULL' AS MegszerzesDatuma,
			'NULL' AS Tagozat
		FROM
			`employee` AS emp
		JOIN
			`employee_contract` AS ec ON
					ec.`employee_id` = emp.`employee_id`
				AND ec.`status` = $statusPublished
				AND (CURDATE() BETWEEN ec.`valid_from` AND ec.`valid_to`)
		LEFT JOIN `employee_position` AS ep ON
					ep.`employee_position_id` = ec.`employee_position_id`
					AND ep.`status` = $statusPublished
					AND CURDATE() BETWEEN ep.`valid_from` AND ep.`valid_to`
		LEFT JOIN
			`employee_group` AS eg ON
					eg.`employee_contract_id` = ec.`employee_contract_id`
				AND eg.`status` = $statusPublished
				AND (CURDATE() BETWEEN eg.`valid_from` AND eg.`valid_to`)
				AND eg.`group_id` = 'workgroup_id'
		LEFT JOIN
			`employee_cost` AS ecost ON
					ecost.`employee_contract_id` = ec.`employee_contract_id`
				AND ecost.`status` = $statusPublished
				AND (CURDATE() BETWEEN ecost.`valid_from` AND ecost.`valid_to`)
		LEFT JOIN
			`cost` ON
					`cost`.`cost_id` = ecost.`cost_id`
				AND `cost`.`status` = $statusPublished
				AND (CURDATE() BETWEEN `cost`.`valid_from` AND `cost`.`valid_to`)
		LEFT JOIN
			`unit` AS u ON
					u.`unit_id` = emp.`unit_id`
				AND u.`status` = $statusPublished
				AND (CURDATE() BETWEEN u.`valid_from` AND u.`valid_to`)
		LEFT JOIN
			`user` ON
					`user`.`employee_id` = emp.`employee_id`
				AND `user`.`status` = $statusPublished
				AND (CURDATE() BETWEEN `user`.`valid_from` AND `user`.`valid_to`)
		LEFT JOIN
			`employee_tab` AS etab ON
					etab.`connect_id` = emp.`employee_id`
				AND etab.`tab_id` = 1
				AND etab.`status` = $statusPublished
				AND (CURDATE() BETWEEN etab.`valid_from` AND etab.`valid_to`)
		LEFT JOIN
			`employee_tab` AS etab3 ON
					etab3.`connect_id` = emp.`employee_id`
				AND etab3.`tab_id` = 3
				AND etab3.`status` = $statusPublished
				AND (CURDATE() BETWEEN etab3.`valid_from` AND etab3.`valid_to`)
		LEFT JOIN
			`employee_tab` AS etab2 ON
					etab2.`connect_id` = emp.`employee_id`
				AND etab2.`tab_id` = 2
				AND etab2.`status` = $statusPublished
				AND (CURDATE() BETWEEN etab2.`valid_from` AND etab2.`valid_to`)
		LEFT JOIN
			`employee_tab` AS etab4 ON
					etab4.`connect_id` = emp.`employee_id`
				AND etab4.`tab_id` = 4
				AND etab4.`status` = $statusPublished
				AND (CURDATE() BETWEEN etab4.`valid_from` AND etab4.`valid_to`)
		LEFT JOIN
			`employee_tab` AS etab7 ON
					etab7.`connect_id` = emp.`employee_id`
				AND etab7.`tab_id` = 7
				AND etab7.`status` = $statusPublished
				AND (CURDATE() BETWEEN etab7.`valid_from` AND etab7.`valid_to`)
		LEFT JOIN
			`employee_tab_item` AS tab ON
					tab.`column_id` LIKE 'nationality'
				AND etab.`employee_tab_id` = tab.`employee_tab_id`
				AND tab.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab2 ON
					tab2.`column_id` LIKE 'mothers_name'
				AND etab.`employee_tab_id` = tab2.`employee_tab_id`
				AND tab2.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab3 ON
					tab3.`column_id` LIKE 'place_of_birth'
				AND etab.`employee_tab_id` = tab3.`employee_tab_id`
				AND tab3.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab4 ON
					tab4.`column_id` LIKE 'date_of_birth'
				AND etab.`employee_tab_id` = tab4.`employee_tab_id`
				AND tab4.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab5 ON
					tab5.`column_id` LIKE 'social_security_number'
				AND etab.`employee_tab_id` = tab5.`employee_tab_id`
				AND tab5.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab6 ON
					tab6.`column_id` LIKE 'country_of_birth'
				AND etab.`employee_tab_id` = tab6.`employee_tab_id`
				AND tab6.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab7 ON
					tab7.`column_id` LIKE 'address_zip'
				AND etab3.`employee_tab_id` = tab7.`employee_tab_id`
				AND tab7.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab8 ON
					tab8.`column_id` LIKE 'address_city'
				AND etab3.`employee_tab_id` = tab8.`employee_tab_id`
				AND tab8.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab9 ON
					tab9.`column_id` LIKE 'phone_number'
				AND etab3.`employee_tab_id` = tab9.`employee_tab_id`
				AND tab9.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab10 ON
					tab10.`column_id` LIKE 'address_adr'
				AND etab3.`employee_tab_id` = tab10.`employee_tab_id`
				AND tab10.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab11 ON
					tab11.`column_id` LIKE 'types_of_termination_term'
				AND etab2.`employee_tab_id` = tab11.`employee_tab_id`
				AND tab11.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab12 ON
					tab12.`column_id` LIKE 'end_of_probation'
				AND etab2.`employee_tab_id` = tab12.`employee_tab_id`
				AND tab12.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab13 ON
					tab13.`column_id` LIKE 'types_of_employment'
				AND etab2.`employee_tab_id` = tab13.`employee_tab_id`
				AND tab13.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab14 ON
					tab14.`column_id` LIKE 'job'
				AND etab4.`employee_tab_id` = tab14.`employee_tab_id`
				AND tab14.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab15 ON
					tab15.`column_id` LIKE 'feor_number'
				AND etab4.`employee_tab_id` = tab15.`employee_tab_id`
				AND tab15.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab16 ON
					tab16.`column_id` LIKE 'fund_type_2'
				AND etab7.`employee_tab_id` = tab16.`employee_tab_id`
				AND tab16.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab17 ON
					tab17.`column_id` LIKE 'referral_account_account_code'
				AND etab.`employee_tab_id` = tab17.`employee_tab_id`
				AND tab17.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab18 ON
					tab18.`column_id` LIKE 'referral_account_number'
				AND etab.`employee_tab_id` = tab18.`employee_tab_id`
				AND tab18.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab19 ON
					tab19.`column_id` LIKE 'fund_type_2_beginning'
				AND etab7.`employee_tab_id` = tab19.`employee_tab_id`
				AND tab19.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab20 ON
					tab20.`column_id` LIKE 'res_address_zip'
				AND etab3.`employee_tab_id` = tab20.`employee_tab_id`
				AND tab20.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab21 ON
					tab21.`column_id` LIKE 'res_address_city'
				AND etab3.`employee_tab_id` = tab21.`employee_tab_id`
				AND tab21.`status` = $statusPublished
		LEFT JOIN
			`employee_tab_item` AS tab22 ON
					tab22.`column_id` LIKE 'res_address_adr'
				AND etab3.`employee_tab_id` = tab22.`employee_tab_id`
				AND tab22.`status` = $statusPublished
		WHERE
				emp.`status` = $statusPublished
			AND (CURDATE() BETWEEN emp.`valid_from` AND emp.`valid_to`)
			AND emp.`tax_number` IS NOT NULL
			AND emp.`tax_number` <> ''
			AND emp.`company_id` = '{$companyId}'
			AND IF(ec.`ec_valid_to` <> '$defaultEnd', tab11.`value` IS NOT NULL, 1=1)
		GROUP BY
			 emp.`employee_id`
		ORDER BY
			emp.`last_name`,
			emp.`first_name`
		";

		return $SQL;
	}

	private function getBaberConnectTableSQL()
	{
		$SQL = "SELECT baber_field, baber_id, baber_name, login_id FROM baber_sync_connect;";
		return $SQL;
	}

	/**
	 * Legenerálja az XLSX fájlt benne a két füllel és a hozzájuk tartozó adatokkal a megadott elérési útvonal alá a megadott névvel
	 * @param string $torzsSheetName
	 * @param array $torzsadat
	 * @param array $torzsAdatHeader
	 * @param string $eltartottSheetName
	 * @param array $eltartott
	 * @param array $eltartottakHeader
	 * @param string $path
	 * @param string $compName
	 * @return void
	 */
	public function generateExcel2($torzsSheetName, $torzsAdat, $torzsAdatHeader, $eltartottSheetName, $eltartott, $eltartottakHeader, $path, $compName)
	{
		// Alap paraméterek, xlsx író meghívás
		$fileName = "BaberBosAdatok" . $compName;
		$fileName .= date("_Y.m.d_H.i.s") . '.xlsx';
		$title3 = "TTWA Report";
		require_once Yang::getBasePath() . '/extensions/XLSXWriter/xlsxwriter.php';
		$objphpwriter = new XLSXWriter();
		$objphpwriter->setAuthor($title3);

		// Fejlécek típusának definiálása
		$colInd = 0;
		$colInd2 = 0;
		$headerRowType = [];
		$headerRowType["torzs"] = [];
		$headerRowType["eltartottak"] = [];
		$valuesWidths = [];
		$valuesWidths["torzs"] = [];
		$valuesWidths["eltartottak"] = [];
		foreach ($torzsAdatHeader as $column => $type) {
			$valuesWidths["torzs"][$colInd] = strlen($column) + 5;
			if ($type === "numeric") {
				$headerRowType["torzs"][$column] = "integer";
			} elseif ($type === "decimal") {
				$headerRowType["torzs"][$column] = "0.00";
			} elseif ($type === "date") {
				$headerRowType["torzs"][$column] = "YYYY-MM-DD";
			} elseif ($type === "time") {
				$headerRowType["torzs"][$column] = "HH:MM:SS";
			} elseif ($type === "datetime") {
				$headerRowType["torzs"][$column] = "YYYY-MM-DD HH:MM:SS";
			} else {
				$headerRowType["torzs"][$column] = "string";
			}
			$colInd++;
		}
		foreach ($eltartottakHeader as $column => $type) {
			$valuesWidths["eltartottak"][$colInd2] = strlen($column) + 5;
			if ($type === "numeric") {
				$headerRowType["eltartottak"][$column] = "integer";
			} elseif ($type === "decimal") {
				$headerRowType["eltartottak"][$column] = "0.00";
			} elseif ($type === "date") {
				$headerRowType["eltartottak"][$column] = "YYYY-MM-DD";
			} elseif ($type === "time") {
				$headerRowType["eltartottak"][$column] = "HH:MM:SS";
			} elseif ($type === "datetime") {
				$headerRowType["eltartottak"][$column] = "YYYY-MM-DD HH:MM:SS";
			} else {
				$headerRowType["eltartottak"][$column] = "string";
			}
			$colInd2++;
		}

		// Adatok kigyűjtése
		$valuesRows = [];
		$valuesRows["torzs"] = [];
		$valuesRows["eltartottak"] = [];
		$valuesRowsEvery = [];
		$valuesRowsEvery["torzs"] = [];
		$valuesRowsEvery["eltartottak"] = [];
		foreach ($torzsAdat as $key => $value) {
			$colInd = 0;
			foreach ($torzsAdatHeader as $column => $type) {
				$val = '';
				if (isset($torzsAdat[$key][$column])) {
					$val = $torzsAdat[$key][$column];
				}

				$valuesRows["torzs"][] = $val;
				$valueWidth["torzs"] = strlen($val);
				if ($valuesWidths["torzs"][$colInd] < $valueWidth["torzs"]) {
					$valuesWidths["torzs"][$colInd] = $valueWidth["torzs"];
				}
				$colInd++;
			}

			$valuesRowsEvery["torzs"][] = $valuesRows["torzs"];
			$valuesRows["torzs"] = [];
		}
		foreach ($eltartott as $key => $value) {
			$colInd2 = 0;
			foreach ($eltartottakHeader as $column => $type) {
				$val2 = '';
				if (isset($eltartott[$key][$column])) {
					$val2 = $eltartott[$key][$column];
				}

				$valuesRows["eltartottak"][] = $val2;
				$valueWidth["eltartottak"] = strlen($val2);
				if ($valuesWidths["eltartottak"][$colInd2] < $valueWidth["eltartottak"]) {
					$valuesWidths["eltartottak"][$colInd2] = $valueWidth["eltartottak"];
				}
				$colInd2++;
			}

			$valuesRowsEvery["eltartottak"][] = $valuesRows["eltartottak"];
			$valuesRows["eltartottak"] = [];
		}

		// Stílusok definiálása és XLSX kiírás
		$stylesHeader = [];
		$stylesHeader["torzs"]			= ['font' => 'Calibri', 'font-size' => 11, 'widths' => $valuesWidths["torzs"]];
		$stylesHeader["eltartottak"]	= ['font' => 'Calibri', 'font-size' => 11, 'widths' => $valuesWidths["eltartottak"]];
		$objphpwriter->writeSheetHeader($torzsSheetName, $headerRowType["torzs"], $stylesHeader["torzs"]);
		$styles = ['font' => 'Calibri', 'font-size' => 11];
		foreach ($valuesRowsEvery["torzs"] as $resultDataArray) {
			$objphpwriter->writeSheetRow($torzsSheetName, $resultDataArray, $styles);
		}

		$objphpwriter->writeSheetHeader($eltartottSheetName, $headerRowType["eltartottak"], $stylesHeader["eltartottak"]);
		foreach ($valuesRowsEvery["eltartottak"] as $resultDataArray) {
			$objphpwriter->writeSheetRow($eltartottSheetName, $resultDataArray, $styles);
		}

		$objphpwriter->writeToFile($path . '/' . $fileName);

		// Visszatérési érték file storagehez
		$ret["fileName"] = $fileName;
		$ret["fileUrl"] =  'file_storage/bosBaberEmployeeSync/' . $fileName;
		$ret["createdBy"] = 'BaberEmpSyncXLS';
		$ret["fileUploadedDate"] = date('Y-m-d H:i:s');
		$ret["fileId"] = uniqid();
		$ret["fileType"] = "xlsx";

		return $ret;
	}

	/**
	 * Lementi a fájlokat a file storage táblába és letölthető linket ad vissza az Ajax lekérés eredményeképp
	 * @param array $files
	 * @return array
	 */
	public function getProcessReturn($files)
	{
		$fileGroupId	= uniqid();
		$color			= "#039BE5";
		$msg			= Dict::getValue("data_upload_success");
		$createdOn		= date("Y-m-d H:i:s");

		$html = '<div style="width:100%; height:100%; box-sizing:border-box;">';
		$html .= '<span style="color:' . $color . '; font-size:16px;">' . Dict::getValue("created_on") . ": <b>{$createdOn}</b>" . '</span><br/>';
		$html .= "<b>{$msg}</b><br/>";

		foreach ($files as $file) {
			$fs = new FileStorage();
			$fs->file_id 			= $file["fileId"];
			$fs->file_group_id		= $fileGroupId;
			$fs->file_url			= $file["fileUrl"];
			$fs->file_name			= $file["fileName"];
			$fs->file_type			= $file["fileType"];
			$fs->created_by			= $file["createdBy"];
			$fs->file_upload_date	= $file["fileUploadedDate"];
			$html .= '<i><a href="/filestorage/downloadFile?file_id=' . $file["fileId"] . '" onclick="window.open(this.href);return false;">' . $fs->file_name . '</a></i></br>';
			$fs->save();
		}

		$html .= '</div>';

		$ret = [];
		$ret["title"] = Dict::getValue("baber_xlsx_generation");
		$ret["html"] = $html;

		return $ret;
	}
}
