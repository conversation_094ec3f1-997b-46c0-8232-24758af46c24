<?php

/**
 * HomeDashboardController
 *
 */
class HomeDashboardController extends Controller
{
	private static $maxPinAttempts = 5;
	private $stPublished;
	private $defaultEnd;


	public function actionIndex()
    {
        if (array_key_exists("inetconntest", $_GET)) {
            $this->testNetworkConnectivity();
        }


        AnyCache::clear();

        $view = "index";
        $cplatformClient = App::getSetting("cplatform_client");
        $this->layout = "//layouts/pageEmpty";
        $card_reader = (int)requestParam('card_reader');
        $card_num_length = (int)requestParam('card_num_length');

        if (!$card_reader && isset($_SESSION["demo"]["cardNumber"])) {
            unset($_SESSION["demo"]["cardNumber"]);
        }

        if (!isset($_SESSION['cplatform']['home'])) {
            $_SESSION['cplatform']['home'] = true;
        }

        if ($cplatformClient == "kuehnenagel") {

            $view = "application.views.customers.kuehnenagel.homeDashboard.index";
        } else if ($cplatformClient == "airbus") {

            $view = "application.views.customers.airbus.homeDashboard.index";
        } else if ($cplatformClient == "masterg") {

            $view = "application.views.customers.masterg.homeDashboard.index";
        } else if ($cplatformClient == "te") {

            $view = "application.views.customers.teesztergom.homeDashboard.index";
        } else if ($cplatformClient == "eisberg") {

            $view = "application.views.customers.eisberg.homeDashboard.index";
        } else if ($cplatformClient == "sonima") {

            $view = "application.views.customers.sonima.homeDashboard.index";
        } else if ($cplatformClient == "coloplast") {

            $view = "application.views.customers.coloplast.homeDashboard.index";			
		} else if ($cplatformClient == "havi") {

			$view = "application.views.customers.havi.homeDashboard.index";
		} else if ($cplatformClient == 'suzuki') {

            $view = 'application.views.customers.suzuki.homeDashboard.index';
        } else if ($cplatformClient == 'danubius') {

			$view = 'application.views.customers.danubius.homeDashboard.index';
		} else {

		    $view = "index";
        }

		$this->render($view, [
			"card_reader" => $card_reader,
			"card_num_length" => $card_num_length,
			"dictionaryTexts" => $this->getDictionaryTexts(),
		]);
	}

	public function actionSwitchQuestion()
	{
		$this->layout = "//layouts/empty";

		$qId = rand(1,25);

		$ret = [
			"qId" => $qId,
			"qContent" => '
				<div id="questionCell_' . $qId . '" class="questionCell" style="left:100%;">
					<div class="questionCellContent">
						<div class="questionCellMainContent">
							<div class="table">
								<div class="cell noSelect">
									Ide jön a(z) ' . $qId . '. kérdés?
								</div>
							</div>
						</div>
						<div class="questionCellBtnContent">
						</div>
					</div>
				</div>
			',
			"qAnswers" => '
				<div class="buttons">
					<div class="relButtons">
						<div id="questionResp_' . $qId . '" class="qButton face_1"></div>
						<div id="questionResp_' . $qId . '" class="qButton face_2"></div>
						<div id="questionResp_' . $qId . '" class="qButton face_3"></div>
						<div id="questionResp_' . $qId . '" class="qButton face_4"></div>
						<div id="questionResp_' . $qId . '" class="qButton face_5"></div>
					</div>
				</div>
			',
		];

		echo json_encode($ret);
	}

	public function actionHasPinSet()
	{
		$userid = userID();
		$response = [ 'status' => 1, 'has_pin' => false ];

		if (isset($userid)) {
			$stPublished = Status::STATUS_PUBLISHED;
			$defaultEnd = App::getSetting('defaultEnd');

			$sql = "
				SELECT `pin`
				FROM `user`
				WHERE `user_id` = '$userid'
				AND `status` = $stPublished
				AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '$defaultEnd')
			";

			$user = dbFetchRow($sql);

			if (sizeof($user) === 1) {
				$response['has_pin'] = $user['pin'] !== null;
			}
		}

		echo json_encode($response);
	}

    public function actionGetFullname()
    {
        $uri = requestParam('uri');
        $user_id = '';
        $fullname = '';

        if (userID() !== null) {
            $user_id = userID();
            $fullname = $this->getEmployeeNameByUserId($user_id);

            if ($fullname == null || $fullname == '') {
                $fullname = $this->getUserNameByUserId($user_id);
            }
        }

        echo $fullname;

    }

	public function actionInitSession()
	{
		$uri = requestParam('uri');
		$user_id = '';
		$fullname = '';

		if (userID() !== null) {
			$user_id = userID();
			$fullname = $this->getEmployeeNameByUserId($user_id);

            if ($fullname == null || $fullname == '') {
                $fullname = $this->getUserNameByUserId($user_id);
            }
		}

		$_SESSION["tiptime"]["settings"]['demoModeBackButton'] = 1;
		$_SESSION["tiptime"]["settings"]['demoModeBackButtonUrl'] = $uri;
		$_SESSION["tiptime"]["settings"]['activitylogger_guest_mode'] = 1;
		$_SESSION["tiptime"]["settings"]["demoRosenbergerKiosk"] = 1;

		echo $fullname;

	}

	public function actionSetNavigationSession()
	{
		$response = [ 'status' => 0 ];

		if (!isset($_SESSION['cplatform']['flex']['navigation_session']))
		{
			$response['status'] = 1;
			$_SESSION['cplatform']['flex']['navigation_session'] = 0;
		}

		echo json_encode($response);
	}

	public function actionSaveCard()
	{
		$cardNumber = requestParam('cardNumber');
		$useRegTable = requestParam('useRegTable');

		if (preg_match('/^a_/', $cardNumber))
		{
			$cardNumberParts = explode('a_', $cardNumber);
			$cardNumber = $cardNumberParts[1];
		}
        if (!empty($cardNumber)) {
            $_SESSION["demo"]["cardNumber"] = $cardNumber;
        }
        else {
            $cardNumber = $_SESSION["demo"]["cardNumber"] ?? '';
        }


		if ($useRegTable === true) {
			$user = $this->checkExistingUserWithRegTable($cardNumber);
			$_SESSION['flex']['user_id'] = $user['user_id'];
		}

		$response = [ 'status' => 1 ];
		echo json_encode($response);
	}

	public function actionGetCardFromSession()
	{
		$cardNumberInSession = $_SESSION["demo"]["cardNumber"];

		if (isset($cardNumberInSession)) {
			$response = ['status' => 1, 'cardNumber' => $cardNumberInSession];
		} else {
			$response = ['status' => 0];
		}

		echo json_encode($response);
	}

	public function actionRemoveCardFromSession()
	{
		if (isset($_SESSION["demo"]["cardNumber"]))
		{
			unset($_SESSION["demo"]["cardNumber"]);
		}

		if (isset($_SESSION['flex']['user_id']))
		{
			unset($_SESSION['flex']['user_id']);
		}

		$response = [ 'status' => 1 ];
		echo json_encode($response);
	}

	public function actionGetQuestionnaireServiceUrl()
	{
		$questionnaireService = str_replace("{host}", isset($_SERVER["SERVER_NAME"])?$_SERVER["SERVER_NAME"]:"localhost", App::getSetting("questionnaireService"));
		echo $questionnaireService;
	}

    /**
     * Check the General terms and conditions is approved
     *
     */
	public function actionCheckGtcIsApproved()
    {
        $userid   = userID();
        $response = [];

        if (isset($userid)) {

            if ($this->getGtcIsApproved($userid) === 1) {

                $response['is_approved'] = 1;
            } else {

                $response['is_approved']     = 0;
                $response['approve_content'] = LongMessage::getMessage('general_terms_and_conditions');
            }
        } else {

            $response['is_approved'] = 0;
            $response['msg']         = Dict::getValue("userIdEmpty");
        }

        echo json_encode($response);
    }

    /**
     * Approve General terms and conditions
     *
     */
    public function actionApproveGtc()
    {
        $userid   = userID();
        $response = [];

        if (isset($userid)) {

            if ($this->getGtcIsApproved($userid) === 0) {

                $model              = new UserGtcLog();
                $model->user_id     = $userid;
                $model->approved_on = date('Y-m-d H:i:s');
				$model->device_type	= UserDeviceLog::categorizeDevice();
                $model->save();

                $response['is_approved'] = 1;
            } else {

                $response['is_approved'] = 0;
                $response['msg']         = Dict::getValue("gtcAlreadyApproved");
            }

        } else {

            $response['is_approved'] = false;
            $response['msg']         = Dict::getValue("userIdEmpty");
        }

        echo json_encode($response);
    }

    /**
     * Get general terms and conditions is approved
     *
     * @param string $userId
     * @return int
     */
    private function getGtcIsApproved(string $userId)
    {
        $model       = new UserGtcLog();
        $result      = $model->findByAttributes(['user_id' => $userId]);
        $is_approved = $result ? 1 : 0;

        return $is_approved;
    }

	private function getRealCardNumber($inCardNumber)
	{
		if (empty($inCardNumber)) {
			return false;
		}
        $inCardNumber = (int)$inCardNumber;
        $maxLengthBinCardNumber = (int)App::getSetting('maxLengthBinCardNumber');


		$binCardNumber = decbin($inCardNumber);

		if (strlen($binCardNumber) > $maxLengthBinCardNumber) {
			$binCardNumber = substr($binCardNumber, -1*$maxLengthBinCardNumber);
		}

		$outCardNumber = bindec($binCardNumber);

		return $outCardNumber;
	}

	private function getCardNumberDecimalFormatFromHex($hex)
	{
		$decimalCardNumber = hexdec($hex);

		return $decimalCardNumber;
	}

    /**
     * Action check card number in DB
     *
     */
	public function actionCheckCardNumberInDb()
	{
		$lang             = userID() ? Dict::getLang() : requestParam('lang', Dict::getLang());
		$card_number      = requestParam('cardNumber');
		$hexFormat        = requestParam("hexFormat");
		$isDecimalFormat  = requestParam("isDecimalFormat");
        $real_card_number = '';
        $stPublished      = Status::PUBLISHED;
        $defaultEnd       = App::getSetting('defaultEnd');
        $response         = [];

		// Check request param 'hexFormat'
        if ($hexFormat == 'true') {
            $hexFormat = true;
        } else {
            $hexFormat = false;
        }

        // Check request param 'isDecimalFormat'
        if ($isDecimalFormat == 'false') {
            $isDecimalFormat = false;
        } else {
            $isDecimalFormat = true;
        }

        // Check card number format
		if (preg_match('/^a_/', $card_number)) {
			$cardNumberParts = explode('a_', $card_number);
			$real_card_number = $cardNumberParts[1];
		} else {
            if ($hexFormat === true && $isDecimalFormat === true) {

                $hex_card_number     = $this->getCardNumberDecimalFormatFromHex($card_number);
                $decimal_card_number = $this->getRealCardNumber($card_number);
                $real_card_number    = "'" . $hex_card_number . "','" . $decimal_card_number . "'";
            } else if ($hexFormat === true && $isDecimalFormat === false) {

                $real_card_number = "'" . $this->getCardNumberDecimalFormatFromHex($card_number) . "'";
            } else if ($hexFormat === false && $isDecimalFormat === true) {

                $real_card_number = "'" . $this->getRealCardNumber($card_number) . "'";
            } else {

                $real_card_number = "'" . $card_number . "'";
            }
		}

		$sql = "
			SELECT
				CONCAT(e.`last_name`,' ',e.`first_name`) as fullname,
				u.`user_id` AS user_id,
			    ecard.`card` AS card
			FROM
			    `employee_card` ecard
			LEFT JOIN
                `employee_contract` ec ON
                ecard.`employee_contract_id` = ec.`employee_contract_id`
				AND ec.`status` = $stPublished
				AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '$defaultEnd')
				AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '$defaultEnd')
			LEFT JOIN
                `employee` e ON
                ec.`employee_id` = e.`employee_id`
				AND e.`status` = $stPublished
				AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '$defaultEnd')
			LEFT JOIN
                `user` u ON
                u.`employee_id` = e.`employee_id`
				AND u.`status` = $stPublished
				AND CURDATE() BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '$defaultEnd')
			WHERE
                ecard.`card` IN ($real_card_number)
				AND ecard.`status` = $stPublished
				AND CURDATE() BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`, '$defaultEnd')
            LIMIT 1
        ";

		$result = dbFetchAll($sql);

		$_SESSION["bypassLDAP"] = false ;
		if (count($result) > 0)
		{
			if ($result[0]['user_id'] !== null) {
				$response['fullname']   = $result[0]['fullname'];
				$response['status']     = 1;
				$_SESSION["bypassLDAP"] = true ;
			} else {
				$response['status'] = 2;
				$response['error']  = Dict::getValueWithLang("missing_user_data", $lang);
			}
		} else {
			$response['status']     = 2;
			$response['error']      = Dict::getValueWithLang('invalid_card_number', $lang);
			$response['cardNumber'] = (int)$real_card_number;
		}

		echo json_encode($response);
	}

	public function actionCheckPinInDb()
	{
		$card_number = requestParam('cardNumber');
		$pin = requestParam('pin');
		$hashedPin = hash('sha512',$pin);
		userID() ? $lang = Dict::getLang() : $lang = requestParam('lang', Dict::getLang());
		$response = [];

		$sql =	"
				SELECT
					`pin`
				FROM
					`employee_card`
				WHERE
						`card` = '$card_number'
					AND `status` = ".Status::PUBLISHED."
					AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '".App::getSetting("defaultEnd")."')
				";

		$result = dbFetchAll($sql);

		if (count($result) > 0) {
			if ($hashedPin === $result[0]['pin']) {
				$response['status'] = 1;
			} else {
				$response['status'] = 2;
				$response['error'] = Dict::getValueWithLang("invalid_pin", $lang);
			}
		} else {
			$response['status'] = 3;
			$response['error'] = Dict::getValueWithLang("not_existing_card", $lang);
		}

		echo json_encode($response);
	}

    /**
     * Get username and password
     *
     */
	public function actionGetUsernameAndPassword()
	{
        $lang             = userID() ? Dict::getLang() : requestParam('lang', Dict::getLang());
        $card_number      = requestParam('cardNumber');
        $hexFormat        = requestParam("hexFormat");
        $isDecimalFormat  = requestParam("isDecimalFormat");
        $real_card_number = '';
        $stPublished      = Status::PUBLISHED;
        $defaultEnd       = App::getSetting('defaultEnd');
        $response         = [];

        // Check request param 'hexFormat'
        if ($hexFormat == 'true') {
            $hexFormat = true;
        } else {
            $hexFormat = false;
        }

        // Check request param 'isDecimalFormat'
        if ($isDecimalFormat == 'false') {
            $isDecimalFormat = false;
        } else {
            $isDecimalFormat = true;
        }

        // Check card number format
        if (preg_match('/^a_/', $card_number)) {
            $cardNumberParts = explode('a_', $card_number);
            $real_card_number = $cardNumberParts[1];
        } else {
            if ($hexFormat === true && $isDecimalFormat === true) {

                $hex_card_number     = $this->getCardNumberDecimalFormatFromHex($card_number);
                $decimal_card_number = $this->getRealCardNumber($card_number);
                $real_card_number    = "'" . $hex_card_number . "','" . $decimal_card_number . "'";
            } else if ($hexFormat === true && $isDecimalFormat === false) {

                $real_card_number = "'" . $this->getCardNumberDecimalFormatFromHex($card_number) . "'";
            } else if ($hexFormat === false && $isDecimalFormat === true) {

                $real_card_number = "'" . $this->getRealCardNumber($card_number) . "'";
            } else {

                $real_card_number = "'" . $card_number . "'";
            }
        }

		$sql =	"
            SELECT
                u.`username`,
                u.`password`
            FROM
                `employee_card` ecard
            LEFT JOIN
                `employee_contract` ec ON
                ecard.`employee_contract_id` = ec.`employee_contract_id`
                AND ec.`status` = $stPublished
                AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '$defaultEnd')
                AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '$defaultEnd')
            LEFT JOIN
                `employee` e ON
                ec.`employee_id` = e.`employee_id`
                AND e.`status` = $stPublished
                AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '$defaultEnd')
            INNER JOIN
                `user` u ON
                e.`employee_id` = u.`employee_id`
                AND u.`status` = $stPublished
                AND CURDATE() BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '$defaultEnd')
            WHERE
                ecard.`card` IN ($real_card_number)
                AND ecard.`status` = $stPublished
                AND CURDATE() BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`, '$defaultEnd')
            LIMIT 1
        ";

		$result = dbFetchAll($sql);

		if (count($result) === 1)
		{
			$response['status'] = 1;
			$response['username'] = $result[0]['username'];
			$response['password'] = $result[0]['password'];
		} else if (count($result) < 1) {
			$response['status'] = 2;
			$response['error'] = Dict::getValueWithLang("missing_user_data", $lang);
		}  else {
			$response['status'] = 3;
			$response['error'] = Dict::getValueWithLang("duplicated_username_belong_to_same_card", $lang);
		}

		echo json_encode($response);
	}

	public function actionGetCPlatformButtonsConfig()
	{
		$navigation = [
		    'flex',
            'valeo',
            'foxconn',
            'legrand',
            'cascade',
            'federal',
            'gnsz',
            'nak',
            'schrack',
            'dreher',
            'mias',
            'masterg',
            'stx',
            'lifefitness',
            'teqball',
            'legrand',
            'bt',
            'fraisa',
            'bemutatotermeloi',
            'vtmetal',
            'sertec',
            'te',
            'hrfest',
            'bemutatocplatformproductivity',
            'easehrdemo',
            'tebekescsaba',
            'praktiker',
            'teesztergom',
            'eisberg',
            'la',
            'bridgestone',
            'kuehnenagel',
            'airbus',
			'sonima',
			'danubius'
        ];
		$lang = requestParam('lang');

		$response = [];
		$userId = userID();

		$sql =	"
				SELECT
					*
				FROM
					`cplatform_button_config`
				";

		$results = dbFetchAll($sql);

		for ($i = 0; $i < count($results); $i++)
		{
			$response[$i]['button_id']   = $results[$i]['button_id'];
			$response[$i]['status']      = $results[$i]['status'];
			$response[$i]['classes']     = $results[$i]['classes'];
			$response[$i]['button_text'] = Dict::getValueWithLang($results[$i]['button_text_dict_id'],$lang);
			$response[$i]['hasRight']    = true;

			if (in_array(App::getSetting('cplatform_client'),$navigation)) {
				$response[$i]['navigation'] = $results[$i]['navigation'];
                $response[$i]['has_return'] = $results[$i]['has_return'];
			}

			if ($results[$i]['controller_id'] !== null && !App::hasRight($results[$i]['controller_id'],"view", null, $userId)) {
				$response[$i]['hasRight'] = false;
			}
		}

		echo json_encode($response);
	}

	public function actionCheckUsernameAndPasswordInDb()
	{
		userID() ? $lang = Dict::getLang() : $lang = requestParam('lang', Dict::getLang());
		$card_number = requestParam('cardNumber');
		$username = requestParam('username');
		$password = requestParam('password');
		$hashedPassword = hash('sha512',$password);

		$sql =	"
				SELECT
					u.`password`,
					u.`username`
				FROM
					`employee_card` ecard
				LEFT JOIN employee_contract ec ON
						ecard.`employee_contract_id` = ec.`employee_contract_id`
					AND ec.`status` = ".Status::PUBLISHED."
					AND CURDATE() BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '".App::getSetting("defaultEnd")."')
					AND CURDATE() BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '".App::getSetting("defaultEnd")."')
				LEFT JOIN employee e ON
						ec.`employee_id` = e.`employee_id`
					AND e.`status` = ".Status::PUBLISHED."
					AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."')
				LEFT JOIN user u ON
						e.`employee_id` = u.`employee_id`
					AND u.`status` = ".Status::PUBLISHED."
					AND CURDATE() BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '".App::getSetting("defaultEnd")."')
				WHERE
						ecard.`card` = '".$card_number."'
					AND ecard.`status` = ".Status::PUBLISHED."
					AND CURDATE() BETWEEN ecard.`valid_from` AND IFNULL(ecard.`valid_to`, '".App::getSetting("defaultEnd")."')
				";

		$result = dbFetchAll($sql);
		$response = [];

		if (count($result) > 0) {
			$username_to_card_number = $result[0]['username'];
			$password_to_card_number = $result[0]['password'];

			if ($username === $username_to_card_number && $hashedPassword === $password_to_card_number) {
				$response['match'] = true;
			} else {
				$response['match'] = false;
				$response['error'] = Dict::getValueWithLang("error_password_incorrect", $lang);
			}
		}

		echo json_encode($response);
	}

	public function actionCheckExistingUserWithRegTable()
	{
		userID() ? $lang = Dict::getLang() : $lang = requestParam('lang', Dict::getLang());
		$realCardNumber = requestParam('cardNumber');

		$existingUser = $this->checkExistingUserWithRegTable($realCardNumber);

		if (!$existingUser)
		{
			$response = [
				'status'	=> 0,
				'error'		=> Dict::getValueWithLang("missing_user_data", $lang)
			];
		}
		else
		{
			if ($existingUser['pin'] === null || $existingUser['pin'] === "")
			{
				$response = [
					'status'	=> 0,
					'error'		=> Dict::getValueWithLang("missingPin", $lang)
				];
			}
			else
			{
				$response = [
					'status'	=> 1
				];
			}
		}

		echo json_encode($response);
	}

	public function actionGetUserDataWithRegTable()
	{
		userID() ? $lang = Dict::getLang() : $lang = requestParam('lang', Dict::getLang());
		$realCardNumber = requestParam('cardNumber');

		$userData = $this->checkExistingUserWithRegTable($realCardNumber);

		if (!$userData)
		{
			$response = [
				'status'	=> 2,
				'error'		=> Dict::getValueWithLang("missing_user_data", $lang)
			];
		}
		else
		{
			$response['status'] = 1;
			$response['username'] = $userData['username'];
			$response['password'] = $userData['password'];
		}

		echo json_encode($response);
	}

	private function checkExistingUserWithRegTable($realCardNumber)
	{
		$publishedStatus = Status::PUBLISHED;
		$defaultEnd = App::getSetting("defaultEnd");

		if (preg_match('/^a_/', $realCardNumber))
		{
			$cardNumberParts = explode('a_', $realCardNumber);
			$realCardNumber = $cardNumberParts[1];
		}

		$SQL = "
				SELECT
					u.`employee_id`,
					u.`user_id`,
					u.`username`,
					u.`password`,
					u.`pin`,
					u.`pin_attempts`,
					u.`pin_date_until`
				FROM registration reg
				LEFT JOIN `employee` e ON
						e.`employee_id` = reg.`card`
					AND e.`status` = {$publishedStatus}
					AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '{$defaultEnd}')
				LEFT JOIN `user` u ON
						u.`employee_id` = e.`employee_id`
					AND u.`status` = {$publishedStatus}
					AND CURDATE() BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$defaultEnd}')
				WHERE
						reg.`real_card_number` = '{$realCardNumber}'
					AND reg.`status` = {$publishedStatus}
					AND reg.`time` > CURDATE() - INTERVAL 30 DAY
				ORDER BY reg.`time` DESC
				LIMIT 1
		";

		return dbFetchRow($SQL);
	}

	public function actionChangePassword()
	{
		$previous_password = requestParam('previous_password');
		$new_password = requestParam('new_password');
		$new_password_retype = requestParam('new_password_retype');
		$username = requestParam('username');
		$userId = userID();
		$userId ? $lang = Dict::getLang() : $lang = requestParam('lang', Dict::getLang());

		$user = $this->getUser($userId, $username);
		$message = '';
		$error	 = false;

		$response = [];

		$correct_previous_password = false;
		$correct_new_password = false;

		if ($previous_password !== '' && $new_password != '' && $new_password_retype != '')
		{
			$hashed_previous_password = hash('sha512',$previous_password);

			if ($this->checkUserPasswordInDb($userId, $username, $hashed_previous_password)) {
				$correct_previous_password = true;

				if (preg_match("/^\S*(?=\S{8,})(?=\S*[a-záéíóöőúüű])(?=\S*[A-ZÁÉÍÓÖŐÚÜŰ])(?=\S*[\d])\S*$/",$new_password))
				{
					if ($previous_password !== $new_password) {
						if ($new_password === $new_password_retype) {
							$correct_new_password = true;
						} else {
							$error = true;
							$message = Dict::getValueWithLang("error_password_doesnt_match", $lang);
						}
					} else {
						$error = true;
						$message = Dict::getValueWithLang("error_old_new_password_must_be_different", $lang);
					}
				} else {
					$error = true;
					$message = Dict::getValueWithLang("error_password_all_security_conditions", $lang, ["minLength"=>8]);
				}
			} else {
				$error = true;
				$message = Dict::getValueWithLang("error_prev_password_incorrect", $lang);
			}
		} else {
			$error = true;
			$message = Dict::getValueWithLang("all_fields_required", $lang);
		}

		if (!$error && $correct_previous_password && $correct_new_password)
		{
			$user->password = hash('sha512', $new_password);
			$user->password_date = date("Y-m-d H:i:s");

			if ($user->save()) {
				$message = Dict::getValueWithLang("successful_password_change", $lang);
			} else {
				$error = true;
				$message = Dict::getValueWithLang("unsuccessful_password_change", $lang);
			}
		}

		$response['message'] = $message;
		$response['status'] = (!$error) ? 1 : 2;

		echo json_encode($response);
	}

	private function getUser($userId, $username)
	{
        $statusPublished     = Status::STATUS_PUBLISHED;
		$model_user			 = new User;
		$criteria			 = new CDbCriteria();
		$criteria->condition = ($username !== null && $username !== "") ? "`username` = '{$username}'" : "`user_id` = '{$userId}'";
        $criteria->condition .= "AND `status` = '{$statusPublished}'";
		$user				 = $model_user->findAll($criteria);

		return $user[0];
	}

	private function checkUserPasswordInDb($userId, $username, $password)
	{
		$userCondition = ($username !== null && $username !== "") ? "`username` = '{$username}'" : "`user_id` = '{$userId}'";

		$SQL = "
				SELECT
					row_id
				FROM `user`
				WHERE
						{$userCondition}
					AND `password` = '$password'
					AND `status` = ".Status::PUBLISHED."
				ORDER BY `password_date`
				LIMIT 1
				";

		$result = dbFetchAll($SQL);

		if (count($result) > 0) {
			return true;
		} else {
			return false;
		}
	}

	public function actionGetNavigationUrl()
	{
		$button_id = requestParam('button_id');
		$response = [];
		userID() ? $lang = Dict::getLang() : $lang = requestParam('lang', Dict::getLang());

		$sql =	"SELECT `navigation_url` FROM `cplatform_button_config` WHERE `button_id` = '$button_id'";

		$result = $result = dbFetchAll($sql);

		if ($result > 0) {
			$response['status'] = 1;
			$response['url'] = $result[0]['navigation_url'];
		} else {
			$response['status'] = 2;
			$response['error'] = Dict::getValueWithLang("missing_navigation_url", $lang);
		}

		echo json_encode($response);
	}

	public function actionGetEmployeeData()
	{
		$employeeData = [];
		$employeeId = $this->getEmployeeIdByUserId();

		if (strlen($employeeId) > 0)
		{
			$currentEmployeeData = new GetActiveEmployeeData_WithoutCalendar(
				["where" => "`employee`.`employee_id` = '{$employeeId}'"],
				[],
				['employee', 'employee_contract', 'employee_address', 'employee_salary', 'employee_ext', 'employee_ext2',
				'company','payroll','unit', 'workgroup', 'cost', 'cost_center',
				'company_org_group1', 'company_org_group2', 'company_org_group3',
				'employee_position', 'user'],
				"`employee`.`employee_id`, employee_contract.`employee_contract_id`",
				true
			);

			$tempTable = $currentEmployeeData->createTempTable();

			$SQL = "
					SELECT
						CONCAT(all_data.`last_name`,' ',all_data.`first_name`)	as name,
						all_data.`emp_id`										as emp_id,
						all_data.`company_name`									as Company,
						all_data.`workgroup_name`								as workgroup,
						all_data.`cost_name`									as cost,
						all_data.`cost_center_name`								as costcenter,
						all_data.`company_org_group1_name`						as company_org_group1,
						all_data.`company_org_group2_name`						as company_org_group2,
						all_data.`company_org_group3_name`						as company_org_group3,
						all_data.`ec_valid_from`								as ec_valid_from,
						all_data.`employee_valid_from`							as valid_from,
						all_data.`tax_number`									as tax_number,
						all_data.`employee_position_name`						as employee_position_name,
						all_data.`daily_worktime`								as daily_worktime,
						all_data.`email`										as email,
						all_data.`address_card_number`							as address_card_number,
						all_data.`full_address`									as full_address,
						all_data.`zip_code`										as zip_code,
						all_data.`city`											as city,
						all_data.`district`										as district,
						all_data.`public_place_name`							as public_place_name,
						all_data.`public_place_type`							as public_place_type,
						all_data.`house_number`									as house_number,
						all_data.`floor`										as floor,
						all_data.`door`											as door,
						all_data.`res_full_address`								as res_full_address,
						all_data.`res_zip_code`									as res_zip_code,
						all_data.`res_city`										as res_city,
						all_data.`res_district`									as res_district,
						all_data.`res_public_place_name`						as res_public_place_name,
						all_data.`res_public_place_type`						as res_public_place_type,
						all_data.`res_house_number`								as res_house_number,
						all_data.`res_floor`									as res_floor,
						all_data.`res_door`										as res_door,
						all_data.`place_of_birth`								as place_of_birth,
						all_data.`date_of_birth`								as date_of_birth,
						all_data.`mothers_name`									as mothers_name,
						all_data.`ssn`											as ssn,
						all_data.`personal_id_card_number`						as personal_id_card_number,
						all_data.`passport_number`								as passport_number,
						all_data.`option1`										as option1,
						all_data.`option2`										as option2,
						all_data.`option3`										as option3,
						all_data.`option4`										as option4,
						all_data.`option5`										as option5,
						all_data.`option6`										as option6,
						all_data.`option7`										as option7,
						all_data.`option8`										as option8,
						all_data.`option9`										as option9,
						all_data.`option10`										as option10,
						all_data.`es_option1`									as es_option1
					FROM $tempTable all_data
					";

			$results = dbFetchAll($SQL);

			if (count($results) > 0) {
				foreach ($results[0] as $key => $value)
				{
					if ($value !== null) {
						$dataType = Dict::getValue($key);
						$employeeData[$dataType] = $value;
					}
				}

				$employeeData["personalDataTitle"] = Dict::getValue("cplatform_gdpr");
			}
		}

		echo json_encode($employeeData);

	}

	private function getEmployeeIdByUserId()
	{
		$employeeId = "";
		$userId = userID();

		$SQL = "
				SELECT
					e.`employee_id` as employee_id
				FROM `user` u
				LEFT JOIN `employee` e ON
						u.`employee_id` = e.`employee_id`
					AND e.`status` = " . Status::PUBLISHED . "
					AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."')
				WHERE
						u.`status` = " . Status::PUBLISHED . "
					AND CURDATE() BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '".App::getSetting("defaultEnd")."')
					AND u.`user_id` = '{$userId}'
				";

		$result = dbFetchAll($SQL);

		if (count($result) > 0) {
			$employeeId = $result[0]["employee_id"];
		}

		return $employeeId;
	}

	public function actionGetPersonalData()
	{
		$employee_id = requestParam('employee_id');
		$lang = requestParam('lang');

		$html = '<div class="gdpr">
					<table class="gdpr_table">
						<thead>
							<tr>
								<th>Kezelt adat kategóriája</th>
								<th>Kezelt adat</th>
								<th>Adatkezelés jogalapja</th>
								<th>Adatkezelés időtartama</th>
								<th>Hozzájárulás</th>
								<th>Visszavonás</th>
							</tr>
						</thead>';

		$SQL = "
				SELECT
					gpdc.`row_id` as row_id,
					gpdc.`personal_data_category_name` as category,
					gpd.`personal_data_value` as data,
					glb.`legal_basis_name` as legal_basis,
					gpd.`duration_of_data_management` as duration,
					gpd.`allowed` as allowed,
					gpd.`cancelled` as cancelled,
					glb.`allowance` as allowance,
					glb.`cancellation` as cancellation
				FROM `gdpr_personal_data` gpd
				LEFT JOIN `gdpr_personal_data_category` gpdc ON
						gpd.`personal_data_category_id` = gpdc.`personal_data_category_id`
					AND gpdc.`status` = ".Status::PUBLISHED."
				LEFT JOIN `gdpr_legal_basis` glb ON
						gpd.`legal_basis_id` = glb.`legal_basis_id`
					AND gpd.`status` = ".Status::PUBLISHED."
				WHERE
						gpd.`employee_id` = '$employee_id'
					AND gpd.`status` = ".Status::PUBLISHED."
				ORDER BY gpdc.`personal_data_category_id`
				";

		$results = dbFetchAll($SQL);

		$response = [];

		if (is_array($results) && count($results) > 0) {

			for ($i = 0; $i < count($results); $i++)
			{
				$html .=	"
							<tr value='".$results[$i]['row_id']."'>
								<td>".$results[$i]['category']."</td>
								<td class='editable'>".$results[$i]['data']."</td>
								<td>".$results[$i]['legal_basis']."</td>
								<td>".$results[$i]['duration']."</td>";

				if ($results[$i]['allowance']) {
					$html .= ($results[$i]['allowed']) ?
							"<td class='buttonContainer'>
								<div class='allowedButton gdprButtons'>".Dict::getValueWithLang('gdpr_allowed',$lang)."</div>
							</td>
							<td class='buttonContainer'>
								<div class='toCancelButton gdprButtons'>".Dict::getValueWithLang('gdpr_cancel',$lang)."</div>
							</td>" :
							"<td class='buttonContainer'>
								<div class='toAllowButton gdprButtons'>".Dict::getValueWithLang('gdpr_allow',$lang)."</div>
							</td>
							<td></td>";
				} else {
					$html .= "<td></td><td></td>";
				}

				$html .=	"
							</tr>
							";
			}

			$html .=		'</table>
						</div>';

			$response['html'] = $html;
			$response['status'] = 1;
		} else {
			$response['status'] = 2;
		}

		echo json_encode($response);
	}

	public function actionUpdatePersonalData()
	{
		$new_data = requestParam('new_data');
		$row_id = requestParam('row_id');
		$data_type = requestParam('data_type');

		$gpd = new GdprPersonalData;

		$currentRow = $gpd->model()->findByPk($row_id);
		$currentRow->$data_type = $new_data;

		if ($data_type === 'cancelled') {
			$currentRow->allowed = 0;
		} else if ($data_type === 'allowed') {
			$currentRow->cancelled = 0;
		}

		$currentRow->save();

		$response = [
					"status"		=> 1,
					"new_data"		=> $new_data
					];

		echo json_encode($response);
	}

	private function getEmployeeNameByUserId($user_id)
	{
		$fullname = '';

		$sql = "
				SELECT
					CONCAT(e.`last_name`,' ',e.`first_name`) as fullname
				FROM `user` u
				LEFT JOIN employee e ON
						u.`employee_id` = e.`employee_id`
					AND e.`status` = ".Status::PUBLISHED."
					AND CURDATE() BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '".App::getSetting("defaultEnd")."')
				WHERE
						u.`user_id` = '$user_id'
					AND u.`status` = ".Status::PUBLISHED."
					AND CURDATE() BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '".App::getSetting("defaultEnd")."')
				LIMIT 1
				";

		$result = dbFetchAll($sql);

		if (count($result) > 0) {
			$fullname = $result[0]['fullname'];
		} else {
			$fullname = null;
		}

		return $fullname;
	}

    private function getUserNameByUserId($user_id)
    {
        $fullname = '';

        $sql = "
				SELECT
					u.`username` as username
				FROM
				    `user` u
				WHERE
						u.`user_id` = '$user_id'
					AND u.`status` = ".Status::PUBLISHED."
					AND CURDATE() BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '".App::getSetting("defaultEnd")."')
				LIMIT 1
				";

        $result = dbFetchAll($sql);

        if (count($result) > 0) {
            $username = $result[0]['username'];
        } else {
            $username = null;
        }

        return $username;
    }

	public function actionSaveEntryOrExitRegistration()
	{
		$buttonId = requestParam('pushedButtonId');
		$cardNumber = requestParam('cardNumber');
		$checkRealCardNumber = (bool) requestParam('checkRealCardNumber');

		$realCardNumber = ($checkRealCardNumber) ? $this->getRealCardNumber($cardNumber) : $cardNumber;

		if (!empty($cardNumber)
            && ($buttonId === "dashbutton1"
            || $buttonId === "dashbutton2"
            || $buttonId === "dashbutton3"
            || $buttonId === "dashbutton4"
            || $buttonId === "dashbutton5"
            || $buttonId === "dashbutton6"
            )) {

		    $event_type_id = "NMB";

		    switch ($buttonId) {

                case "dashbutton1":
                    $event_type_id = "NMB";
                    break;
                case "dashbutton2":
                    $event_type_id = "NMK";
                    break;
                case "dashbutton3":
                    $event_type_id = "KMK";
                    break;
                case "dashbutton4":
                    $event_type_id = "MGK";
                    break;
                case "dashbutton5":
                    $event_type_id = "EGK";
                    break;
                case "dashbutton6":
                    $event_type_id = "EBK";
                    break;
                default:
                    $event_type_id = "NMB";
                    break;
            }

			$new_reg                = new Registration;
			$new_reg->terminal_id   = "rk_001";
            $new_reg->card          = $realCardNumber;
			$new_reg->time          = date("Y-m-d H:i:s");
			$new_reg->event_type_id = $event_type_id;
			$new_reg->status        = Status::PUBLISHED;
			$new_reg->created_by    = "kiosk";
			$new_reg->created_on    = date("Y-m-d H:i:s");

			if ($new_reg->validate()) {
				$new_reg->save();

				$response['status'] = 1;
				$response['message'] = Dict::getModuleValue("ttwa-kiosk","successful_registration");
			} else {
				$response['status'] = 2;
				$response['message'] = Dict::getModuleValue("ttwa-kiosk","failed_registration");
			}
		} else {
			$response['status'] = 2;
			$response['message'] = Dict::getModuleValue("ttwa-kiosk","failed_registration");
		}

		echo json_encode($response);
	}

	public function actionGetCardNumberByUserId()
	{
		$userId = userID();
		$userId ? $lang = Dict::getLang() : $lang = requestParam('lang', Dict::getLang());

		$sql =	"
			SELECT
				ecard.card
			FROM
				user
			LEFT JOIN employee e ON
					user.employee_id = e.employee_id
				AND e.status = ".Status::PUBLISHED."
				AND CURDATE() BETWEEN e.valid_from AND IFNULL(e.valid_to, '".App::getSetting("defaultEnd")."')
			LEFT JOIN employee_contract ec ON
					e.employee_id = ec.employee_id
				AND ec.status = ".Status::PUBLISHED."
				AND CURDATE() BETWEEN ec.valid_from AND IFNULL(ec.valid_to, '".App::getSetting("defaultEnd")."')
				AND CURDATE() BETWEEN ec.ec_valid_from AND IFNULL(ec.ec_valid_to, '".App::getSetting("defaultEnd")."')
			LEFT JOIN employee_card ecard ON
					ecard.employee_contract_id = ec.employee_contract_id
				AND ecard.status = ".Status::PUBLISHED."
				AND CURDATE() BETWEEN ecard.valid_from AND IFNULL(ecard.valid_to, '".App::getSetting("defaultEnd")."')
			WHERE
					user.user_id = '".$userId."'
				AND user.status = ".Status::PUBLISHED."
				AND CURDATE() BETWEEN user.valid_from AND IFNULL(user.valid_to, '".App::getSetting("defaultEnd")."')
		";

		$result = dbFetchValue($sql);

		if (!empty($result)) {
			$response['status'] = 1;
			$response['cardNumber'] = $result;
		} else {
			$response['status'] = 2;
			$response['message'] = Dict::getValueWithLang("error_no_employee_card", $lang);
		}

		echo json_encode($response);
	}

	public function getDictionaryTexts()
	{
		if (userID()) {
			$lang = Dict::getLang();
		} else {
			$lang = requestParam('lang', Dict::getLang());
		}

        if ((int)requestParam('lang_reload') === 1 && !empty(requestParam('lang'))) {
            $lang = requestParam('lang');
        }

		$result = [
            "mobileApp"					=> Dict::getValueWithLang("mobile_app", $lang),
			"withCard"					=> Dict::getValueWithLang("with_card", $lang),
			"withPassword"				=> Dict::getValueWithLang("with_password", $lang),
			"chooseAuthenticationMode"	=> Dict::getValueWithLang("choose_authentication_mode", $lang),
			"username"					=> Dict::getValueWithLang("username", $lang),
			"password"					=> Dict::getValueWithLang("password", $lang),
			"login"						=> Dict::getValueWithLang("cplatform_login", $lang),
			"loginForAuthentication"	=> Dict::getValueWithLang("login_for_authentication", $lang),
			"scanCard"					=> Dict::getValueWithLang("scan_card", $lang),
			"submit"					=> Dict::getValueWithLang("cplatform_submit", $lang),
			"typePin"					=> Dict::getValueWithLang("type_pin", $lang),
			"welcome"					=> Dict::getValueWithLang("cplatform_welcome", $lang),
			"changePassword"			=> Dict::getValueWithLang("cplatform_change_password", $lang),
			"prevPassword"				=> Dict::getValueWithLang("prev_password", $lang),
			"newPassword"				=> Dict::getValueWithLang("new_password", $lang),
			"newPasswordAgain"			=> Dict::getValueWithLang("new_password_again", $lang),
			"save"						=> Dict::getValueWithLang("save", $lang),
			"checkPin"					=> Dict::getValueWithLang("checkPin", $lang),
			"changePin"					=> Dict::getValueWithLang("changePin", $lang),
			"currentPin"				=> Dict::getValueWithLang("currentPin", $lang),
			"newPin"					=> Dict::getValueWithLang("newPin", $lang),
			"newPinAgain"				=> Dict::getValueWithLang("newPinAgain", $lang),
			"savePin"					=> Dict::getValueWithLang("save", $lang),
			"setPin"					=> Dict::getValueWithLang("setPin", $lang),
            "approveGtc"	    		=> Dict::getValueWithLang("approveGtc", $lang),
            "shop"                      => Dict::getValueWithLang("cplatform_shop", $lang),
            "discount"                  => Dict::getValueWithLang("cplatform_discount", $lang),
			"oktaurl"					=> baseURL().'/login/login'
		];

		return $result;
	}

	public function actionCheckPin()
	{
		$cardNumber = $_SESSION['demo']['cardNumber'];
		$currentPin = requestParam('currentPin');
		$user = $this->checkExistingUserWithRegTable($cardNumber);
		$userId = $user['user_id'];
		$usersPin = $user['pin'];
		$pinAttempts = $user['pin_attempts'];
		$pinDate = $user['pin_date_until'];
		$response = [ 'status' => 1 ];
		$pattern = '/^[0-9]{6}$/';

		if (empty($cardNumber))
		{
			$response = [
				'status' => 0,
				'message' => Dict::getValue('use_your_card')
			];

			echo json_encode($response);
			return;
		}

		if (!preg_match($pattern, $currentPin))
		{
			$response = [
				'status' => 0,
				'message' => Dict::getValue('error_pin')
			];

			echo json_encode($response);
			return;
		}

		if ($usersPin !== null)
		{
			if ($pinAttempts == self::$maxPinAttempts)
			{
				$response = [
					'status' => 0,
					'message' => Dict::getValue('error_blocked_card')
				];

				echo json_encode($response);
				return;
			}

			if ($usersPin !== hash('sha512', $currentPin))
			{
				$pinAttempts = $this->incrementPinAttempts($userId);
				$response = [
					'status' => 0,
					'message' => Dict::getValue('error_current_pin_2', [ 'pinAttempts' => (self::$maxPinAttempts) - $pinAttempts ])
				];

				if ($pinAttempts === 5)
				{
					$this->blockPin($userId);

					$response = [
						'status' => 0,
						'message' => Dict::getValue('error_blocked_card')
					];
				}

				echo json_encode($response);
				return;
			}

			$this->setPinAttempts(0, $userId);
		}

		$employeeName = $this->getEmployeeNameByUserId($userId);
		$response = [
			'status' => 1,
			'fullname' => $employeeName
		];

		echo json_encode($response);
	}

	public function actionHasPin()
	{
		$cardNumber = $_SESSION['demo']['cardNumber'];
		$user = $this->checkExistingUserWithRegTable($cardNumber);
		$response = [ 'status' => 1 ];

		if (!empty($user))
		{
			if ($user['pin'] === null)
			{
				$response = [ 'status' => 0 ];
			}
		}

		echo json_encode($response);
	}

	public function actionSetPin()
	{
		$statusPublished = Status::STATUS_PUBLISHED;
		$this->defaultEnd = App::getSetting('defaultEnd');
		$userId = userID();
		$newPin = requestParam('newPin');
		$confirmedPin = requestParam('confirmedPin');
		$pinPattern = '/^[0-9]{6}$/';

		if (!preg_match($pinPattern, $newPin) || !preg_match($pinPattern, $confirmedPin))
		{
			$response = [
				'status' => 2,
				'message' => Dict::getValue('error_pin')
			];

			echo json_encode($response);
			return;
		}

		if ($newPin !== $confirmedPin)
		{
			$response = [
				'status' => 2,
				'message' => Dict::getValue('error_pin_match')
			];

			echo json_encode($response);
			return;
		}

		$this->setUsersPin(hash('sha512', $newPin), $userId);

		$response = [
			'status' => 1,
			'message' => Dict::getValue('set_pin_succeeded')
		];

		echo json_encode($response);
	}

    /**
     * Check discount condition action
     *
     * @return void
     */
    public function actionCheckCondition()
    {
        $userId             = userID();
        $lang               = $userId ? Dict::getLang() : requestParam('lang', Dict::getLang());
        $employeeContractId = $this->getEmployeeContractId($userId);
        $check              = $this->checkDiscount($employeeContractId);
        $response           = [];

        try {

            // Check parameters
            if ($employeeContractId === NULL) {
                throw new Exception(Dict::getValueWithLang("errorNoEmployeeContractId", $lang));
            }

            if ($check == false) {
                throw new Exception(Dict::getValueWithLang("caseOfProblemTurnHR", $lang));
            }

            // Render
            $response['check']           = $check;
            $response['status']          = 1;
            $response['discount_amount'] = $check == true ? App::getSetting('discountAmount') . '%' : '0%';
        } catch (Exception $ex) {
            $response['status']          = 2;
            $response['discount_amount'] = '0%';
            $response['error']           = $ex->getMessage();
        }

        echo json_encode($response);
    }

	public function actionChangePin()
	{
		$userId = userID();
		$usersPin = $this->getUsersPin($userId);
		$currentPin = requestParam('currentPin');
		$newPin = requestParam('newPin');
		$confirmedPin = requestParam('confirmedPin');
		$pinPattern = '/^[0-9]{6}$/';

		if (
			!preg_match($pinPattern, $currentPin) ||
			!preg_match($pinPattern, $newPin) ||
			!preg_match($pinPattern, $confirmedPin)
		)
		{
			$response = [
				'status' => 0,
				'message' => Dict::getValue('error_pin')
			];

			echo json_encode($response);
			return;
		}

		if ($newPin !== $confirmedPin)
		{
			$response = [
				'status' => 0,
				'message' => Dict::getValue('error_pin_match')
			];

			echo json_encode($response);
			return;
		}

		if ($usersPin !== null)
		{
			if ($usersPin !== hash('sha512', $currentPin))
			{
				$response = [
					'status' => 0,
					'message' => Dict::getValue('error_current_pin')
				];

				echo json_encode($response);
				return;
			}
		}

		$this->setUsersPin(hash('sha512', $newPin), $userId);
		$response = [ 'status' => 1, 'message' => Dict::getValue('set_pin_succeeded') ];

		echo json_encode($response);
	}

	private function getUserByCard($cardNumber)
	{
		$statusPublished = Status::STATUS_PUBLISHED;
		$defaultEnd = App::getSetting('defaultEnd');
		$sql = "SELECT
				`user`.`user_id`,
				`user`.`pin`,
				`user`.`pin_attempts`,
				`user`.`pin_date_until`

			FROM
				`employee_card`

			LEFT JOIN `employee_contract` ON `employee_contract`.`employee_contract_id` = `employee_card`.`employee_contract_id`
				AND `employee_contract`.`status` = '$statusPublished'
				AND CURDATE() BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '$defaultEnd')
				AND CURDATE() BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '$defaultEnd')

			LEFT JOIN `user` ON `user`.`employee_id` = `employee_contract`.`employee_id`
				AND `user`.`status` = '$statusPublished'
				AND CURDATE() BETWEEN `user`.`valid_from` AND IFNULL(`user`.`valid_to`, '$defaultEnd')

			WHERE `employee_card`.`card` = '$cardNumber'
				AND `employee_card`.`status` = '$statusPublished'
				AND CURDATE() BETWEEN `employee_card`.`valid_from` AND IFNULL(`employee_card`.`valid_to`, '$defaultEnd')
		";

		return dbFetchRow($sql);
	}

	private function getUsersPin($userId)
	{
		$statusPublished = Status::STATUS_PUBLISHED;
		$defaultEnd = App::getSetting('defaultEnd');
		$SQL = "
				SELECT
					`pin`
				FROM `user`
				WHERE
						`user_id` = '{$userId}'
					AND `status` = '{$statusPublished}'
					AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$defaultEnd}')
				";

		return dbFetchValue($SQL);
	}

	private function setUsersPin($pin, $userId)
	{
		$date = date('Y-m-d H:i:s');
		$statusPublished = Status::STATUS_PUBLISHED;
		$defaultEnd = App::getSetting('defaultEnd');
		$SQL = "
				UPDATE `user`
				SET
					`pin` = '{$pin}',
					`pin_date_until` = '{$date}'
				WHERE
						`user_id` = '{$userId}'
					AND `status` = '{$statusPublished}'
					AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '{$defaultEnd}')
				";

		dbExecute($SQL);
	}

	private function blockPin($userId)
	{
		$sql = "UPDATE `user` SET `pin_date_until` = ADDTIME(`pin_date_until`, '00:30:00') WHERE `user_id` = '$userId' LIMIT 1";
		dbExecute($sql);
	}

	private function setPinAttempts($pinAttempts = 0, $userId = 0)
	{
		$sql = "UPDATE `user` SET `pin_attempts` = '$pinAttempts' WHERE `user_id` = '$userId' LIMIT 1";
		dbExecute($sql);
	}

	private function incrementPinAttempts($userId)
	{
		$statusPublished = Status::STATUS_PUBLISHED;
		$defaultEnd = App::getSetting('defaultEnd');
		$sql = "SELECT
				`user`.`pin_attempts`

			FROM
				`user`

			WHERE
				`user_id` = '$userId'
				AND `status` = '$statusPublished'
				AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, '$defaultEnd')";

		$pinAttempts = dbFetchValue($sql);
		$pinAttempts++;

		$sql = "UPDATE `user` SET `pin_attempts` = '$pinAttempts' WHERE `user_id` = '$userId' LIMIT 1";
		dbExecute($sql);

		return $pinAttempts;
	}

	private function testNetworkConnectivity()
	{
		$randomNumber = $_GET["inetconntest"];
		$hashedRandomNumber = md5($randomNumber);
		$content = "<body>$hashedRandomNumber</body>";
		die ($content) ;
	}

    /**
     * Get employee contract id
     *
     * @param string|null $userId
     * @return string|null
     */
    private function getEmployeeContractId(string $userId = NULL) : ?string
    {
        $result = NULL;

        if ($userId !== null) {
            $sql = "
                SELECT
                    ec.employee_contract_id
                FROM
                    user
                LEFT JOIN employee e ON
                    user.employee_id = e.employee_id
                    AND e.status = " . Status::PUBLISHED . "
                    AND CURDATE() BETWEEN e.valid_from AND IFNULL(e.valid_to, '" . App::getSetting("defaultEnd") . "')
                LEFT JOIN employee_contract ec ON
                    e.employee_id = ec.employee_id
                    AND ec.status = " . Status::PUBLISHED . "
                    AND CURDATE() BETWEEN ec.valid_from AND IFNULL(ec.valid_to, '" . App::getSetting("defaultEnd") . "')
                    AND CURDATE() BETWEEN ec.ec_valid_from AND IFNULL(ec.ec_valid_to, '" . App::getSetting("defaultEnd") . "')
                WHERE
                    user.user_id = '" . $userId . "'
                    AND user.status = " . Status::PUBLISHED . "
                    AND CURDATE() BETWEEN user.valid_from AND IFNULL(user.valid_to, '" . App::getSetting("defaultEnd") . "')
            ";
            $result = dbFetchValue($sql);
        }

        return $result;
    }

    /**
     * Check discount condition
     *
     * @param string|null $employeeContractId
     * @return string|null
     */
    private function checkDiscount(string $employeeContractId = NULL) : ?string
    {
        $result                      = false;
        $current_year_first_today    = " ea.day BETWEEN (MAKEDATE(EXTRACT(YEAR FROM CURDATE()),1)) AND CURDATE()";
        $previous_month_first_last   = " ea.day BETWEEN (LAST_DAY(CURDATE() - INTERVAL 2 MONTH) + INTERVAL 1 DAY) AND (LAST_DAY(CURDATE() - INTERVAL 1 MONTH))";
        $cond_unjustified_absence    = " ea.state_type_id = 'def32968390fb987c823da0cbf7d3bd8'";
        $cond_justified_but_not_paid = " ea.state_type_id = '269b59b4efbbb4ef1d527492dc06cb60'";
        $cond_sick_leave             = "
         ea.state_type_id IN (
            '11d5ce859c686c0dc3c9727d19cfbf5f',
            '12dd1237b950bfa8ece94c120b5d4f8f',
            '5b70cb93b84b05e1803b7825c55c8be1',
            '645929a42799fe05e27801ca605624ca',
            '7477f10be8bf995d7a48c104f1d8fe1c',
            '7b3cf78f9fd2a404567fe572fcb0eaf9',
            '879e1d423b540a25f8c152948bb5066a',
            '8f6615bc77b4099011f7e9e5cbce6023',
            'a8fd428dd18c27257aceb1629c75d637',
            'ab518692163bdcc355dace7d919a47b5',
            'bac18782b00e0921f91817826d517b70'
        )";

        if ($employeeContractId !== null) {

            $sql_unjustified_absence = "
                SELECT
                    IF(COUNT(ea.row_id) > 0, 'FALSE', 'TRUE') AS unjustified_absence
                FROM
                    employee_absence ea
                WHERE
                    ea.employee_contract_id = " . $employeeContractId . "
                    AND ea.status = " . Status::PUBLISHED . "
                    AND " . $cond_unjustified_absence . "
                    AND " . $previous_month_first_last ."
            ";
            $unjustified_absence = dbFetchValue($sql_unjustified_absence);

            $sql_no_paid_absence = "
                SELECT
                    IF(COUNT(ea.row_id) > 0, 'FALSE', 'TRUE') AS justified_but_not_paid
                FROM
                    employee_absence ea
                WHERE
                    ea.employee_contract_id = " . $employeeContractId . "
                    AND ea.status = " . Status::PUBLISHED . "
                    AND " . $cond_justified_but_not_paid . "
                    AND " . $previous_month_first_last ."
            ";
            $no_paid_absence = dbFetchValue($sql_no_paid_absence);

            $sql_sick_pay_5day = "
                SELECT
                    IF(COUNT(ea.row_id) > 5, 'FALSE', 'TRUE') AS sick_leave
                FROM
                    employee_absence ea
                WHERE
                    ea.employee_contract_id = " . $employeeContractId . "
                    AND ea.status = " . Status::PUBLISHED . "
                    AND " . $cond_sick_leave . "
                    AND " . $previous_month_first_last ."
            ";
            $sick_pay_5day = dbFetchValue($sql_sick_pay_5day);

            $sql_sick_pay_30day = "
                SELECT
                    IF(COUNT(ea.row_id) > 30, 'FALSE', 'TRUE') AS sick_leave
                FROM
                    employee_absence ea
                WHERE
                    ea.employee_contract_id = " . $employeeContractId . "
                    AND ea.status = " . Status::PUBLISHED . "
                    AND " . $cond_sick_leave . "
                    AND " . $current_year_first_today ."
            ";
            $sick_pay_30day = dbFetchValue($sql_sick_pay_30day);

            if ($unjustified_absence === 'TRUE' && $no_paid_absence === 'TRUE' && $sick_pay_5day === 'TRUE' && $sick_pay_30day == 'TRUE') {
                $result = true;
            }
        }

        return $result;
    }

}
