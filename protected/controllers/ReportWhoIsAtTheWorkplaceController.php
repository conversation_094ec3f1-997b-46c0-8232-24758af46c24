<?php

class ReportWhoIsAtTheWorkplaceController extends Grid2Controller
{
    private $defaultEnd;
    private $published	= Status::PUBLISHED;
    private $linkGroupToTerminalRelatedId;
    private $usedTerminalsAndReaders = [];
    private $date;
    private $yesterday;
    private $dateTime;

    public function __construct()
    {
        parent::__construct("reportWhoIsAtTheWorkplace");
        $this->defaultEnd	= App::getSetting("defaultEnd");
        $this->linkGroupToTerminalRelatedId = App::getSetting('link_group_to_terminal_related_id');
        $this->usedTerminalsAndReaders = $this->usedTerminals();
        $this->date = date('Y-m-d');
        $this->yesterday = date('Y-m-d', strtotime(' -1 day'));
        $this->dateTime = Yang::session('datetime');
    }

    public function actionIndex($layout = '//Grid2/layouts/indexLayout', $view = '/Grid2/index', $params = array())
    {
       $path = Yang::addAsset(Yang::getAlias('application.assets.base'), false, -1, true);

        Yang::registerScriptFile($path . '/reportLoadGridCallBack/load.js');
        Yang::setSessionValue('datetime', date('Y-m-d H:i:s'));
        parent::actionIndex($layout, $view, $params);
    }

    public function actionGenStatusBar()
    {
        echo Dict::getValue("page_title_report_who_is_at_the_workplace") . " - " . $this->dateTime;
    }

    public function filters()
    {
        return [
            'accessControl',
        ];
    }

    public function accessRules()
    {
        return [
            ['allow',
                'users' => ['@'],
            ],
            ['deny',
                'users' => ['*'],
            ],
        ];
    }

    public function search()
    {
        $originalSearch = $this->getPreDefinedSearchFromDb();

        $this->setMultifilter($originalSearch, 'workgroup');
        $this->setMultifilter($originalSearch, 'unit');
		if(isset($originalSearch['company_org_group1']))
		{
			$this->setMultifilter($originalSearch, 'company_org_group1');
		}
		if(isset($originalSearch['company_org_group2']))
		{
			$this->setMultifilter($originalSearch, 'company_org_group2');
		}
		if(isset($originalSearch['company_org_group3']))
		{
			$this->setMultifilter($originalSearch, 'company_org_group3');
		}
        $originalSearch['submit']['col_type'] = 'searchBarReloadGrid';

        return $originalSearch;
    }

    public function setMultifilter(&$originalSearch, $searchFilter)
    {
        $originalSearch[$searchFilter]['multiple']	= 1;
        $originalSearch[$searchFilter]['col_type']	= 'combo';
        $originalSearch[$searchFilter]['class']		= 'customSelect2Class';
    }

    public function columns()
    {
        $column = [
            'fullname'			=> ['grid' => true, 'export' => true, 'col_type' => 'ro', 'width' => '200'],
            'emp_id'			=> ['grid' => true, 'export' => true, 'col_type' => 'ro', 'width' => '200'],
            'unit_name'			=> ['grid' => true, 'export' => true, 'col_type' => 'ro', 'width' => '200'],
            'is_attendance'		=> ['grid' => true, 'export' => true, 'col_type' => 'ro', 'width' => '200'],
            'date'				=> ['grid' => true, 'export' => true, 'col_type' => 'ro', 'width' => '200'],
            'sort'   		    => ['grid' => false, 'export' => false, 'col_type' => 'ro', 'width' => '0'],
        ];
        return $column;
    }

    public function attributeLabels()
    {
        return [
            'emp_id'		=> Dict::getValue('emp_id'),
            'fullname'		=> Dict::getValue('fullname'),
            'unit_name'		=> Dict::getValue('unit_name'),
            'is_attendance'	=> Dict::getValue('is_attendance'),
            'date'			=> Dict::getValue('date'),
        ];
    }

    protected function G2BInit()
    {
        parent::setControllerPageTitleId("page_title_report_who_is_at_the_workplace");
        parent::setExportFileName(genIdFromString(Dict::getValue("page_title_report_who_is_at_the_workplace")));

        $this->LAGridRights->overrideInitRightSwitches('

			[x]  paging
			[x]  search
			[x]  search header
			[x]  select
			[x]  col sorting
			[x]  reload sortings
			[x]  init open search
			[x]  export xlsx
			[ ]  export csv
			[ ]  export xls
			[ ]  details
			[ ]  multi select

		');

        $this->enableLAGrid();
        $this->LAGridDB->enableArrMode();
        $this->LAGridDB->setPrimaryKey("emp_id", "dthmxGrid");
        parent::G2BInit();
    }

    protected function dataArray($gridID, $filter)
    {
        $filter['valid_date'] = $this->date;
        $gea =new GetActiveEmployeeData(
            $filter,
            [],
            ['employee', 'employee_contract', 'unit', 'workgroup', 'company_org_group1', 'company_org_group2', 'company_org_group3'],
            "cal.`date`, employee.`employee_id`, employee_contract.`employee_contract_id`",
            $this->getControllerID()
        );
        $where = $this->getSqlFilter($filter);
        $employeeSql = preg_replace("/WHERE/", "WHERE {$where} AND ", $gea->getSQL());
        $employeeSql .= 'ORDER BY employee.last_name, employee.first_name';
        $employeeData = dbFetchAll($employeeSql, 'employee_contract_id');
        $ecIds = array_keys($employeeData);
        $regs = $this->getRegistrations($ecIds);

        $retArr = [];
        foreach ($employeeData as $ecId => $employee) {
            $lastRegs = $this->getLastRegs($regs[$ecId] ?? [], $employee[$this->linkGroupToTerminalRelatedId]);

            $retArr[] = [
                'sort'			            => $lastRegs['sort'],
                'fullname_sort'				=> genIdFromString($employee['last_name'] . ' ' . $employee['first_name']),
                'fullname'					=> $employee['last_name'] . ' ' . $employee['first_name'],
                'emp_id'					=> $employee['emp_id'],
                'unit_name'					=> $employee['unit_name'],
                'is_attendance'			    => $lastRegs['in'],
                'date'			            => $this->date
            ];
        }

        asort($retArr);
        return $retArr;
    }

    protected function getSqlFilter($filter)
    {
        $gpf = new GetPreDefinedFilter(
            "wfm/reportAttendance",
            \FALSE,
            ['company' => 'employee',
                'payroll' => 'employee',
                ],
            \FALSE,
            $filter
        );
        $SqlFilter =$gpf->getFilter();
        $SqlFilter .= $this->getFilterByGroup($filter, 'unit', 'unit');
        $SqlFilter .= $this->getFilterByGroup($filter, 'workgroup', 'workgroup');
        $SqlFilter .= $this->getFilterByGroup($filter, 'company_org_group1', 'company_org_group');
        $SqlFilter .= $this->getFilterByGroup($filter, 'company_org_group2', 'company_org_group');
        $SqlFilter .= $this->getFilterByGroup($filter, 'company_org_group3', 'company_org_group');
        $SqlFilter = App::replaceSQLFilter($SqlFilter, $filter);

        return $SqlFilter;
    }

    private function getFilterByGroup($filter, $table, $group)
    {
        if (is_array($filter[$group])) {
            $groups = implode("', '", $filter[$group]);
            $where = " AND ({$table}.{$group}_id IN ('" . $groups . "') OR 'ALL' IN ('" . $groups . "') OR '' IN ('" . $groups . "'))";
        } else {
            $where = " AND ({$table}.{$group}_id = '" . $filter[$group] . "'  OR '" . $filter[$group] . "' = '' OR '" . $filter[$group] . "' = 'ALL')";
        }

        return $where;
    }

    private function getLastRegs($regs, $terminalRelatedValue)
    {
        $retArr = [];
        $foundNMB = false;
        $foundNMK = false;
        $i = 0;
        while (!($foundNMB && $foundNMK) && $i < count($regs)) {
            if ($this->linkGroupToTerminalRelatedId == 'NONE'
                || in_array($regs[$i]['terminal_id'] . ';' . $regs[$i]['reader_id'], $this->usedTerminalsAndReaders[$terminalRelatedValue] ?? [])) {
                switch ($regs[$i]['event_type_id']) {
                    case 'NMB':
                        $foundNMB = true;
                        $retArr['NMB'] = $regs[$i]['time'];
                        break;
                    case 'NMK':
                        $foundNMK = true;
                        $retArr['NMK'] = $regs[$i]['time'];
                }
            }
            $i++;
        }
        $retArr['in'] = $foundNMB && (!$foundNMK || $retArr['NMB'] > $retArr['NMK']) ?
            Dict::getValue('yes') . ' (' . Dict::getValue('in') . ')': false;
        $retArr['sort'] = 0;

        if (!$retArr['in'] && $foundNMB && $retArr['NMB'] > date('Y-m-d', strtotime($this->date)))
        {
            $retArr['in'] = Dict::getValue('no') . ' (' . Dict::getValue('was') . ')';
            $retArr['sort'] = 1;
        }
        else if (!$retArr['in'])
        {
            $retArr['in'] = Dict::getValue('no') . ' (' . Dict::getValue('out') . ')';
            $retArr['sort'] = 2;
        }

        return $retArr;
    }

    private function getRegistrations($ecIds)
    {
        $implodedEcIds = "'".implode("','", $ecIds)."'";

        $SQL = "
			SELECT
				employee_card.employee_contract_id,
				registration.event_type_id,
				registration.time,
                registration.terminal_id,
                registration.reader_id
			FROM
				registration
			LEFT JOIN employee_card ON
					employee_card.card = registration.card
				AND employee_card.status =  {$this->published}
				AND '{$this->date}' BETWEEN employee_card.valid_from AND IFNULL(employee_card.valid_to, '" . $this->defaultEnd . "')
			WHERE
					employee_card.employee_contract_id IN (" . $implodedEcIds . ")
				AND	registration.status =  {$this->published}
				AND registration.event_type_id IN ('NMB', 'NMK')
				AND registration.reg_status =  {$this->published}
                AND registration.time <= '{$this->dateTime}'
                AND date(registration.time) >= '{$this->yesterday}'
                AND registration.created_by <> 'Automatic'
            ORDER BY employee_card.employee_contract_id, registration.time DESC;
		";

        $registrations = dbFetchAll($SQL);
        $retArr = [];

        foreach ($registrations as $reg) {
            $retArr[$reg['employee_contract_id']][] = $reg;
        }

        return $retArr;
    }

    private function usedTerminals()
    {
        if ($this->linkGroupToTerminalRelatedId !== 'NONE') {
            $sql = "
                SELECT
					related_value AS terminal_group_value,
                    terminal_id,
                    reader_id
				FROM link_group_to_terminal
				WHERE
						status = {$this->published}
					AND related_id = '{$this->linkGroupToTerminalRelatedId}'

			";
            $res = dbFetchAll($sql);
            $retArr = [];
            foreach ($res as $row) {
                $retArr[$row['terminal_group_value']][] = $row['terminal_id'] . ';' . $row['reader_id'];
            }
            return $retArr;
        }
        return ['ALL'];
    }
}
