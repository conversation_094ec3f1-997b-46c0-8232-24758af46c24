<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\Dict;
	use app\models\EmployeeGroup;
	use app\models\EmployeeGroupConfig;
	use app\models\Status;
	use Yang;

`/yii2-only';


#yii2: done

/**
 * A Payroll Correction Report kilistázza a bérátadás során zárolt adatokból keletkezett korrekciókat.
 * 
 * Innote link: payroll-correction-report
 */

class PayrollCorrectionReportController extends Grid2Controller
{	
	
	/**
	 * Grid2s szülőosztály konstruktor meghívása
	 */
	public function __construct() {
        parent::__construct("payrollCorrectionReport");
    }
	
	/**
	 * Grid2 inícializálása, használt mode: SQL
	 */
	protected function G2BInit()
    {
		parent::setControllerPageTitleId("page_title_payroll_correction_report");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);

		$this->LAGridDB->enableSQLMode();

		parent::setExportFileName(Dict::getValue("page_title_payroll_correction_report"));
		parent::G2BInit();
	}
	
	/**
	 * Grid2-s adathalmaz generáló SQL összeállítása
	 * @param array $filter
	 * @param string $gridID
	 * @param boolean $forReport
	 * @return string
	 */
	protected function setSQL($filter, $gridID, $forReport = false) 
    {
		$payrollTransferId = $_SESSION['tiptime'][userID()]['payrollTransfer']['payrollTransferId'];

		$SQL = "
			SELECT
				`employee`.`emp_id` AS id,
				CONCAT_WS(' ', `employee`.`last_name`, `employee`.`first_name`) AS name,
				`company`.`company_name` AS company,
				`payroll`.`payroll_name` AS payroll,
				`company_org_group2`.`company_org_group_name` AS cog2,
				REPLACE(ptc.`date`, '-', '.') AS date,
				ptc.`payroll_code`,
				ptc.`value_change` AS value,
				ptc.`type`
			FROM
				`payroll_transfer_corrections` ptc
			JOIN
				`employee` ON
						`employee`.`emp_id` = ptc.`emp_id`
					AND `employee`.`status` = " . Status::PUBLISHED . "
					AND (ptc.`date` BETWEEN `employee`.`valid_from` AND `employee`.`valid_to`)
			JOIN
				`employee_contract` ON
						`employee_contract`.`employee_id` = `employee`.`employee_id`
					AND `employee_contract`.`status` = " . Status::PUBLISHED . "
					AND (ptc.`date` BETWEEN `employee_contract`.`valid_from` AND `employee_contract`.`valid_to`)
					AND (ptc.`date` BETWEEN `employee_contract`.`ec_valid_from` AND `employee_contract`.`ec_valid_to`)
			LEFT JOIN
				`payroll` ON
						`payroll`.`payroll_id` = `employee`.`payroll_id`
					AND `payroll`.`status`= " . Status::PUBLISHED . "
					AND (ptc.`date` BETWEEN `payroll`.`valid_from` AND `payroll`.`valid_to`)
		";

		if (EmployeeGroupConfig::isActiveGroup('company')) {
			$SQL .= EmployeeGroup::getLeftJoinSQL("company_id", "employee_contract", "ptc.`date`");
		}
		if (EmployeeGroupConfig::isActiveGroup('company_org_group2_id')) {
			$SQL .= EmployeeGroup::getLeftJoinSQL("company_org_group2_id", "employee_contract", "ptc.`date`");
		}
		if (EmployeeGroupConfig::isActiveGroup('unit_id')) {
			$SQL .= EmployeeGroup::getLeftJoinSQL("unit_id", "employee_contract", "ptc.`date`");
		}
		if (EmployeeGroupConfig::isActiveGroup('workgroup_id')) {
			$SQL .= EmployeeGroup::getLeftJoinSQL("workgroup_id", "employee_contract", "ptc.`date`");
		}

		$SQL .= "
			LEFT JOIN 
				`company` ON
						`company`.`company_id` = " . EmployeeGroup::getActiveGroupSQL("company_id", "employee") . "
					AND (ptc.`date` BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
					AND `company`.`status` = " . Status::PUBLISHED . "
			LEFT JOIN 
				`company_org_group2` ON
						`company_org_group2`.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL("company_org_group2_id", "employee") . "
					AND (ptc.`date` BETWEEN `company_org_group2`.`valid_from` AND IFNULL(`company_org_group2`.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
					AND `company_org_group2`.`status` = " . Status::PUBLISHED . "
			LEFT JOIN 
				`unit` ON
						`unit`.`unit_id` = " . EmployeeGroup::getActiveGroupSQL("unit_id", "employee") . "
					AND (ptc.`date` BETWEEN `unit`.`valid_from` AND IFNULL(`unit`.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
				AND `unit`.`status` = " . Status::PUBLISHED . "
			LEFT JOIN
				`workgroup` ON
						`workgroup`.`workgroup_id` = " . EmployeeGroup::getActiveGroupSQL("workgroup_id", "employee_contract") . "
					AND `workgroup`.`status` = " . Status::PUBLISHED . "
					AND (ptc.`date` BETWEEN `workgroup`.`valid_from` AND IFNULL(`workgroup`.`valid_to`, '" . App::getSetting("defaultEnd") . "'))
			WHERE
					ptc.`status` = " . Status::PUBLISHED . "
				AND ptc.`payroll_transfer_id` = '{$payrollTransferId}'
			ORDER BY
				id, date
		";
		
		return $SQL;
	}
	
	/**
	 * Oszlopok definiálása
	 * @return array
	 */
	public function columns()
    {
		$columns =
		[
			'id'			=>
			[
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'ed',
				'width'			=> 150,
			],
			'name'			=>
			[
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'ed',
				'width'			=> 150
			],
			'company'		=>
			[
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'ed',
				'width'			=> 300
			],
			'payroll'		=>
			[
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'ed',
				'width'			=> 150
			],
			'cog2'			=> 
			[
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'ed',
				'width'			=> 220
			],
			'date'			=>
			[
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'ed',
				'width'			=> 150
			],
			'payroll_code'	=>
			[
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'ed',
				'width'			=> 300
			],
			'value'			=>
			[
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'ed',
				'width'			=> 150
			],
			'type'			=>
			[
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'ed',
				'width'			=> 150
			]
		];
		
		return $columns;
    }
	
	/**
	 * Oszlop elnevezések definiálása
	 * @return array
	 */
	public function attributeLabels()
    {
        $return =
			[
                'id'			=> Dict::getValue("id"),
				'name'			=> Dict::getValue("name"),
                'company'		=> Dict::getValue("company_id"),
				'payroll'		=> Dict::getValue("pcr_payroll"),
                'cog2'			=> Dict::getValue("pcr_cog2"),
                'date'			=> Dict::getValue("pcr_date"),
				'payroll_code'	=> Dict::getValue("pcr_payroll_code"),
				'value'			=> Dict::getValue("value"),
				'type'			=> Dict::getValue("type")
			];
		
		return $return;
    }
	
	/**
	 * Kereső definiálása
	 * @return array
	 */
	public function search() {
		return $this->getPreDefinedSearchFromDb();
	}

	/**
	 * Biztonsági függvény #1
	 * @return array
	 */
	public function filters() {
        return [
            'accessControl', // perform access control for CRUD operations
        ];
    }
	
	/**
	 * Biztonsági függvény #2
	 * @return array
	 */
	public function accessRules()
	{
        return
		[
            [
				'allow', // allow authenticated users to access all actions
                'users' => ['@'],
            ],
            [
				'deny',  // deny all users
                'users' => ['*'],
            ],
        ];
    }
}
?>