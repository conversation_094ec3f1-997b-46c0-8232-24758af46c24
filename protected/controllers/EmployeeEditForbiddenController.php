<?php

class EmployeeEditForbiddenController extends Grid2Controller
{
	private $statusPublished;

	public function __construct()
	{
		parent::__construct("employeeEditForbidden");
		$this->statusPublished = Status::PUBLISHED;
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("EmployeeEditForbidden");

		parent::setControllerPageTitleId("page_title_employee_edit_forbidden");

		$this->LAGridRights->overrideInitRights("paging", true);
		$this->LAGridRights->overrideInitRights("search_header", true);
		$this->LAGridRights->overrideInitRights("select", true);
		$this->LAGridRights->overrideInitRights("column_move", true);
		$this->LAGridRights->overrideInitRights("reload_sortings", true);
		$this->LAGridRights->overrideInitRights("details", false);
		$this->LAGridRights->overrideInitRights("column_move", true);
		$this->LAGridRights->overrideInitRights("col_sorting", true);
		$this->LAGridRights->overrideInitRights("add", true);
		$this->LAGridRights->overrideInitRights("delete", true);
		$this->LAGridDB->enableSQLMode();

		$SQL = "
			SELECT 
				employee_valid_from.row_id,
				e.employee_id AS id,
				" . Employee::getParam('fullname_with_emp_id') . " AS full_name
			FROM
				employee e
			JOIN (SELECT
				employee_edit_forbidden.row_id,
				e.employee_id,
				MAX(valid_from) AS max_valid_from
			FROM
				employee e
			JOIN employee_edit_forbidden ON
					employee_edit_forbidden.employee_id = e.employee_id
				AND employee_edit_forbidden.status = " . $this->statusPublished . "
			WHERE
				e.status = " . $this->statusPublished . "
			GROUP BY employee_id) AS employee_valid_from ON
				employee_valid_from.employee_id = e.employee_id
				AND e.valid_from = employee_valid_from.max_valid_from
			WHERE
				status = " . $this->statusPublished . "
			ORDER BY full_name
		";
		$this->LAGridDB->setSQLSelection($SQL, "row_id");

		parent::G2BInit();
	}


	/**
	 * @return array customized grid columns (name=>params)
	 */
	public function columns()
	{
		$eefSQL = "
			SELECT 
				e.employee_id AS id,
				" . Employee::getParam('fullname_with_emp_id') . " AS value
			FROM
				employee e
			JOIN (SELECT
				e.emp_id AS inner_emp_id,
				MAX(valid_from) AS max_valid_from
			FROM
				employee e
			LEFT JOIN employee_edit_forbidden ON
					employee_edit_forbidden.employee_id = e.employee_id
				AND employee_edit_forbidden.status = " . $this->statusPublished . "
			WHERE
				employee_edit_forbidden.row_id IS NULL
			AND
				e.status = " . $this->statusPublished . "
			GROUP BY emp_id) AS employee_valid_from ON
				employee_valid_from.inner_emp_id = e.emp_id
				AND e.valid_from = employee_valid_from.max_valid_from
			WHERE
				status = " . $this->statusPublished . " 
			ORDER BY value
		";


		return [
			'employee_id' => ['grid'=>false, 'width'=>300, 'window'=>true, 'col_type'=>'combo',
									'options'	=>	[
										'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
										'sql'	=> $eefSQL
									],
								],
			'full_name' => ['grid'=>true, 'width'=>300, 'window'=>false, 'col_type'=>'ro',]
		];
	}

	public function attributeLabels(){
		return 
			['full_name' => Dict::getValue('full_name')];
	}
}