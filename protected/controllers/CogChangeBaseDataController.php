<?php

class CogChangeBaseDataController  extends Grid2Controller
{
	public function __construct()
	{
		parent::__construct("cogChangeBaseData");
	}
	
	protected function G2BInit() 
	{
		parent::setControllerPageTitleId("page_title_cog_change_base_data");

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("reload",			true);
		$this->LAGridRights->overrideInitRights("details",			false);

		$this->LAGridRights->overrideInitRights("export_xls",		false);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);
		$this->LAGridRights->overrideInitRights("export_pdf_node",	false);
		$this->LAGridRights->overrideInitRights("init_open_search",	false);

		$this->LAGridDB->enableSQLMode();

		parent::setGridProperty("splitColumnEnabled",    false);
		parent::setGridProperty("splitColumn",            2);
		parent::G2BInit();
	}
	
	protected function setSQL($filter, $gridID, $forReport = false) 
	{
		$SQL="";
		
		return $SQL;
	}
	
	public function search()
	{
		return array();
	}
	
	public function columns()
	{
		return array();
	}
	
	public function attributeLabels()
	{
		return array();
	}
}
?>