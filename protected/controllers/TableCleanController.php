<?php

class TableCleanController extends Controller
{
	public function actionIndex() {
		$this->main(1);
	}

	private function main($cleantype)
	{
		$this->log("Clean START");
		$dbCleanType = $cleantype; //1 - tábla fizikai méretét is csökkenti

		$this->dataBasesClean($dbCleanType);

		$this->log("Clean STOP");
		return;
	}

	private function dataBasesClean($dbCleanType)
	{
		$cleanConfig = $this->tableCleanConfig();
		if (!empty($cleanConfig)) {
			if ($dbCleanType == 1) {
				$this->tableReCreateInsert($cleanConfig);
			} else {
				$this->tableDeleteRows($cleanConfig);
			}
		}

		return;
	}

	private function tableReCreateInsert($cleanConfig)
	{
		foreach ($cleanConfig as $config) {
			$now = date("Y-m-d");
			$day = $config["day"];
			$minDatum = date("Y-m-d", strtotime($now . " -$day day"));
			if ($day == 0) {
				$this->tableOnlyReCreate($config);
			} else {
				$SQL = "SHOW CREATE TABLE " . $config["table_name"] . ";";
				$result = dbFetchAll($SQL);
				$createTableSql = $result[0]["Create Table"];

				$newTableName = $config["table_name"] . rand(1000, 9999);
				$SQL = "RENAME TABLE " . $config["table_name"] . " TO " . $newTableName;
				$result = dbExecute($SQL);

				$result = dbExecute($createTableSql);

				$SQL = "SHOW COLUMNS FROM " . $config["table_name"] . ";";
				$result = dbFetchAll($SQL);
				$fields = [];
				foreach($result AS $row) {
					if ($row["Field"] != "row_id") {
						$fields[] = $row["Field"];
					}
				}
				$fieldsReady = "`" . implode("`,`", $fields) . "`";

				$SQL = "INSERT INTO " . $config["table_name"] . "(" . $fieldsReady . ")
						SELECT " . $fieldsReady . " FROM " . $newTableName . "
						WHERE " . $config["table_colum"] . " >= '" . $minDatum . "';";
				$result = dbExecute($SQL);

				$SQL = "DROP TABLE " . $newTableName . ";";
				$result = dbExecute($SQL);
			}
			$this->log("Table: " . $config["table_name"] . " - Colum: " . $config["table_colum"] . " - Day: " . $minDatum);
		}

		return;
	}

	private function tableDeleteRows($cleanConfig)
	{
		foreach ($cleanConfig as $config) {
			$now = date("Y-m-d");
			$day = $config["day"];
			$minDatum = date("Y-m-d", strtotime($now . " -$day day"));
			if ($day == 0) {
				$this->tableOnlyReCreate($config);
			} else {
				$SQL = "DELETE FROM " . $config["table_name"] . " WHERE " . $config["table_colum"] . " < '" . $minDatum . "';";
				$result =dbExecute($SQL);
			}
			$this->log("Table: " . $config["table_name"] . " - Colum: " . $config["table_colum"] . " - Day: " . $minDatum);
		}
		return;
	}

	private function tableOnlyReCreate($config)
	{
		$SQL = "SHOW CREATE TABLE " . $config["table_name"] . ";";
		$result = dbFetchAll($SQL);
		$createTableSql = $result[0]["Create Table"];

		$SQL = "DROP TABLE " . $config["table_name"] . ";";
		$result = dbExecute($SQL);

		$result = dbExecute($createTableSql);
		return;
	}

	private function tableCleanConfig()
	{
		$config = [];
		$SQL = "SELECT `table_name`, `table_colum`, `day` FROM `table_clean_config` WHERE `status` = " . Status::PUBLISHED . " AND `day` >= 0;";
		$result = dbFetchAll($SQL);
		if ($result) {
			$this->log("Config table rows OK");
		} else {
			$this->log("Empty config table!");
		}
		return $result;
	}


	private function log($param = "", $logType = "echo")
	{
		if ($logType == "echo") {
			echo $param . " <BR> ";
		}
	}
}
?>