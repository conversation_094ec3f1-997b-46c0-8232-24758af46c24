<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\FS;
	use app\models\Status;
	use Yang;

`/yii2-only';


#yii2: done

class EmployeeImagesController extends Controller
{
	private $published;


	public function actionIndex()
	{
		$this->published = Status::PUBLISHED;
		$ftpPath = Yang::getBasePath() . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR . 'ftp' . DIRECTORY_SEPARATOR .
				'sanmina_user' . DIRECTORY_SEPARATOR . 'photos' . DIRECTORY_SEPARATOR;
		$this->employeeImageUpload($ftpPath);
	}
	
	private function employeeImageUpload($sourceDir)
	{
		$files = scandir($sourceDir);
		
		$sql = "SELECT row_id, emp_id
				FROM employee
				WHERE status = $this->published
				AND CURDATE() BETWEEN valid_from AND valid_to
				";
		$empRes = dbFetchAll($sql, 'emp_id', 'row_id');
		
		$i = 0;
		foreach ($files as $file)
		{
			$fileexplode = explode('.', $file);
			$emp_id = $fileexplode[0];
			if (isset($empRes[$emp_id]))
			{
				$fs = new FS();
				$fs->uploadFile($sourceDir.$file, $empRes[$emp_id]);
				$customer = Yang::getParam('customerDbPatchName');
				$path = Yang::getBasePath() . DIRECTORY_SEPARATOR . '..' . DIRECTORY_SEPARATOR .'webroot' . DIRECTORY_SEPARATOR .
						'file_storage' . DIRECTORY_SEPARATOR . $customer . '_employee' . DIRECTORY_SEPARATOR;
				
				copy($sourceDir . $file, $path . $file);
				unlink($sourceDir . $file);
				$i++;
			}
		}
		Yang::log('inserted images: '.$i, 'log', 'A');
	}
	
	private function copyImagesOfExistingEmployees($sourceDir, $targetDir)
	{
		//$sourceDir = "C:\Users\<USER>\Documents\FELADATOK\sanmina\T&A photos-20191211T131816Z-001\T_A photos\\";
		//$targetDir = "C:\Users\<USER>\Documents\FELADATOK\sanmina\photos\\";
		
		$sql = "SELECT emp_id
				FROM employee
				WHERE status = $this->published
				AND CURDATE() BETWEEN valid_from AND valid_to
				";
		$empRes = dbFetchColumn($sql);
		
		$files = scandir($sourceDir);

		foreach ($files as $file)
		{
			$fileexplode = explode('.', $file);
			$emp_id = $fileexplode[0];
			if (in_array($emp_id, $empRes))
			{
				copy($sourceDir . $file, $targetDir . $file);
			}
		}
	}
}