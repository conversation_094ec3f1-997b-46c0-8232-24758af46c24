<?php

class DocumentManagementController extends Grid2Controller
{
	private string $publishedStatus;
	private string $defaultEnd;
	private string $lang;

	public function __construct()
	{
		parent::__construct("documentManagement");

		$this->publishedStatus = Status::PUBLISHED;
		$this->defaultEnd = App::getSetting('defaultEnd');
		$this->lang = Dict::getLang();
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName("DocumentTemplate");

		parent::setControllerPageTitleId("page_title_document_management");

		$this->LAGridRights->overrideInitRights("reload",			true);
		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("init_open_search",	false);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("add", 				true);
		$this->LAGridRights->overrideInitRights("delete", 			true);

		$multiSelectConditions = $this->getMultiSelectConditions();

		$model = new DocumentTemplate('insert', ['pdf']);
		$crit = new CDbCriteria();
		$crit->condition = "
                (`template_name` IN ('" . $multiSelectConditions["templateNames"] . "') OR 'ALL' IN ('" . $multiSelectConditions["templateNames"] . "')) AND "
			. "'{valid_date}' BETWEEN `valid_from` AND IFNULL(`valid_to`,'{$this->defaultEnd}') AND "
			. " `status`= {$this->publishedStatus}";
		$crit->order = "`template_name`";
		$this->LAGridDB->setModelSelection($model, $crit, "dhtmlxGrid");
		parent::G2BInit();
	}

	protected function search()
	{
		$searchFields = $this->getPreDefinedSearchFromDb();

		unset($searchFields["submit"]);

		$searchFields["valid_date"]["default_value"] = date("Y-m-d");

		$searchFields["template_name"] = [
			'col_type'		=> 'combo',
			'label_text'	=> Dict::getValue("template_name"),
			'multiple'		=> 1,
			'class'			=> 'customSelect2Class',
			'default_value' => 'ALL',
			'options'		=>	[
				'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
				'sql'	=> $this->getTemplateNameSQL(),
				'array'	=> [["id"=>"ALL","value"=>Dict::getValue("all")]]
			],
		];
		
		$searchFields["submit"]			= ['col_type' => 'searchBarReloadGrid', 'width' => '*', 'label_text' => ''];

		return $searchFields;
	}

	public function columns()
	{
		return
			[
				'template_name'			=>	[
					'grid'			=>	true,
					'window'		=>	false,
					'col_type'		=>	'ed',
					'align'			=>	'center',
					'width'			=> 	'650'
				],
				'valid_from'			=>	['grid'=> true, 'col_type'=>'ed', 'align' => 'center', 'window' => true, 'dPicker' => true, 'width' => '*'],
				'valid_to'				=>	['grid'=> true, 'col_type'=>'ed', 'align' => 'center', 'window' => true, 'dPicker' => true, 'width' => '*'],
				'fs_file_id'			=> 	[
					'grid'						=> false,
					'window'					=> true,
					'dialog_width'				=> '2',
					'col_type'					=> 'documentUpload',
					'acceptedMemes'				=> '.pdf'
				]
			];
	}

	public function attributeLabels()
	{
		return array(
			'template_name'			=> Dict::getValue("documentName"),
			'fs_file_id'			=> Dict::getValue("file"),
			'valid_from'			=> Dict::getValue("valid_from"),
			'valid_to'				=> Dict::getValue("valid_to")
		);
	}

	private function getMultiSelectConditions()
	{
		$filter = requestParam('searchInput');

		return [
			"templateNames" 		=> $this->checkMultiselectConditionType($filter["template_name"])
		];
	}

	private function getTemplateNameSQL()
	{
		$filter = requestParam('searchInput');
		
		$SQL = "
			SELECT 
				`template_name` as id,
				`template_name` as value
			FROM `document_template`
			WHERE 
			    	`status` = {$this->publishedStatus}
				AND '{$filter['valid_date']}' BETWEEN `valid_from` AND IFNULL(`valid_to`,'{$this->defaultEnd}')
			ORDER BY value
		";

		return $SQL;
	}

	private function checkMultiselectConditionType($searchInputField)
	{
		return is_array($searchInputField) ? implode("','", $searchInputField) : "'" . $searchInputField . "'";
	}
}