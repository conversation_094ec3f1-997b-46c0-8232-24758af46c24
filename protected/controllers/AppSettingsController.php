<?php

class AppSettingsController extends Grid2Controller
{
	
	public function __construct()
	{
		parent::__construct("appSettings");

	}

	protected function G2BInit()
	{
		parent::setControllerPageTitleId("page_title_app_settings");
		parent::setExportFileName(Dict::getValue("page_title_app_settings"));
		$this->LAGridRights->overrideInitRights("select",			true);

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			false);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);

		$this->LAGridRights->overrideInitRights("export_xlsx",		true);
		$this->LAGridRights->overrideInitRights("init_open_search",	false);
		$this->LAGridDB->enableSqlMode();
	
		$sql = "
				SELECT 
					aps.`row_id`,
					aps.`setting_id`,
					aps.`setting_value`, 
					aps.`note`, 
					aps.`group_id`, 
					aps.`dependency`, 
					aps.`created_by`, 
					aps.`created_on`, 
					aps.`modified_by`, 
					aps.`modified_on`, 
					dict.`dict_value` 
				FROM `app_settings` as aps
				LEFT JOIN `dictionary` as dict ON 
						dict.`dict_id` = aps.`dict_id` 
					AND dict.`lang` = '" . Dict::getLang() . "'
					AND dict.`valid` = '1'
				WHERE 
					aps.`valid` = '1'	
				ORDER BY `setting_id` ASC;
			";

		$this->LAGridDB->setSQLSelection($sql, "row_id");

		parent::enableLAGrid();
		
		$this->LAGridDB->setPrimaryKey('row_id');
		
		parent::G2BInit();
	}

	public function columns()
	{		
		return [
			'setting_id'		 => ['grid'=>true, 'width'=>300, 'window'=>true, 'export'=> true, 'col_type'=>'ed'],
			'setting_value'		 => ['grid'=>true, 'width'=>150, 'window'=>true, 'export'=> true, 'col_type'=>'ed'],			
			'note'				 => ['grid'=>true, 'width'=>300, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'cssClass'	=> 'wrap'],
			'dict_value'		 => ['grid'=>true, 'width'=>300, 'window'=>true, 'export'=> true, 'col_type'=>'ed', 'cssClass'	=> 'wrap'],
			'group_id'		 	 => ['grid'=>true, 'width'=>100, 'window'=>true, 'export'=> true, 'col_type'=>'ed'],
			'dependency'		 => ['grid'=>true, 'width'=>100, 'window'=>true, 'export'=> true, 'col_type'=>'ed'],
			'created_by'		 => ['grid'=>true, 'width'=>100, 'window'=>true, 'export'=> true, 'col_type'=>'ed'],
			'created_on'		 => ['grid'=>true, 'width'=>100, 'window'=>true, 'export'=> true, 'col_type'=>'ed'],
			'modified_by'		 => ['grid'=>true, 'width'=>100, 'window'=>true, 'export'=> true, 'col_type'=>'ed'],
			'modified_on'		 => ['grid'=>true, 'width'=>100, 'window'=>true, 'export'=> true, 'col_type'=>'ed']			
		];
	}

	public function attributeLabels()
	{
		return [		
			'setting_id'		 => Dict::getValue("setting_id"),
			'setting_value'		 => Dict::getValue("setting_value"),			
			'note'				 => Dict::getValue("note"),
			'dict_value'		 => Dict::getValue("dict_value"),
			'group_id'		 	 => Dict::getValue("group_id"),
			'dependency'		 => Dict::getValue("dependency"),
			'created_by'		 => Dict::getValue("created_by"),
			'created_on'		 => Dict::getValue("created_on"),
			'modified_by'		 => Dict::getValue("modified_by"),
			'modified_on'		 => Dict::getValue("modified_on")		
		];
	}
	public function filters()
	{
		return [
			'accessControl', // perform access control for CRUD operations
		];
	}
	
	public function accessRules()
	{
        return
		[
            [
				'allow', // allow authenticated users to access all actions
				'users' => ['root', '<EMAIL>'],
            ],
            [
				'deny',  // deny all users
                'users' => ['*'],
            ],
        ];
	} 
}