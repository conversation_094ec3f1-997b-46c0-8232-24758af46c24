<?php
/**
 * Munkacsoport keretek megjelenítése - DEV-15111
 */
class WorkGroupFrameController extends Grid2Controller
{
    private $defaultEnd;
    private $published = Status::PUBLISHED;

	public function __construct()
	{
		parent::__construct("workGroupFrame");
        $this->defaultEnd = App::getSetting("defaultEnd");
        $this->maxDays = 9999;
	}

	protected function G2BInit()
	{
		parent::setControllerPageTitleId("page_title_work_group_frame");

		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
 		$this->LAGridRights->overrideInitRights("init_open_search",	true);
        $this->LAGridRights->overrideInitRights("export_xlsx",		true);

        $this->LAGridDB->enableSQLMode();

         $SQL = "SELECT
                    wg.workgroup_name,
                    wf.step,
                    wf.frame_from,
                    wf.frame_to
                FROM
                    workgroup_frame wf
                JOIN
                    workgroup wg
                        ON wg.row_id = wf.workgroup_row_id
                    AND wg.status = {$this->published}
                    AND wg.valid_from <= IFNULL('{valid_to}','{$this->defaultEnd}')
                    AND IFNULL(wg.valid_to,'{$this->defaultEnd}') >= '{valid_from}'
                    AND (wg.workgroup_name = '{group_name}' OR '{group_name}' = 'ALL')
                WHERE
                        wf.frame_from <= IFNULL('{valid_to}','{$this->defaultEnd}')
                    AND IFNULL(wf.frame_to,'{$this->defaultEnd}') >= '{valid_from}'
                ORDER BY wg.workgroup_name
                ";

        $this->LAGridDB->setSQLSelection($SQL);
		parent::G2BInit();
	}

    public function search()
    {
        $search =
            [
                'valid_from' =>
                [
                    'col_type' => 'ed',
                    'width' => '*',
                    'dPicker' => true,
                    'label_text' => Dict::getValue("valid_from"),
                    'default_value' => date('Y-01-01'),
                    'columnId' => 'valid_from',
                    'onchange' => ['group_name']
                ],
                'valid_to' =>
                [
                    'col_type' => 'ed',
                    'width' => '*',
                    'dPicker' => true,
                    'label_text' => Dict::getValue("valid_to"),
                    'default_value' => date("Y-12-31"),
                    'columnId' => 'valid_to',
                    'onchange' => ['group_name']
                ],
                'group_name' =>
                [
                    'col_type' => 'combo',
                    'options' =>
                    [
                        'mode' => Grid2Controller::G2BC_QUERY_MODE_SQL,
                        'sql' => "
                                SELECT DISTINCT
                                    wg.workgroup_name AS id,
                                    wg.workgroup_name AS value
                                FROM
                                    workgroup_frame wf
                                JOIN
                                    workgroup wg
                                        ON wg.row_id = wf.workgroup_row_id
                                    AND wg.status = {$this->published}
                                    AND wg.valid_from <= IFNULL('{valid_to}','{$this->defaultEnd}')
                                    AND IFNULL(wg.valid_to,'{$this->defaultEnd}') >= '{valid_from}'
                                WHERE
                                        wf.frame_from <= IFNULL('{valid_to}','{$this->defaultEnd}')
                                    AND IFNULL(wf.frame_to,'{$this->defaultEnd}') >= '{valid_from}'
                                ORDER BY value
					            ",
                                'array'	=> ['options' => ["id"=>"ALL","value"=>Dict::getValue("all")]],
				    ],
				    'label_text'	    => Dict::getValue("workgroup"),
                    'default_value'		=> "ALL"
			    ],
                'submit' => ['col_type' => 'searchBarReinitGrid', 'gridID' => 'dhtmlxGrid', 'width' => '*', 'label_text' => '']
            ];

        return $search;
    }

	public function columns()
	{
		return [
			'workgroup_name'		            => ['width'=>300, 'col_type'=>'ed'],
            'step'                              => ['width'=>150, 'col_type'=>'ed', 'align' => 'center'],
            'frame_from'                        => ['width'=>150, 'col_type'=>'ed', 'align' => 'center'],
            'frame_to'                          => ['width'=>150, 'col_type'=>'ed', 'align' => 'center'],
            ];
	}

	public function attributeLabels()
	{
        return [
			'workgroup_name'		            => Dict::getValue("workgroup_name"),
            'step'                              => Dict::getValue("step"),
            'frame_from'                        => Dict::getValue("frame_begin"),
            'frame_to'                          => Dict::getValue("frame_end"),
            ];
	}

	public function filters() {
        return [
            'accessControl', // perform access control for CRUD operations
        ];
    }

	public function accessRules()
	{
        return
		[
            [
				'allow', // allow authenticated users to access all actions
                'users' => ['root'],
            ],
            [
				'deny',  // deny all users
                'users' => ['*'],
            ],
        ];
	}
}
?>