<?php

class User2FAQrShowController extends Grid2Controller
{
	public function __construct() {
		parent::__construct("user2FAQrShow");
	}

	protected function G2BInit() :void {
		$this->LAGridDB->setModelName("User");
	}

	public function columns() :array
	{
        $title      = 'Ease++';
        $email      = '<EMAIL>';
        $gfa        = (new PragmaRX\Google2FA\Google2FA());

        if (Yang::session('qr_form_user', '') != '')
        {
            $username   = Yang::session('qr_form_user', '');
			$user       = new User;
			$criteria   = new CDbCriteria();
			$criteria->alias        = "u";
			$criteria->condition    = "u.`username` = '{$username}' AND u.`status`= " . Status::PUBLISHED . " AND NOW() BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '" . App::getSetting("defaultEnd") . "')";
			$result     = $user->find($criteria);
            if ($result)
            {
                if (!empty($result->email)) {
                    $email = $result->email;
                }
                if (!empty($result->google_two_fa_secret_key)) {
                    $secret = $result->google_two_fa_secret_key;
                } else {
                    $secret = $gfa->generateSecretKey(64);
                }
            } else {
                $secret = $gfa->generateSecretKey(64);
            }
        } else {
            $secret = $gfa->generateSecretKey(64);
        }

        $g2faUrl    = $gfa->getQRCodeUrl(
            $title,
            $email,
            $secret
        );

        $writer     = new BaconQrCode\Writer(
            new BaconQrCode\Renderer\ImageRenderer(
                new BaconQrCode\Renderer\RendererStyle\RendererStyle(200),
                new BaconQrCode\Renderer\Image\ImagickImageBackEnd()
            )
        );

        $qrImg      = base64_encode($writer->writeString($g2faUrl));

		return [
            'twoFactorKey'              => ['grid' => false, 'export' => false, 'col_type' => 'ed'],
            'google_two_fa_secret_key'  => ['grid' => false, 'export' => false, 'col_type' => 'hidden', 'default_value' => $secret],
            'qr_form_username'          => ['grid' => false, 'export' => false, 'col_type' => 'hidden', 'default_value' => Yang::session('qr_form_user', '')],
            'qr_form_pass'              => ['grid' => false, 'export' => false, 'col_type' => 'hidden', 'default_value' => Yang::session('qr_form_pass', '')],
            'qr_code'                   => ['grid' => false, 'export' => false, 'col_type' => 'qr_code', 'default_value' => $qrImg]
		];
	}

    public function attributeLabels() :array
    {
        return [
            'twoFactorKey' => Dict::getValue("google_two_fa_secret_key")
        ];
    }

    /**
     * Belépés
     * @param array $data
     * @param string $modelName
     * @param string $pk
     * @param boolean $vOnly
     * @param boolean $ret
     * @param string $contentId
     * @return void
     */
    public function actionSave($data = array(), $modelName = null, $pk = null, $vOnly = false, $ret = false, $contentId = null) :void
    {
        $logrc          = new ReflectionClass("LoginController");
        $log            = $logrc->newInstanceWithoutConstructor();
        $input          = requestParam("dialogInput_dhtmlxGrid") ?? [];
        $login          = new LoginForm;
		$login->ttwaun  = $input["qr_form_username"] ?? "";
		$login->ttwapw  = $input["qr_form_pass"] ?? "";
        $login->setTwoFactorKey(($input["twoFactorKey"] ?? ""));
        $login->setTwoFactorSecretKey(($input["google_two_fa_secret_key"] ?? ""));
		$log->validateAndLoginUser($login, "2FA");
    }
}