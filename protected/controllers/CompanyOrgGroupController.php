<?php

class CompanyOrgGroupController extends Grid2HistoryController
{
	public $layout = '//layouts/main';
	private $modellName;
	private $parentTableName;
	private $parentIdName;
	private $parentValueName;
	private $user;
	protected $usedGroup;
	protected $heatmapGroups;
	private $useCompanyAndPayrollRights;
	private $publishedStatus = Status::PUBLISHED;

	public function __construct($modellName = "CompanyOrgGroup1")
	{
		$this->modellName = $modellName;
		$this->user    = userID();
		parent::__construct(lcfirst($modellName));
		$this->usedGroup = App::getSetting("heatmapGroup");
		$this->heatmapGroups = App::getSetting("heatmapGroups");
		$this->useCompanyAndPayrollRights = App::getSetting("useCompanyAndPayrollRights");

		$table = new $this->modellName;
		$groupHierarchy = explode(';', App::getSetting('group_hierarchy'));
		$parentIndex = array_search($table->tableName(), $groupHierarchy) - 1;
		if ($parentIndex >= 0) {
			$this->parentTableName = $groupHierarchy[$parentIndex];
			$this->parentIdName = strpos($this->parentTableName, 'company_org_group') === 0 ? 'company_org_group_id' : $this->parentTableName . '_id';
			$this->parentValueName = strpos($this->parentTableName, 'company_org_group') === 0 ? 'company_org_group_name' : $this->parentTableName . '_name';
		}
	}

	protected function G2BInit()
	{
		$this->LAGridDB->setModelName($this->modellName);

		parent::setControllerPageTitleId("page_title_" . strtolower($this->modellName));

		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",		true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		true);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);

		$this->LAGridRights->overrideInitRights("export_xls",		false);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);
		$this->LAGridRights->overrideInitRights("export_pdf_node",	false);
		$this->LAGridRights->overrideInitRights("init_open_search",	true);
		$this->LAGridRights->overrideInitRights("add",				true);
		$this->LAGridRights->overrideInitRights("modify",			true);
		$this->LAGridRights->overrideInitRights("delete",			true);

		$this->LAGridDB->enableSQLMode();

		$filters = Yang::session("{$this->modellName}_filters", []);
		if (isset($filters["date"])) {
			$art = new ApproverRelatedGroup;
			$gargSQL = $art->getApproverReleatedGroupSQL($this->modellName, "companyMainData", false, "'{date}'", "AND", $filters["date"]);

			$table = new $this->modellName;

			$companyPayrollFilter = "";
			if ($this->useCompanyAndPayrollRights) {
				$companyPayrollFilter = "
					AND ({$table->tableName()}.`company_id` = '{company}' OR '{company}' = 'ALL' OR '{company}' = '')
					AND ({$table->tableName()}.`payroll_id` = '{payroll}' OR '{payroll}' = 'ALL' OR '{payroll}' = '')
				";
			}

			$SQL = "
				SELECT
					`" . $table->tableName() . "`.*
				FROM " . $table->tableName() . "
				";
			$SQL .= isset($gargSQL["join"]) ? $gargSQL["join"] : "";
			if ($this->parentTableName) {
				$SQL .= "
						LEFT JOIN `" . $this->parentTableName . "` parent_table ON
							`parent_table`.`" . $this->parentIdName . "` = `" . $table->tableName() . "`.`parent` 
							AND `parent_table`.`status`=" . $this->publishedStatus;
				if ($this->parentTableName !== 'work_activity') {
					$SQL .= " AND CURDATE() BETWEEN `parent_table`.`valid_from` AND `parent_table`.`valid_to`";
				}
			}
			$SQL .= "
					WHERE
						`" . $table->tableName() . "`.`status`=" . $this->publishedStatus . "
						{$companyPayrollFilter}
					AND	(`" . $table->tableName() . "`.`company_org_group_id` = '{companyorggroup}' OR '{companyorggroup}' = 'ALL' OR '{companyorggroup}' = '')
					AND ('{date}' = '' OR ('{date}' BETWEEN `" . $table->tableName() . "`.`valid_from` AND default_end(`" . $table->tableName() . "`.`valid_to`)))
				";
			$SQL .= isset($gargSQL["where"]) ? $gargSQL["where"] : "";
			$SQL .= "ORDER BY `company_org_group_name`";

			$this->LAGridDB->setSQLSelection($SQL, "row_id");
		}
		parent::setExportFileName(Dict::getValue("page_title_" . strtolower($this->modellName)));
		parent::G2BInit();
	}

	/**
	 * Kereső értékek átadása sessionnek az oszlopok felépítéséhez
	 * @return void
	 */
	public function actionSetInitProperties()
	{
		Yang::setSessionValue("{$this->modellName}_filters", requestParam('searchInput'));

		parent::actionSetInitProperties();
	}

	public function search()
	{
		$table = new $this->modellName;
		$art = new ApproverRelatedGroup;
		$gargSQL = $art->getApproverReleatedGroupSQL($this->modellName, "companyMainData", false, "'{date}'", "AND", "allDate");
		$gargJoin = $gargSQL["join"] ?? "";
		$gargWhere = $gargSQL["where"] ?? "";
		$cogFilterByCompanyIdPayrollId = "";
		if ($this->useCompanyAndPayrollRights) {
			$cogFilterByCompanyIdPayrollId = "
				AND ({$table->tableName()}.company_id = '{company}' OR {$table->tableName()}.company_id = 'ALL' OR '{company}' like 'ALL')
				AND ({$table->tableName()}.payroll_id = '{payroll}' OR {$table->tableName()}.payroll_id = 'ALL' OR '{payroll}' like 'ALL')";
		}
		$SQL = "
			SELECT
				`company_org_group_id` as id, `company_org_group_name` AS value
			FROM {$table->tableName()}
				{$gargJoin}
			WHERE
					('{date}' = '' OR ('{date}' BETWEEN {$table->tableName()}.`valid_from` AND default_end({$table->tableName()}.`valid_to`)))
				{$cogFilterByCompanyIdPayrollId}
				AND `company_org_group_name` like '%{search}%'
				{$gargWhere}
			ORDER BY `company_org_group_name`";
		$dateOnChange = [];
		$returnCompanyPayroll = [];
		if ($this->useCompanyAndPayrollRights) {

			$art = new ApproverRelatedGroup;
			$gargCompanySQL = $art->getApproverReleatedGroupSQL("Company", "companyMainData", false, "'{date}'", "AND", "allDate");
			$gargPayrollSQL = $art->getApproverReleatedGroupSQL("Payroll", "companyMainData", false, "'{date}'", "AND", "allDate");

			$company = new Company();
			$companyCriteria = $company->getColumnGridCriteria($gargCompanySQL['where'], "{date}");

			$payroll = new Payroll;
			$payrollCriteria = $payroll->getColumnGridCriteria($gargPayrollSQL['where'], "{date}");
			$payrollCriteria->condition .= "
			AND ({$payrollCriteria->alias}.company_id = '{company}' OR {$payrollCriteria->alias}.company_id = 'ALL' OR '{company}' like 'ALL')";
			
			$returnCompanyPayroll =
				[
					'company' =>
					[
						'label_text' => Dict::getValue("company_id"),
						'col_type'	=> 'combo',
						'options'	=>
						[
							'mode'					=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
							'modelSelectionModel'	=> $company,
							'modelSelectionCriteria' => $companyCriteria,
							'comboId'				=> 'company_id',
							'comboValue'			=> 'company_name',
							'array'	=> (!$this->useCompanyAndPayrollRights) ? [["id" => "ALL", "value" => Dict::getValue("all")]] : "",
						],
						'onchange'  => ['payroll'],
					],
					'payroll' =>
					[
						'label_text' => Dict::getValue("payroll_id"),
						'col_type'	=> 'combo',
						'options'	=>
						[
							'mode'					=> Grid2Controller::G2BC_QUERY_MODE_MODEL,
							'modelSelectionModel'	=> $payroll,
							'modelSelectionCriteria' => $payrollCriteria,
							'comboId'				=> 'payroll_id',
							'comboValue'			=> 'payroll_name',
							'array'					=> [["id" => "ALL", "value" => Dict::getValue("all")]],
						],
						'default_value' => 'ALL',
					],
				];

			$dateOnChange = ["company", "payroll"];
		}

		return Yang::arrayMerge(
			[
				'date' =>
				[
					'col_type'      => 'ed',
					'dPicker'       => true,
					'width'         => '*',
					'label_text'    => Dict::getValue("date"),
					'default_value' => date('Y-m-d'),
					'onchange'      => $dateOnChange,
				],
			],
			$returnCompanyPayroll,
			[
				'companyorggroup' =>
				[
					'col_type'	=> 'auto',
					'width'		=> '*',
					'label_text' => Dict::getValue("company_org_group_name"),
					'options' =>
					[
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> $SQL,
					],
				],
				'submit' =>
				[
					'col_type'  => 'searchBarReinitGrid',
					'width'     => '*',
					'label_text' => '',
				],
			]
		);
	}


	/**
	 * @return array customized grid columns (name=>params)
	 */
	public function columns()
	{
		$id = ((App::getSetting("cog1Controller_show_id") && $this->modellName == 'CompanyOrgGroup1') ||
			(App::getSetting("cog2Controller_show_id") && $this->modellName == 'CompanyOrgGroup2') ||
			(App::getSetting("cog3Controller_show_id") && $this->modellName == 'CompanyOrgGroup3') ||
			$this->user == '6acd9683761b153750db382c1c3694f6'
		) ?
			['company_org_group_id'		=> ['grid' => true, 'width' => 200, 'window' => true, 'export' => true, 'col_type' => 'ed', 'edit' => false]
		] :
			[];

		if ($this->useCompanyAndPayrollRights) {

			$filters = Yang::session("{$this->modellName}_filters", []);
			$art = new ApproverRelatedGroup;
			$gargCompanySQL = $art->getApproverReleatedGroupSQL("Company", "companyMainData", false, "'" . $filters["valid_from"] . "'", "AND", $filters["date"]);
			$gargPayrollSQL = $art->getApproverReleatedGroupSQL("Payroll", "companyMainData", false, "'" . $filters["valid_from"] . "'", "AND", $filters["date"]);

			$company = new Company();
			$companyCriteriaGrid = $company->getColumnGridCriteria($gargCompanySQL['where'], $filters["valid_from"]);
			$companyCriteriaDialog = $company->getColumnDialogCriteria($gargCompanySQL['where'], 'valid_from', 'valid_to');

			$payroll = new Payroll;
			$payrollCriteriaGrid = $payroll->getColumnGridCriteria($gargPayrollSQL['where'], $filters["valid_from"]);
			$payrollCriteriaDialog = $payroll->getColumnDialogCriteria($gargPayrollSQL['where'], 'valid_from', 'valid_to');
			$payrollCriteriaDialog->condition .= " AND (`company_id` = '{company_id}' OR 'ALL' = '{company_id}' OR `company_id` = 'ALL')";

			$column =
				[
					'company_id' =>
					[
						'export'	=> true,
						'grid'		=> true,
						'col_type'	=> 'combo',
						'options'	=>
						[
							'mode'					            => Grid2Controller::G2BC_QUERY_MODE_MODEL,
							'modelSelectionModel'   	        => $company,
							'modelSelectionCriteriaGridMode'    => $companyCriteriaGrid,
							'modelSelectionCriteriaDialogMode'  => $companyCriteriaDialog,
							'comboId'				            => 'company_id',
							'comboValue'			            => 'company_name',
							'array' => (!$this->useCompanyAndPayrollRights) ? [["id" => "ALL", "value" => Dict::getValue("all")]] : "",
						],
						'width'     => "*",
						'onchange'  => ["payroll_id"],
					],
					'payroll_id' =>
					[
						'export'	=> true,
						'grid'		=> true,
						'col_type'	=> 'combo',
						'options'	=>
						[
							'mode'					            => Grid2Controller::G2BC_QUERY_MODE_MODEL,
							'modelSelectionModel'	            => $payroll,
							'modelSelectionCriteriaGridMode'    => $payrollCriteriaGrid,
							'modelSelectionCriteriaDialogMode'  => $payrollCriteriaDialog,
							'comboId'				            => 'payroll_id',
							'comboValue'			            => 'payroll_name',
							'array'					            => (!$this->useCompanyAndPayrollRights) ? [["id" => "ALL", "value" => Dict::getValue("all")]] : "",
						],
						'width' => "*",
					]
				];
		}

		$retArr = [

			"identifier" 				=> 	['grid' => true, 'window' => true, 'col_type' => 'ed', 'width' => '200'],

			"company_org_group_name" 	=> 	['grid' => true, 'window' => true, 'col_type' => 'ed', 'width' => '300'],

			"minimal_group_count" 		=>	['export' => false, 'grid' => false, 'window' => false, 'col_type' => 'ed', 'width' => '300', 'default_value' => '0']
		];

		if ($this->parentTableName) {
			$retArr['parent'] = 	
								[
									'grid' => true, 'window' => true, 'col_type' => 'combo', 'width' => '300',
									'options' => [
										'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
										'sql'	=> "SELECT 
														". $this->parentTableName . "." . $this->parentIdName . " as id,
														". $this->parentTableName . "." . $this->parentValueName . " as value
													FROM
														" . $this->parentTableName . "
													WHERE
													" . $this->parentTableName . ".`status` = " . $this->publishedStatus . " AND ".$this->parentTableName.".`$this->parentValueName` LIKE '%%{search}%%'
													ORDER BY
														".$this->parentTableName.".`" . $this->parentValueName . "` ASC",
									],
								];
		}

		$retArr['note']			=	['grid' => false, 'window' => true, 'col_type' => 'ed', 'width' => '300'];
		$retArr['valid_from']	= 	[
										'grid' => true, 'window' => true, 'col_type' => 'ed', 'dPicker' => true,
										'col_align' => 'center', 'onchange' => ['unit_id', 'company_id', 'payroll_id']
									];
		$retArr['valid_to']		= 	[
										'grid' => true, 'window' => true, 'col_type' => 'ed', 'dPicker' => true,
										'col_align' => 'center', 'onchange' => ['unit_id', 'company_id', 'payroll_id']
									];
		$retArr['status']		= 	[
										'grid' => false, 'window' => false, 'col_type' => 'combo', 'export' => false,
										'options' => ['comboModel' => 'Status', 'comboId' => 'row_id', 'comboValue' => 'name'],
										'col_align' => 'center', 'width' => '100'
									];
		$retArr['created_by']	= 	[
										'grid' => false, 'window' => false, 'col_type' => 'combo', 'export' => false, 
										'options' => ['comboModel' => 'User','comboId' => 'user_id', 'comboValue' => 'username']
									];
		$retArr['created_on']	= 	[
										'grid' => false, 'window' => false, 'col_type' => 'ed', 'dPicker' => true, 'export' => false,
										'col_align' => 'center', 'width' => '100'
									];

		$retArr = $this->columnRights($retArr);

		return Yang::arrayMerge($id, $column, $retArr);
	}
}
