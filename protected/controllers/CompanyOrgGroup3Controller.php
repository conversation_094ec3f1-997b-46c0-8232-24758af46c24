<?php

'yii2-only`;

	namespace app\controllers;
	use Yang;

`/yii2-only';

$path = Yang::addAsset(Yang::getAlias('application.assets.base.companyorggroup'), false, -1, true);
Yang::registerScriptFile($path . '/js/companyorggroup3.js');

#yii2: done

class CompanyOrgGroup3Controller extends CompanyOrgGroupController
{
	public function __construct()
	{
		parent::__construct( 'CompanyOrgGroup3');
	}

	public function columns()
	{
		$column = parent::columns();

		if(App::getSetting('specialFrameDaysGroup') == 'company_org_group3')
		{
			// fel kell venni az app_lookup értékeket, ha majd használni kell (sinia keretegyenleg kezelés miatt 1558)
			$column['special_frameday_option']	= [
				'grid'		=> true,
				'window'	=> true ,
				'col_type'	=> 'combo',
				'width'		=> '300',
				'options'	=> [
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> "SELECT
										`lookup_value` as id,
										`dict_value` as value
									FROM
										`app_lookup`
									LEFT JOIN `dictionary` ON
											`app_lookup`.`dict_id` 	= `dictionary`.`dict_id`
										AND `dictionary`.`valid`	= 1
										AND `dictionary`.`lang`		= '" . Dict::getLang() . "'
									WHERE
											`app_lookup`.`valid`		= 1
										AND `app_lookup`.`lookup_id`	= 'yes_no'
									ORDER BY
										`dict_value` ASC",
					],
			];
		}
		if ($this->usedGroup == 'company_org_group3' || strpos( $this->heatmapGroups, 'company_org_group3') !== false) {
			$column["minimal_group_count"]['grid'] = true;
			$column["minimal_group_count"]['window'] = true;
			$column["minimal_group_count"]['export'] = true;
		}
		if (!is_null(Yang::getParam('rocketChatURL'))) {
			array_push($column['valid_from']['onchange'], 'rocket_moderator1', 'rocket_moderator2', 'rocket_moderator3');
			array_push($column['valid_to']['onchange'], 'rocket_moderator1', 'rocket_moderator2', 'rocket_moderator3');

			$filters = Yang::session("CompanyOrgGroup3_filters", []);
			$date = $filters["date"] ? $filters["date"] : date('Y-m-d');
			$userSQL = "
						SELECT
							'' AS id,
							' ' AS value
						UNION
						SELECT
							user.username AS id,
							user.username AS value
						FROM
							user
						WHERE
							user.status = ". Status::PUBLISHED ."
						AND user.valid_from <= IF('{valid_from}' = '', '{$date}','{valid_from}')
						AND IF('{valid_to}' = '', '{$date}','{valid_to}') <= IFNULL(user.valid_to, '" . App::getSetting("defaultEnd") . "')
						ORDER BY value
			";
			$column['rocket_moderator1'] = [
				'grid'		=> true,
				'window'	=> true,
				'col_type'	=> 'combo',
				'options'	=> ['mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
								'sql'	=> $userSQL
								]
			];
			$column['rocket_moderator2'] = [
				'grid'		=> true,
				'window'	=> true,
				'col_type'	=> 'combo',
				'options'	=> ['mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
								'sql'	=> $userSQL
								]
			];
			$column['rocket_moderator3'] = [
				'grid'		=> true,
				'window'	=> true,
				'col_type'	=> 'combo',
				'options'	=> ['mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
								'sql'	=> $userSQL
								]
			];
			$column['rocket_moderator4'] = [
				'grid'		=> true,
				'window'	=> true,
				'col_type'	=> 'combo',
				'options'	=> ['mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
								'sql'	=> $userSQL
								]
			];
			$column['rocket_moderator5'] = [
				'grid'		=> true,
				'window'	=> true,
				'col_type'	=> 'combo',
				'options'	=> ['mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
								'sql'	=> $userSQL
								]
			];
			$column['rocket_moderator6'] = [
				'grid'		=> true,
				'window'	=> true,
				'col_type'	=> 'combo',
				'options'	=> ['mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
								'sql'	=> $userSQL
								]
			];
		}

		return $column;
	}

	protected function getStatusButtons($gridID = null)
	{
		$buttons = [];

		if((!is_null(Yang::getParam('rocketChatURL'))) && App::hasRight($this->getControllerID(), "chatSyncron"))
		{
			$buttons["rocketSyncron"] = [
				"type"		=> "button",
				"id"		=> "rocketSyncron",
				"class"		=> "rocketSyncron",
				"name"		=> "rocketSyncron",
				"img"		=> "/images/status_icons/st_loop.png",
				"label"		=> Dict::getValue("chat_syncron"),
				"onclick"	=> "callRocketChatSyncron();",
			];
			$buttons[] = [
				"type" => "selector",
			];
		}

		$originalButtons = parent::getStatusButtons($gridID);

		return Yang::arrayMerge($buttons, $originalButtons);
	}
}