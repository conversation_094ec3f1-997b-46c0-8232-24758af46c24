<?php
Yang::loadComponentNamespaces('Employee');

use Components\Employee\Enum\EmployeeControllerEnum;

trait EmployeeControllerAttributeLabels
{
    public function baseAttributeLabels(): array
    {
        static $out;
        if ($out) {
            return $out;
        }

        $attributeLabels = [];

        $attributeLabels['dhtmlxGrid'] = [
            'fullname' => Dict::getValue('name'),
            'emp_id' => Dict::getValue('emp_id'),
            'company_name' => Dict::getValue('company_id'),
            'payroll_name' => Dict::getValue('payroll_id'),
            'workgroup_name' => Dict::getModuleValue('ttwa-base', 'workgroup'),
            'unit_name' => Dict::getValue('unit_id'),
            'cost_center_name' => Dict::getValue('cost_center'),
            'company_org_group1_name' => Dict::getModuleValue('ttwa-base', 'company_org_group1'),
            'company_org_group2_name' => Dict::getModuleValue('ttwa-base', 'company_org_group2'),
            'company_org_group3_name' => Dict::getModuleValue('ttwa-base', 'company_org_group3'),
            'date_of_birth' => Dict::getValue('date_of_birth'),
            'employee_contract_number' => Dict::getValue('employee_contract_number'),
            'employee_contract_type' => Dict::getValue('employee_contract_type'),
            'card' => Dict::getValue('cardnum'),
            'employee_position_name' => Dict::getValue('employee_position_name'),
            'tax_number' => Dict::getValue('tax_number'),

            'risk_of_term' => Dict::getValue('risk_of_notice'),
            'time_of_term' => Dict::getValue('period_of_notice'),
            'cause_of_term' => Dict::getValue('reason_of_notice'),
            'ssc_index' => 'SSC Index',
            'mood_index' => Dict::getValue('mood_index'),
            'role_in_tem_comm' => Dict::getValue('role_in_community'),
            'absenteeism' => Dict::getValue('absenteeism'),
            'time_prop_abs' => Dict::getValue('absences_pro_rata'),
            'delays' => Dict::getValue('delays'),
            'overtime' => Dict::getValue('overtime'),
            'cond_eval' => Dict::getValue('evaluation_from_managers'),
            'subs_eval' => Dict::getValue('evaluation_from_employees'),
            'cowrs_eval' => Dict::getValue('evaluation_from_colleagues'),
            'reassignment' => Dict::getValue('reassignment'),
            'position_matching' => Dict::getValue('position_matching'),
            'cost_name' => Dict::getValue('cost_name'),
        ];

        #optimizeThis	[n-64xgv6ga]	Itt a classek betöltődése volt a sok, de érdemes lehet vele foglalkozni
        $attributeLabels['employeeTabHistory'] = App::getModelAttributesValues('Employee');
        $attributeLabels['employeeContractTabHistory'] = App::getModelAttributesValues('EmployeeContract');
        $attributeLabels['employeeSalaryTabHistory'] = App::getModelAttributesValues('EmployeeSalary');
        $attributeLabels['employeeCafeteriaTabHistory'] = App::getModelAttributesValues('EmployeeCafeteria');
        $attributeLabels['employeeAddressTabHistory'] = App::getModelAttributesValues('EmployeeAddress');
        $attributeLabels['employeeExtTabHistory'] = App::getModelAttributesValues('EmployeeExt');
        $attributeLabels['employeeExt2TabHistory'] = App::getModelAttributesValues('EmployeeExt2');
        $attributeLabels['employeeExt3TabHistory'] = App::getModelAttributesValues('EmployeeExt3');
        $attributeLabels['employeeExt4TabHistory'] = App::getModelAttributesValues('EmployeeExt4');
        $attributeLabels['employeeExt5TabHistory'] = App::getModelAttributesValues('EmployeeExt5');
        $attributeLabels['employeeExt6TabHistory'] = App::getModelAttributesValues('EmployeeExt6');
        $attributeLabels['employeeExt7TabHistory'] = App::getModelAttributesValues('EmployeeExt7');
        $attributeLabels['employeeDocsTabHistory'] = App::getModelAttributesValues('EmployeeDocs');
        $attributeLabels['employeeLabourDocsTabHistory'] = App::getModelAttributesValues('EmployeeDocs');
        $attributeLabels['employeeGroupTabHistory'] = App::getModelAttributesValues('EmployeeGroup');
        $attributeLabels['employeeCardTabHistory'] = App::getModelAttributesValues('EmployeeCard');
        $attributeLabels['employeeCostTabHistory'] = App::getModelAttributesValues('EmployeeCost');
        unset($attributeLabels['employeeCostTabHistory']['cost_id']);
        unset($attributeLabels['employeeCostTabHistory']['cost_center_id']);
        $attributeLabels['employeeCostTabHistory']['cost_id'] = Dict::getValue('cost');
        $attributeLabels['employeeCostTabHistory']['cost_center_id'] =
            App::getSetting('akh_tab_profitcentrum') > 0 ?
                Dict::getValue('akh_tab_employeetabs_employeecost') :
                Dict::getValue('costcenter');

        $attributeLabels['employeeBaseAbsenceTabHistory'] = 
            App::getModelAttributesValues('EmployeeBaseAbsence');
        $attributeLabels['employeePersonalCompetencyTabHistory'] = 
            App::getModelAttributesValues('EmployeeCompetency');
        $attributeLabels['employeeBaseArticleTabHistory'] = 
            App::getModelAttributesValues('EmployeeBaseArticle');

        $attributeLabels['employeeWorkActivityTabHistory'] = 
            App::getModelAttributesValues('EmployeeWorkActivity');
        $attributeLabels['employeeTravelCostTabHistory'] = 
            App::getModelAttributesValues('EmployeeTravelCost');
        $attributeLabels['employeeWorkActivityTabHistory']['employee_contract_number'] = 
            $attributeLabels['employeeBaseArticleTabHistory']['employee_contract_id'];
        $attributeLabels['employeeWorkActivityTabHistory']['work_activity_name'] = 
            Dict::getValue('work_activity_name');
        $attributeLabels['employeeWorkActivityTabHistory']['level_name'] = Dict::getValue('level');

        $attributeLabels['employeeSalaryTabHistory']['employee_contract_number'] = 
            $attributeLabels['employeeSalaryTabHistory']['employee_contract_id'];
        $attributeLabels['employeeCafeteriaTabHistory']['employee_contract_number'] = 
            $attributeLabels['employeeCafeteriaTabHistory']['employee_contract_id'];
        $attributeLabels['employeeAddressTabHistory']['emp_id'] = $attributeLabels['employeeTabHistory']['employee_id'];
        $attributeLabels['employeeExtTabHistory']['emp_id'] = $attributeLabels['employeeTabHistory']['employee_id'];
        $attributeLabels['employeeExt2TabHistory']['emp_id'] = $attributeLabels['employeeTabHistory']['employee_id'];
        $attributeLabels['employeeExt3TabHistory']['emp_id'] = $attributeLabels['employeeTabHistory']['employee_id'];
        $attributeLabels['employeeExt4TabHistory']['emp_id'] = $attributeLabels['employeeTabHistory']['employee_id'];
        $attributeLabels['employeeExt5TabHistory']['emp_id'] = $attributeLabels['employeeTabHistory']['employee_id'];
        $attributeLabels['employeeExt6TabHistory']['emp_id'] = $attributeLabels['employeeTabHistory']['employee_id'];
        $attributeLabels['employeeExt7TabHistory']['emp_id'] = $attributeLabels['employeeTabHistory']['employee_id'];
        $attributeLabels['employeeGroupTabHistory']['employee_contract_number'] = 
            $attributeLabels['employeeGroupTabHistory']['employee_contract_id'];
        $attributeLabels['employeeCardTabHistory']['employee_contract_number'] = 
            $attributeLabels['employeeCardTabHistory']['employee_contract_id'];
        $attributeLabels['employeeCostTabHistory']['employee_contract_number'] = 
            $attributeLabels['employeeCostTabHistory']['employee_contract_id'];

        $attributeLabels['employeeBaseAbsenceTabHistory']['employee_contract_number'] = 
            $attributeLabels['employeeBaseAbsenceTabHistory']['employee_contract_id'];
        $attributeLabels['employeeBaseAbsenceTabHistory']['base_absence_type_name'] =
            Dict::getValue('base_absence_type_id');

        $attributeLabels['employeePersonalCompetencyTabHistory']['employee_contract_number'] = 
            $attributeLabels['employeeBaseAbsenceTabHistory']['employee_contract_id'];
        $attributeLabels['employeePersonalCompetencyTabHistory']['competency_name'] = Dict::getValue('competency_name');
        $attributeLabels['employeePersonalCompetencyTabHistory']['competency_group_name'] = 
            Dict::getValue('competency_group_name');
        $attributeLabels['employeePersonalCompetencyTabHistory']['level_name'] = Dict::getValue('level_id');

        $attributeLabels['employeeBaseArticleTabHistory']['employee_contract_number'] = 
            $attributeLabels['employeeBaseArticleTabHistory']['employee_contract_id'];
        $attributeLabels['employeeBaseArticleTabHistory']['base_article_name'] = 
            $attributeLabels['employeeBaseArticleTabHistory']['base_article_id'];

        $attributeLabels['employeeTravelCostTabHistory']['employee_contract_number'] = 
            $attributeLabels['employeeTravelCostTabHistory']['employee_contract_id'];

        if (weHaveModule('ttwa-wwm') && App::hasRight('employee/employeeDeliveredArticleTab', 'view')) {
            $attributeLabels['employeeDeliveredArticleTabHistory'] = 
                App::getModelAttributesValues('ArticleTransactions');
            $attributeLabels['employeeDeliveredArticleTabHistory']['article_name'] = Dict::getValue('article_name');
            $attributeLabels['employeeDeliveredArticleTabHistory']['entitled_or_not'] = Dict::getValue('entitled');
            $attributeLabels['employeeDeliveredArticleTab']['signature'] = '';
        }

        $attributeLabels['importDialog'] = [
            'fullname' => Dict::getValue('name'),
        ];

        $attributeLabels[EmployeeControllerEnum::EMPLOYEE_LOCK_DIALOG][EmployeeControllerEnum::EMPLOYEE_LOCK_DIALOG_DATE_COLUMN_NAME] =
            Dict::getValue('valid_to');
        $attributeLabels[EmployeeControllerEnum::EMPLOYEE_UNLOCK_DIALOG][EmployeeControllerEnum::EMPLOYEE_UNLOCK_DIALOG_DATE_COLUMN_NAME] =
            Dict::getValue('valid_to');

        $attributeLabels['employeeChangePositionDialog']['position'] = Dict::getValue('employee_position_name');

        $dynamicTabs = Tab::getTabs();
        foreach ($dynamicTabs as $tab) {
            if (App::hasRight('employee/dinamicTab_' . $tab['tab_id'], 'view')) {
                $tabColumns = TabColumn::getColumns($tab['tab_id']);
                $dynamicTabId = 'dinamicTab_' . $tab['tab_id'];

                $attributeLabels[$dynamicTabId . 'History'] = [];

                if ($tab['connect'] === Tab::CONNECT_EMPLOYEE) {
                    $attributeLabels[$dynamicTabId . 'History']['emp_id'] = Dict::getValue('emp_id');
                } elseif ($tab['connect'] === Tab::CONNECT_EMPLOYEE_CONTRACT) {
                    $attributeLabels[$dynamicTabId . 'History']['employee_contract_number'] =
                        Dict::getValue('employee_contract_number');
                }

                foreach ($tabColumns as $tabColumn) {
                    $attributeLabels[$dynamicTabId . 'History'][$tabColumn['column_id']] =
                        Dict::getValue($tabColumn['dict_id']);
                }

                if ((int)$tab['valid']) {
                    $attributeLabels[$dynamicTabId . 'History']['valid_from'] = Dict::getValue('valid_from');
                    $attributeLabels[$dynamicTabId . 'History']['valid_to'] = Dict::getValue('valid_to');
                }
            }
        }

        $out = $attributeLabels; // saves it in cache
        return $out;
    }

    public function attributeLabels(): array
    {
        $columns = $this->baseAttributeLabels();
        $columns['uploadDialog'] = $this->getUploadDialogLabels($columns);

        return $columns;
    }
    
    private function getUploadDialogLabels($baseColumns = []): array
    {
        $acKey = AnyCache::key('getUploadDialogLabels', $baseColumns);
        if (AnyCache::has($acKey)) {
            return AnyCache::get($acKey);
        }

        $uplDialogColumns = [];

        $wizards = $this->wizards();
        $currentWizards = [];
        foreach ($wizards['dhtmlxGrid'] as $wizard) {
            $currentWizards[] = $wizard['contentId'];
        }

        foreach ($baseColumns as $tabId => $baseColumn) {
            $tabId = str_replace('History', '', $tabId);
            if (in_array($tabId, $currentWizards)) {
                foreach ($baseColumn as $column => $props) {
                    $uplDialogColumns[$tabId . '__' . $column] = $props;
                }
            }
        }

        AnyCache::set($acKey, $uplDialogColumns); #completeThis // nem tudjuk, milyen tábláktól függ
        return $uplDialogColumns;
    }
}