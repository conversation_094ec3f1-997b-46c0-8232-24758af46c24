<?php

/**
 * Mobile approve general terms and conditions controller
 */
Yang::import('application.components.mobile.*');

class ApproveGtcController extends Grid2Controller
{
	/**
	 * Grid2 konstruktor hívás
	 */
	public function __construct() {
		parent::__construct("approveGtc");
	}

	/**
	 * Biztonsági függvény #1
	 * @return array
	 */
	public function filters() {
        return [
            'accessControl', // perform access control for CRUD operations
		];
    }

	/**
	 * Biztonsági függvény #2
	 * @return array
	 */
	public function accessRules()
	{
        return
		[
            [
				'allow', // allow authenticated users to access all actions
            	'users' => ['@']
			],
            [
				'deny',  // deny all users
                'users' => ['*']
			]
		];
    }

	/**
	 * Mobile layout inicializálás
	 * @return void
	 */
	public function actionIndex($layout = '//mobile/mobile.basic', $view = '//mobile/base/approveGtc', $params = [])
    {
		$this->getContent();
	}

	/**
	 * Átadja a viewnak az adatokat
	 * @return void
	 */
	private function getContent()
	{
		$data                = [];
        $data["gtc_content"] = LongMessage::getMessage('general_terms_and_conditions');
        $data["approve_gtc"] = Dict::getValue("approveGtc");

		$this->beginContent('//mobile/mobile.basic', ['error' => '', 'errorTitle' => Dict::getValue("an_error_occured")]);
		$this->renderPartial('//mobile/base/approveGtc', $data);
		$this->endContent();
	}

    /**
     * Approve General terms and conditions
     *
     */
    public function actionApproveGtc()
    {
        $userid   = userID();
        $response = [];

        if (isset($userid)) {

            if ($this->getGtcIsApproved($userid) === 0) {

                $model              = new UserGtcLog();
                $model->user_id     = $userid;
                $model->approved_on = date('Y-m-d H:i:s');
                $model->device_type = UserDeviceLog::categorizeDevice();
                $model->save();

                $response['is_approved'] = 1;
                $response['url']         = baseURL() . "/" . Yang::getUserHome();
            } else {

                $response['$result'] = 0;
                $response['is_approved'] = 0;
                $response['msg']         = Dict::getValue("gtcAlreadyApproved");
            }

        } else {

            $response['is_approved'] = 0;
            $response['msg']         = Dict::getValue("userIdEmpty");
        }

        echo json_encode($response);
    }

    /**
     * Get general terms and conditions is approved
     *
     * @param string $userId
     * @return int
     */
    private function getGtcIsApproved(string $userId)
    {
        $model       = new UserGtcLog();
        $result      = $model->findByAttributes(['user_id' => $userId]);
        $isApproved = $result ? 1 : 0;

        return $isApproved;
    }

}

?>