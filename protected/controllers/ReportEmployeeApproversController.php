<?php

declare(strict_types=1);

Yang::loadComponentNamespaces('Core');
Yang::loadComponentNamespaces('WorkScheduleCore');
Yang::loadComponentNamespaces('Employee');


use Components\Employee\Builder\EmployeeMainDataBuilder;
use Components\Employee\Descriptor\EmployeeMainDataFilterDescriptor;
use Components\Employee\Descriptor\EmployeeMainDataDescriptor;

final class ReportEmployeeApproversController extends Grid2Controller
{
    private array $approverRelatedGroups;
    public const GROUP_START_SEPARATOR = '"{';
    public const GROUP_END_SEPARATOR = '}"';

    protected function G2BInit(): void
    {
        parent::setControllerPageTitleId("page_title_report_employee_approvers");

        $this->LAGridRights->overrideInitRights("paging", true);
        $this->LAGridRights->overrideInitRights("search", true);
        $this->LAGridRights->overrideInitRights("search_header", true);
        $this->LAGridRights->overrideInitRights("select", false);
        $this->LAGridRights->overrideInitRights("multi_select", false);
        $this->LAGridRights->overrideInitRights("column_move", true);
        $this->LAGridRights->overrideInitRights("col_sorting", true);
        $this->LAGridRights->overrideInitRights("reload_sortings", true);
        $this->LAGridRights->overrideInitRights("details", false);
        $this->LAGridRights->overrideInitRights("export_xls", false);
        $this->LAGridRights->overrideInitRights("export_xlsx", true);
        $this->LAGridRights->overrideInitRights("export_pdf_node", false);
        $this->LAGridRights->overrideInitRights("init_open_search", true);
        $this->LAGridRights->overrideInitRights("modify", false);
        $this->LAGridRights->overrideInitRights("add", false);
        $this->LAGridRights->overrideInitRights("delete", false);

        $this->LAGridDB->enableArrMode();
        $this->LAGridDB->setPrimaryKey("row_id", "dthmxGrid");

        //$this->LAGridDB->enableSQLMode();
        //$this->LAGridDB->setSQLSelection($this->getSQLForGridData(), "row_id");

        parent::setExportFileName(Dict::getValue("page_title_report_employee_approvers"));
        parent::G2BInit();
    }

    /**
     * @throws Exception
     */
    protected function dataArray($gridID, $filter)
    {
        $this->approverRelatedGroups = $this->getApproveRelatedGroups();
        return $this->getEmployeeData($filter);
    }

    /**
     * @throws Exception
     */
    protected function getEmployeeData(array $filter): array
    {
        if ($this->filterValidate($filter)) {
            //TODO: error message??
            return [];
        }
        $filterDescriptor = new EmployeeMainDataFilterDescriptor(
            $filter['valid_date'],
            $filter['valid_date'],
            [$filter['employee_contract']]
        );

        $employeeDescriptors = (new EmployeeMainDataBuilder)->build($filterDescriptor);
        $employeeDescriptor = $employeeDescriptors[$filter['employee_contract']][0];

        return $this->getApprovers($employeeDescriptor, $filter);
    }

    protected function filterValidate(array $filter): bool
    {
        $error = false;
        try {
            $this->validateDate($filter['valid_date'] ?? '-');
        } catch (Exception $e) {
            $error = true;
            Yang::log("Must be set date filter", 'error', 'ReportEmployeeApproversController');
        }
        if (empty($filter['employee_contract'])) {
            $error = true;
            Yang::log("Must be set employee filter", 'error', 'ReportEmployeeApproversController');
        }
        return $error;
    }

    /**
     * @throws Exception
     */
    protected function validateDate(string $value, string $format = 'Y-m-d'): void
    {
        $date = new DateTime($value);
        if (!$date || $date->format($format) !== $value) {
            throw new Exception("Invalid date");
        }
    }

    /**
     * @throws Exception
     */
    protected function getApprovers(EmployeeMainDataDescriptor $employeeDescriptor, array $filter): array
    {
        $published = Status::PUBLISHED;
        $defaultEnd = App::getSetting("defaultEnd");
        $filterDate = $filter['valid_date'];
        $approverSql = $this->getApproverSQL($filterDate);
        $where = $this->buildWhere($employeeDescriptor);
        $where .=
            ((!empty($filter['process_id']) && $filter['process_id'] !== 'ALL') ?
                " AND process_id = '{$filter['process_id']}'" :
                ''
            );

        $sql = "
            SELECT approver.process_id, 
                   user.username as approver_username, 
                   " . Employee::getParam('fullname', 'employee') . " as approver_fullname,
                   employee.emp_id as approver_emp_id,
                   '{$employeeDescriptor->getFullName()}' as employee_name,
                   '{$employeeDescriptor->getEmpId()}' as emp_id,
                   '{$employeeDescriptor->getCompanyName()}' as company_name,
                   '{$employeeDescriptor->getPayrollName()}' as payroll_name,
                   '{$employeeDescriptor->getUnitName()}' as unit_name,
                   '{$employeeDescriptor->getWorkgroupName()}' as workgroup_name,
                   '{$employeeDescriptor->getCompanyOrgGroup1Name()}' as company_org_group1_name,
                   '{$employeeDescriptor->getCompanyOrgGroup2Name()}' as company_org_group2_name,
                   '{$employeeDescriptor->getCompanyOrgGroup3Name()}' as company_org_group3_name                   
            FROM ($approverSql) as approver
            INNER JOIN user ON 
                    user.user_id = approver.approver_user_id
                AND user.status = {$published}
                AND '{$filterDate}' BETWEEN user.valid_from AND IFNULL(user.valid_to, '{$defaultEnd}')
                AND user.rolegroup_id <> 'a5b6bd79e008725744118c7c46e10cda'
            LEFT JOIN employee ON
                    employee.employee_id = user.employee_id
                AND employee.status = {$published}
                AND '{$filterDate}' BETWEEN employee.valid_from AND IFNULL(employee.valid_to, '{$defaultEnd}') 
            WHERE  
                $where
            ORDER BY approver.process_id, employee.first_name, user.username
        ";
        return dbFetchAll($sql);
    }

    /**
     * @throws Exception
     */
    protected function buildWhere(EmployeeMainDataDescriptor $employeeDescriptor): string
    {
        $linkType = (App::getSetting("approver_rule_group_link_type") == 'AND') ? 'AND' : 'OR';
        $groupStartSeparator = self::GROUP_START_SEPARATOR;
        $groupEndSeparator = self::GROUP_END_SEPARATOR;
        $missingGroupValues = [];
        $whereSql = '';
        foreach ($this->approverRelatedGroups as $art) {
            $groupValue = $employeeDescriptor->{'get' . $art->related_model . 'Id'}();
            if (empty($groupValue)) {
                $missingGroupValues[] = "Please set {$employeeDescriptor->getFullName()}'s {$art->related_model}";
                Yang::log(
                    "{$employeeDescriptor->getFullName()} - {$employeeDescriptor->getEmpId()} empty {$art->related_model}",
                    'error',
                    'ReportEmployeeApproversController'
                );
            }

            $whereSql .= ($whereSql === '' ? '' : $linkType);
            $whereSql .= " (approver.{$art->related_model} like '%{$groupStartSeparator}{$groupValue}{$groupEndSeparator}%' 
                OR approver.{$art->related_model}  like '%{$groupStartSeparator}ALL{$groupEndSeparator}%') 
            ";
        }
        if (!empty($missingGroupValues)) {
            throw new Exception(json_encode($missingGroupValues)); //TODO: Tibor tobb hiba??
        }
        return $whereSql;
    }

    protected function getApproverSQL(string $valid_date): string
    {
        $defaultEnd = App::getSetting("defaultEnd");
        $published = Status::PUBLISHED;
        $groupStartSeparator = self::GROUP_START_SEPARATOR;
        $groupEndSeparator = self::GROUP_END_SEPARATOR;
        $cols = '';
        foreach ($this->approverRelatedGroups as $art) {
            $cols .= "
                GROUP_CONCAT(
                    '{$groupStartSeparator}',
                    case when   related_model='{$art->related_model}' 
                            AND related_id = '{$art->related_id}' then related_value end,
                    '{$groupEndSeparator}'
                ) as {$art->related_model},";
        }
        return "
            SELECT process_id, approver_user_id,
                {$cols}
                valid_from,
                valid_to
            FROM approver
            WHERE   status = {$published}
                AND '{$valid_date}' BETWEEN valid_from AND IFNULL(valid_to, '{$defaultEnd}')
                AND approver_user_id <> ''
            GROUP BY process_id, approver_user_id
            ORDER BY process_id, approver_user_id
        ";
    }

    /**
     * @throws Exception
     */
    protected function getApproveRelatedGroups()
    {
        $criteria = new CDbCriteria;
        $criteria->addCondition("related_group = :related_group");
        $criteria->addCondition("status = :status");
        $criteria->params = [':status' => '' . Status::PUBLISHED, ':related_group' => 'Employee'];
        $art = (new ApproverRelatedGroup())->findAll($criteria);
        if ($art === null) {
            throw new Exception("ApproverRelatedGroup empty");
        }
        return $art;
    }

    public function search()
    {
        $rightsOptions = App::getLookup('approver_process_ids', false, null, [], true);
        $searchFields = $this->getPreDefinedSearchFromDb("employeeManagement", true);
        unset($searchFields['submit']);
        $searchFields['process_id'] =
            [
                'col_type' => 'combo',
                'options' => [
                    'mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY,
                    'array' => Yang::arrayMerge(
                        [["id" => "ALL", "value" => Dict::getValue("all")]],
                        $rightsOptions
                    )
                ],
                'label_text' => Dict::getValue("process_id"),
                'default_value' => 'ALL',
            ];

        $submit = ['submit' => ['col_type' => 'searchBarReinitGrid', 'width' => '*', 'label_text' => '']];
        return Yang::arrayMerge(
            $searchFields,
            $submit
        );
    }

    public function columns()
    {
        $columns = [
            'approver_fullname' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
            'approver_username' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
            'approver_emp_id' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
            'process_id' => [
                'export' => true,
                'col_type' => 'combo',
                'width' => 250,
                'options' => [
                    'mode' => Grid2Controller::G2BC_QUERY_MODE_ARRAY,
                    'array' => App::getLookup('approver_process_ids', false, null, [], true)
                ],
            ],
            'employee_name' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
            'emp_id' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
            'company_name' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
            'payroll_name' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
            'workgroup_name' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
            'unit_name' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
            'company_org_group1_name' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
            'company_org_group2_name' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
            'company_org_group3_name' => ['col_type' => 'ed', 'export' => true, 'width' => 250],
        ];
        return $columns;
        //return $this->columnRights($columns);
    }

    public function attributeLabels()
    {
        return [
            'approver_fullname' => Dict::getValue("approver_employeename"),
            'approver_username' => Dict::getValue("approver") . " " . Dict::getValue("username"),
            'approver_emp_id' => Dict::getValue("approver") . " " . Dict::getValue("emp_id"),
            'process_id' => Dict::getValue("process_id"),
            'employee_name' => Dict::getValue("employee"),
            'emp_id' => Dict::getValue("emp_id"),
            'company_name' => Dict::getValue("company_name"),
            'payroll_name' => Dict::getValue("payroll_name"),
            'workgroup_name' => Dict::getValue("workgroup_name"),
            'unit_name' => Dict::getValue("unit_name"),
            'company_org_group1_name' => Dict::getValue("company_org_group1"),
            'company_org_group2_name' => Dict::getValue("company_org_group2"),
            'company_org_group3_name' => Dict::getValue("company_org_group3"),
        ];
    }
}
