<?php

class TableDoubleRowCheckController extends Grid2Controller
{
	private $statusPublished;
	private $overlapDateTrue;
	private $overlapDateFalse;
	private $statusInvalid;
	private $statusSaved;
	private $statusLocked;
	private $customer;

	public function __construct()
	{
		parent::__construct("tableDoubleRowCheck");
		$this->statusPublished 		= Status::PUBLISHED;
		$this->statusInvalid 		= Status::INVALID;
		$this->statusSaved 			= Status::SAVED;
		$this->statusLocked 		= Status::LOCKED;
		$this->overlapDateFalse = "AND e1.valid_from = e2.valid_from AND e1.valid_to = e2.valid_to ";
		$this->overlapDateTrue 	= "AND e1.valid_from <= e2.valid_to	AND e1.valid_to >= e2.valid_from ";
		$this->customer = Yang::getParam('customerDbPatchName');
	}

	protected function G2BInit()
	{
		parent::setControllerPageTitleId("page_title_data_duplication_check");
		parent::setExportFileName(Dict::getValue("page_title_data_duplication_check"));

		$this->LAGridRights->overrideInitRights("paging", true);
		$this->LAGridRights->overrideInitRights("search_header", true);
		$this->LAGridRights->overrideInitRights("col_sorting", true);
		$this->LAGridRights->overrideInitRights("reload_sortings", true);
		$this->LAGridRights->overrideInitRights("export_xlsx", true);
		$this->LAGridRights->overrideInitRights("search", false);
		$this->LAGridRights->overrideInitRights("select", false);
		$this->LAGridRights->overrideInitRights("column_move", false);
		$this->LAGridRights->overrideInitRights("init_open_search", false);
		$this->LAGridRights->overrideInitRights("export_csv", false);
		$this->LAGridRights->overrideInitRights("export_xls", false);
		$this->LAGridRights->overrideInitRights("details", false);
		$this->LAGridRights->overrideInitRights("multi_select", false);
		$this->LAGridRights->overrideInitRights("export_pdf_node", false);

		$this->enableLAGrid();
		$this->LAGridDB->enableArrMode();

		$path = Yang::addAsset(Yang::getAlias('application.assets.base'), false, -1, true);
		Yang::registerScriptFile($path . '/tableDoubleRowCheck/js/tableDoubleRowCheck.js');

		parent::G2BInit();
	}

	public function actionDuplicatesDeleted()
	{
		$tableDouble[] = $this->employeeTabTable();
		$tableDouble[] = $this->companyMenuTable();
		$statusInvalid = $this->statusInvalid;
		$status = 1;
		foreach ($tableDouble as $valueType) {
			foreach ($valueType as $value) {
				$data = $this->tableData($value['idType'], $value['tableName'], $value['where'], $this->overlapDateFalse, $value['diffColumn']);
				$tableName =  $value['tableName'];
				foreach ($data as $dataValue) {
					$rowId = $dataValue['row_id'];
					$SQL = "UPDATE $tableName SET `status` = $statusInvalid, `modified_by` = 'tdrccontroller', `modified_on` = NOW() WHERE `row_id` = $rowId;";
					dbExecute($SQL);
				}
			}
		}

		$status = [
			'status'	=> $status,
			'title' 	=> Dict::getValue("page_title_data_duplication_check"),
			'msg'		=> Dict::getValue("duplicates_deleted"),
		];
		echo json_encode($status);
	}

	protected function dataArray($gridID, $filter)
	{
		$result = [];
		$allEmployeeData = [];
		$data = $this->allEmployee();
		foreach ($data as $value) {
			$allEmployeeData[$value['employee_id']]['emp_id'] = $value['emp_id'];
			$allEmployeeData[$value['employee_id']]['fullname'] = $value['fullname'];
			$allEmployeeData[$value['employee_id']]['employee_id'] = $value['employee_id'];
			$allEmployeeData[$value['employee_id']]['employee_contract_id'] = $value['employee_contract_id'];

			$allEmployeeData[$value['employee_contract_id']]['emp_id'] = $value['emp_id'];
			$allEmployeeData[$value['employee_contract_id']]['fullname'] = $value['fullname'];
			$allEmployeeData[$value['employee_contract_id']]['employee_id'] = $value['employee_id'];
			$allEmployeeData[$value['employee_contract_id']]['employee_contract_id'] = $value['employee_contract_id'];
		}
		$rowId = 0;
		if ($this->customer == 'rosenberger') {
			$result += $this->employeeCalcTableProcess($rowId, $allEmployeeData);
			$rowId = count($result);
		}

		$tableDouble[] = $this->employeeTabTable();
		$tableDouble[] = $this->companyMenuTable();

		foreach ($tableDouble as $valueType) {
			foreach ($valueType as $value) {
				if (!in_array($value['tableName'], ['employee_card', 'employee_base_absence']) && $this->customer == 'rosenberger')
				{
					$data = $this->tableData($value['idType'], $value['tableName'], '1', $this->overlapDateTrue, $value['diffColumn']);
				}
				else
				{
					$data = $this->tableData($value['idType'], $value['tableName'], $value['where'], $this->overlapDateTrue, $value['diffColumn'], $value['orderby']);
				}
				$result += $this->tableDataResult($allEmployeeData, $data, $rowId, $value['dict'], $value['idType'], $value['employeeTab']);
				$rowId = count($result);
			}
		}



		return $result;
	}

	private function tableDataResult(&$allEmployeeData, $data, $rowId, $tableName, $idType, $employeeTab)
	{
		$result = [];
		if ($employeeTab) {
			foreach ($data as $value) {
				$result[$rowId]['tablename'] = $tableName;
				$result[$rowId]['emp_id'] = $allEmployeeData[$value[$idType]]['emp_id'];
				$result[$rowId]['fullname'] = $allEmployeeData[$value[$idType]]['fullname'];
				$result[$rowId]['valid_from'] = $value['valid_from'];
				$result[$rowId]['valid_to']	= $value['valid_to'];
				$rowId++;
			}
		} else {
			foreach ($data as $value) {
				$result[$rowId]['tablename'] = $tableName;
				$result[$rowId]['fullname'] =  $value['name'];
				$result[$rowId]['valid_from'] = $value['valid_from'];
				$result[$rowId]['valid_to']	= $value['valid_to'];
				$rowId++;
			}
		}
		return $result;
	}

	private function allEmployee()
	{
		$statusPublished = $this->statusPublished;
		$SQL = "SELECT 
					e.emp_id,
					" . Employee::getParam('fullname','e') . " AS fullname,
					e.employee_id,
					ec.employee_contract_id
				FROM
					employee e 
				LEFT JOIN employee_contract ec ON ec.employee_id = e.employee_id
				WHERE
					e.`status` = $statusPublished
					AND ec.`status` = $statusPublished
				GROUP BY e.emp_id, ec.employee_contract_id
				";
		$result = dbFetchAll($SQL);
		return $result;
	}

	private function tableData($idType, $tableName, $where, $overlapDate, $diffColumn = null, $orderby = null)
	{
		$statusPublished = $this->statusPublished;
		$diffColumn == null ? $diffColumn = $idType : $diffColumn;
		$SQL = "SELECT 
					e2.row_id,
					e2.$diffColumn,
					e2.valid_from,
					e2.valid_to
				FROM $tableName e1
				LEFT JOIN $tableName e2 ON e1.$idType = e2.$idType
				WHERE
					$where
					$overlapDate
					AND e1.row_id <> e2.row_id
					AND e1.`status` = $statusPublished 
					AND e2.`status` = $statusPublished
				GROUP BY e1.$idType
				$orderby
				";
		$result = dbFetchAll($SQL);
		return $result;
	}

	private function employeeCalcTableSQL()
	{
		$stPublished	= $this->statusPublished;
		$stSaved		= $this->statusSaved;
		$stLocked		= $this->statusLocked;
		$SQL = "SELECT 
					calc1.employee_contract_id,
					calc1.day
				FROM 
					employee_calc calc1
				LEFT JOIN employee_calc calc2 ON calc1.employee_contract_id = calc2.employee_contract_id 
					AND calc1.day = calc2.day
					AND calc1.inside_type_id = calc2.inside_type_id
					AND calc1.shift_type_id = calc2.shift_type_id
					AND calc1.value = calc2.value
				WHERE
					calc1.row_id <> calc2.row_id
					AND calc1.status IN ($stPublished, $stSaved, $stLocked)
					AND calc2.status IN ($stPublished, $stSaved, $stLocked)
				GROUP BY calc1.employee_contract_id, calc1.day;
		";
		$result = dbFetchAll($SQL, 'employee_contract_id');

		return $result;
	}

	private function employeeCalcTableProcess($rowId, $allEmployeeData)
	{
		$datas = $this->employeeCalcTableSQL();
		$result = [];
		foreach($datas AS $key => $value) {
			$result[$rowId]['tablename'] = Dict::getValue('page_title_summary_sheet');;
			$result[$rowId]['datas'] =  '<b>' . Dict::getValue('emp_id') . ': </b> ' . $allEmployeeData[$key]['emp_id'] . ' - ' .
										'<b>' . Dict::getValue('fullname') . ': </b> ' . $allEmployeeData[$key]['fullname'] . ' - ' .
										'<b>' . Dict::getValue('day') . ': </b> ' . $value['day'];
			$rowId++;
		}
		return $result;
	}

	private function employeeTabTable()
	{
		$tableDouble = [];

		$tableDoubleOne['employeeTab'] = 1;
		$tableDoubleOne['idType'] = 'employee_id';
		$tableDoubleOne['tableName'] = 'employee';
		$tableDoubleOne['where'] = 'e1.employee_id = e2.employee_id ';
		$tableDoubleOne['orderby'] = 'ORDER BY e2.valid_from DESC';
		$tableDoubleOne['dict'] = Dict::getValue('tab_employeetabs_employee');
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 1;
		$tableDoubleOne['idType'] = 'employee_id';
		$tableDoubleOne['tableName'] = 'employee_contract';
		$tableDoubleOne['where'] = 'e1.employee_id = e2.employee_id AND e1.employee_contract_id = e2.employee_contract_id ';
		$tableDoubleOne['orderby'] = 'ORDER BY e2.valid_from DESC';
		$tableDoubleOne['dict'] = Dict::getValue('tab_employeetabs_employeecontract');
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 1;
		$tableDoubleOne['idType'] = 'employee_contract_id';
		$tableDoubleOne['tableName'] = 'employee_salary';
		$tableDoubleOne['where'] = 'e1.employee_contract_id = e2.employee_contract_id ';
		$tableDoubleOne['orderby'] = 'ORDER BY e2.valid_from DESC';
		$tableDoubleOne['dict'] = Dict::getValue('tab_employeetabs_employeesalary');
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 1;
		$tableDoubleOne['idType'] = 'employee_id';
		$tableDoubleOne['tableName'] = 'employee_address';
		$tableDoubleOne['where'] = 'e1.employee_id = e2.employee_id ';
		$tableDoubleOne['orderby'] = 'ORDER BY e2.valid_from DESC';
		$tableDoubleOne['dict'] = Dict::getValue('tab_employeetabs_employeeaddress');
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 1;
		$tableDoubleOne['idType'] = 'employee_id';
		$tableDoubleOne['tableName'] = 'employee_ext';
		$tableDoubleOne['where'] =  'e1.employee_id = e2.employee_id ';
		$tableDoubleOne['orderby'] = 'ORDER BY e2.valid_from DESC';
		$tableDoubleOne['dict'] = Dict::getValue('tab_employeetabs_employeeext');
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 1;
		$tableDoubleOne['idType'] = 'employee_id';
		$tableDoubleOne['tableName'] = 'employee_ext2';
		$tableDoubleOne['where'] =  'e1.employee_id = e2.employee_id ';
		$tableDoubleOne['orderby'] = 'ORDER BY e2.valid_from DESC';
		$tableDoubleOne['dict'] = Dict::getValue('tab_employeetabs_employeeext2');
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 1;
		$tableDoubleOne['idType'] = 'employee_id';
		$tableDoubleOne['tableName'] = 'employee_ext3';
		$tableDoubleOne['where'] =  'e1.employee_id = e2.employee_id ';
		$tableDoubleOne['orderby'] = 'ORDER BY e2.valid_from DESC';
		$tableDoubleOne['dict'] = Dict::getValue('tab_employeetabs_employeeext3');
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 1;
		$tableDoubleOne['idType'] = 'employee_id';
		$tableDoubleOne['tableName'] = 'employee_ext4';
		$tableDoubleOne['where'] =  'e1.employee_id = e2.employee_id ';
		$tableDoubleOne['orderby'] = 'ORDER BY e2.valid_from DESC';
		$tableDoubleOne['dict'] = Dict::getValue('tab_employeetabs_employeeext4');
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 1;
		$tableDoubleOne['idType'] = 'employee_contract_id';
		$tableDoubleOne['tableName'] = 'employee_card';
		$tableDoubleOne['where'] =  'e1.employee_contract_id = e2.employee_contract_id AND e1.card = e2.card ';
		$tableDoubleOne['orderby'] = 'ORDER BY e2.valid_from DESC';
		$tableDoubleOne['dict'] = Dict::getValue('tab_employeetabs_employeecard');
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 1;
		$tableDoubleOne['idType'] = 'employee_contract_id';
		$tableDoubleOne['tableName'] = 'employee_cost';
		$tableDoubleOne['where'] = 'e1.employee_contract_id = e2.employee_contract_id AND e1.cost_id = e2.cost_id AND e1.cost_center_id = e2.cost_center_id ';
		$tableDoubleOne['orderby'] = 'ORDER BY e2.valid_from DESC';
		$tableDoubleOne['dict'] = Dict::getValue('tab_employeetabs_employeecost');
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 1;
		$tableDoubleOne['idType'] = 'employee_contract_id';
		$tableDoubleOne['tableName'] = 'employee_base_absence';
		$tableDoubleOne['where'] = 'e1.employee_contract_id = e2.employee_contract_id AND e1.base_absence_type_id = e2.base_absence_type_id ';
		$tableDoubleOne['orderby'] = 'ORDER BY e2.valid_from DESC';
		$tableDoubleOne['dict'] = Dict::getValue('tab_employeetabs_employeebaseabsence');
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 1;
		$tableDoubleOne['idType'] = 'employee_contract_id';
		$tableDoubleOne['tableName'] = 'employee_group';
		$tableDoubleOne['where'] = 'e1.employee_contract_id = e2.employee_contract_id AND e1.group_id = e2.group_id	AND e1.group_value = e2.group_value ';
		$tableDoubleOne['orderby'] = 'ORDER BY e2.valid_from DESC';
		$tableDoubleOne['dict'] = Dict::getValue('tab_employeetabs_employee_group');
		$tableDouble[] = $tableDoubleOne;

		return $tableDouble;
	}

	private function companyMenuTable()
	{
		$tableDouble = [];

		$tableDoubleOne['employeeTab'] = 0;
		$tableDoubleOne['idType'] = 'company_id';
		$tableDoubleOne['tableName'] = 'company';
		$tableDoubleOne['where'] = 'e1.company_id = e2.company_id ';
		$tableDoubleOne['dict'] = Dict::getValue('page_title_company');
		$tableDoubleOne['diffColumn'] = 'company_name AS `name` ';
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 0;
		$tableDoubleOne['idType'] = 'payroll_id';
		$tableDoubleOne['tableName'] = 'payroll';
		$tableDoubleOne['where'] = 'e1.payroll_id = e2.payroll_id ';
		$tableDoubleOne['dict'] = Dict::getValue('page_title_payroll');
		$tableDoubleOne['diffColumn'] = 'payroll_name AS `name` ';
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 0;
		$tableDoubleOne['idType'] = 'workgroup_id';
		$tableDoubleOne['tableName'] = 'workgroup';
		$tableDoubleOne['where'] = 'e1.workgroup_id = e2.workgroup_id ';
		$tableDoubleOne['dict'] = Dict::getValue('page_title_workgroup');
		$tableDoubleOne['diffColumn'] = 'workgroup_name AS `name` ';
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 0;
		$tableDoubleOne['idType'] = 'unit_id';
		$tableDoubleOne['tableName'] = 'unit';
		$tableDoubleOne['where'] = 'e1.unit_id = e2.unit_id ';
		$tableDoubleOne['dict'] = Dict::getValue('page_title_unit');
		$tableDoubleOne['diffColumn'] = 'unit_name AS `name` ';
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 0;
		$tableDoubleOne['idType'] = 'cost_id';
		$tableDoubleOne['tableName'] = 'cost';
		$tableDoubleOne['where'] = 'e1.cost_id = e2.cost_id ';
		$tableDoubleOne['dict'] = Dict::getValue('page_title_cost');
		$tableDoubleOne['diffColumn'] = 'cost_name AS `name` ';
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 0;
		$tableDoubleOne['idType'] = 'cost_center_id';
		$tableDoubleOne['tableName'] = 'cost_center';
		$tableDoubleOne['where'] = 'e1.cost_center_id = e2.cost_center_id ';
		$tableDoubleOne['dict'] = Dict::getValue('page_title_cost_center');
		$tableDoubleOne['diffColumn'] = 'cost_center_name AS `name` ';
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 0;
		$tableDoubleOne['idType'] = 'employee_position_id';
		$tableDoubleOne['tableName'] = 'employee_position';
		$tableDoubleOne['where'] = 'e1.employee_position_id = e2.employee_position_id ';
		$tableDoubleOne['dict'] = Dict::getValue('page_title_employee_position');
		$tableDoubleOne['diffColumn'] = 'employee_position_name AS `name` ';
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 0;
		$tableDoubleOne['idType'] = 'company_org_group_id';
		$tableDoubleOne['tableName'] = 'company_org_group1';
		$tableDoubleOne['where'] = 'e1.company_org_group_id = e2.company_org_group_id ';
		$tableDoubleOne['dict'] = Dict::getValue('page_title_companyorggroup1');
		$tableDoubleOne['diffColumn'] = 'company_org_group_name AS `name` ';
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 0;
		$tableDoubleOne['idType'] = 'company_org_group_id';
		$tableDoubleOne['tableName'] = 'company_org_group2';
		$tableDoubleOne['where'] = 'e1.company_org_group_id = e2.company_org_group_id ';
		$tableDoubleOne['dict'] = Dict::getValue('page_title_companyorggroup2');
		$tableDoubleOne['diffColumn'] = 'company_org_group_name AS `name` ';
		$tableDouble[] = $tableDoubleOne;

		$tableDoubleOne['employeeTab'] = 0;
		$tableDoubleOne['idType'] = 'company_org_group_id';
		$tableDoubleOne['tableName'] = 'company_org_group3';
		$tableDoubleOne['where'] = 'e1.company_org_group_id = e2.company_org_group_id ';
		$tableDoubleOne['dict'] = Dict::getValue('page_title_companyorggroup3');
		$tableDoubleOne['diffColumn'] = 'company_org_group_name AS `name` ';
		$tableDouble[] = $tableDoubleOne;

		return $tableDouble;
	}

	protected function getStatusButtons($gridID = null)
	{
		if (!isset($gridID)) {
			$gridID = 'dhtmlxGrid';
		} elseif (empty($gridID)) {
			$gridID = 'dhtmlxGrid';
		}

		$buttons = [];
		$originalButtons = parent::getStatusButtons($gridID);

		if (App::getRight(null, "su")) {
			$informacio = $this->getInformacio();
			$title = $informacio['title'];
			$message = $informacio['message'];
			$width = '500';
			$height = '650';
			$bgColor = '#039BE5';
			$buttons["infoDialog"] = [
				"type" => "button",
				"id" => "infoDialog",
				"class" => "infoDialog",
				"name" => "infoDialog",
				"img" => "/images/status_icons/information.png",
				"label" => Dict::getValue('informations'),
				"onclick" => 	"notifidialog('" . $title . "', '" . $message . "', true, function() {}, '" . $bgColor . "','" . $width . "','" . $height . "')",
			];
		}

		$buttons["duplicatesDeleted"] = [
			"type" => "button",
			"id" => "duplicatesDeleted",
			"class" => "duplicatesDeleted",
			"name" => "duplicatesDeleted",
			"img" => "/images/status_icons/st_build.png",
			"label" => "duplicatesDeleted",
			"onclick" => 	"duplicatesDeleted('" . Dict::getValue("attention_repair") . "');",
		];

		return Yang::arrayMerge($originalButtons, $buttons);
	}

	protected function getInformacio()
	{
		$info = Dict::getValue('checked_places') . '
				<table>
				<tr>
					<td>
						<b>' . Dict::getValue('page_title_employee_management') . ': </b>
						<ul>
						<li>' . Dict::getValue('tab_employeetabs_employee') . '</li>
						<li>' . Dict::getValue('tab_employeetabs_employeecontract') . '</li>
						<li>' . Dict::getValue('tab_employeetabs_employeesalary') . '</li>
						<li>' . Dict::getValue('tab_employeetabs_employeeaddress') . '</li>
						<li>' . Dict::getValue('tab_employeetabs_employeeext') . '</li>
						<li>' . Dict::getValue('tab_employeetabs_employeeext2') . '</li>
						<li>' . Dict::getValue('tab_employeetabs_employeeext3') . '</li>
						<li>' . Dict::getValue('tab_employeetabs_employeeext4') . '</li>
						<li>' . Dict::getValue('tab_employeetabs_employeecard') . '</li>
						<li>' . Dict::getValue('tab_employeetabs_employeecost') . '</li>
						<li>' . Dict::getValue('tab_employeetabs_employeebaseabsence') . '</li>
						<li>' . Dict::getValue('tab_employeetabs_employee_group') . '</li>
						</ul>
					</td>
				</tr>
				<tr>
					<td>
						<b>' . Dict::getValue('menu_item_company_data') . ': </b>
						<ul>
						<li>' . Dict::getValue('page_title_company') . '</li>
						<li>' . Dict::getValue('page_title_payroll') . '</li>
						<li>' . Dict::getValue('page_title_workgroup') . '</li>
						<li>' . Dict::getValue('page_title_unit') . '</li>
						<li>' . Dict::getValue('page_title_cost') . '</li>
						<li>' . Dict::getValue('page_title_cost_center') . '</li>
						<li>' . Dict::getValue('page_title_employee_position') . '</li>
						<li>' . Dict::getValue('page_title_companyorggroup1') . '</li>
						<li>' . Dict::getValue('page_title_companyorggroup2') . '</li>
						<li>' . Dict::getValue('page_title_companyorggroup3') . '</li>
						</ul>
					</td>
				</tr>
				</table>';
		$info = str_replace(PHP_EOL, '', $info);

		$magyarazat = [
			'title' => Dict::getValue('informations'),
			'message' => $info
		];

		return $magyarazat;
	}

	public function columns()
	{
		$columns =
		[
			'tablename'		=> ['width' => 60, 'col_type' => 'ed', 'grid' => true, 'export' => true],
			'emp_id'		=> ['width' => 60, 'col_type' => 'ed', 'grid' => true, 'export' => true],
			'fullname'		=> ['width' => 120, 'col_type' => 'ed', 'grid' => true, 'export' => true],
			'valid_from' 	=> ['width' => 120, 'col_type' => 'ed', 'grid' => true, 'export' => true],
			'valid_to' 		=> ['width' => 120, 'col_type' => 'ed', 'grid' => true, 'export' => true],
		];

		return $columns;
	}

	public function attributeLabels()
	{
		$attributeLabels = [
			'tablename'		=> Dict::getValue("location_duplicate_data"),
			'emp_id'		=> Dict::getValue('emp_id'),
			'fullname'		=> Dict::getValue('fullname'),
			'valid_from'	=> Dict::getValue('valid_from'),
			'valid_to'		=> Dict::getValue('valid_to'),
		];

		return $attributeLabels;
	}

	public function filters()
	{
		return [
			'accessControl', // perform access control for CRUD operations
		];
	}

	public function accessRules()
	{
		return [
			['allow', // allow updateAutomaticRegistration actions
				'actions' => ['updateAutomaticRegistration', 'index'],
				'users' => ['*'],
			],

			['allow', // allow authenticated users to access all actions
				'users' => ['@'],
			],

			['deny',  // deny all users
				'users' => ['*'],
			],
		];
	}
}
