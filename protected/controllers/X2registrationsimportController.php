<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\API\RegistrationImportFromX2;
	use app\components\App;
	use app\components\Dict;
	use Yang;

`/yii2-only';


#yii2: done

class X2registrationsimportController extends Controller
{

	public $layout = '//layouts/main';

	public function actionIndex()
	{
		// Tesztelésre
		error_reporting (E_ALL & ~E_DEPRECATED & ~E_STRICT );
		ini_set('max_execution_time', 300);

		$this->pageTitle = Yang::appName()." - ".Dict::getValue("page_title_registration_import");
		
		$process_local_file = requestParam('process_local_file');

		if (true)
		//if (App::getRight("wwm/registrationsimport","view"))
		{
			$ts = Yang::getParam('terminal_ftp_settings');
			if (is_null($ts)) { return; }
//			print_r($ts);
			if(is_array($ts[0])){
				foreach ($ts as $terminal_ftp_settings) {
//					print_r($terminal_ftp_settings);
					$rim= new RegistrationImportFromX2('X2RegistrationsImport');
					$result= $rim->run($terminal_ftp_settings, $process_local_file);
//					echo json_encode($result);
				}
			} else {
//				print_r($ts);
				$rim= new RegistrationImportFromX2('X2RegistrationsImport');
				$result= $rim->run($ts, $process_local_file);
//				print_r($result);
			}
		}
		else
		{
			$this->redirect(array(Yang::getParam('permdeniedUrl')));
		}
	}
}
?>
