<?php

'yii2-only`;

	namespace app\controllers;
	use Yang;

`/yii2-only';


#yii2: done

class GetTableDataOnScreenController extends Controller 
{
	public $layout = 'main';
	private $controllerID;
	
	public function __construct($id, $module = null) {
        $this->controllerID = "getTableDataOnScreen";
        
        parent::__construct($id, $module);
    }
	
	public function actionIndex()
	{
		$this->render('index', []);
	}
	
	public function actionGetTableData() {

		$dbConn = Yii::app()->BOSMSSQL;
		
		$sql = "";
		
		$command = $dbConn->createCommand($sql);
		$results = $command->queryAll();
		echo '<pre>'; print_r($results); echo '</pre>';
	}

	public function actionSec() {
		print '-------------------------------------------------';
		$keys = Yii::app()->phpseclib->createRSA()->createKey();
		print_r($keys);
		print "\n". '-------------------------------------------------';
	}

	public function actionUpdateTableIds()
	{
		$resp = [];
		$end = App::getSetting("defaultEnd");
		$pub = Status::PUBLISHED;
		$preSQL = "
			CREATE TABLE `temp_update_ids_in_tables`
			SELECT
				e.`employee_id` AS old_employee_id,
				ec.`employee_contract_id` AS old_employee_contract_id,
				IF(e.`tax_number` IS NOT NULL AND e.`tax_number` <> '', e.`tax_number`, MD5(CONCAT(e.`company_id`, e.`emp_id`))) AS new_employee_id,
				IF(e.`tax_number` IS NOT NULL AND e.`tax_number` <> '', CONCAT(e.`tax_number`, ec.`employee_contract_number`), MD5(CONCAT(IF(e.`tax_number` IS NOT NULL AND e.`tax_number` <> '', e.`tax_number`, MD5(CONCAT(e.`company_id`, e.`emp_id`))), ec.`employee_contract_number`))) AS new_employee_contract_id,
				e.`valid_from` AS employee_valid_from,
				IFNULL(e.`valid_to`, '{$end}') AS employee_valid_to,
				ec.`valid_from` AS employee_contract_valid_from,
				IFNULL(ec.`valid_to`, '{$end}') AS employee_contract_valid_to
			FROM `employee` e
			LEFT JOIN `employee_contract` ec ON
					ec.`status` = {$pub}
				AND ec.`employee_id` = e.`employee_id`
				AND e.`valid_from` <= IFNULL(ec.`valid_to`, '{$end}') AND ec.`valid_from` <= IFNULL(e.`valid_to`, '{$end}')
				AND e.`valid_from` <= IFNULL(ec.`ec_valid_to`, '{$end}') AND ec.`ec_valid_from` <= IFNULL(e.`valid_to`, '{$end}')
			WHERE e.`status` = {$pub}
		";
		dbExecute($preSQL);
		$conn = Yii::app()->db;
		$dbSchema = $conn->schema;
		$tables = $dbSchema->getTables();
		foreach($tables as $tbl)
		{
			if ($tbl->rawName != "`v_employee_calc_per_day`" && $tbl->rawName != "`payroll_errors`")
			{
				if (in_array("status", $tbl->columnNames)) {
					$statusWHERESQL = "WHERE t.`status` = " . $pub;
				} else { $statusWHERESQL = ""; }

				if (in_array("employee_id", $tbl->columnNames))
				{
					if (in_array("valid_from", $tbl->columnNames) && in_array("valid_to", $tbl->columnNames))
					{
						$SQL = "
							UPDATE {$tbl->rawName} AS t
							JOIN `temp_update_ids_in_tables` tid ON
									tid.`old_employee_id` = t.`employee_id`
								AND t.`valid_from` <= tid.`employee_valid_to` AND tid.`employee_valid_from` <= IFNULL(t.`valid_to`, '2038-01-01')
							SET t.`employee_id` = tid.`new_employee_id`
							{$statusWHERESQL}
						";
						dbExecute($SQL);
					} else if (in_array("day", $tbl->columnNames))
					{
						$SQL = "
							UPDATE {$tbl->rawName} AS t
							JOIN `temp_update_ids_in_tables` tid ON
									tid.`old_employee_id` = t.`employee_id`
								AND t.`day` BETWEEN tid.`employee_valid_from` AND tid.`employee_valid_to`
							SET t.`employee_id` = tid.`new_employee_id`
							{$statusWHERESQL}
						";
						dbExecute($SQL);
					} else if (in_array("date", $tbl->columnNames))
					{
						$SQL = "
							UPDATE {$tbl->rawName} AS t
							JOIN `temp_update_ids_in_tables` tid ON
									tid.`old_employee_id` = t.`employee_id`
								AND t.`date` BETWEEN tid.`employee_valid_from` AND tid.`employee_valid_to`
							SET t.`employee_id` = tid.`new_employee_id`
							{$statusWHERESQL}
						";
						dbExecute($SQL);
					} else
					{
						$SQL = "
							UPDATE {$tbl->rawName} AS t
							JOIN `temp_update_ids_in_tables` tid ON
									tid.`old_employee_id` = t.`employee_id`
								AND CURDATE() BETWEEN tid.`employee_valid_from` AND tid.`employee_valid_to`
							SET t.`employee_id` = tid.`new_employee_id`
							{$statusWHERESQL}
						";
						dbExecute($SQL);
					}
				}
				if (in_array("employee_contract_id", $tbl->columnNames))
				{
					if (in_array("valid_from", $tbl->columnNames) && in_array("valid_to", $tbl->columnNames))
					{
						$SQL = "
							UPDATE {$tbl->rawName} AS t
							JOIN `temp_update_ids_in_tables` tid ON
									tid.`old_employee_contract_id` = t.`employee_contract_id`
								AND t.`valid_from` <= tid.`employee_contract_valid_to` AND tid.`employee_contract_valid_from` <= IFNULL(t.`valid_to`, '2038-01-01')
							SET t.`employee_contract_id` = tid.`new_employee_contract_id`
							{$statusWHERESQL}
						";
						dbExecute($SQL);
					} else if (in_array("day", $tbl->columnNames))
					{
						$SQL = "
							UPDATE {$tbl->rawName} AS t
							JOIN `temp_update_ids_in_tables` tid ON
									tid.`old_employee_contract_id` = t.`employee_contract_id`
								AND t.`day` BETWEEN tid.`employee_contract_valid_from` AND tid.`employee_contract_valid_to`
							SET t.`employee_contract_id` = tid.`new_employee_contract_id`
							{$statusWHERESQL}
						";
						dbExecute($SQL);
					} else if (in_array("date", $tbl->columnNames))
					{
						$SQL = "
							UPDATE {$tbl->rawName} AS t
							JOIN `temp_update_ids_in_tables` tid ON
									tid.`old_employee_contract_id` = t.`employee_contract_id`
								AND t.`date` BETWEEN tid.`employee_contract_valid_from` AND tid.`employee_contract_valid_to`
							SET t.`employee_contract_id` = tid.`new_employee_contract_id`
							{$statusWHERESQL}
						";
						dbExecute($SQL);
					} else
					{
						$SQL = "
							UPDATE {$tbl->rawName} AS t
							JOIN `temp_update_ids_in_tables` tid ON
									tid.`old_employee_contract_id` = t.`employee_contract_id`
								AND CURDATE() BETWEEN tid.`employee_contract_valid_from` AND tid.`employee_contract_valid_to`
							SET t.`employee_contract_id` = tid.`new_employee_contract_id`
							{$statusWHERESQL}
						";
						dbExecute($SQL);
					}
				}
			}
		}
		dbExecute("DROP TABLE temp_update_ids_in_tables;");
	}


	//TODO: Flex WD szinkron betöltése után a 2 -es szerződés miatt kellett a következő két függvény, NE töröld ki!
	public function actionEc2GroupFix() {
		$dbConn = Yii::app()->db;

		$sql = "SELECT * FROM employee_contract 
				WHERE 
					employee_contract_number = 2 
					AND status = 2 
					AND CURDATE() BETWEEN valid_from AND valid_to 
					AND CURDATE() BETWEEN ec_valid_from AND ec_valid_to";
		$command = $dbConn->createCommand($sql);
		$results = $command->queryAll();

		foreach ($results as $key => $value) {
			print $key;
			print ' ';
			$ec2 = $value['employee_contract_id'];
			print $ec2;
			print ' ';
			$ec2_hossz = strlen($ec2) -1;
			$ec2_jav = str_split($ec2, $ec2_hossz);
			$ec1 = $ec2_jav[0] . '1';
			print $ec1;
			print ' ';
			$sql_group = "INSERT INTO employee_group (`employee_contract_id`, `group_id`, `group_value`, `valid_from`, `valid_to`, `status`, `created_by`, `created_on`)
							SELECT '$ec2', `group_id`, `group_value`, `valid_from`, `valid_to`, `status`, 'krobi', '2020-11-13 15:00' 
							FROM employee_group
							WHERE employee_contract_id = '$ec1';";
			print $sql_group;
			$command = $dbConn->createCommand($sql_group);
			$results2 = $command->execute();
			print '<br>';
		}
	}

	public function actionEc2CardFix() {
		$dbConn = Yii::app()->db;

		$sql = "SELECT * FROM employee_contract 
				WHERE 
					employee_contract_number = 2 
					AND status = 2 
					AND CURDATE() BETWEEN valid_from AND valid_to 
					AND CURDATE() BETWEEN ec_valid_from AND ec_valid_to";
		$command = $dbConn->createCommand($sql);
		$results = $command->queryAll();

		foreach ($results as $key => $value) {
			print $key;
			print ' ';
			$ec2 = $value['employee_contract_id'];
			print $ec2;
			print ' ';
			$ec2_hossz = strlen($ec2) -1;
			$ec2_jav = str_split($ec2, $ec2_hossz);
			$ec1 = $ec2_jav[0] . '1';
			print $ec1;
			print ' ';
			$sql_group = "INSERT INTO employee_card (`employee_contract_id`, `card`, `valid_from`, `valid_to`, `status`, `created_by`, `created_on`, `card_type`, `temporarily_blocked`, `rgnet_check_freq`)
							SELECT '$ec2', `card`, `valid_from`, `valid_to`, 2, 'krobi', '2020-11-13 15:00', 1, 0, 5
							FROM employee_card
							WHERE 
								employee_contract_id = '$ec1'
								AND status = 2
								AND CURDATE() BETWEEN valid_from AND valid_to;";
			
			//print $sql_group;
			$command = $dbConn->createCommand($sql_group);
			$results2 = $command->execute();
			
			$ec_valid_from = $value['valid_from'];
			$sql_card = "UPDATE employee_card 	
						SET valid_to = '$ec_valid_from'
						WHERE
							employee_contract_id = '$ec1'
							AND status = 2
							AND CURDATE() BETWEEN valid_from AND valid_to;";
			$command = $dbConn->createCommand($sql_card);
			$results3 = $command->execute();
			//print $sql_card;
			print '<br>';
		}

	}
}