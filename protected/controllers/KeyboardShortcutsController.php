<?php

class KeyboardShortcutsController extends Grid2Controller
{
	public function actionIndex($layout = '//Grid2/layouts/indexLayout', $view = '/Grid2/index', $params = array()) {
		$path = Yang::addAsset(Yang::getAlias('application.assets.base.user'), false, -1, true);
		parent::actionIndex($layout, $view);
	}

	public function __construct()
	{
		parent::__construct("keyboardShortcuts");
	}

	protected function G2BInit() {
		$this->LAGridDB->setModelName("KeyboardShortcuts");

		parent::setControllerPageTitleId("page_title_keyboardShortcuts");

		$this->LAGridRights->overrideInitRights("add",				true);
		$this->LAGridRights->overrideInitRights("edit",				true);
		$this->LAGridRights->overrideInitRights("delete",			true);
		$this->LAGridRights->overrideInitRights("modify",			true);
		$this->LAGridRights->overrideInitRights("paging",			true);
		$this->LAGridRights->overrideInitRights("search",			true);
		$this->LAGridRights->overrideInitRights("search_header",	true);
		$this->LAGridRights->overrideInitRights("select",			true);
		$this->LAGridRights->overrideInitRights("multi_select",		false);
		$this->LAGridRights->overrideInitRights("column_move",		true);
		$this->LAGridRights->overrideInitRights("col_sorting",		true);
		$this->LAGridRights->overrideInitRights("reload_sortings",	true);
		$this->LAGridRights->overrideInitRights("details",			false);

		$this->LAGridRights->overrideInitRights("export_xls",		false);
		$this->LAGridRights->overrideInitRights("export_xlsx",		true);
		$this->LAGridRights->overrideInitRights("export_pdf_node",	false);
		
		$filter = requestParam("searchInput");
		$cid	= $filter["controller_id"];
		if($cid) {
            $selFilter .= " AND controller_id LIKE '%" . $cid . "%'";
        }
		
		$m = new KeyboardShortcuts;
		$c = new CDbCriteria();
		$c->condition = "1 ".$selFilter;
		$c->order = "`controller_id`, `action`";

		$this->LAGridDB->setModelSelection($m, $c);

		parent::setExportFileName(Dict::getValue("export_file_keyboardShortcuts"));
		parent::G2BInit();
	}

	public function search() {

		return [
			'controller_id'	    => [
				'col_type'	=> 'auto',
				'columnId'	=> 'controller_id',
				'width'		=> '*',
				'label_text'=> Dict::getValue("controller_id"),
				'options'	=>	[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "SELECT DISTINCT `controller_id` as id, `controller_id` as value
								FROM `auth_controller`
								WHERE `controller_id` is not null
								ORDER BY value",
				],
			],
			'submit'		=> array('col_type'=>'searchBarReloadGrid', 'width'=>'*', 'label_text'=>''),
		];
	}

	public function columns()
	{
		$langOptions = App::getLookup('lang');

		$statusOptions = App::getLookup('status',false,'2',[Status::DRAFT, Status::PUBLISHED, Status::STATUS_REMOVED, Status::STATUS_LOCKED]);

		$columns = [
			'controller_id'		=> [	'export'=> true, 'report_width' => 10, 'col_type'=>'ed', 'width' => 150],
			'action'			=> [	'export'=> true, 'report_width' => 10, 'col_type'=>'ed', 'width' => 150],
			'shortcut'			=> [	'export'=> true, 'report_width' => 10, 'col_type'=>'ed', 'width' => 150],
			'dict_id'			=> [	'export'=> true, 'report_width' => 10, 'col_type'=>'ed', 'width' => 150],

			'note'				=> [	'export'=> false, 'col_type'=>'ed', 'width' => 150],
			'status'			=> [
										'export'		=> false,
										'col_type'		=> 'combo',
										'options'		=>	array(
																'mode'=>Grid2Controller::G2BC_QUERY_MODE_ARRAY,'array'=>$statusOptions
															),
										'col_align'		=> 'center',
										'width' => 100,
									],
		];

		return $columns;
	}
}
?>