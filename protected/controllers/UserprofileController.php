<?php

'yii2-only`;

	namespace app\controllers;
	use app\components\App;
	use app\components\PersonalProperties;
	use app\models\User;
	use app\models\UserProfile;
	use app\models\flow\ClientGroup;
	use Yang;

`/yii2-only';


#yii2: done

class UserprofileController extends GridController
{

	public $layout = 'full'; // main

	public function __construct()
	{
		parent::__construct("userprofile");
		$this->setModelName("UserProfile");
		parent::setTitle("Felhasználó profil adatok");
		parent::enableSubgrid(true);
		parent::setRights(/* add */true, /* mod */ true, /* imod */ false, /* del */ true, /* exp */ true, /* $sel */ false, /* $msel */ false, /* $details */ true);

		parent::selectConditions('( ("{UserProfile_valid_from}" BETWEEN `valid_from` AND IFNULL(`valid_to`,\'' . App::getSetting("defaultEnd") . '\'))'
				. '	OR ("{UserProfile_valid_to}" BETWEEN `valid_from` AND IFNULL(`valid_to`,\'' . App::getSetting("defaultEnd") . '\') )'
				. '	OR (`valid_from` BETWEEN "{UserProfile_valid_from}" AND IF("{UserProfile_valid_to}"="", \'' . App::getSetting("defaultEnd") . '\', "{UserProfile_valid_to}") )'
				. ' )'
		);
	}

	/**
	 * Returns the data model based on the primary key given in the GET variable.
	 * If the data model is not found, an HTTP exception will be raised.
	 * @param integer $id the ID of the model to be loaded
	 * @return User the loaded model
	 * @throws CHttpException
	 */
	public function loadModel($user_id = false)
	{
		if (!$user_id)
		{
			$user_id = userID();
		}
		//$model=  UserProfile::model()->findByPk($id);
		$model = UserProfile::model()->findAllByAttributes(array('user_id' => $user_id));
		if (sizeof($model) == 0)
		{
			$new_model					 = new UserProfile;
			$new_model->user_id			 = $user_id;
			$new_model->created_on		 = date('Y-m-d H:i:s');
			$model[0]					 = $new_model;
		}
		if ($model === null)
			throw new CHttpException(404, 'The requested page does not exist. (' . $user_id . ')');
		return $model[0];
	}

	public function gridFilters()
	{
		return array(
			'valid_from' => array('model' => 'UserProfile', 'columnId' => 'valid_from', 'default' => date("Y-m-d"), 'col_type' => 'ed', 'dPicker' => true),
			'valid_to'	 => array('model' => 'UserProfile', 'columnId' => 'valid_to', 'default' => date("Y-m-d"), 'col_type' => 'ed', 'dPicker' => true),
		);
	}

	/**
	 * @return array customized grid columns (name=>params)
	 */
	public function getColumns()
	{
		return array(
			'user_id'			 => array('export' => false, 'grid' => false, 'window' => true, 'col_type' => 'autco', 'options' => array('comboModel' => 'User', 'comboId' => 'user_id', 'comboValue' => 'username')),
			'model'				 => array('export' => false, 'grid' => false, 'window' => true, 'col_type' => 'ed'),
			'property'			 => array('export' => false, 'grid' => false, 'window' => true, 'col_type' => 'ed'),
			'value_integer'		 => array('export' => false, 'grid' => false, 'window' => true, 'col_type' => 'ed'),
			'value_string'		 => array('export' => false, 'grid' => false, 'window' => true, 'col_type' => 'ed'),
			'value_boolean'		 => array('export' => false, 'grid' => false, 'window' => true, 'col_type' => 'ed'),
			'created_by'		 => array('export' => false, 'grid' => false, 'window' => false, 'col_type' => 'combo', 'options' => array('comboModel' => 'User', 'comboId' => 'user_id', 'comboValue' => 'username')),
			'created_on'		 => array('export' => false, 'grid' => false, 'window' => false, 'col_type' => 'ed', 'dPicker' => true),
			'modified_by'		 => array('export' => false, 'grid' => false, 'window' => false, 'col_type' => 'combo', 'options' => array('comboModel' => 'User', 'comboId' => 'user_id', 'comboValue' => 'username')),
			'modified_on'		 => array('export' => false, 'grid' => false, 'window' => false, 'col_type' => 'ed', 'dPicker' => true),
		);
	}

	public function actionIndex()
	{
		$this->actionChangePassword();
	}

	public function actionChangePassword()
	{
//     $model= $this->loadModel(userID());
		$ok		 = true;
		$user	 = $this->getUser();
//		$client_groups= new ClientGroup;

		$message = '';
		$error	 = '';

		// Ellenőrzés:
		if (isset($_POST['UserProfile']['submit']))
		{
			// - Jelenlegi jelszó
			$good_current_password = false;
			if (isset($_POST['UserProfile']['current_password']))
			{
				if (hash('sha512', $_POST['UserProfile']['current_password']) == $user['password'])
				{
					$good_current_password = true;
				}
			}

			$new_password = '';
			// - Új jelszó
			if (isset($_POST['UserProfile']['new_password']))
			{
				$new_password = $_POST['UserProfile']['new_password'];
			}

			$new_password_retype = '';
			// - Új jelszó ismétlése
			if (isset($_POST['UserProfile']['new_password_retype']))
			{
				$new_password_retype = $_POST['UserProfile']['new_password_retype'];
			}

			if ($new_password != '' || $new_password_retype != '')
			{
				if ($new_password != '' && $new_password_retype != '' && $new_password == $new_password_retype)
				{
					//$error.= __LINE__.'HIBA! ';
				}
				else
				{
					$ok = false;
					$error.= 'HIBA! A jelszavak nem egyeznek! (' . __LINE__ . ')<br>';
				}

				if (!$good_current_password)
				{
					$ok = false;
					$error.= 'HIBA! A jelenlegi jelszó nem egyezik! (' . __LINE__ . ')<br>';
				}
			}
			else
			{
				//$ok= false;
				//$error.= __LINE__.'HIBA! ';
			}
		}
		else
		{
			$ok = false;
			//$error.= __LINE__.'HIBA! ';
		} // if
		// Hibaüzenet:
		if (!$ok || $error != '')
		{
			//echo ('HIBA! '.$error);
			//print_r($_POST);
		}
		else
		{
			// Mentés
			if ($good_current_password)
			{
				$user->password = hash('sha512', $new_password);
				if ($user->save())
				{
					$message.= 'Új jelszó sikeresen módosítva!';
				}
			}
		}

		$this->render('/userprofile/changepassword', array(
			'message'	 => $message,
			'error'		 => $error));
	}

	public function actionChangeClientGroup()
	{
		$model          = $this->loadModel(userID());
		$ok				 = true;
		$user			 = $this->getUser();
		$client_groups	 = new ClientGroup;

		$message = '';
		$error	 = '';

		// Ellenőrzés:
		if (isset($_POST['UserProfile']['submit']))
		{
			// - Ügyfélkör
			if (!isset($_POST['UserProfile']['client_groups']))
			{
				$_POST['UserProfile']['client_groups'] = array();
			}
			if (isset($_POST['UserProfile']['client_groups']) && $_POST['UserProfile']['client_groups'] != $model->client_groups)
			{
				$model->client_groups = '';
				foreach ($_POST['UserProfile']['client_groups'] as $client_group_id)
				{
					$model->client_groups.= $client_group_id . ';';
				}
				if ($model->client_groups == '')
				{
					$model->client_groups = ' ';
				}
				if ($model->save())
				{
					$message.= 'Új ügyfélkör sikeresen módosítva!';
				}
				else
				{
					$error.= 'HIBA! ' . print_r($model->getErrors(), true) . ' (' . __LINE__ . ')';
				}
			}
		}
		else
		{
			$ok = false;
			//$error.= __LINE__.'HIBA! ';
		} // if

		$client_groups_selected = explode(';', $model->client_groups);

		$this->render('changeclientgroup', array('model'					 => $model,
			'client_groups_selected' => $client_groups_selected,
			'client_groups'			 => $client_groups->getOptions(),
			'message'				 => $message,
			'error'					 => $error));
	}

	public function actionEdit()
	{
		$model = $this->loadModel(userID());
		$this->render('edit', array('model'		 => $model,
			'message'	 => 'message'));
	}

	public function getUser($user_id = false)
	{
		if (!$user_id)
		{
			$user_id = userID();
		}
		$model_user			 = new User;
		$criteria			 = new CDbCriteria();
		$criteria->condition = "`user_id` = '$user_id'";
		$user				 = $model_user->findAll($criteria);
		if (!isset($user[0]))
		{
			throw new CHttpException(500, 'Error occured when check User data. (' . $user_id . ')');
		}
		return $user[0];
	}

//	public function getUserProfile($user_id = false)
//	{
//		if (!$user_id)
//		{
//         $user_id= userID();
//		}
//		$model_user_profile = new UserProfile;
//		$criteria = new CDbCriteria();
//		$criteria->condition = "`user_id` = '$user_id'";
//		$user_profile = $model_user_profile->findAll($criteria);
//		if (!isset($user_profile[0]))
//		{
//			//throw new CHttpException(500,'Error occured when check UserProfile data. ('.$user_id.')');
//			$new_user= new UserProfile;
//			$new_user->user_id= $user_id;
//			$user_profile[0]= $new_user;
//		}
//		return $user_profile[0];
//	}

	public function getClientGroupCondition()
	{
		$condition = '';

		$client_group_ids	 = '';
		$client_groups		 = ClientGroup::model()->findAll();
		$separator			 = '';

		foreach ($client_groups as $client_group)
		{
			$client_group_id = PersonalProperties::getProperty($client_group['client_group_id'], 'ClientGroup');
			if ($client_group_id == 1)
			{
				$client_group_ids.= $separator . '\'' . $client_group['client_group_id'] . '\'';
				$separator = ',';
			}
		}
		//$model= $this->loadModel();
		if ($client_group_ids != '')
		{
			//$client_groups= explode(';', $model->client_groups);
			//$client_group_ids= str_replace(';', ',', $model->client_groups);
			//$client_groups= '1,4';
			$condition = ' AND client_id'
					. ' IN (SELECT client_id FROM client_in_group WHERE client_group_id IN'
					. '	(SELECT client_group_id FROM client_group WHERE client_group_id IN (' . $client_group_ids . ')))';
		}
		//print(__LINE__.':'.$condition.'<br>');
		return $condition;
	}

}

?>
