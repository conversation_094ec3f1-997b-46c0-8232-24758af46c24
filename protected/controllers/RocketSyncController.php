<?php

Yang::import('application.components.mobile.RocketChatAPI');
class RocketSyncController extends Controller
{
    public static $published;
    public static $currDate;
    public static $defaultEnd;
    public static $isGiveResponse;

    /**
     * Main function for Ease - Rocket Snyc
     * @return void
     */
    public function actionIndex()
    {
        self::$isGiveResponse = requestParam('buttonCall') ? true : false;
        self::$published = Status::PUBLISHED;
        self::$currDate = date('Y-m-d');
        self::$defaultEnd = App::getSetting("defaultEnd");

        /* Teams */
        $easeGroups= $this->getEaseCompanyOrgGroup3();
        $rocketTeams = $this->getRocketTeamList();
        $easeGroupsOrdered = [];
        $rocketeTeamsOrdered = [];

        foreach ($easeGroups as $group) {
            $easeGroupsOrdered[genIdFromString($group["company_org_group_name"])]=$group;
        }
        foreach ($rocketTeams["teams"] as $team) {
            $rocketeTeamsOrdered[$team["name"]]=$team;
        }

        /**
        * Create Team if Ease Group3 is not in RocketChat yet.
        */
        foreach ($easeGroupsOrdered as $groupName=>$group)
        {
            if (empty($rocketeTeamsOrdered[$groupName])) {
                $this->createRocketTeam($groupName);
            }
        }

        /**
        * Delete Team if Rocket Team is not in Ease Group3 anymore.
        */
        foreach ($rocketeTeamsOrdered as $teamName=>$team)
        {
            if (empty($easeGroupsOrdered[$teamName])) {
                $this->deleteRocketTeam($teamName);
            }
        }

        /* Users */
        $easeUsers= $this->getEaseUsers();
        $rocketUsers = $this->getRocketUserList();
        $easeUsersOrdered = [];
        $rocketUsersOrdered = [];

        foreach ($easeUsers as $user) {
            $easeUsersOrdered[$user["username"]] = $user;
        }
        foreach ($rocketUsers["users"] as $user) {
            $rocketUsersOrdered[$user["username"]] = $user;
        }


        /**
        * Create User if Ease User is not in Rocket yet.
        * IF role or email or namne changed, then update user.
        */
        foreach ($easeUsersOrdered as $username=>$user)
        {
            if (empty($rocketUsersOrdered[$username])) {
                $this->createRocketUser($user["email"], $user["fullname"], $user["username"], $user["rocket_role"]);
            } else {
                $isUpdate = false;
                $role = $rocketUsersOrdered[$username]["roles"];
                $email = $rocketUsersOrdered[$username]["emails"][0]["address"];
                $name = $rocketUsersOrdered[$username]["name"];
                if ($user["rocket_role"] !== $rocketUsersOrdered[$username]["roles"][0]) {
                    $role = $user["rocket_role"];
                    $isUpdate = true;
                }
                if ($user["email"] !== $rocketUsersOrdered[$username]["emails"][0]["address"]) {
                    $email = $user["email"];
                    $isUpdate = true;
                }
                if ($user["fullname"] !== $rocketUsersOrdered[$username]["name"]) {
                    $name = $user["fullname"];
                    $isUpdate = true;
                }
                if ($isUpdate) {
                    $this->updateRocketUser($username, $email, $name, $role);
                }
            }
        }

        /**
        * Delete User if Rocket User is not in Ease anymore.
        * Users with special role cannot be deleted automatically.
        */
        $doNotDeleteUserRoles = ['admin','bot'];
        foreach ($rocketUsersOrdered as  $username=>$user)
        {
            if (empty(array_intersect($doNotDeleteUserRoles, $user["roles"]))
                && empty($easeUsersOrdered[$username]))
            {
                $this->deleteRocketUser($user["username"]);
            }
        }

        /* Users to Teams */
        $easeGroupMembersOrdered = [];
        $easeGroupModerators = [];
        $rocketTeamMembersOrdered = [];
        $rocketTeamModerators = [];
        $rocketTeamOwners =[];
        foreach ($easeUsers as $user) {
            $easeGroupMembersOrdered[genIdFromString($user["groupName"])][] = $user["username"];
        }
        $rocketTeams = $this->getRocketTeamList();
        foreach ($rocketTeams["teams"] as $team)
        {
            $rocketTeamMembers = $this->getRocketTeamMembers($team["name"]);
            foreach($rocketTeamMembers["members"] as $member)
            {
                $rocketTeamMembersOrdered[$team["name"]][] = $member["user"]["username"];
                if (in_array('moderator',$member["roles"]))
                {
                    $rocketTeamModerators[$team["name"]][] = $member["user"]["username"];
                }
                if (in_array('owner',$member["roles"]))
                {
                    $rocketTeamOwners[$team["name"]][] = $member["user"]["username"];
                }
            }
        }

        /**
        * Add moderator to Team if user is not moderator yet in the Team
        */
        foreach ($easeGroups as $group)
        {
            for ($i = 1; $i <= 6; $i++) {
                if (!empty($group["rocket_moderator$i"]))
                {
    				if ((!in_array($group["rocket_moderator$i"], $rocketTeamModerators[genIdFromString($group["company_org_group_name"])]) && isset($rocketTeamModerators[genIdFromString($group["company_org_group_name"])]))
						|| empty($rocketTeamModerators[genIdFromString($group["company_org_group_name"])])) {
                        $this->addRocketTeamMember($group["rocket_moderator$i"], genIdFromString($group["company_org_group_name"]), "moderator");
                    }
                    $easeGroupModerators[genIdFromString($group["company_org_group_name"])][] = $group["rocket_moderator$i"];
                }
            }
        }

        /**
        * Remove moderator from Team if user is not set as moderator in Ease Group Settings.
        * If user is still part of the Ease Group then re-add to Team as user.
        */
        foreach ($rocketTeamModerators as $teamName => $moderators) {
            foreach ($moderators as $moderator)
            {
                if(!in_array($moderator, $easeGroupModerators[$teamName])) {
                     $this->removeRocketTeamMember($teamName, $moderator);
                     if (in_array($moderator, $easeGroupMembersOrdered[$teamName]))
                     {
                        $this->addRocketTeamMember($moderator, $teamName);
                     }
                }
            }
        }

        /**
        * Add Ease User to team if not in the Rocket Team.
        */
        foreach ($easeGroupMembersOrdered as $groupName => $users)
        {
            foreach ($users as $userName)
            {
                if (!in_array($userName, $rocketTeamMembersOrdered[$groupName])
                    && isset($rocketTeamMembersOrdered[$groupName]))
                {
                    $this->addRocketTeamMember($userName, $groupName);
                }
            }
        }

        /**
        * Remove Rocket Team member if not in Ease group.
        * Team moderator (and owner) cannot be removed here.
        */
        foreach ($rocketTeamMembersOrdered as $teamName => $members)
        {
            foreach($members as $memberName)
            {
                if (!in_array($memberName, $easeGroupMembersOrdered[$teamName])
                    && !in_array($memberName, $rocketTeamModerators[$teamName])
                    && !in_array($memberName, $rocketTeamOwners[$teamName]))
                {
                    $this->removeRocketTeamMember($teamName, $memberName);
                }
            }
        }

        if (self::$isGiveResponse) {
            $results["status"] = 1;
            $results["message"] = Dict::getValue('syncron_message');
		    echo json_encode($results);
        }
    }

    /**
     * Action get user ID
     */
    public function actionGetUserID()
    {
        $value  = RocketChatAPI::getUserID();
        $result = [
            'userID' => $value,
        ];

        echo json_encode($result);
    }

    /**
     * Action get user token
     */
    public function actionGetUserToken()
    {
        $value  = RocketChatAPI::getUserToken();
        $result = [
         'userToken' => $value,
        ];

        echo json_encode($result);
    }

    public function actionGetUserIdAndToken()
    {
        $result = [
            'userID' => RocketChatAPI::getUserID(),
            'userToken' => RocketChatAPI::getUserToken()
        ];
        echo json_encode($result);
    }

    /**
     * Action get unread messages count
     */
    public function actionGetUnreadMessagesCount()
    {
        $userId    = requestParam('userID');
        $userToken = requestParam('userToken');
        $userId    = ($userId != null ? $userId : "");
        $userToken = ($userToken != null ? $userToken : "");
        $counter   = $this->getRocketUserUnreadMessagesCount($userId, $userToken);
        $result    = [
         "unreadNotLoaded" => $counter,
         "success"         => true
        ];

        echo json_encode($result);
    }

    /**
     * create Rocket User
     * @param mixed $email
     * @param mixed $fullname
     * @param mixed $username
     * @param mixed $role
     * @return void
     */
    private function createRocketUser($email, $fullname, $username, $role)
    {
        $response = RocketChatAPI::createUser($email, $fullname, $username, [$role]);
        $this->echoResult(__FUNCTION__, $response);
    }

    /**
     * get Rocket UserList
     * @return array
     */
    private function getRocketUserList()
    {
        $response = RocketChatAPI::getUserList();
        return $response;
    }

    /**
     * get Rocket UserId
     * @param mixed $userName
     * @return mixed
     */
    private function getRocketUserId($userName) {

        $response  = RocketChatAPI::getUserInfo($userName);
        $userArray = json_decode(json_encode($response), true);
        $userId    = $userArray["user"]["_id"];

        return $userId;
    }

    /**
     * get Rocket UserId
     * @param mixed $userName
     * @return mixed
     */
    private function getRocketRoomId($roomName) {

        $response  = RocketChatAPI::getRoomInfo($roomName);
        $roomArray = json_decode(json_encode($response), true);
        $roomId    = $roomArray["room"]["_id"];

        return $roomId;
    }

    /**
     * Get Rocket chat user unread messages
     *
     * @param string $userName
     * @return int
     */
    private function getRocketUserUnreadMessagesCount(string $userId = "", string $userToken = "") : int
    {
        $channels = RocketChatAPI::getChannelsInfo()["channels"];
        $groups   = RocketChatAPI::getGroupsInfo()["groups"];
        $ims      = RocketChatAPI::getDirectMessagesInfo()["ims"];
        $counter  = 0;

        // Channels unread messages
        foreach($channels as $channel) {

            $roomId          = $channel["_id"];
            $message_counter = RocketChatAPI::getUserChannelMessageCounters($roomId, $userId, $userToken);
            $counter        += (int)$message_counter["unreads"];
        }

        // Direct messages unread messages
        foreach($ims as $im) {
            $roomId          = $im["_id"];
            $message_counter = RocketChatAPI::getUserDirectMessageCounters($roomId, $userId, $userToken);
            $counter        += (int)$message_counter["unreads"];
        }

        // Groups unread messages
        foreach($groups as $group) {
            $roomId          = $group["_id"];
            $message_counter = RocketChatAPI::getUserGroupMessageCounters($roomId, $userId, $userToken);
            $counter        += (int)$message_counter["unreads"];
        }

        return $counter;
    }

    /**
     * Update Rocket User
     * @param mixed $userName
     * @param mixed $email
     * @param mixed $name
     * @param mixed $roles
     * @return array
     */
    private function updateRocketUser($userName, $email, $name, $roles)
    {
        $userId = $this->getRocketUserId($userName);
        $response = RocketChatAPI::updateUser($userId, $email, $name, [$roles]);
        $this->echoResult(__FUNCTION__, $response);
        return $response;
    }

    /**
     * Delete Rocket User
     * @param mixed $username
     * @return void
     */
    private function deleteRocketUser($username)
    {
        $response = RocketChatAPI::deleteUser($username);
        $this->echoResult(__FUNCTION__, $response);
    }

    /**
     * Create Rocket Team
     * @param mixed $teamName
     * @return void
     */
    private function createRocketTeam($teamName)
    {
        $response = RocketChatAPI::createTeam($teamName);
        $this->echoResult(__FUNCTION__, $response);
    }

    /**
     * get Rocket TeamList
     * @return void
     */
    private function getRocketTeamList()
    {
        $response = RocketChatAPI::getTeamList();
        return $response;
    }

    /**
     * get Rocket TeamMembers
     * @param mixed $teamName
     * @return void
     */
    private function getRocketTeamMembers($teamName)
    {
        $response = RocketChatAPI::getTeamMembers($teamName);
        return $response;
    }

    /**
     * delete Rocket Team
     * @param mixed $teamName
     * @return void
     */
    private function deleteRocketTeam($teamName)
    {
        RocketChatAPI::deleteTeam($teamName);
    }

    /**
     * add Rocket TeamMember
     * @param mixed $userName
     * @param mixed $teamName
     * @param mixed $role
     * @return void
     */
    private function addRocketTeamMember($userName, $teamName, $role = "member")
    {
        $userId = $this->getRocketUserId($userName);
        $members = [
            "userId" => $userId,
            "roles"  => [$role]
        ];
        $response = RocketChatAPI::addTeamMembers($teamName, $members);
        $this->echoResult(__FUNCTION__, $response);
    }

    /**
     * remove Rocket TeamMember
     * @param mixed $teamName
     * @param mixed $userName
     * @return void
     */
    private function removeRocketTeamMember($teamName, $userName)
    {
        $userId = $this->getRocketUserId($userName);
        $response = RocketChatAPI::removeTeamMember($teamName, $userId);
        $this->echoResult(__FUNCTION__, $response);
    }

    /**
     * SQL to get Ease Users
     * @return mixed
     */
    private function getEaseUsers()
    {
        $fullname = Employee::getParam("fullname", "employee");

        $SQL = "
                SELECT
                        user.username,
                        IF(IFNULL(user.email,'') = '', CONCAT('mail', user.row_id, '@mail.com'), user.email) AS email,
                        user.rocket_role,
                        IFNULL(". $fullname . ", user.username) AS fullname,
                        company_org_group_name AS groupName
                FROM    user
                LEFT JOIN  employee
                        ON employee.employee_id = user.employee_id
                    AND employee.status = " . self::$published . "
                    AND employee.valid_from <= '" . self::$currDate . "'
                    AND employee.valid_to >= '" . self::$currDate . "'
                LEFT JOIN employee_contract ON
                    employee_contract.employee_id = employee.employee_id
                AND employee_contract.status = " . self::$published . "
                AND employee.valid_from <= IFNULL(employee_contract.valid_to,'" . self::$defaultEnd. "')
                AND employee.valid_to >= employee_contract.valid_from
                " . $this->getGroupJoinSQL("company_org_group3") . "
                WHERE
                        user.status = " . self::$published . "
                    AND user.valid_from <= '" . self::$currDate . "'
                    AND user.valid_to >= '" . self::$currDate . "'
        ";

        return dbFetchAll($SQL);
    }

    /**
     * SQL to join Group
     * @param mixed $group
     * @return string
     */
    private function getGroupJoinSQL($group)
    {
        $SQL = "";
        $groupId = $group . "_id";

		if(EmployeeGroupConfig::isActiveGroup($groupId)) {
			$SQL .= EmployeeGroup::getLeftJoinSQLWithoutCal($groupId);
		}
		$SQL .="
					LEFT JOIN {$group} ON
						{$group}.company_org_group_id= ".EmployeeGroup::getActiveGroupSQL("{$groupId}","employee")."
					AND {$group}.status= " . self::$published . "
					AND {$group}.valid_from <= '" . self::$currDate . "'
					AND IFNULL({$group}.valid_to, '" . self::$defaultEnd. "') >= '" . self::$currDate . "'
                ";

        return $SQL;
    }

    /**
     * Array of CompanyOrgGroup3 data
     * @return mixed
     */
    private function getEaseCompanyOrgGroup3()
    {
        $SQL = "
                SELECT
                    company_org_group_id,
                    company_org_group_name,
                    rocket_moderator1,
                    rocket_moderator2,
                    rocket_moderator3,
                    rocket_moderator4,
                    rocket_moderator5,
                    rocket_moderator6
                FROM
                    company_org_group3
                WHERE
                    company_org_group3.status = " . self::$published . "
                AND company_org_group3.valid_from <= '" . self::$currDate . "'
                AND company_org_group3.valid_to >= '" . self::$currDate . "'
        ";

        return dbFetchAll($SQL);
    }

    /**
     * echo result of API Call
     * @param mixed $functionName
     * @param mixed $response
     * @return void
     */
    private function echoResult($functionName, $response)
    {
        if (!self::$isGiveResponse)
        {
            $result = (json_encode($response) != "null") ? json_encode($response) : "";
            echo $functionName . " " . " is ready.</br>" . $result;
        }
    }
}
