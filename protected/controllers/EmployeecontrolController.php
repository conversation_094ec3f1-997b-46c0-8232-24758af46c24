<?php

class EmployeecontrolController extends Grid2WizardController
{

	/*use EmployeeControllerData;*/
	use EmployeeControllerFakeData,
		EmployeeControllerBaber,
		EmployeecontrolControllerWizards,
		EmployeecontrolControllerColumns,
		EmployeecontrolControllerActions;

	public $published = Status::PUBLISHED;
	public $defaultEnd;
	public $searchWithFlexGroups;
	public $groupSQLFetchAll;

	public function __construct()
	{
		parent::__construct('employeecontrol');

		$this->enableLAGrid();
		$this->enableLAGridLight();
		$this->enableCustomDialogJs = true;
		$this->defaultEnd = App::getSetting('defaultEnd');
		$this->searchWithFlexGroups = App::getSetting('searchWithFlexGroups');
	}

	protected function G2BInit()
	{
		$this->enableMultiGridMode();

		$this->setControllerPageTitleId("page_title_employee_management");

		$this->LAGridRights->overrideInitRights("paging", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("add", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("search_header", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("select", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("multi_select", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("column_move", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("col_sorting", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("reload_sortings", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("details", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_xls", false, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_xlsx", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("export_pdf_node", true, "dhtmlxGrid");
		$this->LAGridRights->overrideInitRights("modify", true, "dhtmlxGrid");

		$this->LAGridDB->enableArrMode();

		$this->LAGridDB->setPrimaryKey("row_id",'dhtmlxGrid');

		$this->LAGridDB->enableSplitRowID("_");
		$this->LAGridDB->setSplitRowIDStructure(["employee_id", /*"employee_contract_id",*/ "valid_date",]);

		parent::setExportFileName(Dict::getValue("export_file_employee"));
		parent::setGridProperty("splitColumnEnabled", true, "dhtmlxGrid");
		parent::setGridProperty("splitColumn", 4, "dhtmlxGrid");

		$this->setBigFormMode("uploadDialog");

		parent::setDialogMaxWidth(12);
		$this->groupSQLFetchAll = dbFetchAll($this->group_SQL());
		parent::G2BInit();
	}

	function groupDates($intervals) {

		$returnArray = [];
		$validFromArray = [];

		ksort($intervals);

		foreach( $intervals as $validFromKey => $validFrom ){
			$validFromArray[] = $validFromKey;
		}

		foreach ( $validFromArray as $vKey => $validFrom ){
			$validTo = null;

			if( isset($validFromArray[$vKey+1]) ){
				$validTo = $validFromArray[$vKey+1];
				$validTo = (new DateTime($validTo))->modify('-1 day')->format('Y-m-d');
			}else{
				$validTo = $intervals[$validFrom];
			}

			$returnArray[0][] = $validFrom.' - '.$validTo;
		}
		return $returnArray;
	}

	public function dataArray($gridID, $filter)
	{
		$art = new ApproverRelatedGroup;
		$dta = $art->getApproverReleatedGroupSQL("Employee", "employeeManagement");
		$gargSQL = $dta;

		if ($this->searchWithFlexGroups)
		{
			$SQLfilter = $this->getSQLFilter();
		}
		else
		{
			$gpf = new GetPreDefinedFilter(
				$this->getControllerID(),
				["cal.`date`"],
				['company' => "employee", 'payroll' => "employee"]
			);
			$dta = $gpf->getFilter();
			$SQLfilter = $dta;
		}


		$exprFullName = AnyCache::get("G2BInit.exprFullName2");
		if (!$exprFullName) {
			$exprFullName = Employee::getParam('fullname', 'employee');
			AnyCache::set("G2BInit.exprFullName2", $exprFullName,"employee");
		}

		$published = Status::PUBLISHED;
		$defaultEnd = App::getSetting("defaultEnd");


		//Mentjük a filtert, majd pedig öszehasonlítjuk a mentettet az újjal, ha van változás akkor újrakérjük az
		// adatokat a cache hez
		$postFilter = $_POST['searchInput'];
		$postHeadFilter = $_POST['dhtmlxGridHeadFilters'];

		$resume = false;

		{
			$f = AnyCache::get('predFilters'); //Oldalsó szűrők
			$fh = AnyCache::get('predFiltersHead'); //Fejlécben lévő szűrések
			if (!$f) {
				AnyCache::set('predFilters', $postFilter); #completeThis // invalidation tagek hiányoznak
			} else {
				if ($f != $postFilter) {
					$resume = true;
				}
			}

			if (!$fh) {
				AnyCache::set('predFilters', $postFilter); #completeThis // invalidation tagek hiányoznak
			} else {
				if ($fh != $postHeadFilter) {
					$resume = true;
				}
			}
		}


		$resultArray = AnyCache::get("employeecontrolContent");
		if (!$resultArray || $resume === true) {

			$SQL = '
			SELECT
				CONCAT(IFNULL(employee.`employee_id`,""),/*"_",IFNULL(employee_contract.`employee_contract_id`,""),*/"_",cal.`date`) AS row_id,
				employee.`emp_id` AS emp_id,
				/*employee.`company_id`,*/
				employee.employee_id AS employee_id,
				employee_contract.employee_contract_id,
				' . $exprFullName . ' as fullname,
				employee.`row_id` AS e_row_id,
				employee.`first_name`,
				employee.`last_name`,
				employee.`valid_from`,
				employee.`valid_to`,
				employee_contract.`valid_from` AS history_valid_from,
				company.company_name as company_id,
				company.row_id AS company_row_id,
				payroll.payroll_name as payroll_id,
				employee.nameofbirth,
				employee_ext2.ext2_option16,
				employee_ext2.ext2_option8,
				employee_ext2.ext2_option10,
				unit.unit_name as unit_id,
				employee_position.employee_position_name AS employee_position_id,
				employee_contract.`ec_valid_from`,
				employee_contract.`ec_valid_to`,
				employee_contract.row_id AS employee_contract_row_id,
				employee_cost.row_id AS employee_cost_row_id,
				employee_base_absence.`quantity`,
				employee_base_absence.row_id AS employee_base_absence_row_id,
				workgroup.`workgroup_name` AS group_value,
				cost.cost_name as cost_id,
				employee_contract.employee_contract_type,
				employee_card.card,
				employee_contract.employee_contract_number,
				workgroup.workgroup_name as workgroup_id,
				employee_ext.row_id as employee_ext_row_id,
				employee_ext.date_of_birth,
				employee_ext.place_of_birth,
				employee_ext.mothers_name,
				employee_ext.ssn,
				employee_ext.personal_id_card_number,
				employee_ext.option1,
				employee_ext.option10,
				ext2_option1,
				ext2_option11,
				ext2_option12,
				ext2_option15,
				ext4_option11,
				ext2_option14,
				ext2_option13,
				ext2_option9,
				ext3_option40,
				';

			if (EmployeeGroupConfig::isActiveGroup('cost_center_id')) {
				$SQL .= 'cost_center.`cost_center_name` as cost_center_id,';
			}

			$SQL .= '
				company_org_group1.`company_org_group_name` as company_org_group1_id,
				company_org_group2.`company_org_group_name` as company_org_group2_id,
				company_org_group3.`company_org_group_name` as company_org_group3_id,
				CASE SUBSTRING(employee.emp_id, -1)%5
					WHEN 0 THEN "<div class=\'reassignment\'><div class=\'reassignmentColor color0\'></div><div class=\'reassignmentContent\'>' . Dict::getValue(
					'waiting_medical_post'
				) . '</div></div>"
					WHEN 1 THEN "<div class=\'reassignment\'><div class=\'reassignmentColor color1\'></div><div class=\'reassignmentContent\'>' . Dict::getValue(
					'waiting_medical_examination'
				) . '</div></div>"
					WHEN 2 THEN "<div class=\'reassignment\'><div class=\'reassignmentColor color2\'></div><div class=\'reassignmentContent\'>' . Dict::getValue(
					'not_match'
				) . '</div></div>"
					WHEN 3 THEN "<div class=\'reassignment\'><div class=\'reassignmentColor color3\'></div><div class=\'reassignmentContent\'>' . Dict::getValue(
					'not_appeared'
				) . '</div></div>"
					WHEN 4 THEN "<div class=\'reassignment\'><div class=\'reassignmentColor color4\'></div><div class=\'reassignmentContent\'>' . Dict::getValue(
					'reassigned'
				) . '</div></div>"
				END AS reassignment';

			if (
				in_array('ttwa-csm', Yang::getParam("modules")) && App::getSetting(
					'employee_controller_competency_columns'
				)
			) {

				$SQL .= ',CONCAT(
						"<div class=\'position_matching\'><div class=\'position_matchingColor color", ROUND((COUNT(DISTINCT IF(ecomp.competency_id = competency_links.competency_id, 1, NULL)) / COUNT(DISTINCT competency_links.competency_id)) * 100, -1),"\'></div><div class=\'position_matchingContent\'>",
						ROUND((COUNT(DISTINCT IF(ecomp.competency_id = competency_links.competency_id, 1, NULL)) / COUNT(DISTINCT competency_links.competency_id)) * 100, 2),
						"%",
						"</div></div>") AS position_matching';
			}

			$SQL .= '

			FROM
				`calendar` cal
			LEFT JOIN
				`employee` ON
					(cal.`date` BETWEEN employee.`valid_from` AND IFNULL(employee.`valid_to`, "' . $defaultEnd . '"))
						AND employee.`status`=' . $published . '
			LEFT JOIN
				`employee_contract` ON
					employee.`employee_id` = employee_contract.`employee_id`
						AND (cal.`date` BETWEEN employee_contract.`valid_from` AND IFNULL(employee_contract.`valid_to`, "' . $defaultEnd . '"))
							 AND employee_contract.`status`=' . $published . '
			LEFT JOIN
				`employee_card` ON
					employee_contract.`employee_contract_id` = employee_card.`employee_contract_id`
						AND (cal.`date` BETWEEN employee_card.`valid_from` AND IFNULL(employee_card.`valid_to`, "' . $defaultEnd . '"))
							AND employee_card.`status`=' . $published . '

			LEFT JOIN
				`employee_cost` ON
					employee_contract.`employee_contract_id` = employee_cost.`employee_contract_id`
						AND (cal.`date` BETWEEN employee_cost.`valid_from` AND IFNULL(employee_cost.`valid_to`, "' . $defaultEnd . '"))
							AND employee_cost.`status`=' . $published . '

			LEFT JOIN
				`user` ON
					employee.`employee_id` = user.`employee_id`
						AND (cal.`date` BETWEEN user.`valid_from` AND IFNULL(user.`valid_to`, "' . $defaultEnd . '"))
							 AND user.`status`=' . $published . '

			LEFT JOIN
				employee_ext ON
					employee_ext.employee_id = employee.employee_id
						AND (cal.`date` BETWEEN employee_ext.`valid_from` AND IFNULL(employee_ext.`valid_to`, "' . $defaultEnd . '"))
						AND employee_ext.status = ' . $published . '
			 LEFT JOIN
				employee_ext2 ON
					employee_ext2.employee_id = employee.employee_id
						AND (cal.`date` BETWEEN employee_ext2.`valid_from` AND IFNULL(employee_ext2.`valid_to`, "' . $defaultEnd . '"))
						AND employee_ext2.status = ' . $published . '
			LEFT JOIN
				employee_ext3 ON
					employee_ext3.employee_id = employee.employee_id
						AND (cal.`date` BETWEEN employee_ext3.`valid_from` AND IFNULL(employee_ext3.`valid_to`, "' . $defaultEnd . '"))
						AND employee_ext3.status = ' . $published . '
			LEFT JOIN
				employee_ext4 ON
					employee_ext4.employee_id = employee.employee_id
						AND (cal.`date` BETWEEN employee_ext4.`valid_from` AND IFNULL(employee_ext4.`valid_to`, "' . $defaultEnd . '"))
						AND employee_ext4.status = ' . $published . '
			LEFT JOIN
				`company` ON
					employee.`company_id` = company.`company_id`
						AND (cal.`date` BETWEEN company.`valid_from` AND IFNULL(company.`valid_to`, "' . $defaultEnd . '"))
							AND company.`status`=' . $published . '
			LEFT JOIN
				`payroll` ON
					employee.`payroll_id` = payroll.`payroll_id`
						AND (cal.`date` BETWEEN payroll.`valid_from` AND IFNULL(payroll.`valid_to`, "' . $defaultEnd . '"))
							AND payroll.`status`=' . $published . '

			LEFT JOIN
				`cost` ON
					employee_cost.`cost_id` = cost.`cost_id`
						AND (cal.`date` BETWEEN cost.`valid_from` AND IFNULL(cost.`valid_to`, "' . $defaultEnd . '"))
							AND cost.`status`=' . $published . '

			LEFT JOIN
				employee_base_absence ON
					employee_base_absence.employee_contract_id = employee_contract.employee_contract_id
						AND (cal.`date` BETWEEN employee_base_absence.`valid_from` AND IFNULL(employee_base_absence.`valid_to`, "' . $defaultEnd . '"))
							AND employee_base_absence.status = ' . $published . '

			';

			$SQL .= EmployeeGroup::getAllActiveLeftJoinSQL(
				"employee_contract"
			); //---------------------------------------	slow

			if (EmployeeGroupConfig::isActiveGroup('cost_center_id')) {
				$SQL .= "LEFT JOIN `cost_center` ON
						cost_center.`cost_center_id`=" . EmployeeGroup::getActiveGroupSQL("cost_center_id", "ecost") . "
					AND cost_center.`status` = " . $published . "
					AND employee_contract.`valid_from` <= IFNULL(cost_center.`valid_to`,'" . $defaultEnd . "') AND employee_contract.`valid_to` >= cost_center.`valid_from`
					AND employee_contract.`ec_valid_from` <= IFNULL(cost_center.`valid_to`,'" . $defaultEnd . "') AND employee_contract.`ec_valid_to` >= cost_center.`valid_from`
				";
			}

			$SQL .= "
			LEFT JOIN company_org_group1 ON
					company_org_group1.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL(
					"company_org_group1_id", "employee"
				) . "
				AND company_org_group1.`status` = " . $published . "
				AND cal.`date` BETWEEN company_org_group1.`valid_from` AND IFNULL(company_org_group1.`valid_to`,'" . $defaultEnd . "')
			LEFT JOIN company_org_group2 ON
					company_org_group2.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL(
					"company_org_group2_id", "employee"
				) . "
				AND company_org_group2.`status` = " . $published . "
				AND cal.`date` BETWEEN company_org_group2.`valid_from` AND IFNULL(company_org_group2.`valid_to`,'" . $defaultEnd . "')
			LEFT JOIN company_org_group3 ON
					company_org_group3.`company_org_group_id` = " . EmployeeGroup::getActiveGroupSQL(
					"company_org_group3_id", "employee"
				) . "
				AND company_org_group3.`status` = " . $published . "
				AND cal.`date` BETWEEN company_org_group3.`valid_from` AND IFNULL(company_org_group3.`valid_to`,'" . $defaultEnd . "')
			LEFT JOIN `workgroup` ON
					`workgroup`.`workgroup_id`= " . EmployeeGroup::getActiveGroupSQL(
					"workgroup_id", "employee_contract"
				) . "
				AND `workgroup`.`status`=" . $published . "
				AND cal.`date` BETWEEN `workgroup`.`valid_from` AND IFNULL(`workgroup`.`valid_to`,'" . $defaultEnd . "')
			LEFT JOIN `unit` ON
					`unit`.`unit_id`= " . EmployeeGroup::getActiveGroupSQL("unit_id", "employee") . "
				AND `unit`.`status`=" . $published . "
				AND cal.`date` BETWEEN `unit`.`valid_from` AND IFNULL(`unit`.`valid_to`,'" . $defaultEnd . "')
		";

			if (isset($gargSQL["join"])) {
				$SQL .= $gargSQL["join"];
			}

			$SQL .= '
			LEFT JOIN
				`employee_position` ON
					employee_position.`employee_position_id` = employee_contract.`employee_position_id`
						AND (cal.`date` BETWEEN employee_position.`valid_from` AND IFNULL(employee_position.`valid_to`, "' . $defaultEnd . '"))
							AND employee_position.`status`=' . $published;
			if (App::getSetting('employee_controller_competency_columns')) {
				$SQL .= '
					LEFT JOIN `competency_links` ON
							competency_links.`link_table_id` = employee_position.`employee_position_id`
						AND competency_links.link_type = "EP" AND competency_links.status = ' . $published . '
					LEFT JOIN employee_competency ecomp ON employee_contract.employee_contract_id = ecomp.employee_contract_id AND ecomp.status = ' . $published;
			}
			$SQL .= '
				WHERE
					' . $SQLfilter . '
					AND employee.`row_id` IS NOT NULL
					AND employee_contract.`row_id` IS NOT NULL
		';

			if (isset($gargSQL["where"])) {
				$SQL .= $gargSQL["where"];
			}

			$SQL .= '
			GROUP BY
				employee.`row_id`
			ORDER BY
				`fullname`
		';

			$filter = $_POST['searchInput'];

			if (is_array($filter)) {
				foreach ($filter as $column => $value) {
					$search[] = "{" . $column . "}";
					$replace[] = is_array($value) ? implode("', '", $value) : $value;
				}

				$SQL = str_replace($search, $replace, $SQL);
			} else {
				$SQL = preg_replace('/\{[^}]+\}/', '', $SQL);
			}


			$result = dbFetchAll($SQL);

			$resultArray = [];

			foreach ($result as $key => $data) {

				$resultArray[$data['row_id']] = $result[$key];

				$SQL = '
			SELECT
				tab_column.tab_id,
				tab_column.column_id,
				tab_column.type,
				eti.row_id as eti_row_id,
				eti.value
			FROM `tab_column`
			LEFT JOIN employee_tab et
				ON et.tab_id = tab_column.tab_id
				AND et.status = "' . $published . '"
				AND CURDATE() BETWEEN et.`valid_from`
				AND IFNULL(et.`valid_to`, "' . $defaultEnd . '")
				AND et.connect_id = "' . $data['employee_id'] . '"
			LEFT JOIN employee_tab_item eti
				ON eti.employee_tab_id = et.employee_tab_id
				AND eti.status = "' . $published . '"
				AND eti.column_id = tab_column.column_id
			WHERE
				tab_column.`status`= ' . $published . '
				AND `tab_column`.`status`= ' . $published . '
			';

				$tabs = dbFetchAll($SQL);

				foreach ($tabs as $tab_key => $tab) {
					$resultArray[$data['row_id']][$tab['column_id']] = $tab['value'];
				}

			}

			AnyCache::set("employeecontrolContent", $resultArray, "tab_column,employee_tab,employee_tab_id");
		}


		return $resultArray;
	}

	public function actionDialog($layout = '//Grid2/layouts/wizardDialogLayout', $view = '/Grid2/wizardDialog', $additionalParams = []) {
		parent::actionDialog('//Grid2/layouts/wizardDialogLayout', '/employeecontrol/employeecontrolWizardDialog', $additionalParams);
	}

	public function search()
	{
		if ($this->searchWithFlexGroups) {
			return $this->getFlexGroupsSearch();
		} else {
			return $this->getPreDefinedSearchFromDb("employeeManagement");
		}
	}

	protected function getStatusButtons($gridID = null)
	{
		if (!isset($gridID)) {
			$gridID = 'dhtmlxGrid';
		} else {
			if (empty($gridID)) {
				$gridID = 'dhtmlxGrid';
			}
		}

		$controllerId = $this->getControllerID();

		$buttons = [];

		if (App::hasRight($controllerId, "bulkImageUpload")) {
			$buttons["openBulkImportDialog"] = array(
				"type"    => "button",
				"id"      => "openBulkImportDialog",
				"class"   => "openBulkImportDialog",
				"name"    => "openBulkImportDialog",
				"img"     => "/images/status_icons/st_bulk_upload.png",
				"label"   => Dict::getValue("bulkImageUpload"),
				"onclick" => "G2BDialogMultiGrid('dhtmlxGrid','bulkImportDialog',null,0,'./dialog','./save','./gridData',null,'" . $this->getControllerPageTitle() . " - " . Dict::getValue("bulkImageUpload") . "','" . Dict::getValue("please_select_line") . "');",
			);

			$buttons[] = array(
				"type" => "selector",
			);
		}

		if (App::hasRight($controllerId, "import")) {
			$buttons["openImportDialog"] = array(
				"type"    => "button",
				"id"      => "openImportDialog",
				"class"   => "openImportDialog",
				"name"    => "openImportDialog",
				"img"     => "/images/status_icons/st_upload.png",
				"label"   => Dict::getValue("import")
				/*."(Ctrl+Shift+a)"*/,
				// G2BDialogMultiGrid(grid_id, generate_from, selected_ids, dialogMode, dialogContentUrl, actionSaveUrl, gridDataUrl, params, windowtitle, errortext)
				"onclick" => "G2BDialogMultiGrid('dhtmlxGrid','importDialog',null,0,'./dialog','./save','./gridData',null,'" . $this->getControllerPageTitle() . " - " . Dict::getValue("import") . "','" . Dict::getValue("please_select_line") . "');",
			);

			$buttons[] = array(
				"type" => "selector",
			);
		}

		if (App::hasRight($controllerId, "employeeLock")) {
			$buttons["openLockDialog"] = array(
				"type"    => "button",
				"id"      => "openImportDialog",
				"class"   => "openLockDialog",
				"name"    => "openLockDialog",
				"img"     => "/images/status_icons/st_lock.png",
				"label"   => Dict::getValue("lock_employee")
				/*."(Ctrl+Shift+a)"*/,
				// G2BDialogMultiGrid(grid_id, generate_from, selected_ids, dialogMode, dialogContentUrl, actionSaveUrl, gridDataUrl, params, windowtitle, errortext)
				"onclick" => "G2BDialogMultiGrid('dhtmlxGrid','employeeLockDialog',null,1,'./dialog','./save','./gridData',null,'" . $this->getControllerPageTitle() . " - " . Dict::getValue("lock_employee") . "','" . Dict::getValue("please_select_line") . "');",
			);

			$buttons[] = array(
				"type" => "selector",
			);
		}

		if (App::hasRight($controllerId, "employeeChangePosition")) {
			$buttons["openChangePositionDialog"] = array(
				"type"    => "button",
				"id"      => "openChangePositionDialog",
				"class"   => "openChangePositionDialog",
				"name"    => "openChangePositionDialog",
				"img"     => "/images/status_icons/st_workflow_mod.png",
				"label"   => Dict::getValue("employee_change_position"),
				// G2BDialogMultiGrid(grid_id, generate_from, selected_ids, dialogMode, dialogContentUrl, actionSaveUrl, gridDataUrl, params, windowtitle, errortext)
				"onclick" => "G2BDialogMultiGrid('dhtmlxGrid','employeeChangePositionDialog',null,1,'./dialog','./goToMissingCompetencyByPosition','./gridData',null,'" . $this->getControllerPageTitle() . " - " . Dict::getValue("employee_change_position") . "','" . Dict::getValue("please_select_line") . "');",
			);

			$buttons[] = array(
				"type" => "selector",
			);
		}

		if (App::hasRight($controllerId, "syncToLoga")) {
			$buttons["syncToLoga"] = array(
				"type"    => "button",
				"id"      => "syncToLoga",
				"class"   => "syncToLoga",
				"name"    => "syncToLoga",
				"img"     => "/images/status_icons/st_sync_to.png",
				"label"   => Dict::getValue("sync_to_loga"),
				// G2BDialogMultiGrid(grid_id, generate_from, selected_ids, dialogMode, dialogContentUrl, actionSaveUrl, gridDataUrl, params, windowtitle, errortext)
				"onclick" => "logaSync('" . Dict::getValue('sync_to_loga') . "');",
			);

			$buttons[] = array(
				"type" => "selector",
			);
		}

		/* BOS-nél nem lehet rajta a felületen
		if (App::hasRight($controllerId, "syncToBaber"))
		{
			$buttons["syncToBaber"] =
			[
				"type"		=> "button",
				"id"		=> "syncToBaber",
				"class"		=> "syncToBaber",
				"name"		=> "syncToBaber",
				"img"		=> "/images/status_icons/st_sync_to.png",
				"label"		=> Dict::getValue("sync_to_baber"),
				"onclick"	=> "baberSync('" . Dict::getValue('sync_to_baber') . "');",
			];

			$buttons[] = [
				"type" => "selector",
			];
		}
		*/

		if (App::hasRight($controllerId, "syncToLaurel")) {
			$buttons["syncToLaurel"] = array(
				"type"    => "button",
				"id"      => "syncToLaurel",
				"class"   => "syncToLaurel",
				"name"    => "syncToLaurel",
				"img"     => "/images/status_icons/st_income.png",
				"label"   => Dict::getValue("sync_to_laurel"),
				// G2BDialogMultiGrid(grid_id, generate_from, selected_ids, dialogMode, dialogContentUrl, actionSaveUrl, gridDataUrl, params, windowtitle, errortext)
				"onclick" => "laurelSyncQuestion();",
			);

			$buttons[] = array(
				"type" => "selector",
			);
		}

		$originalButtons = parent::getStatusButtons($gridID);

		$searchInGrid = $originalButtons["searchInGrid"];
		unset($originalButtons["searchInGrid"]);
		unset($originalButtons["searchInGridSelector"]);
		$buttons["searchInGrid"] = $searchInGrid;

		if (App::hasRight($controllerId, "uploadDialog")) {
			$buttons["openUploadDialog"] = array(
				"type"    => "button",
				"id"      => "openUploadDialog",
				"class"   => "openUploadDialog",
				"name"    => "openUploadDialog",
				"img"     => "/images/status_icons/st_plus2.png",
				"label"   => Dict::getValue("add")
				/*."(Ctrl+Shift+a)"*/,
				// G2BDialogMultiGrid(grid_id, generate_from, selected_ids, dialogMode, dialogContentUrl, actionSaveUrl, gridDataUrl, params, windowtitle, errortext)
				"onclick" => "G2BDialogMultiGrid('dhtmlxGrid','uploadDialog',null,0,'./dialog','./uploadDialogSave','./gridData',null,'" . $this->getControllerPageTitle() . " - " . Dict::getValue("") . "','" . Dict::getValue("please_select_line") . "', '50%', 'dialogContainer');",
			);
		}

		if ((int)App::getRight($controllerId, "uploadDialogEdit")) {
			$buttons["openUploadDialogEdit"] = array(
				"type"    => "button",
				"id"      => "openUploadDialogEdit",
				"class"   => "openUploadDialogEdit",
				"name"    => "openUploadDialogEdit",
				"img"     => "/images/status_icons/st_cog.png",
				"label"   => Dict::getValue("add")
				/*."(Ctrl+Shift+a)"*/,
				// G2BDialogMultiGrid(grid_id, generate_from, selected_ids, dialogMode, dialogContentUrl, actionSaveUrl, gridDataUrl, params, windowtitle, errortext)
				"onclick" => "G2BDialogMultiGrid('dhtmlxGrid','uploadDialog',null,0,'./dialog?edit=1','./uploadDialogSave','./gridData',null,'" . $this->getControllerPageTitle() . " - " . Dict::getValue("") . "','" . Dict::getValue("please_select_line") . "', '50%', 'dialogContainer');",
			);
		}

		if ((int)App::getRight($controllerId, "absenceCalc")) {
			$buttons["absenceCalc"] = array(
				"type"    => "button",
				"id"      => "absenceCalc",
				"class"   => "absenceCalc",
				"name"    => "absenceCalc",
				"img"     => "/images/status_icons/st_calendar.png",
				"label"   => Dict::getValue("absence_calculation"),
				"onclick" => "absenceCalc('" . Dict::getValue("absenceCalc_confirm") . "');",
			);
		}

		return Yang::arrayMerge($buttons, $originalButtons);
	}

	private function getNextIntervalTable ($usedTables, $employeeId, $employeeContractId, $validFrom, $nextValidFrom){

		$deleteTables = [];
		$updateTables = [];

		foreach ( $usedTables as $key => $modelName ){

			$requiredAttrs = [];

			$m = new $modelName;
			$attributes = $m->attributes;
			foreach ($attributes as $attrKey => $attrVal) {
				if ($m->isAttributeRequired($attrKey)) {
					if (!in_array($attrKey, $requiredAttrs) && (is_null($attrVal) || empty($attrVal) )) {
						$requiredAttrs[] = $attrKey;
					}
				}
			}

			$tableConnect = '';

			if (in_array('employee_id', $requiredAttrs)) {
				$tableConnect = 'employee_id = "'.$employeeId.'"';
			}
			if (in_array('employee_contract_id', $requiredAttrs)) {
				if( !empty($tableConnect) ){
					$tableConnect .= ' AND ';
				}
				$tableConnect .= 'employee_contract_id = "'.$employeeContractId.'"';
			}

			$criteria = new CDbCriteria;
			$newCriteria = new CDbCriteria;

			if( $modelName == 'EmployeeGroup' ){
				$criteria->condition = $tableConnect . " AND group_id = 'workgroup_id'
				AND status = '".$this->published."' AND valid_from = '$validFrom' ";

				$newCriteria->condition = $tableConnect . " AND group_id = 'workgroup_id'
				AND status = '".$this->published."' AND valid_from = '$nextValidFrom' ";
			}else{
				$criteria->condition = $tableConnect . " AND  status = '".$this->published."' AND valid_from = '$validFrom'";
				$newCriteria->condition = $tableConnect . " AND  status = '".$this->published."' AND valid_from = '$nextValidFrom'";
			}

			$model = $modelName::model()->findAll($criteria);
			if( $model && !in_array($model, $deleteTables) ){
				$deleteTables[$modelName] = $model;
			}

			$newModel = $modelName::model()->findAll($newCriteria);

			if( $newModel && !in_array($newModel, $updateTables) ){
				$updateTables[$modelName] = $newModel;
			}
		}

		return ['models' => $deleteTables, 'newModels' => $updateTables];

	}

	/**
	 * Flex keresés
	 * @return array
	 */
	protected function getFlexGroupsSearch()
	{
		$pub			= Status::PUBLISHED;
		$end			= App::getSetting("defaultEnd");
		$art			= new ApproverRelatedGroup;
		$compGarg		= $art->getApproverReleatedGroupSQL("Company", "employeeManagement", userID(), "", "AND", "allDate");
		$payrollGarg	= $art->getApproverReleatedGroupSQL("Payroll", "employeeManagement", userID(), "", "AND", "allDate");
		$workgroupGarg	= $art->getApproverReleatedGroupSQL("Workgroup", "employeeManagement", userID(), "", "AND", "allDate");
		$unitGarg		= $art->getApproverReleatedGroupSQL("Unit", "employeeManagement", userID(), "", "AND", "allDate");
		$cog1Garg		= $art->getApproverReleatedGroupSQL("CompanyOrgGroup1", "employeeManagement", userID(), "", "AND", "allDate");
		$cog2Garg		= $art->getApproverReleatedGroupSQL("CompanyOrgGroup2", "employeeManagement", userID(), "", "AND", "allDate");
		$compGarg		= ["where" => $compGarg["where"] ?? ""];
		$payrollGarg	= ["where" => $payrollGarg["where"] ?? ""];
		$workgroupGarg	= ["where" => $workgroupGarg["where"] ?? ""];
		$unitGarg		= ["where" => $unitGarg["where"] ?? ""];
		$cog1Garg		= ["where" => $cog1Garg["where"] ?? ""];
		$cog2Garg		= ["where" => $cog2Garg["where"] ?? ""];
		$allArray		= [["id" => "ALL", "value" => Dict::getValue("all")]];

		$mepsSQL = "
			SELECT
				`company`.`company_id` AS id,
				`company`.`company_name` AS value
			FROM `company`
			WHERE
					'{valid_date}' BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$end}')
				AND `company`.`status` = {$pub}
				AND `company`.`company_name` IS NOT NULL
				AND `company`.`company_name` <> ''
				AND `company`.`company_name` <> ' '
				{$compGarg["where"]}
			GROUP BY id
			ORDER BY value
		";

		$locationsSQL = "
			SELECT
				`company_org_group_id` AS id,
				`company_org_group_name` AS value
			FROM `company_org_group1`
			LEFT JOIN `company` ON
					(`company`.`company_id` = `company_org_group1`.`company_id` OR `company_org_group1`.`company_id` = 'ALL')
				AND `company`.`status` = {$pub}
				AND `company_org_group1`.`valid_from` <= IFNULL(`company`.`valid_to`, '{$end}')
				AND `company`.`valid_from` <= IFNULL(`company_org_group1`.`valid_to`, '{$end}')
			LEFT JOIN `payroll` ON
					(`payroll`.`payroll_id` = `company_org_group1`.`payroll_id` OR `company_org_group1`.`payroll_id` = 'ALL')
				AND `payroll`.`status` = {$pub}
				AND `company_org_group1`.`valid_from` <= IFNULL(`payroll`.`valid_to`, '{$end}')
				AND `payroll`.`valid_from` <= IFNULL(`company_org_group1`.`valid_to`, '{$end}')
			WHERE
					`company_org_group1`.`status` = {$pub}
				AND (`company_org_group1`.`company_id` IN ('{company}') OR 'ALL' IN ('{company}') OR `company_org_group1`.`company_id` = 'ALL')
				AND '{valid_date}' BETWEEN `company_org_group1`.`valid_from` AND IFNULL(`company_org_group1`.`valid_to`, '{$end}')
				AND `company_org_group1`.`company_org_group_name` IS NOT NULL
				AND `company_org_group1`.`company_org_group_name` <> ''
				AND `company_org_group1`.`company_org_group_name` <> ' '
				{$compGarg["where"]}
				{$payrollGarg["where"]}
				{$cog1Garg["where"]}
			GROUP BY id
			ORDER BY value
		";

		$payrollsSQL = "
			SELECT
				`payroll_id` AS id,
				`payroll_name` AS value
			FROM `payroll`
			LEFT JOIN `company` ON
					(`company`.`company_id` = `payroll`.`company_id` OR `payroll`.`company_id` = 'ALL')
				AND `company`.`status` = {$pub}
				AND `payroll`.`valid_from` <= IFNULL(`company`.`valid_to`, '{$end}')
				AND `company`.`valid_from` <= IFNULL(`payroll`.`valid_to`, '{$end}')
			WHERE
					`payroll`.`status` = {$pub}
				AND (`payroll`.`company_id` IN ('{company}') OR 'ALL' IN ('{company}') OR `payroll`.`company_id` = 'ALL')
				AND '{valid_date}' BETWEEN `payroll`.`valid_from` AND IFNULL(`payroll`.`valid_to`, '{$end}')
				AND `payroll`.`payroll_name` IS NOT NULL
				AND `payroll`.`payroll_name` <> ''
				AND `payroll`.`payroll_name` <> ' '
				{$compGarg["where"]}
				{$payrollGarg["where"]}
			GROUP BY id
			ORDER BY value
		";

		$workgroupsSQL = "
			SELECT
				`workgroup_id` AS id,
				`workgroup_name` AS value
			FROM `workgroup`
			LEFT JOIN `company` ON
					(`company`.`company_id` = `workgroup`.`company_id` OR `workgroup`.`company_id` = 'ALL')
				AND `company`.`status` = {$pub}
				AND `workgroup`.`valid_from` <= IFNULL(`company`.`valid_to`, '{$end}')
				AND `company`.`valid_from` <= IFNULL(`workgroup`.`valid_to`, '{$end}')
			LEFT JOIN `payroll` ON
					(`payroll`.`payroll_id` = `workgroup`.`payroll_id` OR `workgroup`.`payroll_id` = 'ALL')
				AND `payroll`.`status` = {$pub}
				AND `workgroup`.`valid_from` <= IFNULL(`payroll`.`valid_to`, '{$end}')
				AND `payroll`.`valid_from` <= IFNULL(`workgroup`.`valid_to`, '{$end}')
			WHERE
					`workgroup`.`status` = {$pub}
				AND (`workgroup`.`company_id` IN ('{company}') OR 'ALL' IN ('{company}') OR `workgroup`.`company_id` = 'ALL')
				AND (`workgroup`.`payroll_id` IN ('{payroll}') OR 'ALL' IN ('{payroll}') OR `workgroup`.`payroll_id` = 'ALL')
				AND '{valid_date}' BETWEEN `workgroup`.`valid_from` AND IFNULL(`workgroup`.`valid_to`, '{$end}')
				AND `workgroup`.`workgroup_name` IS NOT NULL
				AND `workgroup`.`workgroup_name` <> ''
				AND `workgroup`.`workgroup_name` <> ' '
				{$compGarg["where"]}
				{$payrollGarg["where"]}
				{$workgroupGarg["where"]}
			GROUP BY id
			ORDER BY value
		";

		$unitsSQL = "
			SELECT
				`unit_id` AS id,
				`unit_name` AS value
			FROM `unit`
			LEFT JOIN `company` ON
					(`company`.`company_id` = `unit`.`company_id` OR `unit`.`company_id` = 'ALL')
				AND `company`.`status` = {$pub}
				AND `unit`.`valid_from` <= IFNULL(`company`.`valid_to`, '{$end}')
				AND `company`.`valid_from` <= IFNULL(`unit`.`valid_to`, '{$end}')
			LEFT JOIN `payroll` ON
					(`payroll`.`payroll_id` = `unit`.`payroll_id` OR `unit`.`payroll_id` = 'ALL')
				AND `payroll`.`status` = {$pub}
				AND `unit`.`valid_from` <= IFNULL(`payroll`.`valid_to`, '{$end}')
				AND `payroll`.`valid_from` <= IFNULL(`unit`.`valid_to`, '{$end}')
			WHERE
					`unit`.`status` = {$pub}
				AND (`unit`.`company_id` IN ('{company}') OR 'ALL' IN ('{company}') OR `unit`.`company_id` = 'ALL')
				AND (`unit`.`payroll_id` IN ('{payroll}') OR 'ALL' IN ('{payroll}') OR `unit`.`payroll_id` = 'ALL')
				AND '{valid_date}' BETWEEN `unit`.`valid_from` AND IFNULL(`unit`.`valid_to`, '{$end}')
				AND `unit`.`unit_name` IS NOT NULL
				AND `unit`.`unit_name` <> ''
				AND `unit`.`unit_name` <> ' '
				{$compGarg["where"]}
				{$payrollGarg["where"]}
				{$unitGarg["where"]}
			GROUP BY id
			ORDER BY value
		";

		$cog2sSQL = "
			SELECT
				`company_org_group_id` AS id,
				`company_org_group_name` AS value
			FROM `company_org_group2`
			LEFT JOIN `company` ON
					(`company`.`company_id` = `company_org_group2`.`company_id` OR `company_org_group2`.`company_id` = 'ALL')
				AND `company`.`status` = {$pub}
				AND `company_org_group2`.`valid_from` <= IFNULL(`company`.`valid_to`, '{$end}')
				AND `company`.`valid_from` <= IFNULL(`company_org_group2`.`valid_to`, '{$end}')
			LEFT JOIN `payroll` ON
					(`payroll`.`payroll_id` = `company_org_group2`.`payroll_id` OR `company_org_group2`.`payroll_id` = 'ALL')
				AND `payroll`.`status` = {$pub}
				AND `company_org_group2`.`valid_from` <= IFNULL(`payroll`.`valid_to`, '{$end}')
				AND `payroll`.`valid_from` <= IFNULL(`company_org_group2`.`valid_to`, '{$end}')
			WHERE
					`company_org_group2`.`status` = {$pub}
				AND (`company_org_group2`.`company_id` IN ('{company}') OR 'ALL' IN ('{company}') OR `company_org_group2`.`company_id` = 'ALL')
				AND (`company_org_group2`.`payroll_id` IN ('{payroll}') OR 'ALL' IN ('{payroll}') OR `company_org_group2`.`payroll_id` = 'ALL')
				AND '{valid_date}' BETWEEN `company_org_group2`.`valid_from` AND IFNULL(`company_org_group2`.`valid_to`, '{$end}')
				AND `company_org_group2`.`company_org_group_name` IS NOT NULL
				AND `company_org_group2`.`company_org_group_name` <> ''
				AND `company_org_group2`.`company_org_group_name` <> ' '
				{$compGarg["where"]}
				{$payrollGarg["where"]}
				{$cog2Garg["where"]}
			GROUP BY id
			ORDER BY value
		";

		$search = [
			'valid_date' => [
				'col_type'		=> 'ed',
				'dPicker'		=> true,
				'width'			=> '*',
				'label_text'	=> Dict::getValue("date"),
				'onchange'		=> ['company', 'company_org_group1', 'payroll', 'workgroup', 'unit', 'company_org_group2', 'employee_contract'],
				'default_value'	=> date("Y-m-d")
			],
			'company' =>
			[
				'col_type'		=> 'combo',
				'label_text'	=> Dict::getValue('ext2_option17'),
				'options'		=> [
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> $mepsSQL,
						'array' => $allArray
				],
				'default_value'	=> "ALL",
				'onchange'		=> ['company_org_group1', 'payroll', 'workgroup', 'unit', 'company_org_group2', 'employee_contract']
			],
			'company_org_group1' =>
			[
				'col_type'		=> 'combo',
				'label_text'	=> Dict::getValue('company_org_group1'),
				'options'		=> [
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> $locationsSQL,
						'array' => $allArray
				],
				'default_value'	=> "ALL",
				'onchange'		=> ['employee_contract']
			],
			'payroll' =>
			[
				'col_type'		=> 'combo',
				'label_text'	=> Dict::getValue('payroll_id'),
				'options'		=> [
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> $payrollsSQL,
						'array'	=> $allArray
					],
				'default_value'	=> "ALL",
				'onchange'		=> ['workgroup', 'unit', 'company_org_group2', 'employee_contract']
			],
			'workgroup' =>
			[
				'col_type'		=> 'combo',
				'label_text'	=> Dict::getValue('workgroup'),
				'options'		=> [
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> $workgroupsSQL,
						'array'	=> $allArray
					],
				'default_value'	=> "ALL",
				'onchange'		=> ['employee_contract']
			],
			'unit' =>
			[
				'col_type'		=> 'combo',
				'label_text'	=> Dict::getValue('unit_id'),
				'options'		=> [
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> $unitsSQL,
						'array'	=> $allArray
					],
				'default_value'	=> "ALL",
				'onchange'		=> ['employee_contract']
			],
			'company_org_group2' =>
			[
				'col_type'		=> 'combo',
				'label_text'	=> Dict::getValue('company_org_group2'),
				'options'		=> [
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
						'sql'	=> $cog2sSQL,
						'array'	=> $allArray
					],
				'default_value'	=> "ALL",
				'onchange'		=> ['employee_contract']
			]
		];

		$ecSearch			= $this->getFlexEmployeeContractSearch();
		$search				= array_merge($search, $ecSearch);
		$search['submit']	= ['col_type' => 'searchBarReinitGrid', 'width' => '*', 'label_text' => ''];

		return $search;
	}

	/**
	 * Flex dolgozó keresés
	 * @return array
	 */
	protected function getFlexEmployeeContractSearch()
	{
		$searchBar	= requestParam('searchInput');
		$edFilter	=
		[
			'controllerId'	=> $this->getControllerID(),
			'search'		=> $searchBar,
			'nameFilter'	=> true,
			'where'			=> ''
		];
		$approverParams = ['processId' => 'employeeManagement'];
		$edFilter["where"] .= "
				(`company`.`company_id` IN ('{company}') OR 'ALL' IN ('{company}'))
			AND	(`company_org_group1`.`company_org_group_id` IN ('{company_org_group1}') OR 'ALL' IN ('{company_org_group1}'))
			AND (`payroll`.`payroll_id` IN ('{payroll}') OR 'ALL' IN ('{payroll}'))
			AND (`workgroup`.`workgroup_id` IN ('{workgroup}') OR 'ALL' IN ('{workgroup}'))
			AND (`unit`.`unit_id` IN ('{unit}') OR 'ALL' IN ('{unit}'))
			AND (`company_org_group2`.`company_org_group_id` IN ('{company_org_group2}') OR 'ALL' IN ('{company_org_group2}'))
		";
		$GetActiveEmployeeData = new GetActiveEmployeeData_WithoutCalendar(
			$edFilter,
			$approverParams,
			['employee', 'employee_contract', 'company', 'company_org_group1', 'payroll', 'workgroup', 'unit', 'company_org_group2'],
			'employee_contract.`employee_contract_id`'
		);
		$SQL = $GetActiveEmployeeData->getSQL();
		$SQL = "
			SELECT
				`employee_contract`.`employee_contract_id` AS id,
				" . Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]) . " AS value
			" . substr($SQL, strpos($SQL, "FROM")) . "
			ORDER BY value
		";

		$ecSearch =
		[
			'employee_contract'	=>
			[
				'col_type'		=> 'auto',
				'options'		=>	[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> $SQL
				],
				'label_text'	=> Dict::getValue("name"),
				'default_value'	=> ""
			]
		];

		return $ecSearch;
	}

	protected function getSQLFilter()
	{
		$gpf = new GetPreDefinedFilter(
					$this->getControllerID(),
					["cal.`date`"]
		);
		$SQLFilter = $gpf->getFilter();

		$SQLFilter .= "
				AND (`company`.`company_id`='{company}' OR '{company}' = 'ALL' OR '{company}' = '')
				AND (`company_org_group1`.`company_org_group_id`='{company_org_group1}' OR '{company_org_group1}' = 'ALL' OR '{company_org_group1}' = '')
				AND (`payroll`.`payroll_id`='{payroll}' OR '{payroll}' = 'ALL' OR '{payroll}' = '')
				AND (`workgroup`.`workgroup_id`='{workgroup}' OR '{workgroup}' = 'ALL' OR '{workgroup}' = '')
				AND (`unit`.`unit_id`='{unit}' OR '{unit}' = 'ALL' OR '{unit}' = '')
				AND (`company_org_group2`.`company_org_group_id`='{company_org_group2}' OR '{company_org_group2}' = 'ALL' OR '{company_org_group2}' = '')
				AND (`employee_contract`.`employee_contract_id`='{employee_contract}' OR '{employee_contract}' = 'ALL' OR '{employee_contract}' = '')
		";

		return $SQLFilter;
	}
}
