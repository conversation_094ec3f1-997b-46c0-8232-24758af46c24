<?php
class EmailSendingLogController extends Grid2Controller
{
    protected function G2BInit()
    {
        parent::setControllerPageTitleId("page_title_email_sender_log");

        $this->LAGridRights->overrideInitRights("paging", true);
        $this->LAGridRights->overrideInitRights("search", true);
        $this->LAGridRights->overrideInitRights("search_header", true);
        $this->LAGridRights->overrideInitRights("select", false);
        $this->LAGridRights->overrideInitRights("multi_select", false);
        $this->LAGridRights->overrideInitRights("column_move", true);
        $this->LAGridRights->overrideInitRights("reload_sortings", true);
        $this->LAGridRights->overrideInitRights("details", false);
        $this->LAGridRights->overrideInitRights("init_open_search", true);
        $this->LAGridRights->overrideInitRights("export_xlsx", true);

        $SQLfilter = " sent_on between DATE_FORMAT(NOW() ,'%Y-%m-01') AND LAST_DAY(CURDATE())";

        $filter = requestParam("searchInput");
        $toEmail = $filter["to_email"] ?? false;
		$validFrom = $filter["valid_from"] ?? false;
		$validTo = $filter["valid_to"] ?? false;

		if ($validFrom && $validTo) {
			$SQLfilter = " DATE(sent_on) between '" . $validFrom . "' AND '" . $validTo . "'";
		}

		if($toEmail) {
            $SQLfilter .= " and to_email like '%" . $toEmail . "%'";
        }

        $SQL = "
            SELECT 
                attachment,bcc_email, concat( '<a onmouseup=\'showemailbody(', esl.row_id, ');\' class=\'showemailbody\'>Link</a>') as body, 
                bcc_name,cc_email,cc_name,email_type,esl.row_id,sender_email,sender_name,username as sent_by,sent_on,esl.status,subject,to_email,to_name  
            FROM email_sending_log esl
            INNER JOIN `user` u 
            ON u.user_id = esl.sent_by
            AND u.status = 2
            WHERE $SQLfilter
            "
        ;

        $SQL .= " AND subject != '" . Yang::getParam('customerDbPatchName') ." - error'" ;

        $this->LAGridDB->enableSQLMode();
        $this->LAGridDB->setSQLSelection($SQL, 'row_id');

        $this->registerScript();

        parent::G2BInit();
    }

    /**
     * register script
     */
    private function registerScript() {
            Yang::registerScript('emailbody', '
                function showemailbody(id) {
                    $.ajax({
                        type: "get",
                        url: "/EmailSendingLog/getBody",
                        async: true,
                        dataType: "json",
                        data: {"id" : id},
                        success: function (res) {
                            if (res.status === "ok") {
                                notifidialog("' . Dict::getValue("body") . '", res.body, true, function() {}, "#ffa500", 720, 480);
                            } 
                            hideLoader();
                        },
                        error: function () {
                            hideLoader();
                        }
                    });
                }
            ', CClientScript::POS_END);
    }

    /**
     * @param $id
     */
    public function actionGetBody($id) {
        $sql = "select body from email_sending_log where row_id = " . $id;

        $data = dbFetch($sql, 'queryScalar');

        if($data) {
            echo json_encode([
                'status' => 'ok',
                'body' => $data
            ]);
            Yii::app()->end();
        }

        echo json_encode([
            'status' => 'error',
            'body' => ''
        ]);

        Yii::app()->end();
    }

    protected function search()
    {
        $dateFilter		=	Grid2Controller::G2BC_SEARCH_EMPLOYEE_WITH_FROM_TO;

        $ret = $this->getPreDefinedSearch($dateFilter);

        $emailSearch = ['to_email'	    => [
            'col_type'	=>'auto',
            'columnId'	=> 'to_email',
            'width'		=>'*',
            'label_text'=>Dict::getValue("to_email"),
            'options'	=>	[
                'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
                'sql'			=> "SELECT distinct to_email as id, LEFT(to_email,length(to_email)-1) as value
                            FROM email_sending_log
                            where to_email is not null
                            and to_email != ';'
                            and to_email != ''
                            ORDER BY value",
            ],
            'array'			=> [["id"=>"ALL","value"=>Dict::getValue("all")]]
        ]];

        return Yang::arrayMerge($emailSearch, $ret);
    }

    public function columns()
    {

        $retArr = array(
            'email_type' => array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150', 'report_width' => 15,),
            'sender_name' => array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '100', 'report_width' => 7,),
            'sender_email' => array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '100', 'report_width' => 7,),
            'to_name' => array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'left', 'width' => '150', 'report_width' => 7,),
            'to_email' => array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '200', 'report_width' => 7,),
            'cc_name' => array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '150', 'report_width' => 7,),
            'cc_email' => array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '150', 'report_width' => 7,),
            'bcc_name' => array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '150', 'report_width' => 7,),
            'bcc_email' => array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '100', 'report_width' => 7,),
            'subject' => array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '150', 'report_width' => 7,),
            'body' => array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '350', 'report_width' => 7,),
            'attachment' => array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '350', 'report_width' => 7,),
            'status' => array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '350', 'report_width' => 7,),
            'sent_by' => array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '350', 'report_width' => 7,),
            'sent_on' => array('grid' => true, 'export' => true, 'col_type' => 'ed', 'align' => 'center', 'width' => '350', 'report_width' => 7,),
        );

        return $retArr;
    }

    public function attributeLabels()
    {
        return array(
            'email_type' => Dict::getValue("email_type"),
            'sender_name' => Dict::getValue("sender_name"),
            'sender_email' => Dict::getValue("sender_email"),
            'to_name' => Dict::getValue("to_name"),
            'to_email' => Dict::getValue("to_email"),
            'cc_name' => Dict::getValue("cc_name"),
            'cc_email' => Dict::getValue("cc_email"),
            'bcc_name' => Dict::getValue("bcc_name"),
            'bcc_email' => Dict::getValue("bcc_email"),
            'subject' => Dict::getValue("subject"),
            'body' => Dict::getValue("body"),
            'attachment' => Dict::getValue("attachment"),
            'status' => Dict::getValue("status"),
            'sent_by' => Dict::getValue("sent_by"),
            'sent_on' => Dict::getValue("sent_on"),
        );
    }
}

?>