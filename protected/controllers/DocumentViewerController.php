<?php

Yang::import('application.components.mobile.*');

class DocumentViewerController extends Grid2Controller
{
	private string $publishedStatus;
	private string $defaultEnd;
	public function __construct() {
		parent::__construct("documentViewer");
		$this->publishedStatus = Status::PUBLISHED;
		$this->defaultEnd = App::getSetting("defaultEnd");
	}
	
	public function actionGetDocumentList()
	{
		$results = [];
		
		$docs = new DocumentTemplate;
		$criteria = new CDbCriteria;
		$criteria->select = '*';
		$criteria->condition = '`status` = :publishedStatus AND CURDATE() BETWEEN `valid_from` AND IFNULL(`valid_to`, :defaultEnd)';
		$criteria->order = '`valid_from` DESC';
		$criteria->params = [
			':publishedStatus' 			=> $this->publishedStatus,
			':defaultEnd'				=> $this->defaultEnd
		];

		$documents = $docs->findAll($criteria);
		
		foreach ($documents as $document) 
		{
			$results[] = [
				"id"			=> $document->template_id,
				"name" 			=> $document->template_name,
				"valid_from"	=> $document->valid_from
			];
		}
		
		echo json_encode($results);
	}
	
	public function actionGetSelectedDocument()
	{
		$result					= [];
		$selectedDocumentId 	= requestParam('documentId');

		$criteria = new CDbCriteria();
		$criteria->condition = 'template_id LIKE :id';
		$criteria->params = [':id' => $selectedDocumentId];
		$selectedFileData = DocumentTemplate::model()->find($criteria);

		$selectedFilename = $selectedFileData->template_name;

		$basePath = Yang::getBasePath();
		$pdfPath = $basePath . '/../webroot/file_storage/document_templates/' . Yang::getParam('customerDbPatchName') . '/' . $selectedFilename . ".pdf";

		if (file_exists($pdfPath))
		{
			$result = [
				"empty"				=> 0,
				"file"				=> base64_encode(file_get_contents($pdfPath)),
				"filename" 			=> $selectedFilename,
				"timestamp" 		=> date("Y-m-d_H-i-s")
			];
		} else {
			$result["empty"] = 1;
		}

		echo json_encode($result);
	}

	public function actionIndex($layout = '//mobile/mobile.nav', $view = '//mobile/base/documentViewer', $params = []) {
		if (App::getRight($this->getControllerID(), "view", null, userID())) {
			$this->getContent();
		} else {
			$this->redirect([Yang::getUserHome()]);
		}
	}

	private function getContent()
	{
		$data = [];
		$this->beginContent('//mobile/mobile.nav', ['error' => '', 'errorTitle' => Dict::getValue("an_error_occured")]);
		$this->renderPartial('//mobile/base/documentViewer', $data);
		$this->endContent();
	}
}