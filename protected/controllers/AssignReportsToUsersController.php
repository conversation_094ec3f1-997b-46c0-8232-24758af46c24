<?php

/**
 * Le<PERSON><PERSON><PERSON>sé<PERSON> van felhasználókhoz riport / kimutatás megtekintés jogot rendelni, rá<PERSON><PERSON>ul a saját "normál" jogosultságukon felül további felh. jogokat adni csak a választott kimutatásra.
 *
 * Innote link: assign-reports-to-users
 */
class AssignReportsToUsersController extends Grid2Controller
{
	/* Osztályváltozók */
	/**
     * Root nézi-e
     * @var boolean
     */
	private bool $isRoot;
    /**
     * Root user id
     * @var string
     */
    private string $rootUserId = "6acd9683761b153750db382c1c3694f6";
	/**
	 * Aktív státusz
	 * @var integer
	 */
	public int $pub = Status::PUBLISHED;
	/**
	 * Világvége
	 * @var string
	 */
	public string $end;
	/**
     * "Összes" dictionary
     * @var string
     */
	private string $all;
	/**
	 * Kiv<PERSON>lasztható a jogosultságnál * végződés (--> Minden olyan névvel kezdődőre ad jogot)
	 * @var int
	 */
	private int $acceptRelatedValStarEnding = 0;
	/**
	 * "Report" lista mely szerepkör csoporttól jöjjenek
	 */
	private string $authRolegroupForReportlist;

	/**
	 * Grid2 konstruktor meghívása, controller id beállítás, osztályváltozó def
	 */
	public function __construct()
	{
		parent::__construct("assignReportsToUsers");
		$this->isRoot						= App::isRootSessionEnabled();
		$this->end							= App::getSetting("defaultEnd");
		$this->acceptRelatedValStarEnding	= App::getSetting("userRightRelatedValAcceptStarEnd");
		$this->authRolegroupForReportlist	= App::getSetting("reportRightsReportlistAuthRolegroup");
		$this->all							= Dict::getValue("all");
	}

	/**
	 * Grid inicializálás, használt mód: SQL
	 * @return void
	 */
	protected function G2BInit() :void
    {
		parent::setControllerPageTitleId("page_title_assign_reports_to_users");
		parent::setExportFileName(Dict::getValue("page_title_assign_reports_to_users"));

		$this->LAGridRights->overrideInitRights("paging",				true);
		$this->LAGridRights->overrideInitRights("search",				true);
		$this->LAGridRights->overrideInitRights("search_header",		true);
		$this->LAGridRights->overrideInitRights("select",				true);
		$this->LAGridRights->overrideInitRights("multi_select",			false);
		$this->LAGridRights->overrideInitRights("column_move",			true);
		$this->LAGridRights->overrideInitRights("reload_sortings",		true);
		$this->LAGridRights->overrideInitRights("details",				false);
		$this->LAGridRights->overrideInitRights("init_open_search",		true);
		$this->LAGridRights->overrideInitRights("reload",				true);
		$this->LAGridRights->overrideInitRights("export_xlsx",			true);
		$this->LAGridRights->overrideInitRights("export_pdf_node",		false);

		$this->LAGridDB->setModelName("UserReportRights");
		$this->LAGridDB->enableSQLMode();
		$this->LAGridDB->setSQLSelection($this->getGridDataSQL(), "row_id");

		parent::G2BInit();
    }

	/**
	 * Grid2 kereső
	 * @return array
	 */
	protected function search() :array
	{
		/* Korábbi dátum behelyettesítés SESSION-ből */
        $userId         = userID();
		$controllerId   = $this->getControllerID();
		$input          = "searchInput_date";
        $replace        = false;
        $replaceWith    = "";
        if (isset($_SESSION["tiptime"][$userId][$controllerId][$input])) {
            $replace        = true;
            $replaceWith    = $_SESSION["tiptime"][$userId][$controllerId][$input];
        }

        /* Kereső mezők */
        $searchFields = [];
		$this->addDateSearch($searchFields);
		$this->addContractSearch($searchFields, $replace, $replaceWith);
        $this->addUserSearch($searchFields, $replace, $replaceWith);
		$this->addRelatedModelSearch($searchFields);
        $this->addRelatedSearch($searchFields, $replace, $replaceWith);
		$searchFields['submit'] = ['col_type' => 'searchBarReinitGrid', 'width' => '*', 'label_text' => ''];

		return $searchFields;
	}

	/**
	 * Dátum kereső
	 * @param array $searchFields
	 * @return void
	 */
	private function addDateSearch(array &$searchFields): void {
		$searchFields['date'] = $this->getDetailsHelper("*", "ed", "", "date", "", ['employee_contract', 'user_id', 'related'], true, date("Y-m-d"));
	}

	/**
     * Dolgozó kereső
     * @param array $searchFields
     * @param bool $replace
     * @param string $replaceWith
     * @return void
     */
    private function addContractSearch(array &$searchFields = [], bool $replace = false, string $replaceWith = ""): void
    {
        $employeeName = Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]);

        $searchFields['employee_contract'] =
		[
			'col_type'		=> 'auto',
			'options'		=>	[
				'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
				'sql'	=> "
					SELECT
						`employee_contract`.`employee_contract_id` AS id,
						{$employeeName} AS value
					FROM `employee`
					LEFT JOIN `employee_contract` ON
							`employee_contract`.`employee_id` = `employee`.`employee_id`
						AND `employee_contract`.`status` = {$this->pub}
						AND '{date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->end}')
						AND '{date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->end}')
					WHERE
							`employee`.`status` = {$this->pub}
						AND '{date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->end}')
						AND {$employeeName} LIKE '%%{search}%%'
					ORDER BY value
				",
				'array'	=> [["id" => "", "value" => ""]]
			],
			'label_text'	=> Dict::getValue("name"),
			'default_value'	=> ''
		];

        if ($replace) { $searchFields["employee_contract"]["options"]["sql"] = str_replace("'{date}'", "'" . $replaceWith . "'", $searchFields["employee_contract"]["options"]["sql"] ?? ""); }
    }

	/**
     * Felhasználó kereső
     * @param array $searchFields
     * @param bool $replace
     * @param string $replaceWith
     * @return void
     */
    private function addUserSearch(array &$searchFields = [], bool $replace = false, string $replaceWith = ""): void {
        $whereSQL = " AND `user`.`username` LIKE '%%{search}%%' ";
		$whereSQL .= (!$this->isRoot) ? " AND `user`.`user_id` <> '{$this->rootUserId}' " : ""; // WHERE - root jogok jelenjenek-e meg
        $searchFields['user_id'] = $this->getDetailsHelper("*", "auto", "SQL", "user", $whereSQL, [], false, "", [["id" => "", "value" => ""]]);
        if ($replace) { $searchFields["user_id"]["options"]["sql"] = str_replace("'{date}'", "'" . $replaceWith . "'", $searchFields["user_id"]["options"]["sql"] ?? ""); }
    }

	/**
     * Csoportosítás kereső
     * @param array $searchFields
     * @return void
     */
    private function addRelatedModelSearch(array &$searchFields = []): void
    {
        $searchFields['related_model'] =
		[
			'col_type'		=> 'combo',
			'options'		=>
            [
				'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
				'sql'	=> "
					SELECT
						art.`related_model` AS id,
						d.`dict_value` AS value
					FROM `approver_related_type` art
					LEFT JOIN `dictionary` d ON
							d.`dict_id` = art.`name_dict_id`
						AND d.`lang` = '" . Dict::getLang() . "'
					WHERE
							art.`status` = {$this->pub}
						AND (d.`dict_value` LIKE '%%{search}%%')
					ORDER BY d.`dict_value`
				",
				'array'	=> [["id" => "ALL", "value" => $this->all]]
			],
			'label_text'	=> Dict::getValue("classification"),
			'default_value'	=> ''
		];
    }

	/**
     * Csoportosítás érték kereső
     * @param array $searchFields
     * @param bool $replace
     * @param string $replaceWith
     * @return void
     */
    private function addRelatedSearch(array &$searchFields = [], bool $replace = false, string $replaceWith = ""): void
    {
        $searchFields['related'] =
		[
			'col_type'		=> 'auto',
			'options'		=> [
				'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
				'sql'	=> $this->getSearchRelatedSQL(),
				'array'	=> [["id" => "", "value" => ""]]
			],
			'label_text'	=> Dict::getValue("selected_group_or_employee"),
			'default_value'	=> ''
		];

        if ($replace) { $searchFields["related"]["options"]["sql"] = str_replace("'{date}'", "'" . $replaceWith . "'", $searchFields["related"]["options"]["sql"] ?? ""); }
    }

	/**
	 * Visszaadja a keresőbe a csoport érték lekérő SQL-t.
	 * @return string
	 */
	private function getSearchRelatedSQL(): string
	{
        $relatedDefinitions =
        [
            "unit"                  => ["cogId" => false, 'relatedModel' => 'Unit'],
            "workgroup"             => ["cogId" => false, 'relatedModel' => 'Workgroup'],
            "company"               => ["cogId" => false, 'relatedModel' => 'Company'],
            "payroll"               => ["cogId" => false, 'relatedModel' => 'Payroll'],
            "company_org_group1"    => ["cogId" => true,  'relatedModel' => 'CompanyOrgGroup1'],
            "company_org_group2"    => ["cogId" => true,  'relatedModel' => 'CompanyOrgGroup2'],
            "company_org_group3"    => ["cogId" => true,  'relatedModel' => 'CompanyOrgGroup3'],
            "cost"                  => ["cogId" => false, 'relatedModel' => 'Cost']
        ];

        $groupsSQL = "";
        foreach ($relatedDefinitions as $table => $tableData)
        {
            $tableId            = ($tableData["cogId"]) ? "company_org_group" : $table;
            $groupsSQL          .= "
                UNION
                SELECT
                    `{$table}`.`{$tableId}_id` AS id,
                    `{$table}`.`{$tableId}_name` AS value
                FROM `{$table}`
                WHERE
                        `{$table}`.`status` = {$this->pub}
                    AND '{date}' BETWEEN `{$table}`.`valid_from` AND IFNULL(`{$table}`.`valid_to`, '{$this->end}')
                    AND '" . ($tableData["relatedModel"] ?? "") . "' = '{related_model}'
            ";
        }

		$employeeName = Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]);
		$SQL = "
			SELECT a.* FROM
			(
				SELECT 'ALL' AS `id`, '{$this->all}' AS `value`
                {$groupsSQL}
				UNION
				SELECT
					`employee_contract`.`employee_contract_id` AS id,
					{$employeeName} AS value
				FROM `employee_contract`
				LEFT JOIN `employee` ON
						`employee`.`employee_id` = `employee_contract`.`employee_id`
					AND `employee`.`status` = {$this->pub}
					AND '{date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->end}')
				WHERE
						`employee_contract`.`status` = {$this->pub}
					AND '{date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->end}')
					AND '{date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->end}')
					AND 'EmployeeContract' = '{related_model}'
			) a
			WHERE a.`value` like '%%{search}%%'
            ORDER BY a.`value`
		";

		return $SQL;
	}

	/**
	 * Bácsi féle cache törlés
	 * @return void
	 */
	private function delCache(): void {
		AnyCache::destroyByName('getApproverReleatedGroupSQL');
		AnyCache::destroyByName('getApproverArray');
		unset($_SESSION["tiptime"]["visibility"]);
	}

	/**
	 * Grid2 col def helper
	 * @param [type] $width
	 * @param string $type
	 * @param string $mode
	 * @param string $labelAndSQLTable
	 * @param string $sqlModeWhereSQL
	 * @param array $onChange
	 * @param boolean $dPicker
	 * @param string $def
	 * @param array $comboArr
	 * @param integer $grid
	 * @param integer $export
	 * @param integer $reportWidth
	 * @param integer $window
	 * @return array
	 */
	private function getDetailsHelper(
        $width                       		= "*",
        string $type                        = "ed",
        string $mode                        = "",
        string $labelAndSQLTable            = "",
        string $sqlModeWhereSQL             = "",
        array $onChange                     = [],
        bool $dPicker                       = false,
        string $def                         = "",
        array $comboArr                     = [],
		int $grid							= 2,
		int $export							= 2,
		int $reportWidth					= 0,
		int $window							= 2,
		int $lineBreak						= 2
    ): array
	{
		$col					    = [];
		$col["width"]			    = $width;
		$col["col_type"]		    = $type;

        /* SQL Mode */
        if ($mode === "SQL" && ($type === "combo" || $type === "auto"))
        {
			// Sajnos nem user_name lett ..
            if ($labelAndSQLTable === "user") { $name = "username"; } else { $name = $labelAndSQLTable . "_name"; }
            $col["options"]         =
            [
                "mode"  => Grid2Controller::G2BC_QUERY_MODE_SQL,
                "sql"   => "
                    SELECT
                        `{$labelAndSQLTable}`.`{$labelAndSQLTable}_id` AS id,
                        `{$labelAndSQLTable}`.`{$name}` AS value
                    FROM `{$labelAndSQLTable}`
                    WHERE
                            `{$labelAndSQLTable}`.`status` = {$this->pub}
                        AND '{date}' BETWEEN `{$labelAndSQLTable}`.`valid_from` AND IFNULL(`{$labelAndSQLTable}`.`valid_to`, '{$this->end}')
                        {$sqlModeWhereSQL}
                    ORDER BY `value`
                "
            ];
        }
        /* Array Mode */
        if ($mode === "ARR" && ($type === "combo" || $type === "auto")) {
            $col["options"]         = ["mode" => Grid2Controller::G2BC_QUERY_MODE_ARRAY];
        }
        /* Array */
        if (!empty($comboArr)) {
            $col["options"]["array"]= $comboArr;
        }
        /* Label */
        if ($labelAndSQLTable != "") {
            $col["label_text"]      = Dict::getValue($labelAndSQLTable);
        }
        /* Onchange Fields*/
        if (!empty($onChange)) {
            $col["onchange"]        = $onChange;
        }
        /* Datepicker */
        if ($dPicker) {
            $col["dPicker"]         = true;
        }
        /* Default Value */
        if ($def != "") {
            $col["default_value"]   = $def;
        }
		/* Grid */
		if ($grid === 1) {
			$col["grid"]			= true;
		} else if ($grid === 0) {
			$col["grid"]			= false;
		}
		/* Export */
		if ($export === 1) {
			$col["export"]			= true;
		} else if ($export === 0) {
			$col["export"]			= false;
		}
		/* Report Width */
		if ($reportWidth != 0) {
			$col["report_width"]	= $reportWidth;
		}
		/* Window */
		if ($window === 1) {
			$col["window"]			= true;
		} else if ($window === 0) {
			$col["window"]			= false;
		}
		/* Line Break */
		if ($lineBreak === 1) {
			$col["line_break"]		= true;
		} else if ($lineBreak === 0) {
			$col["line_break"]		= false;
		}

		return $col;
	}

	/**
	 * Grid2 oszlop definiálás, column right check: ON
	 * @return array
	 */
	public function columns() :array
	{
		/* Kereső */
		$filter				= requestParam('searchInput');
		$rowId				= requestParam('editPK') !== null ? requestParam('editPK') : 0;
		$date				= $filter['date'] ?? "";
		/* Beállítások */
        $userIdType			= App::getSetting("userRightDialogApproverUserIdComponentType"); // Auto típusú felvétel (flexnél túl nagy legördülő)
        $relatedValueType	= App::getSetting("userRightDialogRelatedValueComponentType"); // Auto típusú felvétel (flexnél túl nagy legördülő)

        /* Autocomplete Mode */
        if ($userIdType === 'auto') {
            $userIdSearch			= " AND `user`.`username` LIKE '%%{search}%%' ";
			$defaultValueUser		= ($rowId !== 0 ? $this->getUserIdComputedValue((int)$rowId) : '');
        } else {
            $userIdSearch			= "";
			$defaultValueUser		= "";
        }
        if ($relatedValueType === 'auto') {
            $relatedValueSearch		= " AND value LIKE '%%{search}%%' ";
			$defaultValueRelated	= ($rowId !== 0 ? $this->getRelatedValueComputedValue((int)$rowId) : '');
        } else {
            $relatedValueSearch		= "";
			$defaultValueRelated	= "";
        }

		/* User WHERE */
		$userWhereSQL = $userIdSearch;
		$userWhereSQL .= " AND ('{valid_from}' BETWEEN `user`.`valid_from` AND IFNULL(`user`.`valid_to`, '{$this->end}') OR '{valid_to}' BETWEEN `user`.`valid_from` AND IFNULL(`user`.`valid_to`, '{$this->end}')) ";
		$userWhereSQL .= (!$this->isRoot) ? " AND `user`.`user_id` <> '{$this->rootUserId}' " : ""; // Root jogok jelenjenek-e meg

		$columns =
		[
			'valid_from'		=> $this->getDetailsHelper("*", "ed", "", "", "", ['approver_user_id', 'related_value'], true, "", [], 0, 0, 20, 1),
			'valid_to'			=> $this->getDetailsHelper("*", "ed", "", "", "", ['approver_user_id', 'related_value'], true, "", [], 0, 0, 20, 1),
			'username'			=> $this->getDetailsHelper(220, "ed", "", "", "", [], false, "", [], 2, 1, 20, 0),
			'user_id'			=> $this->getDetailsHelper(220, $userIdType, "SQL", "user", $userWhereSQL, ['related_value'], false, $defaultValueUser, [["id" => "", "value" => ""]], 0, 0, 20, 1),
			'role_id'			=>
			[
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width' 		=> 500,
				'options'		=>
				[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							ar.`role_id` AS id,
							IF(d.`dict_value` IS NOT NULL, CONCAT(d.`dict_value`, ' - ', ac.`controller_id`), ac.`controller_id`) AS `value`
						FROM `auth_role` ar
						JOIN `auth_acl` ac ON
								ac.`role_id` = ar.`role_id`
							AND ac.`operation_id` = 'view'
						LEFT JOIN `menu_item_table` m ON m.`menu_visible` = ac.`controller_id`
						LEFT JOIN `dictionary` d ON
								d.`module` = m.`menu_modul`
							AND d.`lang` = '" . Dict::getLang() . "'
							AND d.`dict_id` = m.`menu_label`
						JOIN `auth_role_in_group` arig ON
								arig.`role_id` = ar.`role_id`
							AND arig.`rolegroup_id` = '{$this->authRolegroupForReportlist}'
						WHERE (ar.`role_name` LIKE '%report%' OR ar.`role_name` LIKE '%attendance%')
							AND ar.`customer_visibility` = 1
						GROUP BY ar.`role_id`
						ORDER BY value
					",
					'array'	=> [["id" => "", "value" => ""]]
				]
			],
			'related_model'		=>
			[
				'export'		=> true,
				'report_width'	=> 20,
				'col_type'		=> 'combo',
				'width' 		=> 220,
				'onchange' 		=> ['related_id', 'related_value'],
				'options'		=>
				[
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> "
						SELECT
							art.`related_model` AS id,
							d.`dict_value` AS value
						FROM `approver_related_type` art
						LEFT JOIN `dictionary` d ON
								d.`dict_id` = art.`name_dict_id`
							AND d.`lang` = '" . Dict::getLang() . "'
						WHERE art.`status` = {$this->pub}
						ORDER BY d.`dict_value`
					",
					'array'	=> [["id" => "", "value" => ""]]
				]
			],
			'related_value'	=>
			[
				'export' 		=> false,
				'report_width'	=> 20,
				'col_type' 		=> $relatedValueType,
				'width' 		=> 300,
				'grid' 			=> false,
				'options'		=> [
					'mode'	=> Grid2Controller::G2BC_QUERY_MODE_SQL,
					'sql'	=> $this->createRelatedValueSQL($date, $relatedValueSearch),
					'array'	=> [["id" => "", "value" => ""]]
				],
				'starExpression'=> ($this->acceptRelatedValStarEnding == "1") ? true : false,
				'default_value'	=> $defaultValueRelated
			],
			'related'			=> $this->getDetailsHelper(220, "ed", "", "", "", [], false,  "", [], 1, 1, 20, 0),
			'grid_valid_from'	=> $this->getDetailsHelper("*", "ed", "", "", "", [], true, "", [], 1, 1, 20, 0),
			'grid_valid_to'		=> $this->getDetailsHelper("*", "ed", "", "", "", [], true, "", [], 1, 1, 20, 0),
			'filter_himself'	=> [
					'export'		=> true,
					'report_width'	=> 20,
					'col_type'		=> 'combo',
					'width' 		=> 500,
					'options'		=>
					[
						'mode'	=> Grid2Controller::G2BC_QUERY_MODE_ARRAY,
						'array'	=> App::getLookup('yes_no')
					]
			],
		];
		/* Kereső dátum behelyettesítés */
		$columns['user_id']['options']['sql'] = str_replace("'{date}'", "'" . $date . "'", $columns['user_id']['options']['sql'] ?? "");

		$columns = $this->columnRights($columns);

		return $columns;
	}

	/**
     * Get computed value for report right user autocomplete
     * @param int $id
     * @return string
     */
	private function getUserIdComputedValue(int $id = 0): string
    {
		$userId	= AnyCache::get("UserReportRight.getUserIdComputedValueId");
		$result	= AnyCache::get("UserReportRight.getUserIdComputedValueResult");

		if ($id != (int)$userId)
		{
			$SQL = "
				SELECT u.`username` AS value
				FROM `user_report_rights` AS urr
				LEFT JOIN `user` AS u ON urr.`user_id` = u.`user_id` AND u.`status` = {$this->pub}
				WHERE urr.`row_id` = {$id} AND urr.`status` = {$this->pub}
				LIMIT 1
			";
			$result = dbFetchValue($SQL);

			AnyCache::set("UserReportRight.getUserIdComputedValueId",		$id);
			AnyCache::set("UserReportRight.getUserIdComputedValueResult",	$result);
		}

        return $result;
    }

	/**
     * Get computed value for related value autocomplete
     * @param int $id
     * @return string
     */
    private function getRelatedValueComputedValue(int $id = 0): string
    {
		$rightId	= AnyCache::get("UserReportRight.getRelatedValueComputedValueId");
		$result		= AnyCache::get("UserReportRight.getRelatedValueComputedValueResult");
		if ($id != (int)$rightId)
		{
			// Get report right data
			$SQL = "
				SELECT
					urr.`related_model` AS related_model,
					urr.`related_id` AS related_id,
					urr.`related_value` AS related_value
				FROM `user_report_rights` AS urr
				WHERE urr.`row_id` = {$id} AND urr.`status` = {$this->pub}
			";
			$right = dbFetchRow($SQL);
			if (!is_array($right)) { return ''; }

			// Get related data
			$model			= $right["related_model"];
			$columnId		= $right["related_id"];
			$columnValue	= $right["related_value"];
			$table			= $model::model()->tableSchema->name;

			$columnNames	=
			[
				'Unit'             => 'tab.`unit_name`',
				'Workgroup'        => 'tab.`workgroup_name`',
				'Company'          => 'tab.`company_name`',
				'Payroll'          => 'tab.payroll_name',
				'EmployeeContract' => Employee::getParam('fullname_with_emp_id_ec_id', ["e", "ec"]),
				'CompanyOrgGroup1' => 'tab.`company_org_group_name`',
				'CompanyOrgGroup2' => 'tab.`company_org_group_name`',
				'CompanyOrgGroup3' => 'tab.`company_org_group_name`',
				'Cost'             => 'tab.`cost_name`',
				'Competency'       => 'tab.`competency_name`'
			];

			if ($columnValue == 'ALL') {
				$result = Dict::getValue($table . "_id") . " " . $this->all;
			} else {
				if ($model == 'EmployeeContract')
				{
					$SQL = "
						SELECT
							{$columnNames[$model]} AS name
						FROM `employee_contract` AS ec
						LEFT JOIN `employee` AS e ON ec.`employee_id` = e.`employee_id` AND e.`status` = {$this->pub}
						WHERE
								ec.{$columnId} = '{$columnValue}'
							AND ec.`status` = {$this->pub}
							AND ec.`valid_from` <= IFNULL(e.`valid_to`, '{$this->end}') AND e.`valid_from` <= IFNULL(ec.`valid_to`, '{$this->end}')
							AND ec.`ec_valid_from` <= IFNULL(e.`valid_to`, '{$this->end}') AND e.`valid_from` <= IFNULL(ec.`ec_valid_to`, '{$this->end}')
						GROUP BY e.`employee_id`
					";
				} else {
					$SQL = "
						SELECT
							{$columnNames[$model]} AS name
						FROM
							{$table} tab
						WHERE
								tab.{$columnId} = '{$columnValue}'
							AND tab.status = {$this->pub}
							AND CURDATE() BETWEEN tab.valid_from and IFNULL(tab.`valid_to`, '{$this->end}')
					";
				}
				$result = dbFetchValue($SQL);

				if (empty($result) && substr($columnValue, -1) == "*") {
					$result = $columnValue;
				}
			}

			AnyCache::set("UserReportRight.getRelatedValueComputedValueId",		$id);
			AnyCache::set("UserReportRight.getRelatedValueComputedValueResult",	$result);
		}

        return $result;
    }

	/**
	 * Related valuekat generáló SQL
	 * @param string $date
	 * @param string $relatedValueSearch
	 * @return string
	 */
	protected function createRelatedValueSQL(string $date = "", string $relatedValueSearch = ""): string
	{
		$all = $this->all;
		$pub = $this->pub;
		$end = $this->end;

		$unionAllSQL = "
			UNION
			SELECT
				'ALL' AS id,
				IF('Unit'				= '{related_model}', '" . Dict::getValue("unit_id")					. " {$all}',
				IF('Workgroup'			= '{related_model}', '" . Dict::getValue("workgroup_id")			. " {$all}',
				IF('Company'			= '{related_model}', '" . Dict::getValue("company_id")				. " {$all}',
				IF('Payroll'			= '{related_model}', '" . Dict::getValue("payroll_id")				. " {$all}',
				IF('EmployeeContract'	= '{related_model}', '" . Dict::getValue("employee_contract_id")	. " {$all}',
				IF('CompanyOrgGroup1'	= '{related_model}', '" . Dict::getValue("company_org_group1")		. " {$all}',
				IF('CompanyOrgGroup2'	= '{related_model}', '" . Dict::getValue("company_org_group2")		. " {$all}',
				IF('CompanyOrgGroup3'	= '{related_model}', '" . Dict::getValue("company_org_group3")		. " {$all}',
				IF('Competency'			= '{related_model}', '" . Dict::getValue("competency") 				. " {$all}',
				IF('Cost'				= '{related_model}', '" . Dict::getValue("cost") 					. " {$all}',
				IF('Employee'			= '{related_model}', '" . Dict::getValue("employee") 				. " {$all}',
				'')))))))))))
		";

		$relatedDefinitions =
        [
            "unit"                  => ["cogId" => false, "joinCompany" => true,	"joinPayroll" => true,	"relatedModel" => 'Unit'],
            "workgroup"             => ["cogId" => false, "joinCompany" => true,	"joinPayroll" => true,	"relatedModel" => 'Workgroup'],
            "company"               => ["cogId" => false, "joinCompany" => false,	"joinPayroll" => false,	"relatedModel" => 'Company'],
            "payroll"               => ["cogId" => false, "joinCompany" => true,	"joinPayroll" => false,	"relatedModel" => 'Payroll'],
            "company_org_group1"    => ["cogId" => true,  "joinCompany" => true,	"joinPayroll" => true,	"relatedModel" => 'CompanyOrgGroup1'],
            "company_org_group2"    => ["cogId" => true,  "joinCompany" => true,	"joinPayroll" => true,	"relatedModel" => 'CompanyOrgGroup2'],
            "company_org_group3"    => ["cogId" => true,  "joinCompany" => true,	"joinPayroll" => true,	"relatedModel" => 'CompanyOrgGroup3'],
            "cost"                  => ["cogId" => false, "joinCompany" => true,	"joinPayroll" => true,	"relatedModel" => 'Cost']
        ];

        $groupsSQL	= "";
		$counter	= 0;
        foreach ($relatedDefinitions as $table => $tableData)
        {
			if ($tableData["joinCompany"]) {
				$companyJoinSQL = "
				LEFT JOIN `company` ON
						`company`.`company_id` = `{$table}`.`company_id`
					AND `company`.`status` = {$pub}
					AND '{$date}' BETWEEN `company`.`valid_from` AND IFNULL(`company`.`valid_to`, '{$end}')
				";
			} else {
				$companyJoinSQL = "";
			}
			if ($tableData["joinPayroll"]) {
				$payrollJoinSQL = "
				LEFT JOIN `payroll` ON
						`payroll`.`payroll_id` = `{$table}`.`payroll_id`
					AND `payroll`.`status` = {$pub}
					AND '{$date}' BETWEEN `payroll`.`valid_from` AND IFNULL(`payroll`.`valid_to`, '{$end}')
				";
			} else {
				$payrollJoinSQL = "";
			}
            $tableId	= ($tableData["cogId"]) ? "company_org_group" : $table;

            $groupsSQL	.= "
				" . (($counter > 0) ? "UNION" : "") . "
                SELECT
                    `{$table}`.`{$tableId}_id` AS id,
                    `{$table}`.`{$tableId}_name` AS value
                FROM `{$table}`
				{$companyJoinSQL}
				{$payrollJoinSQL}
                WHERE
                        `{$table}`.`status` = {$pub}
					AND '" . ($tableData["relatedModel"] ?? "") . "' = '{related_model}'
					AND '{$date}' BETWEEN `{$table}`.`valid_from` AND IFNULL(`{$table}`.`valid_to`, '{$end}')
					AND (
						'{valid_from}' BETWEEN `{$table}`.`valid_from` AND IFNULL(`{$table}`.`valid_to`, '{$end}') OR
						'{valid_to}' BETWEEN `{$table}`.`valid_from` AND IFNULL(`{$table}`.`valid_to`, '{$end}')
					)
            ";
			$counter++;
        }

		$name1 = Employee::getParam('fullname_with_emp_id_ec_id', ["employee", "employee_contract"]);
		$name2 = Employee::getParam('fullname', 'employee');
		$SQL = "
			SELECT
				id,
				value
			FROM (
				{$groupsSQL}
			UNION
			SELECT
				`employee_contract`.`employee_contract_id` AS id,
				{$name1} AS value
			FROM `employee_contract`
			LEFT JOIN `employee` ON
					`employee_contract`.`employee_id` = `employee`.`employee_id`
				AND `employee`.`status` = {$pub}
				AND (CURDATE() BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$end}'))
				AND ('{valid_from}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$end}') OR '{valid_to}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$end}'))
			WHERE
					`employee_contract`.`status`= {$pub}
				AND 'EmployeeContract' = '{related_model}'
				AND (CURDATE() BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$end}'))
				AND (CURDATE() BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$end}'))
				AND ('{valid_from}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$end}') OR '{valid_to}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$end}'))
				AND `employee`.`row_id` IS NOT NULL
		";

		if (weHaveModule('ttwa-csm')) {
			$SQL .= "
				UNION
				SELECT
					comp.`competency_id` AS id,
					comp.`competency_name` AS value
				FROM `competency` comp
				WHERE
						comp.`status` = {$pub}
					AND 'Competency' = '{related_model}'
			";
		}

		$SQL .= "
				UNION
				SELECT
					`employee`.`employee_id` AS id,
					{$name2} AS value
				FROM `employee`
				LEFT JOIN `employee_contract` ON
						`employee_contract`.`employee_id` = `employee`.`employee_id`
					AND `employee_contract`.`status` = {$pub}
					AND CURDATE() BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$end}')
					AND CURDATE() BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$end}')
				WHERE
						`employee`.`status` = {$pub}
					AND 'Employee' = '{related_model}'
					AND (CURDATE() BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$end}'))
					AND ('{valid_from}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$end}') OR '{valid_to}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$end}'))
				{$unionAllSQL}
				) as value
				WHERE 1 = 1 {$relatedValueSearch}
				ORDER BY value
		";

		return $SQL;
	}

	/**
	 * Grid2 oszlop felirat definiálás
	 * @return array
	 */
	public function attributeLabels() :array
	{
		$labels =
		[
			'role_id'			=> Dict::getValue("role_name"),
			'related_model'		=> Dict::getValue("classification"),
			'username'			=> Dict::getValue("user"),
			'user_id'			=> Dict::getValue("user"),
			'related'			=> Dict::getValue("selected_group_or_employee"),
			'related_value'		=> Dict::getValue("selected_group_or_employee"),
			'valid_from'		=> Dict::getValue("valid_from"),
			'valid_to'			=> Dict::getValue("valid_to"),
			'grid_valid_from'	=> Dict::getValue("valid_from"),
			'grid_valid_to'		=> Dict::getValue("valid_to"),
			'filter_himself'	=> Dict::getValue("filter_himself")
		];
		return $labels;
	}

	/**
	 * Grid adattartalmat visszaadó SQL
	 * @return string
	 */
	private function getGridDataSQL(): string {
		$whereSQL = " AND (`employee_contract`.`employee_contract_id` = '{employee_contract}' OR '{employee_contract}' = '' OR '{employee_contract}' = 'ALL')"; // WHERE - contract szűrő
		$whereSQL .= (!$this->isRoot) ? " AND u.`user_id` <> '{$this->rootUserId}' " : ""; // WHERE - root jogok jelenjenek-e meg
		$whereSQL .= " AND (urr.related_value = '{related}' OR '{related}' = '' OR '{related}' = 'ALL') "; // WHERE - csoport érték szűrő
		$whereSQL .= " AND (urr.related_model = '{related_model}' OR '{related_model}' = '' OR '{related_model}' = 'ALL') "; // WHERE - csoportosítás szűrő
		$whereSQL .= " AND (u.`user_id` = '{user_id}' OR '{user_id}' = '' OR '{user_id}' = 'ALL') "; // WHERE - approver userszűrő
		$whereSQL .= " AND '{date}' BETWEEN urr.`valid_from` AND IFNULL(urr.`valid_to`, '{$this->end}') "; // WHERE - dátumszűrő

		$SQL = "
			SELECT
				urr.`role_id`,
				urr.`related_model`,
				IF(urr.`related_model` = 'Unit',				IF(urr.`related_value` = 'ALL', '" . Dict::getValue("unit_id")				. " {$this->all}',	IFNULL(un.`unit_name`, urr.`related_value`)),
				IF(urr.`related_model` = 'Workgroup',			IF(urr.`related_value` = 'ALL', '" . Dict::getValue("workgroup_id")			. " {$this->all}',	IFNULL(w.`workgroup_name`, urr.`related_value`)),
				IF(urr.`related_model` = 'Company',				IF(urr.`related_value` = 'ALL', '" . Dict::getValue("company_id") 			. " {$this->all}',	IFNULL(c.`company_name`, urr.`related_value`)),
				IF(urr.`related_model` = 'Payroll',				IF(urr.`related_value` = 'ALL', '" . Dict::getValue("payroll_id") 			. " {$this->all}',	IFNULL(p.`payroll_name`, urr.`related_value`)),
				IF(urr.`related_model` = 'EmployeeContract',	IF(urr.`related_value` = 'ALL', '" . Dict::getValue("employee_contract_id")	. " {$this->all}', 	IF(ec.`row_id` IS NOT NULL, " . Employee::getParam('fullname_with_emp_id_ec_id', ["e", "ec"]) . " , urr.`related_value`)),
				IF(urr.`related_model` = 'CompanyOrgGroup1',	IF(urr.`related_value` = 'ALL', '" . Dict::getValue("company_org_group1") 	. " {$this->all}',	IFNULL(cog1.`company_org_group_name`, urr.`related_value`)),
				IF(urr.`related_model` = 'CompanyOrgGroup2',	IF(urr.`related_value` = 'ALL', '" . Dict::getValue("company_org_group2") 	. " {$this->all}',	IFNULL(cog2.`company_org_group_name`, urr.`related_value`)),
				IF(urr.`related_model` = 'CompanyOrgGroup3',	IF(urr.`related_value` = 'ALL', '" . Dict::getValue("company_org_group3") 	. " {$this->all}',	IFNULL(cog3.`company_org_group_name`, urr.`related_value`)),
				IF(urr.`related_model` = 'Cost',				IF(urr.`related_value` = 'ALL', '" . Dict::getValue("cost") 				. " {$this->all}',	IFNULL(cost2.`cost_name`, urr.`related_value`)),
		";
		if (weHaveModule('ttwa-csm')) {
			$SQL .= "
				IF(urr.`related_model`= 'Competency',			IF(urr.`related_value` = 'ALL', '" . Dict::getValue("comp") 				. " {$this->all}',	IFNULL(comp.`competency_name`, urr.`related_value`)),
				IF(urr.`related_value` = 'ALL', '" . Dict::getValue("employee_id") . " {$this->all}', IF(em.`row_id` IS NOT NULL, CONCAT(" . Employee::getParam('fullname', 'em') . ", ' - ', em.`emp_id`), urr.`related_value`)
				)))))))))))	as related,
			";
		} else {
			$SQL .=	"
				IF(urr.`related_value` = 'ALL', '" . Dict::getValue("employee_id") . " {$this->all}', IF(em.`row_id` IS NOT NULL, CONCAT(" . Employee::getParam('fullname', 'em') . ", ' - ', em.`emp_id`), urr.`related_value`)
				)))))))))) as related,
			";
		}
		$SQL .= "
				urr.`user_id`,
				u.`username`,
				urr.`valid_from`,
				urr.`valid_to`,
				urr.`valid_from` AS grid_valid_from,
				urr.`valid_to` AS grid_valid_to,
				urr.`row_id`,
				urr.filter_himself
			FROM `user_report_rights` urr
			LEFT JOIN (
				SELECT
					`related_model`,
					`related_id`
				FROM `approver_related_group`
				WHERE `status`= {$this->pub}
				GROUP BY `related_model`
			) arg ON arg.`related_model` = urr.`related_model` AND arg.`related_id` = urr.`related_id`
			JOIN `approver_related_type` art ON
					art.`related_model` = arg.`related_model`
				AND art.`related_id` = arg.`related_id`
				AND art.`status` = {$this->pub}
			LEFT JOIN `user` u ON
					urr.`user_id` = u.`user_id`
				AND u.`status` = {$this->pub}
				AND '{date}' BETWEEN u.`valid_from` AND IFNULL(u.`valid_to`, '{$this->end}')
			LEFT JOIN `unit` un ON
					urr.`related_value`= un.`unit_id`
				AND un.`status` = {$this->pub}
				AND urr.`related_model` = 'Unit'
				AND '{date}' BETWEEN un.`valid_from` AND IFNULL(un.`valid_to`, '{$this->end}')
			LEFT JOIN `workgroup` w ON
					urr.`related_value` = w.`workgroup_id`
				AND w.`status` = {$this->pub}
				AND urr.`related_model` = 'Workgroup'
				AND '{date}' BETWEEN w.`valid_from` AND IFNULL(w.`valid_to`, '{$this->end}')
			LEFT JOIN `company` c ON
					urr.`related_value` = c.`company_id`
				AND c.`status` = {$this->pub}
				AND urr.`related_model` = 'Company'
				AND '{date}' BETWEEN c.`valid_from` AND IFNULL(c.`valid_to`, '{$this->end}')
			LEFT JOIN `payroll` p ON
					p.`payroll_id` = urr.`related_value`
				AND p.`status` = {$this->pub}
				AND urr.`related_model` = 'Payroll'
				AND '{date}' BETWEEN p.`valid_from` AND IFNULL(p.`valid_to`, '{$this->end}')
			LEFT JOIN `employee_contract` ec ON
					ec.`employee_contract_id` = urr.`related_value`
				AND ec.`status` = {$this->pub}
				AND urr.`related_model` = 'EmployeeContract'
				AND '{date}' BETWEEN ec.`valid_from` AND IFNULL(ec.`valid_to`, '{$this->end}')
				AND '{date}' BETWEEN ec.`ec_valid_from` AND IFNULL(ec.`ec_valid_to`, '{$this->end}')
			LEFT JOIN `employee` e ON
					ec.`employee_id` = e.`employee_id`
				AND e.`status` = {$this->pub}
				AND '{date}' BETWEEN e.`valid_from` AND IFNULL(e.`valid_to`, '{$this->end}')
			LEFT JOIN `company_org_group1` cog1 ON
					cog1.`company_org_group_id` = urr.`related_value`
				AND cog1.`status` = {$this->pub}
				AND urr.`related_model` = 'CompanyOrgGroup1'
				AND '{date}' BETWEEN cog1.`valid_from` AND IFNULL(cog1.`valid_to`, '{$this->end}')
			LEFT JOIN `company_org_group2` cog2 ON
					cog2.`company_org_group_id` = urr.`related_value`
				AND cog2.`status` = {$this->pub}
				AND urr.`related_model` = 'CompanyOrgGroup2'
				AND '{date}' BETWEEN cog2.`valid_from` AND IFNULL(cog2.`valid_to`, '{$this->end}')
			LEFT JOIN `company_org_group3` cog3 ON
					cog3.`company_org_group_id` = urr.`related_value`
				AND cog3.`status` = {$this->pub}
				AND urr.`related_model` = 'CompanyOrgGroup3'
				AND '{date}' BETWEEN cog3.`valid_from` AND IFNULL(cog3.`valid_to`, '{$this->end}')
			LEFT JOIN `cost` cost2 ON
					cost2.`cost_id` = urr.`related_value`
				AND cost2.`status` = {$this->pub}
				AND urr.`related_model` = 'Cost'
				AND '{date}' BETWEEN cost2.`valid_from` AND IFNULL(cost2.`valid_to`, '{$this->end}')
			LEFT JOIN `employee` em ON
					em.`employee_id` = ec.`employee_id`
				AND em.`status` = {$this->pub}
				AND '{date}' BETWEEN em.`valid_from` AND IFNULL(em.`valid_to`, '{$this->end}')
			LEFT JOIN `employee` ON
					u.`employee_id` = `employee`.`employee_id`
				AND `employee`.`status` = {$this->pub}
				AND '{date}' BETWEEN `employee`.`valid_from` AND IFNULL(`employee`.`valid_to`, '{$this->end}')
			LEFT JOIN `employee_contract` ON
					u.`employee_id` = `employee_contract`.`employee_id`
				AND `employee_contract`.`status` = {$this->pub}
				AND '{date}' BETWEEN `employee_contract`.`valid_from` AND IFNULL(`employee_contract`.`valid_to`, '{$this->end}')
				AND '{date}' BETWEEN `employee_contract`.`ec_valid_from` AND IFNULL(`employee_contract`.`ec_valid_to`, '{$this->end}')
		";
		if (weHaveModule('ttwa-csm')) {
			$SQL .= "
			LEFT JOIN `competency` comp ON
					comp.`competency_id` = urr.`related_value`
				AND urr.`related_model` = 'Competency'
				AND comp.`status` = {$this->pub}
			";
		}
		$SQL .= "
			WHERE
					urr.`status` = {$this->pub}
				{$whereSQL}
			ORDER BY u.`username`, urr.`related_model`
		";

		return $SQL;
	}

	/**
	 * Törlés cache del
	 * @param string $modelName
	 * @param boolean $hasRight
	 * @return void
	 */
	public function actionDelete($modelName = null, $hasRight = false): void {
		parent::actionDelete($modelName, $hasRight);
		$this->delCache();
	}

	/**
	 * Mentés cache del
	 * @param array $data
	 * @param string $modelName
	 * @param string $pk
	 * @param boolean $vOnly
	 * @param boolean $ret
	 * @param string $contentId
	 * @return void
	 */
	public function actionSave($data = [], $modelName = null, $pk = null, $vOnly = false, $ret = false, $contentId = null): void {
		parent::actionSave($data, $modelName, $pk, $vOnly, $ret, $contentId);
		$this->delCache();
	}

	/**
	 * Biztonsági függvény #1
	 * @return array
	 */
	public function filters() :array {
        return [
            'accessControl', // perform access control for CRUD operations
        ];
    }

	/**
	 * Biztonsági függvény #2
	 * @return array
	 */
	public function accessRules() :array
	{
        return
		[
            [
				'allow', // allow authenticated users to access all actions
                'users' => ['@'],
            ],
            [
				'deny',  // deny all users
                'users' => ['*'],
            ],
        ];
    }
}

?>