<?php

/**
 * Set language from login page
 *
 */
class LanguageMenuController extends Controller
{
	/**
	 * Render language menu
	 *
	 */
	public function actionIndex()
	{
		$clientType = requestParam('clientType') ?? "";

		switch ($clientType) {

			case "ease":
				$view = "index";
				break;
			case "wtt":
				$view = "application.views.customers.velux.wtt.languagemenu";
				break;
			default:
				$view = "index";
				break;
		}

		$this->render($view, array());
	}

	/**
	 * Change language
	 *
	 */
	public function actionChangeLanguage()
	{
		AnyCache::clear();
		unset($_COOKIE['tiptime_language']);

		$lang_id = requestParam('id');
		$resp    = [ 'status' => 'false' ];

		if ($lang_id) {

			unset($_SESSION['tiptime']['dict']);
			$cn = 'tiptime_language';
			Yang::setCookie($cn, new CHttpCookie($cn, $lang_id));

			$resp['status'] = 'true';
			echo json_encode($resp);
		} else {
			echo json_encode($resp);
		}
	}
}

?>
