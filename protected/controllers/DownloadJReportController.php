<?php

'yii2-only`;

	namespace app\controllers;
	use <PERSON>;

`/yii2-only';


#yii2: done

class DownloadJReportController extends Controller
{
	public function actionIndex() {

		$reportId = requestParam('report_id');
		$contentDisposition = requestParam('content_disposition');

		if (!empty($contentDisposition) && in_array($contentDisposition, ["inline", "attachment"])) {
			// DO NOTHING...
		} else {
			$contentDisposition = null;
		}

		if (empty($reportId)) {
			return false;
		}

		$getActualReportSQL = "
			SELECT
				r.`report_id`,
				r.`report_type`,
				r.`file_type`,
				r.`file_name`,
				r.`report_path`,
				r.`report_status`,
				r.`created_on`
			FROM
				`report` r
			WHERE
				r.`report_id` = '{$reportId}'
		";

		$actualReport = dbFetchAll($getActualReportSQL);

		if (!count($actualReport)) {
			return false;
		}

		if ($actualReport[0]["report_type"] === "NODE") {
			$file = Yang::getParam('nodeReportDir');
			$file .= "/".$actualReport[0]["report_path"];
		} else if ($actualReport[0]["report_type"] === "JASPER") {
			$file = Yang::getParam('reportDir');
			$file .= "/".$actualReport[0]["report_path"];
		} else {
			$file = Yang::getParam('otherReportDir');
			$file .= "/". $actualReport[0]["report_path"];
		}

		$fileType = $actualReport[0]["file_type"];

		$fileExists = false;
		$fileReadable = false;

		if (file_exists($file)) {
			$fileExists = true;

			if (is_readable($file)) {
				$fileReadable = true;
			}
		}

		if (!$fileExists || !$fileReadable) {
			return false;
		}

		switch ($fileType) {
			case "pdf":
				$contentType = "application/pdf";
				if (empty($contentDisposition)) {
					$contentDisposition = "inline";
				}
				break;
			default:
				$contentType = "application/pdf";
				if (empty($contentDisposition)) {
					$contentDisposition = "attachment";
				}
				break;
		}

		$fp = fopen($file, 'r');
		$fr = fread($fp, filesize($file));

		header('Content-type: '.$contentType);
		header('Content-disposition: '.$contentDisposition.'; filename="'.basename($file).'"'); // inline, attachment
		echo $fr;

		fclose($fp);
	}
}

?>